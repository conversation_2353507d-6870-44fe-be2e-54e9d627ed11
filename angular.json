{"$schema": "./node_modules/@angular/cli/lib/config/schema.json", "version": 1, "newProjectRoot": "projects", "projects": {"bca-caerus-web": {"projectType": "application", "schematics": {"@schematics/angular:component": {"skipTests": true, "standalone": true, "inlineStyle": true, "changeDetection": "OnPush"}, "@schematics/angular:directive": {"skipTests": true, "standalone": true, "flat": false}, "@schematics/angular:pipe": {"skipTests": true, "standalone": true, "flat": false}, "@schematics/angular:service": {"skipTests": true}}, "root": "", "sourceRoot": "src", "prefix": "app", "architect": {"build": {"builder": "@angular/build:application", "options": {"allowedCommonJsDependencies": ["highcharts", "reflect-metadata", "pinyin4js", "lodash"], "outputPath": "dist", "index": "src/index.html", "browser": "src/main.ts", "polyfills": ["zone.js"], "tsConfig": "tsconfig.app.json", "assets": ["src/favicon.ico", "src/assets"], "styles": ["node_modules/ng-zorro-antd/ng-zorro-antd.min.css", "src/styles.css"], "scripts": ["node_modules/leader-line/leader-line.min.js"]}, "configurations": {"production": {"budgets": [{"type": "initial", "maximumWarning": "100mb", "maximumError": "5mb"}, {"type": "anyComponentStyle", "maximumWarning": "8kb", "maximumError": "10kb"}], "outputHashing": "all"}, "development": {"optimization": false, "extractLicenses": false, "sourceMap": true, "namedChunks": true}}, "defaultConfiguration": "production"}, "serve": {"builder": "@angular/build:dev-server", "configurations": {"production": {"buildTarget": "bca-caerus-web:build:production"}, "development": {"buildTarget": "bca-caerus-web:build:development", "proxyConfig": "src/proxy.conf.json", "port": 4500}}, "defaultConfiguration": "development"}, "extract-i18n": {"builder": "@angular/build:extract-i18n", "options": {"buildTarget": "bca-caerus-web:build"}}}}}, "cli": {"analytics": false}}