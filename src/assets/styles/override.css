
address, dl, ol, p, pre, ul {
  @apply mb-0;
}

[type='text']:focus, input:where(:not([type])):focus, [type='email']:focus, [type='url']:focus, [type='password']:focus, [type='number']:focus, [type='date']:focus, [type='datetime-local']:focus, [type='month']:focus, [type='search']:focus, [type='tel']:focus, [type='time']:focus, [type='week']:focus, [multiple]:focus, textarea:focus, select:focus {
  @apply ring-0;
}

.ant-popover {
  @apply max-w-125;
}

.ant-popover-inner-content {
  @apply text-xs;
}

.ant-message-custom-content {
  @apply flex items-center;
}

.ant-message-custom-content span {
  @apply max-w-md text-left break-all;
}

.ant-select-arrow, .ant-select-clear {
  @apply leading-0;
}

.ant-btn,
.ant-pagination-item-link {
  @apply !inline-flex items-center justify-center align-top;
}

.ant-picker-separator .anticon {
  @apply !align-text-top;
}

/* 注释原因：数据集回溯中日期范围组件预设范围样式冲突 */
/* .ant-picker-ranges {
  @apply flex items-center justify-between;
} */

/* tooltip */
.ant-tooltip-inner {
  @apply min-w-[45px] text-left whitespace-pre-wrap;
}

.ant-tabs-tab+.ant-tabs-tab {
  margin: 0 0 0 32px !important;
}

.ant-table-fixed-header .ant-table-scroll .ant-table-header {
  @apply pb-0;
}

.ant-tree .ant-tree-node-content-wrapper.ant-tree-node-selected {
  background-color: var(--surface-d) !important;
}

.ant-layout-sider-children {
  @apply overflow-auto;
}

.ant-page-header-back-button {
  @apply leading-0 align-text-top;
}

.ant-descriptions-item-label {
  @apply truncate;
}

.ant-menu-item,
.ant-picker-ranges .ant-picker-ok,
.ant-select-item-option-selected:not(.ant-select-item-option-disabled) .ant-select-item-option-state,
.ant-select-multiple .ant-select-selection-item-remove {
  @apply flex items-center
}

.ant-select-multiple .ant-select-selection-item-remove>.anticon,
.ant-spin, .ant-switch, .ant-transfer-operation {
  @apply leading-0;
}

.ant-tooltip {
  @apply max-w-96;
}

.ant-avatar {
  @apply
    inline-flex items-center justify-center
    ring-2 ring-slate-700/10;
}

/* .ant-tree, .ant-tree-checkbox-group, */
.ant-select-single .ant-select-selector .ant-select-selection-placeholder {
  @apply text-xs/7.5;
}

.ant-tree-switcher,
.ant-select-tree-switcher {
  @apply flex items-center justify-center;
}


.ant-radio-wrapper {
  @apply items-center!;
}

.ant-radio {
  @apply top-unset!;
}

.ant-radio-wrapper span.ant-radio + * {
  @apply px-1!;
}

/* custom-range-picker */
.custom-range-picker-overlay .ant-picker-dropdown-range {
  @apply p-0!;
}


.custom-range-picker-overlay .ant-picker-panel-container {
  @apply shadow-none;
}

.custom-range-picker-overlay .ant-picker-panel-container .ant-picker-panel {
  @apply border-0!;
}