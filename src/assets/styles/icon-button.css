[iconBtn] {
  @apply
    relative inline-flex items-center justify-center
    w-6 h-6 p-1 bg-transparent border-0 border-transparent
    cursor-pointer align-middle
    hover:fill-inherit

    after:content-['']
    after:absolute
    after:inset-0
    after:rounded
    after:origin-center
    after:bg-black/0
    after:transition-colors
    hover:after:bg-black/5 
    dark:hover:after:bg-white/10 
    focus:after:bg-black/5
    active:after:bg-black/10
    ;
}

[iconBtn].disabled {
  @apply opacity-50 cursor-not-allowed pointer-events-none;
}

[iconBtn].xs {
  @apply w-3 h-3 p-0;
  /* @apply after:scale-[0.5]; */
}

[iconBtn].sm {
  @apply w-5 h-5 p-0.5 rounded-sm;
  /* @apply after:scale-[0.83333]; */
}

[iconBtn].md {
  @apply w-6 h-6;
  /* @apply after:scale-100; */
}

[iconBtn].lg {
  @apply w-7 h-7;
  /* @apply after:scale-[1.1666]; */
}

[iconBtn].xl {
  @apply w-8 h-8;
  /* @apply after:scale-[1.3333]; */
}

[iconBtn].xxl {
  @apply w-10 h-10;
  /* @apply after:scale-[1.6666]; */
}