.backTop {
  @apply
    flex items-center justify-center
    w-10 h-10 rounded-full
    bg-black/20
    text-white
    transition-all
    duration-300
    hover:bg-black/60;
    animation: fadeIn linear both;
    animation-timeline: view(block)
}

.fixed-widgets .backTop {
  @apply
    flex items-center justify-center
    w-10 h-10 rounded-full
    bg-black/20
    text-white
    transition-all
    duration-300
    hover:bg-black/60;
    animation: fadeIn linear both;
    animation-timeline: view(block)
}

.fixed-widgets .btn {
  animation: fadeIn linear both;
}

@supports (animation-timeline: view()) {
  .fixed-widgets .backTop {
    opacity: 0;
  }
}

@keyframes fadeIn {
  0% {
    pointer-events: none;
  }
  30% {
    opacity: 0;
    pointer-events: none;
  }
  31% {
    opacity: 1;
    pointer-events: auto;
  }
  100% {
    opacity: 1;
    pointer-events: auto;
  }
}

@supports (animation-timeline: view()) {
  .backTop {
    opacity: 0;
  }
}


.fixed-header {
  animation: shadowFadeIn linear both;
  animation-timeline: view(block);
}

@keyframes shadowFadeIn {
  0% {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0);
  }
  20% {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0);
  }
  21% {
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
  }
  100% {
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.15);
  }
}

@keyframes scale {
  0% {
    transform: scale(1.0);
  }

  100% {
    transform: scale(1.45);
  }
}


.animate__swing {
  @apply animate-[swing_2s_ease-in-out_infinite]
}

@keyframes swing {
  20% {
    transform: rotate(15deg)
  }

  40% {
    transform: rotate(-10deg)
  }

  60% {
    transform: rotate(5deg)
  }

  80% {
    transform: rotate(-5deg)
  }

  to {
    transform: rotate(0deg)
  }
}


.chjianbian {
  text-transform:uppercase;
  background:
  -webkit-linear-gradient(45deg,
  #ff0000 0%,
  #ffb600 11%,
  #ffcc00 22%,
  #a5ff00 33%,
  #00a9ff 44%,
  #0400ff 55%,
  #8a00fc 66%,
  #ff00e9 77%,
  #ff0059 88%,
  #ff0000 100%);
  color:transparent;
  /*设置字体颜色透明*/
  -webkit-background-clip: text;
  /*背景裁剪为文本形式*/
  animation: ran 10s linear infinite alternate;
  /*动态20s展示*/
}

@keyframes ran {
  from {
    background-position: 0 0;
  }
  to {
    background-position: 1000px 0;
  }
}