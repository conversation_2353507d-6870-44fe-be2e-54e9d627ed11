.tag-radio {
  @apply inline-flex items-center h-4.5 px-1 text-xs rounded-xs
      cursor-pointer whitespace-nowrap leading-none uppercase
      hover:text-primary;

  &.active {
    @apply bg-primary text-white;
  }
}

.tag-radio-new {
  @apply relative z-10 inline-flex items-center h-4.5 px-1 text-xs transition-colors
      cursor-pointer whitespace-nowrap leading-none uppercase;

  &.active {
    @apply text-white z-10;
  }
}

.dida-radio {
  @apply relative z-10 flex-1 flex items-center justify-center w-1/3 h-7 bg-transparent self-stretch rounded-md cursor-pointer hover:bg-white/50 text-xs;

  &.active {
    @apply bg-linear-to-r from-[#465867] to-[#1E2C3A] text-white hover:bg-white/90;
  }
}

.dida-radio-active {
  @apply bg-linear-to-r from-[#465867] to-[#1E2C3A] text-white hover:bg-white/90;
}

.dida-radio-new {
  @apply relative z-10 flex-1 w-1/3 h-7 flex items-center justify-center self-stretch bg-transparent rounded-md cursor-pointer text-xs transition-colors whitespace-nowrap;

  &.active {
    @apply text-white;
  }
}

.dida-radio-arrow {
  @apply
    relative z-10
    flex-1 w-1/3 h-7
    flex items-center justify-center self-stretch
    bg-transparent cursor-pointer
    text-xs transition-colors whitespace-nowrap;

  &:after {
    content: '';
    @apply absolute top-0 h-full w-3 transform translate-x-full bg-linear-to-r from-[#dcdcdd] to-[#dcdcdd];
    right: -1px;
    clip-path: polygon(0 0, 100% 50%, 0 100%);
  }

  /* &:before {
    @apply left-0 transform -translate-x-full;
    clip-path: polygon(100% 0, 0 50%, 100% 100%);
  } */

  &.active {
    @apply text-white;
    &:after {
      @apply bg-linear-to-r from-[#1E2C3A] to-[#1E2C3A];
      right: 2px;
    }
  }

  /* Handle hover state */
  /* &:hover:not(.active) {
    @apply bg-white/50;

    &:after {
      @apply bg-white/50;
    }
  } */
}

.line-radio {
  @apply px-2 py-1 -mb-px border-b-2 border-transparent text-sm cursor-pointer text-neutral-500;

  &.active {
    @apply border-primary text-neutral-600 font-bold;
  }
}

.card-radio {
  @apply relative block
      text-xs cursor-pointer rounded-lg
      border border-slate-300/80
      transition-all outline-hidden!
      bg-white hover:bg-blue-50/80;

  &.active {
    @apply border-blue-600!
      outline-hidden
      ring-2! ring-blue-600
      bg-blue-50/50;
  }
}

.radio-base-wrapper {
  @apply duration-300 hover:bg-black/5 inline-flex items-stretch leading-none rounded-sm text-[#737981] transition-all;
}

.radio-base {
  @apply cursor-pointer flex flex-1 gap-0.5 items-center py-1 px-2 rounded-sm text-xs whitespace-nowrap;

  &.active {
    @apply bg-black/5;
  }
}
