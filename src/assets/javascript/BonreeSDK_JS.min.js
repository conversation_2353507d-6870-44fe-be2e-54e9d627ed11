var BonreeAgent=function(exports){"use strict";var version$1="1.9.4",_typesArr;function _typeof(e){return(_typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function _toPrimitive(e,t){if("object"!==_typeof(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0===n)return("string"===t?String:Number)(e);n=n.call(e,t||"default");if("object"!==_typeof(n))return n;throw new TypeError("@@toPrimitive must return a primitive value.")}function _toPropertyKey(e){e=_toPrimitive(e,"string");return"symbol"===_typeof(e)?e:String(e)}function _defineProperty(e,t,n){return(t=_toPropertyKey(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var isDefined=function(e){return null!=e},isFunction=function(e){return"function"==typeof e},isString$2=function(e){return"string"==typeof e},isArray=function(e){return isDefined(e)&&e instanceof Array},NIL_FN=function(){},$global=window||{},isJSON=function(e){if(isString$2(e))try{var t=JSON.parse(e);return!("object"!=_typeof(t)||!t)}catch(e){return!1}},typesArr=(_typesArr={"3dm":"x-world/x-3dmf","3dmf":"x-world/x-3dmf",a:"application/octet-stream",aab:"application/x-authorware-bin",aam:"application/x-authorware-map",aas:"application/x-authorware-seg",abc:"text/vndabc",acgi:"text/html",afl:"video/animaflex",ai:"application/postscript",aif:"audio/x-aiff",aifc:"audio/x-aiff",aiff:"audio/x-aiff",aim:"application/x-aim",aip:"text/x-audiosoft-intra",ani:"application/x-navi-animation",aos:"application/x-nokia-9000-communicator-add-on-software",aps:"application/mime",arc:"application/octet-stream",arj:"application/octet-stream",art:"image/x-jg",asf:"video/x-ms-asf",asm:"text/x-asm",asp:"text/asp",asx:"application/x-mplayer2"},_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_typesArr,"asx","video/x-ms-asf-plugin"),"au","audio/x-au"),"avi","video/avi"),"bcpio","application/x-bcpio"),"bin","application/x-macbinary"),"bm","image/bmp"),"bmp","image/x-windows-bmp"),"boo","application/book"),"book","application/book"),"boz","application/x-bzip2"),_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_typesArr,"bsh","application/x-bsh"),"bz","application/x-bzip"),"bz2","application/x-bzip2"),"c","text/plain"),"c++","text/plain"),"cat","application/vndms-pki.seccat"),"cc","text/plain"),"ccad","application/clariscad"),"cco","application/x-cocoa"),"cdf","application/x-cdf"),_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_typesArr,"cer","application/pkix-cert"),"cha","application/x-chat"),"chat","application/x-chat"),"class","application/x-java-class"),"com","application/octet-stream"),"conf","text/plain"),"cpio","application/x-cpio"),"cpp","text/x-c"),"cpt","application/x-cpt"),"crl","application/pkcs-crl"),_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_typesArr,"crl","application/pkix-crl"),"crt","application/x-x509-ca-cert"),"csh","application/x-csh"),"css","application/x-pointplus"),"cxx","text/plain"),"dcr","application/x-director"),"deepv","application/x-deepv"),"der","application/x-x509-ca-cert"),"dif","video/x-dv"),"dir","application/x-director"),_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_typesArr,"dl","video/dl"),"doc","application/msword"),"dot","application/msword"),"dp","application/commonground"),"drw","application/drafting"),"dump","application/octet-stream"),"dv","video/x-dv"),"dvi","application/x-dvi"),"dwf","model/vnd.dwf"),"dwg","application/acad"),_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_typesArr,"dxf","application/dxf"),"dxr","application/x-director"),"el","text/x-script.elisp"),"elc","application/x-bytecode.elisp compiled elisp)"),"env","application/x-envoy"),"eps","application/postscript"),"es","application/x-esrehber"),"etx","text/x-setext"),"evy","application/x-envoy"),"exe","application/octet-stream"),_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_typesArr,"f","text/x-fortran"),"f77","text/x-fortran"),"f90","text/x-fortran"),"fdf","application/vnd.fdf"),"fif","application/fractals"),"fli","video/x-fli"),"flo","image/florian"),"flx","text/vnd.fmi.flexstor"),"fmf","video/x-atomic3d-feature"),"for","text/x-fortran"),_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_typesArr,"fpx","image/vnd.net-fpx"),"frl","application/freeloader"),"funk","audio/make"),"g","text/plain"),"g3","image/g3fax"),"gif","image/gif"),"gl","video/x-gl"),"gsd","audio/x-gsm"),"gsm","audio/x-gsm"),"gsp","application/x-gsp"),_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_typesArr,"gss","application/x-gss"),"gtar","application/x-gtar"),"gz","application/x-compressed"),"gzip","application/x-gzip"),"h","text/x-h"),"hdf","application/x-hdf"),"help","application/x-helpfile"),"hgl","application/vnd.hp-hpgl"),"hh","text/x-h"),"hlb","text/x-script"),_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_typesArr,"hlp","application/x-winhelp"),"hpg","application/vnd.hp-hpgl"),"hpgl","application/vnd.hp-hpgl"),"hqx","application/binhex"),"hta","application/hta"),"htc","text/x-component"),"htm","text/html"),"html","text/html"),"htmls","text/html"),"htt","text/webviewhtml"),_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_typesArr,"htx","text/html"),"ice","x-conference/x-cooltalk"),"ico","image/x-icon"),"idc","text/plain"),"ief","image/ief"),"iefs","image/ief"),"iges","application/iges"),"igs","application/iges"),"igs","model/iges"),"ima","application/x-ima"),_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_typesArr,"imap","application/x-httpd-imap"),"inf","application/inf"),"ins","application/x-internett-signup"),"ip","application/x-ip2"),"isu","video/x-isvideo"),"it","audio/it"),"iv","application/x-inventor"),"ivr","i-world/i-vrml"),"ivy","application/x-livescreen"),"jam","audio/x-jam"),_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_typesArr,"jav","text/x-java-source"),"java","text/x-java-source"),"jcm","application/x-java-commerce"),"jfif","image/pjpeg"),"jfif-tbnl","image/jpeg"),"jpe","image/pjpeg"),"jpeg","image/pjpeg"),"jpg","image/pjpeg"),"jps","image/x-jps"),"js","application/x-javascript"),_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_typesArr,"jut","image/jutvision"),"kar","audio/midi"),"ksh","application/x-ksh"),"la","audio/x-nspaudio"),"lam","audio/x-liveaudio"),"latex","application/x-latex"),"lha","application/octet-stream"),"lhx","application/octet-stream"),"list","text/plain"),"lma","audio/x-nspaudio"),_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_typesArr,"log","text/plain"),"lsp","application/x-lisp"),"lst","text/plain"),"lsx","text/x-la-asf"),"ltx","application/x-latex"),"lzh","application/octet-stream"),"lzx","application/octet-stream"),"m","text/x-m"),"m1v","video/mpeg"),"m2a","audio/mpeg"),_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_typesArr,"m2v","video/mpeg"),"m3u","audio/x-mpequrl"),"man","application/x-troff-man"),"map","application/x-navimap"),"mar","text/plain"),"mbd","application/mbedlet"),"mc$","application/x-magic-cap-package-1.0"),"mcd","application/x-mathcad"),"mcf","text/mcf"),"mcp","application/netmc"),_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_typesArr,"me","application/x-troff-me"),"mht","message/rfc822"),"mhtml","message/rfc822"),"mid","application/x-midi"),"midi","application/x-midi"),"mif","application/x-mif"),"mime","www/mime"),"mjf","audio/x-vnd.audioexplosion.mjuicemediafile"),"mjpg","video/x-motion-jpeg"),"mm","application/x-meme"),_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_typesArr,"mod","audio/x-mod"),"moov","video/quicktime"),"mov","video/quicktime"),"movie","video/x-sgi-movie"),"mp2","audio/x-mpeg"),"mp3","audio/x-mpeg-3"),"mpa","audio/mpeg"),"mpc","application/x-project"),"mpe","video/mpeg"),"mpeg","video/mpeg"),_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_typesArr,"mpg","video/mpeg"),"mpga","audio/mpeg"),"mpp","application/vnd.ms-project"),"mpt","application/x-project"),"mpv","application/x-project"),"mpx","application/x-project"),"mrc","application/marc"),"ms","application/x-troff-ms"),"mv","video/x-sgi-movie"),"my","audio/make"),_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_typesArr,"mzz","application/x-vnd.audioexplosion.mzz"),"nap","image/naplps"),"naplps","image/naplps"),"nc","application/x-netcdf"),"ncm","application/vnd.nokia.configuration-message"),"nif","image/x-niff"),"niff","image/x-niff"),"nix","application/x-mix-transfer"),"nsc","application/x-conference"),"nvd","application/x-navidoc"),_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_typesArr,"o","application/octet-stream"),"oda","application/oda"),"omc","application/x-omc"),"omcd","application/x-omcdatamaker"),"omcr","application/x-omcregerator"),"p","text/x-pascal"),"p10","application/x-pkcs10"),"p12","application/x-pkcs12"),"p7a","application/x-pkcs7-signature"),"p7c","application/x-pkcs7-mime"),_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_typesArr,"p7m","application/x-pkcs7-mime"),"p7r","application/x-pkcs7-certreqresp"),"p7s","application/pkcs7-signature"),"part","application/pro_eng"),"pas","text/pascal"),"pbm","image/x-portable-bitmap"),"pcl","application/x-pcl"),"pcx","image/x-pcx"),"pdb","chemical/x-pdb"),"pdf","application/pdf"),_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_typesArr,"pfunk","audio/make.my.funk"),"pgm","image/x-portable-greymap"),"pic","image/pict"),"pict","image/pict"),"pkg","application/x-newton-compatible-pkg"),"pko","application/vnd.ms-pki.pko"),"pl","text/x-script.perl"),"plx","application/x-pixclscript"),"pm","text/x-script.perl-module"),"pm4","application/x-pagemaker"),_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_typesArr,"pm5","application/x-pagemaker"),"png","image/png"),"pnm","application/x-portable-anymap"),"pot","application/mspowerpoint"),"pov","model/x-pov"),"ppa","application/vnd.ms-powerpoint"),"ppm","image/x-portable-pixmap"),"pps","application/vnd.ms-powerpoint"),"ppt","application/powerpoint"),"ppz","application/mspowerpoint"),_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_typesArr,"pre","application/x-freelance"),"prt","application/pro_eng"),"ps","application/postscript"),"psd","application/octet-stream"),"pvu","paleovu/x-pv"),"pwz","application/vnd.ms-powerpoint"),"py","text/x-script.phyton"),"pyc","application/x-bytecode.python"),"qcp","audio/vnd.qcelp"),"qd3","x-world/x-3dmf"),_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_typesArr,"qd3d","x-world/x-3dmf"),"qif","image/x-quicktime"),"qt","video/quicktime"),"qtc","video/x-qtc"),"qti","image/x-quicktime"),"qtif","image/x-quicktime"),"ra","audio/x-realaudio"),"ram","audio/x-pn-realaudio"),"ras","application/x-cmu-raster"),"rast","image/cmu-raster"),_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_typesArr,"rexx","text/x-script.rexx"),"rf","image/vnd.rn-realflash"),"rgb","image/x-rgb"),"rm","application/vnd.rn-realmedia"),"rmm","audio/x-pn-realaudio"),"rmp","audio/x-pn-realaudio-plugin"),"rng","application/vnd.nokia.ringing-tone"),"rnx","application/vnd.rn-realplayer"),"roff","application/x-troff"),"rp","image/vnd.rn-realpix"),_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_typesArr,"rpm","audio/x-pn-realaudio-plugin"),"rt","text/vnd.rn-realtext"),"rtf","application/x-rtf"),"rtx","application/rtf"),"rv","video/vnd.rn-realvideo"),"s","text/x-asm"),"s3m","audio/s3m"),"saveme","application/octet-stream"),"sbk","application/x-tbook"),"scm","application/x-lotusscreencam"),_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_typesArr,"sdml","text/plain"),"sdp","application/x-sdp"),"sdr","application/sounder"),"sea","application/x-sea"),"set","application/set"),"sgm","text/x-sgml"),"sgml","text/x-sgml"),"sh","application/x-sh"),"shar","application/x-shar"),"shtml","text/x-server-parsed-html"),_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_typesArr,"sid","audio/x-psid"),"sit","application/x-sit"),"skd","application/x-koan"),"skm","application/x-koan"),"skp","application/x-koan"),"skt","application/x-koan"),"sl","application/x-seelogo"),"smi","application/smil"),"smil","application/smil"),"snd","audio/x-adpcm"),_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_typesArr,"sol","application/solids"),"spc","application/x-pkcs7-certificates"),"spl","application/futuresplash"),"spr","application/x-sprite"),"sprite","application/x-sprite"),"src","application/x-wais-source"),"ssi","text/x-server-parsed-html"),"ssm","application/streamingmedia"),"sst","application/vnd.ms-pki.certstore"),"step","application/step"),_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_typesArr,"stl","application/vnd.ms-pki.stl"),"stp","application/step"),"sv4cpio","application/x-sv4cpio"),"sv4crc","application/x-sv4crc"),"svf","image/x-dwg"),"svr","application/x-world"),"swf","application/x-shockwave-flash"),"t","application/x-troff"),"talk","text/x-speech"),"tar","application/x-tar"),_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_typesArr,"tbk","application/toolbook"),"tcl","application/x-tcl"),"tcsh","text/x-script.tcsh"),"tex","application/x-tex"),"texi","application/x-texinfo"),"texinfo","application/x-texinfo"),"text","application/plain"),"tgz","application/gnutar"),"tif","image/x-tiff"),"tiff","image/x-tiff"),_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_typesArr,"tr","application/x-troff"),"tsi","audio/tsp-audio"),"tsp","application/dsptype"),"tsv","text/tab-separated-values"),"turbot","image/florian"),"txt","text/plain"),"uil","text/x-uil"),"uni","text/uri-list"),"unis","text/uri-list"),"unv","application/i-deas"),_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_typesArr,"uri","text/uri-list"),"uris","text/uri-list"),"ustar","application/x-ustar"),"uu","application/octet-stream"),"uue","text/x-uuencode"),"vcd","application/x-cdlink"),"vcs","text/x-vcalendar"),"vda","application/vda"),"vdo","video/vdo"),"vew","application/groupwise"),_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_typesArr,"viv","video/vivo"),"vivo","video/vivo"),"vmd","application/vocaltec-media-desc"),"vmf","application/vocaltec-media-file"),"voc","audio/x-voc"),"vos","video/vosaic"),"vox","audio/voxware"),"vqe","audio/x-twinvq-plugin"),"vqf","audio/x-twinvq"),"vql","audio/x-twinvq-plugin"),_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_typesArr,"vrml","application/x-vrml"),"vrt","x-world/x-vrt"),"vsd","application/x-visio"),"vst","application/x-visio"),"vsw","application/x-visio"),"w60","application/wordperfect6.0"),"w61","application/wordperfect6.1"),"w6w","application/msword"),"wav","audio/x-wav"),"wb1","application/x-qpro"),_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_typesArr,"wbmp","image/vnd.wap.wbmp"),"web","application/vnd.xara"),"wiz","application/msword"),"wk1","application/x-123"),"wmf","windows/metafile"),"wml","text/vnd.wap.wml"),"wmlc","application/vnd.wap.wmlc"),"wmls","text/vnd.wap.wmlscript"),"wmlsc","application/vnd.wap.wmlscriptc"),"word","application/msword"),_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_typesArr,"wp","application/wordperfect"),"wp5","application/wordperfect"),"wp6","application/wordperfect"),"wpd","application/wordperfect"),"wq1","application/x-lotus"),"wri","application/x-wri"),"wrl","application/x-world"),"wrz","model/vrml"),"wsc","text/scriplet"),"wsrc","application/x-wais-source"),_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_typesArr,"wtk","application/x-wintalk"),"xbm","image/x-xbitmap"),"xdr","video/x-amt-demorun"),"xgz","xgl/drawing"),"xif","image/vnd.xiff"),"xl","application/excel"),"xla","application/x-msexcel"),"xlb","application/x-excel"),"xlc","application/x-excel"),"xld","application/x-excel"),_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_typesArr,"xlk","application/x-excel"),"xll","application/x-excel"),"xlm","application/x-excel"),"xls","application/excel"),"xlt","application/excel"),"xlv","application/x-excel"),"xlw","application/vnd.ms-excel"),"xm","audio/xm"),"xml","application/xml"),"xmz","xgl/movie"),_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_defineProperty(_typesArr,"xpix","application/x-vnd.ls-xpix"),"xpm","image/x-xpixmap"),"x-png","image/png"),"xsr","video/x-amt-showrun"),"xwd","image/x-xwindowdump"),"xyz","chemical/x-pdb"),"z","application/x-compressed"),"zip","multipart/x-zip"),"zoo","application/octet-stream"),"zsh","text/x-script.zsh"));function getMineTypeByHeader(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"text/html";return isEmpty(e)?t:(e["Content-Type"]||e["content-type"]||t).split(";")[0].trim()}function getMineTypeByUrl(e){var t="text/html";return isEmpty(e)?t:getMineTypeBySuffixes(getTakeSuffixes(e),t)}function getTakeSuffixes(e){var t,n,r;return isEmpty(e)?"":(t="",-1!=(n=e.lastIndexOf("."))&&(-1==(r=e.indexOf("?",n))&&(r=e.length),t=e.substring(n+1,r)),t)}function getMineTypeBySuffixes(e,t){return isEmpty(e)||(e=typesArr[e],isEmpty(e))?t:e}var isReadable=function(e){return isDefined(e)&&!isEmpty(e)},isEmpty=function(e){if(isDefined(e)){if(hasOwnProperty(e,"length"))return!e.length;if(hasOwnProperty(e,"size"))return!e.size;for(var t in e)if(hasOwnProperty(e,t))return!1}return!0},hasOwnProperty=function(e,t){return e&&Object.prototype.hasOwnProperty.call(e,t)},forEachArray=function(e,t){if(isArray(e))for(var n=0,r=e.length;n<r;n++)if(!1===t(e[n],n))break},forEachOwn=function(e,t){if(isDefined(e))for(var n in e)if(hasOwnProperty(e,n)){var r=e[n];try{if(!1===t(r,n))break}catch(e){}}},forEach=function(e,t){return(isArray(e)?forEachArray:forEachOwn)(e,t)},iterate=function(e){var t=[];if(isDefined(e)&&isDefined(e.next))for(var n=e.next();!n.done;)t.push(n.value),n=e.next();return t},first=function(e){if(isArray(e)&&!isEmpty(e))return e[0]},last=function(e){if(isArray(e)&&!isEmpty(e))return e[e.length-1]},extend=function(n,e){return n&&e&&forEachOwn(e,function(e,t){n[t]=e}),n},overwrite=function(n,e){return n&&e&&forEachOwn(e,function(e,t){hasOwnProperty(n,t)&&(n[t]=e)}),n},map=function(e,n){var r=[];return forEach(e,function(e,t){e=n&&n(e,t);isDefined(e)&&r.push(e)}),r},filter=function(e,n){var r=[];return forEach(e,function(e,t){n&&!0===n(e,t)&&r.push(e)}),r},flatten=function(e){return Array.prototype.concat.apply([],e)},prop=function(e,t){return isDefined(e)&&hasOwnProperty(e,t)?e[t]:null};function includes$1(e,t){for(var n=0,r=e.length;n<r;++n)if(e[n]===t)return!0;return!1}var trim=function(e){return e?String.prototype.trim?String.prototype.trim.call(e):e.toString().replace(/(^\s+)|(\s+$)/g,""):""},startWith=function(e,t){return!(!e||!e.indexOf)&&0===e.indexOf(t)},endWith=function(e,t){return!!(e&&t&&e.length>=t.length)&&e.substring(e.length-t.length)===t},withLength=function(e,t){try{return e?e.length<=t?e:e.substr(0,t-3)+"...":""}catch(e){return"The data cannot be parsed normally, please handle it manually"}},toUpper=function(e,t){return isDefined(t)||(t=""),e&&e.toUpperCase?e.toUpperCase():t},toLower=function(e,t){return isDefined(t)||(t=""),e&&e.toLowerCase?e.toLowerCase():t},stringify=isDefined($global.JSON)?$global.JSON.stringify:function(e){var n,r,t=_typeof(e);return"undefined"===t?'"undefined"':"boolean"===t?e?"true":"false":"number"===t?e.toString():"string"===t?'"'+(n=e.replace(/(["\\])/g,"\\$1").replace(/\r/g,"\\r").replace(/\n/g,"\\n"))+'"':"object"===t?e?e instanceof Array?(n="[",forEach(e,function(e,t){0<t&&(n+=","),n+=stringify(e)}),n+="]"):(n="{",r=0,forEachOwn(e,function(e,t){0<r&&(n+=","),n+=stringify(t)+":"+stringify(e),r++}),n+="}"):"null":"[none]"},toString$2=function(e){return"string"==typeof e?e:JSON.stringify(e)},enable=!1,logger=isDefined($global.console)&&isFunction($global.console.log)?function(){return $global.console.log}:NIL_FN,errorLogger=isDefined($global.console)&&isFunction($global.console.error)?$global.console.error:NIL_FN,enableLogger=function(){enable=!0},disableLogger=function(){enable=!1},log=function(e,t,n){(enable||n)&&(isDefined(t)&&stringify(t),isDefined(window.console))&&(window.console.$bonree=!0,logger(),window.console.$bonree=!1)},warn=function(e,t){isDefined(t)&&stringify(t),isDefined(window.console)&&(window.console.$bonree=!0,logger(),window.console.$bonree=!1)},exception=function(e){isDefined(window.console)&&(window.console.$bonree=!0,errorLogger(e),window.console.$bonree=!1)},cookieCfg={path:"/",expires:"Thu, 01 Jan 2099 00:00:01 GMT;"};function configCookie(e){e.sessionDomain&&(cookieCfg.domain=e.sessionDomain)}var get=function(e){try{var t=document.cookie.split(/;\s?/);if(!document.cookie)return getSession(e)||"";for(var n=0,r=t.length;n<r;n++){var i=t[n].split("="),a=i[0],o=i[1];if(a===e)return o}}catch(e){return log("Failed to read document cookie property"),""}},set=function(e,t,n){try{var r=e+"="+t;isDefined((n=n||{}).domain)&&(r+=";domain="+n.domain),isDefined(n.path)&&(r+=";path="+n.path),isDefined(n.expires)&&("string"==typeof n.expires?r+=";expires="+n.expires:r+=";expires="+n.expires.toUTCString()),document.cookie=r,window.sessionStorage&&window.sessionStorage.setItem(e,t),window.localStorage&&window.localStorage.setItem(e,t)}catch(e){log("Failed to write cookie property")}};function getSession(e){return window.sessionStorage?window.sessionStorage.getItem(e)||"":getLocaStore(e)||""}function getLocaStore(e){var t="";return t=window.localStorage?window.localStorage.getItem(e)||"":t}var dateNow=function(){return Date.now?Date.now():(new Date).getTime()},nowtime=function(){var n=dateNow();return function(e){var t;return isDefined($global.performance)&&isDefined($global.performance.now)&&isDefined($global.performance.timing)?[1e3*(t=0===e?0:Math.round($global.performance.now())),1e3*(t+$global.performance.timing.navigationStart)]:[1e3*((t=0===e?n:dateNow())-n),1e3*t]}}(),now=function(e){return nowtime(e)[1]},tillNow=function(e){return nowtime(e)[0]},getTime=function(){var e=nowtime();return{startTime:e[0],timestamp:e[1]}},getNavigationStart=function(){var e=Date.now?Date.now():(new Date).getTime(),t=$global.performance.timing.navigationStart;return{startTime:1e3*(e-t),timestamp:1e3*t}},delay=function(e,t){return e.$$original=!0,setTimeout(e,isDefined(t)?t:0)},bind=function(e,t){return function(){return e.apply(t,arguments)}},args=function(e){return isDefined(e.length)?Array.prototype.slice.call(e):e},hack=function(e,n,r,i){var t;return(e=isDefined(e)?e:function(){}).$$original?e:((t=function(){var t=args(arguments);try{return isDefined(n)&&n.apply(this,t),e.apply(this,t)}catch(e){isDefined(i)?i.apply(this,t.concat(e)):exception(e)}finally{if(isDefined(r)&&r.apply(this,t),t&&t[0]&&t[0]instanceof Error&&e&&"error"!==e.name)throw t[0]}}).$$original=e,t)},xpath=function(e){if(!e)return"";var t=e.id;if(t)return'//*[@id="'+t+'"]';for(var n=[],r=e;r;){var i=r.tagName&&r.tagName.toLowerCase&&r.tagName.toLowerCase()||null;if(!i)break;var a=r.parentElement&&r.parentElement.cloneNode(r.parentElement);if(!isDefined(a)){n.unshift(i);break}for(var o=a.children,s=o.length,c=0,l=0,d=0;d<s;d++){var u=o[d];if(u.tagName.toLowerCase()===i&&(l++,0<c))break;u===r&&(c=l)}n.unshift(1===l?i:i+"["+c+"]"),r=a}return"/"+n.join("/")},getBaseSetting=function(){var e=document.getElementsByTagName("base")||[],e=last(e);return e&&e.href||""},getFixedMetric=function(e,t){e=e[t];return isDefined(e)&&0<e?Math.round(e):0},getContentSize=function(e){return isDefined(e)?window.ArrayBuffer&&e instanceof ArrayBuffer?e.byteLength:window.Blob&&e instanceof Blob?e.size:e.toJSON?stringify(e.toJSON()).length:e.length||0:0},getRequestParam=function(e){if(!isDefined(e))return"";var t="";if("string"==typeof e)t=e;else if(window.FormData&&e instanceof FormData){var r={};try{forEach(iterate(e.entries()),function(e){var t,n=e[0],e=e[1];window.File&&e instanceof File?0<e.size&&(t=e.name?" "+e.name:"",r[n]="[File]"+t):r[n]=e})}catch(e){r={},log("serialize formData error")}t="[FormData] "+stringify(r)}else{if(window.Blob&&e instanceof Blob)return"[Blob]";if(window.ArrayBuffer&&e instanceof ArrayBuffer)return"[ArrayBuffer]";if(e.toJSON)t=stringify(e.toJSON());else{if(!e.toString)return"[unknown]";t=e.toString()}}return withLength(t,2e3)},unicode$1=function(e){for(var t="",n=0;n<e.length;n++)t+="\\u"+("0000"+e.charCodeAt(n).toString(16)).slice(-4);return t};function readBytesFromStream(e,r,i){var a,o,s;function c(){var e,t,n;return a.cancel().catch(noop),r.collectStreamBody?(1===o.length?t=o[0]:(t=new Uint8Array(s),n=0,o.forEach(function(e){t.set(e,n),n+=e.length})),e=t.slice(0,r.bytesLimit),t.length,r.bytesLimit,e.length):e||0}e.getReader&&(a=e.getReader(),o=[],s=0,function n(){a.read().then(function(e){var t;e.done||(r.collectStreamBody&&o.push(e.value),(s+=e.value.length)>r.bytesLimit)?(t=c(),i(t)):n()})}())}function noop(){}function tryToClone(e){try{return e.clone()}catch(e){}}var NAVIGATION_START="navigationStart",START_TIME="startTime",UNLOAD_EVENT_START="unloadEventStart",UNLOAD_EVENT_END="unloadEventEnd",REDIRECT_START="redirectStart",REDIRECT_END="redirectEnd",FETCH_START="fetchStart",DOMAIN_LOOKUP_START="domainLookupStart",DOMAIN_LOOKUP_END="domainLookupEnd",CONNECT_START="connectStart",CONNECT_END="connectEnd",SECURE_CONNECTION_START="secureConnectionStart",REQUEST_START="requestStart",RESPONSE_START="responseStart",RESPONSE_END="responseEnd",RESPONSE_STATUS="responseStatus",DOM_LOADING="domLoading",DOM_INTERACTIVE="domInteractive",DOM_CONTENT_LOADED_EVENT_START="domContentLoadedEventStart",DOM_CONTENT_LOADED_EVENT_END="domContentLoadedEventEnd",DOM_COMPLETE="domComplete",LOAD_EVENT_START="loadEventStart",LOAD_EVENT_END="loadEventEnd",FIRST_PAINT="fp",FIRST_CONTENTFUL_PAINT="fcp",LARGEST_CONTENTFUL_PAINT="lcp",REDIRECT_COUNT="redirectCount",DURATION="duration",WORKER_START="workerStart",NEXT_HOP_PROTOCOL="nextHopProtocol",TRANSFER_SIZE="transferSize",ENCODED_BODY_SIZE="encodedBodySize",DECODED_BODY_SIZE="decodedBodySize",UPLOAD_BODY_SIZE="uploadBodySize",REQUEST_HEADER="requestHeader",RESPONSE_HEADER="responseHeader",CALLBACK_START="callbackStart",CALLBACK_END="callbackEnd",CALLBACK_TIME="callbackTime",TIMESTAMP="timestamp",INITIATOR_TYPE="initiatorType",XML_HTTP_REQUEST="xmlhttprequest",FETCH="fetch",TAG_NAME="tagName",TYPE$3="type",MODEL$1="model",IS_MAIN_DOCUMENT="isMainDocument",PAGE_ID="pageViewId",PAGE_URL="pageUrl",PAGE_TITLE="pageTitle",MESSAGE="message",NAME$2="name",ERROR_LINE="line",ERROR_FILE="file",ERROR_COLUMN="column",ERROR_STACK="stack",IS_SLOW="isSlow",IS_CUSTOM="isCustome",URL$1="url",NET_METHOD="method",NET_PORT="port",NET_IP="ip",NET_PT="protocalType",NET_TID="guid",NET_XBR="xBrResponse",NET_TRACE="traceResponse",NET_EOP="errorOccurrentProcess",NET_EC="code",NET_TYPE="requestType",CUSTOM_IC="customIc",NET_CBBQ="requestBodyByKey",REQUEST_BODY="requestBody",NET_CBHQ="requestHeaderByKey",NET_CBQ="reqUrlDta",E_TYPE="eType",SOURCE_OF_ACTION="sourceOfAction",FRAME_TYPE="frameType",STAY_TIME="stayTime",CORRELATION_ID="correlationId",IS_EXIT="isExit",PAGE_CREATE_TIME="pageCreateTime",REFERRER="referrer",STATUS="status",STATUS_TEXT="statusText",ALIAS="alias",PATH="path",ROOT="root",FULL_URL="fullUrl",FRAME_WORK="framework",CLIENT_TYPE="clientType",VALUE="value",FULL_RESOURCE_LOAD_TIME="fullResourceLoadTime",PAGE_DATA="data/page",PAGE_READY="page/ready",PAGE_LOAD="page/load",PAGE_INVISIBLE="page/invisible",PAGE_VISIBLE="page/visible",RESOURCE_DATA="data/resource",RESOURCE_DUMP="resource/dump",FLUSH_DATA="data/flush",ERROR_DATA="data/error",REQUEST_INIT="request/init",REQUEST_DATA="data/request",ROUTE_DATA="data/route",ACTION_DATA="data/event",TRACE_ACTION_DATA="data/traceAction",PARAM="param",CONSOLE_DATA="data/console",SPAN_DATA="data/span",BROWSER=0,IOS=2,HOS=4,FULLY_COLLECT=1e3,NET="network",VIEW="view",ERROR="jserror",ACTION="action",ROUTE="routechange",WEBVIEWDATA="h5",PAGE="page",RESOURCE="resource",CUSTOM_EVENT="customevent",CUSTOM_METRIC="custommetric",CUSTOM_LOG="customlog",CRASH="crash",SPEED_TEST="speedtest",CONSOLE$1="console",SPAN="span";function Debouncer(e,t){this.lastTimer=null,this.lastEvent=null,this.period=t||1e3,this.compare=function(){return!1},this.submit=e||NIL_FN}extend(Debouncer.prototype,{$schedule:function(e){isDefined(this.lastTimer)&&clearTimeout(this.lastTimer);var t=this;this.lastEvent=e,this.lastTimer=delay(function(){t.submit(t.lastEvent),t.lastEvent=null},this.period)},event:function(e){isDefined(this.lastEvent)&&(this.compare(this.lastEvent,e)?(e.info[DURATION]=e.info[START_TIME]-this.lastEvent.info[START_TIME],e.info[TIMESTAMP]=this.lastEvent.info[TIMESTAMP],e.info[START_TIME]=this.lastEvent.info[START_TIME],e.info.count=this.lastEvent.info.count+1):this.submit(this.lastEvent)),this.$schedule(e)},flush:function(){isDefined(this.lastTimer)&&(clearTimeout(this.lastTimer),this.lastTimer=null),isDefined(this.lastEvent)&&(this.submit(this.lastEvent),this.lastEvent=null)},equalsWith:function(e){"apply"in e&&(this.compare=e)}});var on$1=function(e,t,n,r){if(e&&e.addEventListener)return e.addEventListener(t,n,r);e&&e.attachEvent&&e.attachEvent("on"+t,n)},addListener=function(e,t,n,r){e&&e.addEventListener&&e.addEventListener(t,n,r)};function Notifier(){this.listeners={}}extend(Notifier.prototype,{on:function(e,n){var t;isDefined(e)&&isFunction(n)&&(isArray(t=e)||(t=[e]),forEach(t,bind(function(e){var t=this.listeners[e];(t=isDefined(t)?t:this.listeners[e]=[]).push(n)},this)))},emit:function(t){var e;isDefined(t)&&isDefined(t.t)&&(log(t),e=t.t,e=this.listeners[e],forEach(e,function(e){e(t)}))},remove:function(e){var t;isDefined(e)&&(isArray(t=e)||(t=[e]),forEach(t,bind(function(e){delete this.listeners[e]},this)))}});var notifier=new Notifier,notifierOn=bind(notifier.on,notifier),notifierEmit=bind(notifier.emit,notifier),notifierRemove=bind(notifier.remove,notifier);function Store(e){this.$i=extend({},e)}Store.prototype.i=function(e,t){return isDefined(e)?isDefined(t)?void(this.$i[e]=t):this.$i[e]:this.$i};var store=new Store,i$1=bind(store.i,store),isPageLoaded=function(){return!!i$1("page:load")};function getRandom$1(e){return e<0?NaN:e<=30?0|Math.random()*(1<<e):e<=53?(0|Math.random()*(1<<30))+(0|Math.random()*(1<<e-30))*(1<<30):NaN}function toHex$1(e,t){for(var n=e.toString(16),r=t-n.length,i="0";0<r;r>>>=1,i+=i)1&r&&(n=i+n);return n}function uuid(){return toHex$1(getRandom$1(32),8)+"-"+toHex$1(getRandom$1(16),4)+"-"+toHex$1(16384|getRandom$1(12),4)+"-"+toHex$1(32768|getRandom$1(14),4)+"-"+toHex$1(getRandom$1(48),12)}function uuidWithLength(e){return 16==e?toHex$1(getRandom$1(32),8)+toHex$1(getRandom$1(32),8):32==e?toHex$1(getRandom$1(32),8)+toHex$1(getRandom$1(32),8)+toHex$1(getRandom$1(32),8)+toHex$1(getRandom$1(32),8):void 0}function skyUuid(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){var t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)})}function buildViewID(){return Date.now().toString(36)+Math.ceil(1e3*Math.random()).toString(16)}function enBase64(e){for(var t=[],n=0,r=(e=(new TextEncoder).encode(e)).length;n<r;n+=4096)t.push(String.fromCharCode.bind(String).apply(null,e.subarray(n,n+4096)));return btoa(t.join(""))}function asyncGeneratorStep(e,t,n,r,i,a,o){try{var s=e[a](o),c=s.value}catch(e){return void n(e)}s.done?t(c):Promise.resolve(c).then(r,i)}function _asyncToGenerator(s){return function(){var e=this,o=arguments;return new Promise(function(t,n){var r=s.apply(e,o);function i(e){asyncGeneratorStep(r,t,n,i,a,"next",e)}function a(e){asyncGeneratorStep(r,t,n,i,a,"throw",e)}i(void 0)})}}function unwrapExports(e){e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")&&e.default}function createCommonjsModule(e,t){return e(t={exports:{}},t.exports),t.exports}var _typeof_1=createCommonjsModule(function(t){function n(e){return t.exports=n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},t.exports.__esModule=!0,t.exports.default=t.exports,n(e)}t.exports=n,t.exports.__esModule=!0,t.exports.default=t.exports}),regeneratorRuntime$1=(unwrapExports(_typeof_1),createCommonjsModule(function(R){var O=_typeof_1.default;function e(){R.exports=function(){return o},R.exports.__esModule=!0,R.exports.default=R.exports;var c,o={},e=Object.prototype,l=e.hasOwnProperty,d=Object.defineProperty||function(e,t,n){e[t]=n.value},t="function"==typeof Symbol?Symbol:{},r=t.iterator||"@@iterator",n=t.asyncIterator||"@@asyncIterator",i=t.toStringTag||"@@toStringTag";function a(e,t,n){return Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{a({},"")}catch(c){a=function(e,t,n){return e[t]=n}}function s(e,t,n,r){var i,a,o,s,t=t&&t.prototype instanceof _?t:_,t=Object.create(t.prototype),r=new I(r||[]);return d(t,"_invoke",{value:(i=e,a=n,o=r,s=f,function(e,t){if(s===h)throw new Error("Generator is already running");if(s===m){if("throw"===e)throw t;return{value:c,done:!0}}for(o.method=e,o.arg=t;;){var n=o.delegate;if(n){n=function e(t,n){var r=n.method,i=t.iterator[r];if(i===c)return n.delegate=null,"throw"===r&&t.iterator.return&&(n.method="return",n.arg=c,e(t,n),"throw"===n.method)||"return"!==r&&(n.method="throw",n.arg=new TypeError("The iterator does not provide a '"+r+"' method")),g;r=u(i,t.iterator,n.arg);if("throw"===r.type)return n.method="throw",n.arg=r.arg,n.delegate=null,g;i=r.arg;return i?i.done?(n[t.resultName]=i.value,n.next=t.nextLoc,"return"!==n.method&&(n.method="next",n.arg=c),n.delegate=null,g):i:(n.method="throw",n.arg=new TypeError("iterator result is not an object"),n.delegate=null,g)}(n,o);if(n){if(n===g)continue;return n}}if("next"===o.method)o.sent=o._sent=o.arg;else if("throw"===o.method){if(s===f)throw s=m,o.arg;o.dispatchException(o.arg)}else"return"===o.method&&o.abrupt("return",o.arg);s=h;n=u(i,a,o);if("normal"===n.type){if(s=o.done?m:p,n.arg===g)continue;return{value:n.arg,done:o.done}}"throw"===n.type&&(s=m,o.method="throw",o.arg=n.arg)}})}),t}function u(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}o.wrap=s;var f="suspendedStart",p="suspendedYield",h="executing",m="completed",g={};function _(){}function y(){}function E(){}var t={},v=(a(t,r,function(){return this}),Object.getPrototypeOf),v=v&&v(v(D([]))),w=(v&&v!==e&&l.call(v,r)&&(t=v),E.prototype=_.prototype=Object.create(t));function T(e){["next","throw","return"].forEach(function(t){a(e,t,function(e){return this._invoke(t,e)})})}function b(o,s){var t;d(this,"_invoke",{value:function(n,r){function e(){return new s(function(e,t){!function t(e,n,r,i){var a,e=u(o[e],o,n);if("throw"!==e.type)return(n=(a=e.arg).value)&&"object"==O(n)&&l.call(n,"__await")?s.resolve(n.__await).then(function(e){t("next",e,r,i)},function(e){t("throw",e,r,i)}):s.resolve(n).then(function(e){a.value=e,r(a)},function(e){return t("throw",e,r,i)});i(e.arg)}(n,r,e,t)})}return t=t?t.then(e,e):e()}})}function S(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function A(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function I(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(S,this),this.reset(!0)}function D(t){if(t||""===t){var n,e=t[r];if(e)return e.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length))return n=-1,(e=function e(){for(;++n<t.length;)if(l.call(t,n))return e.value=t[n],e.done=!1,e;return e.value=c,e.done=!0,e}).next=e}throw new TypeError(O(t)+" is not iterable")}return d(w,"constructor",{value:y.prototype=E,configurable:!0}),d(E,"constructor",{value:y,configurable:!0}),y.displayName=a(E,i,"GeneratorFunction"),o.isGeneratorFunction=function(e){e="function"==typeof e&&e.constructor;return!!e&&(e===y||"GeneratorFunction"===(e.displayName||e.name))},o.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,E):(e.__proto__=E,a(e,i,"GeneratorFunction")),e.prototype=Object.create(w),e},o.awrap=function(e){return{__await:e}},T(b.prototype),a(b.prototype,n,function(){return this}),o.AsyncIterator=b,o.async=function(e,t,n,r,i){void 0===i&&(i=Promise);var a=new b(s(e,t,n,r),i);return o.isGeneratorFunction(t)?a:a.next().then(function(e){return e.done?e.value:a.next()})},T(w),a(w,i,"Generator"),a(w,r,function(){return this}),a(w,"toString",function(){return"[object Generator]"}),o.keys=function(e){var t,n=Object(e),r=[];for(t in n)r.push(t);return r.reverse(),function e(){for(;r.length;){var t=r.pop();if(t in n)return e.value=t,e.done=!1,e}return e.done=!0,e}},o.values=D,I.prototype={constructor:I,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=c,this.done=!1,this.delegate=null,this.method="next",this.arg=c,this.tryEntries.forEach(A),!e)for(var t in this)"t"===t.charAt(0)&&l.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=c)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(n){if(this.done)throw n;var r=this;function e(e,t){return a.type="throw",a.arg=n,r.next=e,t&&(r.method="next",r.arg=c),!!t}for(var t=this.tryEntries.length-1;0<=t;--t){var i=this.tryEntries[t],a=i.completion;if("root"===i.tryLoc)return e("end");if(i.tryLoc<=this.prev){var o=l.call(i,"catchLoc"),s=l.call(i,"finallyLoc");if(o&&s){if(this.prev<i.catchLoc)return e(i.catchLoc,!0);if(this.prev<i.finallyLoc)return e(i.finallyLoc)}else if(o){if(this.prev<i.catchLoc)return e(i.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return e(i.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;0<=n;--n){var r=this.tryEntries[n];if(r.tryLoc<=this.prev&&l.call(r,"finallyLoc")&&this.prev<r.finallyLoc){var i=r;break}}var a=(i=i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc?null:i)?i.completion:{};return a.type=e,a.arg=t,i?(this.method="next",this.next=i.finallyLoc,g):this.complete(a)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),g},finish:function(e){for(var t=this.tryEntries.length-1;0<=t;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),A(n),g}},catch:function(e){for(var t=this.tryEntries.length-1;0<=t;--t){var n,r,i=this.tryEntries[t];if(i.tryLoc===e)return"throw"===(n=i.completion).type&&(r=n.arg,A(i)),r}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:D(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=c),g}},o}R.exports=e,R.exports.__esModule=!0,R.exports.default=R.exports})),runtime=(unwrapExports(regeneratorRuntime$1),regeneratorRuntime$1()),regenerator=runtime;try{regeneratorRuntime=runtime}catch(accidentalStrictMode){"object"==typeof globalThis?globalThis.regeneratorRuntime=runtime:Function("r","regeneratorRuntime = r")(runtime)}function _arrayLikeToArray$8(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function _arrayWithoutHoles(e){if(Array.isArray(e))return _arrayLikeToArray$8(e)}function _iterableToArray(e){if("undefined"!=typeof Symbol&&null!=e[Symbol.iterator]||null!=e["@@iterator"])return Array.from(e)}function _unsupportedIterableToArray$8(e,t){var n;if(e)return"string"==typeof e?_arrayLikeToArray$8(e,t):"Map"===(n="Object"===(n=Object.prototype.toString.call(e).slice(8,-1))&&e.constructor?e.constructor.name:n)||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?_arrayLikeToArray$8(e,t):void 0}function _nonIterableSpread(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _toConsumableArray(e){return _arrayWithoutHoles(e)||_iterableToArray(e)||_unsupportedIterableToArray$8(e)||_nonIterableSpread()}function _arrayWithHoles(e){if(Array.isArray(e))return e}function _iterableToArrayLimit(e,t){var n=null==e?null:"undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(null!=n){var r,i,a,o,s=[],c=!0,l=!1;try{if(a=(n=n.call(e)).next,0===t){if(Object(n)!==n)return;c=!1}else for(;!(c=(r=a.call(n)).done)&&(s.push(r.value),s.length!==t);c=!0);}catch(e){l=!0,i=e}finally{try{if(!c&&null!=n.return&&(o=n.return(),Object(o)!==o))return}finally{if(l)throw i}}return s}}function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _slicedToArray(e,t){return _arrayWithHoles(e)||_iterableToArrayLimit(e,t)||_unsupportedIterableToArray$8(e,t)||_nonIterableRest()}function awaitIfAsync(e,t){try{var n=e();isPromise(n)?n.then(function(e){return t(!0,e)},function(e){return t(!1,e)}):t(!0,n)}catch(e){t(!1,e)}}function isPromise(e){return e&&"function"==typeof e.then}function getMathFingerprint(){function e(){return 0}var t=Math,n=t.acos||e,r=t.acosh||e,i=t.asin||e,a=t.asinh||e,o=t.atanh||e,s=t.atan||e,c=t.sin||e,l=t.sinh||e,d=t.cos||e,u=t.cosh||e,f=t.tan||e,p=t.tanh||e,h=t.exp||e,m=t.expm1||e,g=t.log1p||e;return{acos:n(.12312423423423424),acosh:r(1e308),acoshPf:(n=1e154,t.log(n+t.sqrt(n*n-1))),asin:i(.12312423423423424),asinh:a(1),asinhPf:(r=1,t.log(r+t.sqrt(r*r+1))),atanh:o(.5),atanhPf:(n=.5,t.log((1+n)/(1-n))/2),atan:s(.5),sin:c(-1e300),sinh:l(1),sinhPf:(i=1,t.exp(i)-1/t.exp(i)/2),cos:d(10.000000000123),cosh:u(1),coshPf:(a=1,(t.exp(a)+1/t.exp(a))/2),tan:f(-1e300),tanh:p(1),tanhPf:(r=1,(t.exp(2*r)-1)/(t.exp(2*r)+1)),exp:h(1),expm1:m(1),expm1Pf:(o=1,t.exp(o)-1),log1p:g(10),log1pPf:(n=10,t.log(1+n)),powPI:(s=-100,t.pow(t.PI,s))}}function isHDR(){function e(e){return matchMedia("(dynamic-range: ".concat(e,")")).matches}return!!e("high")||!e("standard")&&void 0}function isMotionReduced(){function e(e){return matchMedia("(prefers-reduced-motion: ".concat(e,")")).matches}return!!e("reduce")||!e("no-preference")&&void 0}function getContrastPreference(){var e=-1,t=0,n=1,r=10;function i(e){return matchMedia("(prefers-contrast: ".concat(e,")")).matches}return i("no-preference")?t:i("high")||i("more")?n:i("low")||i("less")?e:i("forced")?r:void 0}function getMonochromeDepth(){if(matchMedia("(min-monochrome: 0)").matches){for(var e=0;e<=100;++e)if(matchMedia("(max-monochrome: ".concat(e,")")).matches)return e;throw new Error("Too high value")}}function areColorsForced(){function e(e){return matchMedia("(forced-colors: ".concat(e,")")).matches}return!!e("active")||!e("none")&&void 0}function areColorsInverted(){function e(e){return matchMedia("(inverted-colors: ".concat(e,")")).matches}return!!e("inverted")||!e("none")&&void 0}function getColorGamut(){for(var e=0,t=["rec2020","p3","srgb"];e<t.length;e++){var n=t[e];if(matchMedia("(color-gamut: ".concat(n,")")).matches)return n}}function areCookiesEnabled(){var e=document;try{e.cookie="cookietest=1; SameSite=Strict;";var t=-1!==e.cookie.indexOf("cookietest=");return e.cookie="cookietest=1; SameSite=Strict; expires=Thu, 01-Jan-1970 00:00:01 GMT",t}catch(e){return!1}}function getVendorFlavors(){for(var e=[],t=0,n=["chrome","safari","__crWeb","__gCrWeb","yandex","__yb","__ybro","__firefox__","__edgeTrackingPreventionStatistics","webkit","oprt","samsungAr","ucweb","UCShellJava","puffinDevice"];t<n.length;t++){var r=n[t],i=window[r];i&&"object"===_typeof(i)&&e.push(r)}return e.sort()}function getVendor(){return navigator.vendor||""}function getTouchSupport(){var t,e=navigator,n=0;void 0!==e.maxTouchPoints?n=toInt(e.maxTouchPoints):void 0!==e.msMaxTouchPoints&&(n=e.msMaxTouchPoints);try{document.createEvent("TouchEvent"),t=!0}catch(e){t=!1}return{maxTouchPoints:n,touchEvent:t,touchStart:"ontouchstart"in window}}function getPlugins(){var e=navigator.plugins;if(e){for(var t=[],n=0;n<e.length;++n){var r=e[n];if(r){for(var i=[],a=0;a<r.length;++a){var o=r[a];i.push({type:o.type,suffixes:o.suffixes})}t.push({name:r.name,description:r.description,mimeTypes:i})}}return t}}function getPlatform(){var e=navigator.platform;return"MacIntel"===e&&isWebKit()&&!isDesktopSafari()?isIPad()?"iPad":"iPhone":e}function isIPad(){var e;return"iPad"===navigator.platform||(e=(e=screen).width/e.height,2<=countTruthy(["MediaSource"in window,!!Element.prototype.webkitRequestFullscreen,.65<e&&e<1.53]))}function getOpenDatabase(){return!!window.openDatabase}function getCpuClass(){return navigator.cpuClass}function getIndexedDB(){if(!isTrident()&&!isEdgeHTML())try{return!!window.indexedDB}catch(e){return!0}}function isTrident(){var e=window,t=navigator;return 4<=countTruthy(["MSCSSMatrix"in e,"msSetImmediate"in e,"msIndexedDB"in e,"msMaxTouchPoints"in t,"msPointerEnabled"in t])}function isEdgeHTML(){var e=window,t=navigator;return 3<=countTruthy(["msWriteProfilerMark"in e,"MSStream"in e,"msLaunchUri"in t,"msSaveBlob"in t])&&!isTrident()}function getLocalStorage(){try{return!!window.localStorage}catch(e){return!0}}function getSessionStorage(){try{return!!window.sessionStorage}catch(e){return!0}}function getColorDepth(){return window.screen.colorDepth}function getHardwareConcurrency(){return replaceNaN(toInt(navigator.hardwareConcurrency),void 0)}function getScreenResolution(){function e(e){return replaceNaN(toInt(e),null)}var t=screen,t=[e(t.width),e(t.height)];return t.sort().reverse(),t}function toInt(e){return parseInt(e)}function getDeviceMemory(){return replaceNaN(toFloat(navigator.deviceMemory),void 0)}function replaceNaN(e,t){return"number"==typeof e&&isNaN(e)?t:e}function toFloat(e){return parseFloat(e)}function getFullscreenElement(){var e=document;return e.fullscreenElement||e.msFullscreenElement||e.mozFullScreenElement||e.webkitFullscreenElement}function exitFullscreen(){var e=document;return(e.exitFullscreen||e.msExitFullscreen||e.mozCancelFullScreen||e.webkitExitFullscreen).call(e)}function round(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:1;return 1<=Math.abs(t)?Math.round(e/t)*t:(t=1/t,Math.round(e*t)/t)}function isChromium86OrNewer(){var e=window;return 3<=countTruthy([!("MediaSettingsRange"in e),"RTCEncodedAudioFrame"in e,""+e.Intl=="[object Intl]",""+e.Reflect=="[object Reflect]"])}function isWebKit606OrNewer(){var e=window;return 3<=countTruthy(["DOMRectList"in e,"RTCPeerConnectionIceEvent"in e,"SVGGeometryElement"in e,"ontransitioncancel"in e])}function parseSimpleCssSelector(e){for(var t="Unexpected syntax '".concat(e,"'"),n=/^\s*([a-z-]*)(.*)$/i.exec(e),e=n[1]||void 0,r={},i=/([.:#][\w-]+|\[.+?\])/gi,a=function(e,t){r[e]=r[e]||[],r[e].push(t)};;){var o=i.exec(n[2]);if(!o)break;var s=o[0];switch(s[0]){case".":a("class",s.slice(1));break;case"#":a("id",s.slice(1));break;case"[":var c,l=/^\[([\w-]+)([~|^$*]?=("(.*?)"|([\w-]+)))?(\s+[is])?\]$/.exec(s);if(!l)throw new Error(t);a(l[1],null!=(c=null!=(c=l[4])?c:l[5])?c:"");break;default:throw new Error(t)}}return[e,r]}function isDesktopSafari(){var e=window;return 3<=countTruthy(["safari"in e,!("DeviceMotionEvent"in e),!("ongestureend"in e),!("standalone"in navigator)])}function isWebKit(){var e=window,t=navigator;return 4<=countTruthy(["ApplePayError"in e,"CSSPrimitiveValue"in e,"Counter"in e,0===t.vendor.indexOf("Apple"),"getStorageUpdates"in t,"WebKitMediaKeys"in e])}function countTruthy(e){return e.reduce(function(e,t){return e+(t?1:0)},0)}function isGecko(){var e,t=window;return 4<=countTruthy(["buildID"in navigator,"MozAppearance"in(null!=(e=null==(e=document.documentElement)?void 0:e.style)?e:{}),"onmozfullscreenchange"in t,"mozInnerScreenX"in t,"CSSMozDocumentRule"in t,"CanvasCaptureMediaStream"in t])}function isAndroid(){var e,t=isChromium(),n=isGecko();return!(!t&&!n)&&2<=countTruthy(["onorientationchange"in(e=window),"orientation"in e,t&&!("SharedWorker"in e),n&&/android/i.test(navigator.appVersion)])}function isChromium(){var e=window,t=navigator;return 5<=countTruthy(["webkitPersistentStorage"in t,"webkitTemporaryStorage"in t,0===t.vendor.indexOf("Google"),"webkitResolveLocalFileSystemURL"in e,"BatteryManager"in e,"webkitMediaStream"in e,"webkitSpeechGrammar"in e])}function suppressUnhandledRejectionWarning(e){e.then(void 0,function(){})}function includes(e,t){for(var n=0,r=e.length;n<r;++n)if(e[n]===t)return 1}function excludes(e,t){return!includes(e,t)}function forEachWithBreaks(e,t){return _forEachWithBreaks.apply(this,arguments)}function _forEachWithBreaks(){return(_forEachWithBreaks=_asyncToGenerator(regenerator.mark(function e(t,n){var r,i,a,o,s=arguments;return regenerator.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:r=2<s.length&&void 0!==s[2]?s[2]:16,i=Date.now(),a=0;case 3:if(a<t.length){if(n(t[a],a),o=Date.now(),i+r<=o)return i=o,e.next=10,wait(0);e.next=10}else e.next=13;break;case 10:++a,e.next=3;break;case 13:case"end":return e.stop()}},e)}))).apply(this,arguments)}function wait(t,n){return new Promise(function(e){return setTimeout(e,t,n)})}function x64Add(e,t){e=[e[0]>>>16,65535&e[0],e[1]>>>16,65535&e[1]],t=[t[0]>>>16,65535&t[0],t[1]>>>16,65535&t[1]];var n=[0,0,0,0];return n[3]+=e[3]+t[3],n[2]+=n[3]>>>16,n[3]&=65535,n[2]+=e[2]+t[2],n[1]+=n[2]>>>16,n[2]&=65535,n[1]+=e[1]+t[1],n[0]+=n[1]>>>16,n[1]&=65535,n[0]+=e[0]+t[0],n[0]&=65535,[n[0]<<16|n[1],n[2]<<16|n[3]]}function x64Multiply(e,t){e=[e[0]>>>16,65535&e[0],e[1]>>>16,65535&e[1]],t=[t[0]>>>16,65535&t[0],t[1]>>>16,65535&t[1]];var n=[0,0,0,0];return n[3]+=e[3]*t[3],n[2]+=n[3]>>>16,n[3]&=65535,n[2]+=e[2]*t[3],n[1]+=n[2]>>>16,n[2]&=65535,n[2]+=e[3]*t[2],n[1]+=n[2]>>>16,n[2]&=65535,n[1]+=e[1]*t[3],n[0]+=n[1]>>>16,n[1]&=65535,n[1]+=e[2]*t[2],n[0]+=n[1]>>>16,n[1]&=65535,n[1]+=e[3]*t[1],n[0]+=n[1]>>>16,n[1]&=65535,n[0]+=e[0]*t[3]+e[1]*t[2]+e[2]*t[1]+e[3]*t[0],n[0]&=65535,[n[0]<<16|n[1],n[2]<<16|n[3]]}function x64Rotl(e,t){return 32===(t%=64)?[e[1],e[0]]:t<32?[e[0]<<t|e[1]>>>32-t,e[1]<<t|e[0]>>>32-t]:[e[1]<<(t-=32)|e[0]>>>32-t,e[0]<<t|e[1]>>>32-t]}function x64LeftShift(e,t){return 0===(t%=64)?e:t<32?[e[0]<<t|e[1]>>>32-t,e[1]<<t]:[e[1]<<t-32,0]}function x64Xor(e,t){return[e[0]^t[0],e[1]^t[1]]}function x64Fmix(e){return e=x64Xor(e,[0,e[0]>>>1]),e=x64Xor(e=x64Multiply(e,[4283543511,3981806797]),[0,e[0]>>>1]),e=x64Xor(e=x64Multiply(e,[3301882366,444984403]),[0,e[0]>>>1])}function x64hash128(e,t){for(var n=(e=e||"").length%16,r=e.length-n,i=[0,t=t||0],a=[0,t],o=[0,0],s=[0,0],c=[2277735313,289559509],l=[1291169091,658871167],d=0;d<r;d+=16)o=[255&e.charCodeAt(d+4)|(255&e.charCodeAt(d+5))<<8|(255&e.charCodeAt(d+6))<<16|(255&e.charCodeAt(d+7))<<24,255&e.charCodeAt(d)|(255&e.charCodeAt(d+1))<<8|(255&e.charCodeAt(d+2))<<16|(255&e.charCodeAt(d+3))<<24],s=[255&e.charCodeAt(d+12)|(255&e.charCodeAt(d+13))<<8|(255&e.charCodeAt(d+14))<<16|(255&e.charCodeAt(d+15))<<24,255&e.charCodeAt(d+8)|(255&e.charCodeAt(d+9))<<8|(255&e.charCodeAt(d+10))<<16|(255&e.charCodeAt(d+11))<<24],o=x64Rotl(o=x64Multiply(o,c),31),i=x64Add(i=x64Rotl(i=x64Xor(i,o=x64Multiply(o,l)),27),a),i=x64Add(x64Multiply(i,[0,5]),[0,1390208809]),s=x64Rotl(s=x64Multiply(s,l),33),a=x64Add(a=x64Rotl(a=x64Xor(a,s=x64Multiply(s,c)),31),i),a=x64Add(x64Multiply(a,[0,5]),[0,944331445]);switch(o=[0,0],s=[0,0],n){case 15:s=x64Xor(s,x64LeftShift([0,e.charCodeAt(d+14)],48));case 14:s=x64Xor(s,x64LeftShift([0,e.charCodeAt(d+13)],40));case 13:s=x64Xor(s,x64LeftShift([0,e.charCodeAt(d+12)],32));case 12:s=x64Xor(s,x64LeftShift([0,e.charCodeAt(d+11)],24));case 11:s=x64Xor(s,x64LeftShift([0,e.charCodeAt(d+10)],16));case 10:s=x64Xor(s,x64LeftShift([0,e.charCodeAt(d+9)],8));case 9:s=x64Multiply(s=x64Xor(s,[0,e.charCodeAt(d+8)]),l),a=x64Xor(a,s=x64Multiply(s=x64Rotl(s,33),c));case 8:o=x64Xor(o,x64LeftShift([0,e.charCodeAt(d+7)],56));case 7:o=x64Xor(o,x64LeftShift([0,e.charCodeAt(d+6)],48));case 6:o=x64Xor(o,x64LeftShift([0,e.charCodeAt(d+5)],40));case 5:o=x64Xor(o,x64LeftShift([0,e.charCodeAt(d+4)],32));case 4:o=x64Xor(o,x64LeftShift([0,e.charCodeAt(d+3)],24));case 3:o=x64Xor(o,x64LeftShift([0,e.charCodeAt(d+2)],16));case 2:o=x64Xor(o,x64LeftShift([0,e.charCodeAt(d+1)],8));case 1:o=x64Multiply(o=x64Xor(o,[0,e.charCodeAt(d)]),c),i=x64Xor(i,o=x64Multiply(o=x64Rotl(o,31),l))}return i=x64Add(i=x64Xor(i,[0,e.length]),a=x64Xor(a,[0,e.length])),a=x64Add(a,i),i=x64Add(i=x64Fmix(i),a=x64Fmix(a)),a=x64Add(a,i),("00000000"+(i[0]>>>0).toString(16)).slice(-8)+("00000000"+(i[1]>>>0).toString(16)).slice(-8)+("00000000"+(a[0]>>>0).toString(16)).slice(-8)+("00000000"+(a[1]>>>0).toString(16)).slice(-8)}function _createForOfIteratorHelper$7(e,t){var n,r,i,a,o="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(o)return r=!(n=!0),{s:function(){o=o.call(e)},n:function(){var e=o.next();return n=e.done,e},e:function(e){r=!0,i=e},f:function(){try{n||null==o.return||o.return()}finally{if(r)throw i}}};if(Array.isArray(e)||(o=_unsupportedIterableToArray$7(e))||t&&e&&"number"==typeof e.length)return o&&(e=o),a=0,{s:t=function(){},n:function(){return a>=e.length?{done:!0}:{done:!1,value:e[a++]}},e:function(e){throw e},f:t};throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray$7(e,t){var n;if(e)return"string"==typeof e?_arrayLikeToArray$7(e,t):"Map"===(n="Object"===(n=Object.prototype.toString.call(e).slice(8,-1))&&e.constructor?e.constructor.name:n)||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?_arrayLikeToArray$7(e,t):void 0}function _arrayLikeToArray$7(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function forceShow(e){e.style.setProperty("display","block","important")}function printDebug(e,t){for(var n=0,r=Object.keys(e);n<r.length;n++){var i,a=r[n],o=("\n".concat(a,":"),_createForOfIteratorHelper$7(e[a]));try{for(o.s();!(i=o.n()).done;){var s=i.value;"\n  ".concat(t[s]?"🚫":"➡️"," ").concat(s)}}catch(e){o.e(e)}finally{o.f()}}}function getDomBlockers(e){return _getDomBlockers.apply(this,arguments)}function _getDomBlockers(){return(_getDomBlockers=_asyncToGenerator(regenerator.mark(function e(t){var n,r,i,a,o;return regenerator.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(n=t.debug,isApplicable()){e.next=3;break}return e.abrupt("return",void 0);case 3:return r=getFilters(),i=Object.keys(r),o=(o=[]).concat.apply(o,_toConsumableArray(i.map(function(e){return r[e]}))),e.next=8,getBlockedSelectors(o);case 8:return a=e.sent,n&&printDebug(r,a),(o=i.filter(function(e){e=r[e];return countTruthy(e.map(function(e){return a[e]}))>.6*e.length})).sort(),e.abrupt("return",o);case 13:case"end":return e.stop()}},e)}))).apply(this,arguments)}function isApplicable(){return isWebKit()||isAndroid()}function addStyleString(e,t){var n,r=_createForOfIteratorHelper$7(t.split(";"));try{for(r.s();!(n=r.n()).done;){var i,a,o,s,c=n.value,l=/^\s*([\w-]+)\s*:\s*(.+?)(\s*!([\w-]+))?\s*$/.exec(c);l&&(a=(i=_slicedToArray(l,5))[1],o=i[2],s=i[4],e.setProperty(a,o,s||""))}}catch(e){r.e(e)}finally{r.f()}}function selectorToElement(e){for(var e=_slicedToArray(parseSimpleCssSelector(e),2),t=e[0],n=e[1],r=document.createElement(null!=t?t:"div"),i=0,a=Object.keys(n);i<a.length;i++){var o=a[i],s=n[o].join(" ");"style"===o?addStyleString(r.style,s):r.setAttribute(o,s)}return r}function getBlockedSelectors(e){return _getBlockedSelectors.apply(this,arguments)}function _getBlockedSelectors(){return(_getBlockedSelectors=_asyncToGenerator(regenerator.mark(function e(t){var n,r,i,a,o,s,c,l,d;return regenerator.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:for(n=document,r=n.createElement("div"),i=new Array(t.length),a={},forceShow(r),o=0;o<t.length;++o)s=selectorToElement(t[o]),forceShow(c=n.createElement("div")),c.appendChild(s),r.appendChild(c),i[o]=s;case 6:if(n.body){e.next=11;break}return e.next=9,wait(50);case 9:e.next=6;break;case 11:n.body.appendChild(r);try{for(l=0;l<t.length;++l)i[l].offsetParent||(a[t[l]]=!0)}finally{null!=(d=r.parentNode)&&d.removeChild(r)}return e.abrupt("return",a);case 14:case"end":return e.stop()}},e)}))).apply(this,arguments)}function getFilters(){var e=atob;return{abpIndo:["#Iklan-Melayang","#Kolom-Iklan-728","#SidebarIklan-wrapper",e("YVt0aXRsZT0iN25hZ2EgcG9rZXIiIGld"),'[title="ALIENBOLA" i]'],abpvn:["#quangcaomb",e("Lmlvc0Fkc2lvc0Fkcy1sYXlvdXQ="),".quangcao",e("W2hyZWZePSJodHRwczovL3I4OC52bi8iXQ=="),e("W2hyZWZePSJodHRwczovL3piZXQudm4vIl0=")],adBlockFinland:[".mainostila",e("LnNwb25zb3JpdA=="),".ylamainos",e("YVtocmVmKj0iL2NsaWNrdGhyZ2guYXNwPyJd"),e("YVtocmVmXj0iaHR0cHM6Ly9hcHAucmVhZHBlYWsuY29tL2FkcyJd")],adBlockPersian:["#navbar_notice_50",".kadr",'TABLE[width="140px"]',"#divAgahi",e("I2FkMl9pbmxpbmU=")],adBlockWarningRemoval:["#adblock-honeypot",".adblocker-root",".wp_adblock_detect",e("LmhlYWRlci1ibG9ja2VkLWFk"),e("I2FkX2Jsb2NrZXI=")],adGuardAnnoyances:['amp-embed[type="zen"]',".hs-sosyal","#cookieconsentdiv",'div[class^="app_gdpr"]',".as-oil"],adGuardBase:[".BetterJsPopOverlay",e("I2FkXzMwMFgyNTA="),e("I2Jhbm5lcmZsb2F0MjI="),e("I2FkLWJhbm5lcg=="),e("I2NhbXBhaWduLWJhbm5lcg==")],adGuardChinese:[e("LlppX2FkX2FfSA=="),e("YVtocmVmKj0iL29kMDA1LmNvbSJd"),e("YVtocmVmKj0iLmh0aGJldDM0LmNvbSJd"),".qq_nr_lad","#widget-quan"],adGuardFrench:[e("I2Jsb2NrLXZpZXdzLWFkcy1zaWRlYmFyLWJsb2NrLWJsb2Nr"),"#pavePub",e("LmFkLWRlc2t0b3AtcmVjdGFuZ2xl"),".mobile_adhesion",".widgetadv"],adGuardGerman:[e("LmJhbm5lcml0ZW13ZXJidW5nX2hlYWRfMQ=="),e("LmJveHN0YXJ0d2VyYnVuZw=="),e("LndlcmJ1bmcz"),e("YVtocmVmXj0iaHR0cDovL3d3dy5laXMuZGUvaW5kZXgucGh0bWw/cmVmaWQ9Il0="),e("YVtocmVmXj0iaHR0cHM6Ly93d3cudGlwaWNvLmNvbS8/YWZmaWxpYXRlSWQ9Il0=")],adGuardJapanese:["#kauli_yad_1",e("YVtocmVmXj0iaHR0cDovL2FkMi50cmFmZmljZ2F0ZS5uZXQvIl0="),e("Ll9wb3BJbl9pbmZpbml0ZV9hZA=="),e("LmFkZ29vZ2xl"),e("LmFkX3JlZ3VsYXIz")],adGuardMobile:[e("YW1wLWF1dG8tYWRz"),e("LmFtcF9hZA=="),'amp-embed[type="24smi"]',"#mgid_iframe1",e("I2FkX2ludmlld19hcmVh")],adGuardRussian:[e("YVtocmVmXj0iaHR0cHM6Ly9hZC5sZXRtZWFkcy5jb20vIl0="),e("LnJlY2xhbWE="),'div[id^="smi2adblock"]',e("ZGl2W2lkXj0iQWRGb3hfYmFubmVyXyJd"),e("I2FkX3NxdWFyZQ==")],adGuardSocial:[e("YVtocmVmXj0iLy93d3cuc3R1bWJsZXVwb24uY29tL3N1Ym1pdD91cmw9Il0="),e("YVtocmVmXj0iLy90ZWxlZ3JhbS5tZS9zaGFyZS91cmw/Il0="),".etsy-tweet","#inlineShare",".popup-social"],adGuardSpanishPortuguese:["#barraPublicidade","#Publicidade","#publiEspecial","#queTooltip",e("W2hyZWZePSJodHRwOi8vYWRzLmdsaXNwYS5jb20vIl0=")],adGuardTrackingProtection:["#qoo-counter",e("YVtocmVmXj0iaHR0cDovL2NsaWNrLmhvdGxvZy5ydS8iXQ=="),e("YVtocmVmXj0iaHR0cDovL2hpdGNvdW50ZXIucnUvdG9wL3N0YXQucGhwIl0="),e("YVtocmVmXj0iaHR0cDovL3RvcC5tYWlsLnJ1L2p1bXAiXQ=="),"#top100counter"],adGuardTurkish:["#backkapat",e("I3Jla2xhbWk="),e("YVtocmVmXj0iaHR0cDovL2Fkc2Vydi5vbnRlay5jb20udHIvIl0="),e("YVtocmVmXj0iaHR0cDovL2l6bGVuemkuY29tL2NhbXBhaWduLyJd"),e("YVtocmVmXj0iaHR0cDovL3d3dy5pbnN0YWxsYWRzLm5ldC8iXQ==")],bulgarian:[e("dGQjZnJlZW5ldF90YWJsZV9hZHM="),"#ea_intext_div",".lapni-pop-over","#xenium_hot_offers",e("I25ld0Fk")],easyList:[e("I0FEX0NPTlRST0xfMjg="),e("LnNlY29uZC1wb3N0LWFkcy13cmFwcGVy"),".universalboxADVBOX03",e("LmFkdmVydGlzZW1lbnQtNzI4eDkw"),e("LnNxdWFyZV9hZHM=")],easyListChina:[e("YVtocmVmKj0iLndlbnNpeHVldGFuZy5jb20vIl0="),e("LmFwcGd1aWRlLXdyYXBbb25jbGljayo9ImJjZWJvcy5jb20iXQ=="),e("LmZyb250cGFnZUFkdk0="),"#taotaole","#aafoot.top_box"],easyListCookie:["#AdaCompliance.app-notice",".text-center.rgpd",".panel--cookie",".js-cookies-andromeda",".elxtr-consent"],easyListCzechSlovak:["#onlajny-stickers",e("I3Jla2xhbW5pLWJveA=="),e("LnJla2xhbWEtbWVnYWJvYXJk"),".sklik",e("W2lkXj0ic2tsaWtSZWtsYW1hIl0=")],easyListDutch:[e("I2FkdmVydGVudGll"),e("I3ZpcEFkbWFya3RCYW5uZXJCbG9jaw=="),".adstekst",e("YVtocmVmXj0iaHR0cHM6Ly94bHR1YmUubmwvY2xpY2svIl0="),"#semilo-lrectangle"],easyListGermany:[e("I0FkX1dpbjJkYXk="),e("I3dlcmJ1bmdzYm94MzAw"),e("YVtocmVmXj0iaHR0cDovL3d3dy5yb3RsaWNodGthcnRlaS5jb20vP3NjPSJd"),e("I3dlcmJ1bmdfd2lkZXNreXNjcmFwZXJfc2NyZWVu"),e("YVtocmVmXj0iaHR0cDovL2xhbmRpbmcucGFya3BsYXR6a2FydGVpLmNvbS8/YWc9Il0=")],easyListItaly:[e("LmJveF9hZHZfYW5udW5jaQ=="),".sb-box-pubbliredazionale",e("YVtocmVmXj0iaHR0cDovL2FmZmlsaWF6aW9uaWFkcy5zbmFpLml0LyJd"),e("YVtocmVmXj0iaHR0cHM6Ly9hZHNlcnZlci5odG1sLml0LyJd"),e("YVtocmVmXj0iaHR0cHM6Ly9hZmZpbGlhemlvbmlhZHMuc25haS5pdC8iXQ==")],easyListLithuania:[e("LnJla2xhbW9zX3RhcnBhcw=="),e("LnJla2xhbW9zX251b3JvZG9z"),e("aW1nW2FsdD0iUmVrbGFtaW5pcyBza3lkZWxpcyJd"),e("aW1nW2FsdD0iRGVkaWt1b3RpLmx0IHNlcnZlcmlhaSJd"),e("aW1nW2FsdD0iSG9zdGluZ2FzIFNlcnZlcmlhaS5sdCJd")],estonian:[e("QVtocmVmKj0iaHR0cDovL3BheTRyZXN1bHRzMjQuZXUiXQ==")],fanboyAnnoyances:["#feedback-tab","#taboola-below-article",".feedburnerFeedBlock",".widget-feedburner-counter",'[title="Subscribe to our blog"]'],fanboyAntiFacebook:[".util-bar-module-firefly-visible"],fanboyEnhancedTrackers:[".open.pushModal","#issuem-leaky-paywall-articles-zero-remaining-nag","#sovrn_container",'div[class$="-hide"][zoompage-fontsize][style="display: block;"]',".BlockNag__Card"],fanboySocial:[".td-tags-and-social-wrapper-box",".twitterContainer",".youtube-social",'a[title^="Like us on Facebook"]','img[alt^="Share on Digg"]'],frellwitSwedish:[e("YVtocmVmKj0iY2FzaW5vcHJvLnNlIl1bdGFyZ2V0PSJfYmxhbmsiXQ=="),e("YVtocmVmKj0iZG9rdG9yLXNlLm9uZWxpbmsubWUiXQ=="),"article.category-samarbete",e("ZGl2LmhvbGlkQWRz"),"ul.adsmodern"],greekAdBlock:[e("QVtocmVmKj0iYWRtYW4ub3RlbmV0LmdyL2NsaWNrPyJd"),e("QVtocmVmKj0iaHR0cDovL2F4aWFiYW5uZXJzLmV4b2R1cy5nci8iXQ=="),e("QVtocmVmKj0iaHR0cDovL2ludGVyYWN0aXZlLmZvcnRobmV0LmdyL2NsaWNrPyJd"),"DIV.agores300","TABLE.advright"],hungarian:["#cemp_doboz",".optimonk-iframe-container",e("LmFkX19tYWlu"),e("W2NsYXNzKj0iR29vZ2xlQWRzIl0="),"#hirdetesek_box"],iDontCareAboutCookies:['.alert-info[data-block-track*="CookieNotice"]',".ModuleTemplateCookieIndicator",".o--cookies--container",".cookie-msg-info-container","#cookies-policy-sticky"],icelandicAbp:[e("QVtocmVmXj0iL2ZyYW1ld29yay9yZXNvdXJjZXMvZm9ybXMvYWRzLmFzcHgiXQ==")],latvian:[e("YVtocmVmPSJodHRwOi8vd3d3LnNhbGlkemluaS5sdi8iXVtzdHlsZT0iZGlzcGxheTogYmxvY2s7IHdpZHRoOiAxMjBweDsgaGVpZ2h0OiA0MHB4OyBvdmVyZmxvdzogaGlkZGVuOyBwb3NpdGlvbjogcmVsYXRpdmU7Il0="),e("YVtocmVmPSJodHRwOi8vd3d3LnNhbGlkemluaS5sdi8iXVtzdHlsZT0iZGlzcGxheTogYmxvY2s7IHdpZHRoOiA4OHB4OyBoZWlnaHQ6IDMxcHg7IG92ZXJmbG93OiBoaWRkZW47IHBvc2l0aW9uOiByZWxhdGl2ZTsiXQ==")],listKr:[e("YVtocmVmKj0iLy9hZC5wbGFuYnBsdXMuY28ua3IvIl0="),e("I2xpdmVyZUFkV3JhcHBlcg=="),e("YVtocmVmKj0iLy9hZHYuaW1hZHJlcC5jby5rci8iXQ=="),e("aW5zLmZhc3R2aWV3LWFk"),".revenue_unit_item.dable"],listeAr:[e("LmdlbWluaUxCMUFk"),".right-and-left-sponsers",e("YVtocmVmKj0iLmFmbGFtLmluZm8iXQ=="),e("YVtocmVmKj0iYm9vcmFxLm9yZyJd"),e("YVtocmVmKj0iZHViaXp6bGUuY29tL2FyLz91dG1fc291cmNlPSJd")],listeFr:[e("YVtocmVmXj0iaHR0cDovL3Byb21vLnZhZG9yLmNvbS8iXQ=="),e("I2FkY29udGFpbmVyX3JlY2hlcmNoZQ=="),e("YVtocmVmKj0id2Vib3JhbWEuZnIvZmNnaS1iaW4vIl0="),".site-pub-interstitiel",'div[id^="crt-"][data-criteo-id]'],officialPolish:["#ceneo-placeholder-ceneo-12",e("W2hyZWZePSJodHRwczovL2FmZi5zZW5kaHViLnBsLyJd"),e("YVtocmVmXj0iaHR0cDovL2Fkdm1hbmFnZXIudGVjaGZ1bi5wbC9yZWRpcmVjdC8iXQ=="),e("YVtocmVmXj0iaHR0cDovL3d3dy50cml6ZXIucGwvP3V0bV9zb3VyY2UiXQ=="),e("ZGl2I3NrYXBpZWNfYWQ=")],ro:[e("YVtocmVmXj0iLy9hZmZ0cmsuYWx0ZXgucm8vQ291bnRlci9DbGljayJd"),'a[href^="/magazin/"]',e("YVtocmVmXj0iaHR0cHM6Ly9ibGFja2ZyaWRheXNhbGVzLnJvL3Ryay9zaG9wLyJd"),e("YVtocmVmXj0iaHR0cHM6Ly9ldmVudC4ycGVyZm9ybWFudC5jb20vZXZlbnRzL2NsaWNrIl0="),e("YVtocmVmXj0iaHR0cHM6Ly9sLnByb2ZpdHNoYXJlLnJvLyJd")],ruAd:[e("YVtocmVmKj0iLy9mZWJyYXJlLnJ1LyJd"),e("YVtocmVmKj0iLy91dGltZy5ydS8iXQ=="),e("YVtocmVmKj0iOi8vY2hpa2lkaWtpLnJ1Il0="),"#pgeldiz",".yandex-rtb-block"],thaiAds:["a[href*=macau-uta-popup]",e("I2Fkcy1nb29nbGUtbWlkZGxlX3JlY3RhbmdsZS1ncm91cA=="),e("LmFkczMwMHM="),".bumq",".img-kosana"],webAnnoyancesUltralist:["#mod-social-share-2","#social-tools",e("LmN0cGwtZnVsbGJhbm5lcg=="),".zergnet-recommend",".yt.btn-link.btn-md.btn"]}}function _createForOfIteratorHelper$6(e,t){var n,r,i,a,o="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(o)return r=!(n=!0),{s:function(){o=o.call(e)},n:function(){var e=o.next();return n=e.done,e},e:function(e){r=!0,i=e},f:function(){try{n||null==o.return||o.return()}finally{if(r)throw i}}};if(Array.isArray(e)||(o=_unsupportedIterableToArray$6(e))||t&&e&&"number"==typeof e.length)return o&&(e=o),a=0,{s:t=function(){},n:function(){return a>=e.length?{done:!0}:{done:!1,value:e[a++]}},e:function(e){throw e},f:t};throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray$6(e,t){var n;if(e)return"string"==typeof e?_arrayLikeToArray$6(e,t):"Map"===(n="Object"===(n=Object.prototype.toString.call(e).slice(8,-1))&&e.constructor?e.constructor.name:n)||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?_arrayLikeToArray$6(e,t):void 0}function _arrayLikeToArray$6(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var testString="mmMwWLliI0O&1",textSize="48px",baseFonts=["monospace","sans-serif","serif"],fontList=["sans-serif-thin","ARNO PRO","Agency FB","Arabic Typesetting","Arial Unicode MS","AvantGarde Bk BT","BankGothic Md BT","Batang","Bitstream Vera Sans Mono","Calibri","Century","Century Gothic","Clarendon","EUROSTILE","Franklin Gothic","Futura Bk BT","Futura Md BT","GOTHAM","Gill Sans","HELV","Haettenschweiler","Helvetica Neue","Humanst521 BT","Leelawadee","Letter Gothic","Levenim MT","Lucida Bright","Lucida Sans","Menlo","MS Mincho","MS Outlook","MS Reference Specialty","MS UI Gothic","MT Extra","MYRIAD PRO","Marlett","Meiryo UI","Microsoft Uighur","Minion Pro","Monotype Corsiva","PMingLiU","Pristina","SCRIPTINA","Segoe UI Light","Serifa","SimHei","Small Fonts","Staccato222 BT","TRAJAN PRO","Univers CE 55 Medium","Vrinda","ZWAdobeF"];function getFonts(){return withIframe(function(e,t){var r=t.document,t=r.body,i=(t.style.fontSize=textSize,r.createElement("div")),a={},o={},n=function(e){var t=r.createElement("span"),n=t.style;return n.position="absolute",n.top="0",n.left="0",n.fontFamily=e,t.textContent=testString,i.appendChild(t),t},s=function(e,t){return n("'".concat(e,"',").concat(t))},c=baseFonts.map(n),l=function(){var e,n={},t=_createForOfIteratorHelper$6(fontList);try{for(t.s();!(e=t.n()).done;)!function(){var t=e.value;n[t]=baseFonts.map(function(e){return s(t,e)})}()}catch(e){t.e(e)}finally{t.f()}return n}();t.appendChild(i);for(var d=0;d<baseFonts.length;d++)a[baseFonts[d]]=c[d].offsetWidth,o[baseFonts[d]]=c[d].offsetHeight;return fontList.filter(function(e){return n=l[e],baseFonts.some(function(e,t){return n[t].offsetWidth!==a[e]||n[t].offsetHeight!==o[e]});var n})})}function withIframe(e,t){return _withIframe.apply(this,arguments)}function _withIframe(){return(_withIframe=_asyncToGenerator(regenerator.mark(function e(t,a){var n,o,s,r,i=arguments;return regenerator.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:n=2<i.length&&void 0!==i[2]?i[2]:50,o=document;case 2:if(o.body){e.next=7;break}return e.next=5,wait(n);case 5:e.next=2;break;case 7:return s=o.createElement("iframe"),e.prev=8,e.next=11,new Promise(function(e,t){var n=!1,r=function(){n=!0,e()},i=(s.onload=r,s.onerror=function(e){n=!0,t(e)},s.style);i.setProperty("display","block","important"),i.position="absolute",i.top="0",i.left="0",i.visibility="hidden",a&&"srcdoc"in s?s.srcdoc=a:s.src="about:blank",o.body.appendChild(s),function e(){var t;n||("complete"===(null==(t=s.contentWindow)||null==(t=t.document)?void 0:t.readyState)?r():setTimeout(e,10))}()});case 11:if(null!=(r=s.contentWindow)&&null!=(r=r.document)&&r.body){e.next=16;break}return e.next=14,wait(n);case 14:e.next=11;break;case 16:return e.next=18,t(s,s.contentWindow);case 18:return e.abrupt("return",e.sent);case 19:return e.prev=19,null!=(r=s.parentNode)&&r.removeChild(s),e.finish(19);case 22:case"end":return e.stop()}},e,null,[[8,,19,22]])}))).apply(this,arguments)}var SpecialFingerprint={KnownToSuspend:-1,NotSupported:-2,Timeout:-3},InnerErrorName={Timeout:"timeout",Suspended:"suspended"},screenFrameBackup,screenFrameSizeTimeoutId;function getAudioFingerprint(){var e=window,e=e.OfflineAudioContext||e.webkitOfflineAudioContext;if(!e)return SpecialFingerprint.NotSupported;if(doesCurrentBrowserSuspendAudioContext())return SpecialFingerprint.KnownToSuspend;var e=new e(1,5e3,44100),t=e.createOscillator(),n=(t.type="triangle",t.frequency.value=1e4,e.createDynamicsCompressor());n.threshold.value=-50,n.knee.value=40,n.ratio.value=12,n.attack.value=0,n.release.value=.25,t.connect(n),n.connect(e.destination),t.start(0);var n=_slicedToArray(startRenderingAudio(e),2),t=n[0],r=n[1],i=t.then(function(e){return getHash(e.getChannelData(0).subarray(4500))},function(e){if(e.name===InnerErrorName.Timeout||e.name===InnerErrorName.Suspended)return SpecialFingerprint.Timeout;throw e});return suppressUnhandledRejectionWarning(i),function(){return r(),i}}function startRenderingAudio(s){var e=function(){};return[new Promise(function(t,n){var r=!1,i=0,a=0,o=(s.oncomplete=function(e){return t(e.renderedBuffer)},function(){setTimeout(function(){return n(makeInnerError(InnerErrorName.Timeout))},Math.min(500,a+5e3-Date.now()))});(function e(){try{switch(s.startRendering(),s.state){case"running":a=Date.now(),r&&o();break;case"suspended":document.hidden||i++,r&&3<=i?n(makeInnerError(InnerErrorName.Suspended)):setTimeout(e,500)}}catch(e){n(e)}})(),e=function(){r||(r=!0,0<a&&o())}}),e]}function getHash(e){for(var t=0,n=0;n<e.length;++n)t+=Math.abs(e[n]);return t}function makeInnerError(e){var t=new Error(e);return t.name=e,t}function doesCurrentBrowserSuspendAudioContext(){return isWebKit()&&!isDesktopSafari()&&!isWebKit606OrNewer()}var roundingPrecision=10,screenFrameCheckInterval=2500;function getRoundedScreenFrame(){var r=getScreenFrame();return _asyncToGenerator(regenerator.mark(function e(){var t,n;return regenerator.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,r();case 2:return t=e.sent,n=function(e){return null===e?null:round(e,roundingPrecision)},e.abrupt("return",[n(t[0]),n(t[1]),n(t[2]),n(t[3])]);case 5:case"end":return e.stop()}},e)}))}function getScreenFrame(){return watchScreenFrame(),_asyncToGenerator(regenerator.mark(function e(){var t;return regenerator.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(isFrameSizeNull(t=getCurrentScreenFrame())){if(screenFrameBackup)return e.abrupt("return",_toConsumableArray(screenFrameBackup));e.next=4}else e.next=8;break;case 4:if(getFullscreenElement())return e.next=7,exitFullscreen();e.next=8;break;case 7:t=getCurrentScreenFrame();case 8:return isFrameSizeNull(t)||(screenFrameBackup=t),e.abrupt("return",t);case 10:case"end":return e.stop()}},e)}))}function watchScreenFrame(){void 0===screenFrameSizeTimeoutId&&function e(){var t=getCurrentScreenFrame();screenFrameSizeTimeoutId=isFrameSizeNull(t)?setTimeout(e,screenFrameCheckInterval):void(screenFrameBackup=t)}()}function getCurrentScreenFrame(){var e=screen;return[replaceNaN(toFloat(e.availTop),null),replaceNaN(toFloat(e.width)-toFloat(e.availWidth)-replaceNaN(toFloat(e.availLeft),0),null),replaceNaN(toFloat(e.height)-toFloat(e.availHeight)-replaceNaN(toFloat(e.availTop),0),null),replaceNaN(toFloat(e.availLeft),null)]}function isFrameSizeNull(e){for(var t=0;t<4;++t)if(e[t])return;return 1}function getLanguages(){var e=navigator,t=[],n=e.language||e.userLanguage||e.browserLanguage||e.systemLanguage;return void 0!==n&&t.push([n]),Array.isArray(e.languages)?isChromium()&&isChromium86OrNewer()||t.push(e.languages):"string"==typeof e.languages&&(n=e.languages)&&t.push(n.split(",")),t}function getTimezone(){var e=null==(e=window.Intl)?void 0:e.DateTimeFormat;if(e){e=(new e).resolvedOptions().timeZone;if(e)return e}e=-getTimezoneOffset();return"UTC".concat(0<=e?"+":"").concat(Math.abs(e))}function getTimezoneOffset(){var e=(new Date).getFullYear();return Math.max(toFloat(new Date(e,0,1).getTimezoneOffset()),toFloat(new Date(e,6,1).getTimezoneOffset()))}function getCanvasFingerprint(){var e,t,n=!1,r=_slicedToArray(makeCanvasContext(),2),i=r[0],r=r[1];return t=isSupported(i,r)?(n=doesSupportWinding(r),renderTextImage(i,r),(t=canvasToString(i))!==canvasToString(i)?e="unstable":(e=t,renderGeometryImage(i,r),canvasToString(i))):e="",{winding:n,geometry:t,text:e}}function makeCanvasContext(){var e=document.createElement("canvas");return e.width=1,e.height=1,[e,e.getContext("2d")]}function isSupported(e,t){return t&&e.toDataURL}function doesSupportWinding(e){return e.rect(0,0,10,10),e.rect(2,2,6,6),!e.isPointInPath(5,5,"evenodd")}function renderTextImage(e,t){e.width=240,e.height=60,t.textBaseline="alphabetic",t.fillStyle="#f60",t.fillRect(100,1,62,20),t.fillStyle="#069",t.font='11pt "Times New Roman"';e="Cwm fjordbank gly ".concat(String.fromCharCode(55357,56835));t.fillText(e,2,15),t.fillStyle="rgba(102, 204, 0, 0.2)",t.font="18pt Arial",t.fillText(e,4,45)}function canvasToString(e){return e.toDataURL()}function renderGeometryImage(e,t){e.width=122,e.height=110,t.globalCompositeOperation="multiply";for(var n=0,r=[["#f2f",40,40],["#2ff",80,40],["#ff2",60,80]];n<r.length;n++){var i=_slicedToArray(r[n],3),a=i[0],o=i[1],i=i[2];t.fillStyle=a,t.beginPath(),t.arc(o,i,40,0,2*Math.PI,!0),t.closePath(),t.fill()}t.fillStyle="#f9c",t.arc(60,60,60,0,2*Math.PI,!0),t.arc(60,60,20,0,2*Math.PI,!0),t.fill("evenodd")}function _createForOfIteratorHelper$5(e,t){var n,r,i,a,o="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(o)return r=!(n=!0),{s:function(){o=o.call(e)},n:function(){var e=o.next();return n=e.done,e},e:function(e){r=!0,i=e},f:function(){try{n||null==o.return||o.return()}finally{if(r)throw i}}};if(Array.isArray(e)||(o=_unsupportedIterableToArray$5(e))||t&&e&&"number"==typeof e.length)return o&&(e=o),a=0,{s:t=function(){},n:function(){return a>=e.length?{done:!0}:{done:!1,value:e[a++]}},e:function(e){throw e},f:t};throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray$5(e,t){var n;if(e)return"string"==typeof e?_arrayLikeToArray$5(e,t):"Map"===(n="Object"===(n=Object.prototype.toString.call(e).slice(8,-1))&&e.constructor?e.constructor.name:n)||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?_arrayLikeToArray$5(e,t):void 0}function _arrayLikeToArray$5(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function load(e,t){return makeAgent(loadBuiltinSources({debug:t}),t)}function makeAgent(r,i){return{get:function(n){return _asyncToGenerator(regenerator.mark(function e(){var t;return regenerator.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=3,r();case 3:return t=e.sent,t=makeLazyGetResult(t),i||null!=n&&n.debug,e.abrupt("return",t);case 7:case"end":return e.stop()}},e)}))()}}}function makeLazyGetResult(e){var t,n=getConfidence(e);return{get visitorId(){return t=void 0===t?hashComponents(this.components):t},set visitorId(e){t=e},confidence:n,components:e}}function hashComponents(e){return x64hash128(componentsToCanonicalString(e))}function componentsToCanonicalString(e){var t,n="",r=_createForOfIteratorHelper$5(Object.keys(e).sort());try{for(r.s();!(t=r.n()).done;){var i=t.value,a=e[i],o=a.error?"error":JSON.stringify(a.value);n+="".concat(n?"|":"").concat(i.replace(/([:|\\])/g,"\\$1"),":").concat(o)}}catch(e){r.e(e)}finally{r.f()}return n}var commentTemplate="$ if upgrade to Pro: https://fpjs.dev/pro";function getConfidence(e){var e=getOpenConfidenceScore(e),t=deriveProConfidenceScore(e);return{score:e,comment:commentTemplate.replace(/\$/g,"".concat(t))}}function getOpenConfidenceScore(e){return isAndroid()?.4:isWebKit()?isDesktopSafari()?.5:.3:(e=e.platform.value||"",/^Win/.test(e)?.6:/^Mac/.test(e)?.5:.7)}function deriveProConfidenceScore(e){return round(.99+.01*e,1e-4)}function getOsCpu(){return navigator.oscpu}var sources={fonts:getFonts,domBlockers:getDomBlockers,audio:getAudioFingerprint,screenFrame:getRoundedScreenFrame,osCpu:getOsCpu,languages:getLanguages,colorDepth:getColorDepth,deviceMemory:getDeviceMemory,screenResolution:getScreenResolution,hardwareConcurrency:getHardwareConcurrency,timezone:getTimezone,sessionStorage:getSessionStorage,localStorage:getLocalStorage,indexedDB:getIndexedDB,openDatabase:getOpenDatabase,cpuClass:getCpuClass,platform:getPlatform,plugins:getPlugins,canvas:getCanvasFingerprint,touchSupport:getTouchSupport,vendor:getVendor,vendorFlavors:getVendorFlavors,cookiesEnabled:areCookiesEnabled,colorGamut:getColorGamut,invertedColors:areColorsInverted,forcedColors:areColorsForced,monochrome:getMonochromeDepth,contrast:getContrastPreference,reducedMotion:isMotionReduced,hdr:isHDR,math:getMathFingerprint};function loadBuiltinSources(e){return loadSources(sources,e,[])}function loadSources(n,r,t){var s=Object.keys(n).filter(function(e){return excludes(t,e)}),c=Array(s.length);return forEachWithBreaks(s,function(e,t){c[t]=loadSource(n[e],r)}),function(){var e=_asyncToGenerator(regenerator.mark(function e(){var i,t,n,r,a,o;return regenerator.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:i={},t=_createForOfIteratorHelper$5(s);try{for(t.s();!(n=t.n()).done;)r=n.value,i[r]=void 0}catch(e){t.e(e)}finally{t.f()}a=Array(s.length),o=regenerator.mark(function e(){var r;return regenerator.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:return r=!0,e.next=3,forEachWithBreaks(s,function(t,e){var n;a[e]||(c[e]?(suppressUnhandledRejectionWarning(n=c[e]().then(function(e){return i[t]=e})),a[e]=n):r=!1)});case 3:if(r)return e.abrupt("return",1);e.next=5;break;case 5:return e.next=7,wait(1);case 7:case"end":return e.stop()}},e)});case 5:return e.delegateYield(o(),"t0",6);case 6:if(e.t0)return e.abrupt("break",10);e.next=8;break;case 8:e.next=5;break;case 10:return e.next=12,Promise.all(a);case 12:return e.abrupt("return",i);case 13:case"end":return e.stop()}},e)}));return function(){return e.apply(this,arguments)}}()}function isFinalResultLoaded(e){return"function"!=typeof e}function ensureErrorWithMessage(e){return e&&"object"===_typeof(e)&&"message"in e?e:{message:e}}function loadSource(e,t){var n=new Promise(function(a){var o=Date.now();awaitIfAsync(e.bind(null,t),function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r,i=Date.now()-o;return t[0]?isFinalResultLoaded(r=t[1])?a(function(){return{value:r,duration:i}}):void a(function(){return new Promise(function(t){var n=Date.now();awaitIfAsync(r,function(){var e=i+Date.now()-n;if(arguments.length<=0||!arguments[0])return t({error:ensureErrorWithMessage(arguments.length<=1?void 0:arguments[1]),duration:e});t({value:arguments.length<=1?void 0:arguments[1],duration:e})})})}):a(function(){return{error:ensureErrorWithMessage(t[1]),duration:i}})})});return suppressUnhandledRejectionWarning(n),function(){return n.then(function(e){return e()})}}function _defineProperties(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,_toPropertyKey(r.key),r)}}function _createClass(e,t,n){return t&&_defineProperties(e.prototype,t),n&&_defineProperties(e,n),Object.defineProperty(e,"prototype",{writable:!1}),e}function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var LIBVERSION="0.7.28",EMPTY="",UNKNOWN="?",FUNC_TYPE="function",UNDEF_TYPE="undefined",OBJ_TYPE="object",STR_TYPE="string",MAJOR="major",MODEL="model",NAME$1="name",TYPE$2="type",VENDOR="vendor",VERSION="version",ARCHITECTURE="architecture",CONSOLE="console",MOBILE="mobile",TABLET="tablet",SMARTTV="smarttv",WEARABLE="wearable",EMBEDDED="embedded",UA_MAX_LENGTH=255,util={extend:function(e,t){var n,r={};for(n in e)t[n]&&t[n].length%2==0?r[n]=t[n].concat(e[n]):r[n]=e[n];return r},has:function(e,t){return _typeof(e)===STR_TYPE&&-1!==t.toLowerCase().indexOf(e.toLowerCase())},lowerize:function(e){return e.toLowerCase()},major:function(e){return _typeof(e)===STR_TYPE?e.replace(/[^\d\.]/g,"").split(".")[0]:void 0},trim:function(e,t){return e=e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,""),_typeof(t)===UNDEF_TYPE?e:e.substring(0,UA_MAX_LENGTH)}},mapper={rgx:function(e,t){for(var n,r,i,a,o,s=0;s<t.length&&!a;){for(var c=t[s],l=t[s+1],d=n=0;d<c.length&&!a;)if(a=c[d++].exec(e))for(r=0;r<l.length;r++)o=a[++n],_typeof(i=l[r])===OBJ_TYPE&&0<i.length?2==i.length?_typeof(i[1])==FUNC_TYPE?this[i[0]]=i[1].call(this,o):this[i[0]]=i[1]:3==i.length?_typeof(i[1])!==FUNC_TYPE||i[1].exec&&i[1].test?this[i[0]]=o?o.replace(i[1],i[2]):void 0:this[i[0]]=o?i[1].call(this,o,i[2]):void 0:4==i.length&&(this[i[0]]=o?i[3].call(this,o.replace(i[1],i[2])):void 0):this[i]=o||void 0;s+=2}},str:function(e,t){for(var n in t)if(_typeof(t[n])===OBJ_TYPE&&0<t[n].length){for(var r=0;r<t[n].length;r++)if(util.has(t[n][r],e))return n===UNKNOWN?void 0:n}else if(util.has(t[n],e))return n===UNKNOWN?void 0:n;return e}},maps={browser:{oldSafari:{version:{"1.0":"/8",1.2:"/1",1.3:"/3","2.0":"/412","2.0.2":"/416","2.0.3":"/417","2.0.4":"/419","?":"/"}},oldEdge:{version:{.1:"12.",21:"13.",31:"14.",39:"15.",41:"16.",42:"17.",44:"18."}}},os:{windows:{version:{ME:"4.90","NT 3.11":"NT3.51","NT 4.0":"NT4.0",2e3:"NT 5.0",XP:["NT 5.1","NT 5.2"],Vista:"NT 6.0",7:"NT 6.1",8:"NT 6.2",8.1:"NT 6.3",10:["NT 6.4","NT 10.0"],RT:"ARM"}}}},regexes={browser:[[/\b(?:crmo|crios)\/([\w\.]+)/i],[VERSION,[NAME$1,"Chrome"]],[/edg(?:e|ios|a)?\/([\w\.]+)/i],[VERSION,[NAME$1,"Edge"]],[/(opera\smini)\/([\w\.-]+)/i,/(opera\s[mobiletab]{3,6})\b.+version\/([\w\.-]+)/i,/(opera)(?:.+version\/|[\/\s]+)([\w\.]+)/i],[NAME$1,VERSION],[/opios[\/\s]+([\w\.]+)/i],[VERSION,[NAME$1,"Opera Mini"]],[/\sopr\/([\w\.]+)/i],[VERSION,[NAME$1,"Opera"]],[/(kindle)\/([\w\.]+)/i,/(lunascape|maxthon|netfront|jasmine|blazer)[\/\s]?([\w\.]*)/i,/(avant\s|iemobile|slim)(?:browser)?[\/\s]?([\w\.]*)/i,/(ba?idubrowser)[\/\s]?([\w\.]+)/i,/(?:ms|\()(ie)\s([\w\.]+)/i,/(flock|rockmelt|midori|epiphany|silk|skyfire|ovibrowser|bolt|iron|vivaldi|iridium|phantomjs|bowser|quark|qupzilla|falkon)\/([\w\.-]+)/i,/(rekonq|puffin|brave|whale|qqbrowserlite|qq)\/([\w\.]+)/i,/(weibo)__([\d\.]+)/i],[NAME$1,VERSION],[/(?:[\s\/]uc?\s?browser|(?:juc.+)ucweb)[\/\s]?([\w\.]+)/i],[VERSION,[NAME$1,"UCBrowser"]],[/(?:windowswechat)?\sqbcore\/([\w\.]+)\b.*(?:windowswechat)?/i],[VERSION,[NAME$1,"WeChat(Win) Desktop"]],[/micromessenger\/([\w\.]+)/i],[VERSION,[NAME$1,"WeChat"]],[/konqueror\/([\w\.]+)/i],[VERSION,[NAME$1,"Konqueror"]],[/trident.+rv[:\s]([\w\.]{1,9})\b.+like\sgecko/i],[VERSION,[NAME$1,"IE"]],[/yabrowser\/([\w\.]+)/i],[VERSION,[NAME$1,"Yandex"]],[/(avast|avg)\/([\w\.]+)/i],[[NAME$1,/(.+)/,"$1 Secure Browser"],VERSION],[/focus\/([\w\.]+)/i],[VERSION,[NAME$1,"Firefox Focus"]],[/opt\/([\w\.]+)/i],[VERSION,[NAME$1,"Opera Touch"]],[/coc_coc_browser\/([\w\.]+)/i],[VERSION,[NAME$1,"Coc Coc"]],[/dolfin\/([\w\.]+)/i],[VERSION,[NAME$1,"Dolphin"]],[/coast\/([\w\.]+)/i],[VERSION,[NAME$1,"Opera Coast"]],[/xiaomi\/miuibrowser\/([\w\.]+)/i],[VERSION,[NAME$1,"MIUI Browser"]],[/fxios\/([\w\.-]+)/i],[VERSION,[NAME$1,"Firefox"]],[/(qihu|qhbrowser|qihoobrowser|360browser)/i],[[NAME$1,"360 Browser"]],[/(oculus|samsung|sailfish)browser\/([\w\.]+)/i],[[NAME$1,/(.+)/,"$1 Browser"],VERSION],[/(comodo_dragon)\/([\w\.]+)/i],[[NAME$1,/_/g," "],VERSION],[/\s(electron)\/([\w\.]+)\ssafari/i,/(tesla)(?:\sqtcarbrowser|\/(20[12]\d\.[\w\.-]+))/i,/m?(qqbrowser|baiduboxapp|2345Explorer)[\/\s]?([\w\.]+)/i],[NAME$1,VERSION],[/(MetaSr)[\/\s]?([\w\.]+)/i,/(LBBROWSER)/i],[NAME$1],[/;fbav\/([\w\.]+);/i],[VERSION,[NAME$1,"Facebook"]],[/FBAN\/FBIOS|FB_IAB\/FB4A/i],[[NAME$1,"Facebook"]],[/safari\s(line)\/([\w\.]+)/i,/\b(line)\/([\w\.]+)\/iab/i,/(chromium|instagram)[\/\s]([\w\.-]+)/i],[NAME$1,VERSION],[/\bgsa\/([\w\.]+)\s.*safari\//i],[VERSION,[NAME$1,"GSA"]],[/headlesschrome(?:\/([\w\.]+)|\s)/i],[VERSION,[NAME$1,"Chrome Headless"]],[/\swv\).+(chrome)\/([\w\.]+)/i],[[NAME$1,"Chrome WebView"],VERSION],[/droid.+\sversion\/([\w\.]+)\b.+(?:mobile\ssafari|safari)/i],[VERSION,[NAME$1,"Android Browser"]],[/(chrome|omniweb|arora|[tizenoka]{5}\s?browser)\/v?([\w\.]+)/i],[NAME$1,VERSION],[/version\/([\w\.]+)\s.*mobile\/\w+\s(safari)/i],[VERSION,[NAME$1,"Mobile Safari"]],[/version\/([\w\.]+)\s.*(mobile\s?safari|safari)/i],[VERSION,NAME$1],[/webkit.+?(mobile\s?safari|safari)(\/[\w\.]+)/i],[NAME$1,[VERSION,mapper.str,maps.browser.oldSafari.version]],[/(webkit|khtml)\/([\w\.]+)/i],[NAME$1,VERSION],[/(navigator|netscape)\/([\w\.-]+)/i],[[NAME$1,"Netscape"],VERSION],[/ile\svr;\srv:([\w\.]+)\).+firefox/i],[VERSION,[NAME$1,"Firefox Reality"]],[/ekiohf.+(flow)\/([\w\.]+)/i,/(swiftfox)/i,/(icedragon|iceweasel|camino|chimera|fennec|maemo\sbrowser|minimo|conkeror)[\/\s]?([\w\.\+]+)/i,/(firefox|seamonkey|k-meleon|icecat|iceape|firebird|phoenix|palemoon|basilisk|waterfox)\/([\w\.-]+)$/i,/(firefox)\/([\w\.]+)\s[\w\s\-]+\/[\w\.]+$/i,/(mozilla)\/([\w\.]+)\s.+rv\:.+gecko\/\d+/i,/(polaris|lynx|dillo|icab|doris|amaya|w3m|netsurf|sleipnir)[\/\s]?([\w\.]+)/i,/(links)\s\(([\w\.]+)/i,/(gobrowser)\/?([\w\.]*)/i,/(ice\s?browser)\/v?([\w\._]+)/i,/(mosaic)[\/\s]([\w\.]+)/i],[NAME$1,VERSION]],cpu:[[/(?:(amd|x(?:(?:86|64)[_-])?|wow|win)64)[;\)]/i],[[ARCHITECTURE,"amd64"]],[/(ia32(?=;))/i],[[ARCHITECTURE,util.lowerize]],[/((?:i[346]|x)86)[;\)]/i],[[ARCHITECTURE,"ia32"]],[/\b(aarch64|armv?8e?l?)\b/i],[[ARCHITECTURE,"arm64"]],[/\b(arm(?:v[67])?ht?n?[fl]p?)\b/i],[[ARCHITECTURE,"armhf"]],[/windows\s(ce|mobile);\sppc;/i],[[ARCHITECTURE,"arm"]],[/((?:ppc|powerpc)(?:64)?)(?:\smac|;|\))/i],[[ARCHITECTURE,/ower/,"",util.lowerize]],[/(sun4\w)[;\)]/i],[[ARCHITECTURE,"sparc"]],[/((?:avr32|ia64(?=;))|68k(?=\))|\barm(?:64|(?=v(?:[1-7]|[5-7]1)l?|;|eabi))|(?=atmel\s)avr|(?:irix|mips|sparc)(?:64)?\b|pa-risc)/i],[[ARCHITECTURE,util.lowerize]]],device:[[/\b(sch-i[89]0\d|shw-m380s|sm-[pt]\w{2,4}|gt-[pn]\d{2,4}|sgh-t8[56]9|nexus\s10)/i],[MODEL,[VENDOR,"Samsung"],[TYPE$2,TABLET]],[/\b((?:s[cgp]h|gt|sm)-\w+|galaxy\snexus)/i,/\ssamsung[\s-]([\w-]+)/i,/sec-(sgh\w+)/i],[MODEL,[VENDOR,"Samsung"],[TYPE$2,MOBILE]],[/\((ip(?:hone|od)[\s\w]*);/i],[MODEL,[VENDOR,"Apple"],[TYPE$2,MOBILE]],[/\((ipad);[\w\s\),;-]+apple/i,/applecoremedia\/[\w\.]+\s\((ipad)/i,/\b(ipad)\d\d?,\d\d?[;\]].+ios/i],[MODEL,[VENDOR,"Apple"],[TYPE$2,TABLET]],[/\b((?:agr|ags[23]|bah2?|sht?)-a?[lw]\d{2})/i],[MODEL,[VENDOR,"Huawei"],[TYPE$2,TABLET]],[/d\/huawei([\w\s-]+)[;\)]/i,/\b(nexus\s6p|vog-[at]?l\d\d|ane-[at]?l[x\d]\d|eml-a?l\d\da?|lya-[at]?l\d[\dc]|clt-a?l\d\di?|ele-l\d\d)/i,/\b(\w{2,4}-[atu][ln][01259][019])[;\)\s]/i],[MODEL,[VENDOR,"Huawei"],[TYPE$2,MOBILE]],[/\b(poco[\s\w]+)(?:\sbuild|\))/i,/\b;\s(\w+)\sbuild\/hm\1/i,/\b(hm[\s\-_]?note?[\s_]?(?:\d\w)?)\sbuild/i,/\b(redmi[\s\-_]?(?:note|k)?[\w\s_]+)(?:\sbuild|\))/i,/\b(mi[\s\-_]?(?:a\d|one|one[\s_]plus|note lte)?[\s_]?(?:\d?\w?)[\s_]?(?:plus)?)\sbuild/i],[[MODEL,/_/g," "],[VENDOR,"Xiaomi"],[TYPE$2,MOBILE]],[/\b(mi[\s\-_]?(?:pad)(?:[\w\s_]+))(?:\sbuild|\))/i],[[MODEL,/_/g," "],[VENDOR,"Xiaomi"],[TYPE$2,TABLET]],[/;\s(\w+)\sbuild.+\soppo/i,/\s(cph[12]\d{3}|p(?:af|c[al]|d\w|e[ar])[mt]\d0|x9007)\b/i],[MODEL,[VENDOR,"OPPO"],[TYPE$2,MOBILE]],[/\svivo\s(\w+)(?:\sbuild|\))/i,/\s(v[12]\d{3}\w?[at])(?:\sbuild|;)/i],[MODEL,[VENDOR,"Vivo"],[TYPE$2,MOBILE]],[/\s(rmx[12]\d{3})(?:\sbuild|;)/i],[MODEL,[VENDOR,"Realme"],[TYPE$2,MOBILE]],[/\s(milestone|droid(?:[2-4x]|\s(?:bionic|x2|pro|razr))?:?(\s4g)?)\b[\w\s]+build\//i,/\smot(?:orola)?[\s-](\w*)/i,/((?:moto[\s\w\(\)]+|xt\d{3,4}|nexus\s6)(?=\sbuild|\)))/i],[MODEL,[VENDOR,"Motorola"],[TYPE$2,MOBILE]],[/\s(mz60\d|xoom[\s2]{0,2})\sbuild\//i],[MODEL,[VENDOR,"Motorola"],[TYPE$2,TABLET]],[/((?=lg)?[vl]k\-?\d{3})\sbuild|\s3\.[\s\w;-]{10}lg?-([06cv9]{3,4})/i],[MODEL,[VENDOR,"LG"],[TYPE$2,TABLET]],[/(lm-?f100[nv]?|nexus\s[45])/i,/lg[e;\s\/-]+((?!browser|netcast)\w+)/i,/\blg(\-?[\d\w]+)\sbuild/i],[MODEL,[VENDOR,"LG"],[TYPE$2,MOBILE]],[/(ideatab[\w\-\s]+)/i,/lenovo\s?(s(?:5000|6000)(?:[\w-]+)|tab(?:[\s\w]+)|yt[\d\w-]{6}|tb[\d\w-]{6})/i],[MODEL,[VENDOR,"Lenovo"],[TYPE$2,TABLET]],[/(?:maemo|nokia).*(n900|lumia\s\d+)/i,/nokia[\s_-]?([\w\.-]*)/i],[[MODEL,/_/g," "],[VENDOR,"Nokia"],[TYPE$2,MOBILE]],[/droid.+;\s(pixel\sc)[\s)]/i],[MODEL,[VENDOR,"Google"],[TYPE$2,TABLET]],[/droid.+;\s(pixel[\s\daxl]{0,6})(?:\sbuild|\))/i],[MODEL,[VENDOR,"Google"],[TYPE$2,MOBILE]],[/droid.+\s([c-g]\d{4}|so[-l]\w+|xq-a\w[4-7][12])(?=\sbuild\/|\).+chrome\/(?![1-6]{0,1}\d\.))/i],[MODEL,[VENDOR,"Sony"],[TYPE$2,MOBILE]],[/sony\stablet\s[ps]\sbuild\//i,/(?:sony)?sgp\w+(?:\sbuild\/|\))/i],[[MODEL,"Xperia Tablet"],[VENDOR,"Sony"],[TYPE$2,TABLET]],[/\s(kb2005|in20[12]5|be20[12][59])\b/i,/\ba000(1)\sbuild/i,/\boneplus\s(a\d{4})[\s)]/i],[MODEL,[VENDOR,"OnePlus"],[TYPE$2,MOBILE]],[/(alexa)webm/i,/(kf[a-z]{2}wi)(\sbuild\/|\))/i,/(kf[a-z]+)(\sbuild\/|\)).+silk\//i],[MODEL,[VENDOR,"Amazon"],[TYPE$2,TABLET]],[/(sd|kf)[0349hijorstuw]+(\sbuild\/|\)).+silk\//i],[[MODEL,"Fire Phone"],[VENDOR,"Amazon"],[TYPE$2,MOBILE]],[/\((playbook);[\w\s\),;-]+(rim)/i],[MODEL,VENDOR,[TYPE$2,TABLET]],[/((?:bb[a-f]|st[hv])100-\d)/i,/\(bb10;\s(\w+)/i],[MODEL,[VENDOR,"BlackBerry"],[TYPE$2,MOBILE]],[/(?:\b|asus_)(transfo[prime\s]{4,10}\s\w+|eeepc|slider\s\w+|nexus\s7|padfone|p00[cj])/i],[MODEL,[VENDOR,"ASUS"],[TYPE$2,TABLET]],[/\s(z[es]6[027][01][km][ls]|zenfone\s\d\w?)\b/i],[MODEL,[VENDOR,"ASUS"],[TYPE$2,MOBILE]],[/(nexus\s9)/i],[MODEL,[VENDOR,"HTC"],[TYPE$2,TABLET]],[/(htc)[;_\s-]{1,2}([\w\s]+(?=\)|\sbuild)|\w+)/i,/(zte)-(\w*)/i,/(alcatel|geeksphone|nexian|panasonic|(?=;\s)sony)[_\s-]?([\w-]*)/i],[VENDOR,[MODEL,/_/g," "],[TYPE$2,MOBILE]],[/droid[x\d\.\s;]+\s([ab][1-7]\-?[0178a]\d\d?)/i],[MODEL,[VENDOR,"Acer"],[TYPE$2,TABLET]],[/droid.+;\s(m[1-5]\snote)\sbuild/i,/\bmz-([\w-]{2,})/i],[MODEL,[VENDOR,"Meizu"],[TYPE$2,MOBILE]],[/(blackberry|benq|palm(?=\-)|sonyericsson|acer|asus|dell|meizu|motorola|polytron)[\s_-]?([\w-]*)/i,/(hp)\s([\w\s]+\w)/i,/(asus)-?(\w+)/i,/(microsoft);\s(lumia[\s\w]+)/i,/(lenovo)[_\s-]?([\w-]+)/i,/linux;.+(jolla);/i,/droid.+;\s(oppo)\s?([\w\s]+)\sbuild/i],[VENDOR,MODEL,[TYPE$2,MOBILE]],[/(archos)\s(gamepad2?)/i,/(hp).+(touchpad(?!.+tablet)|tablet)/i,/(kindle)\/([\w\.]+)/i,/\s(nook)[\w\s]+build\/(\w+)/i,/(dell)\s(strea[kpr\s\d]*[\dko])/i,/[;\/]\s?(le[\s\-]+pan)[\s\-]+(\w{1,9})\sbuild/i,/[;\/]\s?(trinity)[\-\s]*(t\d{3})\sbuild/i,/\b(gigaset)[\s\-]+(q\w{1,9})\sbuild/i,/\b(vodafone)\s([\w\s]+)(?:\)|\sbuild)/i],[VENDOR,MODEL,[TYPE$2,TABLET]],[/\s(surface\sduo)\s/i],[MODEL,[VENDOR,"Microsoft"],[TYPE$2,TABLET]],[/droid\s[\d\.]+;\s(fp\du?)\sbuild/i],[MODEL,[VENDOR,"Fairphone"],[TYPE$2,MOBILE]],[/\s(u304aa)\sbuild/i],[MODEL,[VENDOR,"AT&T"],[TYPE$2,MOBILE]],[/sie-(\w*)/i],[MODEL,[VENDOR,"Siemens"],[TYPE$2,MOBILE]],[/[;\/]\s?(rct\w+)\sbuild/i],[MODEL,[VENDOR,"RCA"],[TYPE$2,TABLET]],[/[;\/\s](venue[\d\s]{2,7})\sbuild/i],[MODEL,[VENDOR,"Dell"],[TYPE$2,TABLET]],[/[;\/]\s?(q(?:mv|ta)\w+)\sbuild/i],[MODEL,[VENDOR,"Verizon"],[TYPE$2,TABLET]],[/[;\/]\s(?:barnes[&\s]+noble\s|bn[rt])([\w\s\+]*)\sbuild/i],[MODEL,[VENDOR,"Barnes & Noble"],[TYPE$2,TABLET]],[/[;\/]\s(tm\d{3}\w+)\sbuild/i],[MODEL,[VENDOR,"NuVision"],[TYPE$2,TABLET]],[/;\s(k88)\sbuild/i],[MODEL,[VENDOR,"ZTE"],[TYPE$2,TABLET]],[/;\s(nx\d{3}j)\sbuild/i],[MODEL,[VENDOR,"ZTE"],[TYPE$2,MOBILE]],[/[;\/]\s?(gen\d{3})\sbuild.*49h/i],[MODEL,[VENDOR,"Swiss"],[TYPE$2,MOBILE]],[/[;\/]\s?(zur\d{3})\sbuild/i],[MODEL,[VENDOR,"Swiss"],[TYPE$2,TABLET]],[/[;\/]\s?((zeki)?tb.*\b)\sbuild/i],[MODEL,[VENDOR,"Zeki"],[TYPE$2,TABLET]],[/[;\/]\s([yr]\d{2})\sbuild/i,/[;\/]\s(dragon[\-\s]+touch\s|dt)(\w{5})\sbuild/i],[[VENDOR,"Dragon Touch"],MODEL,[TYPE$2,TABLET]],[/[;\/]\s?(ns-?\w{0,9})\sbuild/i],[MODEL,[VENDOR,"Insignia"],[TYPE$2,TABLET]],[/[;\/]\s?((nxa|Next)-?\w{0,9})\sbuild/i],[MODEL,[VENDOR,"NextBook"],[TYPE$2,TABLET]],[/[;\/]\s?(xtreme\_)?(v(1[045]|2[015]|[3469]0|7[05]))\sbuild/i],[[VENDOR,"Voice"],MODEL,[TYPE$2,MOBILE]],[/[;\/]\s?(lvtel\-)?(v1[12])\sbuild/i],[[VENDOR,"LvTel"],MODEL,[TYPE$2,MOBILE]],[/;\s(ph-1)\s/i],[MODEL,[VENDOR,"Essential"],[TYPE$2,MOBILE]],[/[;\/]\s?(v(100md|700na|7011|917g).*\b)\sbuild/i],[MODEL,[VENDOR,"Envizen"],[TYPE$2,TABLET]],[/[;\/]\s?(trio[\s\w\-\.]+)\sbuild/i],[MODEL,[VENDOR,"MachSpeed"],[TYPE$2,TABLET]],[/[;\/]\s?tu_(1491)\sbuild/i],[MODEL,[VENDOR,"Rotor"],[TYPE$2,TABLET]],[/(shield[\w\s]+)\sbuild/i],[MODEL,[VENDOR,"Nvidia"],[TYPE$2,TABLET]],[/(sprint)\s(\w+)/i],[VENDOR,MODEL,[TYPE$2,MOBILE]],[/(kin\.[onetw]{3})/i],[[MODEL,/\./g," "],[VENDOR,"Microsoft"],[TYPE$2,MOBILE]],[/droid\s[\d\.]+;\s(cc6666?|et5[16]|mc[239][23]x?|vc8[03]x?)\)/i],[MODEL,[VENDOR,"Zebra"],[TYPE$2,TABLET]],[/droid\s[\d\.]+;\s(ec30|ps20|tc[2-8]\d[kx])\)/i],[MODEL,[VENDOR,"Zebra"],[TYPE$2,MOBILE]],[/\s(ouya)\s/i,/(nintendo)\s([wids3utch]+)/i],[VENDOR,MODEL,[TYPE$2,CONSOLE]],[/droid.+;\s(shield)\sbuild/i],[MODEL,[VENDOR,"Nvidia"],[TYPE$2,CONSOLE]],[/(playstation\s[345portablevi]+)/i],[MODEL,[VENDOR,"Sony"],[TYPE$2,CONSOLE]],[/[\s\(;](xbox(?:\sone)?(?!;\sxbox))[\s\);]/i],[MODEL,[VENDOR,"Microsoft"],[TYPE$2,CONSOLE]],[/smart-tv.+(samsung)/i],[VENDOR,[TYPE$2,SMARTTV]],[/hbbtv.+maple;(\d+)/i],[[MODEL,/^/,"SmartTV"],[VENDOR,"Samsung"],[TYPE$2,SMARTTV]],[/(?:linux;\snetcast.+smarttv|lg\snetcast\.tv-201\d)/i],[[VENDOR,"LG"],[TYPE$2,SMARTTV]],[/(apple)\s?tv/i],[VENDOR,[MODEL,"Apple TV"],[TYPE$2,SMARTTV]],[/crkey/i],[[MODEL,"Chromecast"],[VENDOR,"Google"],[TYPE$2,SMARTTV]],[/droid.+aft([\w])(\sbuild\/|\))/i],[MODEL,[VENDOR,"Amazon"],[TYPE$2,SMARTTV]],[/\(dtv[\);].+(aquos)/i],[MODEL,[VENDOR,"Sharp"],[TYPE$2,SMARTTV]],[/hbbtv\/\d+\.\d+\.\d+\s+\([\w\s]*;\s*(\w[^;]*);([^;]*)/i],[[VENDOR,util.trim],[MODEL,util.trim],[TYPE$2,SMARTTV]],[/[\s\/\(](android\s|smart[-\s]?|opera\s)tv[;\)\s]/i],[[TYPE$2,SMARTTV]],[/((pebble))app\/[\d\.]+\s/i],[VENDOR,MODEL,[TYPE$2,WEARABLE]],[/droid.+;\s(glass)\s\d/i],[MODEL,[VENDOR,"Google"],[TYPE$2,WEARABLE]],[/droid\s[\d\.]+;\s(wt63?0{2,3})\)/i],[MODEL,[VENDOR,"Zebra"],[TYPE$2,WEARABLE]],[/(tesla)(?:\sqtcarbrowser|\/20[12]\d\.[\w\.-]+)/i],[VENDOR,[TYPE$2,EMBEDDED]],[/droid .+?; ([^;]+?)(?: build|\) applewebkit).+? mobile safari/i],[MODEL,[TYPE$2,MOBILE]],[/droid .+?;\s([^;]+?)(?: build|\) applewebkit).+?(?! mobile) safari/i],[MODEL,[TYPE$2,TABLET]],[/\s(tablet|tab)[;\/]/i,/\s(mobile)(?:[;\/]|\ssafari)/i],[[TYPE$2,util.lowerize]],[/(android[\w\.\s\-]{0,9});.+build/i],[MODEL,[VENDOR,"Generic"]],[/(phone)/i],[[TYPE$2,MOBILE]]],engine:[[/windows.+\sedge\/([\w\.]+)/i],[VERSION,[NAME$1,"EdgeHTML"]],[/webkit\/537\.36.+chrome\/(?!27)([\w\.]+)/i],[VERSION,[NAME$1,"Blink"]],[/(presto)\/([\w\.]+)/i,/(webkit|trident|netfront|netsurf|amaya|lynx|w3m|goanna)\/([\w\.]+)/i,/ekioh(flow)\/([\w\.]+)/i,/(khtml|tasman|links)[\/\s]\(?([\w\.]+)/i,/(icab)[\/\s]([23]\.[\d\.]+)/i],[NAME$1,VERSION],[/rv\:([\w\.]{1,9})\b.+(gecko)/i],[VERSION,NAME$1]],os:[[/microsoft\s(windows)\s(vista|xp)/i],[NAME$1,VERSION],[/(windows)\snt\s6\.2;\s(arm)/i,/(windows\sphone(?:\sos)*)[\s\/]?([\d\.\s\w]*)/i,/(windows\smobile|windows)[\s\/]?([ntce\d\.\s]+\w)(?!.+xbox)/i],[NAME$1,[VERSION,mapper.str,maps.os.windows.version]],[/(win(?=3|9|n)|win\s9x\s)([nt\d\.]+)/i],[[NAME$1,"Windows"],[VERSION,mapper.str,maps.os.windows.version]],[/ip[honead]{2,4}\b(?:.*os\s([\w]+)\slike\smac|;\sopera)/i,/cfnetwork\/.+darwin/i],[[VERSION,/_/g,"."],[NAME$1,"iOS"]],[/(mac\sos\sx)\s?([\w\s\.]*)/i,/(macintosh|mac(?=_powerpc)\s)(?!.+haiku)/i],[[NAME$1,"Mac OS"],[VERSION,/_/g,"."]],[/(android|webos|palm\sos|qnx|bada|rim\stablet\sos|meego|sailfish|contiki)[\/\s-]?([\w\.]*)/i,/(blackberry)\w*\/([\w\.]*)/i,/(tizen|kaios)[\/\s]([\w\.]+)/i,/\((series40);/i],[NAME$1,VERSION],[/\(bb(10);/i],[VERSION,[NAME$1,"BlackBerry"]],[/(?:symbian\s?os|symbos|s60(?=;)|series60)[\/\s-]?([\w\.]*)/i],[VERSION,[NAME$1,"Symbian"]],[/mozilla.+\(mobile;.+gecko.+firefox/i],[[NAME$1,"Firefox OS"]],[/web0s;.+rt(tv)/i,/\b(?:hp)?wos(?:browser)?\/([\w\.]+)/i],[VERSION,[NAME$1,"webOS"]],[/crkey\/([\d\.]+)/i],[VERSION,[NAME$1,"Chromecast"]],[/(cros)\s[\w]+\s([\w\.]+\w)/i],[[NAME$1,"Chromium OS"],VERSION],[/(nintendo|playstation)\s([wids345portablevuch]+)/i,/(xbox);\s+xbox\s([^\);]+)/i,/(mint)[\/\s\(\)]?(\w*)/i,/(mageia|vectorlinux)[;\s]/i,/(joli|[kxln]?ubuntu|debian|suse|opensuse|gentoo|arch(?=\slinux)|slackware|fedora|mandriva|centos|pclinuxos|redhat|zenwalk|linpus|raspbian)(?:\sgnu\/linux)?(?:\slinux)?[\/\s-]?(?!chrom|package)([\w\.-]*)/i,/(hurd|linux)\s?([\w\.]*)/i,/(gnu)\s?([\w\.]*)/i,/\s([frentopc-]{0,4}bsd|dragonfly)\s?(?!amd|[ix346]{1,2}86)([\w\.]*)/i,/(haiku)\s(\w+)/i],[NAME$1,VERSION],[/(sunos)\s?([\w\.\d]*)/i],[[NAME$1,"Solaris"],VERSION],[/((?:open)?solaris)[\/\s-]?([\w\.]*)/i,/(aix)\s((\d)(?=\.|\)|\s)[\w\.])*/i,/(plan\s9|minix|beos|os\/2|amigaos|morphos|risc\sos|openvms|fuchsia)/i,/(unix)\s?([\w\.]*)/i],[NAME$1,VERSION]]},_ua="undefined"!=typeof window&&window.navigator&&window.navigator.userAgent?window.navigator.userAgent:EMPTY,UAParser=_createClass(function e(){_classCallCheck(this,e),this.VERSION=LIBVERSION,this.BROWSER={NAME:NAME$1,MAJOR:MAJOR,VERSION:VERSION},this.CPU={ARCHITECTURE:ARCHITECTURE},this.DEVICE={MODEL:MODEL,VENDOR:VENDOR,TYPE:TYPE$2,CONSOLE:CONSOLE,MOBILE:MOBILE,SMARTTV:SMARTTV,TABLET:TABLET,WEARABLE:WEARABLE,EMBEDDED:EMBEDDED},this.ENGINE={NAME:NAME$1,VERSION:VERSION},this.OS={NAME:NAME$1,VERSION:VERSION}}),global_CLIENT=(UAParser.prototype.getBrowser=function(){var e={name:void 0,version:void 0};return mapper.rgx.call(e,_ua,regexes.browser),e.major=util.major(e.version),e},UAParser.prototype.getCPU=function(){var e={architecture:void 0};return mapper.rgx.call(e,_ua,regexes.cpu),e},UAParser.prototype.getDevice=function(){var e={vendor:void 0,model:void 0,type:void 0};return mapper.rgx.call(e,_ua,regexes.device),e},UAParser.prototype.getEngine=function(){var e={name:void 0,version:void 0};return mapper.rgx.call(e,_ua,regexes.engine),e},UAParser.prototype.getOS=function(){var e={name:void 0,version:void 0};return mapper.rgx.call(e,_ua,regexes.os),e},UAParser.prototype.getUA=function(){return _ua},UAParser.prototype.getResult=function(){return{ua:this.getUA(),browser:this.getBrowser(),engine:this.getEngine(),os:this.getOS(),device:this.getDevice(),cpu:this.getCPU()}},"web"),CLIENTID="br-client",deviceInfo={},uaInfo={},pringID;function PrivateMode(){return _PrivateMode.apply(this,arguments)}function _PrivateMode(){return(_PrivateMode=_asyncToGenerator(regenerator.mark(function e(){var t,n,r;return regenerator.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(t=!1,window.webkitRequestFileSystem&&window.webkitRequestFileSystem(window.TEMPORARY,1,function(){t=!1},function(e){t=!0}),"undefined"!=typeof InstallTrigger)return n=function(){return new Promise(function(t,e){try{var n=indexedDB.open("test");n.onerror=function(){return t(!0)},n.onsuccess=function(){return t(!1)}}catch(e){return t(!1)}})},e.next=6,n();e.next=8;break;case 6:n=e.sent,t=!!n;case 8:if(-1<window.navigator.userAgent.indexOf("Edge")||-1<window.navigator.userAgent.indexOf("Chrome")){if("storage"in navigator&&"estimate"in navigator.storage)return e.next=12,navigator.storage.estimate();e.next=18}else e.next=19;break;case 12:(r=e.sent).usage,r=r.quota,t=r<12e8,e.next=19;break;case 18:t=!1;case 19:return e.abrupt("return",t);case 20:case"end":return e.stop()}},e)}))).apply(this,arguments)}document&&document.addEventListener("readystatechange",function(){var t=_asyncToGenerator(regenerator.mark(function e(t){return regenerator.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if("complete"!==t.target.readyState);else if(checkBrowserIsOld())return e.prev=3,e.next=6,PrivateMode();e.next=13;break;case 6:e.sent&&new Promise(function(e,t){return e(load())}).then(function(e){return e.get()}).then(function(e){pringID=e.visitorId,isDefined(window.bonreeRUM)&&(window.bonreeRUM.fingerId=pringID,set(CLIENTID,pringID,cookieCfg))}),e.next=13;break;case 10:e.prev=10,e.t0=e.catch(3);case 13:case"end":return e.stop()}},e,null,[[3,10]])}));return function(e){return t.apply(this,arguments)}}());var getClientID=function(){var e;return isDefined(setting.deviceId)?setting.deviceId:(e=get(CLIENTID),isReadable(e)||(e=isDefined(setting.deviceId)?setting.deviceId:pringID||uuid(),set(CLIENTID,e,cookieCfg)),e)};function parserUA(){if(isEmpty(uaInfo)||!UAParser)try{var e="ios"!==global_CLIENT&&UAParser&&new UAParser;uaInfo=e&&e.getResult()||{}}catch(e){}}function getUserAgent(){try{var e={Chrome:/Chrome/,IE:/MSIE/,Firefox:/Firefox/,Opera:/Presto/,Safari:/Version\/([\d.]+).*Safari/,360:/360SE/,QQBrowswe:/QQ/},t={iPhone:/iPhone/,iPad:/iPad/,Android:/Android/,Windows:/Windows/,Mac:/Macintosh/},n=navigator&&navigator.userAgent;if(!n)return null;var r,i,a={browserName:"",browserVersion:"",osName:"",osVersion:"",deviceName:""};for(r in e)e[r].test(n)&&("Chrome"===(a.browserName=r)?a.browserVersion=n.split("Chrome/")[1].split(" ")[0]:"IE"===r?a.browserVersion=n.split("MSIE ")[1].split(" ")[1]:"Firefox"===r?a.browserVersion=n.split("Firefox/")[1]:"Opera"===r?a.browserVersion=n.split("Version/")[1]:"Safari"===r?a.browserVersion=n.split("Version/")[1].split(" ")[0]:"360"===r?a.browserVersion="":"QQBrowswe"===r&&(a.browserVersion=n.split("Version/")[1].split(" ")[0]));for(i in t)t[i].test(n)&&("Windows"===(a.osName=i)?a.osVersion=n.split("Windows NT ")[1].split(";")[0]:"Mac"===i?a.osVersion=n.split("Mac OS X ")[1].split(")")[0]:"iPhone"===i?a.osVersion=n.split("iPhone OS ")[1].split(" ")[0]:"iPad"===i?a.osVersion=n.split("iPad; CPU OS ")[1].split(" ")[0]:"Android"===i&&(a.osVersion=n.split("Android ")[1].split(";")[0],a.deviceName=n.split("(Linux; Android ")[1].split("; ")[1].split(" Build")[0]));return a}catch(e){}}function getOtName(e){if(e)return/iphone|ipad|ios/i.test(e)?0:/android/i.test(e)?1:/windows/i.test(e)?2:/harmonyOS/i.test(e)?3:/mac/i.test(e)?4:1}function getDeviceInfo(){try{checkBrowserIsOld()&&parserUA(),deviceInfo.di=getClientID(),deviceInfo.a="user",deviceInfo.ot=uaInfo&&isDefined(uaInfo.os)?uaInfo.os.name&&getOtName(uaInfo.os.name):"1",deviceInfo.ram=isDefined(navigator.deviceMemory)&&1024*navigator.deviceMemory||-1,deviceInfo.l=navigator.language||navigator.userLanguage,deviceInfo.ds=window.screen.width+"*"+window.screen.height,deviceInfo.rom=-1,deviceInfo.bn=uaInfo&&isDefined(uaInfo.os)?uaInfo.os.name:"unknown",deviceInfo.omv=uaInfo&&uaInfo.os&&uaInfo.os.version?uaInfo.os.version:"unknown",deviceInfo.ci=uaInfo&&isDefined(uaInfo.cpu)&&uaInfo.cpu.architecture&&uaInfo.cpu.architecture||"unknown",deviceInfo.ctn=uaInfo&&isDefined(uaInfo.browser)?uaInfo.browser.name:"unknown",deviceInfo.ctv=uaInfo&&uaInfo.browser&&uaInfo.browser.version?uaInfo.browser.version:"unknown",isDefined(uaInfo.device)&&isDefined(uaInfo.device.model)&&(deviceInfo.m=uaInfo.device.model)}catch(e){}return deviceInfo}function checkBrowserIsOld(){try{if(setting.exBrowser&&0<setting.exBrowser.length){var e=navigator.userAgent&&navigator.userAgent.match(/(firefox|msie|chrome|safari)[/\s]([\d.]+)/gi)||void 0;if(e&&0<e.length)for(var t=e[0].split("/"),n=0,r=setting.exBrowser.length-1;n<=r;n++){var i=setting.exBrowser[n];if((i=i.split("/"))[0]===t[0]&&Number(t[1].split(".")[0])<=Number(i[1]))return}return 1}}catch(e){}}function getClientType(){try{return navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i)?2:1}catch(e){}}function getTitle(){var e="";return document&&document.ready&&document.ready(function(){e=document.title}),e||document.title||"noTitle"}var title=getTitle;function getIP(e){return e?/(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\.(\d|[1-9]\d|1\d{2}|2[0-4]\d|25[0-5])\:([0-9]{1,5})/.test(e)?["".concat(RegExp.$1,".").concat(RegExp.$2,".").concat(RegExp.$3,".").concat(RegExp.$4),Number(RegExp.$5)]:/\:([0-9]{1,5})/.test(e)?["",Number(RegExp.$1)]:-1<e.indexOf("https")?["",443]:["",80]:["",""]}function getUrl$1(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:"";return"file:"===window.location.protocol&&!startWith(e,"http")||isDefined(e)&&0===e.indexOf("http")?e:/^\/{2}/.test(e)?window&&window.location.protocol+e||"http:"+e:/^\?/.test(e)?window&&window.location.origin+window.location.pathname+e||e:/^\/{1}/.test(e)?window&&window.location.origin+e||e:isDefined(e)&&window&&window.location.origin+"/"+e||e}var chrsz=8;function hex_md5(e){return binl2hex(core_md5(str2binl(e),e.length*chrsz))}function core_md5(e,t){e[t>>5]|=128<<t%32,e[14+(t+64>>>9<<4)]=t;for(var n=1732584193,r=-271733879,i=-1732584194,a=271733878,o=0;o<e.length;o+=16){var s=n,c=r,l=i,d=a,n=md5_ff(n,r,i,a,e[o+0],7,-680876936),a=md5_ff(a,n,r,i,e[o+1],12,-389564586),i=md5_ff(i,a,n,r,e[o+2],17,606105819),r=md5_ff(r,i,a,n,e[o+3],22,-1044525330);n=md5_ff(n,r,i,a,e[o+4],7,-176418897),a=md5_ff(a,n,r,i,e[o+5],12,1200080426),i=md5_ff(i,a,n,r,e[o+6],17,-1473231341),r=md5_ff(r,i,a,n,e[o+7],22,-45705983),n=md5_ff(n,r,i,a,e[o+8],7,1770035416),a=md5_ff(a,n,r,i,e[o+9],12,-1958414417),i=md5_ff(i,a,n,r,e[o+10],17,-42063),r=md5_ff(r,i,a,n,e[o+11],22,-1990404162),n=md5_ff(n,r,i,a,e[o+12],7,1804603682),a=md5_ff(a,n,r,i,e[o+13],12,-40341101),i=md5_ff(i,a,n,r,e[o+14],17,-1502002290),n=md5_gg(n,r=md5_ff(r,i,a,n,e[o+15],22,1236535329),i,a,e[o+1],5,-165796510),a=md5_gg(a,n,r,i,e[o+6],9,-1069501632),i=md5_gg(i,a,n,r,e[o+11],14,643717713),r=md5_gg(r,i,a,n,e[o+0],20,-373897302),n=md5_gg(n,r,i,a,e[o+5],5,-701558691),a=md5_gg(a,n,r,i,e[o+10],9,38016083),i=md5_gg(i,a,n,r,e[o+15],14,-660478335),r=md5_gg(r,i,a,n,e[o+4],20,-405537848),n=md5_gg(n,r,i,a,e[o+9],5,568446438),a=md5_gg(a,n,r,i,e[o+14],9,-1019803690),i=md5_gg(i,a,n,r,e[o+3],14,-187363961),r=md5_gg(r,i,a,n,e[o+8],20,1163531501),n=md5_gg(n,r,i,a,e[o+13],5,-1444681467),a=md5_gg(a,n,r,i,e[o+2],9,-51403784),i=md5_gg(i,a,n,r,e[o+7],14,1735328473),n=md5_hh(n,r=md5_gg(r,i,a,n,e[o+12],20,-1926607734),i,a,e[o+5],4,-378558),a=md5_hh(a,n,r,i,e[o+8],11,-2022574463),i=md5_hh(i,a,n,r,e[o+11],16,1839030562),r=md5_hh(r,i,a,n,e[o+14],23,-35309556),n=md5_hh(n,r,i,a,e[o+1],4,-1530992060),a=md5_hh(a,n,r,i,e[o+4],11,1272893353),i=md5_hh(i,a,n,r,e[o+7],16,-155497632),r=md5_hh(r,i,a,n,e[o+10],23,-1094730640),n=md5_hh(n,r,i,a,e[o+13],4,681279174),a=md5_hh(a,n,r,i,e[o+0],11,-358537222),i=md5_hh(i,a,n,r,e[o+3],16,-722521979),r=md5_hh(r,i,a,n,e[o+6],23,76029189),n=md5_hh(n,r,i,a,e[o+9],4,-640364487),a=md5_hh(a,n,r,i,e[o+12],11,-421815835),i=md5_hh(i,a,n,r,e[o+15],16,530742520),n=md5_ii(n,r=md5_hh(r,i,a,n,e[o+2],23,-995338651),i,a,e[o+0],6,-198630844),a=md5_ii(a,n,r,i,e[o+7],10,1126891415),i=md5_ii(i,a,n,r,e[o+14],15,-1416354905),r=md5_ii(r,i,a,n,e[o+5],21,-57434055),n=md5_ii(n,r,i,a,e[o+12],6,1700485571),a=md5_ii(a,n,r,i,e[o+3],10,-1894986606),i=md5_ii(i,a,n,r,e[o+10],15,-1051523),r=md5_ii(r,i,a,n,e[o+1],21,-2054922799),n=md5_ii(n,r,i,a,e[o+8],6,1873313359),a=md5_ii(a,n,r,i,e[o+15],10,-30611744),i=md5_ii(i,a,n,r,e[o+6],15,-1560198380),r=md5_ii(r,i,a,n,e[o+13],21,1309151649),n=md5_ii(n,r,i,a,e[o+4],6,-145523070),a=md5_ii(a,n,r,i,e[o+11],10,-1120210379),i=md5_ii(i,a,n,r,e[o+2],15,718787259),r=md5_ii(r,i,a,n,e[o+9],21,-343485551),n=safe_add(n,s),r=safe_add(r,c),i=safe_add(i,l),a=safe_add(a,d)}return Array(n,r,i,a)}function md5_cmn(e,t,n,r,i,a){return safe_add(bit_rol(safe_add(safe_add(t,e),safe_add(r,a)),i),n)}function md5_ff(e,t,n,r,i,a,o){return md5_cmn(t&n|~t&r,e,t,i,a,o)}function md5_gg(e,t,n,r,i,a,o){return md5_cmn(t&r|n&~r,e,t,i,a,o)}function md5_hh(e,t,n,r,i,a,o){return md5_cmn(t^n^r,e,t,i,a,o)}function md5_ii(e,t,n,r,i,a,o){return md5_cmn(n^(t|~r),e,t,i,a,o)}function safe_add(e,t){var n=(65535&e)+(65535&t);return(e>>16)+(t>>16)+(n>>16)<<16|65535&n}function bit_rol(e,t){return e<<t|e>>>32-t}function str2binl(e){for(var t=Array(),n=(1<<chrsz)-1,r=0;r<e.length*chrsz;r+=chrsz)t[r>>5]|=(e.charCodeAt(r/chrsz)&n)<<r%32;return t}function binl2hex(e){for(var t="0123456789abcdef",n="",r=0;r<4*e.length;r++)n+=t.charAt(e[r>>2]>>r%4*8+4&15)+t.charAt(e[r>>2]>>r%4*8&15);return n}function set_64_keys(data,emit){var reData={},keys;if(isDefined(data)){if("string"==typeof data&&""!==data){if(data=eval("("+data+")"),!isJSON(data))return data;data=JSON.parse(data)}return"object"!=_typeof(data)?data:(keys=Object.keys(data),keys.length>emit?(keys=keys.slice(0,emit),keys.forEach(function(e){reData[e]=data[e]}),reData):data)}return reData}function set_length(e,t){return e&&(e.length>t?e.slice(0,t):e)}function getRandom(e){return e<0?NaN:e<=30?0|Math.random()*(1<<e):e<=53?(0|Math.random()*(1<<30))+(0|Math.random()*(1<<e-30))*(1<<30):NaN}function toHex(e,t){for(var n=e.toString(16),r=t-n.length,i="0";0<r;r>>>=1,i+=i)1&r&&(n=i+n);return n}function traceId(){return"00-"+toHex(getRandom(32),8)+toHex(getRandom(32),8)+toHex(getRandom(32),8)+toHex(getRandom(32),8)+"-"+toHex(getRandom(32),8)+toHex(getRandom(32),8)+"-00"}var isObj=function(e){return"object"===_typeof(e)};function getElementForNum(n,r){var i;return isObj(n)?(i={},Object.keys(n).map(function(e,t){t<=r&&(i[e]=n[e])}),i):{}}function _createForOfIteratorHelper$4(e,t){var n,r,i,a,o="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(o)return r=!(n=!0),{s:function(){o=o.call(e)},n:function(){var e=o.next();return n=e.done,e},e:function(e){r=!0,i=e},f:function(){try{n||null==o.return||o.return()}finally{if(r)throw i}}};if(Array.isArray(e)||(o=_unsupportedIterableToArray$4(e))||t&&e&&"number"==typeof e.length)return o&&(e=o),a=0,{s:t=function(){},n:function(){return a>=e.length?{done:!0}:{done:!1,value:e[a++]}},e:function(e){throw e},f:t};throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray$4(e,t){var n;if(e)return"string"==typeof e?_arrayLikeToArray$4(e,t):"Map"===(n="Object"===(n=Object.prototype.toString.call(e).slice(8,-1))&&e.constructor?e.constructor.name:n)||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?_arrayLikeToArray$4(e,t):void 0}function _arrayLikeToArray$4(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function supportsLabelProperty(){return"labels"in HTMLInputElement.prototype}function getTextualContent(e){if(!e.isContentEditable){if("innerText"in e){var t=e.innerText;if(!supportsInnerTextScriptAndStyleRemoval())for(var n="script, style",r=e.querySelectorAll(n),i=0;i<r.length;i+=1){var a=r[i];"innerText"in a&&(a=a.innerText)&&0<a.trim().length&&(t=t.replace(a,""))}return t}return e.textContent}}function find(e,t){var n=0;if(n<e.length)return t(n)}function safeTruncate(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:"",r=e.charCodeAt(t-1),r=55296<=r&&r<=56319?t+1:t;return e.length<=r?e:"".concat(e.slice(0,r)).concat(n)}function truncate(e){return 100<e.length?"".concat(safeTruncate(e,100)," [...]"):e}function normalizeWhitespace(e){return e.replace(/\s+/g," ")}function getElementById(e,t){return e.ownerDocument?e.ownerDocument.getElementById(t):null}var priorityStrategies=[function(t){var e;if(supportsLabelProperty()){if("labels"in t&&t.labels&&0<t.labels.length)return getTextualContent(t.labels[0])}else if(t.id)return(e=t.ownerDocument&&find(t.ownerDocument.querySelectorAll("label"),function(e){return e.htmlFor===t.id}))&&getTextualContent(e)},function(e){if("INPUT"===e.nodeName){var t=e.getAttribute("type");if("button"===t||"submit"===t||"reset"===t)return e.value}},function(e){if("BUTTON"===e.nodeName||"LABEL"===e.nodeName||"button"===e.getAttribute("role"))return getTextualContent(e)},function(e){return e.getAttribute("aria-label")},function(t){var e=t.getAttribute("aria-labelledby");if(e)return e.split(/\s+/).map(function(e){return getElementById(t,e)}).filter(function(e){return Boolean(e)}).map(function(e){return getTextualContent(e)}).join(" ")},function(e){return e.getAttribute("alt")},function(e){return e.getAttribute("name")},function(e){return e.getAttribute("title")},function(e){return e.getAttribute("placeholder")},function(e){if("options"in e&&0<e.options.length)return getTextualContent(e.options[0])}];function supportsInnerTextScriptAndStyleRemoval(){return!isIE()}function isIE(){return Boolean(document.documentMode)}var fallbackStrategies=[function(e){return getTextualContent(e)}],MAX_PARENTS_TO_CONSIDER=10;function getActionNameFromElementForStrategies(e,t){for(var n=e,r=0;r<=MAX_PARENTS_TO_CONSIDER&&n&&"BODY"!==n.nodeName&&"HTML"!==n.nodeName&&"HEAD"!==n.nodeName;){var i,a=_createForOfIteratorHelper$4(t);try{for(a.s();!(i=a.n()).done;){var o=(0,i.value)(n);if("string"==typeof o){var s=o.trim();if(s)return truncate(normalizeWhitespace(s))}}}catch(e){a.e(e)}finally{a.f()}if("FORM"===n.nodeName)break;n=n.parentElement,r+=1}}function getActionText(e){return getActionNameFromElementForStrategies(e,priorityStrategies)||getActionNameFromElementForStrategies(e,fallbackStrategies)||""}var deamonEntries=[],deamonStart,requestInQueue=[],types=[XML_HTTP_REQUEST,FETCH],sortCond=function(e,t){return e[START_TIME]-t[START_TIME]};function getCookieUserID(){var e=getUserIdManageClass.cookieArr,t=getUserIdManageClass.configeManage;if(0<e.length)for(var n=0,r=e.length-1;n<=r;n++){var i=get(e[n].rule);if(isDefined(i)&&checkValue(i)&&e[n].index<=getUserIdManageClass.isStopGetValue)return t[e[n].index].value=""+i,void getUserIdManageClass.checkValueRight()}}function filterEntry(e){var n=e.url,r=e[TIMESTAMP],i=types[e.type],e=map(requestInQueue,function(e){if(e.url===n&&types[e.type]===i)return e}),a=(e.sort(sortCond),-1);if(forEach(e,function(e,t){if(e[TIMESTAMP]===r)return a=t,!1}),a<0)log("The associated request was not found in the request queue");else{var t,e=map(deamonEntries,function(e,t){if(match(e,n,i))return e});if(a>=e.length?(log("Associate requests from resource cache"),t=[],window.performance.getEntriesByType&&(t=map(window.performance.getEntriesByType("resource"),function(e){if(match(e,n,i))return e})),e=e.concat(t)):log("Associate requests from dump resources"),e.sort(sortCond),!(e.length<1||a>=e.length))return e[a];log("The requested resource was not matched")}}function inWatchingRequest(e){return(e.initiatorType===XML_HTTP_REQUEST||e.initiatorType===FETCH)&&e[START_TIME]>deamonStart}function match(e,t,n){return inWatchingRequest(e)&&endWith(e.name,t)&&e.initiatorType===n}function isSurelyMatch(e,t){return e&&t[START_TIME]<=1e3*(e[START_TIME]+1)}function isUnaccessibleCrosRequest(e){return 0<e[RESPONSE_END]&&0===e[REQUEST_START]&&0===e[RESPONSE_START]}function isFullyResponsed(e){return 0<e&&e<600}var extendMetrics=function(e){if(window.performance&&window.performance.now){var t=e.info;if(isFullyResponsed(t.status||t.code)){var n=filterEntry(t);if(isSurelyMatch(n,t)){if(isUnaccessibleCrosRequest(n))return log("Cross domain resources"),e;var r=e.metric;t[START_TIME]=1e3*getFixedMetric(n,START_TIME),t[TIMESTAMP]=1e3*(t[START_TIME]/1e3+window.performance.timing.navigationStart),t[DURATION]=1e3*getFixedMetric(n,DURATION),r[REDIRECT_START]=1e3*getFixedMetric(n,REDIRECT_START),r[REDIRECT_END]=1e3*getFixedMetric(n,REDIRECT_END),r[FETCH_START]=1e3*getFixedMetric(n,FETCH_START),r[DOMAIN_LOOKUP_START]=1e3*getFixedMetric(n,DOMAIN_LOOKUP_START),r[DOMAIN_LOOKUP_END]=1e3*getFixedMetric(n,DOMAIN_LOOKUP_END),r[CONNECT_START]=1e3*getFixedMetric(n,CONNECT_START),r[CONNECT_END]=1e3*getFixedMetric(n,CONNECT_END),r[SECURE_CONNECTION_START]=1e3*getFixedMetric(n,SECURE_CONNECTION_START),r[RESPONSE_START]=1e3*getFixedMetric(n,RESPONSE_START),r[REQUEST_START]=r[RESPONSE_START]-1e3*getFixedMetric(n,REQUEST_START),r[RESPONSE_END]=1e3*getFixedMetric(n,RESPONSE_END),isDefined(n[DECODED_BODY_SIZE])&&(t[NEXT_HOP_PROTOCOL]=n[NEXT_HOP_PROTOCOL],r[WORKER_START]=1e3*getFixedMetric(n,WORKER_START),r[TRANSFER_SIZE]=getFixedMetric(n,TRANSFER_SIZE),r[ENCODED_BODY_SIZE]=getFixedMetric(n,ENCODED_BODY_SIZE),r[DECODED_BODY_SIZE]=getFixedMetric(n,DECODED_BODY_SIZE))}else log("The matching request resource deviation is too large")}else log("Exception requests do not support associated resources")}else log("Associated resources are not supported");return e},polyfillMetric=function(e){var t=e.info,n=e.metric;return isDefined(t[DURATION])||(t[DURATION]=n[RESPONSE_END]-t[START_TIME]),isDefined(t.callbackError)||(t.callbackError=0),e},queueRequest=function(e){var t=e.info;isFullyResponsed(t.status||t.code)&&requestInQueue.push(e.info)},defaultInfo=function(){return{block:isPageLoaded()?0:1,callbackError:0,base:getBaseSetting()}};function initDeamon(){deamonStart=tillNow()/1e3||0,on$1(RESOURCE_DUMP,function(e){e=e.p,e=map(e,function(e){if(inWatchingRequest(e))return e});deamonEntries=deamonEntries.concat(e)})}var blackAndWhite=function(){function e(){_classCallCheck(this,e),this.list={},this.isOpen=!1,this.blackKey=[]}return _createClass(e,[{key:"checkIsOpen",value:function(){isDefined(setting.brss)&&setting.brss?this.isOpen=!0:this.isOpen=!1}},{key:"getItem",value:function(e,t,n){var r="".concat(e,"+").concat(t);return isDefined(e)&&this.list[r]?this.list[r]:this.setItem(e,t,n)}},{key:"setItem",value:function(e,t,n){var r=setting.traceConfig,i=r.urlTotalList,a=r.urlWhiteList,o=r.urlBlackList,s={};if(isDefined(i)&&0<i.length)for(var c=0,l=(i=200<i.length?i.slice(0,200):i).length-1;c<=l;c++)extend(s,this.totalHead(i[c],e,t,n));if(isDefined(a)&&0<a.length)for(var d=0,u=(a=200<a.length?a.slice(0,200):a).length-1;d<=u;d++)this.whiteCheck(e,a[d])&&extend(s,this.whiteHead(a[d],e,t,n));if(isDefined(o)&&0<o.length)for(var f=0,p=(o=200<o.length?o.slice(0,200):o).length-1;f<=p;f++)this.black(e,o[f])?extend(s,this.blackHead(o[f])):this.getBlackKey(o[f]);200<Object.keys(s).length&&(s=getElementForNum(s,200));s=this.delBlackKey(s),r="".concat(e,"+").concat(t);return this.list[r]=s}},{key:"delBlackKey",value:function(e){if(0<this.blackKey.length)for(var t=0,n=this.blackKey.length-1;t<=n;t++)isDefined(e[this.blackKey[t]])&&delete e[this.blackKey[t]];return this.blackKey=[],e}},{key:"getBlackKey",value:function(e){var t=e&&e.reqHeaderRules;if(isDefined(t)&&0<t.length&&this.blackKey)for(var n=0,r=t.length-1;n<=r;n++)t[n]&&t[n].key&&this.blackKey.push(t[n].key)}},{key:"totalHead",value:function(e,t,n,r){var i={};return extend(i,this.constructData(e,t,n,r)),i}},{key:"whiteCheck",value:function(e,t){if(t.reqHeaderRules&&0<t.reqHeaderRules.length){if(!1===includes$1([0,1,2,3,4],t.type))return!1;switch(t.type){case 0:if(e==t.rule)return!0;break;case 1:if(startWith(e,t.rule))return!0;break;case 2:if(endWith(e,t.rule))return!0;break;case 3:var n=new RegExp(t.rule);if(n instanceof RegExp&&n.test(e))return!0;break;case 4:if(-1<e.indexOf(t.rule))return!0;break;default:return!1}}return!1}},{key:"whiteHead",value:function(e,t,n,r){var i=e.reqHeaderRules,a={};if(isDefined(i)&&0<i.length)for(var o=0,s=i.length-1;o<=s;o++)extend(a,this.constructData(i[o],t,n,r));return a}},{key:"black",value:function(e,t){if(t.reqHeaderRules&&0<t.reqHeaderRules.length){if(!1===includes$1([0,1,2,3,4],t.type))return!1;switch(t.type){case 0:if(e==t.rule)return!1;break;case 1:if(startWith(e,t.rule))return!1;break;case 2:if(endWith(e,t.rule))return!1;break;case 3:var n=new RegExp(t.rule);if(n instanceof RegExp&&n.test(e))return!1;break;case 4:if(-1<e.indexOf(t.rule))return!1;break;default:return!0}}return!0}},{key:"blackHead",value:function(e,t,n,r){var i=e.reqHeaderRules,a={};if(isDefined(i)&&0<i.length)for(var o=0,s=i.length-1;o<=s;o++)extend(a,this.constructData(i[o],t,n,r));return a}},{key:"checkKey",value:function(e,t,n){return!(!n||1===n)||!!isDefined(e)&&!(e.length>t||(256===t?!/^[0-9a-zA-Z_-]{1,256}$/.test(e):""==trim(e)))}},{key:"constructData",value:function(e,t,n,r){var i={},a=getUserAgent(),o=a&&a.osName+"/"+a.osVersion||setting.osType&&setting.typeArr[setting.osType]||setting.typeArr[1];if(isDefined(e)&&isDefined(e.type)&&this.checkKey(e.key,256)){if(isDefined(e.value)&&!1===this.checkKey(e.value,512))return;switch(e.type){case 1:isDefined(e.value)&&(i[e.key]=e.value);break;case 2:i[e.key]={fun:uuidWithLength,len:16,sky:!1};break;case 3:i[e.key]={fun:uuidWithLength,len:32,sky:!1};break;case 4:i[e.key]={fun:skyData,len:32,sky:!0,url:t,pathname:n};break;case 5:i[e.key]={fun:traceId,len:32,sky:!1};break;case 6:i[e.key]="bnro="+o+"_js/"+setting.agentVersion+r}}return i}}]),e}(),BWexample=new blackAndWhite;function checkBlackAndWhite(e,t){if(isDefined(e.url)){e=0===(e=e.url).indexOf("http")?(n=e.match(/^(https?\:)\/\/(([^:\/?#]*)(?:\:([0-9]+))?)([\/]{0,1}[^?#]*)(\?[^#]*|)(#.*|)$/))&&{href:e,protocol:n[1],host:n[2],hostname:n[3],port:n[4],pathname:n[5],search:n[6],hash:n[7]}:(n=e.match(/^(file?\:)\/\/(([^:\/?#]*)(?:\:([0-9]+))?)([\/]{0,1}[^?#]*)(\?[^#]*|)(#.*|)$/))&&{href:e,protocol:n[1],host:n[2],hostname:n[3],port:n[4],pathname:n[5],search:n[6],hash:n[7]};if(!isDefined(e))return;n=isDefined(e.hostname)?e.hostname:"",e=isDefined(e.pathname)?e.pathname:"";if(isDefined(n))return BWexample.getItem(n,e,t)}var n}function skyData(e,t){var n=String(enBase64(skyUuid())),r=String(enBase64(skyUuid())),i=String(enBase64(setting.appName)),a=String(enBase64(setting.appVersion)),t=t&&String(enBase64(t))||String(enBase64("/")),e=String(enBase64(e));return"1-".concat(n,"-").concat(r,"-0-").concat(i,"-").concat(a,"-").concat(t,"-").concat(e)}function _createForOfIteratorHelper$3(e,t){var n,r,i,a,o="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(o)return r=!(n=!0),{s:function(){o=o.call(e)},n:function(){var e=o.next();return n=e.done,e},e:function(e){r=!0,i=e},f:function(){try{n||null==o.return||o.return()}finally{if(r)throw i}}};if(Array.isArray(e)||(o=_unsupportedIterableToArray$3(e))||t&&e&&"number"==typeof e.length)return o&&(e=o),a=0,{s:t=function(){},n:function(){return a>=e.length?{done:!0}:{done:!1,value:e[a++]}},e:function(e){throw e},f:t};throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray$3(e,t){var n;if(e)return"string"==typeof e?_arrayLikeToArray$3(e,t):"Map"===(n="Object"===(n=Object.prototype.toString.call(e).slice(8,-1))&&e.constructor?e.constructor.name:n)||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?_arrayLikeToArray$3(e,t):void 0}function _arrayLikeToArray$3(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function checkValue(e){return!(!isDefined(e)||e instanceof Object||(isString$2(e)||(e=""+e),!(/^[\u4E00-\u9FFFa-zA-Z0-9:_\-@.]+$/.test(e)&&e.length<=256)))}var getUserIdManage=function(){function e(){_classCallCheck(this,e),this.useAPI=!1,this.configeManage=[],this.originManageData=null,this.cookieArr=[],this.requestArr=[],this.responseArr=[],this.isStopGetValue=50}return _createClass(e,[{key:"checkOriginManageData",value:function(e){this.useAPI||(e=e&&isString$2(e)&&JSON.parse(e)||e,isDefined(e)&&Array.isArray(e)&&(50<(this.originManageData=e).length?this.configeManage=e.slice(0,50):this.configeManage=e,this.startCatchDataFun()))}},{key:"startCatchDataFun",value:function(){for(var e=0,t=this.configeManage.length-1;e<=t;e++)if(this.configeManage[e]&&this.configeManage[e].type)switch(this.configeManage[e].type){case 1:this.configeManage[e].value=this.getDataWithType_1(this.configeManage[e].rule);break;case 2:this.configeManage[e].value=this.getDataWithType_2(this.configeManage[e].rule);break;case 3:this.configeManage[e].value=this.getDataWithType_3(this.configeManage[e].rule);break;case 4:this.getDataWithType_4(this.configeManage[e].rule,e);break;case 5:this.configeManage[e].value=this.getDataWithType_5(this.configeManage[e].rule);break;case 6:this.getDataWithType_6(this.configeManage[e].rule,e);break;case 7:this.getDataWithType_7(this.configeManage[e].rule,e)}getCookieUserID(),this.checkValueRight()}},{key:"checkValueRight",value:function(){for(var e=0,t=this.configeManage.length-1;e<=t;e++)if(isDefined(this.configeManage[e])&&isDefined(this.configeManage[e].value))return this.isStopGetValue=e,void configUser(this.configeManage[e].value,!0)}},{key:"getDataWithType_1",value:function(e){if(!(e&&256<e.length))try{if(e&&0<e.indexOf("@")){var t=e.split("@"),n=document&&document.querySelector(t[0])||null;if(n&&n[t[1]]&&checkValue(n[t[1]]))return n[t[1]]}else{var r=document&&document.querySelector(e)||null;if(r&&checkValue(r.innerText))return r.innerText}}catch(e){log("Selector error")}}},{key:"getDataWithType_2",value:function(e){var n;if(!(e&&256<e.length)&&isDefined(e))if(-1!=e.indexOf(".")){if(e.split(".").map(function(e,t){n=n&&n[e]||0==t&&window[e]||void 0}),n&&checkValue(n))return isString$2(n)?n:""+n}else if(checkValue(window[e]))return""+window[e]}},{key:"getDataWithType_3",value:function(e){if(!(e&&256<e.length))try{var t=document.head.querySelector("[name~=".concat(e,"][content]")).content;if(isDefined(t)&&checkValue(t))return t}catch(e){log("Failed to get meta tag")}}},{key:"getDataWithType_4",value:function(e,t){e&&256<e.length||this.cookieArr.push({rule:e,index:t})}},{key:"getDataWithType_5",value:function(e){try{if(e&&256<e.length)return;if(window&&window.location&&window.location.search){var t,n=_createForOfIteratorHelper$3(decodeURI(window.location.search).split("?")[1].split("&"));try{for(n.s();!(t=n.n()).done;){var r=t.value;if(r.split("=")[0]==e&&checkValue(r.split("=")[1]))return r.split("=")[1]}}catch(e){n.e(e)}finally{n.f()}}}catch(e){log("param is not allow")}}},{key:"getDataWithType_6",value:function(t,e){var n;t&&256<t.length||(n=!1,this.requestArr.map(function(e){e.rule==t&&(n=!0)}),n)||this.requestArr.push({rule:t,index:e})}},{key:"getDataWithType_7",value:function(t,e){var n;t&&256<t.length||(n=!1,this.responseArr.map(function(e){e.rule==t&&(n=!0)}),n)||this.responseArr.push({rule:t,index:e})}},{key:"changeUseAPI",value:function(e){this.useAPI=e}}]),e}(),getUserIdManageClass=new getUserIdManage,GUID_KEY="br-resp-key",X_BR_RESPONSE="x-br-response",TRACE_RESPONSE="traceresponse",BREAK_LINE="\r\n",state=0,changeState=function(e){return state=e};function isLoad(){return 1<state}var traceActionState=!1,user;function openTraceAction(){traceActionState=!0}var userInfoMap={},setUserInfoTimeStamp=0,lastUserInfoSetMapKey="",firstUserInfoSetMapKey="",usdTime=0;function changUsdTime(e){isDefined(e)&&(usdTime=e)}function initUsdTime(){usdTime=0}function getFirstUserInfoSetMapKey(){return firstUserInfoSetMapKey}function updateFirstUserInfoSetMapKey(e){firstUserInfoSetMapKey=e}function getUserInfo(){var e;return isEmpty(userInfoMap)&&""!==(e=getUser())&&configUser(e),userInfoMap}var configUser=function(e){var t,n=1<arguments.length&&void 0!==arguments[1]&&arguments[1];isDefined(e)?e instanceof Object?log("auser is not allow Object"):(isString$2(e)||(e=""+e),!/^[\u4E00-\u9FFFa-zA-Z0-9:_\s\-@./]+$/.test(e)||256<e.length||""===e.trim()?log("Illegal parameter"):(n||getUserIdManageClass.changeUseAPI(!0),n=!1,t=hex_md5(e),(n=isDefined(userInfoMap[t])?!0:n)||(isEmpty(userInfoMap)&&updateFirstUserInfoSetMapKey(t),lastUserInfoSetMapKey=t,setUserInfoTimeStamp=now(),(n={}).ui=e,userInfoMap[t]=n))):log("auser is not defined")};function configUserExtraInfo(e){e=set_64_keys(e,64),isDefined(e)?isEmpty(userInfoMap)?log("User information does not exist"):userInfoMap[lastUserInfoSetMapKey].ei=JSON.stringify(e):log("extInfo does not exist")}function getLocalUser(){var e,t;return isDefined(user)?user:(e=setting.userKey,isDefined(window.sessionStorage)&&(t=sessionStorage.getItem(e),isReadable(t))||isDefined(window.localStorage)&&(t=localStorage.getItem(e),isReadable(t))?t:get(e))}var getUser=function(){var e;return isDefined(user)?user:(e=getLocalUser(),isReadable(e)?e:"")},setting={urlChange:!1,osType:BROWSER,probability:FULLY_COLLECT,sessionPeriod:18e8,sessionTimeout:3e8,sessionEvents:2e3,debounce:1e3,ignoreRequestParams:!1,ignoreRequestHeaders:!1,ignoreResources:!1,ignoreUserEvents:!1,isFirstUpload:1,exBrowser:["Firefox/47"],agentVersion:version$1},rawConfig={appVersion:"1.0.0",appName:"unKnown",appId:"",hcs:1,channelId:null,deviceId:null,pageViewId:"00000000-0000-0000-0000-000000000000",pageUrl:document.URL,reqBodyKey:null,reqURLKey:null,reqHeaderKey:null,uploadAddrHttps:"https://bupload.bonree.com/upload",uploadAddrHttp:"http://bupload.bonree.com/upload",userKey:"br-user",isDebug:!1,enableLogger:!1,enableWebsocket:!0,enable:!0,sessionDomain:!1,typeArr:["","android","ios","pc"],ac:{ac:!0,cp:100,mmd:5,aot:3e4},traceConfig:{urlTotalList:[],urlWhiteList:[],urlBlackList:[]}},probability=setting.probability,checkRate=(extend(setting,rawConfig),function(){return setting.probability===FULLY_COLLECT?FULLY_COLLECT:!!(setting.enable&&Math.random()*FULLY_COLLECT*FULLY_COLLECT%FULLY_COLLECT<=setting.probability)});function checkSetData(n){forEachOwn(n,function(e){var t;includes$1(["reqBodyKey","reqURLKey","reqHeaderKey"],e)&&(isArray(n[e])?(t=[],forEachArray(n[e],function(e){/^[:0-9a-zA-Z_-]{1,}$/.test(e)&&t.push(e)}),n[e]=0==t.length?null:t):/^[:0-9a-zA-Z_-]{1,}$/.test(n[e])||(n[e]=null))})}function setConfig(e){if(e.pageViewId=uuid(),setting.initTime=now(),(isDefined(e.uploadAddrHttps)||isDefined(e.uploadAddrHttp))&&(setting.urlChange=!0),isDefined(e.userId)){if(!/^[a-zA-Z0-9:_-]+$/.test(e.userId)||256<e.userId.length)return void log("The value of userId is invalid!");isDefined(e.extraInfo)?(configUser(e.userId),configUserExtraInfo(e.extraInfo)):configUser(e.userId),getUserIdManageClass.changeUseAPI(!0)}isDefined(e.userdefine)&&getUserIdManageClass.checkOriginManageData(e.userdefine),checkSetData(e),extend(setting,e),probability=checkRate(),window.bonreeRUM&&(setting.version=window.bonreeRUM.version),(setting.isDebug?enableLogger:disableLogger)(),configCookie(setting),BWexample.checkIsOpen()}function isHOS(e){return e===HOS}function isIOS(e){return e===IOS}function isBrowser(e){return e===BROWSER}var checkStatus=function(){return/BonreeRUM=0/.test(window.location.search)&&(setting.enable=!1),setting.enable},Protocol=_createClass(function e(){_classCallCheck(this,e),this.json={v:bonreeRUM&&bonreeRUM.version||"1.0.0",e:[]}}),Proxy$1=(Protocol.prototype.setMonitorTime=function(e){return this.json.mt=e,this},Protocol.prototype.setConfigTime=function(e){return this.json.cmt=e,this},Protocol.prototype.setSessionId=function(e){return this.json.s=e,this},Protocol.prototype.setDeviceInfo=function(e){return this.json.di=e,this},Protocol.prototype.setAppInfo=function(e){return this.json.ai=e,this},Protocol.prototype.setFirstUserInfoIndex=function(){var e=getFirstUserInfoSetMapKey();return""!==e&&(this.json.fui=e,updateFirstUserInfoSetMapKey("")),this},Protocol.prototype.setUserInfo=function(e){return this.json.ui=e,this},Protocol.prototype.setSessionDuration=function(e){return this.json.usd=e,this},Protocol.prototype.pushEventData=function(e){var t={k:e.type,ent:e.ent,sin:["","",""],v:e.data};return isDefined(e.sin)&&(t.sin[0]=e.sin),this.json.e.push(t),this},Protocol.prototype.build=function(e){return this.json},function(){function e(){_classCallCheck(this,e)}return _createClass(e,[{key:"zero",value:function(e){if(0<e){for(var t="0",n=1;n<e;n++)t+=t;return t}return""}},{key:"stringToArray",value:function(e){for(var t=[],n=e.length,r=0;r<n;r++){var i,a,o,s,c,l=e.charCodeAt(r);19968<l&&l<40869?(a="1110",s=o="10",(c=(i=l.toString(2)).length)<=6?(s=s+this.zero(6-c)+i,o+=this.zero(6),a+=this.zero(4)):6<c&&c<=12?(s+=i.slice(-6),o=o+this.zero(12-c)+i.substr(0,c-6),a+=this.zero(4)):(s+=i.slice(-6),o+=i.substr(c-12,6),a=a+this.zero(16-c)+i.substr(0,c-12)),t.push(parseInt(a,2),parseInt(o,2),parseInt(s,2))):t.push(l)}return t}},{key:"stringToArrayBufferInUtf8",value:function(e){return this.stringToArray(e)}}]),e}()),UINT8_BLOCK=(Array.prototype.fill||Object.defineProperty(Array.prototype,"fill",{value:function(e){if(null==this)throw new TypeError("this is null or not defined");for(var t=Object(this),n=t.length>>>0,r=arguments[1]>>0,i=r<0?Math.max(n+r,0):Math.min(r,n),r=arguments[2],r=void 0===r?n:r>>0,a=r<0?Math.max(n+r,0):Math.min(r,n);i<a;)t[i]=e,i++;return t}}),16),Sbox=new Uint8Array([214,144,233,254,204,225,61,183,22,182,20,194,40,251,44,5,43,103,154,118,42,190,4,195,170,68,19,38,73,134,6,153,156,66,80,244,145,239,152,122,51,84,11,67,237,207,172,98,228,179,28,169,201,8,232,149,128,223,148,250,117,143,63,166,71,7,167,252,243,115,23,186,131,89,60,25,230,133,79,168,104,107,129,178,113,100,218,139,248,235,15,75,112,86,157,53,30,36,14,94,99,88,209,162,37,34,124,59,1,33,120,135,212,0,70,87,159,211,39,82,76,54,2,231,160,196,200,158,234,191,138,210,64,199,56,181,163,247,242,206,249,97,21,161,224,174,93,164,155,52,26,85,173,147,50,48,245,140,177,227,29,246,226,46,130,102,202,96,192,41,35,171,13,83,78,111,213,219,55,69,222,253,142,47,3,255,106,114,109,108,91,81,141,27,175,146,187,221,188,127,17,217,92,65,31,16,90,216,10,193,49,136,165,205,123,189,45,116,208,18,184,229,180,176,137,105,151,74,12,150,119,126,101,185,241,9,197,110,198,132,24,240,125,236,58,220,77,32,121,238,95,62,215,203,57,72]),CK=(Uint8Array.prototype.fill=function(){Array.prototype.fill.apply(this,arguments)},Uint8Array.prototype.slice||(Uint8Array.prototype.slice=function(e){return new Uint8Array(this).subarray(e)}),new Uint32Array([462357,472066609,943670861,1415275113,1886879365,2358483617,2830087869,3301692121,3773296373,4228057617,404694573,876298825,1347903077,1819507329,2291111581,2762715833,3234320085,3705924337,4177462797,337322537,808926789,1280531041,1752135293,2223739545,2695343797,3166948049,3638552301,4110090761,269950501,741554753,1213159005,1684763257])),FK=new Uint32Array([2746333894,1453994832,1736282519,2993693404]),Sm4=function(){function t(e){_classCallCheck(this,t),this.Crypt=new Proxy$1;e=this.Crypt.stringToArrayBufferInUtf8(e.key);if(16!==e.length)throw new Error("key should be a 16 bytes string");this.key=e,this.mode="ecb",this.encryptRoundKeys=new Uint32Array(32),this.spawnEncryptRoundKeys(),Uint32Array.prototype.reverse=function(){Array.prototype.reverse.apply(this,arguments)},this.decryptRoundKeys=new Uint32Array(this.encryptRoundKeys),this.decryptRoundKeys.reverse()}return _createClass(t,[{key:"doBlockCrypt",value:function(e,t){var n=new Uint32Array(36);n.set(e,0);for(var r=0;r<32;r++)n[r+4]=n[r]^this.tTransform1(n[r+1]^n[r+2]^n[r+3]^t[r]);e=new Uint32Array(4);return e[0]=n[35],e[1]=n[34],e[2]=n[33],e[3]=n[32],e}},{key:"spawnEncryptRoundKeys",value:function(){var e=new Uint32Array(4),t=(e[0]=this.key[0]<<24|this.key[1]<<16|this.key[2]<<8|this.key[3],e[1]=this.key[4]<<24|this.key[5]<<16|this.key[6]<<8|this.key[7],e[2]=this.key[8]<<24|this.key[9]<<16|this.key[10]<<8|this.key[11],e[3]=this.key[12]<<24|this.key[13]<<16|this.key[14]<<8|this.key[15],new Uint32Array(36));t[0]=e[0]^FK[0],t[1]=e[1]^FK[1],t[2]=e[2]^FK[2],t[3]=e[3]^FK[3];for(var n=0;n<32;n++)t[n+4]=t[n]^this.tTransform2(t[n+1]^t[n+2]^t[n+3]^CK[n]),this.encryptRoundKeys[n]=t[n+4]}},{key:"rotateLeft",value:function(e,t){return e<<t|e>>>32-t}},{key:"linearTransform1",value:function(e){return e^this.rotateLeft(e,2)^this.rotateLeft(e,10)^this.rotateLeft(e,18)^this.rotateLeft(e,24)}},{key:"linearTransform2",value:function(e){return e^this.rotateLeft(e,13)^this.rotateLeft(e,23)}},{key:"tauTransform",value:function(e){return Sbox[e>>>24&255]<<24|Sbox[e>>>16&255]<<16|Sbox[e>>>8&255]<<8|Sbox[255&e]}},{key:"tTransform1",value:function(e){e=this.tauTransform(e);return this.linearTransform1(e)}},{key:"tTransform2",value:function(e){e=this.tauTransform(e);return this.linearTransform2(e)}},{key:"padding",value:function(e){var t,n;return null===e?null:(t=UINT8_BLOCK-e.length%UINT8_BLOCK,(n=new Uint8Array(e.length+t)).set(e,0),n.fill(t,e.length),n)}},{key:"dePadding",value:function(e){var t;return null===e?null:(t=e[e.length-1],e.slice(0,e.length-t))}},{key:"uint8ToUint32Block",value:function(e,t){void 0===t&&(t=0);var n=new Uint32Array(4);return n[0]=e[t]<<24|e[t+1]<<16|e[t+2]<<8|e[t+3],n[1]=e[t+4]<<24|e[t+5]<<16|e[t+6]<<8|e[t+7],n[2]=e[t+8]<<24|e[t+9]<<16|e[t+10]<<8|e[t+11],n[3]=e[t+12]<<24|e[t+13]<<16|e[t+14]<<8|e[t+15],n}},{key:"encrypt",value:function(e){for(var e=this.Crypt.stringToArrayBufferInUtf8(e),t=this.padding(e),n=t.length/UINT8_BLOCK,r=new Uint8Array(t.length),i=0;i<n;i++)for(var a=i*UINT8_BLOCK,o=this.uint8ToUint32Block(t,a),s=this.doBlockCrypt(o,this.encryptRoundKeys),c=0;c<UINT8_BLOCK;c++)r[a+c]=s[parseInt(c/4)]>>(3-c)%4*8&255;return r}}]),t}(),TIMEOUT=3e3,baseUrl;function isFirstUpload(){return setting.isFirstUpload?(extend(setting,{isFirstUpload:0}),1):0}function getUrl(e,t){isDefined(baseUrl)&&!setting.urlChange||(baseUrl="https:"===window.location.protocol?setting.uploadAddrHttps:setting.uploadAddrHttp,setting.urlChange=!1);var n=(n=(n=(n=(n=(n=(n=baseUrl)+"?v=2023090701"+("&a="+e.ai.ai))+("&d="+e.di.di))+("&mt="+e.mt))+("&cmt="+e.cmt))+("&s="+e.s)+("&brkey="+t))+("&if="+isFirstUpload());return void 0!==setting.sm4Config&&/^[A-Za-z0-9_-]{1,256}$/.test(setting.sm4Config.identify||setting.sm4Config.identifier)&&(e=setting.sm4Config.identify||setting.sm4Config.identifier,n+="&BR-Encryption-Method=sm4:".concat(e)),n}function post(e,t,n,r,i){var a;if(window.XDomainRequest)(a=new window.XDomainRequest).open("POST",e),a.timeout=TIMEOUT,a.onload=function(){n&&n(!0)},a.onerror=a.ontimeout=function(){n&&n(!1)},delay(function(){a.send(t)});else{if(!window.XMLHttpRequest)return!1;a=new window.XMLHttpRequest;try{a.$$inner=!0,a.overrideMimeType("text/plain"),a.open("POST",e,!0),a.setRequestHeader("brkey",i),a.timeout=TIMEOUT,a.onreadystatechange=function(){4===a.readyState&&n&&n(200===a.status)}}catch(e){}r?a.send(t):delay(function(){a.send(t)})}return!0}function isQQEnv(){return/\bQQ\b/i.test(navigator.userAgent)}function sendUpload(t,e,n){var r=uuid(),i=getUrl(t,r);if(startWith(i,"http")){if(window.navigator&&navigator.sendBeacon&&!isQQEnv()){if(void 0!==setting.sm4Config&&/^[A-Za-z0-9_-]{1,256}$/.test(setting.sm4Config.identify||setting.sm4Config.identifier)){try{var a=new Sm4(setting.sm4Config)}catch(e){var o=setting.sm4Config.identify||setting.sm4Config.identifier;return n(navigator.sendBeacon(i.replace("&BR-Encryption-Method=sm4:".concat(o),""),stringify(t)))}return n(navigator.sendBeacon(i,a.encrypt(stringify(t))))}return n(navigator.sendBeacon(i,stringify(t)))}if(void 0!==setting.sm4Config)return post(i,(a=new Sm4(setting.sm4Config)).encrypt(stringify(t)),n,e,r);post(i,stringify(t),n,e,r)}}function dataStructHandle(e,t){e=JSON.parse(e);return e.k=t,e}var jsInternalBridge={log:function(e){if(setting.enableLogger)try{isIOS(setting.osType)?window.webkit.messageHandlers.brsWKLog.postMessage(e):isHOS(setting.osType)?window.bonreePrivateInterface.call(stringify({log:e})):window.bonreePrivateInterface.log(e)}catch(e){}},webviewPerformanceTimingEvent:function(e){this.log("webviewPerformanceTimingEvent=>"+e);try{isIOS(setting.osType)?window.webkit.messageHandlers.brsWKWebviewPerformanceTimingEvent.postMessage(e):isHOS(setting.osType)?window.bonreePrivateInterface.call(stringify({h5:[dataStructHandle(e,"h5")]})):window.bonreePrivateInterface.webViewEventBus(stringify({h5:[dataStructHandle(e,"h5")]}),setting.webviewID)}catch(e){}},webviewJSErrorEvent:function(e){this.log("webviewJSErrorEvent=>"+e);try{isIOS(setting.osType)?window.webkit.messageHandlers.brsWKWebviewJSErrorEvent.postMessage(e):isHOS(setting.osType)?window.bonreePrivateInterface.call(stringify({jserror:[dataStructHandle(e,"jserror")]})):window.bonreePrivateInterface.webViewEventBus(stringify({jserror:[dataStructHandle(e,"jserror")]}),setting.webviewID)}catch(e){}},NetworkEvent:function(e){this.log("NetworkEvent=>"+e);try{isIOS(setting.osType)?window.webkit.messageHandlers.brsWKAjaxPerformanceTimingEvent.postMessage(e):isHOS(setting.osType)?window.bonreePrivateInterface.call(stringify({network:[dataStructHandle(e,"network")]})):window.bonreePrivateInterface.webViewEventBus(stringify({network:[dataStructHandle(e,"network")],imd:2}),setting.webviewID)}catch(e){}},webviewActionEvent:function(e){this.log("webviewActionEvent=>"+e);var t=JSON.parse(e);try{isIOS(setting.osType)?(isDefined(t.v.ice)?t.v.isa=!0:t.v.isa=!1,window.webkit.messageHandlers.brsWKWebviewActionEvent.postMessage(stringify(t))):isHOS(setting.osType)?window.bonreePrivateInterface.call(stringify({action:[dataStructHandle(e,"action")]})):window.bonreePrivateInterface.webViewEventBus(stringify({action:[dataStructHandle(e,"action")]}),setting.webviewID)}catch(e){}},webviewPageEvent:function(e){this.log("webviewPageEvent=>"+e);try{isIOS(setting.osType)?window.webkit.messageHandlers.brsWKWebviewPageEvent.postMessage(e):isHOS(setting.osType)?window.bonreePrivateInterface.call(stringify({view:[dataStructHandle(e,"view")]})):window.bonreePrivateInterface.webViewEventBus(stringify({view:[dataStructHandle(e,"view")]}),setting.webviewID)}catch(e){}},routeChangeData:function(e){this.log("routeChangeData=>"+e);try{window.webkit.messageHandlers.brsWKRouteChangeEvent.postMessage(e)}catch(e){}try{window.bonreePrivateInterface.call(stringify({routechange:[dataStructHandle(e,"routechange")]}))}catch(e){}try{window.bonreePrivateInterface.webViewEventBus(stringify({routechange:[dataStructHandle(e,"routechange")]}),setting.webviewID)}catch(e){}},consoleEvent:function(e){this.log("consoleData=>",e);try{isIOS(setting.osType)?window.webkit.messageHandlers.brsWKConsoleEvent.postMessage(e):isHOS(setting.osType)?window.bonreePrivateInterface.call(stringify({console:[dataStructHandle(e,"console")]})):window.bonreePrivateInterface.webViewEventBus(stringify({console:[dataStructHandle(e,"console")]}),setting.webviewID)}catch(e){}},spanEvent:function(e){this.log("spanData=>"+e);try{isIOS(setting.osType)?window.webkit.messageHandlers.brsWKSpanEvent.postMessage(e):isHOS(setting.osType)?window.bonreePrivateInterface.call(stringify({span:[dataStructHandle(e,"span")]})):window.bonreePrivateInterface.webViewEventBus(stringify({span:[dataStructHandle(e,"span")]}),setting.webviewID)}catch(e){}},customLogEvent:function(e){this.log("setCustomLog=>"+e);try{isIOS(setting.osType)?window.webkit.messageHandlers.brsWKSetCustomLog.postMessage(e):isHOS(setting.osType)?window.bonreePrivateInterface.call(stringify({customlog:[dataStructHandle(e,"customlog")]})):window.bonreePrivateInterface.webViewEventBus(stringify({customlog:[dataStructHandle(e,"customlog")]}),setting.webviewID)}catch(e){}},customMetricEvent:function(e){this.log("setCustomMetric=>"+e);try{isIOS(setting.osType)?window.webkit.messageHandlers.brsWKSetCustomMetric.postMessage(e):isHOS(setting.osType)?window.bonreePrivateInterface.call(stringify({custommetric:[dataStructHandle(e,"custommetric")]})):window.bonreePrivateInterface.webViewEventBus(stringify({custommetric:[dataStructHandle(e,"custommetric")]}),setting.webviewID)}catch(e){}},customExceptionEvent:function(e){this.log("setCustomException=>"+e);try{isIOS(setting.osType)?window.webkit.messageHandlers.brsWKSetCustomException.postMessage(e):isHOS(setting.osType)?window.bonreePrivateInterface.call(stringify({crash:[dataStructHandle(e,"crash")]})):window.bonreePrivateInterface.webViewEventBus(stringify({crash:[dataStructHandle(e,"crash")]}),setting.webviewID)}catch(e){}},customSpeedTestEvent:function(e){this.log("speedTest=>"+e);try{isIOS(setting.osType)?window.webkit.messageHandlers.brsWKsetCustomSpeedTest.postMessage(e):isHOS(setting.osType)?window.bonreePrivateInterface.call(stringify({speedtest:[dataStructHandle(e,"speedtest")]})):window.bonreePrivateInterface.webViewEventBus(stringify({speedtest:[dataStructHandle(e,"speedtest")]}),setting.webviewID)}catch(e){}},customEvent:function(e){this.log("setCustomEvent=>"+e);try{isIOS(setting.osType)?window.webkit.messageHandlers.brsWKsetCustomEvent.postMessage(e):isHOS(setting.osType)?window.bonreePrivateInterface.call(stringify({customevent:[dataStructHandle(e,"customevent")]})):window.bonreePrivateInterface.webViewEventBus(stringify({customevent:[dataStructHandle(e,"customevent")]}),setting.webviewID)}catch(e){}},MainDocumentNetworkEvent:function(e){this.log("MainDocumentNetworkEvent=>"+e);try{var t;isIOS(setting.osType)?((t=JSON.parse(e)).imd=1,window.webkit.messageHandlers.brsWKMainDocumentNetworkEvent.postMessage(stringify(t))):isHOS(setting.osType)?window.bonreePrivateInterface.call(stringify({network:[dataStructHandle(e,"network")],imd:1})):window.bonreePrivateInterface.webViewEventBus(stringify({network:[dataStructHandle(e,"network")],imd:1}),setting.webviewID)}catch(e){}},ResourceNetworkEvent:function(e){this.log("ResourceNetworkEvent=>"+e);try{isIOS(setting.osType)?window.webkit.messageHandlers.brsWKResourceNetworkEvent.postMessage(e):isHOS(setting.osType)?window.bonreePrivateInterface.call(e):window.bonreePrivateInterface.webViewEventBus(e,setting.webviewID)}catch(e){}}},WORKER,CAPACITY=50,IDLE=5e3,RETRY=3,DATA_QUEUE=[];function logData(e){setting.enableLogger&&log(e.type+"["+e.ent+"]",stringify(e.data),!0)}function uploadMetric(e,t,n){var r=new Protocol,i={ai:bonreeRUM.getSession().appId,av:setting.appVersion||"unknown",an:setting.appName||"unknown",at:4},a=(isDefined(setting.channelId)&&(i.ci=setting.channelId),r.setMonitorTime(now()).setConfigTime(setting.initTime).setSessionId(bonreeRUM.getSession().sessionID).setDeviceInfo(getDeviceInfo()).setAppInfo(i).setUserInfo(getUserInfo()).setFirstUserInfoIndex(),0),o=0,i=(forEach(e,function(e){logData(e);var t=e.ent;(t<a||0===a)&&(a=t),(o<t||0===o)&&(o=t),r.pushEventData(e)}),usdTime&&o-usdTime||o-a),i=Math.abs(i);changUsdTime(o),r.setSessionDuration(i),sendUpload(r.build(),t,n)}function send(e,t,n){if(isDefined(e)&&!isEmpty(e))try{setting.osType===BROWSER?uploadMetric(e,t,n):forEach(e,function(e){logData(e);var t={ent:e.ent};switch(delete e.ent,t.v=e.data,e.type){case WEBVIEWDATA:var n=window.top&&window.self&&window.top===window.self?1:0;t.v.wpi.imd=n,jsInternalBridge.webviewPerformanceTimingEvent(stringify(t));break;case ERROR:jsInternalBridge.webviewJSErrorEvent(stringify(t));break;case NET:jsInternalBridge.NetworkEvent(stringify(t));break;case ACTION:jsInternalBridge.webviewActionEvent(stringify(t));break;case VIEW:jsInternalBridge.webviewPageEvent(stringify(t));break;case ROUTE:jsInternalBridge.routeChangeData(stringify(t));break;case CUSTOM_LOG:jsInternalBridge.customLogEvent(stringify(t));break;case CUSTOM_METRIC:jsInternalBridge.customMetricEvent(stringify(t));break;case CRASH:jsInternalBridge.customExceptionEvent(stringify(t));break;case SPEED_TEST:jsInternalBridge.customSpeedTestEvent(stringify(t));break;case CUSTOM_EVENT:jsInternalBridge.customEvent(stringify(t));break;case CONSOLE$1:jsInternalBridge.consoleEvent(stringify(t));break;case SPAN:jsInternalBridge.spanEvent(stringify(t))}}),n&&n(!0)}catch(e){log(e),n&&n(!1)}}function prepareCleanTask(n,r){var i,a=this,o=[],s=0;if(RETRY<=s)return log("Service exception, stop reporting");function c(e,t){isEmpty(o)||!e&&o.length!==CAPACITY||(send(o,r,function(e){e?0<s&&(s=0):(s+=1,r||delay(function(){o.push(n)},IDLE/10))}),o=[],i=1===e?t:null)}forEach(n,function(e){var t=e.e;if(!isDefined(t))return o.push(e),c.call(a);var n=t[0],t=t[1],r=e.p.info;r.userTime=Math.max(0,r.timestamp-t),(i=isDefined(i)?i:n)!==n&&c.call(a,1,n),o.push(e),c.call(a)}),c.call(this,2)}function flushData(e){isEmpty(DATA_QUEUE)||(getUserIdManageClass.useAPI||getUserIdManageClass.startCatchDataFun(),log("prepareCleanTask excute"),prepareCleanTask(DATA_QUEUE,e),DATA_QUEUE=[])}function init$2(){notifierOn(FLUSH_DATA,function(e){1!==setting.osType||isDefined(WORKER)||(WORKER=setInterval(flushData,IDLE)),isLoad()&&!isDefined(WORKER)&&(WORKER=setInterval(flushData,IDLE)),flushData(e.p)})}function getXpath$2(e){var t;return e&&e.ext?(t=e.info.target||e.ext.xpath,e=e.ext.outerHTML,"xpath"+unicode$1("=")+"".concat(t)+",outerHTML"+unicode$1("=")+"".concat(e)):""}var Metric=_createClass(function e(t,n){_classCallCheck(this,e),this.d_type=n,this.data=t||{},this.$metric={};n=t&&t.info&&t.info.timestamp&&t.info.timestamp||now();this.ent=n,0!==setUserInfoTimeStamp&&(this.userMapIndex=lastUserInfoSetMapKey)});function extracted(e,t){this.$metric[e]=t}function pushQueueData(e,t){isDefined(e)&&isDefined(DATA_QUEUE)&&(t?DATA_QUEUE.unshift(e):(isDefined(e.t)&&e.t===RESOURCE_DATA&&(t=first(DATA_QUEUE),isDefined(t)&&t.t===PAGE_DATA?DATA_QUEUE.splice(1,0,e):DATA_QUEUE.unshift(e)),DATA_QUEUE.push(e)),DATA_QUEUE.length>=CAPACITY)&&(log("pusher函数进行数据推送"),flushData())}function _createForOfIteratorHelper$2(e,t){var n,r,i,a,o="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(o)return r=!(n=!0),{s:function(){o=o.call(e)},n:function(){var e=o.next();return n=e.done,e},e:function(e){r=!0,i=e},f:function(){try{n||null==o.return||o.return()}finally{if(r)throw i}}};if(Array.isArray(e)||(o=_unsupportedIterableToArray$2(e))||t&&e&&"number"==typeof e.length)return o&&(e=o),a=0,{s:t=function(){},n:function(){return a>=e.length?{done:!0}:{done:!1,value:e[a++]}},e:function(e){throw e},f:t};throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray$2(e,t){var n;if(e)return"string"==typeof e?_arrayLikeToArray$2(e,t):"Map"===(n="Object"===(n=Object.prototype.toString.call(e).slice(8,-1))&&e.constructor?e.constructor.name:n)||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?_arrayLikeToArray$2(e,t):void 0}function _arrayLikeToArray$2(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}Metric.prototype.info=function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"",n=2<arguments.length?arguments[2]:void 0,r=3<arguments.length&&void 0!==arguments[3]&&arguments[3],i=this.data.info;return isDefined(i)&&(i=i[e],isDefined(i)||(i=t),isDefined(n)&&(e=n),r&&""===i||(this.$metric[e]=i)),this},Metric.prototype.metric=function(e){var t=1<arguments.length&&void 0!==arguments[1]?arguments[1]:"",n=2<arguments.length?arguments[2]:void 0,r=this.data.metric;return isDefined(r)&&(r=r[e],isDefined(r)||(r=isDefined(t)?t:-1),isDefined(n)&&(e=n),extracted.call(this,e,r)),this},Metric.prototype.build=function(e){var e={type:this.d_type,ent:(e&&e.ent?e:this).ent,data:{},sin:this.userMapIndex},t=this.$metric;if(isDefined(this.data.ext))switch(this.d_type){case ACTION:t.i=isDefined(this.data.ext)?getXpath$2(this.data):"";break;case NET:break;case ERROR:case ROUTE:break;default:t.ext=this.data.ext}return e.data=t,e};var domianTime=0,connetcTime=0,sslt=0,rti=0,dti=0;function dataFactory(e){var e=e.p.metric,t=e.domainLookupStart,n=e.domainLookupEnd,r=e.connectStart,i=e.connectEnd,a=e.responseStart,o=e.responseEnd,s=e.secureConnectionStart,e=e.fetchStart;isDefined(t)&&isDefined(n)&&0<t&&(domianTime=0<n-t?n-t:0),isDefined(r)&&isDefined(i)&&0<r&&(connetcTime=0<i-r?i-r:0),isDefined(i)&&isDefined(s)&&0<s&&(sslt=0<s?i-s:0),isDefined(a)&&0<a?(isDefined(i)&&(rti=0<a-i?a-i:0),isDefined(o)&&(dti=0<o-a?o-a:0)):isDefined(e)&&(dti=0<o-e?o-e:0)}function sensitiveTypeHandle(e,n){var t=e.mk||e.maskKeys,r=e.mt||e.maskType,e=isDefined(t.qka||t.urlKeys)&&(t.qka||t.urlKeys);if(t.aqk||t.allQueryKeys)switch(r){case 1:n.p.info.url=n.p.info.url.split("?")[0],isDefined(setting.reqURLKey)&&(n.p.info.reqUrlDta=null);break;case 2:n.p.info.url=n.p.info.url.replace(/(\?|&)([^=&]+)=([^&]+)/g,"$1$2=**"),isDefined(setting.reqURLKey)&&(n.p.info.reqUrlDta=n.p.info.url.split("?")[1]);break;default:return}else if(isDefined(e)&&0<e.length&&-1<n.p.info.url.indexOf("?")){for(var i=n.p.info.url.split("?")[1].split("&"),a="",o=0;o<i.length;o++){var s=i[o].split("=");switch(r){case 1:includes$1(t.qka||t.urlKeys,s[0])||(a+=i[o]+"&");break;case 2:includes$1(t.qka||t.urlKeys,s[0])?a+=i[o].split("=")[0]+"=**&":a+=i[o]+"&"}}isDefined(setting.reqURLKey)&&(n.p.info.reqUrlDta=a.slice(0,-1)),""!==a.slice(0,-1)?n.p.info.url=n.p.info.url.split("?")[0]+"?"+a.slice(0,-1):n.p.info.url=n.p.info.url.split("?")[0]}var c=(c=isDefined(t.reqhka||t.requestHeadersKeys)?t.reqhka||t.requestHeadersKeys:[]).map(function(e){return e.toLowerCase()});if(t.areqh||t.allRequestHeaders)switch(r){case 1:n.p.info[REQUEST_HEADER]=void 0,n.p.info.reqhtData={};break;case 2:isDefined(n.p.info[REQUEST_HEADER])&&(n.p.info[REQUEST_HEADER]=n.p.info[REQUEST_HEADER].replace(/:(.*?)($|\r\n)/g,":**$2")),isDefined(setting.reqHeaderTraceKey)&&0<setting.reqHeaderTraceKey.length&&(isEmpty(n.p.info.reqhtData)||forEach(n.p.info.reqhtData,function(e,t){n.p.info.reqhtData[t]="**"}));break;default:return}else switch(r){case 1:if(isDefined(n.p.info[REQUEST_HEADER])&&isDefined(c)&&0<c.length){var l,d=_createForOfIteratorHelper$2(c);try{for(d.s();!(l=d.n()).done;){var u=l.value,f=new RegExp(u+":(.*?)(\r\n|$)","i");f.test(n.p.info[REQUEST_HEADER])&&(n.p.info[REQUEST_HEADER]=n.p.info[REQUEST_HEADER].replace(f,""))}}catch(e){d.e(e)}finally{d.f()}}if(isDefined(setting.reqHeaderTraceKey)&&0<setting.reqHeaderTraceKey.length&&!isEmpty(n.p.info.reqhtData))for(var p in n.p.info.reqhtData)includes$1(c,p.toLowerCase())&&delete n.p.info.reqhtData[p];break;case 2:if(isDefined(n.p.info[REQUEST_HEADER])&&isDefined(c)&&0<c.length){var h,m=_createForOfIteratorHelper$2(c);try{for(m.s();!(h=m.n()).done;){var g=h.value,_=new RegExp(g+":(.*?)(\r\n|$)","i");_.test(n.p.info[REQUEST_HEADER])&&(n.p.info[REQUEST_HEADER]=n.p.info[REQUEST_HEADER].replace(_,function(e,t){return e.replace(t,"**")}))}}catch(e){m.e(e)}finally{m.f()}}if(isDefined(setting.reqHeaderTraceKey)&&0<setting.reqHeaderTraceKey.length&&!isEmpty(n.p.info.reqhtData))for(var y in n.p.info.reqhtData)includes$1(c,y.toLowerCase())&&(n.p.info.reqhtData[y]="**");break;default:return}var E=(E=isDefined(t.reshka||t.responseHeadersKeys)?t.reshka||t.responseHeadersKeys:[]).map(function(e){return e.toLowerCase()});if(t.aresh||t.allResponseHeaders)switch(r){case 1:isDefined(n.p.info.responseHeader)&&delete n.p.info.responseHeader,isEmpty(n.p.info.reshtData)||delete n.p.info.reshtData,isDefined(n.p.info.guid)&&delete n.p.info.guid,isDefined(n.p.info.xBrResponse)&&delete n.p.info.xBrResponse,isDefined(n.p.info.traceResponse)&&delete n.p.info.traceResponse;break;case 2:isDefined(n.p.info.responseHeader)&&(n.p.info.responseHeader=n.p.info.responseHeader.replace(/:(.*?)($|\r\n)/g,":**$2")),isEmpty(n.p.info.reshtData)||forEach(n.p.info.reshtData,function(e,t){n.p.info.reshtData[t]="**"}),isDefined(n.p.info.guid)&&""!==n.p.info.guid&&(n.p.info.guid="**"),isDefined(n.p.info.xBrResponse)&&""!=n.p.info.xBrResponse&&(n.p.info.xBrResponse="**"),isDefined(n.p.info.traceResponse)&&""!==n.p.info.traceResponse&&(n.p.info.traceResponse="**");break;default:;}else switch(r){case 1:if(isDefined(n.p.info.responseHeader)){var v,w=_createForOfIteratorHelper$2(E);try{for(w.s();!(v=w.n()).done;){var T=v.value,b=new RegExp(T+":(.*?)(\r\n|$)","i");b.test(n.p.info.responseHeader)&&(n.p.info.responseHeader=n.p.info.responseHeader.replace(b,""))}}catch(e){w.e(e)}finally{w.f()}}isEmpty(n.p.info.reshtData)||forEach(n.p.info.reshtData,function(e,t){includes$1(E,t.toLowerCase())&&delete n.p.info.reshtData[t]}),isDefined(n.p.info.guid)&&includes$1(E,"br-resp-key")&&delete n.p.info.guid,isDefined(n.p.info.xBrResponse)&&includes$1(E,"x-br-response")&&delete n.p.info.xBrResponse,isDefined(n.p.info.traceResponse)&&includes$1(E,"traceresponse")&&delete n.p.info.traceResponse;break;case 2:if(isDefined(n.p.info.responseHeader)){var S,A=_createForOfIteratorHelper$2(E);try{for(A.s();!(S=A.n()).done;){var I=S.value,D=new RegExp(I+":(.*?)(\r\n|$)","i");D.test(n.p.info.responseHeader)&&(n.p.info.responseHeader=n.p.info.responseHeader.replace(D,function(e,t){return e.replace(t,"**")}))}}catch(e){A.e(e)}finally{A.f()}}isEmpty(n.p.info.reshtData)||forEach(n.p.info.reshtData,function(e,t){includes$1(E,t.toLowerCase())&&(n.p.info.reshtData[t]="**")}),isDefined(n.p.info.guid)&&includes$1(E,"br-resp-key")&&(n.p.info.guid="**"),isDefined(n.p.info.xBrResponse)&&includes$1(E,"x-br-response")&&(n.p.info.xBrResponse="**"),isDefined(n.p.info.traceResponse)&&includes$1(E,"traceresponse")&&(n.p.info.traceResponse="**");break;default:;}}function sensitiveRuleHandle(t){if(isDefined(setting.sensitiveNetworkRule)||isDefined(setting.snr)){try{var n,r=t.p.info.url;if(0===t.p.info.url.indexOf("http")&&(n=t.p.info.url.match(/^(https?\:)\/\/(([^:\/?#]*)(?:\:([0-9]+))?)([\/]{0,1}[^?#]*)(\?[^#]*|)(#.*|)$/)[3]),0===t.p.info.url.indexOf("file:")&&(n=t.p.info.url.match(/^(file?\:)\/\/(([^:\/?#]*)(?:\:([0-9]+))?)([\/]{0,1}[^?#]*)(\?[^#]*|)(#.*|)$/)[3]),0===t.p.info.url.indexOf("ws")&&(n=t.p.info.url.match(/^(wss?\:)\/\/(([^:\/?#]*)(?:\:([0-9]+))?)([\/]{0,1}[^?#]*)(\?[^#]*|)(#.*|)$/)[3]),0!==t.p.info.url.indexOf("http")&&0!==t.p.info.url.indexOf("file:")&&0!==t.p.info.url.indexOf("ws"))return t}catch(e){return t}var e,i=setting.sensitiveNetworkRule||setting.snr,c=function(e){if(1===e.type||1===e.t){if(!isDefined(e.c||e.content))return!1;switch(e.r||e.rule){case 1:if(n.toLowerCase()===(e.c||e.content).toLowerCase())return!0;break;case 2:if(-1<n.toLowerCase().indexOf((e.c||e.content).toLowerCase()))return!0;break;case 3:if(startWith(n.toLowerCase(),(e.c||e.content).toLowerCase()))return!0;break;case 4:if(endWith(n.toLowerCase(),(e.c||e.content).toLowerCase()))return!0;default:return!1}}if(2===e.type||2===e.t)switch(e.r||e.rule){case 1:if(r===(e.c||e.content))return!0;break;case 2:if(-1<r.indexOf(e.c||e.content))return!0;break;case 3:if(startWith(r,e.c||e.content))return!0;break;case 4:if(endWith(r,e.c||e.content))return!0;default:return!1}return!1},a=function(e,t){switch(t){case 1:var n,r=_createForOfIteratorHelper$2(e);try{for(r.s();!(n=r.n()).done;){var i=n.value;if(c(i))return!0}}catch(e){r.e(e)}finally{r.f()}return!1;case 2:var a,o=_createForOfIteratorHelper$2(e);try{for(o.s();!(a=o.n()).done;){var s=a.value;if(!c(s))return!1}}catch(e){o.e(e)}finally{o.f()}return!0;default:return!1}},o=_createForOfIteratorHelper$2(i);try{for(o.s();!(e=o.n()).done;){var s=e.value;1===s.t||1===s.type?a(s.sa||s.scopes,1)&&sensitiveTypeHandle(s,t):2!==s.t&&2!==s.type||a(s.sa||s.scopes,2)&&sensitiveTypeHandle(s,t)}}catch(e){o.e(e)}finally{o.f()}}return t}function specialCase(e){if(isLoad||(e.p.metric.onloadEvent=0),isDefined(setting.reqBodyKey)){var r="";if(isJSON(e.p.info.requestBody))try{var n=new Function("","return "+e.p.info.requestBody)();forEach(setting.reqBodyKey,function(e,t){isDefined(n[e])&&(r+="".concat(e,"=").concat(escape(n[e]),"&"))}),e.p.info[NET_CBBQ]=r.slice(0,-1)}catch(e){}else if(isString$2(e.p.info.requestBody))try{var t=e.p.info.requestBody.split("&");forEach(t,function(n){forEach(setting.reqBodyKey,function(e,t){e&&-1!==n.indexOf(e+"=")&&(r+=n.split("=")[0]+"="+escape(n.split("=")[1])+"&")})}),e.p.info[NET_CBBQ]=r.slice(0,-1)}catch(e){}}if(isDefined(setting.reqURLKey)&&isString$2(e.p.info.reqUrlDta))try{var i="",a=e.p.info.reqUrlDta.split("&");forEach(a,function(n){forEach(setting.reqURLKey,function(e,t){e&&n.split("=")[0]===e&&(i+=n.split("=")[0]+"="+escape(n.split("=")[1])+"&")})}),e.p.info[NET_CBQ]=i.slice(0,-1)}catch(e){}if(isDefined(e.p.metric)&&isDefined(e.p.metric[CALLBACK_END])&&isDefined(e.p.metric[CALLBACK_START])&&(e.p.metric[CALLBACK_TIME]=e.p.metric[CALLBACK_END]-e.p.metric[CALLBACK_START],e.p.metric[CALLBACK_TIME]=0<e.p.metric[CALLBACK_TIME]&&1e3*e.p.metric[CALLBACK_TIME]||0),isDefined(setting.reqHeaderKey)&&isDefined(e.p.info)&&isDefined(e.p.info[REQUEST_HEADER]))try{var o="",s=e.p.info[REQUEST_HEADER].split("\r\n");forEach(s,function(t){forEach(setting.reqHeaderKey,function(e){isDefined(e)&&""!=e&&t.split(":")[0].toLowerCase()===e.toLowerCase()&&(o+=t.split(":")[0]+"="+escape(t.split(":")[1])+"&")})}),e.p.info[NET_CBHQ]=o.slice(0,-1)}catch(e){}return e}function fileHandle(e){var t=e.data;return isDefined(t.tid)&&""===t.tid&&delete t.tid,isDefined(t.xbr)&&""===t.xbr&&delete t.xbr,isDefined(t.trsp)&&""===t.trsp&&delete t.trsp,isDefined(t.tpar)&&""===t.tpar&&delete t.tpar,isDefined(t.cbhq)&&""===t.cbhq&&delete t.cbhq,isDefined(t.cbbq)&&""===t.cbbq&&delete t.cbbq,isDefined(t.cbq)&&""===t.cbq&&delete t.cbq,isDefined(t.ti)&&""===t.ti&&delete t.ti,isDefined(t.ec)&&200<=t.ec&&t.ec<400&&(isDefined(t.ep)&&delete t.ep,isDefined(t.eop))&&delete t.eop,isDefined(t.ec)&&400<t.ec&&t.ec<=602&&isDefined(t.eop)&&(t.eop=4),isDefined(t.cbhq)&&(t.cbhq=t.cbhq.replace("/","%2F")),isDefined(t.ec)&&602===t.ec&&isDefined(t.ru)&&0===t.ru.indexOf("file:")&&(t.ec=200,delete t.eop,delete t.ep,delete t.em),e}function headerData(t){var n;if(isDefined(setting.reqHeaderTraceKey)&&0<setting.reqHeaderTraceKey.length&&(n={},isDefined(t.p.info.reqhtData&&Object.getOwnPropertyNames(t.p.info.reqhtData).length<=64)?n=t.p.info.reqhtData:Object.getOwnPropertyNames(t.p.info.reqhtData).slice(0,64).forEach(function(e){n[e]=t.p.info.reqhtData[e]}),isEmpty(n)||(t.p.info.reqht=n)),isDefined(setting.respHeaderTraceKey)&&0<setting.respHeaderTraceKey.length){for(var e={},r=0,i=0,a=setting.respHeaderTraceKey.length-1;i<=a;i++)r<64&&isDefined(t.p.info.reshtData)&&isDefined(t.p.info.reshtData[setting.respHeaderTraceKey[i]])&&(r+=1,e[setting.respHeaderTraceKey[i]]=t.p.info.reshtData[setting.respHeaderTraceKey[i]]);isEmpty(e)||(t.p.info.resht=e)}}function requestAction(e){if(isDefined(window.$bonreeReplayUrl)&&-1<e.p.info.url.indexOf(window.$bonreeReplayUrl))return null;dataFactory.call(this,e),isDefined(setting.sensitiveNetworkRule||setting.snr)&&sensitiveRuleHandle.call(this,e),specialCase.call(this,e),headerData.call(this,e);var t=e.p.info,n=setting.hcs,n=(isDefined(n)&&isDefined(t.requestHeader)&&isDefined(t.responseHeader)&&(0===n||1===n&&t.status<400||1===n&&t.code<400)&&(delete e.p.info.requestHeader,delete e.p.info.responseHeader),e&&e.p.info.url&&-1<e.p.info.url.indexOf("https")?2:1),t=getMineTypeByHeader[RESPONSE_HEADER]||getMineTypeByHeader(e[REQUEST_HEADER])||getMineTypeByUrl(e[URL$1])||"text/html",r=new Metric(e.p,NET);return(isDefined(r.data.info[NET_EC])&&400<r.data.info[NET_EC]||!isDefined(r.data.info[NET_EC]))&&isDefined(window.BRLog)&&window.BRLog.__sendNetInfo(r.data.info),r.info("id").info(URL$1,"","ru").info(NET_METHOD,"","m").info(NET_IP,"","ti").info(NET_PORT,0,"tp").info(REQUEST_HEADER,"","rh",!0).info(RESPONSE_HEADER,"","rhe",!0).info(NET_TID,"","tid").info(NET_XBR,"","xbr").info(NET_TRACE,"","trsp").info("reqht","","reqht",!0).info("resht","","resht",!0).info(E_TYPE,"http","ep",!0).info(NET_EOP,"","eop",!0).info(NET_TYPE,3,"art").info(PAGE_ID,"","pvid").info(CUSTOM_IC,!1,"ic").info(NET_CBBQ,"","cbbq").info(NET_CBHQ,"","cbhq").info(NET_CBQ,"","cbq").info("ret",t,"ret").metric("",domianTime,"dt").metric("",connetcTime,"ct").metric("",sslt,"sslt").metric(REQUEST_START,0,"rt").metric("",rti,"rti").metric("",dti,"dti").metric(DECODED_BODY_SIZE,0,"ds").metric(UPLOAD_BODY_SIZE,0,"rds").metric(NET_PT,n,"pt"),e.p&&e.p.info.ai&&r.info("ai","","ai"),(e.p&&200==e.p.info[NET_EC]||e.p&&200==e.p[STATUS])&&r.info(NET_EOP,0,"eop"),e.p&&isDefined(e.p.info[NET_EC])&&r.info(NET_EC,200,"ec"),e.p&&isDefined(e.p.info[STATUS])&&r.info(STATUS,200,"ec"),e.p&&"XHR"===e.p.info[E_TYPE]&&r.info(MESSAGE,"","em",!0),e.p&&"FETCH"===e.p.info[E_TYPE]&&r.info(STATUS_TEXT,"","em",!0),fileHandle(r.build())}function WebviewPerformanceToNet(e){var t=new Metric(e.p,NET),e=getMineTypeByUrl(e[URL$1])||"text/html";return t.info("id").info("ru").info("m").info("ti").info("tp").info("art").info(PAGE_ID,"","pvid").info("ic").info("ret",e,"ret").info("dt").info("ct").info("sslt").info("rt").info("rti").info("dti").info("ds",0,"ds").info("rds",0,"rds").info("pt"),isDefined(t.data.info.ep)&&t.info("ep"),isDefined(t.data.info.em)&&t.info("em"),isDefined(t.data.info.eop)&&t.info("eop"),isDefined(t.data.info.ec)&&t.info("ec"),fileHandle(t.build())}var synced=!1,resources=[],FP,FCP,LCP,longTaskEntries=[];function getlongtask(){try{new PerformanceObserver(function(e){longTaskEntries=e.getEntries()}).observe({entryTypes:["longtask"]})}catch(e){}}function getTTI(e,t){for(var n=performance.timing.domContentLoadedEventEnd-performance.timing.domContentLoadedEventStart,r=performance.getEntriesByType("resource"),i=e,a=t,o=r,s=n,c=i;i+5e3<=5e4;){var c=i,l=a.filter(function(e){return e.startTime<i+5e3&&e.startTime+e.duration>i});if(l.length){var l=l[l.length-1];i=l.startTime+l.duration}else{if(!(2<(l=o.filter(function(e){return!(e.startTime>=i+5e3||e.startTime+e.duration<=i)})).length))return Math.max(c,s);l=l[0];i=l.startTime+l.duration}}return Math.max(c,s)}function onload$1(){log("page load done,wait 200 ms emit"),i$1("page:load",!0),setting.pageUrl=sensitiveRuleHandle({p:{info:{url:setting.pageUrl}}}).p.info.url,notifierEmit({t:PAGE_LOAD}),getlongtask(),delay(checkMetrics,1e3)}function checkMetrics(e){synced||(synced=!0,notifierEmit({t:PAGE_DATA,p:getAllPageMetrics()}),notifierEmit({t:RESOURCE_DATA,p:getResourceMetrics()})),notifierEmit({t:FLUSH_DATA,p:!!e})}function getRelativePeriod(e,t){return 0<e?e-t:0}function getResourceLoadTime(){var e=window.performance||window.msPerformance||window.webkitPerformance,t=e.getEntriesByType("resource")||[];return 0===t.length?isDefined(e.timing)?e.timing[LOAD_EVENT_END]-e.timing[NAVIGATION_START]:10:(e=map(t,function(e){return e.responseEnd}).sort(function(e,t){return t-e}),Math.round(e[0]))}function getAllPageMetrics(){var e,t,n=window.screen||{},n={timestamp:now(0),title:title(),referrer:document.referrer,charset:document.characterSet||document.charset||"",embed:window.top&&window.self&&window.top!==window.self?1:0,width:n.width||0,height:n.height||0,completed:isPageLoaded()?1:0,param:location&&location.search||""},r=window.performance&&window.performance.timing;return n.supported=isDefined(r)?1:0,r?(r={},e=(t=window.performance.timing)[NAVIGATION_START]||t[FETCH_START],r[NAVIGATION_START]=e||"",r[UNLOAD_EVENT_START]=getRelativePeriod(t[UNLOAD_EVENT_START],e),r[UNLOAD_EVENT_END]=getRelativePeriod(t[UNLOAD_EVENT_END],e),r[REDIRECT_START]=getRelativePeriod(t[REDIRECT_START],e),r[REDIRECT_END]=getRelativePeriod(t[REDIRECT_END],e),r[FETCH_START]=getRelativePeriod(t[FETCH_START],e),r[DOMAIN_LOOKUP_START]=getRelativePeriod(t[DOMAIN_LOOKUP_START],e),r[DOMAIN_LOOKUP_END]=getRelativePeriod(t[DOMAIN_LOOKUP_END],e),r[CONNECT_START]=getRelativePeriod(t[CONNECT_START],e),r[CONNECT_END]=getRelativePeriod(t[CONNECT_END],e),r[SECURE_CONNECTION_START]=getRelativePeriod(t[SECURE_CONNECTION_START],e),r[REQUEST_START]=getRelativePeriod(t[REQUEST_START],e),r[RESPONSE_START]=getRelativePeriod(t[RESPONSE_START],e),r[RESPONSE_END]=getRelativePeriod(t[RESPONSE_END],e),r[DOM_LOADING]=getRelativePeriod(t[DOM_LOADING],e),r[DOM_INTERACTIVE]=getRelativePeriod(t[DOM_INTERACTIVE],e),r[DOM_CONTENT_LOADED_EVENT_START]=getRelativePeriod(t[DOM_CONTENT_LOADED_EVENT_START],e),r[DOM_CONTENT_LOADED_EVENT_END]=getRelativePeriod(t[DOM_CONTENT_LOADED_EVENT_END],e),r[DOM_COMPLETE]=getRelativePeriod(t[DOM_COMPLETE],e),r[LOAD_EVENT_START]=getRelativePeriod(t[LOAD_EVENT_START],e),r[LOAD_EVENT_END]=getRelativePeriod(t[LOAD_EVENT_END],e),r[FULL_RESOURCE_LOAD_TIME]=getResourceLoadTime(),n[DURATION]=isPageLoaded()?Math.max(0,r[LOAD_EVENT_END],r[LOAD_EVENT_START]):Math.round(tillNow()/1e3),window.performance.getEntriesByType&&(t=last(window.performance.getEntriesByType("navigation")),isDefined(t))&&(n.type=t.type,n[NEXT_HOP_PROTOCOL]=t[NEXT_HOP_PROTOCOL],r[REDIRECT_COUNT]=t[REDIRECT_COUNT],r[TRANSFER_SIZE]=t[TRANSFER_SIZE],r[ENCODED_BODY_SIZE]=t[ENCODED_BODY_SIZE],r[DECODED_BODY_SIZE]=t[DECODED_BODY_SIZE],r[RESPONSE_STATUS]=t[RESPONSE_STATUS]),e=getPaintMetrics(),extend(r,e),e&&e.fcp&&(t=getTTI(e.fcp,longTaskEntries)||0,extend(r,{tti:Math.round(t)})),{info:n,metric:r}):(log("Navigation timing API is not supported"),n.DURATION=tillNow(),{info:n})}function getResourceMetrics(){var e;return window.performance&&window.performance.getEntriesByType?(e=resources.concat(window.performance.getEntriesByType("resource")),resources=[],map(e,function(e){var t,n,r=e[INITIATOR_TYPE],i=e.entryType,a=getMineTypeByUrl(e.name);if(isDefined(e.name)&&-1===e.name.indexOf(setting.uploadAddrHttps)||isDefined(e.name)&&-1===e.name.indexOf(setting.uploadAddrHttp))return n={},(t={}).name=sensitiveRuleHandle({p:{info:{url:e.name}}}).p.info.url,t[START_TIME]=getFixedMetric(e,START_TIME),t[INITIATOR_TYPE]=r,t.entryType=i,t.ret=a,n[WORKER_START]=getFixedMetric(e,WORKER_START),n[REDIRECT_START]=getFixedMetric(e,REDIRECT_START),n[REDIRECT_END]=getFixedMetric(e,REDIRECT_END),n[FETCH_START]=getFixedMetric(e,FETCH_START),n[DOMAIN_LOOKUP_START]=getFixedMetric(e,DOMAIN_LOOKUP_START),n[DOMAIN_LOOKUP_END]=getFixedMetric(e,DOMAIN_LOOKUP_END),n[CONNECT_START]=getFixedMetric(e,CONNECT_START),n[CONNECT_END]=getFixedMetric(e,CONNECT_END),n[SECURE_CONNECTION_START]=getFixedMetric(e,SECURE_CONNECTION_START),n[REQUEST_START]=getFixedMetric(e,REQUEST_START),n[RESPONSE_START]=getFixedMetric(e,RESPONSE_START),n[RESPONSE_END]=getFixedMetric(e,RESPONSE_END),n[RESPONSE_STATUS]=e[RESPONSE_STATUS],t[DURATION]=isDefined(e[DURATION])?getFixedMetric(e,DURATION):n[RESPONSE_END]-n[START_TIME],isDefined(e[DECODED_BODY_SIZE])&&(t[NEXT_HOP_PROTOCOL]=e[NEXT_HOP_PROTOCOL],n[TRANSFER_SIZE]=e[TRANSFER_SIZE],n[ENCODED_BODY_SIZE]=e[ENCODED_BODY_SIZE]>=Math.pow(2,64)?Math.pow(2,60):e[ENCODED_BODY_SIZE],n[DECODED_BODY_SIZE]=e[DECODED_BODY_SIZE]),{info:t,metric:n}})):(log("The resource timing API is not supported"),[])}function getPaintMetrics(e){var t,n;return isPageLoaded()?(isDefined(FP)&&isDefined(FCP)||window.performance.getEntriesByType&&forEach(window.performance.getEntriesByType("paint"),function(e){"first-paint"===e.name&&(FP=e[START_TIME]&&e[START_TIME]||0),"first-contentful-paint"===e.name&&(FCP=e[START_TIME]&&e[START_TIME]||0)}),isDefined(FP)||(t=(n=window.performance.timing)[NAVIGATION_START],n.msFirstPaint&&(log("FP from IE"),FP=n.msFirstPaint-t),window.chrome&&window.chrome.loadTimes&&(n=window.chrome.loadTimes())&&n.firstPaintTime&&(log("FP from chrome legacy"),FP=n.firstPaintTime-t)),{fp:FP?Math.round(FP):0,fcp:FCP?Math.round(FCP):0,lcp:LCP?Math.round(LCP):0}):log("Leave the page ahead of time without calculating the paint index")}function listenForPaintMetrics(){if(!isDefined(window.PerformanceObserver))return log("Performanceobserver is not supported");var e=new PerformanceObserver(function(e){e=e.getEntries();forEach(e,function(e){"first-paint "===e.name&&(FP=e[START_TIME]&&e[START_TIME]||""),"first-contentful-paint"===e.name&&(FCP=e[START_TIME]&&e[START_TIME]||""),"largest-contentful-paint"===e.entryType&&(LCP=(LCP=e.renderTime||e.loadTime)||"")})});try{e.observe({entryTypes:["paint","largest-contentful-paint"]})}catch(e){log("Monitoring LCP is not supported")}}function listenForDumpResources(){var e;window.performance&&window.performance.getEntriesByType&&(e=function(t){return function(){log("Dump when resource buffer is full");var e=window.performance.getEntriesByType("resource"),e=(notifierEmit({t:RESOURCE_DUMP,p:e}),synced||(resources=resources.concat(e)),t+"learResourceTimings");e in window.performance&&window.performance[e]()}},addListener(window.performance,"resourcetimingbufferfull",e("c"),!1),addListener(window.performance,"webkitresourcetimingbufferfull",e("webkitC"),!1))}function listenForHideEvents(){t=[!1,!1,!1],n=[NIL_FN,NIL_FN,NIL_FN];var t,n,e=function(e){t[e]||(n[e]=function(){notifierEmit({t:PAGE_INVISIBLE,p:{info:getTime()}}),checkMetrics(!0)},t[e]=!0),(0,n[e])()};on$1(window,"beforeunload",function(){log("Monitoring LCP is not supported"),e(0)},!1),on$1(window,"pagehide",function(){e(1)},!1),addListener(document,"visibilitychange",function(){"hidden"===document.visibilityState?e(2):notifierEmit({t:PAGE_VISIBLE,p:{info:getTime()}})}),on$1(window,"pageshow",function(e){e.persisted&&notifierEmit({t:PAGE_VISIBLE,p:{info:getNavigationStart()}})})}function initDocumentCollector(){"complete"!==document.readyState||synced?on$1(window,"load",onload$1,!1):(warn("it is recommended to inject the probe before the page is fully loaded and Ignore WebView welcome page"),startWith(document.location.href,"about:")&&startWith(document.URL,"about:")||onload$1.call()),addListener(document,"DOMContentLoaded",function(){notifierEmit({t:PAGE_READY})}),listenForPaintMetrics(),listenForDumpResources(),listenForHideEvents()}function errorIndex(e){return e.message+e.line+e.column}function getNumber(e){e=Number(e);return isNaN(e)?0:e}function extendFromStack(e,t,n){if(!isReadable(e.file)&&t){log("Attempt to recover error location information from the stack");try{for(var r=/[@(\s]+?([\S]+):(\d+):(\d+)\)?/,i=/[@(\s]+?([\S]+):(\d+)\)?/,a=/([^@(\s)]+):(\d+):(\d+)/,o=/([^@(\s)]+):(\d+)/,s=t.split("\n"),c=!0,l=0;l<s.length;l++){var d=s[l];if(r.test(d)||i.test(d)){if(!c||!n){var u=last(d.split(/\s+/)),f=a.exec(u)||o.exec(u);e.file=f[1].replace(/^\(/,""),e.line=getNumber(f[2]),e.column=getNumber(f[3]);break}c=!1,s[l]=""}}n&&(e.stack=s.join("\n").replace(/\n+/g,"\n").replace(/^\n/,""))}catch(e){log("Failed to grab error location information")}}}function extendFromError(e,t,n){var r;isDefined(t)&&(r=t.name||t.constructor&&t.constructor.name,e.name=r&&toString$2(r)||"",isReadable(e.message)||(e.message=(isReadable(r)?r+": ":"")+t.message),isReadable(e.file)||(e.file=t.fileName),isDefined(e.line)||(e.line=t.lineNumber,e.column=t.columnNumber),e.stack=t.stack,extendFromStack(e,e.stack,n))}function submitError(e,t){e={info:extend(e,{message:withLength(e.message,200),duration:0,type:isDefined(e.type)?e.type:0,count:1,file:withLength(e.file,200)||"<anonymous>",stack:withLength(e.stack,5e3)})};isDefined(t)&&(e.ext=t),debouncer$1.event(e)}var debouncer$1=new Debouncer(function(e){notifierEmit({t:ERROR_DATA,p:e})},setting.debounce);function hackError(){log("hack error"),window.onerror=hack(window.onerror,function(e,t,n,r,i){e=extend({message:e,line:n,column:r,file:t},getTime());extendFromError(e,i),submitError(e)})}debouncer$1.equalsWith(function(e,t){return errorIndex(e.info)===errorIndex(t.info)}),notifierOn(FLUSH_DATA,function(){debouncer$1.flush()});var init$1=function(){hackError(),notifierOn(PAGE_READY,function(){hackError()}),addListener(window,"error",function(e){var t=e.target||e.srcElement;isDefined(t)&&(t instanceof HTMLScriptElement||t instanceof HTMLLinkElement||t instanceof HTMLImageElement)&&notifierEmit({t:ERROR_DATA,p:{info:extend({message:xpath(t),duration:0,type:1,count:1,file:withLength(t.src||e.filename,200),line:0,column:0,stack:""},getTime())}})},!0),addListener(window,"unhandledrejection",function(e){isDefined(e)&&(isString$2(e.reason)?submitError(extend({message:e.reason,line:0,column:0},getTime())):error(e.reason))},!0)},error=function(e){if(!isDefined(e))return log("Invalid user defined error");var t,n=getTime();"string"==typeof e?(n.message=e,extendFromError(n,e=new Error(e),!0)):window.ErrorEvent&&e instanceof ErrorEvent?(extend(n,{message:e.message,line:e.lineno,column:e.colno,file:e.filename}),extendFromError(n,e.error),isReadable(n.file)||e.target&&(n.file=e.target.baseURI)):(extendFromError(n,e),"number"==typeof e.type&&(n.type=e.type),t=e.ext),submitError(n,t)},pageLifecycleId="00000000",corelatianIdMap={};function userEventTransform(e){var t=new Metric(e,ACTION);return t.info("vn","","vn").info(DURATION,0,"lt").info(TAG_NAME,"","n").info(TYPE$3,0,"t").info(SOURCE_OF_ACTION,1,"sa").info(IS_SLOW,!1,"is").info(IS_CUSTOM,!1,"ic").info(VALUE,"","i").info("ci","","ci").info("vt",1,"vt"),isDefined(e.info[PARAM])&&""!==e.info[PARAM]&&t.info(PARAM,"","p"),t.build()}function buildAndUpdateCI(e){var t;pageLifecycleId=buildViewID(),isDefined(corelatianIdMap[setting.pageViewId])?(t=corelatianIdMap[setting.pageViewId],1===e&&(t[CORRELATION_ID]=pageLifecycleId,t[IS_EXIT]=!1),2===e&&(t[IS_EXIT]=!0)):((t={})[MODEL$1]=e,t[CORRELATION_ID]=pageLifecycleId,t[IS_EXIT]=!1,corelatianIdMap[setting.pageViewId]=t)}function setShowStartTime(e,t){var n;isDefined(corelatianIdMap[setting.pageViewId])&&((n=corelatianIdMap[setting.pageViewId]).timestamp=e,n.loadtime=t)}function formatPageEventData(e,t){var n,e=e.info;return isDefined(e)?((n={})[TIMESTAMP]=isDefined(e[TIMESTAMP])?e[TIMESTAMP]:now(),n[DURATION]=isDefined(e[DURATION])?1e3*e[DURATION]:1e3,n[MODEL$1]=t,n[IS_MAIN_DOCUMENT]=window.top&&window.self&&window.top===window.self?1:0,n[CORRELATION_ID]=corelatianIdMap[setting.pageViewId][CORRELATION_ID],n[PAGE_ID]=e[PAGE_ID],n[PAGE_URL]=e[PAGE_URL],{info:n}):e}function pageEventTransform(e){var t,n=e.t,r={},i=0;return n===PAGE_INVISIBLE&&isDefined(corelatianIdMap[setting.pageViewId])&&!corelatianIdMap[setting.pageViewId].isExit&&(e.p.info[DURATION]=corelatianIdMap[setting.pageViewId].loadtime/1e3,r=formatPageEventData(e.p,2),t=corelatianIdMap[setting.pageViewId].timestamp,i=r.info[TIMESTAMP]-t-corelatianIdMap[setting.pageViewId].loadtime,buildAndUpdateCI(2)),n!==PAGE_VISIBLE&&n!==PAGE_DATA||(buildAndUpdateCI(1),setShowStartTime((r=formatPageEventData(e.p,1)).info[TIMESTAMP],r.info[DURATION])),0===Object.keys(r).length?null:(t=new Metric(r,VIEW),0===r.info[DURATION]&&(r.info[DURATION]=999),0!==r.info[IS_MAIN_DOCUMENT]?(t.info(PAGE_URL,"","n").info(DURATION,999,"lt").info(MODEL$1,1,"m").info(IS_MAIN_DOCUMENT,1,"imd").info(CORRELATION_ID,"","ci").info(FRAME_TYPE,1,"t").info(IS_SLOW,!1,"is").info(IS_CUSTOM,!1,"ic"),0!==i&&t.info(STAY_TIME,i,"st"),t.build()):void 0)}var transformAction=function(e,t){return 1===t?userEventTransform(e):2===t?pageEventTransform(e):void 0};function jsErrorTransform(e){var t=new Metric(e.p,ERROR);return 1===t.data.info[TYPE$3]?null:(isDefined(window.BRLog)&&window.BRLog.__sendErrorInfo(t.data.info),t.info(PAGE_ID,"","pvid").info(PAGE_URL,"","url").info(ERROR_FILE,"","n").info(MESSAGE,"","m").info(ERROR_LINE,"","l").info(ERROR_COLUMN,"","col").info(PAGE_CREATE_TIME,1,"pct"),isDefined(e.p.info[PAGE_TITLE]&&""!==e.p.info[PAGE_TITLE])&&t.info(PAGE_TITLE,title(),"t"),isDefined(e.p.info[NAME$2]&&""!==e.p.info[NAME$2])&&t.info(NAME$2,"","et"),isDefined(e.p.info[ERROR_STACK])&&""!==e.p.info[ERROR_STACK]?t.info(ERROR_STACK,"","sta"):isDefined(t.data.info.file)&&(t.data.info[ERROR_STACK]="undefined at @"+t.data.info.file,t.info(ERROR_STACK,"at undefined @ "+t.data.info.file,"sta")),isDefined(setting.isSinglePage)?t.info("pt",2,"pt"):t.info("pt",1,"pt"),t.build())}var jsErrorAction=function(e){return jsErrorTransform(e)};function domTimeLineInfor(e){e=new Metric(e.p,PAGE);return e.metric(NAVIGATION_START,"","ns").metric(UNLOAD_EVENT_START,0,"ues").metric(FULL_RESOURCE_LOAD_TIME,0,"frlt").metric(UNLOAD_EVENT_END,0,"uee").metric(REDIRECT_START,0,"rds").metric(REDIRECT_END,0,"rde").metric(FETCH_START,0,"fs").metric(DOMAIN_LOOKUP_START,0,"dls").metric(DOMAIN_LOOKUP_END,0,"dle").metric(CONNECT_START,0,"cs").metric(SECURE_CONNECTION_START,0,"scs").metric(CONNECT_END,0,"ce").metric(REQUEST_START,0,"reqs").metric(RESPONSE_START,0,"rsps").metric(RESPONSE_END,0,"rspe").metric(DOM_LOADING,0,"dl").metric(DOM_INTERACTIVE,0,"di").metric(DOM_CONTENT_LOADED_EVENT_START,0,"dcles").metric(DOM_CONTENT_LOADED_EVENT_END,0,"dclee").metric(DOM_COMPLETE,0,"dc").metric(LOAD_EVENT_START,0,"les").metric(LOAD_EVENT_END,0,"lee").metric(FIRST_PAINT,0,"fp").metric(FIRST_CONTENTFUL_PAINT,0,"fcp").metric(LARGEST_CONTENTFUL_PAINT,0,"lcp").metric(TRANSFER_SIZE,0,"ts").metric(ENCODED_BODY_SIZE,0,"ebs").metric(DECODED_BODY_SIZE,0,"dbs").metric("tti",0,"tti").metric(RESPONSE_STATUS,-1,"rs"),e.build()}function WebviewPerformanceTimingTransformNet(e,t,n){var r,i,a;"about:blank"!==t&&(r=e.data,e=getIP(t),i=getMineTypeByUrl(t)||"text/html",(a={info:{id:uuid(),ru:t,m:"GET",ti:e[0]||"",tp:e[1]||"",dt:1<=r.dle-r.dls?1e3*(r.dle-r.dls):0,sslt:0<r.scs&&1<=r.ce-r.scs?1e3*(r.ce-r.scs):0,rt:0<r.reqs-r.ce&&1<=r.reqs-r.ce?1e3*(r.reqs-r.ce):999,rti:0<r.rsps-r.reqs&&1<=r.rsps-r.reqs?1e3*(r.rsps-r.reqs):r.rsps-r.reqs==0?0:999,dti:0===r.rsps?1e3*(r.rspe-r.fs):0<r.rspe-r.rsps&&1<=r.rspe-r.rsps?1e3*(r.rspe-r.rsps):999,ds:0,rds:0,pt:-1<t.indexOf("https")?2:1,ec:200,art:1,ret:i,pvid:n,ic:!1}}).info.ct=0<r.ce-r.cs?1e3*(r.ce-r.cs):0,a.info.ti.length<1&&delete a.info.ti,setting.osType===BROWSER?setTimeout(function(){return notifierEmit({t:REQUEST_DATA,p:a,type:"transform"})},0):setTimeout(function(){jsInternalBridge.MainDocumentNetworkEvent(stringify({ent:1e3*(r.ns+r.reqs),v:a.info}))}))}function WebviewPerformanceResourceTransformNet(e,t){var n,r,i,e=e.data,a=e.name,o=2;if(isDefined(e.it))switch(e.it){case"xmlhttprequest":o=3;break;case"beacon":o=5;break;case"fetch":o=4;break;default:o=2}return 3===o||4===o?null:(n=getIP(a),r=getMineTypeByUrl(a)||"text/html",(i={info:{timestamp:isDefined(e.st)&&isDefined(window.performance)&&isDefined(window.performance.timing)?1e3*(window.performance.timing.navigationStart+e.st):now(),id:uuid(),ru:a,m:"GET",ti:n[0]||"",tp:n[1]||"",dt:!(1<=e.dle-e.dls)||e.rsps<=0&&e.dura<=10?0:1e3*(e.dle-e.dls),sslt:0<e.scs&&1<=e.ce-e.scs?1e3*(e.ce-e.scs):0,rt:0<e.reqs-e.ce&&1<=e.reqs-e.ce?e.rsps<=0&&e.dura<=10?0:1e3*(e.reqs-e.ce):999,rti:e.rsps-e.reqs==0?0:0<e.rsps-e.reqs&&e.rsps-e.reqs<1?999:1e3*(e.rsps-e.reqs),dti:0===e.rsps?1e3*(e.rspe-e.fs):0<e.rspe-e.rsps&&e.rspe-e.rsps<1?e.rsps<=0&&e.dura<=10?0:999:e.rsps<=0&&e.dura<=10?0:1e3*(e.rspe-e.rsps),ds:0,rds:0,pt:-1<a.indexOf("https")?2:1,ec:isDefined(t)?t:200,art:o,ret:r,ic:!1,pvid:isDefined(setting.pageViewId)?setting.pageViewId:""}}).info.ct=0<e.ce-e.cs?1e3*(e.ce-e.cs):0,setting.osType===BROWSER&&setTimeout(function(){return notifierEmit({t:REQUEST_DATA,p:i,type:"transform"})},0),i)}function resourceDataInfo(e){var e=e.p||[],r=[],i=[];return forEach(e,function(e){var t,n=new Metric(e,RESOURCE),n=(n.info(START_TIME,0,"st").info(NAME$2,"","name").info(DURATION,0,"dura").info("ret","","rt").metric(FETCH_START,0,"fs").metric(DOMAIN_LOOKUP_START,0,"dls").metric(DOMAIN_LOOKUP_END,0,"dle").metric(CONNECT_START,0,"cs").metric(CONNECT_END,0,"ce").metric(SECURE_CONNECTION_START,0,"scs").metric(REQUEST_START,0,"reqs").metric(RESPONSE_START,0,"rsps").metric(RESPONSE_END,0,"rspe").metric(TRANSFER_SIZE,0,"ts").metric(ENCODED_BODY_SIZE,0,"ebs").metric(DECODED_BODY_SIZE,0,"dbs").metric(RESPONSE_STATUS,-1,"rs"),isDefined(e.info)&&isDefined(e.info[INITIATOR_TYPE])&&n.info(INITIATOR_TYPE,"","it"),n.build());1===setting.osType||4===setting.osType?(t=WebviewPerformanceResourceTransformNet(n,e.metric[RESPONSE_STATUS]),isDefined(t)&&i.push({v:t.info,ent:isDefined(window.performance)?1e3*(performance.timing.navigationStart+e.metric[REQUEST_START]):now(),k:"network"})):(t=WebviewPerformanceResourceTransformNet(n,e.metric[RESPONSE_STATUS]),isDefined(t)&&i.push({v:t.info,ent:isDefined(window.performance)?1e3*(performance.timing.navigationStart+e.metric[REQUEST_START]):now()})),r.push(n)}),setting.osType!==BROWSER&&0!==i.length&&setTimeout(function(){jsInternalBridge.ResourceNetworkEvent(stringify({network:i,type:"resource"}))},0),r}function umustFiledHandle(e){var t=e.data;return isDefined(t.al)&&""===t.al&&delete t.al,e}function spaPageRouterData(e){var t=new Metric(e.p,ROUTE),n=getClientType();return t.info(URL$1,"","tu").info(REFERRER,"","fu").info(DURATION,0,"d").info(STATUS,0,"sta").info(ALIAS,"","al").info(PATH,"","pt").info(ROOT,"","rt").info(FULL_URL,"","pu").info(FRAME_WORK,"","fw").info(CUSTOM_IC,!1,"ic").info(CLIENT_TYPE,n,"ctp").info("pvid","","pvid").info("ns",0,"ns"),isDefined(e.p.info.wri)&&t.info("wri",[],"wri"),umustFiledHandle(t.build())}function transformTraceAction(e){var t={},n=new Metric(e,ACTION);return n.info("t").info("n").info("sa").info("i").info("vn").info("ic").info("p","","p",!0).info("lt").info("is").info("id").info("m").info("ice").info("ci").info("vt",1,"vt"),isDefined(e.info.m)&&2===e.info.m&&(e.info.me&&e.info.me.ms&&(e.info.me.ms=delEmptData(e.info.me.ms)),e.info.me&&e.info.me.ms&&0===e.info.me.ms.length&&delete e.info.me.ms,e.info.me&&e.info.me.ne&&0===Object.getOwnPropertyNames(e.info.me.ne).length&&delete e.info.me.ne,n.info("me")),e.info&&e.info.startTime&&(t.ent=e.info.startTime),n.build(t)}function delEmptData(e){return e&&e.filter?e&&e.filter(function(e){return e.ne}):e}function consoleData(e){e=new Metric(e.p,CONSOLE$1);return e.info("lv").info("msg","","msg",!0),e.build()}function spanData(e){var t=new Metric(e.p,SPAN);return t.info("st").info("n","","n",!0).info("t","","t").info("du",0,"du").info("sta",0,"sta").info("ic",!1,"ic"),isDefined(e.p.info)&&isDefined(e.p.info.da)&&t.info("da",[],"da"),isDefined(e.p.info)&&isDefined(e.p.info.m)&&t.info("m",[],"m"),isDefined(e.p.info)&&isDefined(e.p.info.tag)&&t.info("tag",[],"tag"),isDefined(e.p.info)&&isDefined(e.p.info.stac)&&t.info("stac","","stac"),isDefined(e.p.info)&&isDefined(e.p.info.sub)&&t.info("sub",{},"sub"),t.build()}var ManageAction=_createClass(function e(){_classCallCheck(this,e),_defineProperty(this,"manageActionAdd",function(e){var t;return isArray(e)?(t=this.actionArr,this.actionArr=t.concat(e)):this.actionArr.push(e),this}),_defineProperty(this,"manageActionUpload",function(t){var n,e=this.actionArr;return this.actionArr=e.filter(function(e){return e.id===t&&(n=e),e.id!==t}),n}),_defineProperty(this,"manageActionModify",function(e,t,n){if(isDefined(e)&&isDefined(t)&&isDefined(n))for(var r=0,i=this.actionArr.length;i<i;r++)if(this.actionArr[r].id===e){this.actionArr[r]=extend(this.actionArr[r],{tag:n});break}}),_defineProperty(this,"mangaeActionGet",function(t){var n;return isDefined(t)?(this.actionArr.forEach(function(e){e.id===t&&(n=e)}),n):this.actionArr}),_defineProperty(this,"manageActionGetLast",function(){return this.actionArr[this.actionArr.length-1]}),_defineProperty(this,"timerCharge",function(i,a){var o,s;isDefined(i)&&(o=setting.ac.aot&&setting.ac.aot||3e4,s=this,setTimeout(function(){var e=s.manageActionUpload(i);if(isDefined(e)){e=e.rootActionBuild();if(e.timestamp=a,e.me&&e.me.ms){var t=e.me.ms=e.me.ms.filter(function(e){if(isDefined(e))return e});if(0<t.length)for(var n=0,r=t.length;n<r;n++)isDefined(t[n])&&isDefined(t[n].et)&&t[n].et-a>1e3*o&&(t[n].to=!0)}!0===setting.ac.ac&&isDefined(setting.ac.cp)&&checkUpProbability(setting.ac.cp)&&notifierEmit({t:TRACE_ACTION_DATA,p:{info:e}})}},o))}),this.actionArr=[]}),RootAction=_createClass(function e(){_classCallCheck(this,e),_defineProperty(this,"rootActionModify",function(e,t){return isDefined(e)&&isDefined(t)&&("ms"===e?this.me.ms=t:"haveNet"===e?this.haveNet.push(t):"haveNetFalse"===e?this.haveNet.map(function(e){e.pid===t.pid&&(e.haveNet=!1)}):"ice"===e?(this.me.ice=t,this.ice=t):"et"===e?this.me.et=t:this[e]=t),this}),_defineProperty(this,"rootActionNetState",function(e){}),_defineProperty(this,"rootActionGet",function(e){return this[e]}),_defineProperty(this,"rootActionDel",function(e){delete this[e]}),_defineProperty(this,"rootActionArrfunAdd",function(n){200<this.funArr.length||49<this.funArr.reduce(function(e,t){return t.pid===n.pid?e+1:e},0)||isDefined(n)&&this.funArr.push(n)}),_defineProperty(this,"buildTree",function(e,r){var i=e.reduce(function(e,t,n){return e[t.id]=n,e},{}),a=[],o=e.map(function(e){return e.funActionBuild()});return o.forEach(function(e,t){var n=o[i[e.pid]];e.pid===r?(delete(a[t]=e).id,delete e.pid):isDefined(n)&&(delete n.id,delete n.pid,delete e.id,delete e.pid,e&&e.ms&&0==e.ms.length&&delete e.ms,e&&e.ne&&0===Object.getOwnPropertyNames(e.ne).length&&delete e.ne,n&&n.ne&&0===Object.getOwnPropertyNames(n.ne).length&&delete n.ne,n)&&isObj(n.ms)&&(n.ms=[].concat(_toConsumableArray(n.ms||[]),[e]))}),function e(t,n){try{if(0===n&&t&&t[0]&&t[0].ms)return[];for(var r,i,a=t.length-1;0<=a;a--)t[a]&&t[a].ne&&0===Object.getOwnPropertyNames(t[a].ne).length&&delete t[a].ne,t[a]&&t[a].ms&&0<t[a].ms.length&&0<n?(r=n-1,i=t[a].ms&&t[a].ms||void 0,isDefined(i)&&(t[a].ms=e(i,r))):delete t[a].ms}catch(e){}return t}(a,setting.ac.mmd-1)}),_defineProperty(this,"rootActionBuild",function(e){if(0<this.haveNet.length&&0!==this.funArr.length)for(var t,n=0,r=this.funArr.length;n<r;n++)isDefined(this.funArr[n].neOrigin)&&(t=requestAction(this.funArr[n].neOrigin),isDefined(t)&&isDefined(t.data)&&(t.data.ai=this.id),isDefined(t))&&isDefined(t.data)&&this.funArr[n].funActionModify("ne",t.data);0===this.haveNet.length&&delete this.me.ne;var i=this.buildTree(this.funArr,this.id);return this.rootActionModify("ms",i),delete this.me.id,delete this.me.pid,!0===e?{id:this.id,m:1,ice:!1,startTime:this.startTime,t:this.t,sa:this.sa,i:this.i,ci:this.ci,vn:this.vn,ic:this.ic,p:this.p,lt:this.lt,is:this.is,me:this.me,n:this.n}:{id:this.id,m:e?1:2,t:this.t,sa:this.sa,i:this.i,ci:this.ci,vn:this.vn,ic:this.ic,ice:!e&&this.ice,p:this.p,lt:this.lt,is:this.is,me:this.me,n:this.n,ti:this.ti,im:this.im,startTime:this.startTime}}),this.id=uuid(),this.m=1,this.t=1,this.sa=1,this.i="",this.ci=null,this.vn="",this.n="",this.ic=!1,this.ice=!1,this.p="",this.lt=0,this.is=!1,this.me={},this.funArr=[],this.haveNet=[],this.startTime=0}),FunAction=_createClass(function e(t,n){_classCallCheck(this,e),_defineProperty(this,"funActionModify",function(e,t){return isDefined(e)&&isDefined(t)&&(this[e]=t),this}),_defineProperty(this,"funActionGet",function(e){if(isDefined(e))return this[e]}),_defineProperty(this,"funActionBuild",function(){return{n:this.n,t:this.t,to:this.to,ice:this.ice,st:this.st,et:this.et||now(),ms:this.ms,ne:this.ne,id:this.id,pid:this.pid,ti:this.ti,im:this.im}}),this.n=t&&"string"==typeof t?t:t.name,this.t=12,this.to=!1,this.ice=!1,this.st=1,this.et=0,this.ms=[],this.ne={},this.neOrigin=null,this.id=uuid(),this.pid=n,this.ti=1225,this.im=!0}),globalAllTrace=new ManageAction;function checkUpProbability(e){return!(e<Math.floor(100*Math.random()))}function haveNetcheck(e){for(var t=!1,n=0,r=e.length-1;n<=r;n++)if(e[n].haveNet){t=!0;break}return t}function safetySetMetric(e,t,n){e.$metric&&(e=e.$metric.metric,isDefined(e[t])&&!n||(e[t]=tillNow()))}function safetySetInfo(e,t,n){e.$metric&&(e.$metric.info[t]=n)}function traceActionModule(e){var t,n,r,i=globalAllTrace.manageActionGetLast();isDefined(i)&&(t=i.id,(n=new FunAction("xhr",t)).funActionModify("t",1),n.funActionModify("st",now()),r=n.funActionGet("id"),e.$pid=r,e.$trid=t,isDefined(e.$metric)&&isDefined(e.$metric.info)&&(e.$metric.info.ai=t),i.rootActionModify("haveNet",{pid:r,haveNet:!0}),i.rootActionArrfunAdd(n))}function traceActionModuleOpenEndTime(){if(isDefined(this)&&this.$pid&&this.$trid){var e=this.$pid,t=this.$trid,n=globalAllTrace.mangaeActionGet(t);if(isDefined(n)){for(var r=n.funArr,i=0,a=r.length;i<a;i++)if(r[i].id===e){r[i].funActionModify("et",now());break}return t}}}function traceActionModuleEnd(e,t,n){if(isDefined(e)){var r=e.$pid,i=e.$trid;if(isDefined(r)&&isDefined(i)){traceActionModuleOpenEndTime.call(e);e=globalAllTrace.mangaeActionGet(i);if(isDefined(e)){e.rootActionModify("haveNetFalse",{pid:r,haveNet:!1});for(var a=e.funArr,o=0,s=a.length;o<s;o++)if(a[o].id===r){a[o].funActionModify("neOrigin",{p:t});break}e.rootActionGet("haveNet")}}}}function safetySetErrorInfo$1(e,t,n,r,i){e.$metric&&((e=e.$metric.info)[t]=n,e[MESSAGE]=i,e[E_TYPE]=r)}function beforeXHROpen(e,t){var n=getIP(t=getUrl$1(t));this.$$inner||(this.$metric={info:{id:uuid(),url:t,ip:n[0]||"",port:n[1]||"",method:toUpper(e),type:0,protocalType:-1<t.indexOf("https")?2:1,reqUrlDta:isDefined(setting.reqURLKey)&&getParams$1(t)||"",reqhtData:{},reshtData:{}}},isDefined(setting)&&isDefined(setting.pageViewId)&&(this.$metric.info.pageViewId=setting.pageViewId),traceActionState&&traceActionModule(this))}function getParams$1(e){return isDefined(e)?String.prototype.split.call(e,"?")[1]:""}function XHREnd(e){if(this.$metric&&!this.$$done){this.$$done=!0;var t,n=this.$metric,r=n.info,i=n.metric;extendMetrics(n),polyfillMetric(n);try{if((!isDefined(i[DECODED_BODY_SIZE])||i[DECODED_BODY_SIZE]<=0)&&(t=""===this.responseType||"text"===this.responseType?getContentSize(this.responseText):getContentSize(this.response),i[DECODED_BODY_SIZE]=t),this.getAllResponseHeaders){var a=this.getAllResponseHeaders(),o=a,s=toLower(o),c=o.trim().split(/[\r\n]+/),l={};if(c.forEach(function(e){var e=e.split(": "),t=e.shift(),e=e.join(": ");l[t]=e}),r.guid=-1<s.indexOf(GUID_KEY)?l[GUID_KEY].split(",")[0]:"",r.xBrResponse=-1<s.indexOf(X_BR_RESPONSE)?l[X_BR_RESPONSE].split(",")[0]:"",r.traceResponse=-1<s.indexOf(TRACE_RESPONSE)?l[TRACE_RESPONSE].split(",")[0]:"",r[RESPONSE_HEADER]=withLength(o,2e3),isDefined(setting.respHeaderTraceKey)&&0<setting.respHeaderTraceKey.length)for(var d=0,u=setting.respHeaderTraceKey.length-1;d<=u;d++)-1<s.indexOf(toLower(setting.respHeaderTraceKey[d]))&&(r.reshtData[setting.respHeaderTraceKey[d]]=this.getResponseHeader(setting.respHeaderTraceKey[d]));getResponseHead$1(a)}}catch(e){log("xhrEnd status error")}notifierEmit({t:REQUEST_DATA,p:n}),traceActionModuleEnd(this,n),setTimeout(function(){getCookieUserID()},50)}}function getResponseHead$1(e){if(0<(e=e.trim()).length){var e=e.split(/[\r\n]+/),n={},t=(e.forEach(function(e){var e=e.split(": "),t=e.shift(),e=e.join(": ");n[t]=e}),getUserIdManageClass.responseArr),r=getUserIdManageClass.configeManage;if(0<t.length)for(var i=0,a=t.length-1;i<=a;i++){var o=t[i].rule.toLowerCase();if(n.hasOwnProperty(o)&&t[i].index<=getUserIdManageClass.isStopGetValue&&checkValue(n[o]))return r[t[i].index].value=""+n[o],void getUserIdManageClass.checkValueRight()}}}function isHacked(e){return isFunction(prop(e.onreadystatechange,"$$original"))}function isXHRDone(e){return 0===e.readyState||4===e.readyState}function readystatechange(){beforestatechange.call(this),isHacked(this)||(isXHRDone(this)?afterstatechange.call(this):this.onreadystatechange=hack(this.onreadystatechange,beforestatechange,afterstatechange,callbackError))}function beforestatechange(){2===this.readyState&&safetySetMetric(this,RESPONSE_START),isXHRDone(this)&&(safetySetMetric(this,RESPONSE_START),safetySetMetric(this,RESPONSE_END),safetySetMetric(this,CALLBACK_START))}function afterstatechange(){if(isXHRDone(this)){var e=now(),t=(safetySetMetric(this,CALLBACK_END),this);if(this.$metric){var n=0;try{n=this.status,this.$metric.info[E_TYPE]="http"}catch(e){log("readyState endStatus")}0===n&&(this.$metric.info[E_TYPE]="XHR",this.$metric.info[MESSAGE]="XHR internal services not available or Cross domain request"),this.$metric.info.code=n,queueRequest(this.$metric),delay(function(){log("XHR done"),XHREnd.call(t,e)})}}}function callbackError(){log("XHR callback error");var e=last(args(arguments));error(e),safetySetInfo(this,"callbackError",1)}function getReqhead(e,t,n){isDefined(setting.reqHeaderTraceKey)&&includes$1(setting.reqHeaderTraceKey.map(function(e){return e.toLowerCase()}),e.toLowerCase())&&(n.info.reqhtData[e]=t)}var xhrRecord=[];function initXhrRecord(){xhrRecord=[]}function beforeXHRSend(e){if(xhrRecord.push(now()),!this.$$inner&&isDefined(this.$metric)){var t,r=this,n=(isDefined(this.$metric.info)&&BWexample.isOpen&&(n=checkBlackAndWhite(this.$metric.info,"_xhr"),isDefined(n))&&isObj(n)&&forEachOwn(n,function(e,t){var n;isObj(e)?(n=e.sky?e.fun(e.url,e.pathname):e.fun(e.len),r.setRequestHeader(t,n),getReqhead(t,n,r.$metric)):(startWith(e,"bnro=")&&(e+="_XMLHttpRequest"),r.setRequestHeader(t,e),getReqhead(t,e,r.$metric))}),isFunction(this.addEventListener)?(forEach(["abort","timeout","error"],function(e,t){var n=["User terminates XHR request","XHR request timeout","XHR request exception"];addListener(r,e,function(){log(n[t]),safetySetErrorInfo$1(r,"code",600+Number(t),"XHR",n[t])},!1)}),addListener(this,"readystatechange",readystatechange,!1)):(t=3,function e(){delay(function(){if(isXHRDone(r)&&!isHacked(r))return afterstatechange.call(r);isXHRDone(r)||(r.onreadystatechange=hack(r.onreadystatechange,beforestatechange,afterstatechange,callbackError),0<=--t&&e())})}()),this.onreadystatechange=hack(this.onreadystatechange,beforestatechange,afterstatechange,callbackError),this.$metric.info),i=(extend(n,getTime()),notifierEmit({t:REQUEST_INIT,p:this.$metric}),extend(n,defaultInfo()),this.$metric.metric={});try{n[REQUEST_BODY]=getRequestParam(e),i[UPLOAD_BODY_SIZE]=getContentSize(e)}catch(e){log("beforeSend error")}}}function getSetHeader$1(e,t){if(checkValue(t)){var n=getUserIdManageClass.requestArr,r=getUserIdManageClass.configeManage;if(0<n.length)for(var i=0,a=n.length-1;i<=a;i++)if(n[i].rule==e&&n[i].index<=getUserIdManageClass.isStopGetValue)return r[n[i].index].value=""+t,void getUserIdManageClass.checkValueRight()}}function beforeXHRHeader(e,t){var n;getSetHeader$1(e,t),isDefined(this.$metric)&&(getReqhead(e,t,this.$metric),n=this.$metric.info,isDefined(n[REQUEST_HEADER])||(n[REQUEST_HEADER]=""),n[REQUEST_HEADER]=n[REQUEST_HEADER]&&n[REQUEST_HEADER]+BREAK_LINE+e+":"+t||e+":"+t)}function initXHR(){if(!window.XMLHttpRequest)return log("XMLHttpRequest is not supported");var t;XMLHttpRequest.prototype?(XMLHttpRequest.prototype.open=hack(XMLHttpRequest.prototype.open,beforeXHROpen),XMLHttpRequest.prototype.send=hack(XMLHttpRequest.prototype.send,beforeXHRSend),XMLHttpRequest.prototype.setRequestHeader=hack(XMLHttpRequest.prototype.setRequestHeader,beforeXHRHeader)):(log("Xmlhttprequest.prototype is not supported"),t=window.XMLHttpRequest,window.XMLHttpRequest=function(){var e=new t;return e.open=hack(e.open,beforeXHROpen),e.send=hack(e.send,beforeXHRSend),e.setRequestHeader=hack(e.setRequestHeader,beforeXHRHeader),e})}function getBody(e){return getRequestParam(e)}function getBodySize(e){return getContentSize(e)}function getHeaders(e){if(!isDefined(e))return"";var t="";try{var n,t=(window.Headers&&e instanceof Headers?map(iterate(e.entries()),function(e){return e.join(":")}):(n=[],forEachOwn(e,function(e,t){n.push(t+":"+e)}),n)).join(BREAK_LINE)}catch(e){t="",log("serialize Headers")}return t}function isRquest(e){return isDefined(e)&&"string"!=typeof e}function getChild(e,t,n){return"headers"===n&&t?isRquest(e)&&e[n]?extend(t[n],e[n]):t[n]:isRquest(e)?e[n]:t?t[n]:null}function parseRequestAndHeaders(e,t){return{requestBody:getBody(getChild(e,t,"body")),requestHeader:getHeaders(getChild(e,t,"headers"))}}function parsePathAndMethod(e,t){var n=getUrl$1(n=isRquest(e)?e.url:e),e=toUpper(getChild(e,t,"method"))||"GET",t=getIP(n);return{id:uuid(),url:n,method:e,ip:t[0]||"",port:t[1]||"",protocalType:-1<n.indexOf("https")?2:1,reqUrlDta:isDefined(setting.reqURLKey)&&getParams(e,n)||"",requestType:4}}function getParams(e,t){return isDefined(e)&&isDefined(t)?String.prototype.split.call(t,"?")[1]:""}function traceFetchStart(){var e,t,n,r=globalAllTrace.manageActionGetLast(),i={};return isDefined(r)&&(e=r.id,(t=new FunAction("fetch",e)).funActionModify("t",1),t.funActionModify("st",now()),n=t.funActionGet("id"),i.$pid=n,i.$trid=e,r.rootActionModify("haveNet",{pid:n,haveNet:!0}),r.rootActionArrfunAdd(t)),i}function traceFetchEnd(e,t,n){if(isDefined(e)){var r=e.$pid,e=e.$trid;if(isDefined(r)&&isDefined(e)){e=globalAllTrace.mangaeActionGet(e);if(isDefined(e)){e.rootActionModify("haveNetFalse",{pid:r,haveNet:!1});for(var i=e.funArr,a=0,o=i.length;a<o;a++)if(i[a].id===r){i[a].funActionModify("neOrigin",{p:t}),i[a].funActionModify("et",now());break}e.rootActionGet("haveNet")}}}}function requestEnd(e,t){var n=now();if(e){log("Fetch data completed"),extendMetrics(e),polyfillMetric(e);var r,i=e.metric;(!isDefined(i[DECODED_BODY_SIZE])||i[DECODED_BODY_SIZE]<=0)&&(r=e.ext||{},i[DECODED_BODY_SIZE]=r.originalResponseSize||0),notifierEmit({t:REQUEST_DATA,p:e});try{traceFetchEnd(t,e,n)}catch(e){}setTimeout(function(){getCookieUserID()},50)}}var fetchRecord=[];function initFetchRecord(){fetchRecord=[]}function hackFetch(){log("hack Fetch");var n=window.fetch;window.fetch=function(e,r){var t=isDefined(window.$bonreeReplayUrl)?window.$bonreeReplayUrl:null,l=((!isRquest(e)&&-1===e.indexOf(t)||isRquest(e))&&fetchRecord.push(now()),parsePathAndMethod(e,r)),l=extend(l,{reqhtData:{},reshtData:{}}),t=(isDefined(setting)&&isDefined(setting.pageViewId)&&(l.pageViewId=setting.pageViewId),BWexample.isOpen&&(extend(l,{reqhtData:{},reshtData:{}}),t=checkBlackAndWhite(l,"_fetch"),isDefined(t))&&isObj(t)&&forEachOwn(t,function(e,t){var n=e;isObj(e)&&(n=e.sky?e.fun(e.url,e.pathname):e.fun(e.len)),startWith(e,"bnro=")&&(n+="_fetch"),isDefined(r)?isDefined(r.headers)?extend(r.headers,_defineProperty({},t,n)):(e=_defineProperty({},t,n),extend(r,{headers:e})):r={headers:_defineProperty({},t,n)}}),r&&r.headers&&getSetHeader(r.headers,l),l.type=1,extend(l,getTime()),n(e,r)),d=(notifierEmit({t:REQUEST_INIT,p:{info:l}}),extend(l,defaultInfo()),extend(l,parseRequestAndHeaders(e,r)),{info:l}),u=d.metric={},f=(u[UPLOAD_BODY_SIZE]=getBodySize(getChild(e,r,"body")),traceFetchStart());return t.then(function(t){log("Fetch is done"),l.status=t.status,l.statusText=t.statusText;var e=u[RESPONSE_END]=tillNow(),e=(u[RESPONSE_START]=u[CALLBACK_START]=e,queueRequest(d),getHeaders(t.headers)),n=e,r=toLower(n),i=n.trim().split(/[\r\n]+/),a={};i.forEach(function(e){var e=e.split(":"),t=e.shift(),e=e.join(": ");a[t]=e}),l.guid=-1<r.indexOf(GUID_KEY)?a[GUID_KEY].split(",")[0]:"",l.xBrResponse=-1<r.indexOf(X_BR_RESPONSE)?a[X_BR_RESPONSE].split(",")[0]:"",l.traceResponse=-1<r.indexOf(TRACE_RESPONSE)?a[TRACE_RESPONSE].split(",")[0]:"",l[RESPONSE_HEADER]=withLength(n,2e3);try{if(isDefined(setting.respHeaderTraceKey)&&0<setting.respHeaderTraceKey.length)for(var o=0,s=setting.respHeaderTraceKey.length-1;o<=s;o++)-1<r.indexOf(toLower(setting.respHeaderTraceKey[o]))&&(l.reshtData[setting.respHeaderTraceKey[o]]=t.headers.get(setting.respHeaderTraceKey[o]))}catch(e){log("Dirty data for reqht")}getResponseHead(e),t.$metric=d,f.$pid&&f.$trid&&(t.$pid=f.$pid,t.$trid=f.$trid,t.$metric)&&t.$metric.info&&(t.$metric.info.ai=f.$trid),u[CALLBACK_END]=tillNow();i=tryToClone(t);if(i)try{var c={bytesLimit:Number.POSITIVE_INFINITY,collectStreamBody:!0};readBytesFromStream(i.body,c,function(e){d.ext={originalResponseSize:e},requestEnd(d,t)})}catch(e){}else requestEnd(d,t);return t},function(e){log("Fetch error");var t=u[RESPONSE_END]=tillNow();u[RESPONSE_START]=u[CALLBACK_START]=u[CALLBACK_END]=t,l.status=window.TypeError&&e instanceof TypeError?602:600,l.eType="FETCH",l[STATUS_TEXT]=602==l.status?"FETCH request exception":"User terminates FETCH request",f.$pid&&f.$trid&&(e.$pid=f.$pid,e.$trid=f.$trid,d.info.ai=f.$trid),requestEnd(d,e)}).catch(function(r){forEach(["AbortError","timeout","error"],function(e,t){var n=["User terminates FETCH request","FETCH request timeout","FETCH request exception"];e===r.name&&(log(n[t]),safetySetErrorInfo(l,"status",600+Number(t),"FETCH",n[t]))})})}}function getSetHeader(t,n){if(isDefined(t)){var e=getUserIdManageClass.requestArr,r=getUserIdManageClass.configeManage;if(0<e.length)for(var i=0,a=e.length-1;i<=a;i++)if(t.hasOwnProperty(e[i].rule)&&e[i].index<=getUserIdManageClass.isStopGetValue&&checkValue(t[e[i].rule]))return r[e[i].index].value=""+t[e[i].rule],void getUserIdManageClass.checkValueRight();isDefined(setting.reqHeaderTraceKey)&&0<setting.reqHeaderTraceKey.length&&Object.keys(t).map(function(e){includes$1(setting.reqHeaderTraceKey.map(function(e){return e.toLowerCase()}),e.toLowerCase())&&(n.reqhtData[e]=t[e])})}}function getResponseHead(e){if(0<(e=e.trim()).length){var e=e.split(/[\r\n]+/),n={},t=(e.forEach(function(e){var e=e.split(": "),t=e.shift(),e=e.join(": ");n[t]=e}),getUserIdManageClass.responseArr),r=getUserIdManageClass.configeManage;if(0<t.length)for(var i=0,a=t.length-1;i<=a;i++){var o=t[i].rule.toLowerCase();if(n.hasOwnProperty(o)&&t[i].index<=getUserIdManageClass.isStopGetValue&&checkValue(n[o]))return r[t[i].index].value=""+n[o],void getUserIdManageClass.checkValueRight()}}}function safetySetErrorInfo(e,t,n,r,i){e[t]=n,e[STATUS_TEXT]=i,e[E_TYPE]=r}function initFetch(){if(!isFunction(window.fetch))return log("Fetch is not supported");hackFetch()}function initRequestCollector(){initXHR(),initFetch(),window.performance&&window.performance.now&&initDeamon()}function getZoneJsOriginalValue(e,t){var n=null;return n=(n=e.Zone&&"function"==typeof e.Zone.__symbol__?e[e.Zone.__symbol__(t)]:n)||target[t]}function getMutationObserver(){var e,t=null;return window.Zone&&(t=getZoneJsOriginalValue(window,"MutationObserver")),t=(t=window.MutationObserver&&t===window.MutationObserver?(e=getZoneJsOriginalValue(new browserWindow.MutationObserver(function(){}),"originalInstance"))&&e.constructor:t)||window.MutationObserver}var PageActivityManager=function(){function t(n){var r=this,e=(_classCallCheck(this,t),this.mutationRecord=[],this.performanceRecord=[],this.record=!1,this.unlocked=!0,window.PerformanceObserver&&(this.PerformanceObserver=new PerformanceObserver(function(e){var e=e.getEntries(),t=isDefined(window.$bonreeReplayUrl)?window.$bonreeReplayUrl:null;e.some(function(e){return"resource"===e.entryType})&&r.record&&(r.performanceRecord.push(now()),isDefined(n))&&n(e.filter(function(e){return"resource"===e.entryType&&!isExcludedUrl([setting.uploadAddrHttp,setting.uploadAddrHttps,t],e.name.split("?")[0])}))})),getMutationObserver());isDefined(e)&&(this.mutationObserver=new e(function(e){r.record&&r.mutationRecord.push(now())}))}return _createClass(t,[{key:"startPageActivityObserver",value:function(){window.PerformanceObserver&&this.PerformanceObserver.observe({entryTypes:["resource"]}),this.mutationObserver.observe(window.document,{attributes:!0,attributeOldValue:!0,characterData:!0,characterDataOldValue:!0,childList:!0,subtree:!0})}},{key:"turnOnRecord",value:function(){this.record=!0}},{key:"turnOffRecord",value:function(){this.record=!1,this.mutationRecord=[],this.performanceRecord=[]}},{key:"stopPageActivityObserver",value:function(){window.PerformanceObserver&&this.PerformanceObserver.disconnect(),isDefined(this.mutationObserver)&&this.mutationObserver.disconnect()}}]),t}();function isExcludedUrl(e,t){return-1<e.indexOf(t)}function checkRecord(e,t){if(0!==e.length&&!(e[e.length-1]<t))for(var n=0;n<=e.length;n++)if(0<=(e[n]-t)/1e3)return 1}var pageActivityManager=new PageActivityManager,notes=["HTML","BODY","A"];function getXpath$1(e){var t;return isDefined(e)?(e.$xpath||(200<(t=xpath(e)).length?e.$xpath="[Error: Overflow with more than 200 characters.]":e.$xpath=t),e.$xpath):""}function isString$1(e){return"string"==typeof e?e:null}var debouncer=new Debouncer(function(e){notifierEmit({t:ACTION_DATA,p:e})},setting.debounce);function getDefaultInfo(e){return extend(getTime(),{count:1,type:e,namespace:0,duration:0})}function actionHookBefore$1(e){try{var t,n,r,i,a,o,s,c,l,d,u,f=e.target;"BODY"!==(isString$1(f.tagName)||f.name||"")&&(t="xpath"+unicode$1("=")+"".concat(getXpath$1(f))+",outerHTML"+unicode$1("=")+"".concat(f.outerHTML),n=e&&"click"===e.type?1:4,r=e&&e.target&&e.target.tagName||"",i={t:getActionText(f)||f.innerText||"",c:f.className||"",tag:f.nodeName||f.localName||""},a=setting.pageUrl,o=new RootAction,s=isDefined(e.startTime)?e.startTime:now(),c=o.rootActionGet("id"),(l=new FunAction("anonymous",c)).funActionModify("st",s),d=l.funActionBuild(),o.rootActionModify("m",1).rootActionModify("t",n).rootActionModify("n",r).rootActionModify("i",t).rootActionModify("ci",i).rootActionModify("vn",a).rootActionModify("startTime",s).rootActionModify("me",d),globalAllTrace.manageActionAdd(o),(u={t:n,n:r,sa:1,i:t,vn:a,ic:!1,lt:0,is:!1,id:c,m:1,ice:!1,ci:i}).timestamp=s,e.$startTimer=setTimeout(function(){isDefined(pageActivityManager)&&(checkRecord(pageActivityManager.mutationRecord,isDefined(e.startTime)?e.startTime:now())||checkRecord(pageActivityManager.performanceRecord,isDefined(e.startTime)?e.startTime:now())||checkRecord(xhrRecord,isDefined(e.startTime)?e.startTime:now())||checkRecord(fetchRecord,isDefined(e.startTime)?e.startTime:now())?notifierEmit({t:TRACE_ACTION_DATA,p:{info:u}}):(globalAllTrace.actionArr=[],pageActivityManager.turnOffRecord(),initFetchRecord(),initXhrRecord()))},100),e.$startEvent=u)}catch(e){}}function actionHookAfter$1(e){var t,n,r,i,a=globalAllTrace.mangaeActionGet(),a=a[a.length-1];isDefined(a)&&(t=a.rootActionGet("startTime"),n=a.rootActionGet("haveNet")||[],r=a.rootActionGet("id"),i=e+t,a.rootActionModify("lt",e),a.rootActionModify("et",i),haveNetcheck(n)?globalAllTrace.timerCharge(r,t):((e=globalAllTrace.manageActionUpload(r).rootActionBuild()).timestamp=t,!0===setting.ac.ac&&isDefined(setting.ac.cp)&&checkUpProbability(setting.ac.cp)&&notifierEmit({t:TRACE_ACTION_DATA,p:{info:e}})))}debouncer.equalsWith(function(e,t){return e.info.namespace===t.info.namespace&&e.info.type===t.info.type}),on$1(FLUSH_DATA,function(){debouncer.flush()});var onClick=function(e){if(e&&e.target&&!isDefined(e.$hasHandle)){if(isDefined(e.$startTimer&&e.$startEvent)&&(clearTimeout(e.$startTimer),e.$startEvent.lt=now()-e.$startEvent.timestamp,setTimeout(function(){isDefined(pageActivityManager)&&(checkRecord(pageActivityManager.mutationRecord,isDefined(e.startTime)?e.startTime:now())||checkRecord(pageActivityManager.performanceRecord,isDefined(e.startTime)?e.startTime:now())||checkRecord(xhrRecord,isDefined(e.startTime)?e.startTime:now())||checkRecord(fetchRecord,isDefined(e.startTime)?e.startTime:now())?notifierEmit({t:TRACE_ACTION_DATA,p:{info:e.$startEvent}}):(globalAllTrace.actionArr=[],pageActivityManager.turnOffRecord(),initFetchRecord(),initXhrRecord()))},100)),isDefined(e.startTime)||(e.startTime=now()),!0===setting.ac.ac&&0<setting.ac.cp&&!includes$1(notes,e.target.nodeName))try{return void(isDefined(e.$bonree)&&e.$bonree?(e.$hasHandle=!0,setTimeout(function(){isDefined(pageActivityManager)&&(checkRecord(pageActivityManager.mutationRecord,isDefined(e.startTime)?e.startTime:now())||checkRecord(pageActivityManager.performanceRecord,isDefined(e.startTime)?e.startTime:now())||checkRecord(xhrRecord,isDefined(e.startTime)?e.startTime:now())||checkRecord(fetchRecord,isDefined(e.startTime)?e.startTime:now())?actionHookAfter$1(e.$startEvent.lt):globalAllTrace.actionArr=[],pageActivityManager.turnOffRecord(),initFetchRecord(),initXhrRecord())},100),e.$bonree=!1):(actionHookBefore$1(e),e.$bonree=!0))}catch(e){}var t=e.target,n=isString$1(t.tagName)||t.name||"",r=location&&location.search&&location.search||"";if("TEXTAREA"===n||"SELECT"===n||""===n||"BODY"===n)return log("Ignore the click event triggered by the input operation");var i=getDefaultInfo(1),a=(i.target=getXpath$1(t),i.tagName=n,i.param=r,i.timestamp=isDefined(e.startTime)?e.startTime:now(),i.duration=isDefined(e.startTime)?now()-e.startTime:0,i.ci={t:getActionText(t)||t.innerText||"",c:t.className||"",tag:t.nodeName||t.localName||""},i.vn=setting.pageUrl,{className:t.className||"",id:t.id||"",name:t.name||"",innerText:withLength(t.innerText,200),outerHTML:withLength(t.outerHTML,200)});"A"===n&&(a.value=t.href||""),e.$hasHandle=!0,setTimeout(function(){(checkRecord(pageActivityManager.mutationRecord,isDefined(e.startTime)?e.startTime:now())||checkRecord(pageActivityManager.performanceRecord,isDefined(e.startTime)?e.startTime:now())||checkRecord(xhrRecord,isDefined(e.startTime)?e.startTime:now())||checkRecord(fetchRecord,isDefined(e.startTime)?e.startTime:now()))&&debouncer.event({info:i,ext:a}),pageActivityManager.turnOffRecord(),initFetchRecord(),initXhrRecord()},100)}};function initActionCollector(){pageActivityManager.startPageActivityObserver(),on$1(window,"click",function(){setting.pageUrl=sensitiveRuleHandle({p:{info:{url:isDefined(window.location)?window.location.href:""}}}).p.info.url,isDefined(pageActivityManager)&&pageActivityManager.turnOnRecord()},!0),on$1(window,"click",onClick,!0),on$1(window,"click",onClick,!1)}function actionHook(e,n,r,i){var t;return(e=isDefined(e)?e:function(){}).$$original?e:((t=function(){var t=args(arguments);try{isDefined(n)&&n.call(this,t,e)}catch(e){isDefined(i)?i.apply(this,t.concat(e)):log(e)}finally{isDefined(r)&&delay(function(){r.apply(this,t)})}return e.apply(this,t)}).$$original=e,t)}function getXpath(e){var t;return isDefined(e)?(e.$xpath||(200<(t=xpath(e)).length?e.$xpath="[Error: Overflow with more than 200 characters.]":e.$xpath=t),e.$xpath):""}function actionHookBefore(e,t){try{var n,r,i,a,o,s,c,l,d,u,f=e[0],p=now();f.startTime=p,isDefined(f.$sto)||(n=f.target,"BODY"!==(isString(n.tagName)||n.name||"")&&(r=f&&"click"===f.type?1:4,i=f&&f.target&&f.target.tagName||"",a="xpath"+unicode("=")+"".concat(getXpath(n))+",outerHTML"+unicode("=")+"".concat(n.outerHTML),o={t:getActionText(n)||n.innerText||"",c:n.className||"",tag:n.nodeName||n.localName||"",f:"",href:""},s=setting.pageUrl,l=(c=new RootAction).rootActionGet("id"),(d=new FunAction(t,l)).funActionModify("st",p),u=d.funActionBuild(),c.rootActionModify("m",1).rootActionModify("t",r).rootActionModify("n",i).rootActionModify("i",a).rootActionModify("ci",o).rootActionModify("vn",s).rootActionModify("startTime",p).rootActionModify("me",u),globalAllTrace.manageActionAdd(c)))}catch(e){}}function actionHookAfter(){var e,t,n,r=0<arguments.length&&void 0!==arguments[0]&&arguments[0],i=globalAllTrace.mangaeActionGet(),i=i[i.length-1];isDefined(i)&&(n=now(),t=i.rootActionGet("startTime"),i.rootActionGet("haveNet"),e=i.rootActionGet("id"),t=n-t,i.rootActionModify("et",n),i.rootActionModify("lt",t),!0===r&&i.rootActionModify("ice",!0),!0===r)&&(n=globalAllTrace.manageActionUpload(e).rootActionBuild(),!0===setting.ac.ac)&&isDefined(setting.ac.cp)&&checkUpProbability(setting.ac.cp)&&(notifierEmit({t:TRACE_ACTION_DATA,p:{info:n}}),globalAllTrace.actionArr=[])}function recordCustomActionEnd(){actionHookAfter(!0)}function hookActionFun(e){return actionHook(e,actionHookBefore,actionHookAfter)}function onload(){for(var e=Array.prototype.slice.call(document.querySelectorAll("*")),t=["onclick","oninput"],n=0;n<e.length;n++)for(var r,i=e[n],a=0;a<t.length;a++)"function"==typeof i[t[a]]&&(r=i[t[a]],i[t[a]]=hookActionFun(r))}function initNewActionCollection(){on$1(window,"load",onload,!1)}var Z_FIXED$1=4,Z_BINARY=0,Z_TEXT=1,Z_UNKNOWN$1=2;function zero$1(e){for(var t=e.length;0<=--t;)e[t]=0}var STORED_BLOCK=0,STATIC_TREES=1,DYN_TREES=2,MIN_MATCH$1=3,MAX_MATCH$1=258,LENGTH_CODES$1=29,LITERALS$1=256,L_CODES$1=LITERALS$1+1+LENGTH_CODES$1,D_CODES$1=30,BL_CODES$1=19,HEAP_SIZE$1=2*L_CODES$1+1,MAX_BITS$1=15,Buf_size=16,MAX_BL_BITS=7,END_BLOCK=256,REP_3_6=16,REPZ_3_10=17,REPZ_11_138=18,extra_lbits=new Uint8Array([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0]),extra_dbits=new Uint8Array([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13]),extra_blbits=new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7]),bl_order=new Uint8Array([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),DIST_CODE_LEN=512,static_ltree=new Array(2*(L_CODES$1+2)),static_dtree=(zero$1(static_ltree),new Array(2*D_CODES$1)),_dist_code=(zero$1(static_dtree),new Array(DIST_CODE_LEN)),_length_code=(zero$1(_dist_code),new Array(MAX_MATCH$1-MIN_MATCH$1+1)),base_length=(zero$1(_length_code),new Array(LENGTH_CODES$1)),base_dist=(zero$1(base_length),new Array(D_CODES$1)),static_l_desc,static_d_desc,static_bl_desc;function StaticTreeDesc(e,t,n,r,i){this.static_tree=e,this.extra_bits=t,this.extra_base=n,this.elems=r,this.max_length=i,this.has_stree=e&&e.length}function TreeDesc(e,t){this.dyn_tree=e,this.max_code=0,this.stat_desc=t}zero$1(base_dist);var d_code=function(e){return e<256?_dist_code[e]:_dist_code[256+(e>>>7)]},put_short=function(e,t){e.pending_buf[e.pending++]=255&t,e.pending_buf[e.pending++]=t>>>8&255},send_bits=function(e,t,n){e.bi_valid>Buf_size-n?(e.bi_buf|=t<<e.bi_valid&65535,put_short(e,e.bi_buf),e.bi_buf=t>>Buf_size-e.bi_valid,e.bi_valid+=n-Buf_size):(e.bi_buf|=t<<e.bi_valid&65535,e.bi_valid+=n)},send_code=function(e,t,n){send_bits(e,n[2*t],n[2*t+1])},bi_reverse=function(e,t){for(var n=0;n|=1&e,e>>>=1,n<<=1,0<--t;);return n>>>1},bi_flush=function(e){16===e.bi_valid?(put_short(e,e.bi_buf),e.bi_buf=0,e.bi_valid=0):8<=e.bi_valid&&(e.pending_buf[e.pending++]=255&e.bi_buf,e.bi_buf>>=8,e.bi_valid-=8)},gen_bitlen=function(e,t){for(var n,r,i,a,o,s=t.dyn_tree,c=t.max_code,l=t.stat_desc.static_tree,d=t.stat_desc.has_stree,u=t.stat_desc.extra_bits,f=t.stat_desc.extra_base,p=t.stat_desc.max_length,h=0,m=0;m<=MAX_BITS$1;m++)e.bl_count[m]=0;for(s[2*e.heap[e.heap_max]+1]=0,n=e.heap_max+1;n<HEAP_SIZE$1;n++)p<(m=s[2*s[2*(r=e.heap[n])+1]+1]+1)&&(m=p,h++),s[2*r+1]=m,c<r||(e.bl_count[m]++,a=0,f<=r&&(a=u[r-f]),o=s[2*r],e.opt_len+=o*(m+a),d&&(e.static_len+=o*(l[2*r+1]+a)));if(0!==h){do{for(m=p-1;0===e.bl_count[m];)m--}while(e.bl_count[m]--,e.bl_count[m+1]+=2,e.bl_count[p]--,0<(h-=2));for(m=p;0!==m;m--)for(r=e.bl_count[m];0!==r;)c<(i=e.heap[--n])||(s[2*i+1]!==m&&(e.opt_len+=(m-s[2*i+1])*s[2*i],s[2*i+1]=m),r--)}},gen_codes=function(e,t,n){for(var r,i=new Array(MAX_BITS$1+1),a=0,o=1;o<=MAX_BITS$1;o++)a=a+n[o-1]<<1,i[o]=a;for(r=0;r<=t;r++){var s=e[2*r+1];0!==s&&(e[2*r]=bi_reverse(i[s]++,s))}},tr_static_init=function(){for(var e,t,n,r=new Array(MAX_BITS$1+1),i=0,a=0;a<LENGTH_CODES$1-1;a++)for(base_length[a]=i,e=0;e<1<<extra_lbits[a];e++)_length_code[i++]=a;for(_length_code[i-1]=a,a=n=0;a<16;a++)for(base_dist[a]=n,e=0;e<1<<extra_dbits[a];e++)_dist_code[n++]=a;for(n>>=7;a<D_CODES$1;a++)for(base_dist[a]=n<<7,e=0;e<1<<extra_dbits[a]-7;e++)_dist_code[256+n++]=a;for(t=0;t<=MAX_BITS$1;t++)r[t]=0;for(e=0;e<=143;)static_ltree[2*e+1]=8,e++,r[8]++;for(;e<=255;)static_ltree[2*e+1]=9,e++,r[9]++;for(;e<=279;)static_ltree[2*e+1]=7,e++,r[7]++;for(;e<=287;)static_ltree[2*e+1]=8,e++,r[8]++;for(gen_codes(static_ltree,L_CODES$1+1,r),e=0;e<D_CODES$1;e++)static_dtree[2*e+1]=5,static_dtree[2*e]=bi_reverse(e,5);static_l_desc=new StaticTreeDesc(static_ltree,extra_lbits,LITERALS$1+1,L_CODES$1,MAX_BITS$1),static_d_desc=new StaticTreeDesc(static_dtree,extra_dbits,0,D_CODES$1,MAX_BITS$1),static_bl_desc=new StaticTreeDesc(new Array(0),extra_blbits,0,BL_CODES$1,MAX_BL_BITS)},init_block=function(e){for(var t=0;t<L_CODES$1;t++)e.dyn_ltree[2*t]=0;for(t=0;t<D_CODES$1;t++)e.dyn_dtree[2*t]=0;for(t=0;t<BL_CODES$1;t++)e.bl_tree[2*t]=0;e.dyn_ltree[2*END_BLOCK]=1,e.opt_len=e.static_len=0,e.sym_next=e.matches=0},bi_windup=function(e){8<e.bi_valid?put_short(e,e.bi_buf):0<e.bi_valid&&(e.pending_buf[e.pending++]=e.bi_buf),e.bi_buf=0,e.bi_valid=0},smaller=function(e,t,n,r){var i=2*t,a=2*n;return e[i]<e[a]||e[i]===e[a]&&r[t]<=r[n]},pqdownheap=function(e,t,n){for(var r=e.heap[n],i=n<<1;i<=e.heap_len&&(i<e.heap_len&&smaller(t,e.heap[i+1],e.heap[i],e.depth)&&i++,!smaller(t,r,e.heap[i],e.depth));)e.heap[n]=e.heap[i],n=i,i<<=1;e.heap[n]=r},compress_block=function(e,t,n){var r,i,a,o,s=0;if(0!==e.sym_next)for(;r=255&e.pending_buf[e.sym_buf+s++],r+=(255&e.pending_buf[e.sym_buf+s++])<<8,i=e.pending_buf[e.sym_buf+s++],0==r?send_code(e,i,t):(a=_length_code[i],send_code(e,a+LITERALS$1+1,t),0!==(o=extra_lbits[a])&&(i-=base_length[a],send_bits(e,i,o)),a=d_code(--r),send_code(e,a,n),0!==(o=extra_dbits[a])&&(r-=base_dist[a],send_bits(e,r,o))),s<e.sym_next;);send_code(e,END_BLOCK,t)},build_tree=function(e,t){var n,r,i,a=t.dyn_tree,o=t.stat_desc.static_tree,s=t.stat_desc.has_stree,c=t.stat_desc.elems,l=-1;for(e.heap_len=0,e.heap_max=HEAP_SIZE$1,n=0;n<c;n++)0!==a[2*n]?(e.heap[++e.heap_len]=l=n,e.depth[n]=0):a[2*n+1]=0;for(;e.heap_len<2;)a[2*(i=e.heap[++e.heap_len]=l<2?++l:0)]=1,e.depth[i]=0,e.opt_len--,s&&(e.static_len-=o[2*i+1]);for(t.max_code=l,n=e.heap_len>>1;1<=n;n--)pqdownheap(e,a,n);for(i=c;n=e.heap[1],e.heap[1]=e.heap[e.heap_len--],pqdownheap(e,a,1),r=e.heap[1],e.heap[--e.heap_max]=n,e.heap[--e.heap_max]=r,a[2*i]=a[2*n]+a[2*r],e.depth[i]=(e.depth[n]>=e.depth[r]?e.depth[n]:e.depth[r])+1,a[2*n+1]=a[2*r+1]=i,e.heap[1]=i++,pqdownheap(e,a,1),2<=e.heap_len;);e.heap[--e.heap_max]=e.heap[1],gen_bitlen(e,t),gen_codes(a,l,e.bl_count)},scan_tree=function(e,t,n){var r,i,a=-1,o=t[1],s=0,c=7,l=4;for(0===o&&(c=138,l=3),t[2*(n+1)+1]=65535,r=0;r<=n;r++)i=o,o=t[2*(r+1)+1],++s<c&&i===o||(s<l?e.bl_tree[2*i]+=s:0!==i?(i!==a&&e.bl_tree[2*i]++,e.bl_tree[2*REP_3_6]++):s<=10?e.bl_tree[2*REPZ_3_10]++:e.bl_tree[2*REPZ_11_138]++,a=i,l=(s=0)===o?(c=138,3):i===o?(c=6,3):(c=7,4))},send_tree=function(e,t,n){var r,i,a=-1,o=t[1],s=0,c=7,l=4;for(0===o&&(c=138,l=3),r=0;r<=n;r++)if(i=o,o=t[2*(r+1)+1],!(++s<c&&i===o)){if(s<l)for(;send_code(e,i,e.bl_tree),0!=--s;);else 0!==i?(i!==a&&(send_code(e,i,e.bl_tree),s--),send_code(e,REP_3_6,e.bl_tree),send_bits(e,s-3,2)):s<=10?(send_code(e,REPZ_3_10,e.bl_tree),send_bits(e,s-3,3)):(send_code(e,REPZ_11_138,e.bl_tree),send_bits(e,s-11,7));a=i,l=(s=0)===o?(c=138,3):i===o?(c=6,3):(c=7,4)}},build_bl_tree=function(e){var t;for(scan_tree(e,e.dyn_ltree,e.l_desc.max_code),scan_tree(e,e.dyn_dtree,e.d_desc.max_code),build_tree(e,e.bl_desc),t=BL_CODES$1-1;3<=t&&0===e.bl_tree[2*bl_order[t]+1];t--);return e.opt_len+=3*(t+1)+5+5+4,t},send_all_trees=function(e,t,n,r){var i;for(send_bits(e,t-257,5),send_bits(e,n-1,5),send_bits(e,r-4,4),i=0;i<r;i++)send_bits(e,e.bl_tree[2*bl_order[i]+1],3);send_tree(e,e.dyn_ltree,t-1),send_tree(e,e.dyn_dtree,n-1)},detect_data_type=function(e){for(var t=4093624447,n=0;n<=31;n++,t>>>=1)if(1&t&&0!==e.dyn_ltree[2*n])return Z_BINARY;if(0!==e.dyn_ltree[18]||0!==e.dyn_ltree[20]||0!==e.dyn_ltree[26])return Z_TEXT;for(n=32;n<LITERALS$1;n++)if(0!==e.dyn_ltree[2*n])return Z_TEXT;return Z_BINARY},static_init_done=!1,_tr_init$1=function(e){static_init_done||(tr_static_init(),static_init_done=!0),e.l_desc=new TreeDesc(e.dyn_ltree,static_l_desc),e.d_desc=new TreeDesc(e.dyn_dtree,static_d_desc),e.bl_desc=new TreeDesc(e.bl_tree,static_bl_desc),e.bi_buf=0,e.bi_valid=0,init_block(e)},_tr_stored_block$1=function(e,t,n,r){send_bits(e,(STORED_BLOCK<<1)+(r?1:0),3),bi_windup(e),put_short(e,n),put_short(e,~n),n&&e.pending_buf.set(e.window.subarray(t,t+n),e.pending),e.pending+=n},_tr_align$1=function(e){send_bits(e,STATIC_TREES<<1,3),send_code(e,END_BLOCK,static_ltree),bi_flush(e)},_tr_flush_block$1=function(e,t,n,r){var i,a,o=0;0<e.level?(e.strm.data_type===Z_UNKNOWN$1&&(e.strm.data_type=detect_data_type(e)),build_tree(e,e.l_desc),build_tree(e,e.d_desc),o=build_bl_tree(e),i=e.opt_len+3+7>>>3,(a=e.static_len+3+7>>>3)<=i&&(i=a)):i=a=n+5,n+4<=i&&-1!==t?_tr_stored_block$1(e,t,n,r):e.strategy===Z_FIXED$1||a===i?(send_bits(e,(STATIC_TREES<<1)+(r?1:0),3),compress_block(e,static_ltree,static_dtree)):(send_bits(e,(DYN_TREES<<1)+(r?1:0),3),send_all_trees(e,e.l_desc.max_code+1,e.d_desc.max_code+1,o+1),compress_block(e,e.dyn_ltree,e.dyn_dtree)),init_block(e),r&&bi_windup(e)},_tr_tally$1=function(e,t,n){return e.pending_buf[e.sym_buf+e.sym_next++]=t,e.pending_buf[e.sym_buf+e.sym_next++]=t>>8,e.pending_buf[e.sym_buf+e.sym_next++]=n,0===t?e.dyn_ltree[2*n]++:(e.matches++,t--,e.dyn_ltree[2*(_length_code[n]+LITERALS$1+1)]++,e.dyn_dtree[2*d_code(t)]++),e.sym_next===e.sym_end},_tr_init_1=_tr_init$1,_tr_stored_block_1=_tr_stored_block$1,_tr_flush_block_1=_tr_flush_block$1,_tr_tally_1=_tr_tally$1,_tr_align_1=_tr_align$1,trees={_tr_init:_tr_init_1,_tr_stored_block:_tr_stored_block_1,_tr_flush_block:_tr_flush_block_1,_tr_tally:_tr_tally_1,_tr_align:_tr_align_1},adler32=function(e,t,n,r){for(var i=65535&e|0,a=e>>>16&65535|0,o=0;0!==n;){for(n-=o=2e3<n?2e3:n;a=a+(i=i+t[r++]|0)|0,--o;);i%=65521,a%=65521}return i|a<<16|0},adler32_1=adler32,makeTable=function(){for(var e=[],t=0;t<256;t++){for(var n=t,r=0;r<8;r++)n=1&n?3988292384^n>>>1:n>>>1;e[t]=n}return e},crcTable=new Uint32Array(makeTable()),crc32=function(e,t,n,r){var i=crcTable,a=r+n;e^=-1;for(var o=r;o<a;o++)e=e>>>8^i[255&(e^t[o])];return-1^e},crc32_1=crc32,messages={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"},constants$2={Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_MEM_ERROR:-4,Z_BUF_ERROR:-5,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,Z_BINARY:0,Z_TEXT:1,Z_UNKNOWN:2,Z_DEFLATED:8},_tr_init=trees._tr_init,_tr_stored_block=trees._tr_stored_block,_tr_flush_block=trees._tr_flush_block,_tr_tally=trees._tr_tally,_tr_align=trees._tr_align,Z_NO_FLUSH$2=constants$2.Z_NO_FLUSH,Z_PARTIAL_FLUSH=constants$2.Z_PARTIAL_FLUSH,Z_FULL_FLUSH$1=constants$2.Z_FULL_FLUSH,Z_FINISH$3=constants$2.Z_FINISH,Z_BLOCK$1=constants$2.Z_BLOCK,Z_OK$3=constants$2.Z_OK,Z_STREAM_END$3=constants$2.Z_STREAM_END,Z_STREAM_ERROR$2=constants$2.Z_STREAM_ERROR,Z_DATA_ERROR$2=constants$2.Z_DATA_ERROR,Z_BUF_ERROR$1=constants$2.Z_BUF_ERROR,Z_DEFAULT_COMPRESSION$1=constants$2.Z_DEFAULT_COMPRESSION,Z_FILTERED=constants$2.Z_FILTERED,Z_HUFFMAN_ONLY=constants$2.Z_HUFFMAN_ONLY,Z_RLE=constants$2.Z_RLE,Z_FIXED=constants$2.Z_FIXED,Z_DEFAULT_STRATEGY$1=constants$2.Z_DEFAULT_STRATEGY,Z_UNKNOWN=constants$2.Z_UNKNOWN,Z_DEFLATED$2=constants$2.Z_DEFLATED,MAX_MEM_LEVEL=9,MAX_WBITS$1=15,DEF_MEM_LEVEL=8,LENGTH_CODES=29,LITERALS=256,L_CODES=LITERALS+1+LENGTH_CODES,D_CODES=30,BL_CODES=19,HEAP_SIZE=2*L_CODES+1,MAX_BITS=15,MIN_MATCH=3,MAX_MATCH=258,MIN_LOOKAHEAD=MAX_MATCH+MIN_MATCH+1,PRESET_DICT=32,INIT_STATE=42,GZIP_STATE=57,EXTRA_STATE=69,NAME_STATE=73,COMMENT_STATE=91,HCRC_STATE=103,BUSY_STATE=113,FINISH_STATE=666,BS_NEED_MORE=1,BS_BLOCK_DONE=2,BS_FINISH_STARTED=3,BS_FINISH_DONE=4,OS_CODE=3,err=function(e,t){return e.msg=messages[t],t},rank=function(e){return 2*e-(4<e?9:0)},zero=function(e){for(var t=e.length;0<=--t;)e[t]=0},slide_hash=function(e){for(var t,n=e.w_size,r=e.hash_size,i=r;t=e.head[--i],e.head[i]=n<=t?t-n:0,--r;);for(i=r=n;t=e.prev[--i],e.prev[i]=n<=t?t-n:0,--r;);},HASH_ZLIB=function(e,t,n){return(t<<e.hash_shift^n)&e.hash_mask},HASH=HASH_ZLIB,flush_pending=function(e){var t=e.state,n=t.pending;0!==(n=n>e.avail_out?e.avail_out:n)&&(e.output.set(t.pending_buf.subarray(t.pending_out,t.pending_out+n),e.next_out),e.next_out+=n,t.pending_out+=n,e.total_out+=n,e.avail_out-=n,t.pending-=n,0===t.pending)&&(t.pending_out=0)},flush_block_only=function(e,t){_tr_flush_block(e,0<=e.block_start?e.block_start:-1,e.strstart-e.block_start,t),e.block_start=e.strstart,flush_pending(e.strm)},put_byte=function(e,t){e.pending_buf[e.pending++]=t},putShortMSB=function(e,t){e.pending_buf[e.pending++]=t>>>8&255,e.pending_buf[e.pending++]=255&t},read_buf=function(e,t,n,r){var i=e.avail_in;return 0===(i=r<i?r:i)?0:(e.avail_in-=i,t.set(e.input.subarray(e.next_in,e.next_in+i),n),1===e.state.wrap?e.adler=adler32_1(e.adler,t,i,n):2===e.state.wrap&&(e.adler=crc32_1(e.adler,t,i,n)),e.next_in+=i,e.total_in+=i,i)},longest_match=function(e,t){var n,r,i=e.max_chain_length,a=e.strstart,o=e.prev_length,s=e.nice_match,c=e.strstart>e.w_size-MIN_LOOKAHEAD?e.strstart-(e.w_size-MIN_LOOKAHEAD):0,l=e.window,d=e.w_mask,u=e.prev,f=e.strstart+MAX_MATCH,p=l[a+o-1],h=l[a+o];e.prev_length>=e.good_match&&(i>>=2),s>e.lookahead&&(s=e.lookahead);do{if(l[(n=t)+o]===h&&l[n+o-1]===p&&l[n]===l[a]&&l[++n]===l[a+1]){for(a+=2,n++;l[++a]===l[++n]&&l[++a]===l[++n]&&l[++a]===l[++n]&&l[++a]===l[++n]&&l[++a]===l[++n]&&l[++a]===l[++n]&&l[++a]===l[++n]&&l[++a]===l[++n]&&a<f;);if(r=MAX_MATCH-(f-a),a=f-MAX_MATCH,o<r){if(e.match_start=t,s<=(o=r))break;p=l[a+o-1],h=l[a+o]}}}while((t=u[t&d])>c&&0!=--i);return o<=e.lookahead?o:e.lookahead},fill_window=function(e){var t,n,r=e.w_size;do{if(t=e.window_size-e.lookahead-e.strstart,e.strstart>=r+(r-MIN_LOOKAHEAD)&&(e.window.set(e.window.subarray(r,r+r-t),0),e.match_start-=r,e.strstart-=r,e.block_start-=r,e.insert>e.strstart&&(e.insert=e.strstart),slide_hash(e),t+=r),0===e.strm.avail_in)break;if(t=read_buf(e.strm,e.window,e.strstart+e.lookahead,t),e.lookahead+=t,e.lookahead+e.insert>=MIN_MATCH)for(n=e.strstart-e.insert,e.ins_h=e.window[n],e.ins_h=HASH(e,e.ins_h,e.window[n+1]);e.insert&&(e.ins_h=HASH(e,e.ins_h,e.window[n+MIN_MATCH-1]),e.prev[n&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=n,n++,e.insert--,!(e.lookahead+e.insert<MIN_MATCH)););}while(e.lookahead<MIN_LOOKAHEAD&&0!==e.strm.avail_in)},deflate_stored=function(e,t){for(var n,r,i,a=e.pending_buf_size-5>e.w_size?e.w_size:e.pending_buf_size-5,o=0,s=e.strm.avail_in;n=65535,i=e.bi_valid+42>>3,!(e.strm.avail_out<i||(n=(i=e.strm.avail_out-i)<(n=n>(r=e.strstart-e.block_start)+e.strm.avail_in?r+e.strm.avail_in:n)?i:n)<a&&(0===n&&t!==Z_FINISH$3||t===Z_NO_FLUSH$2||n!==r+e.strm.avail_in)||(o=t===Z_FINISH$3&&n===r+e.strm.avail_in?1:0,_tr_stored_block(e,0,0,o),e.pending_buf[e.pending-4]=n,e.pending_buf[e.pending-3]=n>>8,e.pending_buf[e.pending-2]=~n,e.pending_buf[e.pending-1]=~n>>8,flush_pending(e.strm),r&&(n<r&&(r=n),e.strm.output.set(e.window.subarray(e.block_start,e.block_start+r),e.strm.next_out),e.strm.next_out+=r,e.strm.avail_out-=r,e.strm.total_out+=r,e.block_start+=r,n-=r),n&&(read_buf(e.strm,e.strm.output,e.strm.next_out,n),e.strm.next_out+=n,e.strm.avail_out-=n,e.strm.total_out+=n),0!==o)););return(s-=e.strm.avail_in)&&(s>=e.w_size?(e.matches=2,e.window.set(e.strm.input.subarray(e.strm.next_in-e.w_size,e.strm.next_in),0),e.strstart=e.w_size,e.insert=e.strstart):(e.window_size-e.strstart<=s&&(e.strstart-=e.w_size,e.window.set(e.window.subarray(e.w_size,e.w_size+e.strstart),0),e.matches<2&&e.matches++,e.insert>e.strstart)&&(e.insert=e.strstart),e.window.set(e.strm.input.subarray(e.strm.next_in-s,e.strm.next_in),e.strstart),e.strstart+=s,e.insert+=s>e.w_size-e.insert?e.w_size-e.insert:s),e.block_start=e.strstart),e.high_water<e.strstart&&(e.high_water=e.strstart),o?BS_FINISH_DONE:t!==Z_NO_FLUSH$2&&t!==Z_FINISH$3&&0===e.strm.avail_in&&e.strstart===e.block_start?BS_BLOCK_DONE:(i=e.window_size-e.strstart,e.strm.avail_in>i&&e.block_start>=e.w_size&&(e.block_start-=e.w_size,e.strstart-=e.w_size,e.window.set(e.window.subarray(e.w_size,e.w_size+e.strstart),0),e.matches<2&&e.matches++,i+=e.w_size,e.insert>e.strstart)&&(e.insert=e.strstart),(i=i>e.strm.avail_in?e.strm.avail_in:i)&&(read_buf(e.strm,e.window,e.strstart,i),e.strstart+=i,e.insert+=i>e.w_size-e.insert?e.w_size-e.insert:i),e.high_water<e.strstart&&(e.high_water=e.strstart),i=e.bi_valid+42>>3,((a=(i=65535<e.pending_buf_size-i?65535:e.pending_buf_size-i)>e.w_size?e.w_size:i)<=(r=e.strstart-e.block_start)||(r||t===Z_FINISH$3)&&t!==Z_NO_FLUSH$2&&0===e.strm.avail_in&&r<=i)&&(n=i<r?i:r,o=t===Z_FINISH$3&&0===e.strm.avail_in&&n===r?1:0,_tr_stored_block(e,e.block_start,n,o),e.block_start+=n,flush_pending(e.strm)),o?BS_FINISH_STARTED:BS_NEED_MORE)},deflate_fast=function(e,t){for(var n,r;;){if(e.lookahead<MIN_LOOKAHEAD){if(fill_window(e),e.lookahead<MIN_LOOKAHEAD&&t===Z_NO_FLUSH$2)return BS_NEED_MORE;if(0===e.lookahead)break}if(n=0,e.lookahead>=MIN_MATCH&&(e.ins_h=HASH(e,e.ins_h,e.window[e.strstart+MIN_MATCH-1]),n=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),0!==n&&e.strstart-n<=e.w_size-MIN_LOOKAHEAD&&(e.match_length=longest_match(e,n)),e.match_length>=MIN_MATCH)if(r=_tr_tally(e,e.strstart-e.match_start,e.match_length-MIN_MATCH),e.lookahead-=e.match_length,e.match_length<=e.max_lazy_match&&e.lookahead>=MIN_MATCH){for(e.match_length--;e.strstart++,e.ins_h=HASH(e,e.ins_h,e.window[e.strstart+MIN_MATCH-1]),n=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart,0!=--e.match_length;);e.strstart++}else e.strstart+=e.match_length,e.match_length=0,e.ins_h=e.window[e.strstart],e.ins_h=HASH(e,e.ins_h,e.window[e.strstart+1]);else r=_tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++;if(r&&(flush_block_only(e,!1),0===e.strm.avail_out))return BS_NEED_MORE}return e.insert=e.strstart<MIN_MATCH-1?e.strstart:MIN_MATCH-1,t===Z_FINISH$3?(flush_block_only(e,!0),0===e.strm.avail_out?BS_FINISH_STARTED:BS_FINISH_DONE):e.sym_next&&(flush_block_only(e,!1),0===e.strm.avail_out)?BS_NEED_MORE:BS_BLOCK_DONE},deflate_slow=function(e,t){for(var n,r,i;;){if(e.lookahead<MIN_LOOKAHEAD){if(fill_window(e),e.lookahead<MIN_LOOKAHEAD&&t===Z_NO_FLUSH$2)return BS_NEED_MORE;if(0===e.lookahead)break}if(n=0,e.lookahead>=MIN_MATCH&&(e.ins_h=HASH(e,e.ins_h,e.window[e.strstart+MIN_MATCH-1]),n=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),e.prev_length=e.match_length,e.prev_match=e.match_start,e.match_length=MIN_MATCH-1,0!==n&&e.prev_length<e.max_lazy_match&&e.strstart-n<=e.w_size-MIN_LOOKAHEAD&&(e.match_length=longest_match(e,n),e.match_length<=5)&&(e.strategy===Z_FILTERED||e.match_length===MIN_MATCH&&4096<e.strstart-e.match_start)&&(e.match_length=MIN_MATCH-1),e.prev_length>=MIN_MATCH&&e.match_length<=e.prev_length){for(i=e.strstart+e.lookahead-MIN_MATCH,r=_tr_tally(e,e.strstart-1-e.prev_match,e.prev_length-MIN_MATCH),e.lookahead-=e.prev_length-1,e.prev_length-=2;++e.strstart<=i&&(e.ins_h=HASH(e,e.ins_h,e.window[e.strstart+MIN_MATCH-1]),n=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),0!=--e.prev_length;);if(e.match_available=0,e.match_length=MIN_MATCH-1,e.strstart++,r&&(flush_block_only(e,!1),0===e.strm.avail_out))return BS_NEED_MORE}else if(e.match_available){if((r=_tr_tally(e,0,e.window[e.strstart-1]))&&flush_block_only(e,!1),e.strstart++,e.lookahead--,0===e.strm.avail_out)return BS_NEED_MORE}else e.match_available=1,e.strstart++,e.lookahead--}return e.match_available&&(r=_tr_tally(e,0,e.window[e.strstart-1]),e.match_available=0),e.insert=e.strstart<MIN_MATCH-1?e.strstart:MIN_MATCH-1,t===Z_FINISH$3?(flush_block_only(e,!0),0===e.strm.avail_out?BS_FINISH_STARTED:BS_FINISH_DONE):e.sym_next&&(flush_block_only(e,!1),0===e.strm.avail_out)?BS_NEED_MORE:BS_BLOCK_DONE},deflate_rle=function(e,t){for(var n,r,i,a,o=e.window;;){if(e.lookahead<=MAX_MATCH){if(fill_window(e),e.lookahead<=MAX_MATCH&&t===Z_NO_FLUSH$2)return BS_NEED_MORE;if(0===e.lookahead)break}if(e.match_length=0,e.lookahead>=MIN_MATCH&&0<e.strstart&&(r=o[i=e.strstart-1])===o[++i]&&r===o[++i]&&r===o[++i]){for(a=e.strstart+MAX_MATCH;r===o[++i]&&r===o[++i]&&r===o[++i]&&r===o[++i]&&r===o[++i]&&r===o[++i]&&r===o[++i]&&r===o[++i]&&i<a;);e.match_length=MAX_MATCH-(a-i),e.match_length>e.lookahead&&(e.match_length=e.lookahead)}if(e.match_length>=MIN_MATCH?(n=_tr_tally(e,1,e.match_length-MIN_MATCH),e.lookahead-=e.match_length,e.strstart+=e.match_length,e.match_length=0):(n=_tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++),n&&(flush_block_only(e,!1),0===e.strm.avail_out))return BS_NEED_MORE}return e.insert=0,t===Z_FINISH$3?(flush_block_only(e,!0),0===e.strm.avail_out?BS_FINISH_STARTED:BS_FINISH_DONE):e.sym_next&&(flush_block_only(e,!1),0===e.strm.avail_out)?BS_NEED_MORE:BS_BLOCK_DONE},deflate_huff=function(e,t){for(var n;;){if(0===e.lookahead&&(fill_window(e),0===e.lookahead)){if(t===Z_NO_FLUSH$2)return BS_NEED_MORE;break}if(e.match_length=0,n=_tr_tally(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++,n&&(flush_block_only(e,!1),0===e.strm.avail_out))return BS_NEED_MORE}return e.insert=0,t===Z_FINISH$3?(flush_block_only(e,!0),0===e.strm.avail_out?BS_FINISH_STARTED:BS_FINISH_DONE):e.sym_next&&(flush_block_only(e,!1),0===e.strm.avail_out)?BS_NEED_MORE:BS_BLOCK_DONE};function Config(e,t,n,r,i){this.good_length=e,this.max_lazy=t,this.nice_length=n,this.max_chain=r,this.func=i}var configuration_table=[new Config(0,0,0,0,deflate_stored),new Config(4,4,8,4,deflate_fast),new Config(4,5,16,8,deflate_fast),new Config(4,6,32,32,deflate_fast),new Config(4,4,16,16,deflate_slow),new Config(8,16,32,32,deflate_slow),new Config(8,16,128,128,deflate_slow),new Config(8,32,128,256,deflate_slow),new Config(32,128,258,1024,deflate_slow),new Config(32,258,258,4096,deflate_slow)],lm_init=function(e){e.window_size=2*e.w_size,zero(e.head),e.max_lazy_match=configuration_table[e.level].max_lazy,e.good_match=configuration_table[e.level].good_length,e.nice_match=configuration_table[e.level].nice_length,e.max_chain_length=configuration_table[e.level].max_chain,e.strstart=0,e.block_start=0,e.lookahead=0,e.insert=0,e.match_length=e.prev_length=MIN_MATCH-1,e.match_available=0,e.ins_h=0};function DeflateState(){this.strm=null,this.status=0,this.pending_buf=null,this.pending_buf_size=0,this.pending_out=0,this.pending=0,this.wrap=0,this.gzhead=null,this.gzindex=0,this.method=Z_DEFLATED$2,this.last_flush=-1,this.w_size=0,this.w_bits=0,this.w_mask=0,this.window=null,this.window_size=0,this.prev=null,this.head=null,this.ins_h=0,this.hash_size=0,this.hash_bits=0,this.hash_mask=0,this.hash_shift=0,this.block_start=0,this.match_length=0,this.prev_match=0,this.match_available=0,this.strstart=0,this.match_start=0,this.lookahead=0,this.prev_length=0,this.max_chain_length=0,this.max_lazy_match=0,this.level=0,this.strategy=0,this.good_match=0,this.nice_match=0,this.dyn_ltree=new Uint16Array(2*HEAP_SIZE),this.dyn_dtree=new Uint16Array(2*(2*D_CODES+1)),this.bl_tree=new Uint16Array(2*(2*BL_CODES+1)),zero(this.dyn_ltree),zero(this.dyn_dtree),zero(this.bl_tree),this.l_desc=null,this.d_desc=null,this.bl_desc=null,this.bl_count=new Uint16Array(MAX_BITS+1),this.heap=new Uint16Array(2*L_CODES+1),zero(this.heap),this.heap_len=0,this.heap_max=0,this.depth=new Uint16Array(2*L_CODES+1),zero(this.depth),this.sym_buf=0,this.lit_bufsize=0,this.sym_next=0,this.sym_end=0,this.opt_len=0,this.static_len=0,this.matches=0,this.insert=0,this.bi_buf=0,this.bi_valid=0}var deflateStateCheck=function(e){var t;return!e||!(t=e.state)||t.strm!==e||t.status!==INIT_STATE&&t.status!==GZIP_STATE&&t.status!==EXTRA_STATE&&t.status!==NAME_STATE&&t.status!==COMMENT_STATE&&t.status!==HCRC_STATE&&t.status!==BUSY_STATE&&t.status!==FINISH_STATE?1:0},deflateResetKeep=function(e){if(deflateStateCheck(e))return err(e,Z_STREAM_ERROR$2);e.total_in=e.total_out=0,e.data_type=Z_UNKNOWN;var t=e.state;return t.pending=0,t.pending_out=0,t.wrap<0&&(t.wrap=-t.wrap),t.status=2===t.wrap?GZIP_STATE:t.wrap?INIT_STATE:BUSY_STATE,e.adler=2===t.wrap?0:1,t.last_flush=-2,_tr_init(t),Z_OK$3},deflateReset=function(e){var t=deflateResetKeep(e);return t===Z_OK$3&&lm_init(e.state),t},deflateSetHeader=function(e,t){return deflateStateCheck(e)||2!==e.state.wrap?Z_STREAM_ERROR$2:(e.state.gzhead=t,Z_OK$3)},deflateInit2=function(e,t,n,r,i,a){if(!e)return Z_STREAM_ERROR$2;var o=1;if(t===Z_DEFAULT_COMPRESSION$1&&(t=6),r<0?(o=0,r=-r):15<r&&(o=2,r-=16),i<1||MAX_MEM_LEVEL<i||n!==Z_DEFLATED$2||r<8||15<r||t<0||9<t||a<0||Z_FIXED<a||8===r&&1!==o)return err(e,Z_STREAM_ERROR$2);8===r&&(r=9);var s=new DeflateState;return(e.state=s).strm=e,s.status=INIT_STATE,s.wrap=o,s.gzhead=null,s.w_bits=r,s.w_size=1<<s.w_bits,s.w_mask=s.w_size-1,s.hash_bits=i+7,s.hash_size=1<<s.hash_bits,s.hash_mask=s.hash_size-1,s.hash_shift=~~((s.hash_bits+MIN_MATCH-1)/MIN_MATCH),s.window=new Uint8Array(2*s.w_size),s.head=new Uint16Array(s.hash_size),s.prev=new Uint16Array(s.w_size),s.lit_bufsize=1<<i+6,s.pending_buf_size=4*s.lit_bufsize,s.pending_buf=new Uint8Array(s.pending_buf_size),s.sym_buf=s.lit_bufsize,s.sym_end=3*(s.lit_bufsize-1),s.level=t,s.strategy=a,s.method=n,deflateReset(e)},deflateInit=function(e,t){return deflateInit2(e,t,Z_DEFLATED$2,MAX_WBITS$1,DEF_MEM_LEVEL,Z_DEFAULT_STRATEGY$1)},deflate$2=function(e,t){if(deflateStateCheck(e)||Z_BLOCK$1<t||t<0)return e?err(e,Z_STREAM_ERROR$2):Z_STREAM_ERROR$2;var n=e.state;if(!e.output||0!==e.avail_in&&!e.input||n.status===FINISH_STATE&&t!==Z_FINISH$3)return err(e,0===e.avail_out?Z_BUF_ERROR$1:Z_STREAM_ERROR$2);var r=n.last_flush;if(n.last_flush=t,0!==n.pending){if(flush_pending(e),0===e.avail_out)return n.last_flush=-1,Z_OK$3}else if(0===e.avail_in&&rank(t)<=rank(r)&&t!==Z_FINISH$3)return err(e,Z_BUF_ERROR$1);if(n.status===FINISH_STATE&&0!==e.avail_in)return err(e,Z_BUF_ERROR$1);if(n.status===INIT_STATE&&0===n.wrap&&(n.status=BUSY_STATE),n.status===INIT_STATE){r=Z_DEFLATED$2+(n.w_bits-8<<4)<<8;if(r|=(n.strategy>=Z_HUFFMAN_ONLY||n.level<2?0:n.level<6?1:6===n.level?2:3)<<6,0!==n.strstart&&(r|=PRESET_DICT),putShortMSB(n,r+=31-r%31),0!==n.strstart&&(putShortMSB(n,e.adler>>>16),putShortMSB(n,65535&e.adler)),e.adler=1,n.status=BUSY_STATE,flush_pending(e),0!==n.pending)return n.last_flush=-1,Z_OK$3}if(n.status===GZIP_STATE)if(e.adler=0,put_byte(n,31),put_byte(n,139),put_byte(n,8),n.gzhead)put_byte(n,(n.gzhead.text?1:0)+(n.gzhead.hcrc?2:0)+(n.gzhead.extra?4:0)+(n.gzhead.name?8:0)+(n.gzhead.comment?16:0)),put_byte(n,255&n.gzhead.time),put_byte(n,n.gzhead.time>>8&255),put_byte(n,n.gzhead.time>>16&255),put_byte(n,n.gzhead.time>>24&255),put_byte(n,9===n.level?2:n.strategy>=Z_HUFFMAN_ONLY||n.level<2?4:0),put_byte(n,255&n.gzhead.os),n.gzhead.extra&&n.gzhead.extra.length&&(put_byte(n,255&n.gzhead.extra.length),put_byte(n,n.gzhead.extra.length>>8&255)),n.gzhead.hcrc&&(e.adler=crc32_1(e.adler,n.pending_buf,n.pending,0)),n.gzindex=0,n.status=EXTRA_STATE;else if(put_byte(n,0),put_byte(n,0),put_byte(n,0),put_byte(n,0),put_byte(n,0),put_byte(n,9===n.level?2:n.strategy>=Z_HUFFMAN_ONLY||n.level<2?4:0),put_byte(n,OS_CODE),n.status=BUSY_STATE,flush_pending(e),0!==n.pending)return n.last_flush=-1,Z_OK$3;if(n.status===EXTRA_STATE){if(n.gzhead.extra){for(var i=n.pending,a=(65535&n.gzhead.extra.length)-n.gzindex;n.pending+a>n.pending_buf_size;){var o=n.pending_buf_size-n.pending;if(n.pending_buf.set(n.gzhead.extra.subarray(n.gzindex,n.gzindex+o),n.pending),n.pending=n.pending_buf_size,n.gzhead.hcrc&&n.pending>i&&(e.adler=crc32_1(e.adler,n.pending_buf,n.pending-i,i)),n.gzindex+=o,flush_pending(e),0!==n.pending)return n.last_flush=-1,Z_OK$3;i=0,a-=o}r=new Uint8Array(n.gzhead.extra);n.pending_buf.set(r.subarray(n.gzindex,n.gzindex+a),n.pending),n.pending+=a,n.gzhead.hcrc&&n.pending>i&&(e.adler=crc32_1(e.adler,n.pending_buf,n.pending-i,i)),n.gzindex=0}n.status=NAME_STATE}if(n.status===NAME_STATE){if(n.gzhead.name){var s,c=n.pending;do{if(n.pending===n.pending_buf_size){if(n.gzhead.hcrc&&n.pending>c&&(e.adler=crc32_1(e.adler,n.pending_buf,n.pending-c,c)),flush_pending(e),0!==n.pending)return n.last_flush=-1,Z_OK$3;c=0}}while(s=n.gzindex<n.gzhead.name.length?255&n.gzhead.name.charCodeAt(n.gzindex++):0,put_byte(n,s),0!==s);n.gzhead.hcrc&&n.pending>c&&(e.adler=crc32_1(e.adler,n.pending_buf,n.pending-c,c)),n.gzindex=0}n.status=COMMENT_STATE}if(n.status===COMMENT_STATE){if(n.gzhead.comment){var l,d=n.pending;do{if(n.pending===n.pending_buf_size){if(n.gzhead.hcrc&&n.pending>d&&(e.adler=crc32_1(e.adler,n.pending_buf,n.pending-d,d)),flush_pending(e),0!==n.pending)return n.last_flush=-1,Z_OK$3;d=0}}while(l=n.gzindex<n.gzhead.comment.length?255&n.gzhead.comment.charCodeAt(n.gzindex++):0,put_byte(n,l),0!==l);n.gzhead.hcrc&&n.pending>d&&(e.adler=crc32_1(e.adler,n.pending_buf,n.pending-d,d))}n.status=HCRC_STATE}if(n.status===HCRC_STATE){if(n.gzhead.hcrc){if(n.pending+2>n.pending_buf_size&&(flush_pending(e),0!==n.pending))return n.last_flush=-1,Z_OK$3;put_byte(n,255&e.adler),put_byte(n,e.adler>>8&255),e.adler=0}if(n.status=BUSY_STATE,flush_pending(e),0!==n.pending)return n.last_flush=-1,Z_OK$3}if(0!==e.avail_in||0!==n.lookahead||t!==Z_NO_FLUSH$2&&n.status!==FINISH_STATE){r=0===n.level?deflate_stored(n,t):n.strategy===Z_HUFFMAN_ONLY?deflate_huff(n,t):n.strategy===Z_RLE?deflate_rle(n,t):configuration_table[n.level].func(n,t);if(r!==BS_FINISH_STARTED&&r!==BS_FINISH_DONE||(n.status=FINISH_STATE),r===BS_NEED_MORE||r===BS_FINISH_STARTED)return 0===e.avail_out&&(n.last_flush=-1),Z_OK$3;if(r===BS_BLOCK_DONE&&(t===Z_PARTIAL_FLUSH?_tr_align(n):t!==Z_BLOCK$1&&(_tr_stored_block(n,0,0,!1),t===Z_FULL_FLUSH$1)&&(zero(n.head),0===n.lookahead)&&(n.strstart=0,n.block_start=0,n.insert=0),flush_pending(e),0===e.avail_out))return n.last_flush=-1,Z_OK$3}return t!==Z_FINISH$3||!(n.wrap<=0)&&(2===n.wrap?(put_byte(n,255&e.adler),put_byte(n,e.adler>>8&255),put_byte(n,e.adler>>16&255),put_byte(n,e.adler>>24&255),put_byte(n,255&e.total_in),put_byte(n,e.total_in>>8&255),put_byte(n,e.total_in>>16&255),put_byte(n,e.total_in>>24&255)):(putShortMSB(n,e.adler>>>16),putShortMSB(n,65535&e.adler)),flush_pending(e),0<n.wrap&&(n.wrap=-n.wrap),0!==n.pending)?Z_OK$3:Z_STREAM_END$3},deflateEnd=function(e){var t;return deflateStateCheck(e)?Z_STREAM_ERROR$2:(t=e.state.status,e.state=null,t===BUSY_STATE?err(e,Z_DATA_ERROR$2):Z_OK$3)},deflateSetDictionary=function(e,t){var n=t.length;if(deflateStateCheck(e))return Z_STREAM_ERROR$2;var r=e.state,i=r.wrap;if(2===i||1===i&&r.status!==INIT_STATE||r.lookahead)return Z_STREAM_ERROR$2;1===i&&(e.adler=adler32_1(e.adler,t,n,0)),r.wrap=0,n>=r.w_size&&(0===i&&(zero(r.head),r.strstart=0,r.block_start=0,r.insert=0),(a=new Uint8Array(r.w_size)).set(t.subarray(n-r.w_size,n),0),t=a,n=r.w_size);var a=e.avail_in,o=e.next_in,s=e.input;for(e.avail_in=n,e.next_in=0,e.input=t,fill_window(r);r.lookahead>=MIN_MATCH;){for(var c=r.strstart,l=r.lookahead-(MIN_MATCH-1);r.ins_h=HASH(r,r.ins_h,r.window[c+MIN_MATCH-1]),r.prev[c&r.w_mask]=r.head[r.ins_h],r.head[r.ins_h]=c,c++,--l;);r.strstart=c,r.lookahead=MIN_MATCH-1,fill_window(r)}return r.strstart+=r.lookahead,r.block_start=r.strstart,r.insert=r.lookahead,r.lookahead=0,r.match_length=r.prev_length=MIN_MATCH-1,r.match_available=0,e.next_in=o,e.input=s,e.avail_in=a,r.wrap=i,Z_OK$3},deflateInit_1=deflateInit,deflateInit2_1=deflateInit2,deflateReset_1=deflateReset,deflateResetKeep_1=deflateResetKeep,deflateSetHeader_1=deflateSetHeader,deflate_2$1=deflate$2,deflateEnd_1=deflateEnd,deflateSetDictionary_1=deflateSetDictionary,deflateInfo="pako deflate (from Nodeca project)",deflate_1$2={deflateInit:deflateInit_1,deflateInit2:deflateInit2_1,deflateReset:deflateReset_1,deflateResetKeep:deflateResetKeep_1,deflateSetHeader:deflateSetHeader_1,deflate:deflate_2$1,deflateEnd:deflateEnd_1,deflateSetDictionary:deflateSetDictionary_1,deflateInfo:deflateInfo},_has=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},assign=function(e){for(var t=Array.prototype.slice.call(arguments,1);t.length;){var n=t.shift();if(n){if("object"!==_typeof(n))throw new TypeError(n+"must be non-object");for(var r in n)_has(n,r)&&(e[r]=n[r])}}return e},flattenChunks=function(e){for(var t=0,n=0,r=e.length;n<r;n++)t+=e[n].length;for(var i=new Uint8Array(t),a=0,o=0,s=e.length;a<s;a++){var c=e[a];i.set(c,o),o+=c.length}return i},common={assign:assign,flattenChunks:flattenChunks},STR_APPLY_UIA_OK=!0;try{String.fromCharCode.apply(null,new Uint8Array(1))}catch(__){STR_APPLY_UIA_OK=!1}for(var _utf8len=new Uint8Array(256),q=0;q<256;q++)_utf8len[q]=252<=q?6:248<=q?5:240<=q?4:224<=q?3:192<=q?2:1;_utf8len[254]=_utf8len[254]=1;var string2buf=function(e){if("function"==typeof TextEncoder&&TextEncoder.prototype.encode)return(new TextEncoder).encode(e);for(var t,n,r,i,a=e.length,o=0,s=0;s<a;s++)55296==(64512&(n=e.charCodeAt(s)))&&s+1<a&&56320==(64512&(r=e.charCodeAt(s+1)))&&(n=65536+(n-55296<<10)+(r-56320),s++),o+=n<128?1:n<2048?2:n<65536?3:4;for(t=new Uint8Array(o),s=i=0;i<o;s++)55296==(64512&(n=e.charCodeAt(s)))&&s+1<a&&56320==(64512&(r=e.charCodeAt(s+1)))&&(n=65536+(n-55296<<10)+(r-56320),s++),n<128?t[i++]=n:(n<2048?t[i++]=192|n>>>6:(n<65536?t[i++]=224|n>>>12:(t[i++]=240|n>>>18,t[i++]=128|n>>>12&63),t[i++]=128|n>>>6&63),t[i++]=128|63&n);return t},buf2binstring=function(e,t){if(t<65534&&e.subarray&&STR_APPLY_UIA_OK)return String.fromCharCode.apply(null,e.length===t?e:e.subarray(0,t));for(var n="",r=0;r<t;r++)n+=String.fromCharCode(e[r]);return n},buf2string=function(e,t){var n=t||e.length;if("function"==typeof TextDecoder&&TextDecoder.prototype.decode)return(new TextDecoder).decode(e.subarray(0,t));for(var r=new Array(2*n),i=0,a=0;a<n;){var o=e[a++];if(o<128)r[i++]=o;else{var s=_utf8len[o];if(4<s)r[i++]=65533,a+=s-1;else{for(o&=2===s?31:3===s?15:7;1<s&&a<n;)o=o<<6|63&e[a++],s--;1<s?r[i++]=65533:o<65536?r[i++]=o:(o-=65536,r[i++]=55296|o>>10&1023,r[i++]=56320|1023&o)}}}return buf2binstring(r,i)},utf8border=function(e,t){for(var n=(t=(t=t||e.length)>e.length?e.length:t)-1;0<=n&&128==(192&e[n]);)n--;return!(n<0)&&0!==n&&n+_utf8len[e[n]]>t?n:t},strings={string2buf:string2buf,buf2string:buf2string,utf8border:utf8border};function ZStream(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0}var zstream=ZStream,toString$1=Object.prototype.toString,Z_NO_FLUSH$1=constants$2.Z_NO_FLUSH,Z_SYNC_FLUSH=constants$2.Z_SYNC_FLUSH,Z_FULL_FLUSH=constants$2.Z_FULL_FLUSH,Z_FINISH$2=constants$2.Z_FINISH,Z_OK$2=constants$2.Z_OK,Z_STREAM_END$2=constants$2.Z_STREAM_END,Z_DEFAULT_COMPRESSION=constants$2.Z_DEFAULT_COMPRESSION,Z_DEFAULT_STRATEGY=constants$2.Z_DEFAULT_STRATEGY,Z_DEFLATED$1=constants$2.Z_DEFLATED;function Deflate$1(e){this.options=common.assign({level:Z_DEFAULT_COMPRESSION,method:Z_DEFLATED$1,chunkSize:16384,windowBits:15,memLevel:8,strategy:Z_DEFAULT_STRATEGY},e||{});e=this.options;if(e.raw&&0<e.windowBits?e.windowBits=-e.windowBits:e.gzip&&0<e.windowBits&&e.windowBits<16&&(e.windowBits+=16),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new zstream,this.strm.avail_out=0,(t=deflate_1$2.deflateInit2(this.strm,e.level,e.method,e.windowBits,e.memLevel,e.strategy))!==Z_OK$2)throw new Error(messages[t]);if(e.header&&deflate_1$2.deflateSetHeader(this.strm,e.header),e.dictionary){var t,e="string"==typeof e.dictionary?strings.string2buf(e.dictionary):"[object ArrayBuffer]"===toString$1.call(e.dictionary)?new Uint8Array(e.dictionary):e.dictionary;if((t=deflate_1$2.deflateSetDictionary(this.strm,e))!==Z_OK$2)throw new Error(messages[t]);this._dict_set=!0}}function deflate$1(e,t){t=new Deflate$1(t);if(t.push(e,!0),t.err)throw t.msg||messages[t.err];return t.result}function deflateRaw$1(e,t){return(t=t||{}).raw=!0,deflate$1(e,t)}function gzip$1(e,t){return(t=t||{}).gzip=!0,deflate$1(e,t)}Deflate$1.prototype.push=function(e,t){var n,r,i=this.strm,a=this.options.chunkSize;if(this.ended)return!1;for(r=t===~~t?t:!0===t?Z_FINISH$2:Z_NO_FLUSH$1,"string"==typeof e?i.input=strings.string2buf(e):"[object ArrayBuffer]"===toString$1.call(e)?i.input=new Uint8Array(e):i.input=e,i.next_in=0,i.avail_in=i.input.length;;)if(0===i.avail_out&&(i.output=new Uint8Array(a),i.next_out=0,i.avail_out=a),(r===Z_SYNC_FLUSH||r===Z_FULL_FLUSH)&&i.avail_out<=6)this.onData(i.output.subarray(0,i.next_out)),i.avail_out=0;else{if(deflate_1$2.deflate(i,r)===Z_STREAM_END$2)return 0<i.next_out&&this.onData(i.output.subarray(0,i.next_out)),n=deflate_1$2.deflateEnd(this.strm),this.onEnd(n),this.ended=!0,n===Z_OK$2;if(0===i.avail_out)this.onData(i.output);else if(0<r&&0<i.next_out)this.onData(i.output.subarray(0,i.next_out)),i.avail_out=0;else if(0===i.avail_in)break}return!0},Deflate$1.prototype.onData=function(e){this.chunks.push(e)},Deflate$1.prototype.onEnd=function(e){e===Z_OK$2&&(this.result=common.flattenChunks(this.chunks)),this.chunks=[],this.err=e,this.msg=this.strm.msg};var Deflate_1$1=Deflate$1,deflate_2=deflate$1,deflateRaw_1$1=deflateRaw$1,gzip_1$1=gzip$1,constants$1=constants$2,deflate_1$1={Deflate:Deflate_1$1,deflate:deflate_2,deflateRaw:deflateRaw_1$1,gzip:gzip_1$1,constants:constants$1},BAD$1=16209,TYPE$1=16191,inffast=function(e,t){var n,r,i,a,o,s,c=e.state,l=e.next_in,d=e.input,u=l+(e.avail_in-5),f=e.next_out,p=e.output,h=f-(t-e.avail_out),m=f+(e.avail_out-257),g=c.dmax,_=c.wsize,y=c.whave,E=c.wnext,v=c.window,w=c.hold,T=c.bits,b=c.lencode,S=c.distcode,A=(1<<c.lenbits)-1,I=(1<<c.distbits)-1;e:do{for(T<15&&(w+=d[l++]<<T,T+=8,w+=d[l++]<<T,T+=8),n=b[w&A];;){if(w>>>=r=n>>>24,T-=r,0===(r=n>>>16&255))p[f++]=65535&n;else{if(!(16&r)){if(0==(64&r)){n=b[(65535&n)+(w&(1<<r)-1)];continue}if(32&r){c.mode=TYPE$1;break e}e.msg="invalid literal/length code",c.mode=BAD$1;break e}for(i=65535&n,(r&=15)&&(T<r&&(w+=d[l++]<<T,T+=8),i+=w&(1<<r)-1,w>>>=r,T-=r),T<15&&(w+=d[l++]<<T,T+=8,w+=d[l++]<<T,T+=8),n=S[w&I];;){if(w>>>=r=n>>>24,T-=r,!(16&(r=n>>>16&255))){if(0==(64&r)){n=S[(65535&n)+(w&(1<<r)-1)];continue}e.msg="invalid distance code",c.mode=BAD$1;break e}if(a=65535&n,T<(r&=15)&&(w+=d[l++]<<T,(T+=8)<r)&&(w+=d[l++]<<T,T+=8),g<(a+=w&(1<<r)-1)){e.msg="invalid distance too far back",c.mode=BAD$1;break e}if(w>>>=r,T-=r,(r=f-h)<a){if(y<(r=a-r)&&c.sane){e.msg="invalid distance too far back",c.mode=BAD$1;break e}if(s=v,(o=0)===E){if(o+=_-r,r<i){for(i-=r;p[f++]=v[o++],--r;);o=f-a,s=p}}else if(E<r){if(o+=_+E-r,(r-=E)<i){for(i-=r;p[f++]=v[o++],--r;);if(o=0,E<i){for(i-=r=E;p[f++]=v[o++],--r;);o=f-a,s=p}}}else if(o+=E-r,r<i){for(i-=r;p[f++]=v[o++],--r;);o=f-a,s=p}for(;2<i;)p[f++]=s[o++],p[f++]=s[o++],p[f++]=s[o++],i-=3;i&&(p[f++]=s[o++],1<i)&&(p[f++]=s[o++])}else{for(o=f-a;p[f++]=p[o++],p[f++]=p[o++],p[f++]=p[o++],2<(i-=3););i&&(p[f++]=p[o++],1<i)&&(p[f++]=p[o++])}break}}break}}while(l<u&&f<m);w&=(1<<(T-=(i=T>>3)<<3))-1,e.next_in=l-=i,e.next_out=f,e.avail_in=l<u?u-l+5:5-(l-u),e.avail_out=f<m?m-f+257:257-(f-m),c.hold=w,c.bits=T},MAXBITS=15,ENOUGH_LENS$1=852,ENOUGH_DISTS$1=592,CODES$1=0,LENS$1=1,DISTS$1=2,lbase=new Uint16Array([3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0]),lext=new Uint8Array([16,16,16,16,16,16,16,16,17,17,17,17,18,18,18,18,19,19,19,19,20,20,20,20,21,21,21,21,16,72,78]),dbase=new Uint16Array([1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,0,0]),dext=new Uint8Array([16,16,16,16,17,17,18,18,19,19,20,20,21,21,22,22,23,23,24,24,25,25,26,26,27,27,28,28,29,29,64,64]),inflate_table=function(e,t,n,r,i,a,o,s){for(var c,l,d,u,f,p,h,m,g,_=s.bits,y=0,E=0,v=0,w=0,T=0,b=0,S=0,A=0,I=0,D=0,R=null,O=new Uint16Array(MAXBITS+1),C=new Uint16Array(MAXBITS+1),N=null,y=0;y<=MAXBITS;y++)O[y]=0;for(E=0;E<r;E++)O[t[n+E]]++;for(T=_,w=MAXBITS;1<=w&&0===O[w];w--);if(w<T&&(T=w),0===w)i[a++]=20971520,i[a++]=20971520,s.bits=1;else{for(v=1;v<w&&0===O[v];v++);for(T<v&&(T=v),y=A=1;y<=MAXBITS;y++)if((A=(A<<=1)-O[y])<0)return-1;if(0<A&&(e===CODES$1||1!==w))return-1;for(C[1]=0,y=1;y<MAXBITS;y++)C[y+1]=C[y]+O[y];for(E=0;E<r;E++)0!==t[n+E]&&(o[C[t[n+E]]++]=E);if(p=e===CODES$1?(R=N=o,20):e===LENS$1?(R=lbase,N=lext,257):(R=dbase,N=dext,0),y=v,f=a,S=E=D=0,d=-1,u=(I=1<<(b=T))-1,e===LENS$1&&ENOUGH_LENS$1<I||e===DISTS$1&&ENOUGH_DISTS$1<I)return 1;for(;;){for(g=o[E]+1<p?(m=0,o[E]):o[E]>=p?(m=N[o[E]-p],R[o[E]-p]):(m=96,0),c=1<<(h=y-S),v=l=1<<b;i[f+(D>>S)+(l-=c)]=h<<24|m<<16|g|0,0!==l;);for(c=1<<y-1;D&c;)c>>=1;if(D=0!==c?(D&c-1)+c:0,E++,0==--O[y]){if(y===w)break;y=t[n+o[E]]}if(T<y&&(D&u)!==d){for(f+=v,A=1<<(b=y-(S=0===S?T:S));b+S<w&&!((A-=O[b+S])<=0);)b++,A<<=1;if(I+=1<<b,e===LENS$1&&ENOUGH_LENS$1<I||e===DISTS$1&&ENOUGH_DISTS$1<I)return 1;i[d=D&u]=T<<24|b<<16|f-a|0}}0!==D&&(i[f+D]=y-S<<24|64<<16|0),s.bits=T}return 0},inftrees=inflate_table,CODES=0,LENS=1,DISTS=2,Z_FINISH$1=constants$2.Z_FINISH,Z_BLOCK=constants$2.Z_BLOCK,Z_TREES=constants$2.Z_TREES,Z_OK$1=constants$2.Z_OK,Z_STREAM_END$1=constants$2.Z_STREAM_END,Z_NEED_DICT$1=constants$2.Z_NEED_DICT,Z_STREAM_ERROR$1=constants$2.Z_STREAM_ERROR,Z_DATA_ERROR$1=constants$2.Z_DATA_ERROR,Z_MEM_ERROR$1=constants$2.Z_MEM_ERROR,Z_BUF_ERROR=constants$2.Z_BUF_ERROR,Z_DEFLATED=constants$2.Z_DEFLATED,HEAD=16180,FLAGS=16181,TIME=16182,OS=16183,EXLEN=16184,EXTRA=16185,NAME=16186,COMMENT=16187,HCRC=16188,DICTID=16189,DICT=16190,TYPE=16191,TYPEDO=16192,STORED=16193,COPY_=16194,COPY=16195,TABLE=16196,LENLENS=16197,CODELENS=16198,LEN_=16199,LEN=16200,LENEXT=16201,DIST=16202,DISTEXT=16203,MATCH=16204,LIT=16205,CHECK=16206,LENGTH=16207,DONE=16208,BAD=16209,MEM=16210,SYNC=16211,ENOUGH_LENS=852,ENOUGH_DISTS=592,MAX_WBITS=15,DEF_WBITS=MAX_WBITS,zswap32=function(e){return(e>>>24&255)+(e>>>8&65280)+((65280&e)<<8)+((255&e)<<24)};function InflateState(){this.strm=null,this.mode=0,this.last=!1,this.wrap=0,this.havedict=!1,this.flags=0,this.dmax=0,this.check=0,this.total=0,this.head=null,this.wbits=0,this.wsize=0,this.whave=0,this.wnext=0,this.window=null,this.hold=0,this.bits=0,this.length=0,this.offset=0,this.extra=0,this.lencode=null,this.distcode=null,this.lenbits=0,this.distbits=0,this.ncode=0,this.nlen=0,this.ndist=0,this.have=0,this.next=null,this.lens=new Uint16Array(320),this.work=new Uint16Array(288),this.lendyn=null,this.distdyn=null,this.sane=0,this.back=0,this.was=0}var inflateStateCheck=function(e){var t;return!e||!(t=e.state)||t.strm!==e||t.mode<HEAD||t.mode>SYNC?1:0},inflateResetKeep=function(e){var t;return inflateStateCheck(e)?Z_STREAM_ERROR$1:(t=e.state,e.total_in=e.total_out=t.total=0,e.msg="",t.wrap&&(e.adler=1&t.wrap),t.mode=HEAD,t.last=0,t.havedict=0,t.flags=-1,t.dmax=32768,t.head=null,t.hold=0,t.bits=0,t.lencode=t.lendyn=new Int32Array(ENOUGH_LENS),t.distcode=t.distdyn=new Int32Array(ENOUGH_DISTS),t.sane=1,t.back=-1,Z_OK$1)},inflateReset=function(e){var t;return inflateStateCheck(e)?Z_STREAM_ERROR$1:((t=e.state).wsize=0,t.whave=0,t.wnext=0,inflateResetKeep(e))},inflateReset2=function(e,t){var n,r;return inflateStateCheck(e)||(r=e.state,t<0?(n=0,t=-t):(n=5+(t>>4),t<48&&(t&=15)),t&&(t<8||15<t))?Z_STREAM_ERROR$1:(null!==r.window&&r.wbits!==t&&(r.window=null),r.wrap=n,r.wbits=t,inflateReset(e))},inflateInit2=function(e,t){var n;return e?(n=new InflateState,(e.state=n).strm=e,n.window=null,n.mode=HEAD,(n=inflateReset2(e,t))!==Z_OK$1&&(e.state=null),n):Z_STREAM_ERROR$1},inflateInit=function(e){return inflateInit2(e,DEF_WBITS)},virgin=!0,lenfix,distfix,fixedtables=function(e){if(virgin){lenfix=new Int32Array(512),distfix=new Int32Array(32);for(var t=0;t<144;)e.lens[t++]=8;for(;t<256;)e.lens[t++]=9;for(;t<280;)e.lens[t++]=7;for(;t<288;)e.lens[t++]=8;for(inftrees(LENS,e.lens,0,288,lenfix,0,e.work,{bits:9}),t=0;t<32;)e.lens[t++]=5;inftrees(DISTS,e.lens,0,32,distfix,0,e.work,{bits:5}),virgin=!1}e.lencode=lenfix,e.lenbits=9,e.distcode=distfix,e.distbits=5},updatewindow=function(e,t,n,r){var i,e=e.state;return null===e.window&&(e.wsize=1<<e.wbits,e.wnext=0,e.whave=0,e.window=new Uint8Array(e.wsize)),r>=e.wsize?(e.window.set(t.subarray(n-e.wsize,n),0),e.wnext=0,e.whave=e.wsize):(r<(i=e.wsize-e.wnext)&&(i=r),e.window.set(t.subarray(n-r,n-r+i),e.wnext),(r-=i)?(e.window.set(t.subarray(n-r,n),0),e.wnext=r,e.whave=e.wsize):(e.wnext+=i,e.wnext===e.wsize&&(e.wnext=0),e.whave<e.wsize&&(e.whave+=i))),0},inflate$2=function(e,t){var n,r,i,a,o,s,c,l,d,u,f,p,h,m,g,_,y,E,v,w,T,b,S,A,I=0,D=new Uint8Array(4),R=new Uint8Array([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]);if(inflateStateCheck(e)||!e.output||!e.input&&0!==e.avail_in)return Z_STREAM_ERROR$1;(n=e.state).mode===TYPE&&(n.mode=TYPEDO),o=e.next_out,i=e.output,c=e.avail_out,a=e.next_in,r=e.input,s=e.avail_in,l=n.hold,d=n.bits,u=s,f=c,b=Z_OK$1;e:for(;;)switch(n.mode){case HEAD:if(0===n.wrap)n.mode=TYPEDO;else{for(;d<16;){if(0===s)break e;s--,l+=r[a++]<<d,d+=8}2&n.wrap&&35615===l?(0===n.wbits&&(n.wbits=15),D[n.check=0]=255&l,D[1]=l>>>8&255,n.check=crc32_1(n.check,D,2,0),d=l=0,n.mode=FLAGS):(n.head&&(n.head.done=!1),!(1&n.wrap)||(((255&l)<<8)+(l>>8))%31?(e.msg="incorrect header check",n.mode=BAD):(15&l)!==Z_DEFLATED?(e.msg="unknown compression method",n.mode=BAD):(d-=4,T=8+(15&(l>>>=4)),0===n.wbits&&(n.wbits=T),15<T||T>n.wbits?(e.msg="invalid window size",n.mode=BAD):(n.dmax=1<<n.wbits,n.flags=0,e.adler=n.check=1,n.mode=512&l?DICTID:TYPE,d=l=0)))}break;case FLAGS:for(;d<16;){if(0===s)break e;s--,l+=r[a++]<<d,d+=8}if(n.flags=l,(255&n.flags)!==Z_DEFLATED){e.msg="unknown compression method",n.mode=BAD;break}if(57344&n.flags){e.msg="unknown header flags set",n.mode=BAD;break}n.head&&(n.head.text=l>>8&1),512&n.flags&&4&n.wrap&&(D[0]=255&l,D[1]=l>>>8&255,n.check=crc32_1(n.check,D,2,0)),d=l=0,n.mode=TIME;case TIME:for(;d<32;){if(0===s)break e;s--,l+=r[a++]<<d,d+=8}n.head&&(n.head.time=l),512&n.flags&&4&n.wrap&&(D[0]=255&l,D[1]=l>>>8&255,D[2]=l>>>16&255,D[3]=l>>>24&255,n.check=crc32_1(n.check,D,4,0)),d=l=0,n.mode=OS;case OS:for(;d<16;){if(0===s)break e;s--,l+=r[a++]<<d,d+=8}n.head&&(n.head.xflags=255&l,n.head.os=l>>8),512&n.flags&&4&n.wrap&&(D[0]=255&l,D[1]=l>>>8&255,n.check=crc32_1(n.check,D,2,0)),d=l=0,n.mode=EXLEN;case EXLEN:if(1024&n.flags){for(;d<16;){if(0===s)break e;s--,l+=r[a++]<<d,d+=8}n.length=l,n.head&&(n.head.extra_len=l),512&n.flags&&4&n.wrap&&(D[0]=255&l,D[1]=l>>>8&255,n.check=crc32_1(n.check,D,2,0)),d=l=0}else n.head&&(n.head.extra=null);n.mode=EXTRA;case EXTRA:if(1024&n.flags&&((p=s<(p=n.length)?s:p)&&(n.head&&(T=n.head.extra_len-n.length,n.head.extra||(n.head.extra=new Uint8Array(n.head.extra_len)),n.head.extra.set(r.subarray(a,a+p),T)),512&n.flags&&4&n.wrap&&(n.check=crc32_1(n.check,r,p,a)),s-=p,a+=p,n.length-=p),n.length))break e;n.length=0,n.mode=NAME;case NAME:if(2048&n.flags){if(0===s)break e;for(p=0;T=r[a+p++],n.head&&T&&n.length<65536&&(n.head.name+=String.fromCharCode(T)),T&&p<s;);if(512&n.flags&&4&n.wrap&&(n.check=crc32_1(n.check,r,p,a)),s-=p,a+=p,T)break e}else n.head&&(n.head.name=null);n.length=0,n.mode=COMMENT;case COMMENT:if(4096&n.flags){if(0===s)break e;for(p=0;T=r[a+p++],n.head&&T&&n.length<65536&&(n.head.comment+=String.fromCharCode(T)),T&&p<s;);if(512&n.flags&&4&n.wrap&&(n.check=crc32_1(n.check,r,p,a)),s-=p,a+=p,T)break e}else n.head&&(n.head.comment=null);n.mode=HCRC;case HCRC:if(512&n.flags){for(;d<16;){if(0===s)break e;s--,l+=r[a++]<<d,d+=8}if(4&n.wrap&&l!==(65535&n.check)){e.msg="header crc mismatch",n.mode=BAD;break}d=l=0}n.head&&(n.head.hcrc=n.flags>>9&1,n.head.done=!0),e.adler=n.check=0,n.mode=TYPE;break;case DICTID:for(;d<32;){if(0===s)break e;s--,l+=r[a++]<<d,d+=8}e.adler=n.check=zswap32(l),d=l=0,n.mode=DICT;case DICT:if(0===n.havedict)return e.next_out=o,e.avail_out=c,e.next_in=a,e.avail_in=s,n.hold=l,n.bits=d,Z_NEED_DICT$1;e.adler=n.check=1,n.mode=TYPE;case TYPE:if(t===Z_BLOCK||t===Z_TREES)break e;case TYPEDO:if(n.last)l>>>=7&d,d-=7&d,n.mode=CHECK;else{for(;d<3;){if(0===s)break e;s--,l+=r[a++]<<d,d+=8}switch(n.last=1&l,--d,3&(l>>>=1)){case 0:n.mode=STORED;break;case 1:if(fixedtables(n),n.mode=LEN_,t!==Z_TREES)break;l>>>=2,d-=2;break e;case 2:n.mode=TABLE;break;case 3:e.msg="invalid block type",n.mode=BAD}l>>>=2,d-=2}break;case STORED:for(l>>>=7&d,d-=7&d;d<32;){if(0===s)break e;s--,l+=r[a++]<<d,d+=8}if((65535&l)!=(l>>>16^65535)){e.msg="invalid stored block lengths",n.mode=BAD;break}if(n.length=65535&l,d=l=0,n.mode=COPY_,t===Z_TREES)break e;case COPY_:n.mode=COPY;case COPY:if(p=n.length){if(0===(p=c<(p=s<p?s:p)?c:p))break e;i.set(r.subarray(a,a+p),o),s-=p,a+=p,c-=p,o+=p,n.length-=p}else n.mode=TYPE;break;case TABLE:for(;d<14;){if(0===s)break e;s--,l+=r[a++]<<d,d+=8}if(n.nlen=257+(31&l),l>>>=5,d-=5,n.ndist=1+(31&l),l>>>=5,d-=5,n.ncode=4+(15&l),l>>>=4,d-=4,286<n.nlen||30<n.ndist){e.msg="too many length or distance symbols",n.mode=BAD;break}n.have=0,n.mode=LENLENS;case LENLENS:for(;n.have<n.ncode;){for(;d<3;){if(0===s)break e;s--,l+=r[a++]<<d,d+=8}n.lens[R[n.have++]]=7&l,l>>>=3,d-=3}for(;n.have<19;)n.lens[R[n.have++]]=0;if(n.lencode=n.lendyn,n.lenbits=7,S={bits:n.lenbits},b=inftrees(CODES,n.lens,0,19,n.lencode,0,n.work,S),n.lenbits=S.bits,b){e.msg="invalid code lengths set",n.mode=BAD;break}n.have=0,n.mode=CODELENS;case CODELENS:for(;n.have<n.nlen+n.ndist;){for(;_=(I=n.lencode[l&(1<<n.lenbits)-1])>>>16&255,y=65535&I,!((g=I>>>24)<=d);){if(0===s)break e;s--,l+=r[a++]<<d,d+=8}if(y<16)l>>>=g,d-=g,n.lens[n.have++]=y;else{if(16===y){for(A=g+2;d<A;){if(0===s)break e;s--,l+=r[a++]<<d,d+=8}if(l>>>=g,d-=g,0===n.have){e.msg="invalid bit length repeat",n.mode=BAD;break}T=n.lens[n.have-1],p=3+(3&l),l>>>=2,d-=2}else if(17===y){for(A=g+3;d<A;){if(0===s)break e;s--,l+=r[a++]<<d,d+=8}T=0,p=3+(7&(l>>>=g)),l>>>=3,d=d-g-3}else{for(A=g+7;d<A;){if(0===s)break e;s--,l+=r[a++]<<d,d+=8}T=0,p=11+(127&(l>>>=g)),l>>>=7,d=d-g-7}if(n.have+p>n.nlen+n.ndist){e.msg="invalid bit length repeat",n.mode=BAD;break}for(;p--;)n.lens[n.have++]=T}}if(n.mode===BAD)break;if(0===n.lens[256]){e.msg="invalid code -- missing end-of-block",n.mode=BAD;break}if(n.lenbits=9,S={bits:n.lenbits},b=inftrees(LENS,n.lens,0,n.nlen,n.lencode,0,n.work,S),n.lenbits=S.bits,b){e.msg="invalid literal/lengths set",n.mode=BAD;break}if(n.distbits=6,n.distcode=n.distdyn,S={bits:n.distbits},b=inftrees(DISTS,n.lens,n.nlen,n.ndist,n.distcode,0,n.work,S),n.distbits=S.bits,b){e.msg="invalid distances set",n.mode=BAD;break}if(n.mode=LEN_,t===Z_TREES)break e;case LEN_:n.mode=LEN;case LEN:if(6<=s&&258<=c){e.next_out=o,e.avail_out=c,e.next_in=a,e.avail_in=s,n.hold=l,n.bits=d,inffast(e,f),o=e.next_out,i=e.output,c=e.avail_out,a=e.next_in,r=e.input,s=e.avail_in,l=n.hold,d=n.bits,n.mode===TYPE&&(n.back=-1);break}for(n.back=0;_=(I=n.lencode[l&(1<<n.lenbits)-1])>>>16&255,y=65535&I,!((g=I>>>24)<=d);){if(0===s)break e;s--,l+=r[a++]<<d,d+=8}if(_&&0==(240&_)){for(E=g,v=_,w=y;_=(I=n.lencode[w+((l&(1<<E+v)-1)>>E)])>>>16&255,y=65535&I,!(E+(g=I>>>24)<=d);){if(0===s)break e;s--,l+=r[a++]<<d,d+=8}l>>>=E,d-=E,n.back+=E}if(l>>>=g,d-=g,n.back+=g,n.length=y,0===_){n.mode=LIT;break}if(32&_){n.back=-1,n.mode=TYPE;break}if(64&_){e.msg="invalid literal/length code",n.mode=BAD;break}n.extra=15&_,n.mode=LENEXT;case LENEXT:if(n.extra){for(A=n.extra;d<A;){if(0===s)break e;s--,l+=r[a++]<<d,d+=8}n.length+=l&(1<<n.extra)-1,l>>>=n.extra,d-=n.extra,n.back+=n.extra}n.was=n.length,n.mode=DIST;case DIST:for(;_=(I=n.distcode[l&(1<<n.distbits)-1])>>>16&255,y=65535&I,!((g=I>>>24)<=d);){if(0===s)break e;s--,l+=r[a++]<<d,d+=8}if(0==(240&_)){for(E=g,v=_,w=y;_=(I=n.distcode[w+((l&(1<<E+v)-1)>>E)])>>>16&255,y=65535&I,!(E+(g=I>>>24)<=d);){if(0===s)break e;s--,l+=r[a++]<<d,d+=8}l>>>=E,d-=E,n.back+=E}if(l>>>=g,d-=g,n.back+=g,64&_){e.msg="invalid distance code",n.mode=BAD;break}n.offset=y,n.extra=15&_,n.mode=DISTEXT;case DISTEXT:if(n.extra){for(A=n.extra;d<A;){if(0===s)break e;s--,l+=r[a++]<<d,d+=8}n.offset+=l&(1<<n.extra)-1,l>>>=n.extra,d-=n.extra,n.back+=n.extra}if(n.offset>n.dmax){e.msg="invalid distance too far back",n.mode=BAD;break}n.mode=MATCH;case MATCH:if(0===c)break e;if(n.offset>(p=f-c)){if((p=n.offset-p)>n.whave&&n.sane){e.msg="invalid distance too far back",n.mode=BAD;break}h=p>n.wnext?(p-=n.wnext,n.wsize-p):n.wnext-p,p>n.length&&(p=n.length),m=n.window}else m=i,h=o-n.offset,p=n.length;for(c-=p=c<p?c:p,n.length-=p;i[o++]=m[h++],--p;);0===n.length&&(n.mode=LEN);break;case LIT:if(0===c)break e;i[o++]=n.length,c--,n.mode=LEN;break;case CHECK:if(n.wrap){for(;d<32;){if(0===s)break e;s--,l|=r[a++]<<d,d+=8}if(f-=c,e.total_out+=f,n.total+=f,4&n.wrap&&f&&(e.adler=n.check=(n.flags?crc32_1:adler32_1)(n.check,i,f,o-f)),f=c,4&n.wrap&&(n.flags?l:zswap32(l))!==n.check){e.msg="incorrect data check",n.mode=BAD;break}d=l=0}n.mode=LENGTH;case LENGTH:if(n.wrap&&n.flags){for(;d<32;){if(0===s)break e;s--,l+=r[a++]<<d,d+=8}if(4&n.wrap&&l!==(4294967295&n.total)){e.msg="incorrect length check",n.mode=BAD;break}d=l=0}n.mode=DONE;case DONE:b=Z_STREAM_END$1;break e;case BAD:b=Z_DATA_ERROR$1;break e;case MEM:return Z_MEM_ERROR$1;default:return Z_STREAM_ERROR$1}return e.next_out=o,e.avail_out=c,e.next_in=a,e.avail_in=s,n.hold=l,n.bits=d,(n.wsize||f!==e.avail_out&&n.mode<BAD&&(n.mode<CHECK||t!==Z_FINISH$1))&&updatewindow(e,e.output,e.next_out,f-e.avail_out),u-=e.avail_in,f-=e.avail_out,e.total_in+=u,e.total_out+=f,n.total+=f,4&n.wrap&&f&&(e.adler=n.check=(n.flags?crc32_1:adler32_1)(n.check,i,f,e.next_out-f)),e.data_type=n.bits+(n.last?64:0)+(n.mode===TYPE?128:0)+(n.mode===LEN_||n.mode===COPY_?256:0),b=(0==u&&0===f||t===Z_FINISH$1)&&b===Z_OK$1?Z_BUF_ERROR:b},inflateEnd=function(e){var t;return inflateStateCheck(e)?Z_STREAM_ERROR$1:((t=e.state).window&&(t.window=null),e.state=null,Z_OK$1)},inflateGetHeader=function(e,t){return inflateStateCheck(e)||0==(2&(e=e.state).wrap)?Z_STREAM_ERROR$1:((e.head=t).done=!1,Z_OK$1)},inflateSetDictionary=function(e,t){var n,r=t.length;return inflateStateCheck(e)||0!==(n=e.state).wrap&&n.mode!==DICT?Z_STREAM_ERROR$1:n.mode===DICT&&adler32_1(1,t,r,0)!==n.check?Z_DATA_ERROR$1:updatewindow(e,t,r,r)?(n.mode=MEM,Z_MEM_ERROR$1):(n.havedict=1,Z_OK$1)},inflateReset_1=inflateReset,inflateReset2_1=inflateReset2,inflateResetKeep_1=inflateResetKeep,inflateInit_1=inflateInit,inflateInit2_1=inflateInit2,inflate_2$1=inflate$2,inflateEnd_1=inflateEnd,inflateGetHeader_1=inflateGetHeader,inflateSetDictionary_1=inflateSetDictionary,inflateInfo="pako inflate (from Nodeca project)",inflate_1$2={inflateReset:inflateReset_1,inflateReset2:inflateReset2_1,inflateResetKeep:inflateResetKeep_1,inflateInit:inflateInit_1,inflateInit2:inflateInit2_1,inflate:inflate_2$1,inflateEnd:inflateEnd_1,inflateGetHeader:inflateGetHeader_1,inflateSetDictionary:inflateSetDictionary_1,inflateInfo:inflateInfo};function GZheader(){this.text=0,this.time=0,this.xflags=0,this.os=0,this.extra=null,this.extra_len=0,this.name="",this.comment="",this.hcrc=0,this.done=!1}var gzheader=GZheader,toString=Object.prototype.toString,Z_NO_FLUSH=constants$2.Z_NO_FLUSH,Z_FINISH=constants$2.Z_FINISH,Z_OK=constants$2.Z_OK,Z_STREAM_END=constants$2.Z_STREAM_END,Z_NEED_DICT=constants$2.Z_NEED_DICT,Z_STREAM_ERROR=constants$2.Z_STREAM_ERROR,Z_DATA_ERROR=constants$2.Z_DATA_ERROR,Z_MEM_ERROR=constants$2.Z_MEM_ERROR;function Inflate$1(e){this.options=common.assign({chunkSize:65536,windowBits:15,to:""},e||{});var t=this.options,e=(t.raw&&0<=t.windowBits&&t.windowBits<16&&(t.windowBits=-t.windowBits,0===t.windowBits)&&(t.windowBits=-15),!(0<=t.windowBits&&t.windowBits<16)||e&&e.windowBits||(t.windowBits+=32),15<t.windowBits&&t.windowBits<48&&0==(15&t.windowBits)&&(t.windowBits|=15),this.err=0,this.msg="",this.ended=!1,this.chunks=[],this.strm=new zstream,this.strm.avail_out=0,inflate_1$2.inflateInit2(this.strm,t.windowBits));if(e!==Z_OK)throw new Error(messages[e]);if(this.header=new gzheader,inflate_1$2.inflateGetHeader(this.strm,this.header),t.dictionary&&("string"==typeof t.dictionary?t.dictionary=strings.string2buf(t.dictionary):"[object ArrayBuffer]"===toString.call(t.dictionary)&&(t.dictionary=new Uint8Array(t.dictionary)),t.raw)&&(e=inflate_1$2.inflateSetDictionary(this.strm,t.dictionary))!==Z_OK)throw new Error(messages[e])}function inflate$1(e,t){t=new Inflate$1(t);if(t.push(e),t.err)throw t.msg||messages[t.err];return t.result}function inflateRaw$1(e,t){return(t=t||{}).raw=!0,inflate$1(e,t)}Inflate$1.prototype.push=function(e,t){var n,r,i=this.strm,a=this.options.chunkSize,o=this.options.dictionary;if(this.ended)return!1;for(r=t===~~t?t:!0===t?Z_FINISH:Z_NO_FLUSH,"[object ArrayBuffer]"===toString.call(e)?i.input=new Uint8Array(e):i.input=e,i.next_in=0,i.avail_in=i.input.length;;){for(0===i.avail_out&&(i.output=new Uint8Array(a),i.next_out=0,i.avail_out=a),(n=inflate_1$2.inflate(i,r))===Z_NEED_DICT&&o&&((n=inflate_1$2.inflateSetDictionary(i,o))===Z_OK?n=inflate_1$2.inflate(i,r):n===Z_DATA_ERROR&&(n=Z_NEED_DICT));0<i.avail_in&&n===Z_STREAM_END&&0<i.state.wrap&&0!==e[i.next_in];)inflate_1$2.inflateReset(i),n=inflate_1$2.inflate(i,r);switch(n){case Z_STREAM_ERROR:case Z_DATA_ERROR:case Z_NEED_DICT:case Z_MEM_ERROR:return this.onEnd(n),!(this.ended=!0)}var s,c,l,d=i.avail_out;if(!i.next_out||0!==i.avail_out&&n!==Z_STREAM_END||("string"===this.options.to?(s=strings.utf8border(i.output,i.next_out),c=i.next_out-s,l=strings.buf2string(i.output,s),i.next_out=c,i.avail_out=a-c,c&&i.output.set(i.output.subarray(s,s+c),0),this.onData(l)):this.onData(i.output.length===i.next_out?i.output:i.output.subarray(0,i.next_out))),n!==Z_OK||0!==d){if(n===Z_STREAM_END)return n=inflate_1$2.inflateEnd(this.strm),this.onEnd(n),this.ended=!0;if(0===i.avail_in)break}}return!0},Inflate$1.prototype.onData=function(e){this.chunks.push(e)},Inflate$1.prototype.onEnd=function(e){e===Z_OK&&("string"===this.options.to?this.result=this.chunks.join(""):this.result=common.flattenChunks(this.chunks)),this.chunks=[],this.err=e,this.msg=this.strm.msg};var Inflate_1$1=Inflate$1,inflate_2=inflate$1,inflateRaw_1$1=inflateRaw$1,ungzip$1=inflate$1,constants=constants$2,inflate_1$1={Inflate:Inflate_1$1,inflate:inflate_2,inflateRaw:inflateRaw_1$1,ungzip:ungzip$1,constants:constants},Deflate=deflate_1$1.Deflate,deflate=deflate_1$1.deflate,deflateRaw=deflate_1$1.deflateRaw,gzip=deflate_1$1.gzip,Inflate=inflate_1$1.Inflate,inflate=inflate_1$1.inflate,inflateRaw=inflate_1$1.inflateRaw,ungzip=inflate_1$1.ungzip,Deflate_1=Deflate,deflate_1=deflate,deflateRaw_1=deflateRaw,gzip_1=gzip,Inflate_1=Inflate,inflate_1=inflate,inflateRaw_1=inflateRaw,ungzip_1=ungzip,constants_1=constants$2,pako={Deflate:Deflate_1,deflate:deflate_1,deflateRaw:deflateRaw_1,gzip:gzip_1,Inflate:Inflate_1,inflate:inflate_1,inflateRaw:inflateRaw_1,ungzip:ungzip_1,constants:constants_1};function _createForOfIteratorHelper$1(e,t){var n,r,i,a,o="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(o)return r=!(n=!0),{s:function(){o=o.call(e)},n:function(){var e=o.next();return n=e.done,e},e:function(e){r=!0,i=e},f:function(){try{n||null==o.return||o.return()}finally{if(r)throw i}}};if(Array.isArray(e)||(o=_unsupportedIterableToArray$1(e))||t&&e&&"number"==typeof e.length)return o&&(e=o),a=0,{s:t=function(){},n:function(){return a>=e.length?{done:!0}:{done:!1,value:e[a++]}},e:function(e){throw e},f:t};throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray$1(e,t){var n;if(e)return"string"==typeof e?_arrayLikeToArray$1(e,t):"Map"===(n="Object"===(n=Object.prototype.toString.call(e).slice(8,-1))&&e.constructor?e.constructor.name:n)||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?_arrayLikeToArray$1(e,t):void 0}function _arrayLikeToArray$1(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var RecordEvents=[],RecordStart=0,Replayurl="",NodeType;function isElement(e){return e.nodeType===e.ELEMENT_NODE}function isShadowRoot(e){var t=null==e?void 0:e.host;return Boolean((null==t?void 0:t.shadowRoot)===e)}function isNativeShadowDom(e){return"[object ShadowRoot]"===Object.prototype.toString.call(e)}function fixBrowserCompatibilityIssuesInCSS(e){return e=e.includes(" background-clip: text;")&&!e.includes(" -webkit-background-clip: text;")?e.replace(" background-clip: text;"," -webkit-background-clip: text; background-clip: text;"):e}function getCssRulesString(e){try{var t=e.rules||e.cssRules;return t?fixBrowserCompatibilityIssuesInCSS(Array.from(t).map(getCssRuleString).join("")):null}catch(e){return null}}function getCssRuleString(e){var t=e.cssText;if(isCSSImportRule(e))try{t=getCssRulesString(e.styleSheet)||t}catch(e){}return t}function isCSSImportRule(e){return"styleSheet"in e}!function(e){e[e.Document=0]="Document",e[e.DocumentType=1]="DocumentType",e[e.Element=2]="Element",e[e.Text=3]="Text",e[e.CDATA=4]="CDATA",e[e.Comment=5]="Comment"}(NodeType=NodeType||{});var Mirror=function(){function e(){this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap}return e.prototype.getId=function(e){return e&&null!=(e=null==(e=this.getMeta(e))?void 0:e.id)?e:-1},e.prototype.getNode=function(e){return this.idNodeMap.get(e)||null},e.prototype.getIds=function(){return Array.from(this.idNodeMap.keys())},e.prototype.getMeta=function(e){return this.nodeMetaMap.get(e)||null},e.prototype.removeNodeFromMap=function(e){var t=this,n=this.getId(e);this.idNodeMap.delete(n),e.childNodes&&e.childNodes.forEach(function(e){return t.removeNodeFromMap(e)})},e.prototype.has=function(e){return this.idNodeMap.has(e)},e.prototype.hasNode=function(e){return this.nodeMetaMap.has(e)},e.prototype.add=function(e,t){var n=t.id;this.idNodeMap.set(n,e),this.nodeMetaMap.set(e,t)},e.prototype.replace=function(e,t){var n=this.getNode(e);n&&(n=this.nodeMetaMap.get(n))&&this.nodeMetaMap.set(t,n),this.idNodeMap.set(e,t)},e.prototype.reset=function(){this.idNodeMap=new Map,this.nodeMetaMap=new WeakMap},e}();function createMirror(){return new Mirror}function maskInputValue(e){var t=e.element,n=e.maskInputOptions,r=e.tagName,i=e.type,a=e.value,e=e.maskInputFn,a=a||"",i=i&&i.toLowerCase();return a=n[r.toLowerCase()]||i&&n[i]?e?e(a,t):"*".repeat(a.length):a}var ORIGINAL_ATTRIBUTE_NAME="__rrweb_original__";function is2DCanvasBlank(e){var t=e.getContext("2d");if(t)for(var n=0;n<e.width;n+=50)for(var r=0;r<e.height;r+=50){var i=t.getImageData,i=ORIGINAL_ATTRIBUTE_NAME in i?i[ORIGINAL_ATTRIBUTE_NAME]:i;if(new Uint32Array(i.call(t,n,r,Math.min(50,e.width-n),Math.min(50,e.height-r)).data.buffer).some(function(e){return 0!==e}))return}return 1}function getInputType(e){var t=e.type;return e.hasAttribute("data-rr-is-password")?"password":t?t.toLowerCase():null}var _id=1,tagNameRegex=new RegExp("[^a-z0-9-_:]"),IGNORED_NODE=-2,canvasService,canvasCtx;function genId(){return _id++}function getValidTagName(e){return e instanceof HTMLFormElement?"form":(e=e.tagName.toLowerCase().trim(),tagNameRegex.test(e)?"div":e)}function stringifyStyleSheet(e){return e.cssRules?Array.from(e.cssRules).map(function(e){return e.cssText||""}).join(""):""}function extractOrigin(e){return(-1<e.indexOf("//")?e.split("/").slice(0,3).join("/"):e.split("/")[0]).split("?")[0]}var URL_IN_CSS_REF=/url\((?:(')([^']*)'|(")(.*?)"|([^)]*))\)/gm,URL_PROTOCOL_MATCH=/^(?:[a-z+]+:)?\/\//i,URL_WWW_MATCH=/^www\..*/i,DATA_URI=/^(data:)([^,]*),(.*)/i;function absoluteToStylesheet(e,d){return(e||"").replace(URL_IN_CSS_REF,function(e,t,n,r,i,a){n=n||i||a,i=t||r||"";if(!n)return e;if(URL_PROTOCOL_MATCH.test(n)||URL_WWW_MATCH.test(n))return"url(".concat(i).concat(n).concat(i,")");if(DATA_URI.test(n))return"url(".concat(i).concat(n).concat(i,")");if("/"===n[0])return"url(".concat(i).concat(extractOrigin(d)+n).concat(i,")");var o=d.split("/"),a=n.split("/");o.pop();for(var s=0,c=a;s<c.length;s++){var l=c[s];"."!==l&&(".."===l?o.pop():o.push(l))}return"url(".concat(i).concat(o.join("/")).concat(i,")")})}var SRCSET_NOT_SPACES=/^[^ \t\n\r\u000c]+/,SRCSET_COMMAS_OR_SPACES=/^[, \t\n\r\u000c]+/;function getAbsoluteSrcsetString(e,t){if(""===t.trim())return t;var n=0;function r(e){var e=e.exec(t.substring(n));return e?(e=e[0],n+=e.length,e):""}for(var i=[];r(SRCSET_COMMAS_OR_SPACES),!(n>=t.length);)if(","===(o=r(SRCSET_NOT_SPACES)).slice(-1))o=absoluteToDoc(e,o.substring(0,o.length-1)),i.push(o);else for(var a="",o=absoluteToDoc(e,o),s=!1;;){var c=t.charAt(n);if(""===c){i.push((o+a).trim());break}if(s)")"===c&&(s=!1);else{if(","===c){n+=1,i.push((o+a).trim());break}"("===c&&(s=!0)}a+=c,n+=1}return i.join(", ")}function absoluteToDoc(e,t){return t&&""!==t.trim()?((e=e.createElement("a")).href=t,e.href):t}function isSVGElement(e){return Boolean("svg"===e.tagName||e.ownerSVGElement)}function getHref(){var e=document.createElement("a");return e.href="",e.href}function transformAttribute(e,t,n,r){return r&&("src"!==n&&("href"!==n||"use"===t&&"#"===r[0])&&("xlink:href"!==n||"#"===r[0])&&("background"!==n||"table"!==t&&"td"!==t&&"th"!==t)?"srcset"===n?getAbsoluteSrcsetString(e,r):"style"===n?absoluteToStylesheet(r,getHref()):"object"===t&&"data"===n?absoluteToDoc(e,r):r:absoluteToDoc(e,r))}function ignoreAttribute(e,t,n){return("video"===e||"audio"===e)&&"autoplay"===t}function _isBlockedElement(e,t,n){try{if("string"==typeof t){if(e.classList.contains(t))return!0}else for(var r=e.classList.length;r--;){var i=e.classList[r];if(t.test(i))return!0}if(n)return e.matches(n)}catch(e){}return!1}function classMatchesRegex(e,t,n){if(!e)return!1;if(e.nodeType!==e.ELEMENT_NODE)return!!n&&classMatchesRegex(e.parentNode,t,n);for(var r=e.classList.length;r--;){var i=e.classList[r];if(t.test(i))return!0}return!!n&&classMatchesRegex(e.parentNode,t,n)}function needMaskingText(e,t,n){try{var r=e.nodeType===e.ELEMENT_NODE?e:e.parentElement;if(null===r)return;if("string"==typeof t){if(r.classList.contains(t))return 1;if(r.closest(".".concat(t)))return 1}else if(classMatchesRegex(r,t,!0))return 1;if(n){if(r.matches(n))return 1;if(r.closest(n))return 1}}catch(e){}}function onceIframeLoaded(e,t,n){var r=e.contentWindow;if(r){var i,a,o=!1;try{a=r.document.readyState}catch(e){return}if("complete"===a)return r.location.href!==(a="about:blank")||e.src===a||""===e.src?(setTimeout(t,0),e.addEventListener("load",t)):void e.addEventListener("load",t);i=setTimeout(function(){o||(t(),o=!0)},n),e.addEventListener("load",function(){clearTimeout(i),o=!0,t()})}}function onceStylesheetLoaded(e,t,n){var r,i,a=!1;try{r=e.sheet}catch(e){return}r||(i=setTimeout(function(){a||(t(),a=!0)},n),e.addEventListener("load",function(){clearTimeout(i),a=!0,t()}))}function serializeNode(e,t){var n=t.doc,r=t.mirror,i=t.blockClass,a=t.blockSelector,o=t.maskTextClass,s=t.maskTextSelector,c=t.inlineStylesheet,l=t.maskInputOptions,d=void 0===l?{}:l,u=t.maskTextFn,f=t.maskInputFn,l=t.dataURLOptions,p=void 0===l?{}:l,h=t.inlineImages,m=t.recordCanvas,g=t.keepIframeSrcFn,l=t.newlyAddedElement,_=void 0!==l&&l,y=getRootId(n,r);switch(e.nodeType){case e.DOCUMENT_NODE:return"CSS1Compat"!==e.compatMode?{type:NodeType.Document,childNodes:[],compatMode:e.compatMode}:{type:NodeType.Document,childNodes:[]};case e.DOCUMENT_TYPE_NODE:return{type:NodeType.DocumentType,name:e.name,publicId:e.publicId,systemId:e.systemId,rootId:y};case e.ELEMENT_NODE:return serializeElementNode(e,{doc:n,blockClass:i,blockSelector:a,inlineStylesheet:c,maskInputOptions:d,maskInputFn:f,dataURLOptions:p,inlineImages:h,recordCanvas:m,keepIframeSrcFn:g,newlyAddedElement:_,rootId:y});case e.TEXT_NODE:return serializeTextNode(e,{maskTextClass:o,maskTextSelector:s,maskTextFn:u,rootId:y});case e.CDATA_SECTION_NODE:return{type:NodeType.CDATA,textContent:"",rootId:y};case e.COMMENT_NODE:return{type:NodeType.Comment,textContent:e.textContent||"",rootId:y};default:return!1}}function getRootId(e,t){return!t.hasNode(e)||1===(t=t.getId(e))?void 0:t}function serializeTextNode(e,t){var n,r=t.maskTextClass,i=t.maskTextSelector,a=t.maskTextFn,t=t.rootId,o=e.parentNode&&e.parentNode.tagName,s=e.textContent,c="STYLE"===o||void 0,o="SCRIPT"===o||void 0;if(c&&s){try{e.nextSibling||e.previousSibling||null!=(n=e.parentNode.sheet)&&n.cssRules&&(s=stringifyStyleSheet(e.parentNode.sheet))}catch(e){}s=absoluteToStylesheet(s,getHref())}return o&&(s="SCRIPT_PLACEHOLDER"),!c&&!o&&s&&needMaskingText(e,r,i)&&(s=a?a(s):s.replace(/[\S]/g,"*")),{type:NodeType.Text,textContent:s||"",isStyle:c,rootId:t}}function serializeElementNode(t,e){for(var n,r,i,a=e.doc,o=e.blockClass,s=e.blockSelector,c=e.inlineStylesheet,l=e.maskInputOptions,l=void 0===l?{}:l,d=e.maskInputFn,u=e.dataURLOptions,f=void 0===u?{}:u,u=e.inlineImages,p=e.recordCanvas,h=e.keepIframeSrcFn,m=e.newlyAddedElement,m=void 0!==m&&m,e=e.rootId,o=_isBlockedElement(t,o,s),g=getValidTagName(t),_={},y=t.attributes.length,E=0;E<y;E++){var v=t.attributes[E];ignoreAttribute(g,v.name,v.value)||(_[v.name]=transformAttribute(a,g,v.name,v.value))}return"link"===g&&c&&(n=null,n=(s=Array.from(a.styleSheets).find(function(e){return e.href===t.href}))?getCssRulesString(s):n)&&(delete _.rel,delete _.href,_._cssText=absoluteToStylesheet(n,s.href)),"style"===g&&t.sheet&&!(t.innerText||t.textContent||"").trim().length&&(n=getCssRulesString(t.sheet))&&(_._cssText=absoluteToStylesheet(n,getHref())),"input"!==g&&"textarea"!==g&&"select"!==g||(c=t.value,s=t.checked,"radio"!==_.type&&"checkbox"!==_.type&&"submit"!==_.type&&"button"!==_.type&&c?(n=getInputType(t),_.value=maskInputValue({element:t,type:n,tagName:g,value:c,maskInputOptions:l,maskInputFn:d})):s&&(_.checked=s)),"option"===g&&(t.selected&&!l.select?_.selected=!0:delete _.selected),"canvas"===g&&p&&("2d"===t.__context?is2DCanvasBlank(t)||(_.rr_dataURL=t.toDataURL(f.type,f.quality)):"__context"in t||(n=t.toDataURL(f.type,f.quality),(c=document.createElement("canvas")).width=t.width,c.height=t.height,n!==c.toDataURL(f.type,f.quality)&&(_.rr_dataURL=n))),"img"===g&&u&&(canvasService||(canvasService=a.createElement("canvas"),canvasCtx=canvasService.getContext("2d")),i=(r=t).crossOrigin,r.crossOrigin="anonymous",d=function e(){r.removeEventListener("load",e);try{canvasService.width=r.naturalWidth,canvasService.height=r.naturalHeight,canvasCtx.drawImage(r,0,0),_.rr_dataURL=canvasService.toDataURL(f.type,f.quality)}catch(e){}i?_.crossOrigin=i:r.removeAttribute("crossorigin")},r.complete&&0!==r.naturalWidth?d():r.addEventListener("load",d)),"audio"!==g&&"video"!==g||(_.rr_mediaState=t.paused?"paused":"played",_.rr_mediaCurrentTime=t.currentTime),m||(t.scrollLeft&&(_.rr_scrollLeft=t.scrollLeft),t.scrollTop&&(_.rr_scrollTop=t.scrollTop)),o&&(l=(s=t.getBoundingClientRect()).width,p=s.height,_={class:_.class,rr_width:"".concat(l,"px"),rr_height:"".concat(p,"px")}),"iframe"!==g||h(_.src)||(t.contentDocument||(_.rr_src=_.src),delete _.src),{type:NodeType.Element,tagName:g,attributes:_,childNodes:[],isSVG:isSVGElement(t)||void 0,needBlock:o,rootId:e}}function lowerIfExists(e){return null==e?"":e.toLowerCase()}function slimDOMExcluded(e,t){if(t.comment&&e.type===NodeType.Comment)return 1;if(e.type===NodeType.Element){if(t.script&&("script"===e.tagName||"link"===e.tagName&&("preload"===e.attributes.rel||"modulepreload"===e.attributes.rel)&&"script"===e.attributes.as||"link"===e.tagName&&"prefetch"===e.attributes.rel&&"string"==typeof e.attributes.href&&e.attributes.href.endsWith(".js")))return 1;if(t.headFavicon&&("link"===e.tagName&&"shortcut icon"===e.attributes.rel||"meta"===e.tagName&&(lowerIfExists(e.attributes.name).match(/^msapplication-tile(image|color)$/)||"application-name"===lowerIfExists(e.attributes.name)||"icon"===lowerIfExists(e.attributes.rel)||"apple-touch-icon"===lowerIfExists(e.attributes.rel)||"shortcut icon"===lowerIfExists(e.attributes.rel))))return 1;if("meta"===e.tagName){if(t.headMetaDescKeywords&&lowerIfExists(e.attributes.name).match(/^description|keywords$/))return 1;if(t.headMetaSocial&&(lowerIfExists(e.attributes.property).match(/^(og|twitter|fb):/)||lowerIfExists(e.attributes.name).match(/^(og|twitter):/)||"pinterest"===lowerIfExists(e.attributes.name)))return 1;if(t.headMetaRobots&&("robots"===lowerIfExists(e.attributes.name)||"googlebot"===lowerIfExists(e.attributes.name)||"bingbot"===lowerIfExists(e.attributes.name)))return 1;if(t.headMetaHttpEquiv&&void 0!==e.attributes["http-equiv"])return 1;if(t.headMetaAuthorship&&("author"===lowerIfExists(e.attributes.name)||"generator"===lowerIfExists(e.attributes.name)||"framework"===lowerIfExists(e.attributes.name)||"publisher"===lowerIfExists(e.attributes.name)||"progid"===lowerIfExists(e.attributes.name)||lowerIfExists(e.attributes.property).match(/^article:/)||lowerIfExists(e.attributes.property).match(/^product:/)))return 1;if(t.headMetaVerification&&("google-site-verification"===lowerIfExists(e.attributes.name)||"yandex-verification"===lowerIfExists(e.attributes.name)||"csrf-token"===lowerIfExists(e.attributes.name)||"p:domain_verify"===lowerIfExists(e.attributes.name)||"verify-v1"===lowerIfExists(e.attributes.name)||"verification"===lowerIfExists(e.attributes.name)||"shopify-checkout-api-token"===lowerIfExists(e.attributes.name)))return 1}}}function serializeNodeWithId(t,e){var n=e.doc,r=e.mirror,i=e.blockClass,a=e.blockSelector,o=e.maskTextClass,s=e.maskTextSelector,c=e.skipChild,c=void 0!==c&&c,l=e.inlineStylesheet,d=void 0===l||l,l=e.maskInputOptions,u=void 0===l?{}:l,f=e.maskTextFn,p=e.maskInputFn,h=e.slimDOMOptions,l=e.dataURLOptions,m=void 0===l?{}:l,l=e.inlineImages,g=void 0!==l&&l,l=e.recordCanvas,_=void 0!==l&&l,y=e.onSerialize,E=e.onIframeLoad,l=e.iframeLoadTimeout,v=void 0===l?5e3:l,w=e.onStylesheetLoad,l=e.stylesheetLoadTimeout,T=void 0===l?5e3:l,l=e.keepIframeSrcFn,b=void 0===l?function(){return!1}:l,l=e.newlyAddedElement,e=e.preserveWhiteSpace,S=void 0===e||e,e=serializeNode(t,{doc:n,mirror:r,blockClass:i,blockSelector:a,maskTextClass:o,maskTextSelector:s,inlineStylesheet:d,maskInputOptions:u,maskTextFn:f,maskInputFn:p,dataURLOptions:m,inlineImages:g,recordCanvas:_,keepIframeSrcFn:b,newlyAddedElement:void 0!==l&&l});if(!e)return null;var l=r.hasNode(t)?r.getId(t):slimDOMExcluded(e,h)||!S&&e.type===NodeType.Text&&!e.isStyle&&!e.textContent.replace(/^\s+|\s+$/gm,"").length?IGNORED_NODE:genId(),A=Object.assign(e,{id:l});if(r.add(t,A),l===IGNORED_NODE)return null;y&&y(t);e=!c;if(A.type===NodeType.Element&&(e=e&&!A.needBlock,delete A.needBlock,l=t.shadowRoot)&&isNativeShadowDom(l)&&(A.isShadowHost=!0),(A.type===NodeType.Document||A.type===NodeType.Element)&&e){h.headWhitespace&&A.type===NodeType.Element&&"head"===A.tagName&&(S=!1);for(var I={doc:n,mirror:r,blockClass:i,blockSelector:a,maskTextClass:o,maskTextSelector:s,skipChild:c,inlineStylesheet:d,maskInputOptions:u,maskTextFn:f,maskInputFn:p,slimDOMOptions:h,dataURLOptions:m,inlineImages:g,recordCanvas:_,preserveWhiteSpace:S,onSerialize:y,onIframeLoad:E,iframeLoadTimeout:v,onStylesheetLoad:w,stylesheetLoadTimeout:T,keepIframeSrcFn:b},D=0,R=Array.from(t.childNodes);D<R.length;D++)(O=serializeNodeWithId(R[D],I))&&A.childNodes.push(O);if(isElement(t)&&t.shadowRoot)for(var O,C=0,N=Array.from(t.shadowRoot.childNodes);C<N.length;C++)(O=serializeNodeWithId(N[C],I))&&(isNativeShadowDom(t.shadowRoot)&&(O.isShadow=!0),A.childNodes.push(O))}return t.parentNode&&isShadowRoot(t.parentNode)&&isNativeShadowDom(t.parentNode)&&(A.isShadow=!0),A.type===NodeType.Element&&"iframe"===A.tagName&&onceIframeLoaded(t,function(){var e=t.contentDocument;e&&E&&(e=serializeNodeWithId(e,{doc:e,mirror:r,blockClass:i,blockSelector:a,maskTextClass:o,maskTextSelector:s,skipChild:!1,inlineStylesheet:d,maskInputOptions:u,maskTextFn:f,maskInputFn:p,slimDOMOptions:h,dataURLOptions:m,inlineImages:g,recordCanvas:_,preserveWhiteSpace:S,onSerialize:y,onIframeLoad:E,iframeLoadTimeout:v,onStylesheetLoad:w,stylesheetLoadTimeout:T,keepIframeSrcFn:b}))&&E(t,e)},v),A.type===NodeType.Element&&"link"===A.tagName&&"stylesheet"===A.attributes.rel&&onceStylesheetLoaded(t,function(){var e;w&&(e=serializeNodeWithId(t,{doc:n,mirror:r,blockClass:i,blockSelector:a,maskTextClass:o,maskTextSelector:s,skipChild:!1,inlineStylesheet:d,maskInputOptions:u,maskTextFn:f,maskInputFn:p,slimDOMOptions:h,dataURLOptions:m,inlineImages:g,recordCanvas:_,preserveWhiteSpace:S,onSerialize:y,onIframeLoad:E,iframeLoadTimeout:v,onStylesheetLoad:w,stylesheetLoadTimeout:T,keepIframeSrcFn:b}))&&w(t,e)},T),A}function snapshot(e,t){var t=t||{},n=t.mirror,n=void 0===n?new Mirror:n,r=t.blockClass,i=t.blockSelector,a=t.maskTextClass,o=t.maskTextSelector,s=t.inlineStylesheet,c=t.inlineImages,l=t.recordCanvas,d=t.maskAllInputs,d=void 0!==d&&d,u=t.maskTextFn,f=t.maskInputFn,p=t.slimDOM,p=void 0!==p&&p,h=t.dataURLOptions,m=t.preserveWhiteSpace,g=t.onSerialize,_=t.onIframeLoad,y=t.iframeLoadTimeout,E=t.onStylesheetLoad,v=t.stylesheetLoadTimeout,t=t.keepIframeSrcFn;return serializeNodeWithId(e,{doc:e,mirror:n,blockClass:void 0===r?"rr-block":r,blockSelector:void 0===i?null:i,maskTextClass:void 0===a?"rr-mask":a,maskTextSelector:void 0===o?null:o,skipChild:!1,inlineStylesheet:void 0===s||s,maskInputOptions:!0===d?{color:!0,date:!0,"datetime-local":!0,email:!0,month:!0,number:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0,textarea:!0,select:!0,password:!0}:!1===d?{password:!0}:d,maskTextFn:u,maskInputFn:f,slimDOMOptions:!0===p||"all"===p?{script:!0,comment:!0,headFavicon:!0,headWhitespace:!0,headMetaDescKeywords:"all"===p,headMetaSocial:!0,headMetaRobots:!0,headMetaHttpEquiv:!0,headMetaAuthorship:!0,headMetaVerification:!0}:!1===p?{}:p,dataURLOptions:h,inlineImages:void 0!==c&&c,recordCanvas:void 0!==l&&l,preserveWhiteSpace:m,onSerialize:g,onIframeLoad:_,iframeLoadTimeout:y,onStylesheetLoad:E,stylesheetLoadTimeout:v,keepIframeSrcFn:void 0===t?function(){return!1}:t,newlyAddedElement:!1})}function on(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:document,r={capture:!0,passive:!0};return n.addEventListener(e,t,r),function(){return n.removeEventListener(e,t,r)}}var DEPARTED_MIRROR_ACCESS_WARNING="Please stop import mirror directly. Instead of that,\r\nnow you can use replayer.getMirror() to access the mirror instance of a replayer,\r\nor you can use record.mirror to access the mirror instance during recording.",_mirror={map:{},getId:function(){return-1},getNode:function(){return null},removeNodeFromMap:function(){},has:function(){return!1},reset:function(){}};function throttle(o,s){var c=2<arguments.length&&void 0!==arguments[2]?arguments[2]:{},l=null,d=0;return function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=Date.now(),i=(d||!1!==c.leading||(d=r),s-(r-d)),a=this;i<=0||s<i?(l&&(clearTimeout(l),l=null),d=r,o.apply(a,t)):l||!1===c.trailing||(l=setTimeout(function(){d=!1===c.leading?0:Date.now(),l=null,o.apply(a,t)},i))}}function hookSetter(e,t,n,r){var i=4<arguments.length&&void 0!==arguments[4]?arguments[4]:window,a=i.Object.getOwnPropertyDescriptor(e,t);return i.Object.defineProperty(e,t,r?n:{set:function(e){var t=this;setTimeout(function(){n.set.call(t,e)},0),a&&a.set&&a.set.call(this,e)}}),function(){return hookSetter(e,t,a||{},!0)}}function patch(e,t,n){try{var r,i;return t in e?("function"==typeof(i=n(r=e[t]))&&(i.prototype=i.prototype||{},Object.defineProperties(i,{__rrweb_original__:{enumerable:!1,value:r}})),e[t]=i,function(){e[t]=r}):function(){}}catch(e){return function(){}}}function getWindowScroll(e){var t,n=e.document;return{left:n.scrollingElement?n.scrollingElement.scrollLeft:void 0!==e.pageXOffset?e.pageXOffset:(null==n?void 0:n.documentElement.scrollLeft)||(null==(t=null==(t=null==n?void 0:n.body)?void 0:t.parentElement)?void 0:t.scrollLeft)||(null==(t=null==n?void 0:n.body)?void 0:t.scrollLeft)||0,top:n.scrollingElement?n.scrollingElement.scrollTop:void 0!==e.pageYOffset?e.pageYOffset:(null==n?void 0:n.documentElement.scrollTop)||(null==(e=null==(t=null==n?void 0:n.body)?void 0:t.parentElement)?void 0:e.scrollTop)||(null==(t=null==n?void 0:n.body)?void 0:t.scrollTop)||0}}function getWindowHeight(){return window.innerHeight||document.documentElement&&document.documentElement.clientHeight||document.body&&document.body.clientHeight}function getWindowWidth(){return window.innerWidth||document.documentElement&&document.documentElement.clientWidth||document.body&&document.body.clientWidth}function isBlocked(e,t,n,r){if(e){e=e.nodeType===e.ELEMENT_NODE?e:e.parentElement;if(e){try{if("string"==typeof t){if(e.classList.contains(t))return!0;if(r&&null!==e.closest("."+t))return!0}else if(classMatchesRegex(e,t,r))return!0}catch(e){}if(n){if(e.matches(n))return!0;if(r&&null!==e.closest(n))return!0}}}return!1}function isSerialized(e,t){return-1!==t.getId(e)}function isIgnored(e,t){return t.getId(e)===IGNORED_NODE}function isAncestorRemoved(e,t){var n;return!isShadowRoot(e)&&(n=t.getId(e),!t.has(n)||(!e.parentNode||e.parentNode.nodeType!==e.DOCUMENT_NODE)&&(!e.parentNode||isAncestorRemoved(e.parentNode,t)))}function legacy_isTouchEvent(e){return Boolean(e.changedTouches)}function polyfill(){var i=this,e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:window;"NodeList"in e&&!e.NodeList.prototype.forEach&&(e.NodeList.prototype.forEach=Array.prototype.forEach),"DOMTokenList"in e&&!e.DOMTokenList.prototype.forEach&&(e.DOMTokenList.prototype.forEach=Array.prototype.forEach),Node.prototype.contains||(Node.prototype.contains=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r=t[0];if(!(0 in t))throw new TypeError("1 argument is required");do{if(i===r)return!0}while(r=r&&r.parentNode);return!1})}function isSerializedIframe(e,t){return Boolean("IFRAME"===e.nodeName&&t.getMeta(e))}function isSerializedStylesheet(e,t){return Boolean("LINK"===e.nodeName&&e.nodeType===e.ELEMENT_NODE&&e.getAttribute&&"stylesheet"===e.getAttribute("rel")&&t.getMeta(e))}function hasShadowRoot(e){return Boolean(null==e?void 0:e.shadowRoot)}"undefined"!=typeof window&&window.Proxy&&window.Reflect&&(_mirror=new Proxy(_mirror,{get:function(e,t,n){return Reflect.get(e,t,n)}}));var StyleSheetMirror=function(){function e(){_classCallCheck(this,e),this.id=1,this.styleIDMap=new WeakMap,this.idStyleMap=new Map}return _createClass(e,[{key:"getId",value:function(e){return null!=(e=this.styleIDMap.get(e))?e:-1}},{key:"has",value:function(e){return this.styleIDMap.has(e)}},{key:"add",value:function(e,t){return this.has(e)?this.getId(e):(t=void 0===t?this.id++:t,this.styleIDMap.set(e,t),this.idStyleMap.set(t,e),t)}},{key:"getStyle",value:function(e){return this.idStyleMap.get(e)||null}},{key:"reset",value:function(){this.styleIDMap=new WeakMap,this.idStyleMap=new Map,this.id=1}},{key:"generateId",value:function(){return this.id++}}]),e}();function getShadowHost(e){var t,n=null;return n=(null==(t=null==(t=e.getRootNode)?void 0:t.call(e))?void 0:t.nodeType)===Node.DOCUMENT_FRAGMENT_NODE&&e.getRootNode().host?e.getRootNode().host:n}function getRootShadowHost(e){for(var t,n=e;t=getShadowHost(n);)n=t;return n}function shadowHostInDom(e){var t=e.ownerDocument;return!!t&&(e=getRootShadowHost(e),t.contains(e))}function inDom(e){var t=e.ownerDocument;return t&&(t.contains(e)||shadowHostInDom(e))}var EventType=function(e){return e[e.DomContentLoaded=0]="DomContentLoaded",e[e.Load=1]="Load",e[e.FullSnapshot=2]="FullSnapshot",e[e.IncrementalSnapshot=3]="IncrementalSnapshot",e[e.Meta=4]="Meta",e[e.Custom=5]="Custom",e[e.Plugin=6]="Plugin",e}(EventType||{}),IncrementalSource=function(e){return e[e.Mutation=0]="Mutation",e[e.MouseMove=1]="MouseMove",e[e.MouseInteraction=2]="MouseInteraction",e[e.Scroll=3]="Scroll",e[e.ViewportResize=4]="ViewportResize",e[e.Input=5]="Input",e[e.TouchMove=6]="TouchMove",e[e.MediaInteraction=7]="MediaInteraction",e[e.StyleSheetRule=8]="StyleSheetRule",e[e.CanvasMutation=9]="CanvasMutation",e[e.Font=10]="Font",e[e.Log=11]="Log",e[e.Drag=12]="Drag",e[e.StyleDeclaration=13]="StyleDeclaration",e[e.Selection=14]="Selection",e[e.AdoptedStyleSheet=15]="AdoptedStyleSheet",e}(IncrementalSource||{}),MouseInteractions=function(e){return e[e.MouseUp=0]="MouseUp",e[e.MouseDown=1]="MouseDown",e[e.Click=2]="Click",e[e.ContextMenu=3]="ContextMenu",e[e.DblClick=4]="DblClick",e[e.Focus=5]="Focus",e[e.Blur=6]="Blur",e[e.TouchStart=7]="TouchStart",e[e.TouchMove_Departed=8]="TouchMove_Departed",e[e.TouchEnd=9]="TouchEnd",e[e.TouchCancel=10]="TouchCancel",e}(MouseInteractions||{}),PointerTypes=function(e){return e[e.Mouse=0]="Mouse",e[e.Pen=1]="Pen",e[e.Touch=2]="Touch",e}(PointerTypes||{}),CanvasContext=function(e){return e[e["2D"]=0]="2D",e[e.WebGL=1]="WebGL",e[e.WebGL2=2]="WebGL2",e}(CanvasContext||{}),MediaInteractions=function(e){return e[e.Play=0]="Play",e[e.Pause=1]="Pause",e[e.Seeked=2]="Seeked",e[e.VolumeChange=3]="VolumeChange",e[e.RateChange=4]="RateChange",e}(MediaInteractions||{});function isNodeInLinkedList(e){return"__ln"in e}var DoubleLinkedList=function(){function e(){_classCallCheck(this,e),this.length=0,this.head=null}return _createClass(e,[{key:"get",value:function(e){if(e>=this.length)throw new Error("Position outside of list range");for(var t=this.head,n=0;n<e;n++)t=(null==t?void 0:t.next)||null;return t}},{key:"addNode",value:function(e){var t,n={value:e,previous:null,next:null};e.__ln=n,e.previousSibling&&isNodeInLinkedList(e.previousSibling)?(t=e.previousSibling.__ln.next,n.next=t,n.previous=e.previousSibling.__ln,e.previousSibling.__ln.next=n,t&&(t.previous=n)):e.nextSibling&&isNodeInLinkedList(e.nextSibling)&&e.nextSibling.__ln.previous?(t=e.nextSibling.__ln.previous,n.previous=t,n.next=e.nextSibling.__ln,e.nextSibling.__ln.previous=n,t&&(t.next=n)):(this.head&&(this.head.previous=n),n.next=this.head,this.head=n),this.length++}},{key:"removeNode",value:function(e){var t=e.__ln;this.head&&(t.previous?(t.previous.next=t.next,t.next&&(t.next.previous=t.previous)):(this.head=t.next,this.head&&(this.head.previous=null)),e.__ln&&delete e.__ln,this.length--)}}]),e}(),moveKey=function(e,t){return"".concat(e,"@").concat(t)},MutationBuffer=function(){function e(){var E=this;_classCallCheck(this,e),this.frozen=!1,this.locked=!1,this.texts=[],this.attributes=[],this.removes=[],this.mapRemoves=[],this.movedMap={},this.addedSet=new Set,this.movedSet=new Set,this.droppedSet=new Set,this.processMutations=function(e){e.forEach(E.processMutation),E.emit()},this.emit=function(){if(!E.frozen&&!E.locked){for(var i=[],a=new DoubleLinkedList,o=function(e){for(var t=e,n=IGNORED_NODE;n===IGNORED_NODE;)n=(t=t&&t.nextSibling)&&E.mirror.getId(t);return n},e=function(t){var e,n,r;if(t.parentNode&&inDom(t))return e=isShadowRoot(t.parentNode)?E.mirror.getId(getShadowHost(t)):E.mirror.getId(t.parentNode),n=o(t),-1===e||-1===n?a.addNode(t):void((r=serializeNodeWithId(t,{doc:E.doc,mirror:E.mirror,blockClass:E.blockClass,blockSelector:E.blockSelector,maskTextClass:E.maskTextClass,maskTextSelector:E.maskTextSelector,skipChild:!0,newlyAddedElement:!0,inlineStylesheet:E.inlineStylesheet,maskInputOptions:E.maskInputOptions,maskTextFn:E.maskTextFn,maskInputFn:E.maskInputFn,slimDOMOptions:E.slimDOMOptions,dataURLOptions:E.dataURLOptions,recordCanvas:E.recordCanvas,inlineImages:E.inlineImages,onSerialize:function(e){isSerializedIframe(e,E.mirror)&&E.iframeManager.addIframe(e),isSerializedStylesheet(e,E.mirror)&&E.stylesheetManager.trackLinkElement(e),hasShadowRoot(t)&&E.shadowDomManager.addShadowRoot(t.shadowRoot,E.doc)},onIframeLoad:function(e,t){E.iframeManager.attachIframe(e,t),E.shadowDomManager.observeAttachShadow(e)},onStylesheetLoad:function(e,t){E.stylesheetManager.attachLinkElement(e,t)}}))&&i.push({parentId:e,nextId:n,node:r}))};E.mapRemoves.length;)E.mirror.removeNodeFromMap(E.mapRemoves.shift());var t,n=_createForOfIteratorHelper$1(E.movedSet);try{for(n.s();!(t=n.n()).done;){var r=t.value;isParentRemoved(E.removes,r,E.mirror)&&!E.movedSet.has(r.parentNode)||e(r)}}catch(e){n.e(e)}finally{n.f()}var s,c=_createForOfIteratorHelper$1(E.addedSet);try{for(c.s();!(s=c.n()).done;){var l=s.value;!isAncestorInSet(E.droppedSet,l)&&!isParentRemoved(E.removes,l,E.mirror)||isAncestorInSet(E.movedSet,l)?e(l):E.droppedSet.add(l)}}catch(e){c.e(e)}finally{c.f()}for(var d=null;a.length;){var u,f,p=null;if(!(p=d&&(u=E.mirror.getId(d.value.parentNode),f=o(d.value),-1!==u)&&-1!==f?d:p))for(var h=a.length-1;0<=h;h--){var m=a.get(h);if(m){var g=E.mirror.getId(m.value.parentNode),_=o(m.value);if(-1!==_){if(-1!==g){p=m;break}_=m.value;if(_.parentNode&&_.parentNode.nodeType===Node.DOCUMENT_FRAGMENT_NODE){g=_.parentNode.host;if(-1!==E.mirror.getId(g)){p=m;break}}}}}if(!p){for(;a.head;)a.removeNode(a.head.value);break}d=p.previous,a.removeNode(p.value),e(p.value)}var y={texts:E.texts.map(function(e){return{id:E.mirror.getId(e.node),value:e.value}}).filter(function(e){return E.mirror.has(e.id)}),attributes:E.attributes.map(function(e){return{id:E.mirror.getId(e.node),attributes:e.attributes}}).filter(function(e){return E.mirror.has(e.id)}),removes:E.removes,adds:i};(y.texts.length||y.attributes.length||y.removes.length||y.adds.length)&&(E.texts=[],E.attributes=[],E.removes=[],E.addedSet=new Set,E.movedSet=new Set,E.droppedSet=new Set,E.movedMap={},E.mutationCb(y))}},this.processMutation=function(r){if(!isIgnored(r.target,E.mirror))switch(r.type){case"characterData":var e=r.target.textContent;isBlocked(r.target,E.blockClass,E.blockSelector,!1)||e===r.oldValue||E.texts.push({value:needMaskingText(r.target,E.maskTextClass,E.maskTextSelector)&&e?E.maskTextFn?E.maskTextFn(e):e.replace(/[\S]/g,"*"):e,node:r.target});break;case"attributes":var t=r.target,e=r.attributeName,n=r.target.getAttribute(e);if("value"===e&&(i=getInputType(t),n=maskInputValue({element:t,maskInputOptions:E.maskInputOptions,tagName:t.tagName,type:i,value:n,maskInputFn:E.maskInputFn})),isBlocked(r.target,E.blockClass,E.blockSelector,!1)||n===r.oldValue)return;var i=E.attributes.find(function(e){return e.node===r.target});if("IFRAME"===t.tagName&&"src"===e&&!E.keepIframeSrcFn(n)){if(t.contentDocument)return;e="rr_src"}if(i||(i={node:r.target,attributes:{}},E.attributes.push(i)),"type"===e&&"INPUT"===t.tagName&&"password"===(r.oldValue||"").toLowerCase()&&t.setAttribute("data-rr-is-password","true"),"style"===e){for(var a=E.doc.createElement("span"),o=(r.oldValue&&a.setAttribute("style",r.oldValue),void 0!==i.attributes.style&&null!==i.attributes.style||(i.attributes.style={}),i.attributes.style),s=0,c=Array.from(t.style);s<c.length;s++){var l=c[s],d=t.style.getPropertyValue(l),u=t.style.getPropertyPriority(l);d===a.style.getPropertyValue(l)&&u===a.style.getPropertyPriority(l)||(o[l]=""===u?d:[d,u])}for(var f=0,p=Array.from(a.style);f<p.length;f++){var h=p[f];""===t.style.getPropertyValue(h)&&(o[h]=!1)}}else ignoreAttribute(t.tagName,e)||(i.attributes[e]=transformAttribute(E.doc,t.tagName,e,n));break;case"childList":isBlocked(r.target,E.blockClass,E.blockSelector,!0)||(r.addedNodes.forEach(function(e){return E.genAdds(e,r.target)}),r.removedNodes.forEach(function(e){var t=E.mirror.getId(e),n=isShadowRoot(r.target)?E.mirror.getId(r.target.host):E.mirror.getId(r.target);isBlocked(r.target,E.blockClass,E.blockSelector,!1)||isIgnored(e,E.mirror)||!isSerialized(e,E.mirror)||(E.addedSet.has(e)?(deepDelete(E.addedSet,e),E.droppedSet.add(e)):E.addedSet.has(r.target)&&-1===t||isAncestorRemoved(r.target,E.mirror)||(E.movedSet.has(e)&&E.movedMap[moveKey(t,n)]?deepDelete(E.movedSet,e):E.removes.push({parentId:n,id:t,isShadow:!(!isShadowRoot(r.target)||!isNativeShadowDom(r.target))||void 0})),E.mapRemoves.push(e))}))}},this.genAdds=function(t,e){if(!E.processedNodeManager.inOtherBuffer(t,E)){if(E.mirror.hasNode(t)){if(isIgnored(t,E.mirror))return;E.movedSet.add(t);var n=null;(n=e&&E.mirror.hasNode(e)?E.mirror.getId(e):n)&&-1!==n&&(E.movedMap[moveKey(E.mirror.getId(t),n)]=!0)}else E.addedSet.add(t),E.droppedSet.delete(t);isBlocked(t,E.blockClass,E.blockSelector,!1)||(t.childNodes.forEach(function(e){return E.genAdds(e)}),hasShadowRoot(t)&&t.shadowRoot.childNodes.forEach(function(e){E.processedNodeManager.add(e,E),E.genAdds(e,t)}))}}}return _createClass(e,[{key:"init",value:function(t){var n=this;["mutationCb","blockClass","blockSelector","maskTextClass","maskTextSelector","inlineStylesheet","maskInputOptions","maskTextFn","maskInputFn","keepIframeSrcFn","recordCanvas","inlineImages","slimDOMOptions","dataURLOptions","doc","mirror","iframeManager","stylesheetManager","shadowDomManager","canvasManager","processedNodeManager"].forEach(function(e){n[e]=t[e]})}},{key:"freeze",value:function(){this.frozen=!0,this.canvasManager.freeze()}},{key:"unfreeze",value:function(){this.frozen=!1,this.canvasManager.unfreeze(),this.emit()}},{key:"isFrozen",value:function(){return this.frozen}},{key:"lock",value:function(){this.locked=!0,this.canvasManager.lock()}},{key:"unlock",value:function(){this.locked=!1,this.canvasManager.unlock(),this.emit()}},{key:"reset",value:function(){this.shadowDomManager.reset(),this.canvasManager.reset()}}]),e}(),errorHandler$1;function deepDelete(t,e){t.delete(e),e.childNodes.forEach(function(e){return deepDelete(t,e)})}function isParentRemoved(e,t,n){return 0!==e.length&&_isParentRemoved(e,t,n)}function _isParentRemoved(e,t,n){var r,t=t.parentNode;return!!t&&(r=n.getId(t),!!e.some(function(e){return e.id===r})||_isParentRemoved(e,t,n))}function isAncestorInSet(e,t){return 0!==e.size&&_isAncestorInSet(e,t)}function _isAncestorInSet(e,t){t=t.parentNode;return!!t&&(!!e.has(t)||_isAncestorInSet(e,t))}function registerErrorHandler(e){errorHandler$1=e}function unregisterErrorHandler(){errorHandler$1=void 0}var callbackWrapper=function(e){return errorHandler$1?function(){try{return e.apply(void 0,arguments)}catch(e){if(!errorHandler$1||!0!==errorHandler$1(e))throw e}}:e},__defProp$2=Object.defineProperty,__defProps$2=Object.defineProperties,__getOwnPropDescs$2=Object.getOwnPropertyDescriptors,__getOwnPropSymbols$3=Object.getOwnPropertySymbols,__hasOwnProp$3=Object.prototype.hasOwnProperty,__propIsEnum$3=Object.prototype.propertyIsEnumerable,__defNormalProp$2=function(e,t,n){return t in e?__defProp$2(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n},__spreadValues$2=function(e,t){for(var n in t=t||{})__hasOwnProp$3.call(t,n)&&__defNormalProp$2(e,n,t[n]);if(__getOwnPropSymbols$3){var r,i=_createForOfIteratorHelper$1(__getOwnPropSymbols$3(t));try{for(i.s();!(r=i.n()).done;){n=r.value;__propIsEnum$3.call(t,n)&&__defNormalProp$2(e,n,t[n])}}catch(e){i.e(e)}finally{i.f()}}return e},__spreadProps$2=function(e,t){return __defProps$2(e,__getOwnPropDescs$2(t))},mutationBuffers=[];function getEventTarget(t){try{if("composedPath"in t){var e=t.composedPath();if(e.length)return e[0]}else if("path"in t&&t.path.length)return t.path[0];return t.target}catch(e){return t.target}}function initMutationObserver(e,t){var n=new MutationBuffer,e=(mutationBuffers.push(n),n.init(e),window.MutationObserver||window.__rrMutationObserver),r=null==(r=null==(i=null==window?void 0:window.Zone)?void 0:i.__symbol__)?void 0:r.call(i,"MutationObserver"),i=new(e=r&&window[r]?window[r]:e)(callbackWrapper(n.processMutations.bind(n)));return i.observe(t,{attributes:!0,attributeOldValue:!0,characterData:!0,characterDataOldValue:!0,childList:!0,subtree:!0}),i}function initMoveObserver(e){var i,a,o,t,n=e.mousemoveCb,r=e.sampling,s=e.doc,c=e.mirror;return!1===r.mousemove?function(){}:(e="number"==typeof r.mousemove?r.mousemove:50,r="number"==typeof r.mousemoveCallback?r.mousemoveCallback:500,i=[],o=throttle(callbackWrapper(function(e){var t=Date.now()-a;n(i.map(function(e){return e.timeOffset-=t,e}),e),i=[],a=null}),r),r=callbackWrapper(throttle(callbackWrapper(function(e){var t=getEventTarget(e),n=legacy_isTouchEvent(e)?e.changedTouches[0]:e,r=n.clientX,n=n.clientY;a=a||Date.now(),i.push({x:r,y:n,id:c.getId(t),timeOffset:Date.now()-a}),o("undefined"!=typeof DragEvent&&e instanceof DragEvent?IncrementalSource.Drag:e instanceof MouseEvent?IncrementalSource.MouseMove:IncrementalSource.TouchMove)}),e,{trailing:!1})),t=[on("mousemove",r,s),on("touchmove",r,s),on("drag",r,s)],callbackWrapper(function(){t.forEach(function(e){return e()})}))}function initMouseInteractionObserver(e){var t,r,o,s=e.mouseInteractionCb,i=e.doc,c=e.mirror,l=e.blockClass,d=e.blockSelector,e=e.sampling;return!1===e.mouseInteraction?function(){}:(t=!0===e.mouseInteraction||void 0===e.mouseInteraction?{}:e.mouseInteraction,r=[],o=null,Object.keys(MouseInteractions).filter(function(e){return Number.isNaN(Number(e))&&!e.endsWith("_Departed")&&!1!==t[e]}).forEach(function(e){function t(e){var t=getEventTarget(e);if(!isBlocked(t,l,d,!0)){var n=null,r=a;if("pointerType"in e){switch(e.pointerType){case"mouse":n=PointerTypes.Mouse;break;case"touch":n=PointerTypes.Touch;break;case"pen":n=PointerTypes.Pen}n===PointerTypes.Touch?MouseInteractions[a]===MouseInteractions.MouseDown?r="TouchStart":MouseInteractions[a]===MouseInteractions.MouseUp&&(r="TouchEnd"):PointerTypes.Pen}else legacy_isTouchEvent(e)&&(n=PointerTypes.Touch);null!==n?(o=n,(r.startsWith("Touch")&&n===PointerTypes.Touch||r.startsWith("Mouse")&&n===PointerTypes.Mouse)&&(n=null)):MouseInteractions[a]===MouseInteractions.Click&&(n=o,o=null);var i,e=legacy_isTouchEvent(e)?e.changedTouches[0]:e;e&&(t=c.getId(t),i=e.clientX,e=e.clientY,callbackWrapper(s)(__spreadValues$2({type:MouseInteractions[r],id:t,x:i,y:e},null!==n&&{pointerType:n})))}}var a,n=e.toLowerCase();a=e;if(window.PointerEvent)switch(MouseInteractions[e]){case MouseInteractions.MouseDown:case MouseInteractions.MouseUp:n=n.replace("mouse","pointer");break;case MouseInteractions.TouchStart:case MouseInteractions.TouchEnd:return}r.push(on(n,t,i))}),callbackWrapper(function(){r.forEach(function(e){return e()})}))}function initScrollObserver(e){var r=e.scrollCb,i=e.doc,a=e.mirror,o=e.blockClass,s=e.blockSelector,e=e.sampling;return on("scroll",callbackWrapper(throttle(callbackWrapper(function(e){var t,n,e=getEventTarget(e);e&&!isBlocked(e,o,s,!0)&&(t=a.getId(e),e===i&&i.defaultView?(n=getWindowScroll(i.defaultView),r({id:t,x:n.left,y:n.top})):r({id:t,x:e.scrollLeft,y:e.scrollTop}))}),e.scroll||100)),i)}function initViewportResizeObserver(e){var n=e.viewportResizeCb,r=-1,i=-1;return on("resize",callbackWrapper(throttle(callbackWrapper(function(){var e=getWindowHeight(),t=getWindowWidth();r===e&&i===t||(n({width:Number(t),height:Number(e)}),r=e,i=t)}),200)),window)}function wrapEventWithUserTriggeredFlag(e,t){e=__spreadValues$2({},e);return t||delete e.userTriggered,e}var INPUT_TAGS=["INPUT","TEXTAREA","SELECT"],lastInputValueMap=void 0!==window.WeakMap?new WeakMap:null;function initInputObserver(e){var r=e.inputCb,o=e.doc,i=e.mirror,s=e.blockClass,c=e.blockSelector,l=e.ignoreClass,d=e.maskInputOptions,u=e.maskInputFn,t=e.sampling,f=e.userTriggeredOnInput;function n(e){var t,n,r,i=getEventTarget(e),e=e.isTrusted,a=i&&i.tagName;!(i=i&&"OPTION"===a?i.parentElement:i)||!a||INPUT_TAGS.indexOf(a)<0||isBlocked(i,s,c,!0)||i.classList.contains(l)||(t=i.value,n=!1,"radio"===(r=getInputType(i)||"")||"checkbox"===r?n=i.checked:(d[a.toLowerCase()]||d[r])&&(t=maskInputValue({element:i,maskInputOptions:d,tagName:a,type:r,value:t,maskInputFn:u})),p(i,callbackWrapper(wrapEventWithUserTriggeredFlag)({text:t,isChecked:n,userTriggered:e},f)),a=i.name,"radio"===r&&a&&n&&o.querySelectorAll('input[type="radio"][name="'.concat(a,'"]')).forEach(function(e){e!==i&&p(e,callbackWrapper(wrapEventWithUserTriggeredFlag)({text:e.value,isChecked:!n,userTriggered:!1},f))}))}function p(e,t){var n;null===lastInputValueMap||(n=lastInputValueMap.get(e))&&n.text===t.text&&n.isChecked===t.isChecked||(lastInputValueMap.set(e,t),n=i.getId(e),callbackWrapper(r)(__spreadProps$2(__spreadValues$2({},t),{id:n})))}var a=("last"===t.input?["change"]:["input","change"]).map(function(e){return on(e,callbackWrapper(n),o)}),h=o.defaultView;return h?(e=h.Object.getOwnPropertyDescriptor(h.HTMLInputElement.prototype,"value"),t=[[h.HTMLInputElement.prototype,"value"],[h.HTMLInputElement.prototype,"checked"],[h.HTMLSelectElement.prototype,"value"],[h.HTMLTextAreaElement.prototype,"value"],[h.HTMLSelectElement.prototype,"selectedIndex"],[h.HTMLOptionElement.prototype,"selected"]],e&&e.set&&a.push.apply(a,_toConsumableArray(t.map(function(e){return hookSetter(e[0],e[1],{set:function(){callbackWrapper(n)({target:this,isTrusted:!1})}},!1,h)}))),callbackWrapper(function(){a.forEach(function(e){return e()})})):function(){a.forEach(function(e){return e()})}}function getNestedCSSRulePositions(e){var t,n;return e=e,t=[],hasNestedCSSRule("CSSGroupingRule")&&e.parentRule instanceof CSSGroupingRule||hasNestedCSSRule("CSSMediaRule")&&e.parentRule instanceof CSSMediaRule||hasNestedCSSRule("CSSSupportsRule")&&e.parentRule instanceof CSSSupportsRule||hasNestedCSSRule("CSSConditionRule")&&e.parentRule instanceof CSSConditionRule?(n=Array.from(e.parentRule.cssRules).indexOf(e),t.unshift(n)):e.parentStyleSheet&&(n=Array.from(e.parentStyleSheet.cssRules).indexOf(e),t.unshift(n)),t}function getIdAndStyleId(e,t,n){var r,i;return e?(e.ownerNode?r=t.getId(e.ownerNode):i=n.getId(e),{styleId:i,id:r}):{}}function initStyleSheetObserver(e,t){var n,r,i,a,o,s,c=e.styleSheetRuleCb,l=e.mirror,d=e.stylesheetManager,u=t.win;return u.CSSStyleSheet&&u.CSSStyleSheet.prototype?(n=u.CSSStyleSheet.prototype.insertRule,u.CSSStyleSheet.prototype.insertRule=new Proxy(n,{apply:callbackWrapper(function(e,t,n){var r=_slicedToArray(n,2),i=r[0],r=r[1],a=getIdAndStyleId(t,l,d.styleMirror),o=a.id,a=a.styleId;return(o&&-1!==o||a&&-1!==a)&&c({id:o,styleId:a,adds:[{rule:i,index:r}]}),e.apply(t,n)})}),r=u.CSSStyleSheet.prototype.deleteRule,u.CSSStyleSheet.prototype.deleteRule=new Proxy(r,{apply:callbackWrapper(function(e,t,n){var r=_slicedToArray(n,1)[0],i=getIdAndStyleId(t,l,d.styleMirror),a=i.id,i=i.styleId;return(a&&-1!==a||i&&-1!==i)&&c({id:a,styleId:i,removes:[{index:r}]}),e.apply(t,n)})}),u.CSSStyleSheet.prototype.replace&&(i=u.CSSStyleSheet.prototype.replace,u.CSSStyleSheet.prototype.replace=new Proxy(i,{apply:callbackWrapper(function(e,t,n){var r=_slicedToArray(n,1)[0],i=getIdAndStyleId(t,l,d.styleMirror),a=i.id,i=i.styleId;return(a&&-1!==a||i&&-1!==i)&&c({id:a,styleId:i,replace:r}),e.apply(t,n)})})),u.CSSStyleSheet.prototype.replaceSync&&(a=u.CSSStyleSheet.prototype.replaceSync,u.CSSStyleSheet.prototype.replaceSync=new Proxy(a,{apply:callbackWrapper(function(e,t,n){var r=_slicedToArray(n,1)[0],i=getIdAndStyleId(t,l,d.styleMirror),a=i.id,i=i.styleId;return(a&&-1!==a||i&&-1!==i)&&c({id:a,styleId:i,replaceSync:r}),e.apply(t,n)})})),o={},canMonkeyPatchNestedCSSRule("CSSGroupingRule")?o.CSSGroupingRule=u.CSSGroupingRule:(canMonkeyPatchNestedCSSRule("CSSMediaRule")&&(o.CSSMediaRule=u.CSSMediaRule),canMonkeyPatchNestedCSSRule("CSSConditionRule")&&(o.CSSConditionRule=u.CSSConditionRule),canMonkeyPatchNestedCSSRule("CSSSupportsRule")&&(o.CSSSupportsRule=u.CSSSupportsRule)),s={},Object.entries(o).forEach(function(e){var e=_slicedToArray(e,2),t=e[0],e=e[1];s[t]={insertRule:e.prototype.insertRule,deleteRule:e.prototype.deleteRule},e.prototype.insertRule=new Proxy(s[t].insertRule,{apply:callbackWrapper(function(e,t,n){var r=_slicedToArray(n,2),i=r[0],r=r[1],a=getIdAndStyleId(t.parentStyleSheet,l,d.styleMirror),o=a.id,a=a.styleId;return(o&&-1!==o||a&&-1!==a)&&c({id:o,styleId:a,adds:[{rule:i,index:[].concat(_toConsumableArray(getNestedCSSRulePositions(t)),[r||0])}]}),e.apply(t,n)})}),e.prototype.deleteRule=new Proxy(s[t].deleteRule,{apply:callbackWrapper(function(e,t,n){var r=_slicedToArray(n,1)[0],i=getIdAndStyleId(t.parentStyleSheet,l,d.styleMirror),a=i.id,i=i.styleId;return(a&&-1!==a||i&&-1!==i)&&c({id:a,styleId:i,removes:[{index:[].concat(_toConsumableArray(getNestedCSSRulePositions(t)),[r])}]}),e.apply(t,n)})})}),callbackWrapper(function(){u.CSSStyleSheet.prototype.insertRule=n,u.CSSStyleSheet.prototype.deleteRule=r,i&&(u.CSSStyleSheet.prototype.replace=i),a&&(u.CSSStyleSheet.prototype.replaceSync=a),Object.entries(o).forEach(function(e){var e=_slicedToArray(e,2),t=e[0],e=e[1];e.prototype.insertRule=s[t].insertRule,e.prototype.deleteRule=s[t].deleteRule})})):function(){}}function initAdoptedStyleSheetObserver(e,t){var n=e.mirror,r=e.stylesheetManager,i=null,i="#document"===t.nodeName?n.getId(t):n.getId(t.host),n="#document"===t.nodeName?null==(e=t.defaultView)?void 0:e.Document:null==(e=null==(n=t.ownerDocument)?void 0:n.defaultView)?void 0:e.ShadowRoot,a=Object.getOwnPropertyDescriptor(null==n?void 0:n.prototype,"adoptedStyleSheets");return null!==i&&-1!==i&&n&&a?(Object.defineProperty(t,"adoptedStyleSheets",{configurable:a.configurable,enumerable:a.enumerable,get:function(){var e;return null==(e=a.get)?void 0:e.call(this)},set:function(e){var t=null==(t=a.set)?void 0:t.call(this,e);if(null!==i&&-1!==i)try{r.adoptStyleSheets(e,i)}catch(e){}return t}}),callbackWrapper(function(){Object.defineProperty(t,"adoptedStyleSheets",{configurable:a.configurable,enumerable:a.enumerable,get:a.get,set:a.set})})):function(){}}function initStyleDeclarationObserver(e,t){var c=e.styleDeclarationCb,l=e.mirror,d=e.ignoreCSSAttributes,u=e.stylesheetManager,n=t.win,f=n.CSSStyleDeclaration.prototype.setProperty,o=(n.CSSStyleDeclaration.prototype.setProperty=new Proxy(f,{apply:callbackWrapper(function(e,t,n){var r,i,a=_slicedToArray(n,3),o=a[0],s=a[1],a=a[2];return d.has(o)?f.apply(t,[o,s,a]):(r=(i=getIdAndStyleId(null==(i=t.parentRule)?void 0:i.parentStyleSheet,l,u.styleMirror)).id,i=i.styleId,(r&&-1!==r||i&&-1!==i)&&c({id:r,styleId:i,set:{property:o,value:s,priority:a},index:getNestedCSSRulePositions(t.parentRule)}),e.apply(t,n))})}),n.CSSStyleDeclaration.prototype.removeProperty);return n.CSSStyleDeclaration.prototype.removeProperty=new Proxy(o,{apply:callbackWrapper(function(e,t,n){var r,i,a=_slicedToArray(n,1)[0];return d.has(a)?o.apply(t,[a]):(r=(i=getIdAndStyleId(null==(i=t.parentRule)?void 0:i.parentStyleSheet,l,u.styleMirror)).id,i=i.styleId,(r&&-1!==r||i&&-1!==i)&&c({id:r,styleId:i,remove:{property:a},index:getNestedCSSRulePositions(t.parentRule)}),e.apply(t,n))})}),callbackWrapper(function(){n.CSSStyleDeclaration.prototype.setProperty=f,n.CSSStyleDeclaration.prototype.removeProperty=o})}function initMediaInteractionObserver(e){var i=e.mediaInteractionCb,a=e.blockClass,o=e.blockSelector,s=e.mirror,t=e.sampling,e=callbackWrapper(function(r){return throttle(callbackWrapper(function(e){var t,n,e=getEventTarget(e);e&&!isBlocked(e,a,o,!0)&&(t=e.currentTime,e.volume,e.muted,n=e.playbackRate,i({type:r,id:s.getId(e),currentTime:t,volume:0,muted:!0,playbackRate:n}))}),t.media||500)}),n=[on("play",e(MediaInteractions.Play)),on("pause",e(MediaInteractions.Pause)),on("seeked",e(MediaInteractions.Seeked)),on("volumechange",e(MediaInteractions.VolumeChange)),on("ratechange",e(MediaInteractions.RateChange))];return callbackWrapper(function(){n.forEach(function(e){return e()})})}function initFontObserver(e){var t,i,a,n=e.fontCb,e=e.doc,r=e.defaultView;return r?(t=[],i=new WeakMap,a=r.FontFace,r.FontFace=function(e,t,n){var r=new a(e,t,n);return i.set(r,{family:e,buffer:"string"!=typeof t,descriptors:n,fontSource:"string"==typeof t?t:JSON.stringify(Array.from(new Uint8Array(t)))}),r},e=patch(e.fonts,"add",function(e){return function(t){return setTimeout(callbackWrapper(function(){var e=i.get(t);e&&(n(e),i.delete(t))}),0),e.apply(this,[t])}}),t.push(function(){r.FontFace=a}),t.push(e),callbackWrapper(function(){t.forEach(function(e){return e()})})):function(){}}function initSelectionObserver(e){var c=e.doc,l=e.mirror,d=e.blockClass,u=e.blockSelector,f=e.selectionCb,p=!0,e=callbackWrapper(function(){var e=c.getSelection();if(!(!e||p&&null!=e&&e.isCollapsed)){p=e.isCollapsed||!1;for(var t=[],n=e.rangeCount||0,r=0;r<n;r++){var i=e.getRangeAt(r),a=i.startContainer,o=i.startOffset,s=i.endContainer,i=i.endOffset;isBlocked(a,d,u,!0)||isBlocked(s,d,u,!0)||t.push({start:l.getId(a),startOffset:o,end:l.getId(s),endOffset:i})}f({ranges:t})}});return e(),on("selectionchange",e)}function mergeHooks(e,t){var n=e.mutationCb,r=e.mousemoveCb,i=e.mouseInteractionCb,a=e.scrollCb,o=e.viewportResizeCb,s=e.inputCb,c=e.mediaInteractionCb,l=e.styleSheetRuleCb,d=e.styleDeclarationCb,u=e.canvasMutationCb,f=e.fontCb,p=e.selectionCb;e.mutationCb=function(){t.mutation&&t.mutation.apply(t,arguments),n.apply(void 0,arguments)},e.mousemoveCb=function(){t.mousemove&&t.mousemove.apply(t,arguments),r.apply(void 0,arguments)},e.mouseInteractionCb=function(){t.mouseInteraction&&t.mouseInteraction.apply(t,arguments),i.apply(void 0,arguments)},e.scrollCb=function(){t.scroll&&t.scroll.apply(t,arguments),a.apply(void 0,arguments)},e.viewportResizeCb=function(){t.viewportResize&&t.viewportResize.apply(t,arguments),o.apply(void 0,arguments)},e.inputCb=function(){t.input&&t.input.apply(t,arguments),s.apply(void 0,arguments)},e.mediaInteractionCb=function(){t.mediaInteaction&&t.mediaInteaction.apply(t,arguments),c.apply(void 0,arguments)},e.styleSheetRuleCb=function(){t.styleSheetRule&&t.styleSheetRule.apply(t,arguments),l.apply(void 0,arguments)},e.styleDeclarationCb=function(){t.styleDeclaration&&t.styleDeclaration.apply(t,arguments),d.apply(void 0,arguments)},e.canvasMutationCb=function(){t.canvasMutation&&t.canvasMutation.apply(t,arguments),u.apply(void 0,arguments)},e.fontCb=function(){t.font&&t.font.apply(t,arguments),f.apply(void 0,arguments)},e.selectionCb=function(){t.selection&&t.selection.apply(t,arguments),p.apply(void 0,arguments)}}function initObservers(e){var t=e.doc.defaultView;if(!t)return function(){};mergeHooks(e,1<arguments.length&&void 0!==arguments[1]?arguments[1]:{});var n,r=initMutationObserver(e,e.doc),i=initMoveObserver(e),a=initMouseInteractionObserver(e),o=initScrollObserver(e),s=initViewportResizeObserver(e),c=initInputObserver(e),l=initMediaInteractionObserver(e),d=initStyleSheetObserver(e,{win:t}),u=initAdoptedStyleSheetObserver(e,e.doc),f=initStyleDeclarationObserver(e,{win:t}),p=e.collectFonts?initFontObserver(e):function(){},h=initSelectionObserver(e),m=[],g=_createForOfIteratorHelper$1(e.plugins);try{for(g.s();!(n=g.n()).done;){var _=n.value;m.push(_.observer(_.callback,t,_.options))}}catch(e){g.e(e)}finally{g.f()}return callbackWrapper(function(){mutationBuffers.forEach(function(e){return e.reset()}),r.disconnect(),i(),a(),o(),s(),c(),l(),d(),u(),f(),p(),h(),m.forEach(function(e){return e()})})}function hasNestedCSSRule(e){return void 0!==window[e]}function canMonkeyPatchNestedCSSRule(e){return Boolean(void 0!==window[e]&&window[e].prototype&&"insertRule"in window[e].prototype&&"deleteRule"in window[e].prototype)}for(var CrossOriginIframeMirror=function(){function t(e){_classCallCheck(this,t),this.generateIdFn=e,this.iframeIdToRemoteIdMap=new WeakMap,this.iframeRemoteIdToIdMap=new WeakMap}return _createClass(t,[{key:"getId",value:function(e,t,n,r){n=n||this.getIdToRemoteIdMap(e),r=r||this.getRemoteIdToIdMap(e),e=n.get(t);return e||(e=this.generateIdFn(),n.set(t,e),r.set(e,t)),e}},{key:"getIds",value:function(t,e){var n=this,r=this.getIdToRemoteIdMap(t),i=this.getRemoteIdToIdMap(t);return e.map(function(e){return n.getId(t,e,r,i)})}},{key:"getRemoteId",value:function(e,t,n){n=n||this.getRemoteIdToIdMap(e);return"number"!=typeof t?t:n.get(t)||-1}},{key:"getRemoteIds",value:function(t,e){var n=this,r=this.getRemoteIdToIdMap(t);return e.map(function(e){return n.getRemoteId(t,e,r)})}},{key:"reset",value:function(e){e?(this.iframeIdToRemoteIdMap.delete(e),this.iframeRemoteIdToIdMap.delete(e)):(this.iframeIdToRemoteIdMap=new WeakMap,this.iframeRemoteIdToIdMap=new WeakMap)}},{key:"getIdToRemoteIdMap",value:function(e){var t=this.iframeIdToRemoteIdMap.get(e);return t||this.iframeIdToRemoteIdMap.set(e,t=new Map),t}},{key:"getRemoteIdToIdMap",value:function(e){var t=this.iframeRemoteIdToIdMap.get(e);return t||this.iframeRemoteIdToIdMap.set(e,t=new Map),t}}]),t}(),IframeManager=function(){function t(e){_classCallCheck(this,t),this.iframes=new WeakMap,this.crossOriginIframeMap=new WeakMap,this.crossOriginIframeMirror=new CrossOriginIframeMirror(genId),this.crossOriginIframeRootIdMap=new WeakMap,this.mutationCb=e.mutationCb,this.wrappedEmit=e.wrappedEmit,this.stylesheetManager=e.stylesheetManager,this.recordCrossOriginIframes=e.recordCrossOriginIframes,this.crossOriginIframeStyleMirror=new CrossOriginIframeMirror(this.stylesheetManager.styleMirror.generateId.bind(this.stylesheetManager.styleMirror)),this.mirror=e.mirror,this.recordCrossOriginIframes&&window.addEventListener("message",this.handleMessage.bind(this))}return _createClass(t,[{key:"addIframe",value:function(e){this.iframes.set(e,!0),e.contentWindow&&this.crossOriginIframeMap.set(e.contentWindow,e)}},{key:"addLoadListener",value:function(e){this.loadListener=e}},{key:"attachIframe",value:function(e,t){this.mutationCb({adds:[{parentId:this.mirror.getId(e),nextId:null,node:t}],removes:[],texts:[],attributes:[],isAttachIframe:!0}),null!=(t=this.loadListener)&&t.call(this,e),e.contentDocument&&e.contentDocument.adoptedStyleSheets&&0<e.contentDocument.adoptedStyleSheets.length&&this.stylesheetManager.adoptStyleSheets(e.contentDocument.adoptedStyleSheets,this.mirror.getId(e.contentDocument))}},{key:"handleMessage",value:function(e){var t=e;"rrweb"===t.data.type&&t.origin===t.data.origin&&e.source&&(e=(e=this.crossOriginIframeMap.get(e.source))&&this.transformCrossOriginEvent(e,t.data.event))&&this.wrappedEmit(e,t.data.isCheckout)}},{key:"transformCrossOriginEvent",value:function(n,e){var t,r=this;switch(e.type){case EventType.FullSnapshot:this.crossOriginIframeMirror.reset(n),this.crossOriginIframeStyleMirror.reset(n),this.replaceIdOnNode(e.data.node,n);var i=e.data.node.id;return this.crossOriginIframeRootIdMap.set(n,i),this.patchRootIdOnNode(e.data.node,i),{timestamp:e.timestamp,type:EventType.IncrementalSnapshot,data:{source:IncrementalSource.Mutation,adds:[{parentId:this.mirror.getId(n),nextId:null,node:e.data.node}],removes:[],texts:[],attributes:[],isAttachIframe:!0}};case EventType.Meta:case EventType.Load:case EventType.DomContentLoaded:return!1;case EventType.Plugin:return e;case EventType.Custom:return this.replaceIds(e.data.payload,n,["id","parentId","previousId","nextId"]),e;case EventType.IncrementalSnapshot:switch(e.data.source){case IncrementalSource.Mutation:return e.data.adds.forEach(function(e){r.replaceIds(e,n,["parentId","nextId","previousId"]),r.replaceIdOnNode(e.node,n);var t=r.crossOriginIframeRootIdMap.get(n);t&&r.patchRootIdOnNode(e.node,t)}),e.data.removes.forEach(function(e){r.replaceIds(e,n,["parentId","id"])}),e.data.attributes.forEach(function(e){r.replaceIds(e,n,["id"])}),e.data.texts.forEach(function(e){r.replaceIds(e,n,["id"])}),e;case IncrementalSource.Drag:case IncrementalSource.TouchMove:case IncrementalSource.MouseMove:return e.data.positions.forEach(function(e){r.replaceIds(e,n,["id"])}),e;case IncrementalSource.ViewportResize:return!1;case IncrementalSource.MediaInteraction:case IncrementalSource.MouseInteraction:case IncrementalSource.Scroll:case IncrementalSource.CanvasMutation:case IncrementalSource.Input:return this.replaceIds(e.data,n,["id"]),e;case IncrementalSource.StyleSheetRule:case IncrementalSource.StyleDeclaration:return this.replaceIds(e.data,n,["id"]),this.replaceStyleIds(e.data,n,["styleId"]),e;case IncrementalSource.Font:return e;case IncrementalSource.Selection:return e.data.ranges.forEach(function(e){r.replaceIds(e,n,["start","end"])}),e;case IncrementalSource.AdoptedStyleSheet:return this.replaceIds(e.data,n,["id"]),this.replaceStyleIds(e.data,n,["styleIds"]),null!=(t=e.data.styles)&&t.forEach(function(e){r.replaceStyleIds(e,n,["styleId"])}),e}}}},{key:"replace",value:function(e,t,n,r){var i,a=_createForOfIteratorHelper$1(r);try{for(a.s();!(i=a.n()).done;){var o=i.value;!Array.isArray(t[o])&&"number"!=typeof t[o]||(Array.isArray(t[o])?t[o]=e.getIds(n,t[o]):t[o]=e.getId(n,t[o]))}}catch(e){a.e(e)}finally{a.f()}return t}},{key:"replaceIds",value:function(e,t,n){return this.replace(this.crossOriginIframeMirror,e,t,n)}},{key:"replaceStyleIds",value:function(e,t,n){return this.replace(this.crossOriginIframeStyleMirror,e,t,n)}},{key:"replaceIdOnNode",value:function(e,t){var n=this;this.replaceIds(e,t,["id","rootId"]),"childNodes"in e&&e.childNodes.forEach(function(e){n.replaceIdOnNode(e,t)})}},{key:"patchRootIdOnNode",value:function(e,t){var n=this;e.type===NodeType.Document||e.rootId||(e.rootId=t),"childNodes"in e&&e.childNodes.forEach(function(e){n.patchRootIdOnNode(e,t)})}}]),t}(),__defProp$1=Object.defineProperty,__defProps$1=Object.defineProperties,__getOwnPropDescs$1=Object.getOwnPropertyDescriptors,__getOwnPropSymbols$2=Object.getOwnPropertySymbols,__hasOwnProp$2=Object.prototype.hasOwnProperty,__propIsEnum$2=Object.prototype.propertyIsEnumerable,__defNormalProp$1=function(e,t,n){return t in e?__defProp$1(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n},__spreadValues$1=function(e,t){for(var n in t=t||{})__hasOwnProp$2.call(t,n)&&__defNormalProp$1(e,n,t[n]);if(__getOwnPropSymbols$2){var r,i=_createForOfIteratorHelper$1(__getOwnPropSymbols$2(t));try{for(i.s();!(r=i.n()).done;){n=r.value;__propIsEnum$2.call(t,n)&&__defNormalProp$1(e,n,t[n])}}catch(e){i.e(e)}finally{i.f()}}return e},__spreadProps$1=function(e,t){return __defProps$1(e,__getOwnPropDescs$1(t))},ShadowDomManager=function(){function t(e){_classCallCheck(this,t),this.shadowDoms=new WeakSet,this.restoreHandlers=[],this.mutationCb=e.mutationCb,this.scrollCb=e.scrollCb,this.bypassOptions=e.bypassOptions,this.mirror=e.mirror,this.init()}return _createClass(t,[{key:"init",value:function(){this.reset(),this.patchAttachShadow(Element,document)}},{key:"addShadowRoot",value:function(e,t){var n,r=this;isNativeShadowDom(e)&&!this.shadowDoms.has(e)&&(this.shadowDoms.add(e),n=initMutationObserver(__spreadProps$1(__spreadValues$1({},this.bypassOptions),{doc:t,mutationCb:this.mutationCb,mirror:this.mirror,shadowDomManager:this}),e),this.restoreHandlers.push(function(){return n.disconnect()}),this.restoreHandlers.push(initScrollObserver(__spreadProps$1(__spreadValues$1({},this.bypassOptions),{scrollCb:this.scrollCb,doc:e,mirror:this.mirror}))),setTimeout(function(){e.adoptedStyleSheets&&0<e.adoptedStyleSheets.length&&r.bypassOptions.stylesheetManager.adoptStyleSheets(e.adoptedStyleSheets,r.mirror.getId(e.host)),r.restoreHandlers.push(initAdoptedStyleSheetObserver({mirror:r.mirror,stylesheetManager:r.bypassOptions.stylesheetManager},e))},0))}},{key:"observeAttachShadow",value:function(e){e.contentWindow&&e.contentDocument&&this.patchAttachShadow(e.contentWindow.Element,e.contentDocument)}},{key:"patchAttachShadow",value:function(e,n){var r=this;this.restoreHandlers.push(patch(e.prototype,"attachShadow",function(t){return function(e){e=t.call(this,e);return this.shadowRoot&&inDom(this)&&r.addShadowRoot(this.shadowRoot,n),e}}))}},{key:"reset",value:function(){this.restoreHandlers.forEach(function(e){try{e()}catch(e){}}),this.restoreHandlers=[],this.shadowDoms=new WeakSet}}]),t}(),chars="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",lookup="undefined"==typeof Uint8Array?[]:new Uint8Array(256),i=0;i<chars.length;i++)lookup[chars.charCodeAt(i)]=i;var encode=function(e){for(var t=new Uint8Array(e),n=t.length,r="",i=0;i<n;i+=3)r=(r=(r=(r+=chars[t[i]>>2])+chars[(3&t[i])<<4|t[i+1]>>4])+chars[(15&t[i+1])<<2|t[i+2]>>6])+chars[63&t[i+2]];return n%3==2?r=r.substring(0,r.length-1)+"=":n%3==1&&(r=r.substring(0,r.length-2)+"=="),r},canvasVarMap=new Map;function variableListFor(e,t){var n=canvasVarMap.get(e);return n||canvasVarMap.set(e,n=new Map),n.has(t)||n.set(t,[]),n.get(t)}var saveWebGLVar=function(e,t,n){if(e&&(isInstanceOfWebGLObject(e,t)||"object"===_typeof(e)))return-1===(n=(t=variableListFor(n,e.constructor.name)).indexOf(e))&&(n=t.length,t.push(e)),n};function serializeArg(e,t,n){return e instanceof Array?e.map(function(e){return serializeArg(e,t,n)}):null===e?e:e instanceof Float32Array||e instanceof Float64Array||e instanceof Int32Array||e instanceof Uint32Array||e instanceof Uint8Array||e instanceof Uint16Array||e instanceof Int16Array||e instanceof Int8Array||e instanceof Uint8ClampedArray?{rr_type:e.constructor.name,args:[Object.values(e)]}:e instanceof ArrayBuffer?{rr_type:e.constructor.name,base64:encode(e)}:e instanceof DataView?{rr_type:e.constructor.name,args:[serializeArg(e.buffer,t,n),e.byteOffset,e.byteLength]}:e instanceof HTMLImageElement?{rr_type:e.constructor.name,src:e.src}:e instanceof HTMLCanvasElement?{rr_type:"HTMLImageElement",src:e.toDataURL()}:e instanceof ImageData?{rr_type:e.constructor.name,args:[serializeArg(e.data,t,n),e.width,e.height]}:isInstanceOfWebGLObject(e,t)||"object"===_typeof(e)?{rr_type:e.constructor.name,index:saveWebGLVar(e,t,n)}:e}var serializeArgs=function(e,t,n){return _toConsumableArray(e).map(function(e){return serializeArg(e,t,n)})},isInstanceOfWebGLObject=function(t,n){var e=["WebGLActiveInfo","WebGLBuffer","WebGLFramebuffer","WebGLProgram","WebGLRenderbuffer","WebGLShader","WebGLShaderPrecisionFormat","WebGLTexture","WebGLUniformLocation","WebGLVertexArrayObject","WebGLVertexArrayObjectOES"].filter(function(e){return"function"==typeof n[e]});return Boolean(e.find(function(e){return t instanceof n[e]}))};function initCanvas2DMutationObserver(o,s,c,l){var e,n=[],t=_createForOfIteratorHelper$1(Object.getOwnPropertyNames(s.CanvasRenderingContext2D.prototype));try{for(t.s();!(e=t.n()).done;)!function(){var a=e.value;try{if("function"!=typeof s.CanvasRenderingContext2D.prototype[a])return;var t=patch(s.CanvasRenderingContext2D.prototype,a,function(i){return function(){for(var t=this,e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return isBlocked(this.canvas,c,l,!0)||setTimeout(function(){var e=serializeArgs([].concat(n),s,t);o(t.canvas,{type:CanvasContext["2D"],property:a,args:e})},0),i.apply(this,n)}});n.push(t)}catch(e){t=hookSetter(s.CanvasRenderingContext2D.prototype,a,{set:function(e){o(this.canvas,{type:CanvasContext["2D"],property:a,args:[e],setter:!0})}});n.push(t)}}()}catch(e){t.e(e)}finally{t.f()}return function(){n.forEach(function(e){return e()})}}function initCanvasContextObserver(e,a,o){var t=[];try{var n=patch(e.HTMLCanvasElement.prototype,"getContext",function(i){return function(e){isBlocked(this,a,o,!0)||"__context"in this||(this.__context=e);for(var t=arguments.length,n=new Array(1<t?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];return i.apply(this,[e].concat(n))}});t.push(n)}catch(e){}return function(){t.forEach(function(e){return e()})}}function patchGLPrototype(n,s,c,l,d,e,u){var r,i=[],t=_createForOfIteratorHelper$1(Object.getOwnPropertyNames(n));try{for(t.s();!(r=t.n()).done;)(function(){var o=r.value;if(["isContextLost","canvas","drawingBufferWidth","drawingBufferHeight"].includes(o))return;try{if("function"!=typeof n[o])return;var t=patch(n,o,function(a){return function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];var r,i=a.apply(this,t);return saveWebGLVar(i,u,this),isBlocked(this.canvas,l,d,!0)||(r=serializeArgs([].concat(t),u,this),c(this.canvas,{type:s,property:o,args:r})),i}});i.push(t)}catch(e){t=hookSetter(n,o,{set:function(e){c(this.canvas,{type:s,property:o,args:[e],setter:!0})}});i.push(t)}})()}catch(e){t.e(e)}finally{t.f()}return i}function initCanvasWebGLMutationObserver(e,t,n,r,i){var a=[];return a.push.apply(a,_toConsumableArray(patchGLPrototype(t.WebGLRenderingContext.prototype,CanvasContext.WebGL,e,n,r,i,t))),void 0!==t.WebGL2RenderingContext&&a.push.apply(a,_toConsumableArray(patchGLPrototype(t.WebGL2RenderingContext.prototype,CanvasContext.WebGL2,e,n,r,i,t))),function(){a.forEach(function(e){return e()})}}function decodeBase64(e,t){var n=atob(e);if(t){for(var r=new Uint8Array(n.length),i=0,a=n.length;i<a;++i)r[i]=n.charCodeAt(i);return String.fromCharCode.apply(null,new Uint16Array(r.buffer))}return n}function createURL(e,t,n){t=void 0===t?null:t,e=decodeBase64(e,void 0!==n&&n),n=e.indexOf("\n",10)+1,e=e.substring(n)+(t?"//# sourceMappingURL="+t:""),n=new Blob([e],{type:"application/javascript"});return URL.createObjectURL(n)}function createBase64WorkerFactory(t,n,r){var i;return function(e){return i=i||createURL(t,n,r),new Worker(i,e)}}var WorkerFactory=createBase64WorkerFactory("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",null,!1),__getOwnPropSymbols$1=Object.getOwnPropertySymbols,__hasOwnProp$1=Object.prototype.hasOwnProperty,__propIsEnum$1=Object.prototype.propertyIsEnumerable,__objRest=function(e,t){var n={};for(a in e)__hasOwnProp$1.call(e,a)&&t.indexOf(a)<0&&(n[a]=e[a]);if(null!=e&&__getOwnPropSymbols$1){var r,i=_createForOfIteratorHelper$1(__getOwnPropSymbols$1(e));try{for(i.s();!(r=i.n()).done;){var a=r.value;t.indexOf(a)<0&&__propIsEnum$1.call(e,a)&&(n[a]=e[a])}}catch(e){i.e(e)}finally{i.f()}}return n},__async=function(e,o,s){return new Promise(function(t,n){function r(e){e.done?t(e.value):Promise.resolve(e.value).then(i,a)}var i=function(e){try{r(s.next(e))}catch(e){n(e)}},a=function(e){try{r(s.throw(e))}catch(e){n(e)}};r((s=s.apply(e,o)).next())})},CanvasManager=function(){function c(e){var n=this,t=(_classCallCheck(this,c),this.pendingCanvasMutations=new Map,this.rafStamps={latestId:0,invokeId:null},this.frozen=!1,this.locked=!1,this.processMutation=function(e,t){(n.rafStamps.invokeId&&n.rafStamps.latestId!==n.rafStamps.invokeId||!n.rafStamps.invokeId)&&(n.rafStamps.invokeId=n.rafStamps.latestId),n.pendingCanvasMutations.has(e)||n.pendingCanvasMutations.set(e,[]),n.pendingCanvasMutations.get(e).push(t)},e.sampling),t=void 0===t?"all":t,r=e.win,i=e.blockClass,a=e.blockSelector,o=e.recordCanvas,s=e.dataURLOptions;this.mutationCb=e.mutationCb,this.mirror=e.mirror,o&&"all"===t&&this.initCanvasMutationObserver(r,i,a),o&&"number"==typeof t&&this.initCanvasFPSObserver(t,r,i,a,{dataURLOptions:s})}return _createClass(c,[{key:"reset",value:function(){this.pendingCanvasMutations.clear(),this.resetObservers&&this.resetObservers()}},{key:"freeze",value:function(){this.frozen=!0}},{key:"unfreeze",value:function(){this.frozen=!1}},{key:"lock",value:function(){this.locked=!0}},{key:"unlock",value:function(){this.locked=!1}},{key:"initCanvasFPSObserver",value:function(e,n,r,i,a){var o=this,t=initCanvasContextObserver(n,r,i),s=new Map,c=new WorkerFactory,l=(c.onmessage=function(e){var t,n,r,i=e.data.id;s.set(i,!1),"base64"in e.data&&(t=(e=e.data).base64,n=e.type,r=e.width,e=e.height,o.mutationCb({id:i,type:CanvasContext["2D"],commands:[{property:"clearRect",args:[0,0,r,e]},{property:"drawImage",args:[{rr_type:"ImageBitmap",args:[{rr_type:"Blob",data:[{rr_type:"ArrayBuffer",base64:t}],type:n}]},0,0]}]}))},1e3/e),d=0,u=function(){var t=[];return n.document.querySelectorAll("canvas").forEach(function(e){isBlocked(e,r,i,!0)||t.push(e)}),t},f=requestAnimationFrame(function e(t){f=(d&&t-d<l||(d=t,u().forEach(function(i){return __async(o,null,regenerator.mark(function e(){var t,n,r;return regenerator.wrap(function(e){for(;;)switch(e.prev=e.next){case 0:if(t=this.mirror.getId(i),s.get(t))return e.abrupt("return");e.next=3;break;case 3:return s.set(t,!0),["webgl","webgl2"].includes(i.__context)&&(n=i.getContext(i.__context),!1===(null==(r=null==n?void 0:n.getContextAttributes())?void 0:r.preserveDrawingBuffer))&&null!=n&&n.clear(n.COLOR_BUFFER_BIT),e.next=7,createImageBitmap(i);case 7:r=e.sent,c.postMessage({id:t,bitmap:r,width:i.width,height:i.height,dataURLOptions:a.dataURLOptions},[r]);case 9:case"end":return e.stop()}},e,this)}))})),requestAnimationFrame(e))});this.resetObservers=function(){t(),cancelAnimationFrame(f)}}},{key:"initCanvasMutationObserver",value:function(e,t,n){this.startRAFTimestamping(),this.startPendingCanvasMutationFlusher();var r=initCanvasContextObserver(e,t,n),i=initCanvas2DMutationObserver(this.processMutation.bind(this),e,t,n),a=initCanvasWebGLMutationObserver(this.processMutation.bind(this),e,t,n,this.mirror);this.resetObservers=function(){r(),i(),a()}}},{key:"startPendingCanvasMutationFlusher",value:function(){var e=this;requestAnimationFrame(function(){return e.flushPendingCanvasMutations()})}},{key:"startRAFTimestamping",value:function(){var n=this;requestAnimationFrame(function e(t){n.rafStamps.latestId=t,requestAnimationFrame(e)})}},{key:"flushPendingCanvasMutations",value:function(){var r=this;this.pendingCanvasMutations.forEach(function(e,t){var n=r.mirror.getId(t);r.flushPendingCanvasMutationFor(t,n)}),requestAnimationFrame(function(){return r.flushPendingCanvasMutations()})}},{key:"flushPendingCanvasMutationFor",value:function(e,t){var n,r;this.frozen||this.locked||(r=this.pendingCanvasMutations.get(e))&&-1!==t&&(n=r.map(function(e){return __objRest(e,["type"])}),r=r[0].type,this.mutationCb({id:t,type:r,commands:n}),this.pendingCanvasMutations.delete(e))}}]),c}(),StylesheetManager=function(){function t(e){_classCallCheck(this,t),this.trackedLinkElements=new WeakSet,this.styleMirror=new StyleSheetMirror,this.mutationCb=e.mutationCb,this.adoptedStyleSheetCb=e.adoptedStyleSheetCb}return _createClass(t,[{key:"attachLinkElement",value:function(e,t){"_cssText"in t.attributes&&this.mutationCb({adds:[],removes:[],texts:[],attributes:[{id:t.id,attributes:t.attributes}]}),this.trackLinkElement(e)}},{key:"trackLinkElement",value:function(e){this.trackedLinkElements.has(e)||(this.trackedLinkElements.add(e),this.trackStylesheetInLinkElement(e))}},{key:"adoptStyleSheets",value:function(e,t){if(0!==e.length){var n,r={id:t,styleIds:[]},i=[],a=_createForOfIteratorHelper$1(e);try{for(a.s();!(n=a.n()).done;){var o,s=n.value,c=void 0;this.styleMirror.has(s)?c=this.styleMirror.getId(s):(c=this.styleMirror.add(s),o=Array.from(s.rules||CSSRule),i.push({styleId:c,rules:o.map(function(e,t){return{rule:getCssRuleString(e),index:t}})})),r.styleIds.push(c)}}catch(e){a.e(e)}finally{a.f()}0<i.length&&(r.styles=i),this.adoptedStyleSheetCb(r)}}},{key:"reset",value:function(){this.styleMirror.reset(),this.trackedLinkElements=new WeakSet}},{key:"trackStylesheetInLinkElement",value:function(e){}}]),t}(),ProcessedNodeManager=function(){function e(){_classCallCheck(this,e),this.nodeMap=new WeakMap,this.loop=!0,this.periodicallyClear()}return _createClass(e,[{key:"periodicallyClear",value:function(){var e=this;requestAnimationFrame(function(){e.clear(),e.loop&&e.periodicallyClear()})}},{key:"inOtherBuffer",value:function(e,t){e=this.nodeMap.get(e);return e&&Array.from(e).some(function(e){return e!==t})}},{key:"add",value:function(e,t){this.nodeMap.set(e,(this.nodeMap.get(e)||new Set).add(t))}},{key:"clear",value:function(){this.nodeMap=new WeakMap}},{key:"destroy",value:function(){this.loop=!1}}]),e}(),__defProp=Object.defineProperty,__defProps=Object.defineProperties,__getOwnPropDescs=Object.getOwnPropertyDescriptors,__getOwnPropSymbols=Object.getOwnPropertySymbols,__hasOwnProp=Object.prototype.hasOwnProperty,__propIsEnum=Object.prototype.propertyIsEnumerable,__defNormalProp=function(e,t,n){return t in e?__defProp(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n},__spreadValues=function(e,t){for(var n in t=t||{})__hasOwnProp.call(t,n)&&__defNormalProp(e,n,t[n]);if(__getOwnPropSymbols){var r,i=_createForOfIteratorHelper$1(__getOwnPropSymbols(t));try{for(i.s();!(r=i.n()).done;){n=r.value;__propIsEnum.call(t,n)&&__defNormalProp(e,n,t[n])}}catch(e){i.e(e)}finally{i.f()}}return e},__spreadProps=function(e,t){return __defProps(e,__getOwnPropDescs(t))},wrappedEmit,takeFullSnapshot,canvasManager;function wrapEvent(e){return __spreadProps(__spreadValues({},e),{timestamp:Date.now()})}var recording=!1,mirror=createMirror();function record(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},r=e.emit,i=e.checkoutEveryNms,a=e.checkoutEveryNth,t=e.blockClass,n=void 0===t?"rr-block":t,t=e.blockSelector,o=void 0===t?null:t,t=e.ignoreClass,P=void 0===t?"rr-ignore":t,t=e.maskTextClass,s=void 0===t?"rr-mask":t,t=e.maskTextSelector,c=void 0===t?null:t,t=e.inlineStylesheet,l=void 0===t||t,t=e.maskAllInputs,d=e.maskInputOptions,u=e.slimDOMOptions,L=e.maskInputFn,f=e.maskTextFn,$=e.hooks,H=e.packFn,p=e.sampling,h=void 0===p?{}:p,p=e.dataURLOptions,m=void 0===p?{}:p,p=e.mousemoveWait,g=e.recordCanvas,_=void 0!==g&&g,g=e.recordCrossOriginIframes,g=void 0!==g&&g,y=e.recordAfter,B=void 0===y?"DOMContentLoaded"===e.recordAfter?e.recordAfter:"load":y,y=e.userTriggeredOnInput,U=void 0!==y&&y,y=e.collectFonts,F=void 0!==y&&y,y=e.inlineImages,E=void 0!==y&&y,v=e.plugins,y=e.keepIframeSrcFn,w=void 0===y?function(){return!1}:y,y=e.ignoreCSSAttributes,V=void 0===y?new Set([]):y,T=(registerErrorHandler(e.errorHandler),!g||window.parent===window),b=!1;if(!T)try{window.parent.document&&(b=!1)}catch(e){b=!0}if(T&&!r)throw new Error("emit function is required");void 0!==p&&void 0===h.mousemove&&(h.mousemove=p),mirror.reset();function S(e){wrappedEmit(wrapEvent({type:EventType.IncrementalSnapshot,data:__spreadValues({source:IncrementalSource.Mutation},e)}))}function Z(e){return wrappedEmit(wrapEvent({type:EventType.IncrementalSnapshot,data:__spreadValues({source:IncrementalSource.Scroll},e)}))}function W(e){return wrappedEmit(wrapEvent({type:EventType.IncrementalSnapshot,data:__spreadValues({source:IncrementalSource.CanvasMutation},e)}))}var z,G,A=!0===t?{color:!0,date:!0,"datetime-local":!0,email:!0,month:!0,number:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0,textarea:!0,select:!0,password:!0}:void 0!==d?d:{password:!0},I=!0===u||"all"===u?{script:!0,comment:!0,headFavicon:!0,headWhitespace:!0,headMetaSocial:!0,headMetaRobots:!0,headMetaHttpEquiv:!0,headMetaVerification:!0,headMetaAuthorship:"all"===u,headMetaDescKeywords:"all"===u}:u||{},D=(polyfill(),0),Y=function(e){var t,n=_createForOfIteratorHelper$1(v||[]);try{for(n.s();!(t=n.n()).done;){var r=t.value;r.eventProcessor&&(e=r.eventProcessor(e))}}catch(e){n.e(e)}finally{n.f()}return e=H&&!b?H(e):e},R=(wrappedEmit=function(e,t){var n;null==(n=mutationBuffers[0])||!n.isFrozen()||e.type===EventType.FullSnapshot||e.type===EventType.IncrementalSnapshot&&e.data.source===IncrementalSource.Mutation||mutationBuffers.forEach(function(e){return e.unfreeze()}),T?null!=r&&r(Y(e),t):b&&(n={type:"rrweb",event:Y(e),origin:window.location.origin,isCheckout:t},window.parent.postMessage(n,"*")),e.type===EventType.FullSnapshot?(z=e,D=0):e.type!==EventType.IncrementalSnapshot||e.data.source===IncrementalSource.Mutation&&e.data.isAttachIframe||(D++,t=a&&a<=D,n=i&&e.timestamp-z.timestamp>i,(t||n)&&takeFullSnapshot(!0))},new StylesheetManager({mutationCb:S,adoptedStyleSheetCb:function(e){return wrappedEmit(wrapEvent({type:EventType.IncrementalSnapshot,data:__spreadValues({source:IncrementalSource.AdoptedStyleSheet},e)}))}})),O=new IframeManager({mirror:mirror,mutationCb:S,stylesheetManager:R,recordCrossOriginIframes:g,wrappedEmit:wrappedEmit}),C=_createForOfIteratorHelper$1(v||[]);try{for(C.s();!(G=C.n()).done;){var j=G.value;j.getMirror&&j.getMirror({nodeMirror:mirror,crossOriginIframeMirror:O.crossOriginIframeMirror,crossOriginIframeStyleMirror:O.crossOriginIframeStyleMirror})}}catch(e){C.e(e)}finally{C.f()}var N=new ProcessedNodeManager,k=(canvasManager=new CanvasManager({recordCanvas:_,mutationCb:W,win:window,blockClass:n,blockSelector:o,mirror:mirror,sampling:h.canvas,dataURLOptions:m}),new ShadowDomManager({mutationCb:S,scrollCb:Z,bypassOptions:{blockClass:n,blockSelector:o,maskTextClass:s,maskTextSelector:c,inlineStylesheet:l,maskInputOptions:A,dataURLOptions:m,maskTextFn:f,maskInputFn:L,recordCanvas:_,inlineImages:E,sampling:h,slimDOMOptions:I,iframeManager:O,stylesheetManager:R,canvasManager:canvasManager,keepIframeSrcFn:w,processedNodeManager:N},mirror:mirror}));takeFullSnapshot=function(){var e=0<arguments.length&&void 0!==arguments[0]&&arguments[0],t=(wrappedEmit(wrapEvent({type:EventType.Meta,data:{href:window.location.href,width:getWindowWidth(),height:getWindowHeight()}}),e),R.reset(),k.init(),mutationBuffers.forEach(function(e){return e.lock()}),snapshot(document,{mirror:mirror,blockClass:n,blockSelector:o,maskTextClass:s,maskTextSelector:c,inlineStylesheet:l,maskAllInputs:A,maskTextFn:f,slimDOM:I,dataURLOptions:m,recordCanvas:_,inlineImages:E,onSerialize:function(e){isSerializedIframe(e,mirror)&&O.addIframe(e),isSerializedStylesheet(e,mirror)&&R.trackLinkElement(e),hasShadowRoot(e)&&k.addShadowRoot(e.shadowRoot,document)},onIframeLoad:function(e,t){O.attachIframe(e,t),k.observeAttachShadow(e)},onStylesheetLoad:function(e,t){R.attachLinkElement(e,t)},keepIframeSrcFn:w}));t&&(wrappedEmit(wrapEvent({type:EventType.FullSnapshot,data:{node:t,initialOffset:getWindowScroll(window)}}),e),mutationBuffers.forEach(function(e){return e.unlock()}),document.adoptedStyleSheets)&&0<document.adoptedStyleSheets.length&&R.adoptStyleSheets(document.adoptedStyleSheets,mirror.getId(document))};try{var x=[],K=function(e){return callbackWrapper(initObservers)({mutationCb:S,mousemoveCb:function(e,t){return wrappedEmit(wrapEvent({type:EventType.IncrementalSnapshot,data:{source:t,positions:e}}))},mouseInteractionCb:function(e){return wrappedEmit(wrapEvent({type:EventType.IncrementalSnapshot,data:__spreadValues({source:IncrementalSource.MouseInteraction},e)}))},scrollCb:Z,viewportResizeCb:function(e){return wrappedEmit(wrapEvent({type:EventType.IncrementalSnapshot,data:__spreadValues({source:IncrementalSource.ViewportResize},e)}))},inputCb:function(e){return wrappedEmit(wrapEvent({type:EventType.IncrementalSnapshot,data:__spreadValues({source:IncrementalSource.Input},e)}))},mediaInteractionCb:function(e){return wrappedEmit(wrapEvent({type:EventType.IncrementalSnapshot,data:__spreadValues({source:IncrementalSource.MediaInteraction},e)}))},styleSheetRuleCb:function(e){return wrappedEmit(wrapEvent({type:EventType.IncrementalSnapshot,data:__spreadValues({source:IncrementalSource.StyleSheetRule},e)}))},styleDeclarationCb:function(e){return wrappedEmit(wrapEvent({type:EventType.IncrementalSnapshot,data:__spreadValues({source:IncrementalSource.StyleDeclaration},e)}))},canvasMutationCb:W,fontCb:function(e){return wrappedEmit(wrapEvent({type:EventType.IncrementalSnapshot,data:__spreadValues({source:IncrementalSource.Font},e)}))},selectionCb:function(e){wrappedEmit(wrapEvent({type:EventType.IncrementalSnapshot,data:__spreadValues({source:IncrementalSource.Selection},e)}))},blockClass:n,ignoreClass:P,maskTextClass:s,maskTextSelector:c,maskInputOptions:A,inlineStylesheet:l,sampling:h,recordCanvas:_,inlineImages:E,userTriggeredOnInput:U,collectFonts:F,doc:e,maskInputFn:L,maskTextFn:f,keepIframeSrcFn:w,blockSelector:o,slimDOMOptions:I,dataURLOptions:m,mirror:mirror,iframeManager:O,stylesheetManager:R,shadowDomManager:k,processedNodeManager:N,canvasManager:canvasManager,ignoreCSSAttributes:V,plugins:(null==(e=null==v?void 0:v.filter(function(e){return e.observer}))?void 0:e.map(function(t){return{observer:t.observer,options:t.options,callback:function(e){return wrappedEmit(wrapEvent({type:EventType.Plugin,data:{plugin:t.name,payload:e}}))}}}))||[]},$)},M=(O.addLoadListener(function(e){try{x.push(K(e.contentDocument))}catch(e){}}),function(){takeFullSnapshot(),x.push(K(document)),recording=!0});return"interactive"===document.readyState||"complete"===document.readyState?M():(x.push(on("DOMContentLoaded",function(){wrappedEmit(wrapEvent({type:EventType.DomContentLoaded,data:{}})),"DOMContentLoaded"===B&&M()})),x.push(on("load",function(){wrappedEmit(wrapEvent({type:EventType.Load,data:{}})),"load"===B&&M()},window))),function(){x.forEach(function(e){return e()}),N.destroy(),recording=!1,unregisterErrorHandler()}}catch(e){}}function compressData(e){e=JSON.stringify(e),e=pako.gzip(e,{level:9});return btoa(new Uint8Array(e).reduce(function(e,t){return e+String.fromCharCode(t)},""))}function RecordDataHandle(e){var t,n;isDefined(window.stopBonreeRecordUpload)||0!==RecordEvents.length&&(0===RecordStart&&(RecordStart=1e3*RecordEvents[0].timestamp),(t=new FormData).append("s",bonreeRUM.getSession().sessionID),t.append("v",bonreeRUM&&bonreeRUM.version||"1.0.0"),t.append("mt",String(now())),t.append("cmt",String(setting.initTime)),t.append("di",JSON.stringify(getDeviceInfo())),t.append("ai",JSON.stringify({ai:setting.appId,av:setting.appVersion||"unknown",an:setting.appName||"unknown",at:4})),0===RecordStart&&(RecordStart=1e3*RecordEvents[0].timestamp),t.append("st",RecordStart),0!==Object.getOwnPropertyNames(getUserInfo()).length&&t.append("ui",JSON.stringify(Object.entries(getUserInfo())[0][1])),n=1e3*RecordEvents[RecordEvents.length-1].timestamp-RecordStart,t.append("ud",n),RecordStart=1e3*RecordEvents[RecordEvents.length-1].timestamp,t.append("wev","2.0.0-alpha.8"),n=new Blob([JSON.stringify({wsd:compressData(RecordEvents)})],{type:"application/json"}),t.append("event",n),UploadSessionReplayEvent(t,e+"?a="+setting.appId+"&brkey="+uuid()+"&d="+getDeviceInfo().di+"&v=2023061901"))}function UploadSessionReplayEvent(e,t){window.fetch&&fetch(t,{method:"POST",body:e}).then(function(e){if(void 0!==e)return e.json()},function(e){}).then(function(e){RecordEvents=[]}).catch(function(e){})}function BonreeRecord(e){(n=n=e)instanceof Array&&n.forEach(function(e){"sessionreplay"===e.n&&(n=e)});function t(e){return!(e<Math.floor(100*Math.random()))}var n,r,i;if(void 0!==(e=n).Rate&&t(e.Rate)||void 0!==e.c&&t(e.c))return Replayurl="https:"===window.location.protocol?e.UploadHttps||e.src&&e.src.uas:e.UploadHttp||e.src&&e.src.ua,window.$bonreeReplayUrl=Replayurl,setInterval(RecordDataHandle,1e3*(e.UploadCycle||e.src&&e.src.uc),Replayurl),r={emit:function(e){RecordEvents.push(e)},maskAllInputs:!0,recordCanvas:!0,recordCrossOriginIframes:!0,maskTextSelector:"body",maskTextFn:function(e){var t;return-1===e.indexOf("\n")?"*".repeat(e.length):(t=e.trim(),e.replace(t,"*".repeat(t.length)))}},i={emit:function(e){RecordEvents.push(e)},maskAllInputs:!0,recordCanvas:!0,recordCrossOriginIframes:!0},e.src&&void 0!==e.src.sl?1===e.src.sl?record(r):record(i):void 0!==e.Sensitivity?1===e.Sensitivity?record(r):record(i):void 0}record.addCustomEvent=function(e,t){if(!recording)throw new Error("please add custom event after start recording");wrappedEmit(wrapEvent({type:EventType.Custom,data:{tag:e,payload:t}}))},record.freezePage=function(){mutationBuffers.forEach(function(e){return e.freeze()})},record.takeFullSnapshot=function(e){RecordStart=0,recording&&takeFullSnapshot(e)},record.mirror=mirror,BonreeRecord.takeFullSnapshot=record.takeFullSnapshot;var currentLocation=isDefined(window.location)?window.location:"";function getFramework(){return window?void 0!==window.Vue||window.__VUE__||window.$nuxt&&window.$nuxt._isVue?"Vue":window.React?"React":void 0!==window.ng||isDefined(window.Zone)?"Angular":"Other":"Other"}var storageResource=[],hasHandleRecord={};function onLocationChange(){var e,n,t,r,i;currentLocation.href!==location.href&&(isDefined(hasHandleRecord)&&0<Object.getOwnPropertyNames(hasHandleRecord).length&&Object.keys(hasHandleRecord).map(function(e){"function"==typeof hasHandleRecord[e]&&(hasHandleRecord[e](),delete hasHandleRecord[e])}),setting.isSinglePage=!0,setting.pageViewId=uuid(),e=extend({},location),-1<(n={info:{uuid:uuid(),timestamp:now(),url:e.href,referrer:currentLocation.href,duration:1e3,status:0,path:""===e.hash?e.pathname:e.hash,root:""===e.hash?e.origin+e.pathname:e.origin+e.pathname+e.hash.replace("#/",""),fullUrl:""!==e.hash?e.origin+e.pathname:e.origin+"/",framework:getFramework(),pvid:setting.pageViewId,ns:isDefined(performance)&&isDefined(performance.timing)?performance.timing.navigationStart:0}}).info.url.indexOf("?")&&(n.info.url=n.info.url.split("?")[0]),-1<n.info.referrer.indexOf("?")&&(n.info.referrer=n.info.referrer.split("?")[0]),-1<n.info.path.indexOf("?")&&(n.info.path=n.info.path.split("?")[0]),-1<n.info.root.indexOf("?")&&(n.info.root=n.info.root.split("?")[0]),-1<n.info.fullUrl.indexOf("?")&&(n.info.fullUrl=n.info.fullUrl.split("?")[0]),t=function(){isDefined(hasHandleRecord)&&isDefined(hasHandleRecord[n.info.uuid])&&(isDefined(hasHandleRecord)&&isDefined(hasHandleRecord[n.info.uuid])&&delete hasHandleRecord[n.info.uuid],notifierEmit({t:ROUTE_DATA,p:n}))},isDefined(hasHandleRecord)&&(hasHandleRecord[n.info.uuid]=t),r=setTimeout(t,1e3),(i=new PageActivityManager(function(e){e.forEach(function(e){var t={st:getFixedMetric(e,START_TIME),name:sensitiveRuleHandle({p:{info:{url:e[NAME$2]}}}).p.info.url,dura:isDefined(e[DURATION])?getFixedMetric(e,DURATION):e[RESPONSE_END]-e[START_TIME],rt:getMineTypeByUrl(e[NAME$2]),fs:getFixedMetric(e,FETCH_START),dls:getFixedMetric(e,DOMAIN_LOOKUP_START),dle:getFixedMetric(e,DOMAIN_LOOKUP_END),cs:getFixedMetric(e,CONNECT_START),ce:getFixedMetric(e,CONNECT_END),scs:getFixedMetric(e,SECURE_CONNECTION_START),reqs:getFixedMetric(e,REQUEST_START),rsps:getFixedMetric(e,RESPONSE_START),rspe:getFixedMetric(e,RESPONSE_END),rs:isDefined(e[RESPONSE_STATUS])?e[RESPONSE_STATUS]:-1};isDefined(e[INITIATOR_TYPE])&&(t.it=e[INITIATOR_TYPE]),isDefined(e[DECODED_BODY_SIZE])?(t.ts=e[TRANSFER_SIZE],t.ebs=e[ENCODED_BODY_SIZE]>=Math.pow(2,64)?Math.pow(2,60):e[ENCODED_BODY_SIZE],t.dbs=e[DECODED_BODY_SIZE]):(t.ts=0,t.ebs=0,t.dbs=0),storageResource.push(t)}),isDefined(r)&&(clearTimeout(r),r=setTimeout(t,1e3))})).turnOnRecord(),i.startPageActivityObserver(),t=function(){var e,t;isDefined(hasHandleRecord)&&isDefined(hasHandleRecord[n.info.uuid])?(isDefined(hasHandleRecord)&&isDefined(hasHandleRecord[n.info.uuid])&&delete hasHandleRecord[n.info.uuid],0<i.mutationRecord.length?0<i.performanceRecord.length?(e=i.performanceRecord.pop(),t=i.mutationRecord.pop(),n.info.duration=0<e-t?e-n.info.timestamp:t-n.info.timestamp):n.info.duration=i.mutationRecord.pop()-n.info.timestamp:0<i.performanceRecord.length&&(n.info.duration=i.performanceRecord.pop()-n.info.timestamp),i.turnOffRecord(),i.stopPageActivityObserver(),isDefined(n)&&isDefined(storageResource)&&0<storageResource.length&&(n.info.wri=storageResource),notifierEmit({t:ROUTE_DATA,p:n}),isDefined(storageResource)&&(storageResource=[])):(i.turnOffRecord(),i.stopPageActivityObserver())},isDefined(hasHandleRecord)&&(hasHandleRecord[n.info.uuid]=t),currentLocation=e)}var LocationChangeObserver=function(){function t(e){_classCallCheck(this,t),this.currentLocation=extend({},e),currentLocation=this.currentLocation}return _createClass(t,[{key:"trackHistory",value:function(){var r=history.pushState,i=history.replaceState;isDefined(r)&&!isDefined(r.$hasHooked)&&(history.pushState=function(e,t,n){r.apply(this,[e,t,n]),onLocationChange()},history.pushState.$hasHooked=!0),isDefined(i)&&!isDefined(history.replaceState.$hasHooked)&&(history.replaceState=function(e,t,n){i.apply(this,[e,t,n]),onLocationChange()},history.replaceState.$hasHooked=!0),window.addEventListener("popstate",onLocationChange)}},{key:"trackHashchage",value:function(){window.addEventListener("hashchange",onLocationChange)}},{key:"init",value:function(){this.trackHistory(),this.trackHashchage()}}]),t}();function initLocationChangeObserver(){var e=new LocationChangeObserver(isDefined(window.location)?window.location:"");e.init()}function initConsole(){console&&console.error&&(console.error=hackConsole(console.error,null,errorHandler))}function hackConsole(t,n,r){var e;return t.$original?t:((e=function(){var e=args(arguments);try{e[0]instanceof Error&&error(e[0])}catch(e){}try{return isDefined(n)&&n.apply(this,e),t.apply(this,e)}catch(e){throw e}finally{r.apply(this,e)}}).$original=t,e)}function errorHandler(e){var t;console.$bonree||(t=formatArg(args(arguments)),notifierEmit({t:CONSOLE_DATA,p:{info:{lv:"error",msg:t}}}))}function formatArg(e){return e&&isArray(e)?map(e,function(e){return formatMessage(e=void 0===e?String(e):e)}).join(" "):e}function formatMessage(t){var n;if(isString$2(t))return t;if(t instanceof Error||t instanceof Function)return String(t);try{n=stringify(t)}catch(e){n=String(t)}return n}var spanRecord={},SpanStruct=function(){function s(e,t){var n=2<arguments.length&&void 0!==arguments[2]&&arguments[2],r=3<arguments.length?arguments[3]:void 0,i=4<arguments.length&&void 0!==arguments[4]?arguments[4]:-1,a=5<arguments.length&&void 0!==arguments[5]&&arguments[5],o=6<arguments.length?arguments[6]:void 0;_classCallCheck(this,s),this.isChild=n,isDefined(r)&&(this.startTime=r),isDefined(e)&&isDefined(t)&&"string"==typeof e&&"string"==typeof t&&/^[a-zA-Z0-9:_-\s@./]+$/.test(t)&&""!==t.trim()&&/^[a-zA-Z0-9:_-\s@./]+$/.test(e)&&""!==e.trim()?(this.spanEvent={n:e.slice(0,256),t:t.slice(0,256),da:[],tag:[],m:[],ic:!0,st:isDefined(r)?1e3*(new Date).getTime()-r:0},this.special=a):this.special=!0,n||this.special||(this.spanEvent.timestamp=1e3*(new Date).getTime(),this.startTime=this.spanEvent.timestamp),n?(this.identify=o,spanRecord[this.identify]=spanRecord[this.identify]+1):(this.identify=uuid(),spanRecord[this.identify]=0),this.dataSave=[],this.tagSave=[],this.metricSave=[],this.childSave=[],this.hasFinished=!1,this.deep=i+1}return _createClass(s,[{key:"startChild",value:function(e,t){return this.hasFinished||!(isDefined(e)&&isDefined(t)&&"string"==typeof e&&"string"==typeof t&&/^[a-zA-Z0-9:_-\s@./]+$/.test(t)&&""!==t.trim()&&/^[a-zA-Z0-9:_-\s@./]+$/.test(e)&&""!==e.trim())||this.special||10<=this.deep||isDefined(this.childSave)&&50<=this.childSave.length||isDefined(spanRecord)&&isDefined(spanRecord[this.identify])&&200<=spanRecord[this.identify]?new s("name","type",!0,this.startTime,this.deep,!0,this.identify):isDefined(this.spanEvent)?(e=new s(e,t,!0,this.startTime,this.deep,!1,this.identify),isDefined(this.spanEvent.sub)?this.spanEvent.sub.push(e.spanEvent):this.spanEvent.sub=[e.spanEvent],this.childSave.push(e),e):void 0}},{key:"setData",value:function(e,t){if(!this.special&&!this.hasFinished&&isDefined(e)&&isDefined(t)&&"string"==typeof e&&"string"==typeof t&&!(200<e.length)&&/^[a-zA-Z][a-zA-Z\.\_-]*[a-zA-Z]$|^[a-zA-Z]$/.test(e)&&""!==e.trim()&&""!==t.trim()&&!(isDefined(this.spanEvent.da)&&64<=this.spanEvent.da.length)&&isDefined(this.spanEvent.da))if(0<this.spanEvent.da.length)if(isDefined(this.dataSave)&&-1<this.dataSave.indexOf(e))for(var n=0;n<this.spanEvent.da.length;n++)isDefined(this.spanEvent.da[n].k)&&this.spanEvent.da[n].k===e&&(this.spanEvent.da[n].k=e,this.spanEvent.da[n].v=t.slice(0,7e3));else this.spanEvent.da.push({k:e,v:t.slice(0,7e3)}),this.dataSave.push(e);else this.spanEvent.da.push({k:e,v:t.slice(0,7e3)}),this.dataSave.push(e)}},{key:"removeData",value:function(e){if(!this.special&&!this.hasFinished&&isDefined(e)&&"string"==typeof e&&!(200<e.length)&&/^[a-zA-Z][a-zA-Z\.\_-]*[a-zA-Z]$|^[a-zA-Z]$/.test(e)&&""!==e.trim()&&isDefined(this.dataSave)&&-1<this.dataSave.indexOf(e))for(var t,n=0;n<this.spanEvent.da.length;n++)isDefined(this.spanEvent.da[n].k)&&this.spanEvent.da[n].k===e&&(this.spanEvent.da.splice(n,1),t=this.dataSave.indexOf(e),this.dataSave.splice(t,1))}},{key:"setTag",value:function(e,t){if(!this.special&&!this.hasFinished&&isDefined(e)&&isDefined(t)&&"string"==typeof e&&"string"==typeof t&&!(200<e.length)&&/^[a-zA-Z][a-zA-Z\.\_-]*[a-zA-Z]$|^[a-zA-Z]$/.test(e)&&""!==e.trim()&&""!==t.trim()&&!(isDefined(this.spanEvent.tag)&&64<=this.spanEvent.tag.length)&&isDefined(this.spanEvent.tag))if(0<this.spanEvent.tag.length)if(isDefined(this.tagSave)&&-1<this.tagSave.indexOf(e))for(var n=0;n<this.spanEvent.tag.length;n++)isDefined(this.spanEvent.tag[n].k)&&this.spanEvent.tag[n].k===e&&(this.spanEvent.tag[n].k=e,this.spanEvent.tag[n].v=t.slice(0,7e3));else this.spanEvent.tag.push({k:e,v:t.slice(0,7e3)}),this.tagSave.push(e);else this.spanEvent.tag.push({k:e,v:t.slice(0,7e3)}),this.tagSave.push(e)}},{key:"removeTag",value:function(e){if(!this.special&&!this.hasFinished&&isDefined(e)&&"string"==typeof e&&!(200<e.length)&&/^[a-zA-Z][a-zA-Z\.\_-]*[a-zA-Z]$|^[a-zA-Z]$/.test(e)&&""!==e.trim()&&isDefined(this.tagSave)&&-1<this.tagSave.indexOf(e))for(var t,n=0;n<this.spanEvent.tag.length;n++)isDefined(this.spanEvent.tag[n].k)&&this.spanEvent.tag[n].k===e&&(this.spanEvent.tag.splice(n,1),t=this.tagSave.indexOf(e),this.tagSave.splice(t,1))}},{key:"setMetric",value:function(e,t,n){if(!this.special&&!this.hasFinished&&isDefined(e)&&isDefined(t)&&"number"==typeof t&&"string"==typeof e&&!(200<e.length)&&/^[a-zA-Z][a-zA-Z\.\_-]*[a-zA-Z]$|^[a-zA-Z]$/.test(e)&&""!==e.trim()&&!(isDefined(this.spanEvent.m)&&64<=this.spanEvent.m.length)&&isDefined(this.spanEvent.m))if(0<this.spanEvent.m.length)if(isDefined(this.metricSave)&&-1<this.metricSave.indexOf(e))for(var r=0;r<this.spanEvent.m.length;r++)isDefined(this.spanEvent.m[r].k)&&this.spanEvent.m[r].k===e&&(this.spanEvent.m[r].k=e,this.spanEvent.m[r].v=Math.round(t),isDefined(n)&&"string"==typeof n&&n.length<=256&&/^[a-zA-Z0-9:_-\s@./]+$/.test(n)&&""!==n.trim()?this.spanEvent.m[r].u=n:delete this.spanEvent.m[r].u);else isDefined(n)&&"string"==typeof n&&n.length<=256&&/^[a-zA-Z0-9:_-\s@./]+$/.test(n)&&""!==n.trim()?this.spanEvent.m.push({k:e,v:Math.round(t),u:n}):this.spanEvent.m.push({k:e,v:Math.round(t)}),this.metricSave.push(e);else isDefined(n)&&"string"==typeof n&&n.length<=256&&/^[a-zA-Z0-9:_-\s@./]+$/.test(n)&&""!==n.trim()?this.spanEvent.m.push({k:e,v:Math.round(t),u:n}):this.spanEvent.m.push({k:e,v:Math.round(t)}),this.metricSave.push(e)}},{key:"removeMetric",value:function(e){if(!this.special&&!this.hasFinished&&isDefined(e)&&"string"==typeof e&&!(200<e.length)&&/^[a-zA-Z][a-zA-Z\.\_-]*[a-zA-Z]$|^[a-zA-Z]$/.test(e)&&""!==e.trim()&&isDefined(this.metricSave)&&-1<this.metricSave.indexOf(e))for(var t,n=0;n<this.spanEvent.m.length;n++)isDefined(this.spanEvent.m[n].k)&&this.spanEvent.m[n].k===e&&(this.spanEvent.m.splice(n,1),t=this.metricSave.indexOf(e),this.metricSave.splice(t,1))}},{key:"setDuration",value:function(e){this.special||this.hasFinished||isDefined(e)&&"number"==typeof e&&(this.spanEvent.du=Math.round(e))}},{key:"setStatus",value:function(e){this.special||this.hasFinished||!isDefined(e)||"number"!=typeof e||0!==e&&1!==e&&2!==e||(this.spanEvent.sta=e)}},{key:"setStatusCode",value:function(e){this.special||this.hasFinished||!isDefined(e)||"string"!=typeof e||7e3<e.length||""===e.trim()||(this.spanEvent.stac=e)}},{key:"finish",value:function(e){if(!this.special&&!this.hasFinished&&(!isDefined(e)&&"number"!=typeof e||0!==e&&1!==e&&2!==e||(this.spanEvent.sta=e),this.hasFinished=!0,isDefined(this.spanEvent.da)&&0===this.spanEvent.da.length&&delete this.spanEvent.da,isDefined(this.spanEvent.tag)&&0===this.spanEvent.tag.length&&delete this.spanEvent.tag,isDefined(this.spanEvent.m)&&0===this.spanEvent.m.length&&delete this.spanEvent.m,isDefined(this.spanEvent.du)||(this.spanEvent.du=1e3*(new Date).getTime()-this.startTime-this.spanEvent.st),isDefined(this.spanEvent.sta)||(this.spanEvent.sta=1),isDefined(this.spanEvent.st))&&isDefined(this.spanEvent.n)&&isDefined(this.spanEvent.t)&&isDefined(this.spanEvent.ic)){if(isDefined(this.childSave)&&0<this.childSave.length)for(var t=0;t<this.childSave.length;t++)this.childSave[t].finish();this.isChild||(isDefined(spanRecord)&&isDefined(spanRecord[this.identify])&&delete spanRecord[this.identify],notifierEmit({t:SPAN_DATA,p:{info:this.spanEvent}}))}}}]),s}(),websocketSendStart=0;function beforeWebsocketSend(e){websocketSendStart=1e3*(new Date).getTime()}function afterWebsocketSend(e){var t,n;isDefined(e)&&(-1<e.constructor.toString().indexOf("String")&&(t=(isDefined(window.TextEncoder)?(new TextEncoder).encode(e):e).length),-1<e.constructor.toString().indexOf("ArrayBuffer")&&(t=e.byteLength),-1<e.constructor.toString().indexOf("Blob")&&(t=e.size),-1<e.constructor.toString().indexOf("Array")&&(t=e.byteLength),e=isDefined(this.name)?this.name:"",(e=new SpanStruct(e,"websocket")).setStatus(1),e.spanEvent.ic=!1,e.setTag("process","send"),e.setTag("connectID",isDefined(this.identify)?this.identify:uuid()),e.setData("url",this.handledUrl||""),0===(n=1e3*(new Date).getTime()-websocketSendStart)&&(n=999),!isDefined(this.readyState)||2!==this.readyState&&3!==this.readyState||(e.setStatus(2),e.setStatusCode("WebSocket is already in CLOSING or CLOSED state.")),e.setMetric("send",n,"us"),e.setDuration(n),e.setMetric("size",t,"byte"),e.finish())}function hackWebsocket(){var t=window.WebSocket;window.WebSocket.prototype.send=hack(window.WebSocket.prototype.send,beforeWebsocketSend,afterWebsocketSend),window.WebSocket=function(){var n=1e3*(new Date).getTime(),r=void 0===arguments[1]?new t(arguments[0]):new t(arguments[0],arguments[1]),i=r&&r.url&&-1<r.url.indexOf("?")?r.url.split("?")[0]:r.url,e=sensitiveRuleHandle({p:{info:{url:r.url}}}).p.info.url;return r.handledUrl=isDefined(e)?e:"",r.identify=uuid(),r.name=isDefined(i)?i:"",r.hasHandleConnect=!1,r.addEventListener("open",function(){var e=new SpanStruct(i,"websocket");e.spanEvent.st=0,e.spanEvent.timestamp=n,e.setStatus(1),e.spanEvent.ic=!1,e.setTag("process","connect"),e.setTag("connectID",isDefined(r.identify)?r.identify:uuid()),e.setData("url",r.handledUrl),e.setDuration(1e3*(new Date).getTime()-n),e.finish(),r.hasHandleConnect=!0}),r.addEventListener("error",function(e){var t;r.hasHandleConnect||((t=new SpanStruct(i,"websocket")).spanEvent.st=0,t.spanEvent.timestamp=n,t.setStatus(2),t.spanEvent.ic=!1,t.setTag("process","connect"),t.setTag("connectID",isDefined(r.identify)?r.identify:uuid()),t.setData("url",r.handledUrl),t.setDuration(1e3*(new Date).getTime()-n),t.setStatusCode("Websocket connection failed"),t.finish(),r.hasHandleConnect=!0)}),r},window.WebSocket=extend(window.WebSocket,t),window.WebSocket.prototype=t.prototype}function initWebsocket(){void 0!==window.WebSocket&&void 0!==window.WebSocket.prototype&&hackWebsocket()}var defaultAPI={config:NIL_FN,error:NIL_FN,on:NIL_FN,recordCustomActionEnd:NIL_FN,takeFullSnapshot:NIL_FN};function dataEngine(){if("undefined"==typeof document)return log("Document is undefined","",!0),defaultAPI;if(window.bonreeRUM)return log("bonreeRUM is Defined","",!0),window.bonreeRUM;if(!checkStatus())return log("Disable bonreeRUM probe done","",!0),window.bonreeRUM=defaultAPI;initLocationChangeObserver(),init$1(),initDocumentCollector(),initRequestCollector(),initConsole(),setting.enableWebsocket&&initWebsocket(),isDefined(setting.ac)&&isDefined(setting.ac.ac)&&isDefined(setting.ac.cp)&&isDefined(setting.ac.mmd)&&isDefined(setting.ac.aot)&&(openTraceAction(),initActionCollector(),initNewActionCollection()),isDefined(setting.RecordConfiguration||setting.mc)&&(window.stopBonreeRecord=BonreeRecord(setting.RecordConfiguration||setting.mc));var e=overwrite(defaultAPI,{config:setConfig,error:error,on:notifierOn,recordCustomActionEnd:recordCustomActionEnd,takeFullSnapshot:BonreeRecord.takeFullSnapshot});return window.bonreeRUM=e}var curRoute$1="";function routeData(n){var e=n.url,r=getTime(),e=(forEach([START_TIME,TIMESTAMP,DURATION,"status","framework","version","path","alias","mode","root","pattern","fullUrl"],function(e){var t=n[e];isDefined(t)&&(r[e]=t)}),r.url=e,r.referrer=curRoute$1,curRoute$1=e,{info:r});isReadable(n.ext)&&(e.ext=n.ext),(isDefined(setting.sensitiveNetworkRule)||isDefined(setting.snr))&&(isDefined(e.info.path)&&-1<e.info.path.indexOf("?")&&(e.info.path=e.info.path.split("?")[0]+"?"),isDefined(e.info.root))&&-1<e.info.root.indexOf("?")&&(e.info.root=e.info.root.split("?")[0]+"?");try{var t=spaPageRouterData({t:ROUTE_DATA,p:e});jsInternalBridge.routeChangeData(stringify({ent:t.ent,v:t.data}))}catch(e){}notifierEmit({t:ROUTE_DATA,p:e})}function RouteCleaner(){this.route=null,this.runingRoute=null}function extendInfo(n,r){isDefined(r)&&forEach(["url","framework","version","path","alias","root","mode","ext","pattern","fullUrl"],function(e){var t=r[e];isDefined(t)&&(n[e]=t)})}RouteCleaner.prototype.start=function(e,t){var t=nowtime(t),n={};n[START_TIME]=t[0],n[TIMESTAMP]=t[1],extendInfo(n,e),n.referrer=isDefined(this.route)?this.route.url:"",this.runingRoute=n},RouteCleaner.prototype.end=function(e){var t=this.runingRoute;if(!isDefined(t))return log();var n=now(),n=(t[DURATION]=n-t[TIMESTAMP],t.status=e&&isDefined(e.status)?e.status:0,extendInfo(t,e),t.url.split("//"));t.fullUrl=n[0]+"//"+n[1].replace(/[!#]/g,"").replace(/\/\//g,"/"),routeData(t),this.route=t,this.runingRoute=null};var dropSearch=function(e){return isDefined(e)?e.replace(/\?[^#?]+/g,""):""},getRootPath=function(e,t){var e="/"+e.replace(/[!#]/g,""),n=e.lastIndexOf(t||"/");return-1===n?"/":("/"+e.substring(0,n)+"/"+e.substring(n+t.length)).replace(/^\/+/,"/")},cleaner=new RouteCleaner;function startNG$1(){if(probability){if(!isDefined(window.angular))return log("C6200");window.bonreeRUM.AngularPluginVersion="1.0.0",log("C6202");var l=angular.version?angular.version.full:"";angular.module("brAngularjsPlugin",[]).config(["$provide",function(e){e.decorator("$exceptionHandler",["$delegate",function(n){return log("C6203"),function(e,t){n(e,t),error(e)}}])}]).run(["$location","$rootScope",function(c,e){function r(e,t){log("C6205");var n,r=dropSearch(c.absUrl()),i=c.path(),a=-1<window.location.hash.indexOf(i)?"hash":"history",o="hash"==a?window.location.hash:window.location.pathname,o=getRootPath(o,i),s=0,e=(isDefined(e)?(n=isDefined(e.originalPath)?e.originalPath:e.url,r=r.replace(i,n)):(s=2,n="[404]"),{url:r,path:i,status:s,alias:"",framework:"Angular",version:l,root:o,mode:a,pattern:n});isEmpty(t)||(e.ext=t),cleaner.end(e)}log("C6204"),e.$on("$routeChangeStart",function(e,t,n){cleaner.start()}),e.$on("$routeChangeSuccess",function(e,t,n){r(prop(t,"$$route"),prop(t,"params"))}),e.$on("$stateChangeStart",function(){cleaner.start()}),e.$on("$stateChangeSuccess",function(e,t,n){r(t,n)})}])}}var BonreeNG=startNG$1;function startNG(){if(probability){if(!isDefined(window.angular))return log("C6200");window.bonreeRUM.AngularPluginVersion="1.0.0",log("C6202");var l=angular.version?angular.version.full:"";angular.module("brAngularjsPlugin",[]).config(["$provide",function(e){e.decorator("$exceptionHandler",["$delegate",function(n){return log("C6203"),function(e,t){n(e,t),error(e)}}])}]).run(["$location","$transitions",function(c,e){log("C6206"),e.onStart({},function(){cleaner.start()}),e.onSuccess({},function(e){var t,n,r,i,a,o,s;e=e,log("C6205"),n=e.to(),e=e.params("to"),r=dropSearch(c.absUrl()),i=c.path(),a=-1<window.location.hash.indexOf(i)?"hash":"history",o="hash"==a?window.location.hash:window.location.pathname,o=getRootPath(o,i),s=0,isDefined(n)?(t=n.url,r=r.replace(i,t)):(s=2,t="[404]"),n={url:r,path:i,status:s,alias:"",framework:"Angular",version:l,root:o,mode:a,pattern:t},isEmpty(e)||(n.ext=e),cleaner.end(n)})}])}}var BonreeUiNG=startNG,version="unknown";function initRoute(){var e=this.history||this.props.history||{};e.$$install_2=/^\d+$/.test(e.$$install_2)&&e.$$install_2+1||0,e.listen&&!e.$$install&&(e.listen(function(){0===e.$$install_2&&(cleaner.start(),endRoute.call(window))}),e.$$install=!0),cleaner.start()}function endRoute(){var e,t=(this.history||this.props.history||{}).location,n=window.location,r=n.pathname,t=(t&&(r=t.pathname),dropSearch(n.href)),i=-1===n.hash.indexOf(r)?"history":"hash",n="hash"==i?n.hash:n.pathname,n=getRootPath(n,r),a=0;"/404"===n&&(a=2,e="[404]"),cleaner.end({framework:"React",version:version,mode:i,url:t,root:r,path:n,pattern:e,alias:"",status:a})}var BonreeReact=function(e,t){probability&&(window.bonreeRUM&&(window.bonreeRUM.ReactVersion=e.version),t.prototype.componentDidMount=hack(t.prototype.componentDidMount,initRoute,endRoute),t.prototype.componentDidUpdate=hack(t.prototype.componentDidUpdate,initRoute,endRoute))},curRoute=0;function getMatchedPattern(e,t){return 0<e.length?e[e.length-1].path||t:"[404]"}function resolveQueue(e,t){for(var n=Math.max(e.length,t.length),r=0;r<n&&e[r]===t[r];r++);return{updated:t.slice(0,r),activated:t.slice(r),deactivated:e.slice(r)}}var endIt=function(e,t){--curRoute,!0!==e&&0!==curRoute||(e=isDefined(t)?{status:t}:null,cleaner.end(e),curRoute=0)};function install(e,t){var u,n,r;probability&&!install.installed&&(install.installed=!0,u=e.version,window.bonreeRUM.VueVersion=u,e.mixin({beforeCreate:function(){var e,l,t,d,n=this.$options.router;isDefined(n)&&(t=(e=n.options).base,l=void 0===t?"":t,t=e.mode,d=void 0===t?"hash":t,n.beforeEach(function(e,t,n){var r=e.path,i=getMatchedPattern(e.matched,r)||r,a=0,o=i,s=("[404]"===i&&(o="/404",a=2),window.location),c=s.href,o=(s.search&&(c=c.replace(/\?[^#?]+/g,"")),"hash"===d?c=c.replace(s.hash,"#"+o):"history"===d&&(c=(l+o).replace("//","/"),c=s.origin+c),{framework:"Vue",version:u,status:a,mode:d,url:c,path:r,pattern:i,alias:e.name||"",root:l||r||"/",ext:e.params||{}});cleaner.start(o),n()}),n.afterEach(function(t,e){e=resolveQueue(e.matched,t.matched).activated,t=[];try{t=flatten(e.map(function(t){return Object.keys(t.components).map(function(e){return t.components[e]})})).filter(function(e){return!e.functional})}catch(e){t=[]}0===(curRoute=t.length)&&endIt(!0,2)}),isDefined(n.onError))&&n.onError(function(e){error(e),endIt(!0,0)})},beforeRouteEnter:function(e,t,n){n(function(e){endIt()})}}),r=hack(e.config.errorHandler,n=function(e,t,n){error(e)}),Object.defineProperty(e.config,"errorHandler",{get:function(){return r},set:function(e){r=hack(e,n)}}))}var BonreeVUE=install,filterData=function(e,t,n,r,i){return!(void 0===e||(null==t||""===t?(jsInternalBridge.log(n+" Parameter has a value or key ["+e+"] that does not exist!"),0):null!==r&&t.length>r?(jsInternalBridge.log(n+" Parameter has a value or key ["+e+"] that the value length exceeds "+t.length+"!"),0):!i||!isNaN(t)||(jsInternalBridge.log(n+" Parameter has a value or key ["+e+"] value is not a valid number!"),0)))},MAX_LENGTH_1=2083,MAX_LENGTH_2=256,filterInfo=function(e){var t={};if(void 0!==e&&"[object Object]"===Object.prototype.toString.call(e)){if(64<Object.getOwnPropertyNames(e).length){var n,r=Object.getOwnPropertyNames(e).slice(0,64);for(n in r)"string"!=typeof r[n]||256<r[n].length||""===r[n]||(t[r[n]]=e[r[n]])}else for(var i in e)"string"!=typeof i||256<i.length||""===i||(t[i]=e[i]);return 0===Object.getOwnPropertyNames(t).length?void 0:t}},timeStamp={},identification={},correlationId={},timeStampEvent={},CustomViewExistQueue=[],CustomEventExistQueue=[],jsBridge={setCustomSpeedTest:function(e,t){var n;arguments.length<1||2<arguments.length?log("setCustomSpeedTest parameter number error"):e&&256<e.length?log("setCustomSpeedTest ip length error"):Array.isArray(t)&&0<t.length&&t.length<=1e3?(n={},isDefined(e)&&""!==e&&(n.oip=e,n.sti=t,(e=new Metric({info:n},"speedtest")).info("oip").info("sti"),pushQueueData(e.build(),!1))):log("setCustomSpeedTest custom speedtest parameter number error")},setExtraInfo:function(e){jsInternalBridge.log("setExtraInfo=>"+e);e=e&&set_64_keys(e,64)||e;log("setExtraInfo=>"+e);try{isIOS(setting.osType)?window.webkit.messageHandlers.brsWKSetExtraInfo.postMessage(stringify(e)):isHOS(setting.osType)?window.bonreePrivateInterface.call(stringify({setExtraInfo:e})):window.bonreePrivateInterface.setExtraInfo(stringify(e))}catch(e){}},setUserID:function(e){if(jsInternalBridge.log("setUserID=>"+e),!filterData("userId",e,"setUserID",MAX_LENGTH_2,!1)&&/^[a-zA-Z0-9:_-]+$/.test(e)){log("setUserID=>"+e);try{isIOS(setting.osType)?window.webkit.messageHandlers.brsWKSetUserId.postMessage(e):isHOS(setting.osType)?window.bonreePrivateInterface.call(stringify({setUserID:e})):window.bonreePrivateInterface.setUserID(e)}catch(e){}}},setCustomEventWithLabel:function(e,t,n,r,i){var a;arguments.length<1||5<arguments.length?log("setCustomEvent wrong number of custom event parameters"):filterData("eventId",e,"setCustomEvent",MAX_LENGTH_2,!1)?log("setCustomEvent  parameters length >256"):isDefined(i)&&7e3<JSON.stringify(i).length||(a={},t=set_length(t,MAX_LENGTH_2),r=set_length(r,7e3),a.eventId=isDefined(e)?e:"",a.eventName=t,a.eventParam=r,a.eventLabel=set_length(n,256),a.info=filterInfo(i),(e=new Metric({info:a},CUSTOM_EVENT)).info("eventId","","i").info("t",0).info("d",0),void 0!==a.eventName&&""!==a.eventName&&null!==a.eventName&&e.info("eventName","","n"),void 0!==a.eventLabel&&"unuse"!==a.eventLabel&&""!==a.eventLabel&&null!==a.eventLabel&&e.info("eventLabel","","l"),void 0!==a.eventParam&&""!==a.eventParam&&null!==a.eventParam&&e.info("eventParam","","p"),void 0!==a.info&&a.info instanceof Object&&e.info("info","","info"),pushQueueData(e.build(),!1))},setCustomEvent:function(e,t,n,r){this.setCustomEventWithLabel(e,t,"unuse",n,r)},setCustomLog:function(e,t){var n;arguments.length<1||2<arguments.length?jsInternalBridge.log("setcustomlog custom log parameter number error"):filterData("logInfo",e,"setCustomLog",null,!1)||(n={},e=isDefined(e)&&set_length(e,1e4)||"",t=isDefined(t)?set_length(t,1e4):t,n.logInfo=e,n.logParam=t,(e=new Metric({info:n},CUSTOM_LOG)).info("logInfo","","i"),void 0!==n.logParam&&e.info("logParam","","p"),pushQueueData(e.build(),!1))},setCustomMetric:function(e,t,n){var r;arguments.length<1||3<arguments.length?jsInternalBridge.log("setCustomMetric自定义指标参数个数错误"):filterData("metricName",e,"setCustomMetric",MAX_LENGTH_2,!1)||filterData("metricValue",t,"setCustomMetric",null,!1)||((r={}).metricName=isDefined(e)?e:"",r.metricValue=isDefined(t)?Math.round(t):"",isDefined(n)&&(r.metricParam=1e4<n.length?n.slice(0,1e4):n),(e=new Metric({info:r},CUSTOM_METRIC)).info("metricName","","n").info("metricValue",0,"v"),void 0!==n&&e.info("metricParam","","p"),pushQueueData(e.build(),!1))},setCustomException:function(e,t,n){var r;arguments.length<1||3<arguments.length?jsInternalBridge.log("onEvent自定义事件参数个数错误"):filterData("exceptionType",e,"setCustomException",MAX_LENGTH_2,!1)||(r={},t=isDefined(t)&&set_length(t,512)||"",n=isDefined(n)&&set_length(n,1e4)||"",r.exceptionType=isDefined(e)?e:"",r.causedBy=t,r.errorDump=n,(e=new Metric({info:r},CRASH)).info("causedBy","","cab").info("exceptionType","","t").info("errorDump","","p").info("ic",!0).info("id",uuid()),pushQueueData(e.build(),!1))},setCustomPageStart:function(e,t){var n;arguments.length<1||2<arguments.length?log("onPageStart自定义事件参数个数错误"):filterData("pageName",e,"setCustomPageStart",MAX_LENGTH_2,!1)?log("onPageStart自定义事件参数长度不符合"):includes$1(CustomViewExistQueue,e)||(CustomViewExistQueue.push(e),timeStamp[e]=dateNow(),identification[e]=buildViewID(),e&&(n=set_length(t,MAX_LENGTH_2),(n=new Metric({info:{pageName:e,param:n}},VIEW)).info("pageName","","n").info("ci",identification[e]).info("lt",0).info("m",1).info("t",1).info("ic",!0),void 0!==t&&n.info("param","","p"),pushQueueData(n.build(),!1)))},setCustomPageEnd:function(t,e){var n,r;arguments.length<1||2<arguments.length?log("onPageEnd自定义事件参数个数错误"):filterData("pageName",t,"setCustomPageEnd",MAX_LENGTH_2,!1)?log("onPageEnd自定义事件参数长度不符合"):includes$1(CustomViewExistQueue,t)&&(r=0,isDefined(dateNow))&&timeStamp&&isDefined(timeStamp[t])&&(r=dateNow()-timeStamp[t],r*=1e3,t)&&(n=set_length(e,MAX_LENGTH_2),n={pageName:t,param:n},CustomViewExistQueue=filter(CustomViewExistQueue,function(e){return e!==t}),delete timeStamp[t],(n=new Metric({info:n},VIEW)).info("pageName","","n").info("ci",identification[t]).info("st",r).info("lt",0).info("m",2).info("t",1).info("ic",!0),void 0!==e&&n.info("param","","p"),pushQueueData(n.build(),!1),delete identification[t])},setCustomH5performanceData:function(e){var t={};try{t=JSON.parse(e)}catch(e){return void jsInternalBridge.log("setCustomH5performanceData has error! ==>"+e)}var n=!0;if((n&&forEach(["url"],function(e){filterData(e,t[e],"setCustomH5performanceData",MAX_LENGTH_1,!1)&&(t[e]=set_length(t[e],MAX_LENGTH_1))}),n&&forEach(["imd","ns","ues","uee","rds","rde","fs","dls","dle","cs","scs","ce","reqs","rsps","rspe","dl","di","dcles","dclee","dc","les","lee","fp","fcp","lcp"],function(e){if(filterData(e,t[e],"setCustomH5performanceData",null,!0))return n=!1}),n)&&((void 0===t.pvid||null===t.pvid||""===t.pvid||t.pvid.length>MAX_LENGTH_2)&&(t.pvid=uuid()),t={url:t.url,pvid:t.pvid,ic:!0,wpi:{ns:isDefined(t.ns)&&0<t.ns?t.ns:0,ues:isDefined(t.ues)&&0<t.ues?t.ues:-1,uee:isDefined(t.uee)&&0<t.uee?t.uee:-1,rds:isDefined(t.rds)&&0<t.rds?t.rds:-1,rde:isDefined(t.rde)&&0<t.rde?t.rde:-1,fs:isDefined(t.fs)&&0<t.fs?t.fs:0,dls:isDefined(t.dls)&&0<t.dls?t.dls:-1,dle:isDefined(t.dle)&&0<t.dle?t.dle:-1,cs:isDefined(t.cs)&&0<t.cs?t.cs:-1,scs:isDefined(t.scs)&&0<t.scs?t.scs:-1,ce:isDefined(t.ce)&&0<t.ce?t.ce:-1,reqs:isDefined(t.reqs)&&0<t.reqs?t.reqs:0,rsps:isDefined(t.rsps)&&0<t.rsps?t.rsps:0,rspe:isDefined(t.rspe)&&0<t.rspe?t.rspe:0,dl:isDefined(t.dl)&&0<t.dl?t.dl:0,di:isDefined(t.di)&&0<t.di?t.di:0,dcles:isDefined(t.dcles)&&0<t.dcles?t.dcles:0,dclee:isDefined(t.dclee)&&0<t.dclee?t.dclee:0,dc:isDefined(t.dc)&&0<t.dc?t.dc:0,les:isDefined(t.les)&&0<t.les?t.les:0,lee:isDefined(t.lee)&&0<t.lee?t.lee:-1,fp:isDefined(t.fp)&&0<t.fp?t.fp:-1,fcp:isDefined(t.fcp)&&0<t.fcp?t.fcp:-1,lcp:isDefined(t.lcp)&&0<t.lcp?t.lcp:-1,imd:t.imd}},isDefined(t.url)&&""!==t.url.trim("")))if(isBrowser(setting.osType))(e=new Metric({info:t},WEBVIEWDATA)).info("url","").info("ic",!0).info("pvid").info("wpi"),pushQueueData(e.build(),!1);else{var e=stringify({h5:[{ent:now(),v:t,k:"h5"}]}),r=stringify({ent:now(),v:t});jsInternalBridge.log("setCustomH5performanceData=> "+r),log("setCustomH5performanceData=> "+r);try{isIOS(setting.osType)?window.webkit.messageHandlers.brsWKSetCustomH5performanceData.postMessage(r):isHOS(setting.osType)?window.bonreePrivateInterface.call(e):window.bonreePrivateInterface.webViewEventBus(e,setting.webviewID)}catch(e){}}},setCustomRouteChangeData:function(e){var t={};try{(t=JSON.parse(e)).d=1e3*Math.round(t.d)}catch(e){return void jsInternalBridge.log("setCustomRouteChangeData has error! ==>"+e)}var n=!0;n&&forEach(["tu","fu","rt","pu"],function(e){filterData(e,t[e],"setCustomRouteChangeData",MAX_LENGTH_1,!1)&&(t[e]=set_length(t[e],MAX_LENGTH_1))}),n&&forEach(["pt","fw"],function(e){if(filterData(e,t[e],"setCustomRouteChangeData",MAX_LENGTH_2,!1))return n=!1}),n&&forEach(["d","sta"],function(e){if(filterData(e,t[e],"setCustomRouteChangeData",null,!0))return n=!1}),n&&(void 0===t.fu&&null===t.fu?jsInternalBridge.log("setCustomRouteChangeData Parameter has a value or key [fu] that does not exist!"):(void 0!==t.al&&null!==t.al&&t.al.length>MAX_LENGTH_2&&(jsInternalBridge.log("setCustomRouteChangeData Parameter has a value or key [al] that the value length exceeds "+MAX_LENGTH_2+"!"),t.al=""),t.ent=1e3*(new Date).getTime(),(e=new Metric({info:t},ROUTE)).info("tu","").info("fu","").info("d",0).info("sta",0).info("pt","").info("rt","").info("pu","").info("fw","").info("ic",!0).info("ctp",2).info("pvid",isDefined(setting.pageViewId)?setting.pageViewId:""),isDefined(t.al)&&""!==t.al&&e.info("al",""),isDefined(window.performance)&&isDefined(window.performance.timing)&&e.info("ns",window.performance.timing.navigationStart),e=e.build(),jsInternalBridge.routeChangeData(stringify({ent:e.ent,v:e.data})),pushQueueData(e,!1)))},setCustomNetworkData:function(e){var t={};try{t=JSON.parse(e)}catch(e){return void jsInternalBridge.log("setCustomNetworkData has error! ==>"+e)}var n=!0;forEach(["tp","dt","ct","sslt","rt","rti","dti","ds","pt","rds"],function(e){if(filterData(e,t[e],"setCustomNetworkData",null,!0)||t[e]<0)return n=!1}),filterData("ti",t.ti,"setCustomNetworkData",null,!1)&&(n=!1),isDefined(t.ru)&&""!==t.ru&&(filterData("ru",t.ru,"setCustomNetworkData",MAX_LENGTH_1,!1)&&(t.ru=set_length(t.ru,MAX_LENGTH_1)),filterData("m",t.m,"setCustomNetworkData",MAX_LENGTH_1,!1)&&(t.m=set_length(t.m,MAX_LENGTH_1)),n)&&(t.ent=1e3*(new Date).getTime(),(e=new Metric({info:t},NET)).info("ru","").info("m","").info("ti","").info("tp",1e3).info("dt",1e3).info("ct",1e3).info("sslt",1e3).info("rt",1e3).info("rti",1e3).info("dti",1e3).info("ds",0).info("pt",0).info("rh","").info("rhe","").info("ret","").info("rds",0).info("id",uuid()).info("ic",!0).info("art",0).info("ec",200),pushQueueData(e.build(),!1))},setCustomEventStart:function(e,t,n,r,i){var a;arguments.length<1||5<arguments.length?log("setCustomEventStart自定义事件参数个数错误"):filterData("eventId",e,"setCustomEventStart",null,!1)?log("setCustomEventStart自定义事件参数不合法"):isDefined(i)&&7e3<JSON.stringify(i).length||includes$1(CustomEventExistQueue,e)||(CustomEventExistQueue.push(e),e&&(timeStampEvent[e]=dateNow(),correlationId[e]=buildViewID(),a={},t=set_length(t,MAX_LENGTH_2),r=set_length(r,7e3),a.eventId=isDefined(e)?e:"",256<e.length||0===e.length||(a.eventName=t,a.eventParam=r,a.eventLabel=set_length(n,256),a.info=filterInfo(i),(t=new Metric({info:a},CUSTOM_EVENT)).info("eventId","","i").info("t",1).info("d",0).info("ci",correlationId[e]),void 0!==a.eventName&&""!==a.eventName&&t.info("eventName","","n"),void 0!==a.eventLabel&&""!==a.eventLabel&&t.info("eventLabel","","l"),void 0!==a.eventParam&&""!==a.eventParam&&t.info("eventParam","","p"),void 0!==a.info&&a.info instanceof Object&&t.info("info","","info"),pushQueueData(t.build(),!1))))},setCustomEventEnd:function(t,e,n,r,i){if(arguments.length<1||5<arguments.length)log("setCustomEventEnd自定义事件参数个数错误");else if(filterData("eventId",t,"setCustomEventEnd",null,!1))log("setCustomEventEnd自定义事件参数不合法");else if(!(isDefined(i)&&7e3<JSON.stringify(i).length)&&includes$1(CustomEventExistQueue,t)){if(t){var a=0,o=(isDefined(dateNow)&&timeStampEvent&&isDefined(timeStampEvent[t])&&(a=dateNow()-timeStampEvent[t],a*=1e3),{}),e=set_length(e,MAX_LENGTH_2),r=set_length(r,7e3);if(o.eventId=isDefined(t)?t:"",256<t.length||0===t.length)return;o.eventName=e,o.eventParam=r,o.eventLabel=set_length(n,256),o.info=filterInfo(i);e=new Metric({info:o},CUSTOM_EVENT);e.info("eventId","","i").info("t",2).info("d",a).info("ci",correlationId[t]),void 0!==o.eventName&&""!==o.eventName&&e.info("eventName","","n"),void 0!==o.eventLabel&&""!==o.eventLabel&&e.info("eventLabel","","l"),void 0!==o.eventParam&&""!==o.eventParam&&e.info("eventParam","","p"),void 0!==o.info&&o.info instanceof Object&&e.info("info","","info"),pushQueueData(e.build(),!1)}CustomEventExistQueue=filter(CustomEventExistQueue,function(e){return e!==t}),delete timeStampEvent[t],delete correlationId[t]}}},eventList=[ROUTE_DATA,REQUEST_DATA,PAGE_DATA,RESOURCE_DATA,ERROR_DATA,ACTION_DATA,PAGE_INVISIBLE,PAGE_VISIBLE,TRACE_ACTION_DATA,CONSOLE_DATA,SPAN_DATA],eventListenList=[],pageResourceData={info:{ic:"false",wpi:{},wri:[]}},pageCreateTimeStamp=now(),time;function shouldIgnoreEvent(e){var t=e.t;return setting.ignoreResources&&t===RESOURCE_DATA||setting.ignoreUserEvents&&t===ACTION_DATA||void(t===REQUEST_DATA&&(t=e.p.info,setting.ignoreRequestParams&&(t.requestBody=""),setting.ignoreRequestHeaders)&&(t[REQUEST_HEADER]=""))}function webviewData(e,t){var n;1===t&&(clearTimeout(time),n=e.data,pageResourceData.info.wpi=n,pageResourceData.info.timestamp=1e3*n.ns,time=setTimeout(function(){pushQueueData(pageInfoData(pageResourceData),!1),clearTimeout(time)},5e3)),2===t&&(clearTimeout(time),forEach(e,function(e){pageResourceData.info.wri.push(e.data)}),pushQueueData(pageInfoData(pageResourceData),!1))}function pageInfoData(e){var t=new Metric(e,WEBVIEWDATA);return t.info("pvid").info("url").info("wpi").info("ic"),0<e.info.wri.length&&t.info("wri"),t.build()}function buildEventListener(e){notifierOn(e,function(e){if(!isDefined(e.p))return log("Data exception, discard",e);if(shouldIgnoreEvent(e))return log("Custom data filtering",e);var t=e.t,n=(isDefined(e.p)&&isDefined(e.p.info)&&!isDefined(e.p.info[PAGE_ID])&&(e.p.info[PAGE_ID]=setting.pageViewId,e.p.info[PAGE_URL]=sensitiveRuleHandle({p:{info:{url:isDefined(window.location)?window.location.href:""}}}).p.info.url),{}),r={};switch(t){case PAGE_DATA:WebviewPerformanceTimingTransformNet(r=domTimeLineInfor(e),e.p.info[PAGE_URL],e.p.info[PAGE_ID]),pageResourceData.info.pvid=e.p.info[PAGE_ID],pageResourceData.info.url=e.p.info[PAGE_URL],pageCreateTimeStamp=e.p.info[TIMESTAMP];break;case RESOURCE_DATA:r=resourceDataInfo(e);break;case REQUEST_DATA:n=(isDefined(e.type)?WebviewPerformanceToNet:requestAction)(e);break;case ERROR_DATA:e.p.info[PAGE_CREATE_TIME]=pageCreateTimeStamp||0,n=jsErrorAction(e);break;case ACTION_DATA:n=transformAction(e.p,1);break;case TRACE_ACTION_DATA:n=transformTraceAction(e.p);break;case ROUTE_DATA:n=spaPageRouterData(e);break;case CONSOLE_DATA:n=consoleData(e);break;case PAGE_INVISIBLE:case PAGE_VISIBLE:break;case SPAN_DATA:n=spanData(e);break;default:log("Packet type not defined")}t!==PAGE_DATA&&t!==RESOURCE_DATA||(isEmpty(r)||t!==PAGE_DATA?webviewData(r,2):webviewData(r,1)),isEmpty(n)||!isDefined(n.data)||isEmpty(n.data)||(log("pushQueueData excute"),pushQueueData(n,!1))})}function service(){buildEventListener(eventListenList=eventList)}function buildOrRemoveMoudles(e){var t=[],n=[],r=(forEach(e,function(e){switch(e){case NET:-1===eventListenList.indexOf(REQUEST_DATA)&&t.push(REQUEST_DATA),n.push(REQUEST_DATA);break;case WEBVIEWDATA:-1===eventListenList.indexOf(PAGE_DATA)&&t.push(PAGE_DATA),-1===eventListenList.indexOf(RESOURCE_DATA)&&t.push(RESOURCE_DATA),n.push(PAGE_DATA),n.push(RESOURCE_DATA);break;case ERROR:-1===eventListenList.indexOf(ERROR_DATA)&&t.push(ERROR_DATA),n.push(ERROR_DATA);break;case ACTION:-1===eventListenList.indexOf(ACTION_DATA)&&t.push(ACTION_DATA),n.push(ACTION_DATA);break;case VIEW:-1===eventListenList.indexOf(PAGE_INVISIBLE)&&t.push(PAGE_INVISIBLE),-1===eventListenList.indexOf(PAGE_VISIBLE)&&t.push(PAGE_VISIBLE),n.push(PAGE_INVISIBLE),n.push(PAGE_VISIBLE);break;case ROUTE:-1===eventListenList.indexOf(ROUTE_DATA)&&t.push(ROUTE_DATA),n.push(ROUTE_DATA);break;case CONSOLE$1:-1===eventListenList.indexOf(CONSOLE_DATA)&&t.push(CONSOLE_DATA),n.push(CONSOLE_DATA)}}),filter(eventListenList,function(e){return-1===n.indexOf(e)}));return isEmpty(r)||notifierRemove(r),eventListenList=filter(eventListenList,function(e){return-1===r.indexOf(e)}),t}function moduleConfig(e){var t;if(isDefined(e)&&isArray(e))return buildEventListener(t=buildOrRemoveMoudles(e)),eventListenList=eventListenList.concat(filter(t,function(e){return-1===eventListenList.indexOf(e)})),e;log("warn","moduleConfig has bad params")}function _createForOfIteratorHelper(e,t){var n,r,i,a,o="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(o)return r=!(n=!0),{s:function(){o=o.call(e)},n:function(){var e=o.next();return n=e.done,e},e:function(e){r=!0,i=e},f:function(){try{n||null==o.return||o.return()}finally{if(r)throw i}}};if(Array.isArray(e)||(o=_unsupportedIterableToArray(e))||t&&e&&"number"==typeof e.length)return o&&(e=o),a=0,{s:t=function(){},n:function(){return a>=e.length?{done:!0}:{done:!1,value:e[a++]}},e:function(e){throw e},f:t};throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _unsupportedIterableToArray(e,t){var n;if(e)return"string"==typeof e?_arrayLikeToArray(e,t):"Map"===(n="Object"===(n=Object.prototype.toString.call(e).slice(8,-1))&&e.constructor?e.constructor.name:n)||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?_arrayLikeToArray(e,t):void 0}function _arrayLikeToArray(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var sessionCache="br-session-cache",current="br-current-appid",SessionManager=function(){function t(e){_classCallCheck(this,t),isDefined(e)&&(get(sessionCache)&&this.queryAppid(e)||(this.currentAppid={appId:e,sessionID:uuid(),lastVisitedTime:(new Date).getTime()},window.bonreeRUM.sessionId=this.currentAppid.sessionID,set(sessionCache,JSON.stringify([this.currentAppid]),this.getSessionConfig())),set(current,e,this.getSessionConfig()))}return _createClass(t,[{key:"getSessionConfig",value:function(){var e={path:"/"};return setting.sessionDomain&&(e.domain=setting.sessionDomain),e}},{key:"queryAppid",value:function(e){var t,n=get(sessionCache),r=isJSON(n)&&JSON.parse(n)||[],i=_createForOfIteratorHelper(r);try{for(i.s();!(t=i.n()).done;){var a=t.value;if(a.appId===e){if((new Date).getTime()-a.lastVisitedTime>setting.sessionTimeout/1e3){setting.isFirstUpload=1,initUsdTime(),a.sessionID=uuid(),window.bonreeRUM.sessionId=a.sessionID;try{bonreeRUM.takeFullSnapshot()}catch(e){}}return a.lastVisitedTime=(new Date).getTime(),set(sessionCache,JSON.stringify(r),this.getSessionConfig()),a}}}catch(e){i.e(e)}finally{i.f()}return!1}},{key:"setAppid",value:function(e){setConfig(e);e=e.appId;if(isDefined(e)){if(get(sessionCache))if(this.queryAppid(e))set(current,e,this.getSessionConfig());else{var t=JSON.parse(get(sessionCache));setting.isFirstUpload=1,initUsdTime(),this.currentAppid={appId:e,sessionID:uuid(),lastVisitedTime:(new Date).getTime()},window.bonreeRUM.sessionId=this.currentAppid.sessionID;try{bonreeRUM.takeFullSnapshot()}catch(e){}t.unshift(this.currentAppid),20<=t.length&&t.pop(),set(sessionCache,JSON.stringify(t),this.getSessionConfig())}else{setting.isFirstUpload=1,initUsdTime(),this.currentAppid={appId:e,sessionID:uuid(),lastVisitedTime:(new Date).getTime()},window.bonreeRUM.sessionId=this.currentAppid.sessionID;try{bonreeRUM.takeFullSnapshot()}catch(e){}set(sessionCache,JSON.stringify([this.currentAppid]),this.getSessionConfig())}set(current,e,this.getSessionConfig())}}},{key:"getSession",value:function(){var e=get(current);return this.queryAppid(e)}}]),t}();function BonreeStart(e){if(window.bonreeRUM)return log("Bonree JS Agent running probe already exists",now(),!0),bonreeRUM.config(e),bonreeRUM;setConfig(e),init()}function checkConfigFun(){try{var e,t=document.querySelector("#BonreeAgent");isDefined(t)&&(e=t.getAttribute("data"),e=window.decodeURIComponent&&window.decodeURIComponent(e),isJSON(e)?(e=JSON.parse(e),isDefined(e.appId)||(log("Appid is a mandatory parameter "),log("BonreeAgent stops working")),setConfig(e),probability&&init()):log("config data error, BonreeAgent stops working"))}catch(e){log("config data error, BonreeAgent stops working")}}function init(){var e,t;if(probability)return autoFun(),window.bonreeRUM?(log("Bonree JS Agent running probe already exists",now(),!0),bonreeRUM):(e=dataEngine(),service(),init$2(),e.on([PAGE_READY,PAGE_LOAD],function(e){e.t===PAGE_READY?changeState(1):changeState(2)}),t=new SessionManager(setting.appId),e.sessionId=t.getSession().sessionID,bonreeRUM.version=version$1,bonreeRUM.setUserID=configUser,e.setExtraInfo=configUserExtraInfo,e.updateConfig=function(e){isDefined(window.stopBonreeRecord)&&(window.stopBonreeRecord(),window.stopBonreeRecordUpload=!0),flushData(),new SessionManager(setting.appId).setAppid(e)},e.getSession=function(){return new SessionManager(setting.appId).getSession()},bonreeRUM.moduleConfig=moduleConfig,void 0===window.bonreePrivateInterface&&(window.bonreePrivateInterface={log:function(e){},webviewPerformanceTimingEvent:function(e){},webviewJSErrorEvent:function(e){},ajaxPerformanceTimingEvent:function(e){},actionEvent:function(e){},actionPage:function(e){},routeChangeData:function(e){},networkData:function(e){},setExtraInfo:function(e){},setUserID:function(e){},setCustomEvent:function(e,t,n){},setCustomLog:function(e,t){},setCustomMetric:function(e,t,n){},setCustomException:function(e,t,n){},setCustomPageStart:function(e,t){},setCustomPageEnd:function(e,t){},setCustomH5performanceData:function(e){},setCustomRouteChangeData:function(e){},setCustomEventStart:function(e,t,n,r){},setCustomEventEnd:function(e,t,n,r){},setCustomSpeedTest:function(e,t){}}),window.bonreeJsBridge={setExtraInfo:function(e){7e3<JSON.stringify(e).length||(e=e&&set_64_keys(e,64)||e,isDefined(e)&&(configUserExtraInfo(e),jsBridge.setExtraInfo(e)))},setUserID:function(e){e&&256<e.length||/^[a-zA-Z0-9:_-\s@./]+$/.test(e)&&(configUser(e),jsBridge.setUserID(e))},setCustomEventWithLabel:function(e,t,n,r,i){jsBridge.setCustomEventWithLabel(e,t,n,r,i)},setCustomEvent:function(e,t,n,r){jsBridge.setCustomEvent(e,t,n,r)},setCustomLog:function(e,t){jsBridge.setCustomLog(e,t)},setCustomMetric:function(e,t,n){jsBridge.setCustomMetric(e,t,n)},setCustomException:function(e,t,n){jsBridge.setCustomException(e,t,n)},setCustomPageStart:function(e,t){jsBridge.setCustomPageStart(e,t)},setCustomPageEnd:function(e,t){jsBridge.setCustomPageEnd(e,t)},setCustomH5performanceData:function(e){jsBridge.setCustomH5performanceData(e)},setCustomRouteChangeData:function(e){jsBridge.setCustomRouteChangeData(e)},setCustomNetworkData:function(e){jsBridge.setCustomNetworkData(e)},setCustomEventStart:function(e,t,n,r,i){jsBridge.setCustomEventStart(e,t,n,r,i)},setCustomEventEnd:function(e,t,n,r,i){jsBridge.setCustomEventEnd(e,t,n,r,i)},setCustomSpeedTest:function(e,t){jsBridge.setCustomSpeedTest(e,t)},startSpan:function(e,t){return new SpanStruct(e,t)}},(bonreeRUM=extend(bonreeRUM,bonreeJsBridge)).$$standard=!0,log("Bonree JS Agent Started By Timestamp",now(),!0),e)}function autoFun(){function n(){for(var e=0;e<a.length;e++)a[e]()}var r=!(!window.attachEvent||window.opera),i=/webkit\/(\d+)/i.test(navigator.userAgent)&&RegExp.$1<525,a=[],o=document;o.ready=function(e){if(!r&&!i&&o.addEventListener)return o.addEventListener("DOMContentLoaded",e,!1);var t;1<a.push(e)||(r?function(){try{o.documentElement.doScroll("left"),n()}catch(e){setTimeout(arguments.callee,0)}}():i&&(t=setInterval(function(){/^(loaded|complete)$/.test(o.readyState)&&(clearInterval(t),n())},0)))}}return window.setJsBridge=function(e){window.bonreePrivateInterface=e},checkConfigFun(),window.BonreeAgent||(window.BonreeAgent={init:init}),exports.BonreeNG=BonreeNG,exports.BonreeReact=BonreeReact,exports.BonreeStart=BonreeStart,exports.BonreeUiNG=BonreeUiNG,exports.BonreeVUE=BonreeVUE,exports.setExtraInfo=configUserExtraInfo,exports.setUserID=configUser,exports}({});
