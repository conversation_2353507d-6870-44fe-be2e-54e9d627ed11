<!doctype html>
<html lang="en">
<head>
  <meta charset="utf-8">
  <title>管理驾驶舱 - Caerus</title>
  <base id="caerus-base" href="/" />
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <script>
    // 区分测试环境以及线上环境
    (window).global = window;
    (function() {
      const hasPath = (searchString) => {
        return window.location.href.indexOf(searchString) > -1;
      }

      const isLocal = () => hasPath('localhost:4500');
      const isTest = () => hasPath('test-caerus');
      const isPreview = () => hasPath('preonline');
      const isEmulator = () => hasPath('aliyun-caerus');
      const isProd = () => hasPath('caerus.didapinche.com');
      const base = document.getElementById("caerus-base");

      base.setAttribute("href", (
        isPreview() ? '/preonline/' :
        isProd() ? '/' :
        isTest() ? '/test-caerus/' :
        isEmulator() ? '/aliyun-caerus/' :
        '/'
      ));
    })();

    (function() {
      const supportedBrowsers = /((CPU[ +]OS|iPhone[ +]OS|CPU[ +]iPhone|CPU IPhone OS)[ +]+(14[_.]5|14[_.]([6-9]|\d{2,})|14[_.]8|14[_.](9|\d{2,})|(1[5-9]|[2-9]\d|\d{3,})[_.]\d+|15[_.]0|15[_.]([1-9]|\d{2,})|(1[6-9]|[2-9]\d|\d{3,})[_.]\d+)(?:[_.]\d+)?)|(Opera Mini(?:\/att)?\/?(\d+)?(?:\.\d+)?(?:\.\d+)?)|(Opera\/.+Opera Mobi.+Version\/(64\.0|64\.([1-9]|\d{2,})|(6[5-9]|[7-9]\d|\d{3,})\.\d+))|(Opera\/(64\.0|64\.([1-9]|\d{2,})|(6[5-9]|[7-9]\d|\d{3,})\.\d+).+Opera Mobi)|(Opera Mobi.+Opera(?:\/|\s+)(64\.0|64\.([1-9]|\d{2,})|(6[5-9]|[7-9]\d|\d{3,})\.\d+))|((?:Chrome).*OPR\/(79\.0|79\.([1-9]|\d{2,})|([8-9]\d|\d{3,})\.\d+)\.\d+)|(SamsungBrowser\/(14\.0|14\.([1-9]|\d{2,})|(1[5-9]|[2-9]\d|\d{3,})\.\d+))|(Edge\/(94(?:\.0)?|94(?:\.([1-9]|\d{2,}))?|(9[5-9]|\d{3,})(?:\.\d+)?))|((Chromium|Chrome)\/(94\.0|94\.([1-9]|\d{2,})|(9[5-9]|\d{3,})\.\d+)(?:\.\d+)?)|(Version\/(14\.1|14\.([2-9]|\d{2,})|(1[5-9]|[2-9]\d|\d{3,})\.\d+|15\.0|15\.([1-9]|\d{2,})|(1[6-9]|[2-9]\d|\d{3,})\.\d+)(?:\.\d+)? Safari\/)|(Firefox\/(92\.0|92\.([1-9]|\d{2,})|(9[3-9]|\d{3,})\.\d+)\.\d+)|(Firefox\/(92\.0|92\.([1-9]|\d{2,})|(9[3-9]|\d{3,})\.\d+)(pre|[ab]\d+[a-z]*)?)/;
      const browserSupported = supportedBrowsers.test(navigator.userAgent)

      if (!browserSupported) {
        console.error(navigator.userAgent);
        new Error('浏览器版本过低，请升级浏览器');
      }
    })();
  </script>
  <link rel="icon" type="image/x-icon" href="favicon.ico">
  <script src="assets/components/browser-warning.js"></script>
</head>
<body>
  <browser-warning></browser-warning>
  <app-root></app-root>
</body>
<!-- <script type="text/javascript" src="assets/javascript/BonreeSDK_JS.min.js" data="%7B%22reqHeaderTraceKey%22%3A%5B%22tracestate%22%2C%22traceparent%22%5D%2C%22uploadAddrHttps%22%3A%22https%3A%2F%2Fbonree.didapinche.com%2FRUM%2Fupload%22%2C%22mc%22%3A%5B%7B%22n%22%3A%22network%22%2C%22cs%22%3A%7B%22fc%22%3A0%7D%7D%5D%2C%22appId%22%3A%22856161c0bee34a9b81c873f977ed7fff%22%2C%22uploadAddrHttp%22%3A%22http%3A%2F%2Fbonree.didapinche.com%2FRUM%2Fupload%22%2C%22userdefine%22%3A%22%5B%7B%5C%22type%5C%22%3A4%2C%5C%22rule%5C%22%3A%5C%22caerus.auth.username.py%5C%22%7D%5D%22%2C%22respHeaderTraceKey%22%3A%5B%22traceresponse%22%2C%22x-br-response%22%5D%2C%22brss%22%3Afalse%7D" id="BonreeAgent" ></script> -->
<!-- <script type="text/javascript" src="assets/javascript/BonreeSDK_JS.min.js" data="%7B%22reqHeaderTraceKey%22%3A%5B%22tracestate%22%2C%22traceparent%22%5D%2C%22uploadAddrHttps%22%3A%22https%3A%2F%2Fbonree.didapinche.com%2FRUM%2Fupload%22%2C%22mc%22%3A%5B%7B%22n%22%3A%22network%22%2C%22cs%22%3A%7B%22fc%22%3A0%7D%7D%5D%2C%22appId%22%3A%22856161c0bee34a9b81c873f977ed7fff%22%2C%22uploadAddrHttp%22%3A%22http%3A%2F%2Fbonree.didapinche.com%2FRUM%2Fupload%22%2C%22userdefine%22%3A%22%5B%7B%5C%22type%5C%22%3A4%2C%5C%22rule%5C%22%3A%5C%22caerus.auth.username.py%5C%22%7D%5D%22%2C%22respHeaderTraceKey%22%3A%5B%22traceresponse%22%2C%22x-br-response%22%5D%2C%22brss%22%3Afalse%7D" id="BonreeAgent" ></script> -->
</html>
