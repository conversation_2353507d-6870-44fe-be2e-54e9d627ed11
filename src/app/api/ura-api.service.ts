import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

import { URA_API_URL } from '@common/const';
import { Get, Cache, Param, BaseUri } from '@common/decorator';
import { ResultBody } from '@common/interface'
import { BaseApiService } from '@common/service';


export interface UserInfo {
  avatarUrl: string;
  mobile: string;
  name: string;
  cn: string;
  email: string;
}


@Injectable({
  providedIn: 'root'
})
@BaseUri(`${URA_API_URL}`)
export class UraApiService extends BaseApiService {

  @Cache()
  @Get('/user/privilege/menu/v2')
  getMenu(
    @Param('userName') userName: string,
    @Param('projectCode') projectCode: 'Caerus'
  ): Observable<ResultBody<string>> {
    return null;
  }


  @Cache()
  @Get('/external/get/all/users/info')
  fetchAllUsersInfo(): Observable<ResultBody<UserInfo[]>> {
    return null;
  }

}
