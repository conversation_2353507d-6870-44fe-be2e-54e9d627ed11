import { Injectable, signal } from '@angular/core';
import { Observable } from 'rxjs';

import { ResultBody } from '@common/interface';
import { Get, Param, Cache } from '@common/decorator';
import { URA_API_URL, isDev } from '@common/const';
import { BaseApiService, Role } from '@common/service';

export interface CurrentUser {
  status: string;
  describe: string;
  user: string;
  ts: string;
  avatar: string;
}

interface UraButton {
  name: string;
  id: number;
  url: string;
}

export interface UraMenu {
  backUrl: any[];
  button: UraButton[];
  childMenu: UraMenu[];
  frontButton: UraButton[];
  id: number;
  name: string;
  sort: number;
  url: string;
}


@Injectable({
  providedIn: 'root',
})
export class UserInfoService extends BaseApiService {
  privateName = isDev() ? 'M7ijxGjmiJ5Kxb0sqGmkVw%3D%3D' : null;
  ts = isDev() ? 'ZDkC6JHLjTq0AZVJOGJGP2ly4ilAnHyjOdGkQDkcFfU%3D' : null;
  menu = signal<UraMenu[]>([]);
  config = signal<any[]>([]);

  @Cache()
  @Get(`/current_user`)
  fetchCurrentUser(): Observable<CurrentUser> {
    return null;
  }

  @Cache()
  @Get(`${URA_API_URL}/security/decode`)
  fetchUserName(
    @Param('userName') userName: string,
    @Param('type') type: 'cn' | 'en'
  ): Observable<ResultBody<string>> {
    return null;
  }

  /**
   * 
   * @param {string} userName privateName
   * @returns 
   */
  @Cache()
  @Get(`${URA_API_URL}/security/role/info/v2`)
  getRoleInfo(@Param('userName') userName: string): Observable<ResultBody<Role[]>> {
    return null;
  }
}
