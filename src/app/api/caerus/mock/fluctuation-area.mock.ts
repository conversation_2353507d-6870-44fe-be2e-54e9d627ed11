export const FluctuationAreaMock = {
  headerNames: null,
  headers: {
    city_bio_region: {
      aliasName: '城市归属区域',
      dateFilter: 0,
      valueType: null,
      columnType: 'dimension',
      dataType: 'string',
      dataRound: null,
      dataDecimalRound: null,
      dataUnit: null,
      extendName: 'city_bio_region',
      diffName: null,
      ratioName: null,
      showName: null,
    },
    c_book_reply_ord_rate: {
      aliasName: '(顺)下单接单率',
      dateFilter: 0,
      valueType: null,
      columnType: 'metrics',
      dataType: 'decimal',
      dataRound: 18,
      dataDecimalRound: 4,
      dataUnit: '%',
      extendName: 'c_book_reply_ord_rate',
      diffName: 'c_book_reply_ord_rate_DIFF',
      ratioName: 'c_book_reply_ord_rate_DIFF_RATIO',
      showName: '下单接单率',
    },
    c_reply_finish_ord_rate: {
      aliasName: '(顺)接单完单率',
      dateFilter: 0,
      valueType: null,
      columnType: 'metrics',
      dataType: 'decimal',
      dataRound: 18,
      dataDecimalRound: 4,
      dataUnit: '%',
      extendName: 'c_reply_finish_ord_rate',
      diffName: 'c_reply_finish_ord_rate_DIFF',
      ratioName: 'c_reply_finish_ord_rate_DIFF_RATIO',
      showName: '接单完单率',
    },
    c_davg_book_ord_cnt: {
      aliasName: '(顺)日均下单量',
      dateFilter: 0,
      valueType: null,
      columnType: 'metrics',
      dataType: 'bigint',
      dataRound: 18,
      dataDecimalRound: 4,
      dataUnit: '个',
      extendName: 'c_davg_book_ord_cnt',
      diffName: 'c_davg_book_ord_cnt_DIFF',
      ratioName: 'c_davg_book_ord_cnt_DIFF_RATIO',
      showName: '下单量',
    },
    c_davg_finish_ord_cnt: {
      aliasName: '(顺)日均完单量',
      dateFilter: 0,
      valueType: null,
      columnType: 'metrics',
      dataType: 'bigint',
      dataRound: 18,
      dataDecimalRound: 4,
      dataUnit: '无单位',
      extendName: 'c_davg_finish_ord_cnt',
      diffName: 'c_davg_finish_ord_cnt_DIFF',
      ratioName: 'c_davg_finish_ord_cnt_DIFF_RATIO',
      showName: '完单量',
    },
    c_davg_reply_ord_cnt: {
      aliasName: '(顺)日均接单量',
      dateFilter: 0,
      valueType: null,
      columnType: 'metrics',
      dataType: 'bigint',
      dataRound: 18,
      dataDecimalRound: 4,
      dataUnit: '个',
      extendName: 'c_davg_reply_ord_cnt',
      diffName: 'c_davg_reply_ord_cnt_DIFF',
      ratioName: 'c_davg_reply_ord_cnt_DIFF_RATIO',
      showName: '接单量',
    },
  },
  headerTypes: null,
  data: [
    {
      c_davg_book_ord_cnt_DIFF: '-5532',
      c_davg_finish_ord_cnt: '4982',
      c_davg_finish_ord_cnt_DIFF: '-1348',
      c_davg_reply_ord_cnt: '12643',
      c_davg_reply_ord_cnt_DIFF_RATIO: '-15.99',
      c_davg_book_ord_cnt_DIFF_RATIO: '-14.07',
      c_book_reply_ord_rate: '0.3743',
      c_reply_finish_ord_rate: '0.3941',
      c_davg_book_ord_cnt: '33777',
      c_reply_finish_ord_rate_DIFF: '-0.0265',
      c_book_reply_ord_rate_DIFF_RATIO: '-2.25',
      c_davg_reply_ord_cnt_DIFF: '-2407',
      c_book_reply_ord_rate_DIFF: '-0.0086',
      c_reply_finish_ord_rate_DIFF_RATIO: '-6.30',
      c_davg_finish_ord_cnt_DIFF_RATIO: '-21.30',
      city_bio_region: '全国',
      c_davg_finish_ord_cnt_FLUCTUATION_RATIO: '-21.30',
    },
    {
      c_davg_book_ord_cnt_DIFF: '-1864',
      c_davg_finish_ord_cnt: '1713',
      c_davg_finish_ord_cnt_DIFF: '-504',
      c_davg_reply_ord_cnt: '3964',
      c_davg_reply_ord_cnt_DIFF_RATIO: '-17.33',
      c_davg_book_ord_cnt_DIFF_RATIO: '-18.04',
      city_bio_region: '江浙沪',
      c_book_reply_ord_rate: '0.4681',
      c_reply_finish_ord_rate: '0.4322',
      c_davg_book_ord_cnt: '8468',
      c_reply_finish_ord_rate_DIFF: '-0.0301',
      c_book_reply_ord_rate_DIFF_RATIO: '0.86',
      c_davg_reply_ord_cnt_DIFF: '-831',
      c_book_reply_ord_rate_DIFF: '0.0040',
      c_reply_finish_ord_rate_DIFF_RATIO: '-6.51',
      c_davg_finish_ord_cnt_DIFF_RATIO: '-22.73',
      c_davg_finish_ord_cnt_FLUCTUATION_RATIO: '-7.96',
    },
    {
      c_davg_book_ord_cnt_DIFF: '-394',
      c_davg_finish_ord_cnt: '725',
      c_davg_finish_ord_cnt_DIFF: '-163',
      c_davg_reply_ord_cnt: '2230',
      c_davg_reply_ord_cnt_DIFF_RATIO: '-10.91',
      c_davg_book_ord_cnt_DIFF_RATIO: '-5.11',
      city_bio_region: '广东',
      c_book_reply_ord_rate: '0.3046',
      c_reply_finish_ord_rate: '0.3249',
      c_davg_book_ord_cnt: '7321',
      c_reply_finish_ord_rate_DIFF: '-0.0301',
      c_book_reply_ord_rate_DIFF_RATIO: '-6.10',
      c_davg_reply_ord_cnt_DIFF: '-273',
      c_book_reply_ord_rate_DIFF: '-0.0198',
      c_reply_finish_ord_rate_DIFF_RATIO: '-8.48',
      c_davg_finish_ord_cnt_DIFF_RATIO: '-18.36',
      c_davg_finish_ord_cnt_FLUCTUATION_RATIO: '-2.58',
    },
    {
      c_davg_book_ord_cnt_DIFF: '-666',
      c_davg_finish_ord_cnt: '633',
      c_davg_finish_ord_cnt_DIFF: '-135',
      c_davg_reply_ord_cnt: '1475',
      c_davg_reply_ord_cnt_DIFF_RATIO: '-14.29',
      c_davg_book_ord_cnt_DIFF_RATIO: '-15.37',
      city_bio_region: '京津冀',
      c_book_reply_ord_rate: '0.4024',
      c_reply_finish_ord_rate: '0.4289',
      c_davg_book_ord_cnt: '3667',
      c_reply_finish_ord_rate_DIFF: '-0.0174',
      c_book_reply_ord_rate_DIFF_RATIO: '1.31',
      c_davg_reply_ord_cnt_DIFF: '-246',
      c_book_reply_ord_rate_DIFF: '0.0052',
      c_reply_finish_ord_rate_DIFF_RATIO: '-3.90',
      c_davg_finish_ord_cnt_DIFF_RATIO: '-17.58',
      c_davg_finish_ord_cnt_FLUCTUATION_RATIO: '-2.13',
    },
    {
      c_davg_book_ord_cnt_DIFF: '-626',
      c_davg_finish_ord_cnt: '604',
      c_davg_finish_ord_cnt_DIFF: '-191',
      c_davg_reply_ord_cnt: '1581',
      c_davg_reply_ord_cnt_DIFF_RATIO: '-20.19',
      c_davg_book_ord_cnt_DIFF_RATIO: '-15.46',
      city_bio_region: '山东',
      c_book_reply_ord_rate: '0.4619',
      c_reply_finish_ord_rate: '0.3821',
      c_davg_book_ord_cnt: '3423',
      c_reply_finish_ord_rate_DIFF: '-0.0191',
      c_book_reply_ord_rate_DIFF_RATIO: '-5.60',
      c_davg_reply_ord_cnt_DIFF: '-400',
      c_book_reply_ord_rate_DIFF: '-0.0274',
      c_reply_finish_ord_rate_DIFF_RATIO: '-4.76',
      c_davg_finish_ord_cnt_DIFF_RATIO: '-24.03',
      c_davg_finish_ord_cnt_FLUCTUATION_RATIO: '-3.02',
    },
    {
      c_davg_book_ord_cnt_DIFF: '-728',
      c_davg_finish_ord_cnt: '547',
      c_davg_finish_ord_cnt_DIFF: '-204',
      c_davg_reply_ord_cnt: '1260',
      c_davg_reply_ord_cnt_DIFF_RATIO: '-22.22',
      c_davg_book_ord_cnt_DIFF_RATIO: '-18.70',
      city_bio_region: '川渝',
      c_book_reply_ord_rate: '0.3981',
      c_reply_finish_ord_rate: '0.4343',
      c_davg_book_ord_cnt: '3166',
      c_reply_finish_ord_rate_DIFF: '-0.0294',
      c_book_reply_ord_rate_DIFF_RATIO: '-4.33',
      c_davg_reply_ord_cnt_DIFF: '-360',
      c_book_reply_ord_rate_DIFF: '-0.0180',
      c_reply_finish_ord_rate_DIFF_RATIO: '-6.34',
      c_davg_finish_ord_cnt_DIFF_RATIO: '-27.16',
      c_davg_finish_ord_cnt_FLUCTUATION_RATIO: '-3.22',
    },
    {
      c_davg_book_ord_cnt_DIFF: '-263',
      c_davg_finish_ord_cnt: '334',
      c_davg_finish_ord_cnt_DIFF: '-79',
      c_davg_reply_ord_cnt: '631',
      c_davg_reply_ord_cnt_DIFF_RATIO: '-18.89',
      c_davg_book_ord_cnt_DIFF_RATIO: '-17.34',
      city_bio_region: '东北三省',
      c_book_reply_ord_rate: '0.5030',
      c_reply_finish_ord_rate: '0.5300',
      c_davg_book_ord_cnt: '1254',
      c_reply_finish_ord_rate_DIFF: '-0.0004',
      c_book_reply_ord_rate_DIFF_RATIO: '-2.01',
      c_davg_reply_ord_cnt_DIFF: '-147',
      c_book_reply_ord_rate_DIFF: '-0.0103',
      c_reply_finish_ord_rate_DIFF_RATIO: '-0.08',
      c_davg_finish_ord_cnt_DIFF_RATIO: '-19.13',
      c_davg_finish_ord_cnt_FLUCTUATION_RATIO: '-1.25',
    },
  ],
  compareData: [
    {
      c_book_reply_ord_rate: '0.3829',
      c_reply_finish_ord_rate: '0.4206',
      c_davg_book_ord_cnt: '39309',
      c_davg_finish_ord_cnt: '6330',
      c_davg_reply_ord_cnt: '15050',
    },
    {
      city_bio_region: '广东',
      c_book_reply_ord_rate: '0.3244',
      c_reply_finish_ord_rate: '0.3550',
      c_davg_book_ord_cnt: '7715',
      c_davg_finish_ord_cnt: '888',
      c_davg_reply_ord_cnt: '2503',
    },
    {
      city_bio_region: '京津冀',
      c_book_reply_ord_rate: '0.3972',
      c_reply_finish_ord_rate: '0.4463',
      c_davg_book_ord_cnt: '4333',
      c_davg_finish_ord_cnt: '768',
      c_davg_reply_ord_cnt: '1721',
    },
    {
      city_bio_region: '山东',
      c_book_reply_ord_rate: '0.4893',
      c_reply_finish_ord_rate: '0.4012',
      c_davg_book_ord_cnt: '4049',
      c_davg_finish_ord_cnt: '795',
      c_davg_reply_ord_cnt: '1981',
    },
    {
      city_bio_region: '东北三省',
      c_book_reply_ord_rate: '0.5133',
      c_reply_finish_ord_rate: '0.5304',
      c_davg_book_ord_cnt: '1517',
      c_davg_finish_ord_cnt: '413',
      c_davg_reply_ord_cnt: '778',
    },
    {
      city_bio_region: '江浙沪',
      c_book_reply_ord_rate: '0.4641',
      c_reply_finish_ord_rate: '0.4623',
      c_davg_book_ord_cnt: '10332',
      c_davg_finish_ord_cnt: '2217',
      c_davg_reply_ord_cnt: '4795',
    },
    {
      city_bio_region: '川渝',
      c_book_reply_ord_rate: '0.4161',
      c_reply_finish_ord_rate: '0.4637',
      c_davg_book_ord_cnt: '3894',
      c_davg_finish_ord_cnt: '751',
      c_davg_reply_ord_cnt: '1620',
    },
  ],
  totalValue: 6,
  pageValue: 1,
  id: 346338,
};
