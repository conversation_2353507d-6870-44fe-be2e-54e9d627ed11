export interface TrendVo {

  /** 年份 */
  year: string;

  /** 月份 */
  month: string;

  /** 指标值 */
  value: number;

}


export class BaseInput {

  /** 数据月份 */
  public date: string;

  /** 来源
   * - 总体 (all)
   * - 腾讯外输(tencent)
   */
  public source?: 'all' | 'tencent' | string;

  /** 地域 */
  public area?: string;

  /** 订单类型 */
  public rideType?: 'ALL' | '城际' | '市内' | string;

  
}


export interface BaseOutput {
  /**
   * 月日均值
   */
  value: number;
  
  /**
   * 上月环比值
   */
  lastMonthValue: number;

  /**
   * 去年同比值
   */
  lastYearValue: number;

  /**
   * 指标说明
   */
  metricsDescription: string;

  /**
   * 指标名称
   */
  metricsName: string;

  /**
   * 指标key
   */
  metricsKey: string;

  /**
   * 指标别名
   */
  aliasName: string;

  /**
   * 平均方式
   */
  avgType: number;


  /**
   * 平均方式枚举值
   */
  avgTypeValue: number;

  /**
   * 小数点位保留规则 0(取整)，2 (保留2位小数), 101 (转化为百分比后，保留1位小数)
   */
  roundType: number;

  /** 同比增长 */
  yearOnYearGrowth: number;

  /** 环比增长 */
  quarterOnQuarterGrowth: number;

}


export type TrendDate = 0 | 1 | 2;

export const TrendDateOptions: Array<{ label: string, value: TrendDate }> = [
  { label: '过去一年', value: 1 },
  { label: '过去两年', value: 2 },
  { label: '上线至今', value: 0 },
];

export class BaseTrendInput {

  /** 数据月份 */
  public date: string;

  /** 时间参数 (默认1)
   * - 上线至今 0
   * - 过去一年 1
   * - 过去两年 2
   */
  public trendDate?: TrendDate;

  /** 地域 */
  public area?: string;

  /** 订单类型 */
  public rideType?: string;

}


export interface BaseDataOutput {

  /** 平均方式 */
  avgType: string;
  
  /** 指标别名 */
  aliasName: string;
  
  /** 达成率 */
  achievement: number;

  /** 上月环比月日均值 */
  lastMonthValue: number;

  /** 去年同比月日均值 */
  lastYearValue: number;

  /** 指标说明 */
  metricsDescription: string;

  /** 指标名称 */
  metricsName: string;

  /** 同比增长 */
  quarterOnQuarterGrowth: number;

  /** 目标值 */
  targetValue: number;

  /** 指标趋势图 */
  trendVo: TrendVo[];

  /** 月日均值 */
  value: number;

  /** 当月整体值【用来跟目标值做完成率,使用前要判断是否为null】 */
  sumValue: number;

  /** 环比增长 */
  yearOnYearGrowth: number;


  /** 是否为百分比 */
  percent?: boolean;

  label?: string;

  targetLabel?: string;

  /** 单位 */
  unit?: string | null;

}


export interface BaseTrendVo {

  /** 年份 */
  year: string;

  /** 月份 */
  month: string;
  
}


export interface FluctuationInput extends BaseInput {

  /** 波动类型 1： 区域波动 2： 城市波动 3： 城际|市内波动 */
  type: string;

  /** 日期维度 month 月环比、year 年同比 */
  dateType: string;

}
