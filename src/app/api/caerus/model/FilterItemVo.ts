import { DimensionValueVo } from './DimensionValueVo';

/** @name 运算标识 条件运算公式 [>,<,=,>=,<=,!=,not null,in],逻辑运算公式 [and,or] */
export type Condition = '>' | '<' | '=' | '>=' | '<=' | '!=' | 'not null' | 'in' | 'and' | 'or';

/** @name 筛选值类型 指标(metrics)/维度(dimension) */
export type ValueType = 'metrics' | 'dimension';

/** @name 运算类型 逻辑运算 1, 条件运算 2 */
export type ConditionType = 1 | 2;

/**
 * where筛选条件
 */
export class FilterItemVo {

  /** @name 运算类型 逻辑运算 1, 条件运算 2 */
  conditionType: ConditionType = 2;
  
  /** @name 运算标识 条件运算公式 [>,<,=,>=,<=,!=,not null,in],逻辑运算公式 [and,or] */
  condition: Condition = '=';

  /// region 条件运算时生效
  
  /** 指标id/维度id */
  id: number = null;
  
  /** 维度英文名 */
  extendName: string;

  /** 筛选值 */
  value: DimensionValueVo[] = [];
  
  /** @name 筛选值类型 指标(metrics)/维度(dimension) */
  valueType: ValueType = null;

  /** @name 指标筛选类型 在指标筛选时用 */
  predefineCompareType: string;

  /** @name 子条件 [只有逻辑运算才支持] */
  subFilter: Partial<FilterItemVo>[];

  constructor(props?: Partial<FilterItemVo>) {
    if (props) {
      Object.assign(this, { ...props });
    }
  }

}