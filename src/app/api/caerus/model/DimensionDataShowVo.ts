import { DimensionValueMenuVo } from "./DimensionValueMenuVo";

/**
 * 维度配置返回对象
 */
export class DimensionDataShowVo {

  /** 
   * @name 唯一标记
   */
  keyName: string;

  /** 
   * @name 英文名称
   */
  extendName: string;

  /** 
   * @name 展示别名
   */
  showName: string;

  /** 
   * @name 别名
   */
  aliasName: string;

  /** 
   * @name 维度口径说明
   */
  bizExpression: string;

  /** 
   * @name 维度类型
   * - 标准维 1
   * - 杂项维 2
   */
  type: number;

  /** 
   * @name 默认展示数据
   */
  values: DimensionValueMenuVo[];

  /**
   * @name 分组
   */
  groupName: string;

  /** 
   * @name 类型
   * - 分组（group）
   * - 数据数组（data）
   */
  nodeType: string;


  data: DimensionDataShowVo[];

}