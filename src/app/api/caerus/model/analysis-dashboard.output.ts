export interface AnalysisDashboardOutput {
  id: number;

  /**
   * 名称
   */
  dashboardName: string;

  /**
   * 说明
   */
  dashboardInfo: string;

  /**
   * 权限类型 1 公共，2 私有
   */
  shareType: 1 | 2;

  /**
   * 是否删除 0 未删除，1 已删除，默认0
   */
  isDeleted: number;

  /**
   * 创建人
   */
  createBy: string;

  /**
   * 最近操作人
   */
  updateBy: string;

  /**
   * 创建人
   */
  createByCn: string;

  /**
   * 最近操作人
   */
  updateByCn: string;

  /**
   * 创建时间
   */
  createTime: string;

  /**
   * 最近更新时间
   */
  updateTime: string;

  /**
   * 主题名称
   */
  subjectName: string;

  /**
   * 主题ID
   */
  subjectId: number;

  records: any;

  pages: number;

  total: number;
}
