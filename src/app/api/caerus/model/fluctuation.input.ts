import { FilterVo, QueryDt } from '@api/query-engine/model';

/** 波动诊断基础入参 */
export interface FluctuationBaseInput {

  /** 时间分区查询类型【日:dt; 年月:ym; 年周:yw; 年季:yq;默认是日】 */
  dtType: string;

  /** 时间分区查询【必传参数】 */
  dt: QueryDt;

  /** 时间对比分区查询 */
  compareDt: QueryDt;

  /** 区域筛选值 */
  // areaFilter: DimensionValueVo[];

}

/** 波动区域入参 */
export interface FluctuationAreaInput extends FluctuationBaseInput {

  /** 视角【book 下单，finish 完单】 */
  view?: string;

  /** 区域参数 【大区 city_bio_region，省份 province_name，城市 city_name】 */
  area: string;

  /** 数据类型 【业务主题 subject，转化链路 transform】 */
  dataType: string;

  /** 区域参数 【大区 city_bio_region，省份 province_name，城市 city_name，top20城市 is_top20】 */
  filterVo: FilterVo;

}