/** 指标菜单对象 */
export interface MetricsMenuGroupVo {

  /** 分组名称 */
  groupName: string;

  /** 指标列表 */
  subMetric: MetricsMenuVo[];

}


/** 指标菜单对象 */
export interface MetricsMenuVo {

  /** 
   * @name 英文名称
   */
  extendName: string;

  /** 
   * @name 指标别名
   */
  aliasName: string;

  showName?: string;

  /**
   * @name 类型
   * 1. 原子
   * 2. 派生
   * 3. 复合
   */
  type: number;

  /** 
   * @name 指标说明
   */
  bizExpression: string;

  dataUnit: string;

  /** 
   * @name 标签名称
   */
  tagName: string;

  /** 
   * @name [自选维度指标新增字段] 默认推荐指标 
   * - 1 推荐指标
   * - 0 不是
   */
  recommend: 0 | 1 | number;

  /**
   * @name [自选维度指标新增字段] 是否用户选中，
   * - 1 选中
   * - 其他 未选中
   */
  display?: 0 | 1 | number;
  
  /**
   * @name [自选维度指标新增字段] 指标展示顺序
   */
  displayOrder?: number;

  /**
   * @name [自选维度指标新增字段] 标签排序
   */
  tagOrder: number;
  tagOutOrder?: number;

  metricsType?: string;

  classTag?: string;
  
  bizTag?: string;

  card?: string;

  color?: string;

  auth?: boolean;

  compareDiff?: string;

  dtQuery?: boolean;
  // checked?: boolean;

}


export interface MetricsMenuOutputVo {

  /** 指标分组列表 */
  groupMetricList: MetricsMenuGroupVo[];

  /** 更多指标列表 */
  moreMetricList: MetricsMenuVo[];

  /** 用户锁定指标列表 */
  userMetricList: MetricsMenuVo[];
  
}
