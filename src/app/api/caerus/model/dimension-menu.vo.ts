import { DimensionValueMenuVo } from './DimensionValueMenuVo';

/** 维度菜单对象 */
export interface DimensionMenuVo {
  key: string;

  /** 英文名称 */
  extendName: string;

  /** 别名 */
  aliasName: string;

  /** 维度描述 */
  bizExpression: string;

  /** 维度类型，1-杂项维，2-标准维 */
  type: string;

  /** 是否用户构成分析，1 用于构成分析，其他不能 */
  constitute: number;

  /** [自选维度指标新增字段]是否用户选中，1 选中，其他未选中 */
  display?: 0 | 1 | number;

  /** [自选维度指标新增字段]展示顺序	 */
  displayOrder?: number;

  /** 子类型 */
  valueList: DimensionValueMenuVo[];

  showValue?: string;
}
