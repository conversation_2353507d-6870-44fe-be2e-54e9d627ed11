export interface MetricsBaseInfo {

  /** 主键 */
  id: number;

  /** 主题域id */
  themeDomainId: number;

  /** 指标编码 */
  code: string;

  /** 指标中文名称 */
  name: string;

  /** 英文名称 */
  extendName: string;

  /** 指标别名 */
  aliasName: string;

  /** 类型1-原子 2-派生 3-复合 */
  type: number;

  /** 状态 */
  status: number;

  /** 指标等级 */
  level: number;

  /** 是否自助分析：0-非自助分析 1-自助分析 */
  autoAnalysis: number;

  /** 业务口径 */
  bizExpression: string;

  /** 业务线归属1: 顺风车 2：出租车 3：金融  */
  businessType: number;

  /** 标签角色 1-嘀嗒乘客,2-顺风车车主,3-出租车司机 */
  userType: string;

  /** 场景 1-数据大盘 2-A/B实验分析3-分群数据指标 */
  scene: string;

  /** 数据类型 */
  dataType: string;

  /** 整数位数 */
  dataRound: number;

  /** 小数位数 */
  dataDecimalRound: number;

  /** 技术口径 */
  technicalExpression: string;

  /** 业务负责人 */
  businessOwner: string;

  /** 数据负责人 */
  developOwner: string;

  /** 创建人 */
  creator: string;

  /** 修改人 */
  modifier: string;

  /** 创建时间 */
  createTime: string;

  /** 更新时间 */
  updateTime: string;

}