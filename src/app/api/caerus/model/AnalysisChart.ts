export interface AnalysisChart {

  id: number;
 
  /**
   * 看板id
   */
  dashboardId: number;
 
  /**
   * 图表名称
   */
  chartName: string;
 
  /**
   * 分析类型 1 趋势分析，2 构成分析，3 对比分析
   */
  analysisType: 1 | 2 | 3;
 
  /**
   * 图表配置json
   */
  chartConfig: string;
 
  /**
   * 图表展示配置json
   */
  chartShowConfig: string;
 
  /**
   * 排序
   */
  sort: number;
 
  /**
   * 是否删除 0 未删除，1 已删除，默认0
   */
  isDeleted: number;
 
  /**
   * 创建人
   */
  createBy: string;
 
  /**
   * 最近操作人
   */
  updateBy: string;
 
  /**
   * 创建人
   */
  createByCn: string;
 
  /**
   * 最近操作人
   */
  updateByCn: string;
 
  /**
   * 创建时间
   */
  createTime: string;
 
  /**
   * 最近更新时间
   */
  updateTime: string;
 
 }
 