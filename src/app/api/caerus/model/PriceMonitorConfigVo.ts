/**
 * 指标类型
 * - ROUTE_PRICE = 1
 * - REFERENCE_PRICE = 2
 * - NORMAL_PRICE = 3
 */
export type MetricsMonitorType = 1 | 2 | 3;


/**
 * 价格监控配置
 */
export interface PriceMonitorConfigVo {

  /**
   * @name id
   * @type {number}
   */
  id: number;

  /**
   * @name 监控名称
   * @type {string}
   */
  monitorName: string;

  /**
   * @name 对标选择
   * @type {string}
   */
  competition: string;

  /**
   * @name 指标类型
   * - 1 路线价格指标
   * - 2 刊例价格指标
   * - 3 常规指标
   */
  monitorType: MetricsMonitorType;

  /**
   * @name 责任人多个
   * @type {string}
   */
  personInCharge: string;

  /**
   * @name 维度筛选 查询服务的维度filter对象
   * @type {string}
   */
  dimensionFilter: string;

  /**
   * @name 指标筛选 查询服务的指标filter对象
   * @type {string}
   */
  metricsFilter: string;

  /**
   * @name 监控说明
   * @type {string}
   */
  description: string;

  /**
   * @name 告警群组
   * @type {string}
   */
  alarmImGroup: string;

  /**
   * @name 是否开启 1 开启 0 关闭 
   * @type {number}
   * @default 0
   */
  state: 1 | 0;
  isDefault: 1 | 0;
  isSubscribe: boolean
  createBy: string;
  createByCn: string;
  createTime: string;

  switchLoading?: boolean;

}