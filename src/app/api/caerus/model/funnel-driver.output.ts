import { BaseOutput, BaseTrendVo } from './base-output';


interface FunnelDriverOrderVo {

  /**
   * 车主dau
   */
  dau: number;

  /**
   * 接单车主uv
   */
  orderReceiveCal: number;

  /**
   * 完单车主uv
   */
  orderCompleteCal: number;

}


interface FunnelDriverTransformItem extends BaseTrendVo {

  /**
   * ucvr（车主）
   */
  ucvr: BaseOutput;

  /**
   * 接单率（车主）
   */
  replyRate: BaseOutput;

  /**
   * 接单完单率（车主）
   */
  replyFinishRate: BaseOutput;

}


export interface FunnelDriverTransformTrendVo extends BaseTrendVo {

  /**
   * ucvr（车主）
   */
  ucvr: number;

  /**
   * 接单率（车主）
   */
  replyRate: number;

  /**
   * 接单完单率（车主）
   */
  replyFinishRate: number;

}


export interface FunnelDriverOutput {

  /**
   * 乘客转换漏斗
   */
  funnel: FunnelDriverOrderVo;
  
  /**
   * 当前转化趋势
   */
  curTrendItem: FunnelDriverTransformItem;
  
  /**
   * 转化趋势
   */
  trend: FunnelDriverTransformTrendVo[];

}