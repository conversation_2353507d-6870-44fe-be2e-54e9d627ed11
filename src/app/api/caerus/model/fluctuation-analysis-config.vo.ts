/** where筛选条件 */
export interface DimensionValueVo {
  /** 编码【数字或者英文字符】 */
  key: string;
  /** 码值【中文名称可用来展示】 */
  value: string;
}

/** 层级数据对象 */
interface LevelData {
  /** 筛选条件 */
  param: Param;
  /** 行数据描述 */
  lineData: LineData[];
}

/** 当前层级的查询入餐配置 */
export interface Param {
  /** 筛选条件 */
  filter?: FilterItem[];
  /** 维度 */
  dimension?: MetricsItem[];
  /** 指标 */
  metrics?: MetricsItem[];
}

type Condition = '>' | '<' | '=' | '>=' | '<=' | '!=' | 'not null' | 'in' | 'and' | 'or';
type ValueType = 'metrics' | 'dimension';


export class FilterItemVo {

  /** 运算类型 逻辑运算 1,条件运算 2 */
  conditionType: 1 | 2 | number = 2;
  
  /** 运算标识;条件运算公式 [>,<,=,>=,<=,!=,not null,in],逻辑运算公式 [and,or] */
  condition: Condition | string = '=';

  /// region 条件运算时生效
  
  /** 指标id/维度id */
  id: number = null;
  
  /** 维度英文名 */
  extendName: string;

  /** 筛选值 */
  value: DimensionValueVo[] = [];
  
  /** 筛选值类型 指标(metrics)/维度(dimension) */
  valueType: ValueType | string = null;

  constructor(extendName: string, value?: DimensionValueVo | DimensionValueVo[], condition?: Condition) {
    this.extendName = extendName;
    if (value) {
      if (Array.isArray(value)) {
        this.value = value;
      } else {
        this.value = [value];
      }
    }

    if (condition) {
      this.condition = condition;
    }
  }

}

/** where筛选条件 */
export interface FilterItem {
  /** 运算类型 逻辑运算 1,条件运算 2 */
  conditionType: number;
  /** 运算标识;条件运算公式 [>,<,=,>=,<=,!=,not null,in],逻辑运算公式 [and,or] */
  condition: string;
  /// region 条件运算时生效
  /** 指标id/维度id */
  id: number;
  /** 维度英文名 */
  extendName: string;
  /** 筛选值 */
  value: DimensionValueVo[];
  /// endregion
  /// region 逻辑运算时生效
  /** 子条件[只有逻辑运算才支持] */
  subFilter: FilterItemVo[];
  valueType: any;
}

/** 指标或者维度对象 */
export interface MetricsItem {
  /** 指标或者维度英文名 */
  extendName: string;
}

/** 层级的单指标数据对象 */
export interface LineData {
  /** 维度筛选 */
  metricsDataList: MetricsData[];
  /** 子级别展示数据 */
  levelData: LevelData;
}

/** 层级的单指标数据对象 */
export interface MetricsData {
  parentShowName: string;
  aliasName: string;
  /** 展示名称 */
  showName: string;
  /** 指标类型 rate 比率，frequency 频次，num 数值型  */
  showType: 'rate' | 'frequency' | 'num';
  /** 指标英文名 */
  extendName: string;
  /** 展示的 = + -> ->> 图标 */
  icon: string;
  /** 维度筛选 */
  dimensionFilters: DimensionFilter[];
  /** 指标计算公式说明 */
  bizExpression: string;
}

/** where筛选条件 */
export interface DimensionFilter {
  condition: Condition;
  conditionType: 1|2;
  /** 维度英文名 */
  extendName: string;
  /** 维度值映射 */
  value: DimensionValueVo[];
  valueType: any;
}


/** 波动分析配置 */
export interface FluctuationAnalysisConfigVo {

  /** tab名称 */
  tabName: string;
  
  /** tab子名称 */
  tabSubName: string;
  
  /** 展示数据 */
  levelData: LevelData;

}