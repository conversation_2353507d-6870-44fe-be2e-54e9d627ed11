export interface MetricsDimensionInfo {

  /** 主键 */
  id: number;

  /** 修饰词id以逗号分割 */
  adjunctIds: string;

  /** 维度编码 */
  code: string;

  /** 中文名称 */
  name: string;

  /** 英文名称 */
  extendName: string;

  /** 别名 */
  aliasName: string;

  /** 维度类型，1-杂项维，2-标准维 */
  type: number;

  /** 数据负责人 */
  developOwner: string;

  /** 维度描述 */
  description: string;

  /** 数据库id */
  databaseId: number;

  /** 数据表id */
  tableId: number;

  /** 维值字段id */
  dimensionFieldId: number;

  /** 码值字段id */
  codeFieldId: number;

  /** 杂项维详情 */
  info: string;

  /** 创建人 */
  creator: string;

  /** 修改人 */
  modifier: string;

  /** 创建时间 */
  createTime: string;

  /** 更新时间 */
  updateTime: string;

}