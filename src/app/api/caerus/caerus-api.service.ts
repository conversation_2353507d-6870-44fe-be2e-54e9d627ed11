import { Injectable } from '@angular/core'
import { httpResource, HttpResponse } from '@angular/common/http'
import { Observable, of } from 'rxjs'

import { API_URL } from '@common/const'
import { BaseApiService } from '@common/service'
import { BaseUri, Get, Param, Post, Body, Download, Request, FilenName, Cache, Mock, Message } from '@common/decorator'
import { QueryInputExtendVo, QueryInputVo, QueryOutputVo } from '@api/query-engine/model'
import { Page, ResultBody } from '@common/interface'

import { MetricsDetailInput } from './model/metrics-detail.input'
import { MetricsDetailOutput } from './model/metrics-detail.output'
import { MetricsMenuGroupVo, MetricsMenuOutputVo, MetricsMenuVo } from './model/metrics-menu-output.vo'
import { DimensionMenuVo } from './model/dimension-menu.vo'
import { BaseInput, BaseTrendInput } from './model/base-output'
import { FunnelDriverOutput, FunnelDriverTransformTrendVo } from './model/funnel-driver.output'
import { FluctuationAreaInput } from './model/fluctuation.input'
import {
  AnalysisChart,
  AnalysisChartDetailVo,
  AnalysisChartInput,
  AnalysisChartSortInput,
  AnalysisTreeVo,
  DimensionValueMenuVo,
  FluctuationAnalysisConfigVo,
  MetricsMonitorSubscriptionVo,
  PriceMonitorConfigVo,
} from './model'
import { ConstituteMenuVo } from './model/constitute-menu.vo'
import { DeadlineDateInput } from './model/deadline-date.input'
import { CityWeatherInput, CityWeatherVo } from './model/city-weather.model'
import { CityWeatherMock } from './mock/city-weather.mock'
import { AnalysisSubject } from './model/analysis-subject'
import { AnalysisSubjectInput } from './model/analysis-subject.input'
import { AnalysisDashboardOutput } from './model/analysis-dashboard.output'
import { AnalysisDashboardInput } from './model/analysis-dashboard.input'
import { DimensionDataShowVo } from './model/DimensionDataShowVo'
import { FluctuationAreaMock } from './mock/fluctuation-area.mock'
import { DataUpdateVo } from '@views/cockpit/models/data-update.vo'

@Injectable({
  providedIn: 'root',
})
@BaseUri(`${API_URL}`)
export class CaerusApiService extends BaseApiService {
  /**
   * @see {@link https://demeter.didapinche.com/test/api/caerus/doc.html#/default/用户配置数据接口/getMenuConstituteDataUsingGET 维度构成分析接口}
   * @returns
   */
  @Get(`/user/data/menu/constitute/get`)
  fetchConstitute(): Observable<ResultBody<ConstituteMenuVo[]>> {
    return null
  }

  /**
   * @see {@link https://demeter.didapinche.com/test/api/caerus/doc.html#/default/用户配置数据接口/getMenuDimensionDataUsingGET 维度基础信息}
   * @returns
   */
  @Get(`/user/data/menu/dimension/get`)
  fetchDimension(@Param('module') module?: string): Observable<ResultBody<DimensionMenuVo[]>> {
    return null
  }

  /**
   * @see {@link https://demeter.didapinche.com/test/api/caerus/doc.html#/default/用户配置数据接口/saveUserMenuDimensionDataUsingPOST [自选]保存用户选中维度值接口}
   * @returns
   */
  @Post(`/user/data/menu/dimension/save`)
  patchCustomDimension(@Body() body: DimensionMenuVo[]): Observable<ResultBody<DimensionMenuVo[]>> {
    return null
  }

  /**
   * @see {@link https://demeter.didapinche.com/test/api/caerus/doc.html#/default/用户配置数据接口/getMenuMetricsDataUsingGET 指标基础信息}
   * @param {*} params
   * @returns
   */
  @Get(`/user/data/menu/metrics/get`)
  fetchMetrics(@Param() params?: { extendName: string }): Observable<ResultBody<MetricsMenuOutputVo>> {
    return null
  }

  /**
   * @see {@link https://demeter.didapinche.com/test/api/caerus/doc.html#/default/用户配置数据接口/getMenuMetricsDataV2UsingGET [自选]指标值接口V2}
   * @returns
   */
  @Get(`/user/data/menu/metrics/list`)
  // @Mock([])
  fetchMenuMetricsDataV2(@Param('module') module?: string): Observable<ResultBody<MetricsMenuVo[]>> {
    return null
  }

  @Cache()
  @Get(`/user/data/menu/metrics/tag`)
  fetchMenuMetricsTag(): Observable<ResultBody<{ [key: string]: { describe: string } }>> {
    return null
  }

  /**
   * @see {@link https://demeter.didapinche.com/test/api/caerus/doc.html#/default/用户配置数据接口/saveUserMenuMetricsDataUsingPOST [自选]用户配置展示的指标保存}
   * @returns
   */
  @Post(`/user/data/menu/metrics/save`)
  patchCustomMetrics(@Body() body: MetricsMenuVo[]): Observable<ResultBody<MetricsMenuVo[]>> {
    return null
  }

  /**
   * 指标相关配置
   * @see {@link https://demeter.didapinche.com/test/api/caerus/doc.html#/default/用户配置数据接口/getConfigUsingGET}
   * @param {string} key 参数
   * - fluctuation_position_top: 波动诊断-波动定位
   * - fluctuation_position_top_v2
   * - fluctuation_position_area: 波动诊断-波动区域
   * - customer_value_abc: 车主洞察
   * - business_monitoring_target: 业务盯盘目标
   * - business_monitoring_promotion_detail_pass_foot_v2: 新用户留存-table
   */
  @Get(`/user/data/config`)
  fetchConfig<T = any>(@Param('key') key: string): Observable<ResultBody<T>> {
    return null
  }

  /**
   * @see {@link https://pass.didapinche.com/harley/v3/project/api?pid=20&type=2&treeId=1758 总览关键指标明细}
   * @returns
   */
  @Post('/data/metrics/overview/detail')
  fetchMetricsOverviewDetail(@Body() body: MetricsDetailInput): Observable<ResultBody<MetricsDetailOutput>> {
    return null
  }

  /**
   * @see {@link https://demeter.didapinche.com/test/api/caerus/doc.html#/default/用户配置数据接口/saveUserMenuMetricsDataUsingPOST 用户配置展示的指标保存}
   * @param {MetricsMenuVo[]} body
   * @returns
   */
  @Post('/user/data/menu/metrics/save')
  saveUserMenuMetricsData(@Body() body: MetricsMenuVo[]): Observable<ResultBody<MetricsMenuVo[]>> {
    return null
  }

  /**
   * @see {@link https://demeter.didapinche.com/test/api/caerus/doc.html#/default/数据接口/getFunnelDriverUsingPOST 车主转化漏斗}
   * @param {BaseInput} body
   * @returns
   */
  @Post('/data/funnel/driver')
  fetchFunnelDriver(@Body() body: BaseInput): Observable<ResultBody<FunnelDriverOutput>> {
    return null
  }

  /**
   * @see {@link https://demeter.didapinche.com/test/api/caerus/doc.html#/default/数据接口/getFunnelDriverTrendUsingPOST 车主转化漏斗转化趋势}
   * @param {BaseTrendInput} body
   * @returns
   */
  @Post('/data/funnel/driver/trend')
  fetchFunnelDriverTrend(@Body() body: BaseTrendInput): Observable<ResultBody<FunnelDriverTransformTrendVo[]>> {
    return null
  }

  /**
   * @see {@link https://pass.didapinche.com/harley/v3/project/api?pid=20&type=2&treeId=8320&env=93 首页城市}
   * @returns
   */
  @Cache()
  @Get('/user/data/home/<USER>')
  fetchHomeCityData(): Observable<ResultBody<DimensionValueMenuVo[]>> {
    return null
  }

  /**
   * @see {@link https://demeter.didapinche.com/test/api/caerus/doc.html#/default/波动诊断相关接口/getFluctuationDetailUsingPOST_1 波动区域}
   * @param {FluctuationAreaInput} body
   * @returns
   */
  @Post('/fluctuation/area')
  // @Mock(FluctuationAreaMock)
  fetchFluctuationArea(@Body() body: Partial<FluctuationAreaInput>): Observable<ResultBody<QueryOutputVo>> {
    return null
  }

  /**
   * 波动分析指标配置接口(全部)
   * @see {@link https://demeter.didapinche.com/test/api/caerus/doc.html#/default/波动诊断相关接口/getFluctuationAnalysisConfigAllUsingPOST}
   * @returns
   */
  @Post('/fluctuation/analysis/all/config')
  fetchFluctuationAnalysisConfigAll(): Observable<ResultBody<{ [key: string]: FluctuationAnalysisConfigVo }>> {
    return null
  }

  /**
   * 合拼类型趋势图【传入metrics,dimensions,filter,dt,compareDt,dtType,scene即可】
   * @param {QueryInputExtendVo} body
   * @returns
   */
  @Post('/fluctuation/pkg/type/data')
  fetchPkgTypeValueData(@Body() body: QueryInputExtendVo): Observable<ResultBody<QueryOutputVo>> {
    return null
  }

  /**
   * @see {@link https://demeter.didapinche.com/test/api/caerus/doc.html#/default/数据接口/getCommonUsingPOST 数据更新时间}
   * @returns
   */
  @Post('/fluctuation/update/time')
  fetchFlucatuationUpdateTime(): Observable<ResultBody<{ minDt: string }>> {
    return null
  }

  /**
   * @see {@link https://demeter.didapinche.com/test/api/caerus/doc.html#/default/实时大盘相关接口/getDateDeadlineUsingPOST 数据截止时间}
   * @param {DeadlineDateInput} body
   * @returns
   */
  @Post('/real/time/data/deadline')
  fetchRealTimeDeadLine(@Body() body: DeadlineDateInput): Observable<ResultBody<string>> {
    return null
  }

  /**
   * @see {@link https://demeter.didapinche.com/test/api/caerus/doc.html#/default/时大盘相关接口/getCityWeatherUsingPOST 省份天气数据}
   * @returns
   */
  @Post('/real/time/city/weather')
  // @Mock(CityWeatherMock)
  fetchRealTimeCityWeather(@Body() body: CityWeatherInput): Observable<ResultBody<CityWeatherVo[]>> {
    return null
  }

  /**
   * 多维分析主题-新增
   * @returns
   */
  @Post('/analysis/subject/save')
  createAnalysisSubject(@Body() body: AnalysisSubjectInput): Observable<ResultBody<number>> {
    return null
  }

  /**
   * 多维分析主题-删除
   * @returns
   */
  @Post('/analysis/subject/delete')
  removeAnalysisSubject(@Body() body: { id: number }): Observable<ResultBody<null>> {
    return null
  }

  /**
   * 多维分析主题-修改
   * @returns
   */
  @Post('/analysis/subject/edit')
  updateAnalysisSubject(@Body() body: AnalysisSubjectInput): Observable<ResultBody<number>> {
    return null
  }

  /**
   * 多维分析主题-列表
   * @returns
   */
  @Post('/analysis/subject/list')
  fetchAnalysisSubjectList(@Param('module') module?: string): Observable<ResultBody<AnalysisSubject[]>> {
    return null
  }

  /**
   * 多维分析主题-列表
   */
  fetchAnalysisSubjectList2(module = 'subject-list') {
    return httpResource<ResultBody<AnalysisSubject[]>>(() => ({
      method: 'POST',
      url: `${API_URL}/analysis/subject/list`,
      params: {
        module,
      },
    }))
  }

  /**
   * 多维分析看板-新增
   * @param {AnalysisDashboardInput} body 多维分析图表保存-看板-入参
   * @returns
   */
  @Post('/analysis/dashboard/save')
  createAnalysisDashboard(@Body() body: AnalysisDashboardInput): Observable<ResultBody<null>> {
    return null
  }

  /**
   * 多维分析看板-删除
   * @param {Object} body
   * @param {number} [body.id] 看板id
   * @returns
   */
  @Post('/analysis/dashboard/delete')
  removeAnalysisDashboard(@Body() body: { id: number }): Observable<ResultBody<null>> {
    return null
  }

  /**
   * 多维分析看板-修改
   * @param {AnalysisDashboardInput} body 多维分析图表保存-看板-入参
   * @returns
   */
  @Post('/analysis/dashboard/edit')
  updateAnalysisDashboard(@Body() body: AnalysisDashboardInput): Observable<ResultBody<null>> {
    return null
  }

  /**
   * 多维分析看板-详情
   * @param {string} id 看板id
   * @returns
   */
  @Get('/analysis/dashboard/detail')
  fetchAnalysisDashboardDetail(@Param('id') id?: string): Observable<ResultBody<AnalysisDashboardOutput>> {
    return null
  }

  /**
   * 多维分析看板-列表
   * @param {Object} body
   * @param {string} [body.key] 查询文字
   * @param {string} [body.subjectId] 主题id
   * @param {string} [body.self] 只看我创建的 1: true; 0: false; 默认 0 或者 null
   * @returns
   */
  @Post('/analysis/dashboard/list')
  fetchAnalysisDashboardList(
    @Body() body?: { key?: string; subjectId?: string; self?: 0 | 1 },
    @Param('module') module?: string
  ): Observable<ResultBody<AnalysisDashboardOutput[]>> {
    return null
  }

  /**
   * 多维分析看板-分页列表
   * @param {Object} body
   * @param {string} [body.key] 查询文字
   * @param {string} [body.subjectId] 主题id
   * @returns
   */
  @Post('/analysis/dashboard/page')
  fetchAnalysisDashboardListPage(
    @Body()
    body?: {
      key?: string
      subjectId?: string
      pageNum: number
      pageSize: number
    },
    @Param('module') module?: string
  ): Observable<ResultBody<any>> {
    return null
  }

  /**
   * 多维分析看板-分页列表
   * @param {string} key 查询文字
   * @param {string} subjectId 主题id
   * @param {number} pageNum
   * @param {number} pageSize
   * @returns
   */
  fetchAnalysisDashboardListPage2(
    key: () => string,
    subjectId: () => string,
    pageNum: () => number,
    pageSize: () => number
  ) {
    return httpResource<ResultBody<Page<any>>>(() => ({
      method: 'POST',
      url: `${API_URL}/analysis/dashboard/page`,
      body: {
        key: key(),
        subjectId: subjectId(),
        pageNum: pageNum(),
        pageSize: pageSize(),
      },
      params: {
        module: 'dashboard-list',
      },
    }))
  }

  /**
   * 多维分析看板-批量更换主题
   * @param {Object} body
   * @param {string} [body.subjectId] 主题id
   * @param {number[]} [body.dashboardIdList] 看板列表
   * @returns
   */
  @Post('/analysis/dashboard/move')
  moveAnalysisDashboard(
    @Body() body: { subjectId: number; dashboardIdList: number[] }
  ): Observable<ResultBody<unknown>> {
    return null
  }

  /**
   * 多维分析看板-树形结构
   * @returns
   */
  @Get('/analysis/dashboard/tree')
  fetchAnalysisDashboardTree(): Observable<ResultBody<AnalysisTreeVo[]>> {
    return null
  }

  /**
   * 多维分析图表保存-图表
   * @param {AnalysisChartInput} body
   * @returns
   */
  @Post('/analysis/chart/save')
  saveAnalysisChart(
    @Body() body: AnalysisChartInput,
    @Param('module') module?: string
  ): Observable<ResultBody<unknown>> {
    return null
  }

  /**
   * 多维分析图表-删除
   * @param {number[]} body【要删除的图表id列表】
   * @returns
   */
  @Post('/analysis/chart/delete')
  removeAnalysisChart(@Body() body: number[]): Observable<ResultBody<boolean>> {
    return null
  }

  /**
   * 多维分析图表-列表
   * @param {Object} body
   * @param {string} [body.dashboardId] 看板id
   * @param {string} [body.key] 查询文字
   * @returns
   */
  @Post('/analysis/chart/list')
  fetchAnalysisChartList(
    @Body() body: { dashboardId: string; key?: string },
    @Param('module') module?: string
  ): Observable<ResultBody<AnalysisChart[]>> {
    return null
  }

  /**
   * 多维分析图表-详情
   * @param {string} id 图表id
   * @returns
   */
  @Get('/analysis/chart/detail')
  fetchAnalysisChartDetail(@Param('id') id: string): Observable<ResultBody<AnalysisChartDetailVo>> {
    return null
  }

  /**
   * 多维分析图表-排序
   * @param {AnalysisChartSortInput} body 多维分析图表排序入参
   * @returns
   */
  @Post('/analysis/chart/sort')
  updateAnalysisChartSort(@Body() body: AnalysisChartSortInput[]): Observable<ResultBody<unknown>> {
    return null
  }

  @Get('/business/monitor/resume')
  fetchBusinessMonitorResume(@Param('dt') dt: string): Observable<ResultBody<string>> {
    return null
  }

  @Get('/business/monitor/resumev2')
  fetchBusinessMonitorResumeV2(
    @Param('auth') auth: boolean,
    @Param('dt') dt: string
  ): Observable<ResultBody<Array<{ title: string; data: string[] }>>> {
    return null
  }

  /**
   * 目标管理-详情接口
   * @returns
   */
  @Get('/business/monitor/target/metrics/value/list')
  getMonitorManageDetail(@Param('date') date?: string): Observable<ResultBody<unknown>> {
    return null
  }

  /**
   * 分页获取目标上传历史
   * @returns
   */
  @Get('/business/monitor/file/history/page')
  getMonitorUploadHistory(
    @Param('pageNo') pageNo: number,
    @Param('pageSize') pageSize: number
  ): Observable<ResultBody<string>> {
    return null
  }

  /**
   * 模板下载
   * @returns
   */
  @Download()
  @Get('/business/monitor/template/download', { responseType: 'blob' })
  getTemplateDownload(@FilenName() fileName: string, @Param('type') type: string): Observable<ResultBody<null>> {
    return null
  }

  /**
   * 历史目标文件下载
   * @returns
   */
  @Download()
  @Get('/business/monitor/target/file/download', { responseType: 'blob' })
  getHistoryFileDownload(@Param('id') id: number, @FilenName() fileName: string): Observable<ResultBody<null>> {
    return null
  }

  /**
   * 模板上传
   * @returns
   */
  @Request('POST', '/business/monitor/target/file/upload')
  uploadTargetFile(formData: FormData): Observable<HttpResponse<ResultBody<string>>> {
    return null
  }

  /**
   * 业务目标模板上传
   * @returns
   */
  @Request('POST', '/business/monitor/target/file/uploadv2/1')
  uploadTargetFileType1(formData: FormData): Observable<HttpResponse<ResultBody<string>>> {
    return null
  }

  /**
   * 财务目标模板上传
   * @returns
   */
  @Request('POST', '/business/monitor/target/file/uploadv2/2')
  uploadTargetFileType2(formData: FormData): Observable<HttpResponse<ResultBody<string>>> {
    return null
  }

  /**
   * 获取预算列表
   * @returns
   */
  @Get('/market/budget/list')
  getBudgetList(): Observable<ResultBody<any[]>> {
    return null
  }

  /**
   * 获取业务目标列表
   * @returns
   */
  @Get('/metrics/month/target/list')
  getBusinessBudgetList(): Observable<ResultBody<any[]>> {
    return null
  }

  /**
   * 新增预算
   * @returns
   */
  @Post('/market/budget/save')
  addBudget(@Body() body: any): Observable<ResultBody<unknown>> {
    return null
  }

  /**
   * 编辑预算
   * @returns
   */
  @Post('/market/budget/update')
  updateBudget(@Body() body: any): Observable<ResultBody<unknown>> {
    return null
  }

  /**
   * 新增业务目标
   * @returns
   */
  @Post('/metrics/month/target/save')
  addBusinessBudget(@Body() body: any): Observable<ResultBody<unknown>> {
    return null
  }

  /**
   * 编辑业务目标
   * @returns
   */
  @Post('/metrics/month/target/update')
  updateBusinessBudget(@Body() body: any): Observable<ResultBody<unknown>> {
    return null
  }

  /**
   * @param {string} key 参数
   * - price_monitor_dimension
   * - cockpit_dim
   */
  @Cache()
  @Get('/user/data/config/dimension')
  fetchDimensionConfig(@Param('key') key: string): Observable<
    ResultBody<{
      price_base?: DimensionDataShowVo[]
      price_save?: DimensionDataShowVo[]
      pop_filter?: DimensionDataShowVo[]
      top_filter?: DimensionDataShowVo[]
      top_filter_left?: DimensionDataShowVo[]
      top_filter_right?: DimensionDataShowVo[]
      competition?: DimensionDataShowVo[]
    }>
  > {
    return null
  }

  /**
   * @param {string} key 参数
   * - price_monitor_metrics
   * - cockpit_top_metrics 首页-大盘核心指标总览
   */
  @Cache()
  @Get('/user/data/config/metrics')
  fetchMetricsConfig(@Param('key') key: string): Observable<ResultBody<Record<string, MetricsMenuGroupVo>>> {
    return null
  }

  @Cache()
  @Get('/user/data/config/metrics/v2')
  fetchMetricsConfigV2(
    @Param('key') key: 'cockpit_top_metrics_v2'
  ): Observable<ResultBody<Record<string, MetricsMenuGroupVo>>> {
    return null
  }

  /**
   * 告警群列表
   * @see {@link https://demeter.didapinche.com/aliyun/api/caerus/doc.html#/default/价格监控相关接口/groupListUsingGET 告警群列表}
   * @param prefix 参数
   * - asteria
   * @returns
   */
  @Cache()
  @Get('/monitor/config/group-list')
  fetchMonitorConfig(@Param('prefix') prefix: 'caerus'): Observable<ResultBody<string[]>> {
    return null
  }

  /**
   * @see {@link https://demeter.didapinche.com/aliyun/api/caerus/doc.html#/default/价格监控相关接口/listUsingGET_1 监控列表}
   * @param {number} pageNo
   * @param {number} pageSize
   * @param {string} subscriber
   * @returns
   */
  @Get('/monitor/config/list')
  fetchMonitorConfigList(
    @Param('pageNo') pageNo: number,
    @Param('pageSize') pageSize: number,
    @Param('subscriber') subscriber: string
  ): Observable<ResultBody<Page<PriceMonitorConfigVo>>> {
    return null
  }

  @Get('/monitor/config/owner-list')
  fetchMonitorConfigOwnerList(
    @Param('pageNo') pageNo: number,
    @Param('pageSize') pageSize: number,
    @Param('userName') userName: string
  ): Observable<ResultBody<Page<PriceMonitorConfigVo>>> {
    return null
  }

  @Get('/monitor/config/subscription-list')
  fetchMonitorConfigSubscriptionList(
    @Param('pageNo') pageNo: number,
    @Param('pageSize') pageSize: number,
    @Param('userName') userName: string
  ): Observable<ResultBody<Page<PriceMonitorConfigVo>>> {
    return null
  }

  /**
   * @see {@link https://demeter.didapinche.com/aliyun/api/caerus/doc.html#/default/价格监控相关接口/saveUsingPOST_4 保存}
   * @param body
   * @returns
   */
  @Post('/monitor/config/save')
  saveMonitorConfig(@Body() body: Partial<PriceMonitorConfigVo>): Observable<ResultBody<number>> {
    return null
  }

  /**
   * @see {@link https://demeter.didapinche.com/aliyun/api/caerus/doc.html#/default/价格监控相关接口/deleteUsingPOST_3 删除监控}
   * @param {Object} body
   * @param {number} [body.id] 监控id
   * @returns
   */
  @Message()
  @Post('/monitor/config/delete')
  deleteMonitorConfig(@Body() body: { id: number }): Observable<ResultBody<unknown>> {
    return null
  }

  /**
   * @see {@link https://demeter.didapinche.com/aliyun/api/caerus/doc.html#/default/价格监控相关接口/saveUsingPOST_4 修改}
   * @param {*} body
   * @returns
   */
  @Message()
  @Post('/monitor/config/edit')
  updateMonitorConfig(@Body() body: Partial<PriceMonitorConfigVo>): Observable<ResultBody<unknown>> {
    return null
  }

  /**
   * 价格监控-区域分析
   * @see {@link https://demeter.didapinche.com/aliyun/api/caerus/doc.html#/default/价格监控相关接口/getRouteUsingPOST 价格监控-区域分析}
   * @param {*} body
   * @param {string} module 所属模块名
   * @returns
   */
  @Post('/price/route')
  search(@Body() body: QueryInputVo, @Param('module') module?: string): Observable<ResultBody<QueryOutputVo>> {
    return null
  }

  @Post(`/search/offplt/promt/reten`)
  searchForOffpltPromtReten(
    @Body() body: QueryInputVo,
    @Param('module') module?: string
  ): Observable<ResultBody<QueryOutputVo>> {
    return null
  }

  /**
   * @see {@link https://demeter.didapinche.com/aliyun/api/caerus/doc.html#/default/价格监控相关接口/subscribeUsingPOST 订阅监控}
   * @param body
   * @returns
   */
  @Post('/monitor/config/subscribe')
  subscribeMonitor(@Body() body: MetricsMonitorSubscriptionVo): Observable<ResultBody<unknown>> {
    return null
  }

  /**
   * @see {@link https://demeter.didapinche.com/aliyun/api/caerus/doc.html#/default/价格监控相关接口/unsubscribeUsingPOST 取消订阅监控}
   * @param body
   * @returns
   */
  @Post('/monitor/config/unsubscribe')
  unsubscribeMonitor(@Body() body: MetricsMonitorSubscriptionVo): Observable<ResultBody<unknown>> {
    return null
  }

  /**
   * @see {@link https://demeter.didapinche.com/aliyun/api/caerus/doc.html#/default/价格监控相关接口/listUsingGET_1 监控列表}
   * @returns
   */
  @Get('/monitor/config/list-default')
  fetchMonitorConfigListDefault(
    @Param('competition') competition: string
  ): Observable<ResultBody<Page<PriceMonitorConfigVo>>> {
    return null
  }

  /**
   * @see {@link https://demeter.didapinche.com/aliyun/api/caerus/doc.html#/default/价格监控相关接口/listUsingGET_1 监控列表}
   * @returns
   */
  @Get('/monitor/config/tree')
  fetchMonitorConfigListTree(@Param('competition') competition: string): Observable<ResultBody<any>> {
    return null
  }

  /**
   * @see {@link https://demeter.didapinche.com/aliyun/api/caerus/doc.html#/default/价格监控相关接口/listUsingGET_1 价格监控-监控预警}
   * @param {} body
   * @returns
   */
  @Post('/price/monitor')
  postPriceMonitorData(@Body() body): Observable<ResultBody<any>> {
    return null
  }

  /**
   * @see {@link https://demeter.didapinche.com/aliyun/api/caerus/doc.html#/default/价格监控相关接口/listUsingGET_1 价格监控-监控预警}
   * @param {} body
   * @returns
   */
  @Get('/business/monitor/target/metrics/finance/value/list')
  getFinanceManageList(@Param('date') date?: string): Observable<ResultBody<any>> {
    return null
  }

  /**
   *
   * @see {@link https://demeter.didapinche.com/aliyun/api/caerus/doc.html#/default/首页查询接口封装/getCoreResultAllUsingPOST 核心结果指标}
   * @param body
   * @param module
   * @returns
   */
  @Post(`/home/<USER>/result`)
  getCoreResult(@Body() body: QueryInputVo, @Param('module') module?: string): Observable<ResultBody<QueryOutputVo>> {
    return null
  }

  @Post('/home/<USER>/process/dt')
  getKeyProcessDt(@Body() body: QueryInputVo, @Param('module') module?: string): Observable<ResultBody<QueryOutputVo>> {
    return null
  }

  /**
   *
   * @see {@link https://demeter.didapinche.com/aliyun/api/caerus/doc.html#/default/首页查询接口封装/getCoreResultAllUsingPOST 核心结果指标-关键过程指标-全部}
   * @param body
   * @param module
   * @returns
   */
  @Post(`/home/<USER>/process/all`)
  getCoreResultAll(
    @Body() body: QueryInputVo,
    @Param('module') module?: string
  ): Observable<ResultBody<QueryOutputVo>> {
    return null
  }

  @Post('/home/<USER>/process/trend/out')
  getCoreResultTrendAll(
    @Body() body: QueryInputVo,
    @Param('module') module?: string
  ): Observable<ResultBody<QueryOutputVo>> {
    return null
  }

  @Post('/home/<USER>/process/trend/pop')
  getCoreResultAllPop(
    @Body() body: QueryInputVo,
    @Param('module') module?: string
  ): Observable<ResultBody<QueryOutputVo>> {
    return null
  }

  /**
   *
   * @see {@link https://demeter.didapinche.com/aliyun/api/caerus/doc.html#/default/首页查询接口封装/getCoreResultAllUsingPOST 关键过程指标-车主}
   * @param body
   * @param module
   * @returns
   */
  @Post(`/home/<USER>/process/driver`)
  getKeyProcessDriver(
    @Body() body: QueryInputVo,
    @Param('module') module?: string
  ): Observable<ResultBody<QueryOutputVo>> {
    return null
  }

  /**
   *
   * @see {@link https://demeter.didapinche.com/aliyun/api/caerus/doc.html#/default/首页查询接口封装/getCoreResultAllUsingPOST 关键过程指标-乘客}
   * @param body
   * @param module
   * @returns
   */
  @Post(`/home/<USER>/process/passenger`)
  // @Post('/home/<USER>/process/dt')
  getKeyProcessPassenger(
    @Body() body: QueryInputVo,
    @Param('module') module?: string
  ): Observable<ResultBody<QueryOutputVo>> {
    return null
  }

  @Post('/home/<USER>/process/dt')
  getKeyProcessPassengerNew(
    @Body() body: QueryInputVo,
    @Param('module') module?: string
  ): Observable<ResultBody<QueryOutputVo>> {
    return null
  }

  @Post('/experiment/price/change/city')
  postChangeCity(@Body() body: any): Observable<ResultBody<QueryOutputVo[]>> {
    return null
  }

  @Post('/experiment/list')
  postRheaList(@Body() body: any): Observable<ResultBody<any[]>> {
    return null
  }

  @Post('/experiment/logs')
  postRheaLog(@Body() body: any): Observable<ResultBody<any[]>> {
    return null
  }
}
