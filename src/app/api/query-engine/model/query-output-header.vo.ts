export class QueryOutputHeaderVo {

  /** 维度/指标 别名 */
  aliasName: string;

  /** 是否是时间筛选字段 [用作认定是否是x轴] */
  dateFilter: number = 0;

  /** 返回数据类型[String,Int,Array等] */
  valueType: string = 'String';

  /** 列类型【metrics 指标，dimension 维度】 */
  columnType: string = 'dimension';

  /**
   * 数据类型 
   * - string
   * - bigint
   * - double
   * - decimal
   */
  dataType: string = 'string';

  /** 整数位数 */
  dataRound: number;

  /** 小数位数 */
  dataDecimalRound: number;

  /** 数据单位 */
  dataUnit: string;

  extendName: string;
  
  diffName: string;
  
  ratioName: string;
}