import { QueryOutputHeaderVo } from './query-output-header.vo';


export interface QueryOutputVo {

  /** 列标题 */
  headerNames: string[];

  /** 指标维度英文名 */
  headers: {[key: string]: QueryOutputHeaderVo};

  /** 列数据类型 */
  headerTypes: {[key: string]: string};

  /** 数据【二维数组】 */
  data: Array<{[key: string]: string}>;

  /** 对比数据【二维数组】 */
  compareData: Array<{[key: string]: string}>;

  /** 总条数 */
  totalValue: number;

  /** 总页数 */
  pageValue: number;

}
