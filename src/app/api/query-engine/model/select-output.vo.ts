export interface SelectDimensionItemVo {

  /** 主键 */
  id: number;
 
  /** 英文名称 */
  extendName: string;
 
  /** 别名 */
  aliasName: string;
 
  /** 维度类型，1-杂项维，2-标准维 */
  type: string;
 
}


export interface SelectMetricsItemVo {
  
  /** 主键 */
  id: number;

  /** 主题域id */
  themeDomainId: number;

  /** 业务线归属1: 顺风车 2：出租车 3：金融 */
  businessType: number;

  /** 英文名称 */
  extendName: string;

  /** 指标别名 */
  aliasName: string;

  /** 类型1-原子 2-派生 3-复合 */
  type: number;

  /** 指标等级 */
  level: number;

  /** 场景  1-数据大盘； 2-A/B实验分析； 3-分群数据指标 */
  scene: string;

}


export interface SelectOutputVo {

  /** 可选维度数组 */
  dimensions: SelectDimensionItemVo[];

  /** 可选指标数组 */
  metrics: SelectMetricsItemVo[];

}