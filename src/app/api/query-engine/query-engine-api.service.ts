import { Injectable } from '@angular/core';
import { QUERY_ENGINE_API } from '@common/const';
import { BaseUri, Body, Download, Get, Param, Post } from '@common/decorator';
import { ResultBody } from '@common/interface';
import { BaseApiService } from '@common/service';
import { Observable } from 'rxjs';

import { QueryInputVo } from './model/query-input.vo';
import { QueryOutputVo } from './model/query-output.vo';
import { QueryFilterValuesInputVo } from './model/query-filter-values-input.vo';
import { DimensionValueOutputVo } from './model/dimension-value-output.vo';
import { SelectInputVo } from './model/select-input.vo';
import { SelectOutputVo } from './model/select-output.vo';

@Injectable({
  providedIn: 'root',
})
@BaseUri(`${QUERY_ENGINE_API}`)
export class QueryEngineApiService extends BaseApiService {
  /**
   * @see {@link https://demeter.didapinche.com/test/api/query/engine/doc.html#/default/%E6%95%B0%E6%8D%AE%E6%8E%A5%E5%8F%A3/getDimensionValuesUsingGET 维度值列表}
   * @param {*} extendName
   * @returns 维度值列表
   */
  @Get(`/data/dimension/values`)
  fetchDimensionValues(
    @Param() params: { extendName: string }
  ): Observable<ResultBody<DimensionValueOutputVo>> {
    return null;
  }

  /**
   * @see {@link https://demeter.didapinche.com/test/api/query/engine/doc.html#/default/%E6%95%B0%E6%8D%AE%E6%8E%A5%E5%8F%A3/getDimensionMapUsingPOST 维度值列表【查询多个的情况】}
   * @param {QueryFilterValuesInputVo} body 维度值列表请求参数
   * @returns 维度值列表
   */
  @Post(`/data/dimension/map`)
  fetchDimensionMap(
    @Body() body: QueryFilterValuesInputVo
  ): Observable<ResultBody<{ [key: string]: DimensionValueOutputVo }>> {
    return null;
  }

  /**
   * @see {@link https://demeter.didapinche.com/test/api/query/engine/doc.html#/default/%E6%8C%87%E6%A0%87%E7%BB%B4%E5%BA%A6%E8%81%94%E5%8A%A8%E9%80%89%E5%8F%96/selectUsingPOST_1 指标维度联动选取}
   * @param {SelectInputVo} body 指标选取请求参数
   * @returns 指标选取结果
   */
  @Post(`/select/list`)
  fetchSelectList(
    @Body() body: SelectInputVo,
    @Param('module') module?: string
  ): Observable<ResultBody<SelectOutputVo>> {
    return null;
  }

  /**
   * @see {@link https://demeter.didapinche.com/test/api/query/engine/doc.html#/default/%E6%95%B0%E6%8D%AE%E6%8E%A5%E5%8F%A3/selectUsingPOST 数据查询逻辑}
   * @param {QueryInputVo} body 指标查询请求参数
   * @param {string} [module] 所在模块
   * @returns 指标维度查询结果
   */
  @Post(`/data/search`)
  search(
    @Body() body: QueryInputVo,
    @Param('module') module?: string
  ): Observable<ResultBody<QueryOutputVo>> {
    return null;
  }

  /**
   * @see {@link https://demeter.didapinche.com/test/api/query/engine/doc.html#/default/%E6%95%B0%E6%8D%AE%E6%8E%A5%E5%8F%A3/selectUsingPOST 数据查询逻辑}
   * @param {QueryInputVo} body 指标查询请求参数
   * @returns 指标维度查询结果
   */
  @Post(`/data/search`)
  newSearch(@Body() body: QueryInputVo): Observable<ResultBody<QueryOutputVo>> {
    return null;
  }

  @Download()
  @Post(`/export/excel`, { responseType: 'blob' })
  exportToExcel(@Body() body: QueryInputVo): Observable<any> {
    return null;
  }

  @Get('update/time/get/businessMonitoringPromotionDetail')
  fetchUpdateTime(
    @Param('month') month: string
  ): Observable<ResultBody<{ dt: string }>> {
    return null;
  }

  @Get('update/time/get/businessMonitoring')
  fetchUpdateTimeModel2(
    @Param('month') month: string
  ): Observable<ResultBody<{ dt: string }>> {
    return null;
  }

  @Get('/test/ts')
  test(): Observable<any> {
    return null;
  }
}
