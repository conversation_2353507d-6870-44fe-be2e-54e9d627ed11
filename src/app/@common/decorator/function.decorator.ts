import {
  HttpClient,
  HttpErrorResponse,
  HttpRequest,
  HttpResponse,
} from '@angular/common/http';
import { coerceBooleanProperty } from '@angular/cdk/coercion';
import {
  isArray,
  isBoolean,
  isNotUndefinedOrNotNull,
  isNumber,
  isObject,
  isString,
} from '@common/function';
import { Observable, of, Subscription } from 'rxjs';
import { concatMap, filter, tap } from 'rxjs/operators';
// import * as _ from 'loadsh';
import * as _ from 'lodash';

import { Util } from '@common/class';
import { FILE_NAME_KEY } from '@common/const';
import { ResultBody } from '@common/interface';
import { getBodyFromMetadata, paramBuilder } from './http.decorator';


export function Log(msg?: string, logArgs?: boolean): any {
  return function (target, name, descriptor): void {
    const oldValue = descriptor.value;
    descriptor.value = function (...args: any): void {
      msg && console.group(msg);

      if (logArgs) {
        console.log(
          `${target.constructor.name}.${name}(${
            (args && JSON.stringify(args)) || ''
          })`
        );
      } else {
        console.log(`${target.constructor.name}.${name}()`);
      }

      msg && console.groupEnd();
      return oldValue.apply(this, args);
    };
  };
}

export function Mock<T>(mockData: T) {
  return function (target, name, descriptor): void {
    descriptor.value = function MockFn(...args: any): Observable<ResultBody<T>> {
      return of(new ResultBody(mockData));
    };
  };
}

/**
 * 执行类方法前执行
 * ## Examples
 * ```
 * @Before((): string => {
 *    if (condition) {
 *      return true; // 将继续执行`openModal`方法
 *    } else {
 *      return false; // 将不会执行`openModal`方法
 *    }
 * })
 * openModal(): void {
 *   // todosomething
 * }
 * ```
 */
export function Before<T = any>(
  callback: (context: T, ...args) => boolean | void
): any {
  return function (
    target,
    propertyKey: string,
    descriptor: PropertyDescriptor
  ): void {
    const oldValue = descriptor.value as Function;

    descriptor.value = function (...params) {
      const res = callback(this, ...params);

      if (res === true || res === undefined) {
        return oldValue.apply(this, params);
      }
    };
  };
}

export function After(
  callback: (...params) => boolean | void,
  time?: number
): any {
  return function (target, name, descriptor): void {
    const oldValue = descriptor.value;
    descriptor.value = function (...args): void {
      oldValue.apply(this, args);

      if (time !== undefined) {
        setTimeout(() => {
          callback.apply(this, args);
        }, time);
      } else {
        callback.apply(this, args);
      }
    };
  };
}

export function SwitchMap(): any {
  return function (target, name, descriptor): void {
    const oldValue = descriptor.value;
    let subscription: Subscription;

    descriptor.value = function (...args: any): void {
      if (subscription instanceof Subscription) {
        subscription.unsubscribe();
      }

      subscription = oldValue.apply(this, args);
    };
  };
}

// export function TransformParams(): any {
//   return function(target, name, descriptor): void {
//     const oldValue = descriptor.value;

//     descriptor.value = function(...args: any) {
//       console.log(args);
//       return oldValue.apply(this, args);
//     };
//   };
// }

export function Sleep(time: number = 0): any {
  return function (target, name, descriptor): void {
    const oldValue = descriptor.value;
    let s = null;

    descriptor.value = function (...args: any): void {
      clearTimeout(s);
      s = setTimeout(() => {
        oldValue.apply(this, args);
      }, time);
    };
  };
}

export function Cache(): any {
  return function (target, propertyKey, descriptor): void {
    const oldValue = descriptor.value;
    let temp = null;
    let previousBody = null;
    let previousParams = null;

    descriptor.value = function (...args: any) {
      const body = getBodyFromMetadata(target, propertyKey, args);
      const { params } = paramBuilder(target, propertyKey, args);
      const isBodyDifferent = _.isEqual(previousBody, body) === false;
      const isParamsDifferent = params.toString() !== previousParams;

      // console.log('[Cache]', params.toString());

      if (isBodyDifferent || isParamsDifferent) {
        temp = null;
      }

      if (temp === null) {
        return (<Observable<any>>oldValue.apply(this, args)).pipe(
          tap((res) => {
            temp = res;
            previousParams = params.toString();
            previousBody = body;
          })
        );
      } else {
        return of(temp);
      }
    };
  };
}

export function DistinctUntilChanged(): any {
  return function (target, name, descriptor): void {
    const oldValue = descriptor.value;
    let previous = null;

    descriptor.value = function (args: string | number | object | any[]): void {
      const isDiff = isString(args)
        ? previous !== args
        : isNumber(args)
        ? previous !== args
        : isBoolean(args)
        ? previous !== args
        : isObject(args)
        ? JSON.stringify(previous) !== JSON.stringify(args)
        : isArray(args)
        ? JSON.stringify(previous) !== JSON.stringify(args)
        : false;

      if (isDiff) {
        previous = args;
        oldValue.call(this, args);
      }
    };
  };
}

export function Debounce(time: number = 0): any {
  return function (target, name, descriptor): void {
    const oldValue = descriptor.value;
    let s = null;

    descriptor.value = function (...args: any): void {
      clearTimeout(s);
      s = setTimeout(() => {
        oldValue.apply(this, args);
      }, time);
    };
  };
}

export function Throttle(time: number = 0): any {
  return function (target, name, descriptor): void {
    const oldValue = descriptor.value;
    let valid = true;

    descriptor.value = function (...args: any): void {
      if (valid) {
        valid = false;
        oldValue.apply(this, args);
        setTimeout(() => {
          valid = true;
        }, time);
      }
    };
  };
}

export function Skip(count: number = 0): any {
  return function (target, name, descriptor): void {
    const oldValue = descriptor.value;
    let _currentCount = 0;
    let valid = false;

    descriptor.value = function (...args: any): void {
      _currentCount++;
      valid = _currentCount >= count;
      if (valid) {
        oldValue.apply(this, args);
      }
    };
  };
}

export function Confirmed(nzTitle: string, nzContent?: string): any {
  return function (target, name, descriptor): void {
    const oldValue = descriptor.value;
    descriptor.value = function (...args: any): void {
      if (!!this.modal) {
        this.modal
          .confirm({
            nzTitle,
            nzContent,
            nzOkText: '确定',
            nzCancelText: '取消',
            nzOnOk: () => true,
          })
          .afterClose.pipe(filter((v) => v === true))
          .subscribe(() => {
            oldValue.apply(this, args);
          });
      } else {
        if (window.confirm(nzTitle)) {
          oldValue.apply(this, args);
        }
      }
    };
  };
}

export function Developing(title: string = ''): any {
  return function (target, name, descriptor): void {
    descriptor.value = function (): void {
      this.modal.alert(`${title}功能开发中...`, 'info');
    };
  };
}

export function Message(successMsg = '操作成功', errorMsg = '操作失败'): any {
  return function (target, name, descriptor): void {
    const oldValue = descriptor.value;

    descriptor.value = function (...args: any): void {
      return oldValue.apply(this, args).pipe(
        tap((res: ResultBody) => {
          if (res?.status && res.status === '00000') {
            this.message.success(successMsg);
          } else {
            this.message.error(
              `${errorMsg}: ${res?.message || ''} ${res?.error || ''}`
            );
          }
        })
      );
    };
  };
}

export function ErrorMessage(): any {
  return function (target, name, descriptor): void {
    const oldValue = descriptor.value;

    descriptor.value = function (...args: any): void {
      return oldValue.apply(this, args).pipe(
        tap((res: ResultBody) => {
          if (res?.status && res.status !== '00000') {
            res?.message &&
              this.message.error(`${res?.message || ''} ${res?.error || ''}`);
          }
        })
      );
    };
  };
}

export function Download(format: string = '.xlsx'): any {
  return function (target, propertyKey, descriptor): void {
    const parameterMetadata =
      Reflect.getOwnMetadata(FILE_NAME_KEY, target, propertyKey) || {};
    const metadataKeys = Reflect.ownKeys(parameterMetadata);
    const oldValue = descriptor.value;

    descriptor.value = function (...args: any): void {
      const { messageId, onClose } = this.message.loading('Loading...', {
        nzDuration: 0,
      });
      const arr = [...args];
      const index = metadataKeys[0];
      const fileName = arr[index];

      return oldValue.apply(this, args).pipe(
        tap((res: ResultBody) => {
          console.log(res);
          if (res instanceof HttpErrorResponse) {
            this.message.error('下载失败');
          } else {
            onClose
              .pipe(
                concatMap(
                  () =>
                    this.message.success('开始下载', { nzDuration: 2500 })
                      .onClose!
                )
              )
              .subscribe(() => {
                Util.downloadFile(res, fileName);
              });
          }

          this.message.remove(messageId);
        })
      );
    };
  };
}

function propDecoratorFactory<T, D>(
  name: string,
  fallback: (v: T) => D
): (target: any, propName: string) => void {
  // tslint:disable-next-line: no-any
  function propDecorator(target: any, propName: string): void {
    const privatePropName = `$$__${propName}`;

    if (Object.prototype.hasOwnProperty.call(target, privatePropName)) {
      console.warn(
        `The prop "${privatePropName}" is already exist, it will be overrided by ${name} decorator.`
      );
    }

    Object.defineProperty(target, privatePropName, {
      configurable: true,
      writable: true,
    });

    Object.defineProperty(target, propName, {
      get(): string {
        return this[privatePropName]; // tslint:disable-line:no-invalid-this
      },
      set(value: T): void {
        this[privatePropName] = fallback(value); // tslint:disable-line:no-invalid-this
      },
    });
  }

  return propDecorator;
}

export function toBoolean(value: boolean | string): boolean {
  return coerceBooleanProperty(value);
}

export function InputBoolean(): any {
  // tslint:disable-line: no-any
  return propDecoratorFactory('InputBoolean', toBoolean);
}
