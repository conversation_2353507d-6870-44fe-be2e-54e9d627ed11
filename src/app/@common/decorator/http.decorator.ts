import { HttpClient, HttpErrorResponse, HttpHeaders, HttpParams, HttpRequest, HttpResponse } from '@angular/common/http'
import { Observable, of } from 'rxjs'
import { catchError, filter, tap } from 'rxjs/operators'
import 'reflect-metadata'

import { isNotEmpty, isNotUndefinedOrNotNull, isObject, isUndefined } from '@common/function'
import { QUERY_KEY, REQUEST_BODY_KEY, PARAM_KEY, BASH_URI, HTTP_HEADERS_TOKEN } from '@common/const'

export function transformURL(target, propertyKey, args: any[], url: string): string {
  const parameterMetadata = Reflect.getOwnMetadata(QUERY_KEY, target, propertyKey) || []
  const metadataKeys = Reflect.ownKeys(parameterMetadata)

  if (isUndefined(url)) {
    return ''
  }

  for (const item of metadataKeys) {
    const key = parameterMetadata[item]
    const value = args[item]

    url = url.replace(new RegExp(`:${key}/?`), `${value}`)
  }

  return url
}

export function getBodyFromMetadata(target, propertyKey: string, args: any[]) {
  const parameterMetadata = Reflect.getOwnMetadata(REQUEST_BODY_KEY, target, propertyKey) || []
  const metadataKeys = Reflect.ownKeys(parameterMetadata)

  let body = null

  for (const item of metadataKeys) {
    const value = args[item]

    if (isObject(value)) {
      return value
    } else {
      body = value
    }
  }

  return body
}

function filterNullValue(obj: { [key: string]: string | number }) {
  const new_obj = {}

  Object.entries(obj)
    .filter(([, value]) => isNotUndefinedOrNotNull(value) && isNotEmpty(value))
    .forEach(([key, value]) => (new_obj[key] = value))

  return new_obj
}

export function getHttpParamsFromMetadata(target, propertyKey: string, args: any[]) {
  const parameterMetadata = Reflect.getOwnMetadata(PARAM_KEY, target, propertyKey) || {}
  const metadataKeys = Reflect.ownKeys(parameterMetadata)

  let fromObject = {}

  for (const item of metadataKeys) {
    const key = parameterMetadata[item]
    const value = args[item]

    if (isObject(value)) {
      const copy_value = filterNullValue(value)
      Object.assign(fromObject, { ...copy_value })
    } else if (isNotUndefinedOrNotNull(value)) {
      fromObject[key] = value
    }
  }

  return new HttpParams({ fromObject })
}

export function getHttpHeadersFromMetadata(target, propertyKey: string, args: any[]) {
  const parameterMetadata = Reflect.getOwnMetadata(HTTP_HEADERS_TOKEN, target, propertyKey) || {}
  const metadataKeys = Reflect.ownKeys(parameterMetadata)
  const headers = {} as {
    [name: string]: string | string[]
  }

  for (const item of metadataKeys) {
    const key = parameterMetadata[item]
    const value = args[item]

    if (isNotUndefinedOrNotNull(value)) {
      headers[key] = value
    }
  }

  return new HttpHeaders(headers)
}

export function paramBuilder(target: Function, propertyKey, args, url?: string) {
  const params = getHttpParamsFromMetadata(target, propertyKey, args)
  const copy_url = isNotUndefinedOrNotNull(url) && transformURL(target, propertyKey, args, url)
  const body = getBodyFromMetadata(target, propertyKey, args)
  const prefix = Reflect.getOwnMetadata(BASH_URI, target.constructor) || ''
  const headers = getHttpHeadersFromMetadata(target, propertyKey, args)

  return {
    params,
    copy_url: prefix !== '' && copy_url ? copy_url.replace(/^\//, '') : copy_url,
    body,
    prefix,
    headers,
  }
}

export function Get(url: string = '', options: any = {}) {
  return function (target, propertyKey: string, descriptor: PropertyDescriptor) {
    const oldValue = descriptor.value

    if (oldValue.name === 'MockFn') {
      return
    }

    descriptor.value = function (...args: any[]) {
      const { prefix, copy_url, params } = paramBuilder(target, propertyKey, args, url)

      return (<HttpClient>this.http).get(`${prefix}${copy_url}`, { params, ...options }).pipe(
        catchError((err: HttpErrorResponse, caught) => {
          return of(err)
        })
      )
    }
  }
}

export function Post(url?: string, options: any = {}) {
  return function (target, propertyKey: string, descriptor: PropertyDescriptor) {
    const oldValue = descriptor.value

    if (oldValue.name === 'MockFn') {
      return
    }

    descriptor.value = function (...args: any[]): Observable<any> {
      const { prefix, copy_url, body, headers, params } = paramBuilder(target, propertyKey, args, url)

      return (<HttpClient>this.http).post(`${prefix}${copy_url}`, body, { headers, params, ...options }).pipe(
        tap((res: any) => {
          // const { status, message } = res;
          // if (status !== '00000') {
          //   this.message.warning(message);
          // }
        }),
        catchError((err: HttpErrorResponse | any, caught) => {
          if (err instanceof HttpErrorResponse) {
            console.error(err)
          }

          return of(err)
        })
      )
    }
  }
}

export function Patch(url?: string): Function {
  return function (target, propertyKey: string, descriptor: PropertyDescriptor) {
    descriptor.value = function (...args: any[]): Observable<any> {
      const { prefix, copy_url, body, params } = paramBuilder(target, propertyKey, args, url)

      return (<HttpClient>this.http).patch(`${prefix}${copy_url}`, body, {
        params,
      })
    }
  }
}

export function Delete(url?: string) {
  return function (target, propertyKey: string, descriptor: PropertyDescriptor) {
    descriptor.value = function (...args: any[]): Observable<any> {
      const { prefix, copy_url, params } = paramBuilder(target, propertyKey, args, url)

      return (<HttpClient>this.http).delete(`${prefix}${copy_url}`, { params })
    }
  }
}

export function Put(url?: string): Function {
  return function (target, propertyKey: string, descriptor: PropertyDescriptor) {
    descriptor.value = function (...args: any[]): Observable<any> {
      const { prefix, copy_url, body, params } = paramBuilder(target, propertyKey, args, url)

      return (<HttpClient>this.http).put(`${prefix}${copy_url}`, body, {
        params,
      })
    }
  }
}

export function Request(method: 'POST' | 'GET', url: string): Function {
  return function (target, propertyKey: string, descriptor: PropertyDescriptor): void {
    descriptor.value = function (formData): Observable<any> {
      const { prefix, copy_url } = paramBuilder(target, propertyKey, arguments, url)
      const req = new HttpRequest(method, `${prefix}${copy_url}`, formData, {})
      console.log('req', req)
      return (<HttpClient>this.http).request(req).pipe(filter(event => event instanceof HttpResponse))
    }
  }
}
