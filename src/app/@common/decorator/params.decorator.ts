import 'reflect-metadata';
// import * as moment from 'moment';
// import { isUndefined } from '@common/function';
import { FILE_NAME_KEY, ARGS_TYPE_KEY, BASH_URI, PARAM_KEY, QUERY_KEY, REQUEST_BODY_KEY, HTTP_HEADERS_TOKEN } from '@common/const';

export interface Type<T> extends Function {
  new (...args: any[]): T;
}

export interface PipeTransform<T = any, R = any> {
  /**
   * Method to implement a custom pipe.  Called with two parameters
   *
   * @param value argument before it is received by route handler method
   * @param metadata contains metadata about the value
   */
  transform(value: T): R;
}


// export class ParseDatePipe implements PipeTransform {
//   transform([start, end]: Date[] | string[]) {
//     if (isUndefined(start, end)) {
//       return [start, end];
//     }
    
//     return [
//       moment(new Date(start)).format('YYYY-MM-DD 00:00:00'),
//       moment(new Date(end)).format('YYYY-MM-DD 23:59:59'),
//     ];
//   }
// }


export class ParseIntPipe implements PipeTransform {
  transform(value: string) {
    return parseFloat(value);
  }
}


export class ToString implements PipeTransform {
  transform(value: string) {
    return `${value}`;
  }
}


// const STRIP_COMMENTS = /((\/\/.*$)|(\/\*[\s\S]*?\*\/))/mg;
// const ARGUMENT_NAMES = /([^\s,]+)/g;

// function getParamNames(func: Function) {
//   const fnStr = func.toString().replace(STRIP_COMMENTS, '');
//   let result: any = fnStr.slice(fnStr.indexOf('(')+1, fnStr.indexOf(')')).match(ARGUMENT_NAMES);

//   if(result === null) {
//     result = [];
//   }
//   return result;
// }


export function BaseUri(uri: string) {
  return function (target: Object) {
    uri = uri.endsWith('/') ? uri : `${uri}/`;
    Reflect.defineMetadata(BASH_URI, uri, target);
  };
}


export function Query(key: string) {
  return (target: any, propertyKey: string | symbol, parameterIndex: number) => {
    const metadataValue = (Reflect.getMetadata(QUERY_KEY, target, propertyKey) || {});
    // const args = getParamNames(target[propertyKey]);
    // const key = args[parameterIndex];
    
    metadataValue[parameterIndex] = key;
    
    Reflect.defineMetadata(QUERY_KEY, metadataValue, target, propertyKey);
  };
}


export function Headers(key: string) {
  return (target: any, propertyKey: string | symbol, parameterIndex: number) => {
    const metadataValue = (Reflect.getMetadata(HTTP_HEADERS_TOKEN, target, propertyKey) || {});
    
    metadataValue[parameterIndex] = key;
    
    Reflect.defineMetadata(HTTP_HEADERS_TOKEN, metadataValue, target, propertyKey);
  };
}


export function Param(key = 'http_params') {
  return (target: any, propertyKey: string | symbol, parameterIndex: number) => {
    const metadataValue = (Reflect.getMetadata(PARAM_KEY, target, propertyKey) || {});
    // const paramtypes = Reflect.getMetadata('design:paramtypes', target, propertyKey) as any[];
    // const returntype = Reflect.getMetadata('design:returntype', target, propertyKey);

    // console.log('[paramtypes]', paramtypes);
    // console.log('[returntype]', returntype);

    // paramtypes.forEach((item, index) => {
    //   const isString = item === String;
    //   const isNumber = item === Number;
    //   const isObject = item === Object;

    //   isString && console.log(paramtypes, `[${index}]参数为[string]类型`);
    //   isNumber && console.log(paramtypes, `[${index}]参数为[number]类型`);
    //   isObject && console.log(paramtypes, `[${index}]参数为[object]类型`);
    // })
    
    metadataValue[parameterIndex] = key;
    
    Reflect.defineMetadata(PARAM_KEY, metadataValue, target, propertyKey);
  };
}


export function Body() {
  return (target: any, propertyKey: string | symbol, parameterIndex: number) => {
    const metadataValue = (Reflect.getMetadata(REQUEST_BODY_KEY, target, propertyKey) || {});

    metadataValue[parameterIndex] = 'body';
    
    Reflect.defineMetadata(REQUEST_BODY_KEY, metadataValue, target, propertyKey);
  };
}


export function FilenName() {
  return (target: any, propertyKey: string | symbol, parameterIndex: number) => {
    const metadataValue = (Reflect.getMetadata(FILE_NAME_KEY, target, propertyKey) || {});

    metadataValue[parameterIndex] = 'FileNameDecorator';
    
    Reflect.defineMetadata(FILE_NAME_KEY, metadataValue, target, propertyKey);
  };
}



export function Args(pipe: Type<PipeTransform>) {
  return (target: any, propertyKey: string | symbol, parameterIndex: number) => {
    const metadataValue = (Reflect.getMetadata(ARGS_TYPE_KEY, target, propertyKey) || {});

    metadataValue[parameterIndex] = pipe;
    
    Reflect.defineMetadata(ARGS_TYPE_KEY, metadataValue, target, propertyKey);
  };
}


export function Transfer(): Function {
  return function (target, propertyKey: string, descriptor: PropertyDescriptor) {
    const parameterMetadata = (Reflect.getOwnMetadata(ARGS_TYPE_KEY, target, propertyKey) || []);
    const metadataKeys = Reflect.ownKeys(parameterMetadata);
    const oldValue = descriptor.value;

    descriptor.value = function(...args: any[]) {
      const arr = [...args];

      for (const item of metadataKeys) {
        const key = parameterMetadata[item] as Type<PipeTransform>;
        const value = args[item];

        arr[item] = new key().transform(value);
      }
      
      return oldValue.apply(this, arr);
    };
  };
}
