import { BehaviorSubject } from 'rxjs';


export function DefaultValue<T = any>(value: T): any {
  return (target: any, propertyName: string): void => {
    target[propertyName] = value;
  };
}


export function Reactive<T = any>(): any {
  return (target: any, propertyName: string): void => {
    const subject = new BehaviorSubject<T>(null);

    Object.defineProperty(target, propertyName, {
      set(value: T) {
        subject.next(value);
      },
      get() {
        return subject.asObservable();
      },
    })
  };
}
