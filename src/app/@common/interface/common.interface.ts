
export type ValueOf<T> = T[keyof T];

export class ResultBody<T = any> {
  error: string = '';
  message: string = '一切 OK';
  status: string = '00000';
  timestamp: string = new Date().toISOString();
  constructor(
    public data: T
  ) {}
}


export interface Page<T> {
  records: T[],
  total: number,
  size: number,
  current: number,
  orders: unknown[],
  optimizeCountSql: boolean,
  hitCount: boolean,
  countId: number,
  maxLimit: number,
  searchCount: boolean,
  pages: number;
}
