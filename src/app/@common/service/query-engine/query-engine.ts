import { FormArray, FormControl, FormGroup, Validators } from '@angular/forms';
import { addDays, format } from 'date-fns';
import {
  DimensionValueVo,
  FilterItemVo,
  MetricVo,
} from '@api/query-engine/model';

export type DtType = 'dt' | 'ym' | 'yw';

/** 分区筛选参数 */
export class QueryDt {
  /**
   * 时间开始分区值
   * - day格式：yyyy-MM-dd
   * - month格式：yyyy-MM
   * - week格式：YYYY-WW
   */
  startTime = new FormControl<string>(null, {
    validators: Validators.required,
  });

  /**
   * 时间结束分区值，
   * - day格式：yyyy-MM-dd
   * - month格式：yyyy-MM
   * - week格式：YYYY-WW
   */
  endTime = new FormControl<string>(null, { validators: Validators.required });

  constructor(startTime?: string, endTime?: string) {
    startTime && this.startTime.patchValue(startTime);
    endTime && this.endTime.patchValue(endTime);
  }
}

/** 维度对象 */
export class DimensionVo {
  /** 维度id */
  id = new FormControl<number>(null);

  /** 英文名称 */
  extendName = new FormControl<string>(null);

  /** 同环比时间对比 dt-日环比，yw-周环比，ym-月环比 */
  predefineCompareType = new FormControl<string[]>(null);
}

/** 指标对象 */
export class MetricVoForm {
  /** 维度id */
  id = new FormControl<number>(null);

  /** 指标别名 */
  aliasName = new FormControl<string>(null);

  /** 英文名称 */
  extendName = new FormControl<string>(null);

  /** 指标类型 */
  type = new FormControl<number>(null);

  /** 自定义计算类型 */
  customType = new FormControl<string>(null);

  /** 比例维度,占总体时传空数组即可 */
  proportionDimension = new FormArray<FormGroup<DimensionVo>>([]);

  /** 筛选条件【新增：用于对比分析】 */
  filter = new FormGroup(new FilterForm());

  dataUnit = new FormControl<string>(null);
  
  yAxis = new FormControl<number>(null);

  constructor(options?: Partial<MetricVo>) {
    this.aliasName.patchValue(options.aliasName ?? null);
    this.extendName.patchValue(options.extendName ?? null);
    this.dataUnit.patchValue(options.dataUnit ?? null);
    this.yAxis.patchValue(options.yAxis ?? null);
    this.type.patchValue(options.type ?? null);

    if (options?.filter) {
      options.filter.items.forEach(item => {
        const control = new FormGroup(new FilterItemForm(item));

        this.filter.controls.items.push(control);
      });
    }
  }
}

/**
 * 运算标识;
 * - 条件运算公式 [>, <, =, >=, <=, !=, not null, in]
 * - 逻辑运算公式 [and,or]
 */
type Condition =
  | '>'
  | '<'
  | '='
  | '>='
  | '<='
  | '!='
  | 'not null'
  | 'in'
  | 'and'
  | 'or';

/**
 * 筛选值类型
 * - metrics (指标)
 * - dimension (维度)
 */
type ValueType = 'metrics' | 'dimension';

/**
 * 运算类型
 * 1. 逻辑运算
 * 2. 条件运算
 */
type ConditionType = 1 | 2;

export class DimensionValueForm {
  /** 编码【数字或者英文字符】 */
  key = new FormControl<string>(null);

  /** 码值【中文名称可用来展示】 */
  value = new FormControl<string>(null);

  constructor(options?: Partial<DimensionValueVo>) {
    options?.key && this.key.patchValue(options?.key);
    options?.value && this.value.patchValue(options?.value);
  }
}

export class FilterItemForm {
  /**
   * 运算类型
   * 1. 逻辑运算
   * 2. 条件运算
   */
  conditionType = new FormControl<ConditionType | number>(2, {
    nonNullable: true,
  });

  /**
   * 运算标识;
   * - 条件运算公式 [>, <, =, >=, <=, !=, not null, in]
   * - 逻辑运算公式 [and,or]
   */
  condition = new FormControl<Condition | string>('=', { nonNullable: true });

  /** 指标id/维度id */
  id = new FormControl<number>(null);

  /** 维度英文名 */
  extendName = new FormControl<string>(null);

  /** 筛选值 */
  value = new FormArray<FormGroup<DimensionValueForm>>([]);

  /**
   * 筛选值类型
   * - 指标(metrics)
   * - 维度(dimension)
   */
  valueType = new FormControl<ValueType | string>(null, {
    validators: [Validators.required],
  });

  constructor(
    options?: Partial<FilterItemVo>,
    control?: FormGroup<DimensionValueForm>
  ) {
    options?.conditionType &&
      this.conditionType.patchValue(options?.conditionType);
    options?.condition && this.condition.patchValue(options?.condition);
    options?.id && this.id.patchValue(options?.id);
    options?.extendName && this.extendName.patchValue(options?.extendName);

    if (control) {
      this.value.push(control);
    }

    if (options?.value) {
      options.value.forEach((value) => {
        this.value.push(new FormGroup(new DimensionValueForm(value)));
      });
    }
  }
}

class FilterForm {
  /** 筛选标识 */
  items = new FormArray<FormGroup<FilterItemForm>>([]);

  /** 筛选项列表 */
  type = new FormControl<string>(null);
}

export class QueryEngineForm {
  /**
   * @default day
   * 时间分区查询类型
   * - dt (日)
   * - ym (月)
   * - yw (周)
   * - 默认是日
   */
  dtType = new FormControl<DtType>('dt', { nonNullable: true });

  /** 时间对比分区查询 */
  compareDt?: FormGroup<QueryDt>;

  /**
   * @requires
   * 时间分区查询【必传参数】
   */
  dt = new FormGroup(
    new QueryDt(
      format(addDays(new Date(), -14), 'yyyy-MM-dd'),
      format(addDays(new Date(), -1), 'yyyy-MM-dd')
      // '2024-07-01',
      // '2024-07-31',
    )
  );

  /** 维度数组 */
  dimensions = new FormArray<FormGroup<DimensionVo>>([]);

  /** 额外的维度组合 */
  extraDimensionGroups = new FormArray<FormArray<FormGroup<DimensionVo>>>([]);

  /** 指标数组 */
  metrics = new FormArray<FormGroup<MetricVoForm>>([]);

  /** 筛选条件【用于指标筛选】 */
  filter = new FormGroup(new FilterForm());

  /** 场景
   * 1. 数据大盘
   * 2. A/B实验分析
   * 3. 分群数据指标
   * 4. 驾驶舱
   */
  scene = new FormControl(4, { nonNullable: true });
}
