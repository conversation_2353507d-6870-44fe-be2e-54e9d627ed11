import { Injectable, signal } from '@angular/core';
import { FormArray, FormGroup } from '@angular/forms';
import { addDays, format, addWeeks, addMonths } from 'date-fns';
import { toSignal } from '@angular/core/rxjs-interop';

import { sleep } from '@common/function';
import { FilterItemVo, MetricVo, QueryInputVo, SelectInputVo, SelectOutputVo } from '@api/query-engine/model';
import { DimensionVo, FilterItemForm, MetricVoForm, QueryDt, QueryEngineForm } from './query-engine';

export function setDefaultDateRange(dtControl, compareDtControl) {
  dtControl.patchValue({
    startTime: format(addDays(new Date(), -14), 'yyyy-MM-dd'),
    endTime: format(addDays(new Date(), -1), 'yyyy-MM-dd'),
  });

  compareDtControl.patchValue({
    startTime: format(addDays(new Date(), -21), 'yyyy-MM-dd'),
    endTime: format(addDays(new Date(), -8), 'yyyy-MM-dd'),
  });
}

export function setDefaultWeekRange(dtControl, compareDtControl) {
  dtControl.patchValue({
    startTime: format(addWeeks(new Date(), -2), 'YYYY-ww'),
    endTime: format(addWeeks(new Date(), -1), 'YYYY-ww'),
  });

  compareDtControl.patchValue({
    startTime: format(addWeeks(new Date(), -4), 'YYYY-ww'),
    endTime: format(addWeeks(new Date(), -3), 'YYYY-ww'),
  });
}

export function setDefaultMonthRange(dtControl, compareDtControl) {
  dtControl.patchValue({
    startTime: format(addMonths(new Date(), 0), 'yyyy-MM'),
    endTime: format(addMonths(new Date(), 0), 'yyyy-MM'),
  });

  compareDtControl.patchValue({
    startTime: format(addMonths(new Date(), -1), 'yyyy-MM'),
    endTime: format(addMonths(new Date(), -1), 'yyyy-MM'),
  });
}

@Injectable()
export class QueryEngineFormService {
  form = new FormGroup(new QueryEngineForm());
  support = signal<SelectOutputVo>(null);
  form$ = toSignal(this.form.valueChanges);

  get filterItems() {
    return this.form.controls.filter.controls.items;
  }

  get dimensions() {
    return this.form.controls.dimensions;
  }

  get extraDimensionGroups() {
    return this.form.controls.extraDimensionGroups;
  }

  get metrics() {
    return this.form.controls.metrics;
  }

  get dt() {
    return this.form.controls.dt;
  }

  get dtType() {
    return this.form.controls.dtType;
  }

  get compareDt() {
    return this.form.controls.compareDt;
  }

  // @Sleep(500)
  async init(value: string) {
    const { dt, dtType, dimensions, metrics, filter, compareDt } = JSON.parse(value) as QueryInputVo;

    this.dtType.reset(dtType);
    await sleep(100);
    this.dt.reset(dt);
    this.setMetrics(metrics);
    this.addFilters(filter.items);

    if (compareDt) {
      this.addDateCompare(compareDt.startTime, compareDt.endTime);
    }

    if (dimensions.length > 1) {
      const { extendName } = dimensions[1];
      this.addDimension(extendName);
    }
  }

  dtValue() {
    return this.compareDt.getRawValue();
  }

  value(): QueryInputVo {
    const value = this.form.getRawValue();
    const dtType = this.form.get('dtType').value;
    const dtDimension = {
      id: null,
      extendName: dtType,
      predefineCompareType: [],
    };

    if (dtType === 'dt') {
      dtDimension.predefineCompareType = ['yw'];
    }

    return {
      ...value,
      dimensions: [dtDimension, ...value.dimensions],
    };
  }

  setExtraDimension(value: string) {
    const control = new FormGroup(new DimensionVo());
    const arrayControl = new FormArray([control]);

    control.controls.extendName.patchValue(value);
    this.extraDimensionGroups.clear();
    this.extraDimensionGroups.push(arrayControl);
  }

  setDimension(value: string) {
    const control = new FormGroup(new DimensionVo());
    control.controls.extendName.patchValue(value);
    this.dimensions.clear();
    this.dimensions.push(control);
  }

  addDimension(value: string) {
    const control = new FormGroup(new DimensionVo());
    control.controls.extendName.patchValue(value);
    this.dimensions.push(control);
  }

  setMetric(value: string) {
    const control = new FormGroup(new MetricVoForm());
    control.controls.extendName.patchValue(value);
    this.metrics.clear();
    this.metrics.push(control);
  }

  setMetrics(values: MetricVo[]) {
    if (values && Array.isArray(values)) {
      this.metrics.clear();
      this.addMetrics(values);
    }
  }

  addMetrics(values: MetricVo[]) {
    values.forEach((item) => {
      const control = new FormGroup(new MetricVoForm(item));

      this.metrics.push(control);
    });
  }

  addFilters(values: FilterItemVo[]) {
    values.forEach((item) => {
      const control = new FormGroup(new FilterItemForm(item));

      this.filterItems.push(control);
    });
  }

  addDateCompare(startTime?: string, endTime?: string) {
    const control = new FormGroup(new QueryDt(startTime, endTime));

    this.form.addControl('compareDt', control);
  }

  removeDateCompare() {
    this.form.removeControl('compareDt');
  }

  hasDateCompare() {
    return this.form.get('compareDt') !== null;
  }

  getSelectedList(): SelectInputVo {
    const metricEnNameList = this.metrics.value.map((item) => item.extendName);
    const dimensionEnNameList = [
      ...this.filterItems.value,
      ...this.dimensions.value,
    ].map((item) => item.extendName);

    dimensionEnNameList.push(this.form.get('dtType').value);

    return {
      dimensionEnNameList: [...new Set(dimensionEnNameList)],
      metricEnNameList,
      scene: 4,
    };
  }
}
