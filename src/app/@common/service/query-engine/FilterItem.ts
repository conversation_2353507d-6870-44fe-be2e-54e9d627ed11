import { FormControl, FormArray, FormGroup } from '@angular/forms';
import { Condition, ConditionType, ValueType, FilterItemVo } from '@api/caerus/model/FilterItemVo';
import { DimensionValueVo } from '@api/query-engine/model';

export class FilterValueItem {
  key = new FormControl<string>(null);
  value = new FormControl<string>(null);

  constructor(props?: Partial<DimensionValueVo>) {
    if (props) {
      this.key.patchValue(props.key);
      this.value.patchValue(props.value);
    }
  }
}

export class FilterItem {
  
  /** 
   * @name 运算类型 逻辑运算 1, 条件运算 2
   */
  conditionType = new FormControl<ConditionType>(2, { nonNullable: true });
  

  /** 
   * 运算标识
   */
  condition = new FormControl<Condition>('=', { nonNullable: true });

  
  /** 
   * @name 指标id/维度id
   */
  id = new FormControl<number>(null);

  
  /** 
   * @name 筛选值类型 指标(metrics)/维度(dimension) 
   */
  valueType = new FormControl<ValueType>(null);


  /** 
   * @name 维度英文名
   */
  extendName = new FormControl<string>(null);


  /**
   * @name 指标筛选类型 在指标筛选时用
   */
  predefineCompareType = new FormControl<string>('', { nonNullable: true });


  /** 
   * @name 筛选值
   */
  value = new FormArray<FormGroup<FilterValueItem>>([]);


  /** 
   * @name 子条件 [只有逻辑运算才支持]
   */
  subFilter: FormArray<FormGroup<FilterItem>>;


  constructor(props?: Partial<FilterItemVo>) {
    if (props) {
      this.id.patchValue(props.id ?? null);
      this.condition.patchValue(props.condition ?? '=');
      this.conditionType.patchValue(props.conditionType ?? 2);
      this.valueType.patchValue(props.valueType ?? null);
      this.predefineCompareType.patchValue(props.predefineCompareType ?? '');
      this.extendName.patchValue(props.extendName);

      if (props.value) {
        props.value.forEach(item => {
          const control = new FormGroup(new FilterValueItem(item));
          this.value.push(control);
        })
      }

      if (props.subFilter) {
        this.subFilter = new FormArray<FormGroup<FilterItem>>([]);
        
        props.subFilter.forEach(item => {
          const control = new FormGroup(new FilterItem({ ...item }));
          
          this.subFilter.push(control);
        })
      }
    }
  }

}