import { Injectable } from '@angular/core';
import { AbstractControl, FormGroup } from '@angular/forms';

@Injectable()
export abstract class BasePaginationFormService<
  T extends { [U in keyof T]: AbstractControl<any, any>; },
  D extends { [key: string]: string | number | any }
> {

  public abstract form: FormGroup<T>;
  
  public get pageIndexControl(): AbstractControl<number, number> {
    return this.get('pageNum');
  }

  public get pageSizeControl(): AbstractControl<number, number> {
    return this.get('pageSize');
  }

  set<K extends keyof D>(key: K, value: D[K]) {
    this.form.get(key as any).patchValue(value as any);
  }

  get<K extends keyof D>(key: K): AbstractControl<D[K], D[K]> {
    return this.form.get(key as any);
  }

  reload() {
    this.form.reset(this.form.value);
  }

  restore() {
    this.form.reset();
  }

}