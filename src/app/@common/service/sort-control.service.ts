import { Injectable } from '@angular/core';
import { AbstractControl, FormControl, FormGroup, FormArray } from '@angular/forms';
import { Observable } from 'rxjs';
import { startWith } from 'rxjs/operators';

import { Before } from '@common/decorator';
import { isNotNull } from '@common/function';


@Injectable()
export class SortControlService {

  public form: FormGroup;

  public sortChange$: Observable<any>;
  

  public init(form: FormGroup): void {
    this.form = form;
    this.sortChange$ = this.sort.valueChanges.pipe(startWith(this.sort.value));
  }


  private get sort(): FormArray {
    return this.form.get('sort') as FormArray;
  }


  public getSortControl(sortName: string): AbstractControl {
    return this.sort.controls.find(control => control.value.sortName === sortName);
  }


  private getSortControlIndex(sortName: string): number {
    return this.sort.controls.findIndex(control => control.value.sortName === sortName);
  }


  public remove(sortName: string): void {
    const index = this.getSortControlIndex(sortName);
    
    if (index >= 0) {
      this.sort.removeAt(index);
    }
  }


  @Before((ctx, sortName) => ctx.remove(sortName))
  public add(sortName: string, sortType: 'asc' | 'desc' | null): void {
    if (isNotNull(sortType)) {
      const control = new FormGroup({
        sortName: new FormControl(sortName),
        sortType: new FormControl(sortType),
      });

      this.sort.push(control);
    }
  }


  public clear(): void {
    this.sort.clear();
  }

  
  public restore(): void {
    this.clear();
  }
}
