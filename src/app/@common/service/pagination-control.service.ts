import { Injectable } from '@angular/core';
import { AbstractControl } from '@angular/forms';
import { BehaviorSubject } from 'rxjs';

@Injectable()
export class PaginationControlService {

  private pageIndexControl: AbstractControl<number, number>;
  private pageSizeControl: AbstractControl<number, number>;
  
  private _total = new BehaviorSubject<number>(0);
  public total$ = this._total.asObservable();

  
  set total(value: number) {
    this._total.next(value || 0);
  }
  
  
  public init(
    pageIndexControl: AbstractControl<number, number>,
    pageSizeControl: AbstractControl<number, number>
  ): void {
    this.pageIndexControl = pageIndexControl;
    this.pageSizeControl = pageSizeControl;
  }


  public pageIndexChange(value: number): void {
    this.pageIndexControl.patchValue(value);
  }


  public pageSizeChange(value: number): void {
    this.pageSizeControl.patchValue(value);
  }

  
  public get pageIndex(): number {
    return this.pageIndexControl.value;
  }


  public get pageSize(): number {
    return this.pageSizeControl.value;
  }

}
