import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';

export interface Role {
  group: string;
  id: number;
  name: string;
}

@Injectable({
  providedIn: 'root'
})
export class UserService {

  private userIdSource   = new BehaviorSubject<string | null>(null);
  private userNameSource = new BehaviorSubject<string | null>(null);
  private roleSource   = new BehaviorSubject<Role[] | null>(null);

  public userId$   = this.userIdSource.asObservable();
  public userName$ = this.userNameSource.asObservable();
  public role$   = this.roleSource.asObservable();
  

  setUserId(value: string) {
    this.userIdSource.next(value);
  }


  getUserId() {
    return this.userIdSource.getValue();
  }


  setUserName(value: string) {
    this.userNameSource.next(value);
  }


  getUserName() {
    return this.userNameSource.getValue();
  }


  setRoles(value: Role[]) {
    this.roleSource.next(value);
  }


  getRoles() {
    return this.roleSource.getValue();
  }


  hasRole(role: string) {
    return this.getRoles().some(item => item.name === role);
  }

}
