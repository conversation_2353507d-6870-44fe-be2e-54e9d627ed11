import { Injectable, inject, signal } from '@angular/core';
import { CookieService } from 'ngx-cookie-service';
import BsTrack from 'dida-web-common/src/BsTrack';

import { VERSION, isDev } from '@common/const';
import { UserInfoService } from '../../api/user-info.service';


export enum BuriedPointActionType {
  /** 浏览 */
  VIEW = 'view',
  /** 下载 */
  DOWNLOAD = 'download',
  /** 分享 */
  SHARE = 'share',
  /** 查询 */
  QUERY = 'query',
  /** 其他 */
  OTHER = 'other',
  /** 点击 */
  CLICK = 'click',
}


@Injectable({
  providedIn: 'root'
})
export class BuriedPointService {

  private _bsTrack: BsTrack;
  private readonly _userInfo = inject(UserInfoService);
  private readonly cookieService = inject(CookieService);

  username = signal('');

  constructor() {    
    if (isDev()) {
      this.username.set('开发环境');
    } else {
      this._userInfo.fetchCurrentUser().subscribe(res => {
        console.log(res);
        const { user, ts } = res;

        this._userInfo.privateName = user;
        this._userInfo.ts = ts;
        this.fetchUserName(user);
      })
    }
  }


  private fetchUserName(userName: string) {
    this._userInfo.fetchUserName(userName, 'en').subscribe(res => {
      const pinyinName = res.data;

      window.localStorage.setItem('caerus.auth.username.py', pinyinName);
      this.cookieService.set('caerus.auth.username.py', pinyinName, { path: '/' });
      this._bsTrack = new BsTrack({ name: 'Caerus', type: 'teese', cid: pinyinName }, { version: VERSION.full }, []);
    });
    
    this._userInfo.fetchUserName(userName, 'cn').subscribe(res => {
      if (res.data) {
        const name = res.data;

        window.localStorage.setItem('caerus.auth.username', name);
        this.cookieService.set('caerus.auth.username', name, { path: '/' });
        
        this.username.set(name);
      }
    })
  }


  addStat(code: string, data = {}, callback?: (data: { code: number, message: string }) => void) {
    this._bsTrack && this._bsTrack.addStat(code, data, callback);
  }

}
