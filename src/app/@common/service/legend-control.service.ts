import { computed, Injectable, signal } from '@angular/core'
import { SeriesItem } from '@common/chart/highcharts'
import { BehaviorSubject } from 'rxjs'

export interface LegendItemClickEvent {
  before: string
  current: string
  shiftKey: boolean
}

export function LegendItemClickHandler(legendControl: LegendControlService) {
  return function (event) {
    const { shiftKey } = event?.browserEvent || {}
    const { name: current } = event.target

    legendControl.emit({ current, shiftKey })

    return false
  }
}

@Injectable()
export class LegendControlService {
  private _showType = new BehaviorSubject<'number' | 'ratio'>(null)
  showType$ = this._showType.asObservable()

  legends = signal<SeriesItem[]>(null)
  visibleLegends = computed(() => this.legends()?.filter(item => item.visible) || null)
  visibleLegendNames = computed(() => this.visibleLegends()?.map(item => item.name) || null)

  private _legendItemClickEvent = new BehaviorSubject<LegendItemClickEvent>(null)
  legendItemClickEvent$ = this._legendItemClickEvent.asObservable()

  before = signal<string>(null)

  get activeLegendItem() {
    return this._legendItemClickEvent.getValue()
  }

  showType() {
    return this._showType.value
  }

  setShowType(type: 'number' | 'ratio') {
    this._showType.next(type)
  }

  emit(value: Omit<LegendItemClickEvent, 'before'>) {
    if (value) {
      const { current, shiftKey } = value
      this._legendItemClickEvent.next({ before: this.before(), current, shiftKey })
      this.before.set(current)
    } else {
      this._legendItemClickEvent.next(null)
      this.before.set(null)
    }
  }

  reset() {
    this._showType.next(null)
    this._legendItemClickEvent.next(null)
    this.before.set(null)
  }
}
