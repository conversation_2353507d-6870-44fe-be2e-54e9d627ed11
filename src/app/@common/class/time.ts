import {
  getDateFromYearWeek,
  getFirstDayForWeek,
  getFirstDayOfISOWeek,
  isDate,
  isEmpty,
  isNotUndefined,
  isNotUndefinedOrNotNull,
  toNumber,
} from '@common/function'
import {
  addDays,
  addWeeks,
  addMonths,
  addYears,
  format,
  subDays,
  startOfWeek,
  getDay,
  isBefore,
  subMonths,
  setMonth,
  subYears,
} from 'date-fns'
import { DatePipe } from '@angular/common'

const unitMap = new Map([
  ['s', '秒'],
  ['m', '分钟'],
  ['h', '小时'],
  ['d', '天'],
  ['w', '周'],
  ['M', '个月'],
  ['y', '年'],
])

/** 获取上周六 */
function getLastSaturday() {
  /// 获取今天的日期
  const today = new Date()

  // 获取今天是星期几 (0为周日，6为周六)
  const dayOfWeek = getDay(today)

  // 如果今天是周六或之后（周六），返回上周六
  if (dayOfWeek === 6) {
    return subDays(today, dayOfWeek + 1) // 获取上周六
  }

  // 如果今天是周一到周四，返回上上周六
  return subDays(today, dayOfWeek + 1 + 7) // 获取上上周六
}

/** 获取本周五或上周五 */
function getThisOrLastFriday() {
  const today = new Date()
  const dayOfWeek = getDay(today)
  const startOfThisWeek = startOfWeek(today, { weekStartsOn: 1 }) // 1 = Monday
  const thisFriday = addDays(startOfThisWeek, 4) // 本周五（从本周一减去3天）
  const lastFriday = subDays(thisFriday, 7) // 上周五

  if (dayOfWeek === 0 || dayOfWeek === 6) {
    return thisFriday
  }

  return lastFriday
}

/** 获取一个日期的当月第一天和最后一天 */
export function getMonthFirstAndLastDay(date: string) {
  if (date === null) {
    return [null, null]
  }

  if (/^\d{4}-\d{2}-\d{2}$/.test(date)) {
    // yyyy-MM-dd
    const [year, month] = date.split('-').map(toNumber)
    const firstDay = new Date(year, month - 1, 1)
    const lastDay = new Date(year, month, 0)

    return [format(firstDay, 'yyyy-MM-dd'), format(lastDay, 'yyyy-MM-dd')]
  }
}

/** 获取一个日期的当月第一天和今天 */
export function getMonthFirstAndNowDay(date: string) {
  if (date === null) {
    return [null, null]
  }

  if (/^\d{4}-\d{2}-\d{2}$/.test(date)) {
    // yyyy-MM-dd
    const [year, month] = date.split('-').map(toNumber)
    const firstDay = new Date(year, month - 1, 1)
    const lastDay = new Date(year, month, 0)
    const today = new Date()

    // 如果lastDay大于今天，则返回今天，否则返回lastDay
    const endDay = isBefore(today, lastDay) ? today : lastDay

    return [format(firstDay, 'yyyy-MM-dd'), format(endDay, 'yyyy-MM-dd')]
  }
}

/** 
 * 获取一个日期相对于当前月总天数的进度
 * @param {string} date yyyy-MM-dd
 */
export function getMonthProgress(input: string) {
  if (input === null) {
    return null
  }

  if (/^\d{4}-\d{2}-\d{2}$/.test(input)) {
    const [year, month, dayOfMonth] = input.split('-').map(toNumber)
    const lastDay = new Date(year, month, 0)

    // 计算当月天数
    const totalDaysInMonth = lastDay.getDate()

    // 计算进度
    const progress = (dayOfMonth / totalDaysInMonth) * 100

    return +progress.toFixed(2)
  }
}

export function getWeekRangeStartAndEnd(
  startYear: number,
  startWeek: number,
  endYear: number,
  endWeek: number
): [Date, Date] {
  // 获取开始周的第一天
  const startDate = getFirstDayOfISOWeek(startYear, startWeek)

  // 获取结束周的第一天
  const endWeekStart = getFirstDayOfISOWeek(endYear, endWeek)

  // 结束周的最后一天（开始日期加6天）
  const endDate = addDays(endWeekStart, 6)

  return [startDate, endDate]
}

export class Time {
  constructor(
    public from: string,
    public to: string
  ) {}

  static getType(value: string) {
    switch (true) {
      case /now/.test(value):
        return 'relative'
      case /\d{4}-\d{2}-\d{2}/.test(value):
        return 'custom'
      case isEmpty(value):
      default:
        return undefined
    }
  }

  static isRelative(value: string) {
    return Time.getType(value) === 'relative'
  }

  static isCustom(value: string) {
    return Time.getType(value) === 'custom'
  }

  static format(from: string, to: string) {
    if (/^now-/.test(from) && to === 'now-1d') {
      if (Time.isRelative(from)) {
        const suffix = from.match(/(\w$)/g)[0]
        const unit = unitMap.get(suffix)

        return from.replace(/(now-)(\d+)(\w$)/, `近$2${unit}`)
      }
    } else {
      switch (true) {
        case from === 'now/d' && to === 'now/d':
          return '今天'
        case from === 'now-2d' && to === 'now-1d':
          return '昨天'
        case from === 'now-3d' && to === 'now-2d':
          return '前天'
        case from === 'now/w' && to === 'now/w':
          return '本周'
        case from === 'now-2w' && to === 'now-1w':
          return '上周'
        case from === 'now/M' && to === 'now-1d':
          return '本月'
        case from === 'now-2M' && to === 'now-1M':
          return '上月'
        case from === 'now/y' && to === 'now-1d':
          return '今年'
        case from === 'now-2y' && to === 'now-1y':
          return '去年'
        case from === 'now-w6' && to === 'now/w5':
          return '上周六～本周五'

        // yw
        case from === 'week-4w' && to === 'week-1w':
          return '过去4周'
        case from === 'week-8w' && to === 'week-1w':
          return '过去8周'
        case from === 'week-12w' && to === 'week-1w':
          return '过去12周'
        case from === 'week-52w' && to === 'week-1w':
          return '过去52周'
        case from === 'week-104w' && to === 'week-1w':
          return '过去104周'
        // case from === 'week-M1' && to === 'week-1w':
        //   return '本月'
        // case from === 'week-y1' && to === 'week-1w':
        //   return '今年'
        // case from === 'week-y2' && to === 'week-1w':
        //   return '近2年'
        case from === 'week-4w' && to === 'week-0w':
          return '近4周'
        case from === 'week-8w' && to === 'week-0w':
          return '近8周'
        case from === 'week-12w' && to === 'week-0w':
          return '近12周'
        case from === 'week-52w' && to === 'week-0w':
          return '近52周'
        case from === 'week-104w' && to === 'week-0w':
          return '近104周'
        case from === 'week-M1' && to === 'week-0w':
          return '本月'
        case from === 'week-y1' && to === 'week-0w':
          return '今年'
        case from === 'week-y2' && to === 'week-0w':
          return '近2年'

        // ym
        case from === 'month-3m' && to === 'month-1m':
          return '过去3月'
        case from === 'month-6m' && to === 'month-1m':
          return '过去6月'
        case from === 'month-12m' && to === 'month-1m':
          return '过去12月'
        case from === 'month-24m' && to === 'month-1m':
          return '过去24月'

        case from === 'month-3m' && to === 'month':
          return '近3月'
        case from === 'month-6m' && to === 'month':
          return '近6月'
        case from === 'month-12m' && to === 'month':
          return '近12月'
        case from === 'month-24m' && to === 'month':
          return '近24月'

        // case from === 'month-2m' && to === 'month':
        //   return '近3月'
        // case from === 'month-5m' && to === 'month':
        //   return '近6月'
        // case from === 'month-11m' && to === 'month':
        //   return '近12月'
        // case from === 'month-23m' && to === 'month':
        //   return '近24月'

        case from === 'month-y1' && to === 'month':
          return '今年'
        case from === 'month-1y1' && to === 'month-1y0':
          return '去年'
        case from === 'month-2y1' && to === 'month':
          return '近2年'

        case from === '' && to === '':
          return ''

        default:
          // if (isNotUndefined(from, to)) {
          //   const endLabel = to === 'now' ? '至今' : format(+to, 'yyyy-MM-dd');

          //   return `${format(+from, 'yyyy-MM-dd')} ~ ${endLabel}`;
          // }
          return null
      }
    }
  }

  static startDateMap = (() => {
    const now = new Date().setHours(0, 0, 0, 0)
    const startDateForMonth = new Date(now).setDate(1)
    const startDateForYear = new Date(startDateForMonth).setMonth(0)

    return new Map([
      // dt
      ['now-7d', addDays(now, -7)],
      ['now-14d', addDays(now, -14)],
      ['now-30d', addDays(now, -30)],
      ['now-60d', addDays(now, -60)],
      ['now-90d', addDays(now, -90)],
      ['now-180d', addDays(now, -180)],
      ['now-365d', addDays(now, -365)],
      ['now-730d', addDays(now, -730)],
      ['now-3M', addMonths(now, -3)],
      ['now-6M', addMonths(now, -6)],
      ['now-1y', addYears(now, -1)],
      ['now/d', addDays(now, 0)],
      ['now-2d', addDays(now, -1)],
      ['now-3d', addDays(now, -2)],
      ['now/w', getFirstDayForWeek(addWeeks(now, 0))],
      ['now-2w', getFirstDayForWeek(addWeeks(now, -1))],
      ['now/M', addMonths(startDateForMonth, 0)],
      ['now-2M', addMonths(startDateForMonth, -1)],
      ['now/y', addYears(startDateForYear, 0)],
      ['now-2y', addYears(startDateForYear, -1)],
      ['now-w6', getLastSaturday()],

      // yw
      ['week-4w', addWeeks(now, -4)],
      ['week-8w', addWeeks(now, -8)],
      ['week-12w', addWeeks(now, -12)],
      ['week-52w', addWeeks(now, -52)],
      ['week-104w', addWeeks(now, -104)],
      ['week-M1', addMonths(startDateForMonth, 0)],
      ['week-y1', addYears(startDateForYear, 0)],
      ['week-y2', subYears(startDateForYear, 2)],

      // ym
      ['month-3m', subMonths(now, 3)],
      ['month-6m', subMonths(now, 6)],
      ['month-12m', subMonths(now, 12)],
      ['month-24m', subMonths(now, 24)],
      ['month-2m', subMonths(now, 2)],
      ['month-5m', subMonths(now, 5)],
      ['month-11m', subMonths(now, 11)],
      ['month-23m', subMonths(now, 23)],
      ['month-y1', addYears(startDateForYear, 0)],
      ['month-1y1', addYears(startDateForYear, -1)],
      ['month-2y1', addYears(startDateForYear, -2)],
    ])
  })()

  static endDateMap = (() => {
    const now = new Date().setHours(23, 59, 59, 999);
    const yesterday = addDays(now, -1);
    const startDateForMonth = new Date(now).setDate(1)
    const startDateForYear = new Date(startDateForMonth).setMonth(0)

    return new Map([
      // dt
      ['now', addDays(now, 0)],
      ['now/d', addDays(now, 0)],
      ['now-1d', addDays(now, -1)],
      ['now-2d', addDays(now, -2)],
      ['now/w', addDays(new Date(), 0)],
      ['now-1w', addDays(getFirstDayForWeek(addWeeks(now, -1)), 6)],
      ['now/w5', getThisOrLastFriday()],
      ['now/M', addDays(new Date(), 0)],
      ['now-1M', addDays(addMonths(startDateForMonth, 0), -1)],
      ['now/y', addDays(new Date(), 0)],
      ['now-1y', addDays(addYears(startDateForYear, 0), -1)],

      // yw
      ['week-1w', getFirstDayForWeek(addWeeks(now, -1))],
      ['week-0w', addWeeks(yesterday, 0)],

      // ym
      ['month-1m', subMonths(now, 1)],
      ['month', addDays(now, -1)],
      ['month-1y0', setMonth(subYears(now, 1), 11)],
    ])
  })()

  static getDate(mode: 'date' | 'week' | 'month', value: string | Date[] | Time) {
    if (Array.isArray(value)) {
      const [from, to] = value
      // if (mode === 'week') {
      //   const start = `${format(new Date(from), 'yyyy')}-${format(new Date(from), 'II')}`
      //   const startYear = Number(start.split('-')[0])
      //   const startWeek = Number(start.split('-')[1])
      //   const end = `${format(new Date(to), 'yyyy')}-${format(new Date(to), 'II')}`
      //   const endYear = Number(end.split('-')[0])
      //   const endWeek = Number(end.split('-')[1])
      //   const res = getWeekRangeStartAndEnd(startYear, startWeek, endYear, endWeek)
      //   return res
      // }
      return [new Date(from), new Date(to)]
    } else {
      const [from, to] = value instanceof Time ? [value.from, value.to] : value.split(',').map(v => v.trim())
      const reg = /(\d{4}-\d{2})?(-\d{2})/
      if (mode === 'week' && reg.test(from) && reg.test(to)) {
        return [getDateFromYearWeek(from), getDateFromYearWeek(to)]
      }

      if (reg.test(from) && reg.test(to)) {
        return [new Date(from), new Date(to)]
      } else {
        // if (mode === 'week') {
        //   const start = `${format(Time.startDateMap.get(from), 'yyyy')}-${format(Time.startDateMap.get(from), 'II')}`
        //   const startYear = Number(start.split('-')[0])
        //   const startWeek = Number(start.split('-')[1])
        //   const end = `${format(Time.endDateMap.get(to), 'yyyy')}-${format(Time.endDateMap.get(to), 'II')}`
        //   const endYear = Number(end.split('-')[0])
        //   const endWeek = Number(end.split('-')[1])
        //   const res = getWeekRangeStartAndEnd(startYear, startWeek, endYear, endWeek)
        //   return res
        // }
        return [Time.startDateMap.get(from), Time.endDateMap.get(to)]
      }
    }
  }

  toString() {
    return `${this.from},${this.to}`
  }

  toLocalString() {
    return Time.format(this.from, this.to)
  }
}
