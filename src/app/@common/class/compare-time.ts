import {
  getDateFromYearWeek,
  getFirstDayForWeek,
  getFirstDayOfISOWeek,
  isDate,
  isEmpty,
  isNotUndefined,
  isNotUndefinedOrNotNull,
} from '@common/function'
import {
  addDays,
  addWeeks,
  addMonths,
  addYears,
  format,
  subDays,
  subMonths,
  subYears,
  startOfWeek,
  getDay,
  isBefore,
} from 'date-fns'

const unitMap = new Map([
  ['s', '秒'],
  ['m', '分钟'],
  ['h', '小时'],
  ['d', '天'],
  ['w', '周'],
  ['M', '个月'],
  ['y', '年'],
])

function getWeekRangeStartAndEnd(startYear: number, startWeek: number, endYear: number, endWeek: number): [Date, Date] {
  // 获取开始周的第一天
  const startDate = getFirstDayOfISOWeek(startYear, startWeek)

  // 获取结束周的第一天
  const endWeekStart = getFirstDayOfISOWeek(endYear, endWeek)

  // 结束周的最后一天（开始日期加6天）
  const endDate = addDays(endWeekStart, 6)

  return [startDate, endDate]
}

export class CompareTime {
  constructor(
    public from: string,
    public to: string
  ) {
    // console.log(this);
  }

  static getType(value: string) {
    switch (true) {
      case /now/.test(value):
        return 'relative'
      case /\d{4}-\d{2}-\d{2}/.test(value):
      case /\d{4}-\d{2}/.test(value):
        return 'custom'
      case isEmpty(value):
      default:
        return undefined
    }
  }

  static isRelative(value: string) {
    return CompareTime.getType(value) === 'relative'
  }

  static isCustom(value: string) {
    return CompareTime.getType(value) === 'custom'
  }

  static format(from: string, to: string) {
    switch (true) {
      case from === 'now-1d' && to === 'now-1d':
        return '日环比'
      case from === 'now-7d' && to === 'now-7d':
        return '周同比'
      case from === 'now/1M' && to === 'now/1M':
        return '上月同期'
      case from === 'now/1y' && to === 'now/1y':
        return '去年同期'
      case from === 'now-2w' && to === 'now-2w':
        return '上周'
      case from === 'now-2M' && to === 'now-2M':
        return '上月'
      case from === 'now-2y' && to === 'now-2y':
        return '去年'

      case from === 'week-1w' && to === 'week-1w':
        return '周环比'
      case from === 'week-1y' && to === 'week-1y':
        return '去年同期'

      case from === 'month-1m' && to === 'month-1m':
        return '月环比'
      case from === 'month-1y' && to === 'month-1y':
        return '去年同期'

      default:
        return null // 为了判定自定义日期
    }
  }

  // result.set('上周', [getFirstDayForWeek(addWeeks(now, -1)), addDays(getFirstDayForWeek(addWeeks(now, -1)), 6)]);
  // result.set('上月', [addMonths(startDateForMonth, -1),      addDays(addMonths(startDateForMonth, 0), -1)]);
  // result.set('去年', [addYears(startDateForYear, -1),        addDays(addYears(startDateForYear, 0), -1)]);

  /**
    1天前 | 7天前 | 14天前 | 上月同期 | 去年同期 | 上周 | 上月 | 去年
    ----- | ----- | ----- | ----- | ----- | ----- | ----- | ----- |
    now-1d, ? | now-7d, ? | now-14d, ? | now-1M, ? | now-1y, ? | now-2w, now-1w | now-2M, now-1M | now-2y, now-1y
   */
  static startDateMap(date: Date) {
    const now = new Date().setHours(0, 0, 0, 0)
    const startDateForMonth = new Date(now).setDate(1)
    const startDateForYear = new Date(startDateForMonth).setMonth(0)

    // console.log('input:', format(date, 'yyyy-MM-dd'));

    return new Map([
      ['now-1d', addDays(date, -1)],
      ['now-7d', addDays(date, -7)],
      ['now/1M', subMonths(date, 1)],
      ['now/1y', subYears(date, 1)],
      ['now-2w', getFirstDayForWeek(addWeeks(now, -1))],
      ['now-2M', addMonths(startDateForMonth, -1)],
      ['now-2y', addYears(startDateForYear, -1)],

      // yw
      ['week-1w', getFirstDayForWeek(addWeeks(date, -1))],
      // ['week-1y',  subYears(getFirstDayForWeek(addWeeks(date, 1)), 1)],
      ['week-1y', subYears(addWeeks(date, 0), 1)],

      // ym
      ['month-1m', subMonths(date, 1)],
      ['month-1y', subYears(date, 1)],
    ])
  }

  static endDateMap(date: Date) {
    const now = new Date().setHours(0, 0, 0, 0)
    const startDateForMonth = new Date(now).setDate(1)
    const startDateForYear = new Date(startDateForMonth).setMonth(0)

    return new Map([
      ['now-1d', addDays(date, -1)],
      ['now-7d', addDays(date, -7)],
      ['now/1M', subMonths(date, 1)],
      ['now/1y', subYears(date, 1)],
      ['now-2w', addDays(getFirstDayForWeek(addWeeks(now, -1)), 6)],
      ['now-2M', addDays(addMonths(startDateForMonth, 0), -1)],
      ['now-2y', addDays(addYears(startDateForYear, 0), -1)],

      // yw
      ['week-1w', getFirstDayForWeek(addWeeks(date, -1))],
      ['week-1y', subYears(addWeeks(date, 1), 1)],

      // ym
      ['month-1m', subMonths(date, 1)],
      ['month-1y', subYears(date, 1)],
    ])
  }

  static getDate(mode: 'date' | 'week' | 'month', value: string | Date[], baseStart?: Date, baseEnd?: Date) {
    if (Array.isArray(value)) {
      const [from, to] = value
      // if (mode === 'week') {
      //   const start = `${format(new Date(from), 'yyyy')}-${format(new Date(from), 'II')}`
      //   const startYear = Number(start.split('-')[0])
      //   const startWeek = Number(start.split('-')[1])
      //   const end = `${format(new Date(to), 'yyyy')}-${format(new Date(to), 'II')}`
      //   const endYear = Number(end.split('-')[0])
      //   const endWeek = Number(end.split('-')[1])
      //   const res = getWeekRangeStartAndEnd(startYear, startWeek, endYear, endWeek)
      //   return res
      // }
      return [new Date(from), new Date(to)]
    } else {
      const [from, to] = value.split(',')
      const reg = /(\d{4}-\d{2})?(-\d{2})/

      if (mode === 'week' && reg.test(from) && reg.test(to)) {
        // const _start = getDateFromYearWeek(from)
        // const _end = getDateFromYearWeek(to)
        // const start = `${format(_start, 'yyyy')}-${format(_start, 'II')}`
        // const startYear = Number(start.split('-')[0])
        // const startWeek = Number(start.split('-')[1])
        // const end = `${format(_end, 'yyyy')}-${format(_end, 'II')}`
        // const endYear = Number(end.split('-')[0])
        // const endWeek = Number(end.split('-')[1])
        // return getWeekRangeStartAndEnd(startYear, startWeek, endYear, endWeek)
        return [getDateFromYearWeek(from), getDateFromYearWeek(to)]
      }

      if (reg.test(from) && reg.test(to)) {
        return [new Date(from.replace(/-/g, '/')), new Date(to.replace(/-/g, '/'))]
      }

      if (baseStart !== undefined && baseEnd !== undefined) {
        // if (mode === 'week') {
        //   const start = `${format(this.startDateMap(baseStart).get(from), 'yyyy')}-${format(this.startDateMap(baseStart).get(from), 'II')}`
        //   const startYear = Number(start.split('-')[0])
        //   const startWeek = Number(start.split('-')[1])
        //   const end = `${format(this.endDateMap(baseEnd).get(to), 'yyyy')}-${format(this.endDateMap(baseEnd).get(to), 'II')}`
        //   const endYear = Number(end.split('-')[0])
        //   const endWeek = Number(end.split('-')[1])
        //   const res = getWeekRangeStartAndEnd(startYear, startWeek, endYear, endWeek)
        //   return res
        // }
        return [this.startDateMap(baseStart).get(from), this.endDateMap(baseEnd).get(to)]
      } else {
        console.log('else')
      }
    }
  }

  toString() {
    return `${this.from},${this.to}`
  }

  toLocalString() {
    return CompareTime.format(this.from, this.to)
  }
}
