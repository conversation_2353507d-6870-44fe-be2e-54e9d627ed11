import { addDays, addWeeks, addMonths, addHours, addMinutes, format, getISOWeek, getISOWeekYear } from 'date-fns';
import { QueryDt, QueryOutputHeaderVo } from '@api/query-engine/model';
import { _pad, getFirstDayOfISOWeek, groupBy, toNumber } from '@common/function';
import { DtType } from '@common/service/query-engine';

type FillType = DtType | string;

export class FillDataOfConstitute {

  dateFields: string;
  dimensionField: string;
  types: string[];
  keys: string[];
  startDate: string;
  endDate: string;

  constructor(public data: any[], headers: {[key: string]: QueryOutputHeaderVo }, { startTime, endTime }: QueryDt = { startTime: '', endTime: '' }) {
    const [dateFields] = this._getDateFields(headers);
    const [dimensionField] = this._getDimensionField(headers);
    const groupData = groupBy<{[key: string]: { [key: string]: string }}>(data, dimensionField);

    this.dateFields = dateFields;
    this.dimensionField = dimensionField;
    this.types = Object.keys(groupData).sort();
    this.keys = Object.keys(headers).filter(key => key !== dateFields && key !== dimensionField );
    
    this.startDate = startTime.replace(/-/g, '/');
    this.endDate = endTime.replace(/-/g, '/');
  }


  private _fillDate() {
    const results = [];
    let day = new Date(this.startDate);
    
    while (day <= new Date(this.endDate)) {
      const dateStr = format(day, 'yyyy-MM-dd');

      day = addDays(day, 1);
      this.types.forEach(type => {
        const obj = {
          [`${this.dateFields}`]: dateStr,
          [`${this.dimensionField}`]: type
        };

        this.keys.forEach(key => {
          obj[key] = null;
        })

        results.push(obj);
      })
    }

    return results;
  }


  private _fillWeek() {
    const results = [];
    const [startYear, startWeek] = this.startDate.split('/').map(toNumber);
    const [endYear, endWeek] = this.endDate.split('/').map(toNumber);
    const endDate = getFirstDayOfISOWeek(endYear, endWeek);
    let day = getFirstDayOfISOWeek(startYear, startWeek);

    while (day <= new Date(endDate)) {
      const year = getISOWeekYear(day);
      const week = getISOWeek(day);
      const dateStr = `${year}-${_pad(week)}`;

      day = addWeeks(day, 1);
      this.types.forEach(type => {
        const obj = {
          [`${this.dateFields}`]: dateStr,
          [`${this.dimensionField}`]: type
        };

        this.keys.forEach(key => {
          obj[key] = null;
        })

        results.push(obj);
      })
    }
    
    return results;
  }


  private _fillMonth() {
    const results = [];
    const [startYear, startMonth] = this.startDate.split('/').map(toNumber);
    const [endYear, endMonth] = this.endDate.split('/').map(toNumber);
    const endDate = new Date(endYear, endMonth - 1);
    let day = new Date(startYear, startMonth - 1);

    while (day <= endDate) {
      const dateStr = format(day, 'yyyy-MM');

      day = addMonths(day, 1);
      this.types.forEach(type => {
        const obj = {
          [`${this.dateFields}`]: dateStr,
          [`${this.dimensionField}`]: type
        };

        this.keys.forEach(key => {
          obj[key] = null;
        })

        results.push(obj);
      })
    }

    return results;
  }


  public fill(dtType: DtType) {
    let results = [];

    switch(dtType) {
      case 'dt':
        results = this._fillDate();
        break;

      case 'yw':
        results = this._fillWeek();
        break;

      case 'ym':
        results = this._fillMonth();
        break;
    }

    return results.map(result => {
      const origin = this.data.find(item => (
        item[this.dateFields] === result[this.dateFields] &&
        item[this.dimensionField] === result[this.dimensionField]
      ))

      return origin || result;
    });
  }


  private _getDateFields(headers: { [key: string]: QueryOutputHeaderVo }) {
    return Object.keys(headers).filter(key => {
      return headers[key].dateFilter === 1;
    })
  }


  private _getDimensionField(headers: { [key: string]: QueryOutputHeaderVo }) {
    return Object.keys(headers).filter(key => {
      const { dateFilter, columnType } = headers[key];

      return (
        dateFilter === 0 && 
        columnType === 'dimension'
      );
    })
  }

}


export class FillDataOfTrend {

  dateFields: string;
  startDate: string;
  endDate: string;
  keys: string[];

  constructor(
    public data: any[], 
    headers: {[key: string]: QueryOutputHeaderVo }, 
    { startTime, endTime }: QueryDt = { startTime: '', endTime: '' }
  ) {
    const [dateFields] = this._getDateFields(headers);

    this.dateFields = dateFields;
    this.keys = Object.keys(headers).filter(key => key !== dateFields);

    this.startDate = startTime.replace(/-/g, '/');
    this.endDate = endTime.replace(/-/g, '/');
  }


  private _fillDate() {
    const results = [];
    let day = new Date(this.startDate);
    
    while (day <= new Date(this.endDate)) {
      const dateStr = format(day, 'yyyy-MM-dd');
      const obj = { [`${this.dateFields}`]: dateStr };

      day = addDays(day, 1);

      this.keys.forEach(key => {
        obj[key] = null;
      })

      results.push(obj);
    }

    return results;
  }


  private _fillWeek() {
    const results = [];
    const [startYear, startWeek] = this.startDate.split('/').map(toNumber);
    const [endYear, endWeek] = this.endDate.split('/').map(toNumber);
    const endDate = getFirstDayOfISOWeek(endYear, endWeek);
    let day = getFirstDayOfISOWeek(startYear, startWeek);

    while (day <= new Date(endDate)) {
      const year = getISOWeekYear(day);
      const week = getISOWeek(day);
      const dateStr = `${year}-${_pad(week)}`;
      const obj = { [`${this.dateFields}`]: dateStr };

      day = addWeeks(day, 1);

      this.keys.forEach(key => {
        obj[key] = null;
      })

      results.push(obj);
    }
    
    return results;
  }


  private _fillMonth() {
    const results = [];
    const [startYear, startMonth] = this.startDate.split('/').map(toNumber);
    const [endYear, endMonth] = this.endDate.split('/').map(toNumber);
    const endDate = new Date(endYear, endMonth - 1);
    let day = new Date(startYear, startMonth - 1);

    while (day <= endDate) {
      const dateStr = format(day, 'yyyy-MM');
      const obj = { [`${this.dateFields}`]: dateStr };

      day = addMonths(day, 1);

      this.keys.forEach(key => {
        obj[key] = null;
      })

      results.push(obj);
    }

    return results;
  }


  private _fillDataByMinutes(timeKey: string, interval: number) {
    const results = [];

    Object.keys(groupBy(this.data, this.dateFields)).forEach(dateStr => {
      const [year, month, date] = dateStr.split('-').map(toNumber);
      const endDate = new Date(year, month - 1, date, 23, 59, 59);
      let startDate = new Date(year, month - 1, date, 0, 0, 0);

      while (startDate <= endDate) {
        startDate = addMinutes(startDate, interval);
        const hours = startDate.getHours();
        const minutes = startDate.getMinutes();
        const obj = {
          [`${this.dateFields}`]: dateStr,
          [`${timeKey}`]: `${_pad(hours)}:${_pad(minutes)}:00`
        };

        this.keys
          .filter(key => key !== timeKey)
          .forEach(key => {
            obj[key] = null;
          })

        results.push(obj);
      }

      const extraObj = {
        [`${this.dateFields}`]: dateStr,
        [`${timeKey}`]: `24:00:00`
      };

      this.keys
        .filter(key => key !== timeKey)
        .forEach(key => {
          extraObj[key] = null;
        })

      results.push(extraObj);
    })

    return results;
  }


  private _fillDataByHours(timeKey: string) {
    const results = [];

    Object.keys(groupBy(this.data, this.dateFields)).forEach(dateStr => {
      const [year, month, date] = dateStr.split('-').map(toNumber);
      const endDate = new Date(year, month - 1, date, 23, 59, 59);
      let startDate = new Date(year, month - 1, date, 0, 0, 0);

      while (startDate <= endDate) {
        const hours = startDate.getHours();
        const obj = {
          [`${this.dateFields}`]: dateStr,
          [`${timeKey}`]: `${_pad(hours)}:00:00`
        };

        startDate = addHours(startDate, 1);

        this.keys
          .filter(key => key !== timeKey)
          .forEach(key => {
            obj[key] = null;
          })

        results.push(obj);
      }

      const extraObj = {
        [`${this.dateFields}`]: dateStr,
        [`${timeKey}`]: `24:00:00`
      };

      this.keys
        .filter(key => key !== timeKey)
        .forEach(key => {
          extraObj[key] = null;
        })

      results.push(extraObj);
    })

    return results;
  }


  public fill(type: FillType) {
    let results = [];

    switch(type) {
      case 'dt':
        results = this._fillDate();
        break;

      case 'yw':
        results = this._fillWeek();
        break;

      case 'ym':
        results = this._fillMonth();
        break;

      case 'one_min_time_slice':
        results = this._fillDataByMinutes(type, 1);
        break;

      case 'five_min_time_slice':
        results = this._fillDataByMinutes(type, 5);
        break;

      case 'one_h_time_slice':
        results = this._fillDataByHours(type);
        break;

      default: // 如果传入的类型无法识别，如因指标变动导致，则返回原始数据
        return this.data;
    }

    return results.map(result => {
      const origin = this.data.find(item => {
        return (
          item[this.dateFields] === result[this.dateFields] &&
          (
            ['one_min_time_slice', 'five_min_time_slice', 'one_h_time_slice'].includes(type)
              ? (<string>result[type]).startsWith(item[type])
              : true
          ) // 如果需要填充的是每分钟、每5分钟、每小时，则需要同事判断时间字段内容
        );
      })

      return origin || result;
    });
  }


  private _getDateFields(headers: { [key: string]: QueryOutputHeaderVo }) {
    return Object.keys(headers).filter(key => {
      return headers[key].dateFilter === 1;
    })
  }


}