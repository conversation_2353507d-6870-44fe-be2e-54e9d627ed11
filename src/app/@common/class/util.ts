import { AbstractControl, ValidatorFn } from '@angular/forms';


export class Util {

  /**
   * 中英文字符最大长度
   */
  static CharMaxLength(maxlength: number = 255): ValidatorFn {
    return (control: AbstractControl): any => {
      const value = control.value;
      if (value === null || value === undefined) {
        return null;
      }
      return value.replace(/[^\x00-\xff]/g, '01').length > maxlength ? {charmaxlength: true} : null;
    };
  }


  static checkPhone(): ValidatorFn {
    return ({ value }: AbstractControl): any => {
      if (!value) { return null }
      
      return new RegExp(/^1(3|4|5|7|8)\d{9}$/).test(value) ? null : { phone: true };
    };
  }


  static requiredArray(): ValidatorFn {
    return ({ value }: AbstractControl): any => {
      if (!value) { return null }
      
      return value.length ? null : { requiredArray: true };
    };
  }


  /**
   * 验证checkbox中至少一个选中
   */
  static AtLeastOneIsTrue = (control: AbstractControl): any => {
    const obj = Object.keys(control.value).filter(item => control.get(item).value === true);
    return Boolean(obj.length) ? null : {nomatch: true};
  }


  static isEmpty(): ValidatorFn {
    return ({ value }: AbstractControl): any => {
      if (
        !Array.isArray(value) || // 如果value不是数组类型
        Array.isArray(value) && value.length > 0 // 如果value是数组且长度大于0
      ) {
        return null;
      }

      return { empty: true }
    }
  }


  static downloadFile(blob: any, fileName: string = Math.random().toString(32).substr(2), format: string = '.xlsx'): void {
    if (blob) {
      const objectUrl = window.URL.createObjectURL(blob);
      const anchor    = document.createElement('a');

      anchor.setAttribute('href',     objectUrl);
      anchor.setAttribute('download', `${fileName}${format}`);
      anchor.click();
      anchor.remove();

      window.URL.revokeObjectURL(blob);
    }
  }


  static isMoblie(): boolean {
    return /iPhone|iPad|iPod|Android/i.test(navigator.userAgent);
  }


  static jumpTeese() {
    var url_path = window.location.pathname + window.location.search + window.location.hash;
    var url_path_b64 = btoa(encodeURIComponent(url_path));
    var target_host = location.hostname;
    var teese_url = "https://teese.didapinche.com/authCenter";
    var jump_args = teese_url + "?target=" + url_path_b64 + "&target_host=" + target_host;

    window.open(jump_args, '_self');
  }


  static sum(items: any[], key?: string): number {
    if (!items || items?.length === 0) {
      return 0;
    }

    if (key) {
      return items.map(item => item[key]).reduce((prev,next) => prev + next);
    }

    return (
      items.reduce((prev,next) => prev + next)
    );
  }

}
