import { ConnectedOverlayPositionChange, ConnectionPositionPair } from '@angular/cdk/overlay';
import { Version } from '@angular/core';

export const VERSION = new Version('1.9.2');
export const CHANGE_LOG_URL = '';
export const HELP_DOC_URL = '';
export const IP_PATTERN = '(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)';
export const PASSWORD_PATTERN = `^(?=.*[a-zA-Z])(?=.*\\d)(?=.*[!$^&*@+=|{}';./ \",、！#￥%…（）<>?~—【】‘；：”“。，？-])[a-zA-Z\\d!$^&*@+=|{}';,.<>/?~！#￥%…—【】‘ \"；：”“。，、？-]`;
export const PHONE_PATTERN = `^1(3|4|5|6|7|8|9)\\d{9}$`;
export const CHINESE_CHARS_PATTERN = /^[\u4e00-\u9fa5]{0,}$/;
export const IS_CHINESE_CHARS = (value: string) => new RegExp(CHINESE_CHARS_PATTERN).test(value);

const hasPath = (searchString: string) => {
  return window.location.href.indexOf(searchString) > -1;
}

/** 开发环境 */
export const isDev = () => hasPath('localhost:4500');
/** 测试环境 https://demeter.didapinche.com/test-caerus/ */
export const isTest = () => hasPath('test-caerus');
/** 预上线环境 https://caerus.didapinche.com/preonline/cockpit */
export const isPreview = () => hasPath('preonline');
/** 仿真环境 https://demeter.didapinche.com/aliyun-caerus */
export const isEmulator = () => hasPath('aliyun-caerus');
/** 线上环境 https://caerus.didapinche.com/ */
export const isProd = () => hasPath('caerus.didapinche.com');

export const trace = (isDev() || /debug/.test(location.search)) ? console.log : (() => {});

export const API_URL = (
  isPreview() 
    ? '/caerus-pre' 
    : isProd()     
      ? '/caerus' 
      : isTest()
        ? '//demeter.didapinche.com/test/api/caerus'
        : isEmulator()
          ? '//demeter.didapinche.com/aliyun/api/caerus'
          : isDev()
            ? '/api'
    : ''
);


export const URA_API_URL = (
  isPreview()  ? '/ura' :
  isProd()     ? '/ura' : 
  isTest()     ? '//demeter.didapinche.com/test/api/ura' : 
  isEmulator() ? '//demeter.didapinche.com/aliyun/api/ura' :  
  isDev()      ? '/ura/api' :
  ''
);

export const QUERY_ENGINE_API = (
  isPreview()  ? '/query/engine-pre' :
  isProd()     ? '/query/engine' : 
  isTest()     ? '//demeter.didapinche.com/test/api/query/engine' : 
  isEmulator() ? '//demeter.didapinche.com/aliyun/api/query/engine' :  
  isDev()      ? '/query/engine/api' :
  ''
);
                           
export const POSITION_MAP = {
  top:          new ConnectionPositionPair({ originX: 'center', originY: 'top'    }, { overlayX: 'center', overlayY: 'bottom' }),
  topCenter:    new ConnectionPositionPair({ originX: 'center', originY: 'top'    }, { overlayX: 'center', overlayY: 'bottom' }),
  topLeft:      new ConnectionPositionPair({ originX: 'start',  originY: 'top'    }, { overlayX: 'start',  overlayY: 'bottom' }),
  topRight:     new ConnectionPositionPair({ originX: 'end',    originY: 'top'    }, { overlayX: 'end',    overlayY: 'bottom' }),
  right:        new ConnectionPositionPair({ originX: 'end',    originY: 'center' }, { overlayX: 'start',  overlayY: 'center' }),
  rightTop:     new ConnectionPositionPair({ originX: 'end',    originY: 'top'    }, { overlayX: 'start',  overlayY: 'top'    }),
  rightBottom:  new ConnectionPositionPair({ originX: 'end',    originY: 'bottom' }, { overlayX: 'start',  overlayY: 'bottom' }),
  bottom:       new ConnectionPositionPair({ originX: 'center', originY: 'bottom' }, { overlayX: 'center', overlayY: 'top'    }),
  bottomCenter: new ConnectionPositionPair({ originX: 'center', originY: 'bottom' }, { overlayX: 'center', overlayY: 'top'    }),
  bottomLeft:   new ConnectionPositionPair({ originX: 'start',  originY: 'bottom' }, { overlayX: 'start',  overlayY: 'top'    }),
  bottomRight:  new ConnectionPositionPair({ originX: 'end',    originY: 'bottom' }, { overlayX: 'end',    overlayY: 'top'    }),
  left:         new ConnectionPositionPair({ originX: 'start',  originY: 'center' }, { overlayX: 'end',    overlayY: 'center' }),
  leftTop:      new ConnectionPositionPair({ originX: 'start',  originY: 'top'    }, { overlayX: 'end',    overlayY: 'top'    }),
  leftBottom:   new ConnectionPositionPair({ originX: 'start',  originY: 'bottom' }, { overlayX: 'end',    overlayY: 'bottom' }),
};

export const DEFAULT_CASCADER_POSITIONS = [
  POSITION_MAP.bottomLeft,
  POSITION_MAP.bottomRight,
  POSITION_MAP.topLeft,
  POSITION_MAP.topRight
];


export type POSITION_TYPE = keyof typeof POSITION_MAP;


export function getPlacementName(position: ConnectedOverlayPositionChange): string | undefined {
  for (const placement in POSITION_MAP) {
    if (
      position.connectionPair.originX  === POSITION_MAP[placement as POSITION_TYPE].originX  &&
      position.connectionPair.originY  === POSITION_MAP[placement as POSITION_TYPE].originY  &&
      position.connectionPair.overlayX === POSITION_MAP[placement as POSITION_TYPE].overlayX &&
      position.connectionPair.overlayY === POSITION_MAP[placement as POSITION_TYPE].overlayY
    ) {
      return placement;
    }
  }
  return undefined;
}
