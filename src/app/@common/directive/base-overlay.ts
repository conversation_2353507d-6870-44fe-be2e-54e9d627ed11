import { ChangeDetectorRef, Directive, ElementRef, EventEmitter, Output, ViewChild, inject, signal, effect } from '@angular/core';
import { CdkOverlayOrigin, Overlay, CdkConnectedOverlay, ScrollStrategy, ConnectionPositionPair, ConnectedOverlayPositionChange, ScrollStrategyOptions } from '@angular/cdk/overlay';

import { Before } from '@common/decorator';
import { DEFAULT_CASCADER_POSITIONS, getPlacementName } from '@common/const';
import { EventInterceptor } from '@common/function';

@Directive()
export class BaseOverlayDirective {

  @ViewChild(CdkOverlayOrigin, { static: true })
  public cdkOverlayOrigin: CdkOverlayOrigin;

  @ViewChild(CdkConnectedOverlay, { static: false })
  public overlayConnect: CdkConnectedOverlay;

  @ViewChild(Overlay, { static: false })
  public overlay: Overlay;

  @Output()
  public visibleChange = new EventEmitter<boolean>();

  public visible = signal(false);
  public scrollStrategy: ScrollStrategy;
  public position = signal('');
  public listOfPositions: ConnectionPositionPair[] = [...DEFAULT_CASCADER_POSITIONS];

  protected readonly cdr: ChangeDetectorRef;
  protected readonly host: ElementRef<HTMLElement>;
  protected readonly scrollStrategyOptions: ScrollStrategyOptions;

  constructor() {
    this.cdr = inject(ChangeDetectorRef);
    this.host = inject(ElementRef);
    this.scrollStrategyOptions = inject(ScrollStrategyOptions);
    this.scrollStrategy = this.scrollStrategyOptions.close();

    effect(() => {
      this.visibleChange.emit(this.visible());
    })
  }


  onPositionChange(value: ConnectedOverlayPositionChange): void {
    const placement = getPlacementName(value);
    const position = placement.replace(/top|bottom/, '').toLocaleLowerCase();

    this.position.set(position);
  }


  handleOutside(event: MouseEvent): void {
    if (!this.host.nativeElement.contains(event.target as HTMLElement)) {
      if (event.target instanceof HTMLElement) {
        const { className } = (<HTMLElement>event.target);
        const excludes = ['ant-select', 'ant-picker'];
        const isNotInclude = !excludes.some(item => className?.startsWith(item));

        if (isNotInclude) {
          this.close();
        }
      } else {
        this.close();
      }
    }
  }


  open() {
    this.visible.set(true);
  }

  
  close() {
    this.visible.set(false);
  }


  @Before(EventInterceptor)
  toggle(event: MouseEvent): void {
    this.visible() ? this.close() : this.open();
  }
  
}