import { AfterViewInit, Directive, InjectionToken, OnDestroy, inject } from '@angular/core'
import { BuriedPointService } from '@common/service'
import { DurationPipe } from '@shared/pipes/duration'

export const PAGE_NAME = new InjectionToken('page_name')

@Directive({
  selector: '[appPageEnterLeave]',
  providers: [DurationPipe],
})
export class PageEnterLeaveDirective implements AfterViewInit, OnDestroy {
  readonly source_type = inject(PAGE_NAME)
  readonly buriedPointService = inject(BuriedPointService)
  readonly durationPipe = inject(DurationPipe)
  readonly enterTime = Date.now()

  ngAfterViewInit(): void {
    this.buriedPointService.addStat('dida_dpm_caerus_page_enter', {
      page_name: this.source_type,
    })
  }

  ngOnDestroy(): void {
    const duration = Date.now() - this.enterTime

    this.buriedPointService.addStat('dida_dpm_caerus_page_leave', {
      page_name: this.source_type,
      duration: this.durationPipe.transform(duration),
    })
  }
}
