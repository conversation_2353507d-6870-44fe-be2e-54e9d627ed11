import { AfterViewInit, Directive, ElementRef, inject, input, OnDestroy, output } from '@angular/core';

@Directive({
  selector: '[appIntersection]',
})
export class IntersectionDirective implements AfterViewInit, OnDestroy {

  private _elementRef = inject(ElementRef);
  private _host = this._elementRef.nativeElement as HTMLElement;
  private _observer: IntersectionObserver;

  visible = output<void>();
  root = input<HTMLElement>(null);
  rootMargin = input('0px');
  once = input(true);
  threshold = input<number | number[]>(0.1);

  constructor() {
    const options = {
      root: this.root(), // 默认是视口
      rootMargin: this.rootMargin(),
      threshold: this.threshold(),
    };

    const callback = (entries, observer) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          // console.log('元素进入视口');
          // console.log(entry);
          this.visible.emit();
          if (this.once()) {
            this._observer.unobserve(this._host);
          }
        } else {
          // console.log('元素离开视口');
        }
      });
    };

    // 创建 IntersectionObserver 实例
    this._observer = new IntersectionObserver(callback, options);
  }
  

  ngAfterViewInit(): void {
    this._observer.observe(this._host);
  }


  ngOnDestroy(): void {
    this._observer.disconnect();
  }

}
