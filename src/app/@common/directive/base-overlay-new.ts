import { ChangeDetectorRef, Directive, ElementRef, inject, signal, effect, viewChild, output, DestroyRef } from '@angular/core';
import { CdkOverlayOrigin, Overlay, CdkConnectedOverlay, ScrollStrategy, ConnectionPositionPair, ConnectedOverlayPositionChange, ScrollStrategyOptions, ScrollDispatcher, OverlayContainer } from '@angular/cdk/overlay';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

import { Before } from '@common/decorator';
import { DEFAULT_CASCADER_POSITIONS, getPlacementName } from '@common/const';
import { EventInterceptor } from '@common/function';

@Directive()
export class NewBaseOverlayDirective {

  protected destroyRef = inject(DestroyRef);
  public overlayContainer = inject(OverlayContainer, { optional: true });

  public overlay = viewChild(Overlay);
  public overlayConnect = viewChild(CdkConnectedOverlay);
  public cdkOverlayOrigin = viewChild(CdkOverlayOrigin);
  public cdkConnectedOverlay = viewChild(CdkConnectedOverlay);
  public visibleChange = output<boolean>();

  public visible = signal(false);
  public scrollStrategy: ScrollStrategy;
  public position = signal('');
  public listOfPositions: ConnectionPositionPair[] = [...DEFAULT_CASCADER_POSITIONS];

  protected readonly cdr: ChangeDetectorRef;
  protected readonly host: ElementRef<HTMLElement>;
  protected readonly scrollStrategyOptions: ScrollStrategyOptions;

  constructor() {
    this.cdr = inject(ChangeDetectorRef);
    this.host = inject(ElementRef);
    this.scrollStrategyOptions = inject(ScrollStrategyOptions);
    this.scrollStrategy = this.scrollStrategyOptions.close();
    const scrollDispatcher = inject(ScrollDispatcher, { optional: true });
    
    effect(() => {
      this.visibleChange.emit(this.visible());
    })

    if (scrollDispatcher) {
      scrollDispatcher.scrolled().pipe(takeUntilDestroyed(this.destroyRef)).subscribe(() => {
        this.overlayConnect()?.overlayRef?.updatePosition();
      })
    }
  }


  onPositionChange(value: ConnectedOverlayPositionChange): void {
    const placement = getPlacementName(value);
    const position = placement.replace(/top|bottom/, '').toLocaleLowerCase();

    this.position.set(position);
  }


  handleOutside(event: MouseEvent): void {
    if (!this.host.nativeElement.contains(event.target as HTMLElement)) {
      if (event.target instanceof HTMLElement) {
        const { className } = (<HTMLElement>event.target);
        const excludes = ['ant-select', 'ant-picker'];
        const isNotInclude = !excludes.some(item => className?.startsWith(item));

        if (isNotInclude) {
          this.close();
        }
      } else {
        this.close();
      }
    }
  }


  open() {
    this.visible.set(true);
    setTimeout(() => {
      // const dom = this.overlayConnect().overlayRef.hostElement?.parentElement?.classList;
      const dom = this.overlayContainer.getContainerElement().classList;
      
      dom?.add('z-1001!');
      dom?.remove('z-50!');
    })
  }

  
  close() {
    this.visible.set(false);
  }


  @Before(EventInterceptor)
  toggle(event: MouseEvent): void {
    this.visible() ? this.close() : this.open();
  }
  
}