// import { Directive, InjectionToken, inject } from '@angular/core';
// import { sleep } from '@common/function';
// import { BuriedPointActionType, BuriedPointService } from '@common/service';

// export const PAGE_NAME = new InjectionToken('page_name');

// @Directive({
//   standalone: true,
//   selector: '[appRenderTime]'
// })
// export class RenderTimeDirective {

//   private buriedPointService = inject(BuriedPointService);
//   private source_type = inject(PAGE_NAME);
//   private startTime: number;
//   private endTime: number;

//   constructor() {
//     this.start();
//   }


//   private addStat() {
//     const { source_type } = this;
//     const duration = (this.endTime - this.startTime).toFixed(2);

//     // console.log(`页面 ${source_type} 渲染完成，总耗时${duration}ms`);

//     this.buriedPointService.addStat('dida_dpm_page_render_time', {
//       duration,
//       source_type,
//     });

//     this.buriedPointService.addStat('dida_dpm_ast_page_view', {
//       source_type,
//       action_type: BuriedPointActionType.VIEW
//     });
//   }


//   start() {
//     this.startTime = performance.now();
//   }
  

//   end() {
//     !this.endTime && (async () => {
//       await sleep(100);
//       this.addStat();
//     })();
//     this.endTime = performance.now();
//   }
  
// }
