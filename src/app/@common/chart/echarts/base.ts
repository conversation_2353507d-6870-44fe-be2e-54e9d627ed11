export class xAxisItem {
  type = 'category';
  data: string[];
  axisTick = {
    alignWithLabel: true
  };
}


export class yAxisItem {
  type = 'value';
  interval?: number;
  min?: number;
  max?: number;
}

export class ItemStyle {
  color = 'auto';
  borderColor = '#000';
  borderWidth = 0;
  borderType = 'solid';
  borderRadius = 0;
  shadowOffsetX = 0;
  shadowOffsetY = 0;
  opacity = 1;

  decal = {
    symbol: 'rect',
    symbolSize: 1,
    symbolKeepAspect: true,
    color: 'rgba(0, 0, 0, 0.2)',
    dashArrayX: [1, 0],
    dashArrayY: [4, 3],
    dirty: false,
    backgroundColor: null,
    rotation: -0.5235987755982988,
    maxTileWidth: 512,
    maxTileHeight: 512,
  };
}

interface LineStyle {
  type: 'solid' | 'dashed' | 'dotted' | number[],
  width: number;
}

export class SeriesItem {
  name: string;
  type = 'line';
  stack: string;
  xAxisIndex = 0;
  yAxisIndex = 0;
  data: number[];
  itemStyle: ItemStyle;
  label = { show: false };
  // emphasis = {focus: 'series'};
  symbolSize = 3;
  lineStyle: LineStyle = {
    type: 'solid',
    width: 2,
  };
}


export class BaseEcharts {

  tooltip = {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    },
  };
  
  legend: {
    show: boolean;
    data?: string[]
  } = { show: false };
  
  xAxis: xAxisItem | xAxisItem[] = new xAxisItem();
  yAxis: yAxisItem | yAxisItem[] = new yAxisItem();
  series: SeriesItem[] = [];

  setSeries(series: SeriesItem[]) {
    this.series = series;
  }
  
  getOption() {
    return this;
  }
  
}