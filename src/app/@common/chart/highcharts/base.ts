import {
  DashStyleValue,
  OptionsStackingValue,
  ColorString,
  GradientColorObject,
  PatternObject,
  XAxisOptions,
  YAxisOptions,
  TooltipOptions,
  PointMarkerOptionsObject,
  PlotOptions,
  LegendOptions,
} from 'highcharts'

export class SeriesItem {
  name: string
  type: string
  xAxis: number
  yAxis = 0
  data:
    | number[]
    | Array<{
        x: number
        y: number
        color?: ColorString | GradientColorObject | PatternObject
        yw?: {
          diff: number
          ratio: number
        }
        // 自定义属性
        weather: string
      }>
    | Array<Array<Date | number>>
    | any[] = []
  dashStyle: DashStyleValue
  visible = true

  // 条形图或柱状图可用
  pointWidth?: number
  stack?: number | string
  stacking?: OptionsStackingValue

  // 折线图可用
  lineWidth = 2

  linkedTo: string
  color: ColorString | GradientColorObject | PatternObject
  marker: PointMarkerOptionsObject

  // 自定义属性
  isPercent = false
  /** 是否为占比 */
  isProportion = false
  dataUnit: string
  weatherMap: Map<string, string>
  zIndex: number
  extraData?: any
  pointPadding?: number
  pointPlacement?: number
  fillOpacity?: number
  opacity?: number
  legendSymbolColor?: any
}

export class BaseHighCharts {
  chart: any = {
    type: 'column',
    backgroundColor: '#ffffff',
    zoomType: 'x',
  }

  title = {
    text: '',
  }

  subtitle = {
    text: '',
  }

  legend: LegendOptions = {
    verticalAlign: 'bottom',
  }

  tooltip: TooltipOptions = {
    shared: true,
    useHTML: true,
    outside: true,
    hideDelay: 0,
    style: {
      zIndex: 9999, // 设置 tooltip 的 z-index
    },
  }

  colors = [
    '#5087EC',
    '#68BBC4',
    '#58A55C',
    '#F2BD42',
    '#EE752F',
    '#B0665D',
    '#E4C477',
    '#A3C2FB',
    '#A0D0D5',
    '#98B3E2',
    '#DE868F',
    '#F4CE98',
    '#B4C8D9',
    '#93D2F3',
    '#4095E5',
    '#7F83F7',
    '#E99D42',
    '#CBA43F',
    '#BFBF3D',
    '#81B337',
    '#347CAF',
    '#377F7F',
    '#FCCA00',
    '#B886F8',
    '#A16222',
    '#5087EC',
    '#68BBC4',
    '#58A55C',
    '#F2BD42',
    '#EE752F',
    '#B0665D',
    '#E4C477',
    '#A3C2FB',
    '#A0D0D5',
    '#98B3E2',
    '#DE868F',
    '#F4CE98',
    '#B4C8D9',
    '#93D2F3',
    '#4095E5',
    '#7F83F7',
    '#E99D42',
    '#CBA43F',
    '#BFBF3D',
    '#81B337',
    '#347CAF',
    '#377F7F',
    '#FCCA00',
    '#B886F8',
    '#A16222',
    '#5087EC',
    '#68BBC4',
    '#58A55C',
    '#F2BD42',
    '#EE752F',
    '#B0665D',
    '#E4C477',
    '#A3C2FB',
    '#A0D0D5',
    '#98B3E2',
    '#DE868F',
    '#F4CE98',
    '#B4C8D9',
    '#93D2F3',
    '#4095E5',
    '#7F83F7',
    '#E99D42',
    '#CBA43F',
    '#BFBF3D',
    '#81B337',
    '#347CAF',
    '#377F7F',
    '#FCCA00',
    '#B886F8',
    '#A16222',
    '#5087EC',
    '#68BBC4',
    '#58A55C',
    '#F2BD42',
    '#EE752F',
    '#B0665D',
    '#E4C477',
    '#A3C2FB',
    '#A0D0D5',
    '#98B3E2',
    '#DE868F',
    '#F4CE98',
    '#B4C8D9',
    '#93D2F3',
    '#4095E5',
    '#7F83F7',
    '#E99D42',
    '#CBA43F',
    '#BFBF3D',
    '#81B337',
    '#347CAF',
    '#377F7F',
    '#FCCA00',
    '#B886F8',
    '#A16222',
    '#5087EC',
    '#68BBC4',
    '#58A55C',
    '#F2BD42',
    '#EE752F',
    '#B0665D',
    '#E4C477',
    '#A3C2FB',
    '#A0D0D5',
    '#98B3E2',
    '#DE868F',
    '#F4CE98',
    '#B4C8D9',
    '#93D2F3',
    '#4095E5',
    '#7F83F7',
    '#E99D42',
    '#CBA43F',
    '#BFBF3D',
    '#81B337',
    '#347CAF',
    '#377F7F',
    '#FCCA00',
    '#B886F8',
    '#A16222',
  ]

  xAxis: XAxisOptions | XAxisOptions[] = {
    categories: [] as string[],
    lineWidth: 1,
    lineColor: '#ccd6eb',
    tickWidth: 1,
    tickColor: '#ccd6eb',
  }

  yAxis: YAxisOptions | YAxisOptions[] = {
    title: {
      text: '',
    },
    labels: {
      format: undefined,
    },
    gridLineWidth: 1,
    gridLineColor: '#e6e6e6',
  }

  credits = {
    enabled: false,
  }

  series: SeriesItem[]

  plotOptions: PlotOptions = {}

  responsive = {
    rules: [
      {
        condition: { maxWidth: 1200 },
        chartOptions: {
          xAxis: [{ tickInterval: 2 }, { tickInterval: 2 }],
        },
      },
      {
        condition: { maxWidth: 800 },
        chartOptions: {
          xAxis: [{ tickInterval: 4 }, { tickInterval: 4 }],
        },
      },
      {
        condition: { maxWidth: 500 },
        chartOptions: {
          xAxis: [{ tickInterval: 6 }, { tickInterval: 6 }],
        },
      },
    ],
  }

  sortFn = (a, b) => 0
  sortSeriesFn = (a, b) => 0

  getOption() {
    // return JSON.parse(JSON.stringify(this));
    return this
  }

  setSeries(value: SeriesItem[]) {
    this.series = value
  }
}
