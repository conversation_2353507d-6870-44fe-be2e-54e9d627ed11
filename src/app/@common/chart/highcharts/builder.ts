
import { ValueOf } from '@common/interface';
import { XAxisOptions, YAxisOptions, TooltipOptions, PlotOptions, LegendOptions, TitleOptions, YAxisTitleOptions, YAxisLabelsOptions, ChartOptions, SubtitleOptions, CreditsOptions, SeriesOptionsType, } from 'highcharts';
import { handleLegendItemClick } from '@common/function';
import { ChartType } from './typings';
import { XAxisItem } from './x_axis_item';
import { YAxisItem } from './y_axis_item';


export class HighChartBuilder {
  
  protected chart: ChartOptions = {
    backgroundColor: 'rgba(0, 0, 0, 0)',
    zooming: {
      type: 'x'
    }
  };

  protected title: TitleOptions = {
    text: '',
  };

  protected subtitle: SubtitleOptions = {
    text: '',
  }

  protected legend: LegendOptions = {};

  protected tooltip: TooltipOptions = {
    shared: true,
    useHTML: true,
    outside: true,
    hideDelay: 0,
    style: {
      zIndex: 9999, // 设置 tooltip 的 z-index
    },
  };
  
  protected colors = ['#5087EC', '#68BBC4', '#58A55C', '#F2BD42', '#EE752F', '#B0665D', '#E4C477', '#A3C2FB', '#A0D0D5', '#98B3E2', '#DE868F', '#F4CE98', '#B4C8D9', '#93D2F3', '#4095E5', '#7F83F7', '#E99D42', '#CBA43F', '#BFBF3D', '#81B337', '#347CAF', '#377F7F', '#FCCA00', '#B886F8', '#A16222'];

  protected credits: CreditsOptions = {
    enabled: false
  };

  protected plotOptions: PlotOptions = {
    series: {
      events: {
        legendItemClick: handleLegendItemClick(this) /** default undefined */
      }
    }
  };
  
  protected series: SeriesOptionsType[];
  protected xAxis: XAxisOptions[] = [new XAxisItem()];
  protected yAxis: YAxisOptions[] = [new YAxisItem()];


  constructor(type: keyof ChartType = 'line') {
    this.chart.type = type;
  }


  setTheme(value: string[]) {
    this.colors = value;
    return this;
  }


  setTitle(value: string) {
    this.title.text = value;
    return this;
  }


  setSubTitle(value: string) {
    this.subtitle.text = value;
    return this;
  }


  setChart(key: keyof ChartOptions, value: ValueOf<ChartOptions>) {
    (this.chart[key] as any) = value;
    return this;
  }


  setLegend(key: keyof LegendOptions, value: ValueOf<LegendOptions>) {
    (this.legend[key] as any) = value;
    return this;
  }


  setTooltip(key: keyof TooltipOptions, value: ValueOf<TooltipOptions>) {
    (this.tooltip[key] as any) = value;
    return this;
  }


  setSeries(value: SeriesOptionsType[]) {
    this.series = value;
    return this;
  }


  setXAxis(key: keyof XAxisOptions, value:ValueOf<XAxisOptions>, index = 0) {
    (this.xAxis.at(index)[key] as any) = value;
    return this;
  }


  addXAxis() {
    this.xAxis.push(new XAxisItem());
    return this;
  }


  setYAxis(key: keyof YAxisOptions, value:ValueOf<YAxisOptions>, index = 0) {
    (this.yAxis.at(index)[key] as any) = value;
    return this;
  }


  addYAxis() {
    this.yAxis.push(new YAxisItem());
    return this;
  }


  setPlotOptions(key: keyof PlotOptions, value: ValueOf<PlotOptions>) {
    (this.plotOptions[key] as any) = value;
    return this;
  }


  getOption() {
    const value = this;
    
    return {
      ...value,
    };
  }

}

