import { YAxisLabelsOptions, YAxisOptions, YAxisTitleOptions } from 'highcharts';

export class YAxisItem implements YAxisOptions {
  title: YAxisTitleOptions = {
    text: ''
  }

  labels: YAxisLabelsOptions = {
    format: undefined
  }

  gridLineWidth = 1;
  gridLineColor = '#e6e6e6';

  constructor(props?: Partial<YAxisOptions>) {
    if (props) {
      Object.assign(this, { ...props });
    }
  }
}
