import { animate, AnimationTriggerMetadata, state, style, transition, trigger } from '@angular/animations';
import { AnimationDuration, AnimationCurves } from './animation-consts';


const ANIMATION_TRANSITION_IN = `${AnimationDuration.BASE} ${AnimationCurves.EASE_OUT_QUINT}`;
const ANIMATION_TRANSITION_OUT = `${AnimationDuration.BASE} ${AnimationCurves.EASE_IN_QUINT}`;

export class Animations {

  static slideMotion: AnimationTriggerMetadata = trigger('slideMotion', [
    state(
      'void',
      style({
        opacity: 0,
      })
    ),
    state(
      'enter',
      style({
        opacity: 1,
      })
    ),
    transition('void => *', [animate(ANIMATION_TRANSITION_IN)]),
    transition('* => void', [animate(ANIMATION_TRANSITION_OUT)])
  ]);
  
  
  static HeightCollapseAnimation = [
    trigger('heightCollapseAnimation', [
      transition(':enter', [
        style({ height: 0 }),
          animate('300ms ease', style({ height: '*' })),
        ]),
        transition(':leave', [
          animate('300ms ease', style({ height: 0 }))
        ])
    ])
  ];
  
  static OpacityAnimation = [
    trigger('opacityAnimation', [
      state('true', style({
        opacity: 1
      })),
      state('false', style({
        opacity: 0
      })),
      transition('true <=> false', [
        animate('0.3s ease')
      ]),
    ])
  ];


  static ScaleInToOutAnimation = [
    trigger('scaleInToOutAnimation', [
      state('true', style({
        transform: 'scale(1)',
        opacity: 1,
      })),
      state('false', style({
        transform: 'scale(0.85)',
        opacity: 0,
      })),
      transition('true <=> false', [
        animate('0.3s ease')
      ]),
    ])
  ];


  static ScaleOutToInAnimation = [
    trigger('scaleOutToInAnimation', [
      state('true', style({
        transform: 'scale(1)',
        opacity: 1,
      })),
      state('false', style({
        transform: 'scale(1.15)',
        opacity: 0,
      })),
      transition('true <=> false', [
        animate('0.3s ease')
      ]),
    ])
  ];


  static ModalInToOutAnimation = [
    trigger('modalInToOutAnimation', [
      state('invisible', style({
        transform: 'scale(0.85)',
        opacity: 0,
      })),
      state('visible', style({
        transform: 'scale(1)',
        opacity: 1,
      })),
      state('leave', style({
        transform: 'scale(1.25)',
        opacity: 0,
      })),
      transition('* <=> *', [animate('0.3s ease')]),
    ])
  ];


  static ModalOutToInAnimation = [
    trigger('modalOutToInAnimation', [
      state('invisible', style({
        transform: 'scale(1.25)',
        opacity: 0,
      })),
      state('visible', style({
        transform: 'scale(1)',
        opacity: 1,
      })),
      state('leave', style({
        transform: 'scale(0.85)',
        opacity: 0,
      })),
      transition('* <=> *', [animate('0.3s ease')]),
    ])
  ];


  static OpenCloseAnimation = [
    trigger('openClose', [
      state('true', style({ height: '*' })),
      state('false', style({ height: '0px' })),
      transition('false <=> true', animate('0.3s ease')),
    ]),
  ]

}
