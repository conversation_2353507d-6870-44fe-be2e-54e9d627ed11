import { inject } from '@angular/core'
import { HttpEventType, HttpEvent } from '@angular/common/http'
import { FormArray, FormGroup, UntypedFormArray } from '@angular/forms'
import { ActivatedRoute } from '@angular/router'
import { startOfWeek, addWeeks, startOfMonth, endOfMonth, format } from 'date-fns'
import { map } from 'rxjs'

import { PHONE_PATTERN } from '@common/const'
import { DimensionValueMenuVo, FilterItemVo } from '@api/caerus/model'
import { DtType, FilterItem } from '@common/service/query-engine'

export const createPoint = (color: string | any) => {
  if (color?.pattern?.color) {
    // 如果颜色是纹理
    color = color.pattern.color
  } else if (typeof color === 'object') {
    // 如果颜色是渐变
    const colors = color.stops.map(item => item[1]).flat(1)

    color = `linear-gradient(to right, ${colors.join(',')})`
  }

  return `<em style="background: ${color};" class="w-2 h-2 rounded-full inline-block mr-1"></em>`
}

export const createValueElement = (value: number, template: string) => {
  if (isNaN(value) || isUndefinedOrNull(value) || !isEveryFinite(value)) {
    return `-`;
  }

  const symbol = value > 0 ? '+' : ''
  const color = value === 0 ? 'text-neutral-500' : value > 0 ? 'text-red-600' : 'text-green-500'
  const replaceValue = `${symbol}${Intl.NumberFormat().format(+value.toFixed(2))}`

  return `
    <span class="${color} whitespace-nowrap">
      ${template.replace('{n}', replaceValue)}
    </span>
  `
}

export function transformUrl(url: string, paramsMap?: object): string {
  let copyUrl: string = url

  if (!!paramsMap) {
    Object.keys(paramsMap).forEach((item: string) => {
      copyUrl = copyUrl.replace(`{${item}}`, paramsMap[item])
    })
  }

  return copyUrl
}

export function getProgress(event: HttpEvent<any>): number | void {
  switch (event.type) {
    case HttpEventType.DownloadProgress:
    case HttpEventType.UploadProgress:
      const num = Math.round((100 * event.loaded) / event.total)
      return isNaN(num) ? 100 : num
  }
}

export function removeParams(): any {
  return (item: string) => {
    this.params = this.params.delete(item)
    return item
  }
}

export function addParams(obj: any): (item: string) => void {
  return (item: string) => {
    this.params = this.params.set(item, obj[item])
  }
}

const getType = (value: any): string => {
  const type = Object.prototype.toString.call(value)

  return type.replace(/\[object+\s|\]$/g, '')
}

export function isString(value: any): boolean {
  return getType(value) === 'String'
}

export function isNumber(value: any): boolean {
  if (typeof value === 'string' || typeof value === 'object') {
    return false
  }

  value = parseInt(value, 10)

  if (isNaN(value)) {
    return false
  }

  return getType(value) === 'Number'
}

export function isTrue(...values: boolean[]): boolean {
  return values.every(value => {
    return value === true
  })
}

export function isFalse(...values: boolean[]): boolean {
  return values.every(value => {
    return value === false
  })
}


export function isEveryFinite(...values: number[]): boolean {
  return values.every(value => Number.isFinite(value));
}


export function isNull<T>(...values: T[]): boolean {
  return values.every(value => {
    return getType(value) === 'Null'
  })
}

export function isNotNull<T, U>(...values: T[] | U[]): boolean {
  return values.every(value => {
    return isNull(value) === false
  })
}

export function isUndefined<T>(...values: T[]): boolean {
  return values.every(value => {
    return getType(value) === 'Undefined'
  })
}

export function hasUndefined<T>(...values: T[]): boolean {
  return values.some(value => {
    return getType(value) === 'Undefined'
  })
}

export function isNotUndefined<T>(...values: T[]): boolean {
  return values.every(value => {
    return isUndefined(value) === false
  })
}

export function isNotUndefinedOrNotNull<T>(...values: T[]): boolean {
  return values.every(value => {
    return isUndefined(value) === false && isNull(value) === false
  })
}

export function isUndefinedOrNull<T>(...values: T[]): boolean {
  return values.every(value => {
    return isUndefined(value) === true || isNull(value) === true
  })
}

export function isEqual<T>(value: T): (v: T) => boolean {
  return (v: T) => {
    return v === value
  }
}

export function isNotEqual<T>(value: T): (v: T) => boolean {
  return (v: T) => {
    return v !== value
  }
}

export function isFunction(value: any): boolean {
  return getType(value) === 'Function'
}

export function isDate(value: any): boolean {
  const pattern = /\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/

  return getType(value) === 'Date' || new RegExp(pattern).test(value)
}

export function isRegExp(value: any): boolean {
  return getType(value) === 'RegExp'
}

export function isArray<T>(...values: T[]): boolean {
  return values.every(value => {
    return getType(value) === 'Array'
  })
}

export function isStringArray(value: any[]): boolean {
  if (!isArray(value)) {
    return false
  }

  return value.every(item => isString(item))
}

export function isNumberArray(value: any[]): boolean {
  if (!isArray(value)) {
    return false
  }

  return value.every(item => isNumber(item))
}

export function isBoolean(value: any): boolean {
  return getType(value) === 'Boolean'
}

export function isObject(value: any): boolean {
  return getType(value) === 'Object'
}

export function isMap(value: any): boolean {
  return getType(value) === 'Map'
}

export function isNotObject(value: any): boolean {
  return getType(value) !== 'Object'
}

export function isPromise(value: any): boolean {
  return getType(value) === 'Promise'
}

export function Some(...values): boolean {
  return values.some(isEqual(true))
}

export function Every(...values): boolean {
  return values.every(isEqual(true))
}

export function allTruthy<T extends object>(obj: T): T | undefined {
  return Object.values(obj).every(v => !!v) ? obj : undefined
}

// export function isEmptyObject<T>(...values: T[]): boolean {
//   return values.every((value: object) => {
//     return Object.keys(value).length < 1;
//   });
// }

// export function isNotEmptyObject<T>(...values: T[]): boolean {
//   return values.every((value: object) => {
//     return Object.keys(value).length > 0;
//   });
// }

/**
 * `判断空字符串`
 */
export function isEmpty(value: string): boolean
/**
 * `判断空数组`
 */
export function isEmpty(value: Array<any>): boolean
/**
 * `判断空Map`
 */
export function isEmpty(value: Map<any, any>): boolean
/**
 * `判断空对象`
 */
export function isEmpty(value: Object): boolean
export function isEmpty(value: any): boolean {
  if (isUndefinedOrNull(value)) {
    return true
  }

  if (typeof value === 'string' && value.trim() === '') {
    return true
  }

  if (Array.isArray(value) && value.length === 0) {
    return true
  }

  if (value instanceof Map && value.size === 0) {
    return true
  }

  if (isObject(value)) {
    return Object.keys(value).length < 1
  }

  return false
}

export function isNotEmpty(value: string): boolean
export function isNotEmpty(value: Array<any>): boolean
export function isNotEmpty(value: Map<any, any>): boolean
export function isNotEmpty(value: Object): boolean
export function isNotEmpty(value: any): boolean {
  return isEmpty(value) === false
}

export function isValidDate(value: string | number | Date): boolean {
  if (
    isArray(value) ||
    isObject(value) ||
    isUndefined(value) ||
    isNull(value) ||
    value === '' ||
    new RegExp(/(\d)+\.+(\d)+\.+(\d)/g).test(value as string)
  ) {
    return false
  }

  if (typeof +value === 'number' && `${value}`.length < 13) {
    return false
  }

  if (isDate(value) || isNaN(new Date(value).getTime()) === false || isNaN(new Date(+value).getTime()) === false) {
    return true
  }

  return false
}

export function isPhone(value: string): boolean {
  return new RegExp(PHONE_PATTERN).test(value)
}

export function keyValue(param: object): any {
  const res: Array<{ key: string; value: any }> = []

  if (isObject(param)) {
    Object.keys(param).forEach(key => {
      res.push({ key, value: param[key] })
    })
  }

  return res
}

/**
 * 链接字符串转换为Map
 *
 * ## Examples
 * ### Demo 1
 *
 * ```javascript
 * const url = 'http://www.baidu.com?code=L0OX9J&service=http://www.baidu.com';
 * const map = paramsToMap(url);
 *
 * map.get('code');    // output: L0OX9J
 * map.get('service'); // output: http://www.baidu.com
 *
 * ```
 * ### Demo 2
 * ```javascript
 * const url = 'code=L0OX9J&service=http://www.baidu.com';
 * const map = paramsToMap(url);
 *
 * map.get('code');    // output: L0OX9J
 * map.get('service'); // output: http://www.baidu.com
 *
 * ```
 */
export function paramsToMap(url: string): Map<string, string> {
  const map = new Map<string, string>()
  const arr = url.split('?')
  let params = url

  if (arr.length > 1) {
    params = arr[1]
  }

  params
    .split('&')
    .map(item => item.split('='))
    .map(([key, value]) => map.set(key, value))

  return map
}

export function paramsToObject(url: string): Map<string, string> {
  const obj = {} as any
  const arr = url.split('?')
  let params = url

  if (arr.length > 1) {
    params = arr[1]
  }

  params
    .split('&')
    .map(item => item.split('='))
    .map(([key, value]) => (obj[key] = value))

  if (isEmpty(obj)) {
    return void 0
  }

  return obj
}

export function EventInterceptor(ctx, event: MouseEvent | KeyboardEvent): void {
  if (event) {
    event.preventDefault()
    event.stopPropagation()
    event.stopImmediatePropagation()
  }
}

/**
 * sleep
 * @example
 * ```
 * (async () => {
 *  for(var i = 0; i < 5; i++) {
 *    await sleep(1000);
 *    // todo someting
 *  }
 * })();
 * ```
 */
export async function sleep<T = any>(time: number): Promise<T> {
  return new Promise(resolve => {
    setTimeout(resolve, time)
  })
}

export function getMapKeyByValue<T>(map, value): T | void {
  for (const [k, v] of map.entries()) {
    if (v === value) {
      return k
    }
  }
}

export function clickOutside() {
  const btn = document.createElement('button')
  btn.style.display = 'none'
  document.body.append(btn)
  dispatchClick(btn)
  btn.click()
  btn.remove()
}

function debounce(callback: () => void, ms: number) {
  let timer = null

  return function (...args: any) {
    timer && clearTimeout(timer)
    timer = setTimeout(() => callback.apply(this, args), ms)
  }
}

export const windowResize = debounce(() => {
  if (document.createEvent) {
    const eventVarite = document.createEvent('HTMLEvents')
    eventVarite.initEvent('resize', true, true)
    window.dispatchEvent(eventVarite)
  } else if (document['createEventObject']) {
    window['fireEvent']('onresize')
  }
}, 100)

export function targetScroll(target: HTMLElement) {
  if (document.createEvent) {
    const eventVarite = document.createEvent('HTMLEvents')
    eventVarite.initEvent('scroll', true, true)
    target.dispatchEvent(eventVarite)
  }
}

export function dispatchClick(target: HTMLElement) {
  if (document.createEvent) {
    const eventVarite = document.createEvent('HTMLEvents')
    eventVarite.initEvent('click', true, true)
    target.dispatchEvent(eventVarite)
  }
}

export const _pad = (value: number, n = 2) => {
  return ('0' + value).slice(n * -1)
}

export function base64toBlob(dataurl) {
  var arr = dataurl.split(','),
    mime = arr[0].match(/:(.*?);/)[1],
    bstr = atob(arr[1]),
    n = bstr.length,
    u8arr = new Uint8Array(n)
  while (n--) {
    u8arr[n] = bstr.charCodeAt(n)
  }
  return new Blob([u8arr], { type: mime })
}

export const toNumber = (item: string) => {
  if (isUndefinedOrNull(item)) {
    return null;
  }
  return parseFloat(item)
}

export const toDate = (item: string) => new Date(item.replace(/-/g, '/'))

/**
 * 将小数转换为百分比形式,并保留指定位数的小数
 * @param {number} value 要转换的数值
 * @param {number} multiple 倍数，默认为100
 * @param {number} fractionDigits 要保留的小数位数,默认为2
 * @returns {number} 转换后的百分比数值
 *
 * @example
 * toDecimals(0.1234) // 返回 12.34
 * toDecimals(0.1234, 1) // 返回 0.12
 */
export const toDecimals = (value: number, multiple = 100, fractionDigits = 2) => {
  if (value === null || value === undefined) { return value };
  const reuslt = parseFloat((value * multiple).toFixed(fractionDigits))
  return Number.isFinite(reuslt) ? reuslt : null
}

/**
 * 用于将`dimensionConfig`项直接转换成查询服务可用的筛选条件，
 * `value`用于`app-radio`或`app-dimension-filter`组件的value。
 */
export function toFilterItem(data: DimensionValueMenuVo) {
  if (data) {
    const { key, value, extendName, showValue } = data

    return {
      key: showValue,
      value: new FormGroup(
        new FilterItem({
          extendName,
          value: [{ key, value }],
        })
      ).value as FilterItemVo,
    }
  }

  return null
}

/** 根据周数获取日期 */
export function getFirstDayOfISOWeek(year: number, week: number) {
  // 创建年份第一天
  const simple = new Date(year, 0, 1 + (week - 1) * 7)
  // 找到当周的周四
  const dayOfWeek = simple.getDay()
  const ISOweekStart = simple

  if (dayOfWeek <= 4) {
    ISOweekStart.setDate(simple.getDate() - simple.getDay() + 1)
  } else {
    ISOweekStart.setDate(simple.getDate() + 8 - simple.getDay())
  }

  return ISOweekStart
}

/** 根据ISO周计算该周的第一天和最后一天 */
export function getDateRangeOfISOWeek(year: number, week: number) {
  const simple = new Date(year, 0, 1 + (week - 1) * 7);
  const dow = simple.getDay();
  const ISOweekStart = new Date(simple);
  
  // ISO周的第一天是周一（getDay() 0 = 周日，1 = 周一，...）
  const dayDiff = dow <= 4 ? dow - 1 : dow - 8;
  ISOweekStart.setDate(simple.getDate() - dayDiff);
  
  const ISOweekEnd = new Date(ISOweekStart);
  ISOweekEnd.setDate(ISOweekStart.getDate() + 6);
  
  return {
    start: ISOweekStart,
    end: ISOweekEnd,
  };
}

/**
 * 用于格式化日期
 * - 日：input: `2025-06-08` output: `周日`
 * - 周：input: `2025-22` output: `05-26~06-01`
 * @param {string} input 日期字符串
 * @param {DtType} dtType 日期类型
 * @returns {string}
 */
export const getWeekByDate = (input: string, dtType: DtType) => {
  if (
    !input ||
    isNumber(input) || 
    (isString(input) && /\d{4}-\d{2}(?:-\d{2})?/.test(input) === false)
  ) {
    return input;
  }
  const isDateStr = /\d{4}-\d{2}-\d{2}/.test(input);
  const weeks = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
  const day = new Date(input.replace(/-/g, '/')).getDay();

  if (dtType === 'dt') {
    return isDateStr && weeks[day] || '';
  }

  if (dtType === 'yw') {
    const [year, week] = input.split('-').map(toNumber);
    const { start, end } = getDateRangeOfISOWeek(year, week);

    return ` ${format(start, 'MM-dd')}~${format(end, 'MM-dd')}`;
  }
  
  return '';
}

/**
 * 用于格式化日期，目前仅支持周
 * @param input 日期字符串
 * @param dtType 日期类型
 * @returns {string}
 */
export const getDateStr = (input: string, dtType: DtType) => {
  if (dtType === 'yw' && /\d{4}-\d{2}/.test(input)) {
    const [year, week] = input.split('-');
    return `${year}第${week}周`;
  }

  return input;
}


export const isWebp = () => {
  try {
    return document.createElement('canvas').toDataURL('image/webp').indexOf('data:image/webp') === 0
  } catch (err) {
    return false
  }
}

export const Assets_Image_Format = isWebp() ? 'webp' : 'jpg'

export function getImageStyle(width: number, height: number, multiple: number): string {
  const widthVal = width * multiple
  const heightVal = height * multiple

  // interlace,1/
  return `?x-oss-process=image/resize,m_fill,w_${widthVal},h_${heightVal}/quality,q_100/format,${Assets_Image_Format}`
}

const _utf8_encode = (input: string) => {
  input = input.replace(/\r\n/g, '\n')
  var utftext = ''
  for (var n = 0; n < input.length; n++) {
    var c = input.charCodeAt(n)
    if (c < 128) {
      utftext += String.fromCharCode(c)
    } else if (c > 127 && c < 2048) {
      utftext += String.fromCharCode((c >> 6) | 192)
      utftext += String.fromCharCode((c & 63) | 128)
    } else {
      utftext += String.fromCharCode((c >> 12) | 224)
      utftext += String.fromCharCode(((c >> 6) & 63) | 128)
      utftext += String.fromCharCode((c & 63) | 128)
    }
  }
  return utftext
}

export function toBase64(value: string | Object) {
  const _keyStr = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/='

  if (isObject(value)) {
    value = JSON.stringify(value)
  }

  let input = value as string
  let output = ''
  let chr1, chr2, chr3, enc1, enc2, enc3, enc4
  let i = 0

  input = _utf8_encode(input)

  while (i < input.length) {
    chr1 = input.charCodeAt(i++)
    chr2 = input.charCodeAt(i++)
    chr3 = input.charCodeAt(i++)
    enc1 = chr1 >> 2
    enc2 = ((chr1 & 3) << 4) | (chr2 >> 4)
    enc3 = ((chr2 & 15) << 2) | (chr3 >> 6)
    enc4 = chr3 & 63
    if (isNaN(chr2)) {
      enc3 = enc4 = 64
    } else if (isNaN(chr3)) {
      enc4 = 64
    }
    output = output + _keyStr.charAt(enc1) + _keyStr.charAt(enc2) + _keyStr.charAt(enc3) + _keyStr.charAt(enc4)
  }
  return output
}

export function handleLegendItemClick(value) {
  return function (event) {
    const { shiftKey } = event?.browserEvent || {}
    const { name } = event.target

    let originLegendData = []

    if (shiftKey) {
      const copy = this.chart.series.map((val: { name: string; visible: boolean }) => {
        if (val.name === name || val.name === `${name}-占比`) {
          val.visible = !val.visible
        }

        return {
          name: val.name,
          visible: val.visible,
        }
      })

      originLegendData = value.series.map(item => {
        const { visible } = copy.find(v => v.name === item.name)

        return {
          ...item,
          visible,
        }
      })
    } else {
      originLegendData = value.series as any[]
      event.preventDefault()

      if (this.chart.recordBeforeEle === name) {
        originLegendData = originLegendData.map(item => {
          return { ...item, visible: true }
        })
      } else {
        originLegendData = originLegendData.map(item => {
          return { ...item, visible: item.name === name || item.name === `${name}-占比` }
        })
      }

      this.chart.recordBeforeEle = name

      if (originLegendData.every(item => item.visible) && isNotUndefinedOrNotNull(this.chart.recordBeforeEle)) {
        this.chart.recordBeforeEle = null
      }
    }

    // 更改页面展示
    setTimeout(() => {
      console.log(originLegendData)

      this.chart.update({ series: originLegendData })
    })
  }
}

export function makeCenterText() {
  return function () {
    let text = ''

    if (this.series[0]) {
      const { name, total } = this.series[0]

      text = `${name}<br />${Intl.NumberFormat().format(total)}`
    }

    if (this.centerText) {
      this.centerText.destroy()
    }

    this.centerText = this.renderer
      .text(text, this.plotWidth / 2 + this.plotLeft, this.plotHeight / 2 + this.plotTop)
      .attr({
        align: 'center',
        zIndex: 5,
      })
      .css({
        fontSize: '14px',
        textAlign: 'center',
      })
      .add()
  }
}

export function handleSeriesClick(legends) {
  return function () {
    const { name } = this
    const hasVisible = legends.some(legend => legend?.visible === true)
    const allVisible = legends.every(legend => legend?.visible === true)

    if (hasVisible && !allVisible) {
      // 显示全部
      legends = legends.map(({ ...legend }) => ({ ...legend, visible: true }))
    } else {
      // 显示指定
      legends = legends.map(legend => ({ ...legend, visible: legend.name === name }))
    }

    this.chart.update({ series: legends })
  }
}

export function tooltipFormatter(params: any) {
  if (params.split) {
    return ['<b>' + this.x + '</b>'].concat(
      this.points
        ? this.points.map(function (point) {
            return `${createPoint(point.color)}${point.series.name}: ${point.y}`
          })
        : []
    )
  }

  if (params?.shared) {
    const days = '日一二三四五六'
    const isDate = /\d{4}-\d{1,2}-\d{1,2}/.test(this.x)
    const day = new Date(this.x).getDay()

    let dayStr = isDate ? `星期${days.charAt(day)}` : ''
    let result = `<span class="block text-sm font-bold">${this.x} ${dayStr}</span>`

    if (this.points) {
      this.points.forEach(item => {
        result += `
          <span class="block">
            ${createPoint(item.color)}
            <span>${item.series.name}：</span>
            <b>${item.point.n} </b>
          </span>
        `
      })
    }

    return `<div class="bg-white p-[7px] -m-[7px]">${result.replace(/\n/g, '').replace(/(>\s+<)/g, '><')}</div>`
  }

  return `<span>${createPoint(this.color)}${this.x}: ${this.y}</span>`
}

export function moveItemInFormArray(formArray: UntypedFormArray, fromIndex: number, toIndex: number): void {
  const dir = toIndex > fromIndex ? 1 : -1

  const item = formArray.at(fromIndex)
  for (let i = fromIndex; i * dir < toIndex * dir; i = i + dir) {
    const current = formArray.at(i + dir)
    formArray.setControl(i, current)
  }
  formArray.setControl(toIndex, item)
}

export function rgba2hex(rgbai) {
  var a,
    rgb = rgbai.replace(/\s/g, '').match(/^rgba?\((\d+),(\d+),(\d+),?([^,\s)]+)?/i),
    alpha = ((rgb && rgb[4]) || '').trim(),
    hex = rgb
      ? (rgb[1] | (1 << 8)).toString(16).slice(1) +
        (rgb[2] | (1 << 8)).toString(16).slice(1) +
        (rgb[3] | (1 << 8)).toString(16).slice(1)
      : rgbai

  if (alpha !== '') {
    a = alpha
  } else {
    a = 0o1
  }

  a = ((a * 255) | (1 << 8)).toString(16).slice(1)

  return `#${hex}${a === 'ff' ? '' : a}`
}

export function hexToRgba(hex, alpha: string | number) {
  // 移除可能存在的 "#" 符号
  hex = hex.replace(/^#/, '');

  // 如果是 3 位格式的 hex 颜色（如 #FFF），则扩展为 6 位格式（#FFFFFF）
  if (hex.length === 3) {
      hex = hex.split('').map(function (char) {
          return char + char;
      }).join('');
  }

  // 将十六进制颜色值转为 RGB
  const r = parseInt(hex.slice(0, 2), 16);
  const g = parseInt(hex.slice(2, 4), 16);
  const b = parseInt(hex.slice(4, 6), 16);

  // 返回带透明度的 rgba 颜色值
  return `rgba(${r}, ${g}, ${b}, ${alpha})`;
}

export function getFirstDayForWeek(d) {
  d = new Date(d)
  var day = d.getDay(),
    diff = d.getDate() - day + (day == 0 ? -6 : 1) // adjust when day is sunday
  return new Date(d.setDate(diff))
}

export function getWeeksCount(value: number | string) {
  // 获取当前日期
  const currentDate = new Date(value)

  // 获取当前年份
  const currentYear = currentDate.getFullYear()

  // 创建一个新的Date对象，设置为当年的第一天
  const firstDayOfYear = new Date(currentYear, 0, 1)

  // 计算当前日期是当年的第几天
  const millisecondsPerDay = 24 * 60 * 60 * 1000 // 一天的毫秒数
  const dayOfYear = Math.floor((currentDate.getTime() - firstDayOfYear.getTime()) / millisecondsPerDay) + 1

  // 计算当前日期是第几周
  const weekNumber = Math.ceil(dayOfYear / 7)

  return weekNumber
}

export function getDateFromYearWeek(yearWeek: string) {
  const [year, week] = yearWeek.split('-').map(Number)

  // 创建指定年份的第一个周
  const startOfYear = new Date(year, 0, 1)
  const startOfWeekDate = startOfWeek(startOfYear, { weekStartsOn: 1 }) // 1 表示周一

  // 计算指定周的日期
  const date = addWeeks(startOfWeekDate, week - 1)
  return date
}

/** 将“年-月”转换为日期的函数 */
export function getDatesFromYearMonth(yearMonth: string) {
  const [year, month] = yearMonth.split('-').map(Number)

  // 创建该年月的第一天
  const startDate = startOfMonth(new Date(year, month - 1))

  // 创建该年月的最后一天
  const endDate = endOfMonth(startDate)

  return endDate
}

export function checkForm(controls: any): void {
  for (const i in controls) {
    if (controls[i] instanceof FormArray) {
      ;(<FormArray>controls[i]).controls.forEach(item => {
        checkForm((<any>item).controls)
      })
    } else if (controls[i] instanceof FormGroup) {
      checkForm((<any>controls[i]).controls)
    } else {
      controls[i].markAsDirty()
      controls[i].updateValueAndValidity()
    }
  }
}

export function getColumnWidth(item: string): number {
  if (!item) {
    return 0
  }

  const span = document.createElement('span')
  let width = 0
  span.innerText = item
  span.style.fontSize = '14px'
  span.style.fontWeight = '500'
  span.style.display = 'inline-block'
  span.style.padding = '0 8px'
  span.style.whiteSpace = 'nowrap'

  document.body.appendChild(span)
  width = span.offsetWidth + 20
  span.remove()
  return width
}

export function groupBy<T = any>(data: any[], property: string): { [key: string]: T[] } {
  if (!data || !data.length || !property) {
    return {}
  }

  return data.reduce((result, item) => {
    // 使用dashboardName属性作为结果对象的键
    let key = item[property]

    // 如果该键值不存在于结果对象中，初始化一个空数组
    if (!result[key]) {
      result[key] = []
    }

    // 将当前项push到与其dashboardName相关联的数组中
    result[key].push(item)

    // 返回已更新的结果对象
    return result
  }, {})
}

export const transformValueSuffix = (value: number, symbol = '%') => {
  return (value === null ? '-' : `${value + (value.toString().endsWith(symbol) ? '' : symbol)}`) as any
}

export const formatValue = (value: number | string, template: string) => {
  const replaceValue = `${value ?? '-'}`

  return template.replace('{n}', replaceValue)
}

export const formatMileageLabel = (input: string) => {
  const isMore = new RegExp(', +').test(input)
  const value = input.replace(/(\[|\))/g, '')

  if (isMore) {
    return value.replace(', +', 'km以上')
  }
  return `${value.replace(',', '~')}km`
}

export const getRouteParam = (key: string) => {
  return inject(ActivatedRoute).paramMap.pipe(map(paramMap => paramMap.get(key)))
}

export const getRoutePath = () => {
  return inject(ActivatedRoute).url
}

export const getMaxLength = (a = [], b = []) => {
  return Math.max(a.length, b.length)
}

export const fillEmpty = (length: number, value = '') => {
  const emptyArr = Array.from({ length }).fill(value)

  return (arr: any[]) => {
    arr.forEach((item, i) => {
      emptyArr[i] = item
    })

    return emptyArr as string[]
  }
}

export class DiffTime {
  constructor(
    public startTime: Date,
    public endTime: Date
  ) {}

  count(unit: 'days') {
    const day = 1000 * 60 * 60 * 24

    switch (unit) {
      case 'days':
        return Math.floor(Math.abs((this.startTime.getTime() - this.endTime.getTime()) / day))
    }
  }
}

export function getValueByPath(obj, path) {
  const keys = path.split('.')
  let currentObject = obj

  for (let key of keys) {
    if (currentObject && currentObject[key] !== undefined) {
      currentObject = currentObject[key]
    } else {
      return path
    }
  }
  return currentObject
}

export const sortCategoriesFn = (a, b) => {
  function parseDistance(str) {
    // 匹配数字部分，例如 '0-10km' 取 '0'
    const match = str.match(/\d+/)
    return match ? parseInt(match[0]) : Infinity // 解析数字，如果匹配不到返回无穷大
  }

  if (isNotNull(a, b)) {
    if (typeof a === 'string' && typeof b === 'string') {
      // 将字符串转换为可以比较的数值
      const aDistance = parseDistance(a)
      const bDistance = parseDistance(b)

      // 比较两个数值
      return aDistance - bDistance
    } else {
      // 将字符串转换为可以比较的数值
      const aDistance = parseDistance(a?.seriesName)
      const bDistance = parseDistance(b?.seriesName)

      // 比较两个数值
      return aDistance - bDistance
    }
  }

  return 0
}

export const sortCategoriesFn3 = (key: string) => {
  return (a, b) => {
    function parseDistance(str) {
      // 匹配数字部分，例如 '0-10km' 取 '0'
      const match = str.match(/\d+/)
      return match ? parseInt(match[0]) : Infinity // 解析数字，如果匹配不到返回无穷大
    }

    const getNameValue = item => getValueByPath(item, key)

    const aName = getNameValue(a)
    const bName = getNameValue(b)

    // 分解字符串
    const [prefixA, typeA, extraA] = aName.split('-')
    const [prefixB, typeB, extraB] = bName.split('-')

    // 确定顺序的主要依据：前缀和类型
    if (prefixA !== prefixB) {
      return prefixA.localeCompare(prefixB)
    }

    if (typeA !== typeB) {
      if (/\[+(.*)+\)$/.test(typeA) && /\[+(.*)+\)$/.test(typeB)) {
        // 将字符串转换为可以比较的数值
        const aDistance = parseDistance(typeA)
        const bDistance = parseDistance(typeB)

        // 比较两个数值
        return aDistance - bDistance
      }

      return typeA.localeCompare(typeB)
    }

    // 处理是否包含 '-占比'
    const hasRatioA = extraA === '占比' || (extraA && extraA.startsWith('占比'))
    const hasRatioB = extraB === '占比' || (extraB && extraB.startsWith('占比'))

    return hasRatioA && !hasRatioB ? 1 : hasRatioB && !hasRatioA ? -1 : 0
  }
}

export const sortBy = (order: string[], key = 'name') => {
  return function <T extends { name: string } | any>(a: T, b: T) {
    if (!key) {
      const indexA = order.indexOf(a as any)
      const indexB = order.indexOf(b as any)

      return indexA - indexB
    } else {
      const indexA = order.indexOf(a[key])
      const indexB = order.indexOf(b[key])

      return indexA - indexB
    }
  }
}
