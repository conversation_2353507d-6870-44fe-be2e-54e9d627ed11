import { Component } from '@angular/core';
import { RouterLink } from '@angular/router';
import { NzButtonModule } from 'ng-zorro-antd/button';

@Component({
  selector: 'app-not-found',
  template: `
    <div class="flex flex-col items-center justify-center w-full h-full overflow-hidden">
      <img class="w-[252px] pointer-events-none select-none mb-6" src="assets/svg/404.svg">
      <div class="text-2xl text-black/80">404</div>
      <div class="text-sm text-black/40 ">对不起，您访问的页面不存在。</div>
      <button class="mt-6!" nz-button nzType="primary" routerLink="/">返回首页</button>
    </div>
  `,
  imports: [
    NzButtonModule,
    RouterLink,
  ]
})
export class NotFoundComponent {

}
