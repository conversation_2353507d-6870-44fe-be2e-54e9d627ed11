import { Component } from '@angular/core';
import { RouterLink } from '@angular/router';
import { NzButtonModule } from 'ng-zorro-antd/button';

@Component({
  selector: 'app-no-permission',
  template: `
    <div class="flex flex-col items-center justify-center w-full h-full overflow-hidden">
      <img class="w-[251px] pointer-events-none select-none mb-6" src="assets/svg/403.svg">
      <div class="text-2xl text-black/80 mb-2">403</div>
      <div class="text-sm text-black/40 text-center">对不起，您没有权限访问此页面</div>
      <button class="mt-6!" nz-button nzType="primary" routerLink="/">返回首页</button>
    </div>
  `,
  imports: [
    NzButtonModule,
    RouterLink,
  ]
})
export class NoPermissionComponent {

}
