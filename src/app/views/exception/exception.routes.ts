import { Routes } from '@angular/router';

export const ROUTES: Routes = [
  {
    path: '403',
    loadComponent: () => import('./no-permission.component').then(mod => mod.NoPermissionComponent),
    title: '403 Forbidden'
  },
  {
    path: '404',
    loadComponent: () => import('./not-found.component').then(mod => mod.NotFoundComponent),
    title: '404 Not Found'
  },
  {
    path: '',
    redirectTo: '404',
    pathMatch: 'full'
  },
];
