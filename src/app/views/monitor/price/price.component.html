<app-navigation />

<div class="sticky top-0 z-40 py-2 mb-1 bg-white shadow-lg">
  <div class="flex justify-center">
    <div class="max-w-(--breakpoint-2xl) flex-1 min-w-0 flex items-center gap-x-2 text-xs px-5">
      <app-date-compare
        class="-ml-10"
        [class.pointer-events-none]="querying()"
        showDtType="false"
        compareBtn
        deleteBtn
        gap-x="2"
      />

      <div class="flex items-center">
        <label class="inline-flex items-center font-bold leading-0 whitespace-nowrap">对标选择：</label>
        <app-radio-group
          [(ngModel)]="competition"
          class="relative flex gap-1"
          (ngModelChange)="handleCompetitionChange($event)"
        >
          @for (item of competitionOptions(); track $index) {
            <app-radio class="tag-radio !text-[16px]" activeClass="active" [value]="item">
              {{ item.showValue }}
            </app-radio>
          }
        </app-radio-group>
      </div>
    </div>
    <div class="w-17.5"></div>
  </div>
</div>

<div class="flex justify-center bg-white">
  <div class="max-w-(--breakpoint-2xl) flex-1 min-w-0">
    <app-price-monitor id="price-monitor" />

    @defer (on viewport) {
      <app-price-difference id="price-difference" />
    } @placeholder {
      <div id="price-difference" class="relative w-full h-194">
        <app-progress-loader />
      </div>
    }
  </div>

  <div class="tools-bar pointer-events-none">
    <div class="btn pointer-events-auto cursor-pointer hover:bg-black/5 rounded-sm" (click)="openPriceConfigModal()">
      <nz-icon nzType="alert" nzTheme="fill" class="text-2xl text-red-500! mb-1" />
      <span class="label">监控设置</span>
    </div>

    <div class="my-auto flex flex-col justify-center pointer-events-auto">
      <nz-anchor hidden [nzBounds]="200" [nzTargetOffset]="0">
        <nz-link nzTitle="总览" nzHref="#price-monitor"></nz-link>
        <nz-link nzTitle="区域" nzHref="#price-difference"></nz-link>
      </nz-anchor>
    </div>

    <app-back-top />
  </div>
</div>
