import { XAxisOptions, YAxisOptions } from 'highcharts'
import { BaseHighCharts, SeriesItem } from '@common/chart/highcharts'
import { createPoint, createValueElement, isNotUndefined, toDecimals } from '@common/function'
import { QueryOutputHeaderVo, QueryOutputVo } from '@api/query-engine/model'
import { getCategories, getNumberFields, getPercentFields, isEveryElementPercent } from '@common/chart'
import { use } from 'echarts'
import { sortBy } from 'lodash'

function removeUppercaseLetters(arr: any[]): any[] {
  return arr.map(item => {
    if (item.series && item.series.name) {
      item.series.name = item.series.name.replace(/[A-Z]/g, '')
    }
    return item
  })
}

function removeUppercaseLettersSeries(arr: any[]): any[] {
  return arr.map(item => {
    if (item.name) {
      item.name = item.name.replace(/[A-Z]/g, '')
    }
    return item
  })
}

function tooltipFormatter(that: BasicLine) {
  return function (param: any) {
    const result = []
    const map = new Map()
    const params = this.points.sort(that.sortFn)

    params.forEach(item => {
      if (map.has(item.series.name)) {
        const arr = map.get(item.series.name) as any[]
        arr.push(item)
        arr.reverse()
      } else {
        map.set(item.series.name, [item])
      }
    })

    const merged = [...map.values()].flat(1)
    const arr = sortBy(merged, [
      function (m) {
        return m.series.name
      },
    ])
    result.push('<table class="text-sm">')
    arr.forEach((params, index) => {
      const {
        series: {
          name: seriesName,
          yAxis: { index: yAxisIndex },
        },
        y: value,
        x: categorie,
        color,
        point: { yw, dt, ym },
      } = params
      const { isPercent } = that.series.find(item => item.name === seriesName) ?? { isPercent: false }
      const isDateStr = /\d{4}-\d{2}-\d{2}/.test(categorie)
      const day = new Date(categorie.replace(/-/g, '/')).getDay()
      const weeks = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
      const week = isDateStr && weeks[day]

      if (index === 0) {
        result.push(`
          <tr>
            <span class="text-[14px]">
              ${categorie}${week ? '<span class="flex-1 min-w-0 w-1"></span>' : ''}${week || ''}
            </span>
          </tr>
          <thead>
             <tr class="border-b border-gray-200 py-2">
              <td>指标</td>
              <td class="px-5">当前期</td>
              <td class="text-right">日环比</td>
              <td class="px-5 text-right">周同比</td>

            </tr>
          </thead>
        `)
      }
      result.push(`
        <tr>
          <td class="pr-2" style="color:${color}">
            ${seriesName
              .split('_userDefExtendName')
              [seriesName.split('_userDefExtendName').length - 1].replace('<', '&lt;')}:
          </td>

          <td class="text-right px-5">
            ${
              yAxisIndex === 1 || isPercent
                ? Number.isFinite(value)
                  ? value + '%'
                  : '-'
                : Number.isFinite(value)
                  ? Intl.NumberFormat().format(value)
                  : '-'
            }
          </td>
          <td class="text-right">
            ${
              dt
                ? isPercent
                  ? Number.isFinite(dt.diff)
                    ? createValueElement(dt.diff * 100, '{n}pp')
                    : '--'
                  : Number.isFinite(dt.diff)
                    ? `${createValueElement(dt.diff, '{n}')}(${createValueElement(dt.ratio, '{n}%')})`
                    : '--'
                : ''
            }
           </td>
           <td class="text-right px-5">
            ${
              yw
                ? isPercent
                  ? Number.isFinite(yw.diff)
                    ? createValueElement(yw.diff * 100, '{n}pp')
                    : '--'
                  : Number.isFinite(yw.diff)
                    ? `${createValueElement(yw.diff, '{n}')}(${createValueElement(yw.ratio, '{n}%')})`
                    : '--'
                : ''
            }
           </td>

        </tr>
      `)
    })
    result.push('</table>')

    return result.join('')
  }
}

class xAxisItem {
  categories: string[]
  opposite: boolean
  tickInterval = 1
  tickWidth = 1
  tickColor = '#ccd6eb'
  lineColor = '#ccd6eb'
  gridLineColor = '#e6e6e6'
  crosshair = true
  labels = {
    useHTML: true,
    formatter: function () {
      if (/\d{4}-\d{2}-\d{2}/.test(this.value)) {
        const day = new Date(this.value.replace(/-/g, '/')).getDay()
        if (day === 0 || day === 6) {
          return `<span class="text-primary font-semibold">${this.value}</span>`
        }
      }

      return this.value
    },
  }

  constructor({ categories, opposite }: Partial<xAxisItem>) {
    categories && (this.categories = categories)
    opposite && (this.opposite = opposite)
  }
}

export class BasicLine extends BaseHighCharts {
  responsive = {
    rules: [
      {
        condition: { maxWidth: 1200 },
        chartOptions: {
          xAxis: [{ tickInterval: 2 }],
        },
      },
      {
        condition: { maxWidth: 800 },
        chartOptions: {
          xAxis: [{ tickInterval: 4 }],
        },
      },
      {
        condition: { maxWidth: 500 },
        chartOptions: {
          xAxis: [{ tickInterval: 6 }],
        },
      },
    ],
  }

  xAxis: XAxisOptions[] = []
  yAxis: YAxisOptions[] = [
    {
      title: { text: '' },
      labels: { format: undefined },
      gridLineWidth: 1,
      gridLineColor: '#e6e6e6',
    },
  ]

  plotOptions: any = {
    series: {
      turboThreshold: 999999999,
      marker: {
        radius: 3,
        symbol: 'circle',
      },
    },
  }

  constructor(properties: QueryOutputVo, type: string) {
    super()

    const { headers, data } = properties
    const categories = getCategories(headers, data)
    const series = this.getSeries(headers, data, type)
    const [percentFields] = getPercentFields(headers)
    const isEveryFieldPercent = isEveryElementPercent(headers)
    const _series = sortBy(series, [
      function (m) {
        return m.name
      },
    ])
    if (isEveryFieldPercent) {
      this.yAxis = [
        {
          title: { text: '' },
          labels: { format: '{text}%' },
          gridLineWidth: 1,
          gridLineColor: '#e6e6e6',
        },
      ]
    } else if (percentFields) {
      this.yAxis.push({
        title: { text: '' },
        labels: { format: '{text}%' },
        gridLineWidth: 1,
        gridLineColor: '#e6e6e6',
        opposite: true,
      })
    }

    this.setCategories([categories])
    this.setSeries(_series)
    this.legend.verticalAlign = 'top'
    this.chart.type = 'spline'
  }

  getSeries(headers: { [key: string]: QueryOutputHeaderVo }, data: { [key: string]: string }[], type) {
    const numberFields = getNumberFields(headers)
    const isEveryFieldPercent = isEveryElementPercent(headers)

    return numberFields.map(key => {
      const series = new SeriesItem()
      const { aliasName, dataUnit, extendName } = headers[key]
      if (dataUnit === '%') {
        series.isPercent = true
      }

      if (dataUnit === '%' && !isEveryFieldPercent) {
        series.yAxis = 1
      }

      const _name = aliasName.split('_userDefExtendName')[aliasName.split('_userDefExtendName').length - 1].split(' ')[
        aliasName.split(' ').length - 1
      ]

      series.name = _name.replace('<', '&lt;').replace('DD', type)

      series.dataUnit = dataUnit
      series.data = data.map(item => {
        const numeric = Number(item[key])
        let value: number = null

        if (item[key] === null) {
          value = null
        } else if (dataUnit === '%') {
          value = toDecimals(numeric)
        } else {
          value = numeric
        }

        if (isNotUndefined(item[`${key}:yw_DIFF`], item[`${key}:yw_DIFF_RATIO`])) {
          return {
            y: value,
            yw: {
              diff: +item[`${key}:yw_DIFF`],
              ratio: +item[`${key}:yw_DIFF_RATIO`],
            },
            dt: {
              diff: +item[`${key}:dt_DIFF`],
              ratio: +item[`${key}:dt_DIFF_RATIO`],
            },
            ym: {
              diff: +item[`${key}:ym_DIFF`],
              ratio: +item[`${key}:ym_DIFF_RATIO`],
            },
          }
        }

        return {
          y: value,
        }
      })

      return series
    })
  }

  setCategories(values: string[][]): void {
    values.forEach((categories, index) => {
      this.xAxis.push(new xAxisItem({ categories, opposite: index > 0 }))
    })
  }

  override getOption() {
    const value = this

    return {
      ...value,
      tooltip: {
        ...value.tooltip,
        formatter: tooltipFormatter(this),
      },
      plotOptions: {
        ...value.plotOptions,
        series: {
          ...value.plotOptions.series,
          // events: {
          //   legendItemClick: handleLegendItemClick(this),
          // }
        },
      },
    }
  }
}
