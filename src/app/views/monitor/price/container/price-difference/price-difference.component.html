<div class="px-5 mt-5">
  <app-heading [title]="'价格差异归因'" [helpTemplate]="helpTemplate" />
  <ng-template #helpTemplate>
    说明：
    <br />
    {{ '价格差异归因主要是针对【嘀嗒与DD线上价格差异率】指标做进一步拆解，' | replace: priceService.competitionName() }}
    {{ '明确价格差异由哪些因素造成的，以及这些因素各自的影响程度。' | replace: priceService.competitionName() }}
  </ng-template>
  <div class="flex items-center flex-wrap gap-x-10 gap-y-2 py-4">
    <div class="flex items-center">
      <label [class]="labelClass">订单类型：</label>
      <app-radio-group class="relative flex gap-1" [(ngModel)]="c_ord_type" (ngModelChange)="changeOrdType()">
        <app-radio class="tag-radio" activeClass="active" value="0">市内</app-radio>
        <app-radio class="tag-radio" activeClass="active" value="1">城际</app-radio>
      </app-radio-group>
    </div>
    <div class="flex items-center">
      <label [class]="labelClass">起点城市：</label>
      <app-city-picker placeholder="全部" [(ngModel)]="city" useReferencePrice (ngModelChange)="getChartData()" />
    </div>
    <div class="flex items-center">
      <label [class]="labelClass">价格类型选择：</label>
      <app-radio-group class="relative flex gap-1" [(ngModel)]="price_type" (ngModelChange)="getChartData()">
        @for (item of price_type_list(); track item) {
          <app-radio class="tag-radio" activeClass="active" [value]="item">{{ item.showValue }}</app-radio>
        }
      </app-radio-group>
    </div>
    <div class="flex items-center">
      <label [class]="labelClass">里程：</label>
      <nz-select
        class="w-50"
        [nzPlaceHolder]="'请选择'"
        [(ngModel)]="singleIntal"
        (ngModelChange)="changeSingleIntal()"
      >
        @for (item of intal_list(); track item) {
          <nz-option [nzLabel]="item.showValue" [nzValue]="item"></nz-option>
        }
      </nz-select>
    </div>
  </div>
  <div>
    <div class="grid grid-cols-12 gap-x-5 mb-5">
      <div class="col-span-4">
        <app-graph-column />
      </div>
      <div class="col-span-8">
        <div class="flex flex-col h-96 shadow-md rounded-sm border border-neutral-100">
          <div class="flex flex-col gap-1 px-4 pt-4">
            <div class="text-xs text-neutral-500 pb-1">
              说明：为方便看数， 当用户选择时间是单天时，趋势这里会以用户选择的日期作为结束日期，往前展示30天的数据。
            </div>
            <div class="flex gap-1">
              <label class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap">
                指标选择：
              </label>
              <app-radio-group class="relative flex gap-1" [(ngModel)]="metics" (ngModelChange)="getChartData()">
                @for (l of metics_list(); track l) {
                  <ng-template #metricsContentTemplate>
                    <div
                      class="leading-normal"
                      [innerHTML]="l?.bizExpression | replace: priceService.competitionName()"
                    ></div>
                  </ng-template>
                  <app-radio
                    nz-popover
                    [nzPopoverMouseEnterDelay]="0.5"
                    [nzPopoverTitle]="l.aliasName | replace: priceService.competitionName()"
                    [nzPopoverContent]="metricsContentTemplate"
                    class="tag-radio"
                    activeClass="active"
                    [value]="l"
                  >
                    {{ l.aliasName | replace: priceService.competitionName() }}
                  </app-radio>
                }
              </app-radio-group>
              <!-- <nz-checkbox-group [(ngModel)]="metics" (ngModelChange)="getChartData()">
                @for (l of metics_list(); track l) {
                  <ng-template #metricsContentTemplate>
                    <div
                      class="leading-normal"
                      [innerHTML]="l?.bizExpression | replace: priceService.competitionName()"
                    ></div>
                  </ng-template>
                  <label
                    nz-popover
                    [nzPopoverMouseEnterDelay]="0.5"
                    [nzPopoverTitle]="l.aliasName | replace: priceService.competitionName()"
                    [nzPopoverContent]="metricsContentTemplate"
                    nz-checkbox
                    class="text-xs! ml-0!"
                    [nzValue]="l"
                  >
                    {{ l.aliasName | replace: priceService.competitionName() }}
                  </label>
                }
              </nz-checkbox-group> -->
            </div>
            <!-- <div class="flex gap-2">
              <label class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap">
                里程选择：
              </label>
              <nz-checkbox-group [(ngModel)]="intal" (ngModelChange)="getChartData()">
                @for (l of intal_list(); track l) {
                  <label nz-checkbox class="text-xs! ml-0!" [nzValue]="l">
                    {{ l.showValue }}
                  </label>
                }
              </nz-checkbox-group>
            </div> -->
            <!-- <div class="text-xs text-neutral-400 px-4">说明：点击图例默认单选，长按shift键点击可多选。</div> -->
          </div>
          <div class="flex-1 min-h-0 relative flex items-center justify-center text-xs">
            @if (loading()) {
              <app-line-spin />
            } @else {
              @if (lineOption()) {
                <div class="absolute inset-2">
                  <app-graph [options]="lineOption()" [multiple]="1" />
                </div>
              } @else if (lineErrorMessage()) {
                <span>{{ lineErrorMessage() }}</span>
              } @else {
                <span></span>
              }
            }
          </div>
        </div>
      </div>
    </div>
  </div>
  <div class="my-5 border rounded-lg py-4 px-10">
    <h2 class="text-lg font-bold mb-2 text-center">嘀嗒价格差异归因口径说明</h2>
    <p class="text-xs text-gray-500 mb-4">
      说明：以嘀嗒一条确定路线为参照，模拟不同因素影响下的价格，通过价格间对比确定不同因素对价格影响；
    </p>
    <div class="relative flex gap-4">
      <div class="absolute inset-0 flex left-[45%] items-center pointer-events-none text-2xl">
        <svg class="inline-flex! size-em" viewBox="0 0 20 18" version="1.1" xmlns="http://www.w3.org/2000/svg">
          <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g id="arrow-right" transform="translate(0.043478, 0.982078)" fill="currentColor" fill-rule="nonzero">
              <path
                d="M18.7619048,7.41624857 L11.4761905,0.273391429 C11.1904762,0.0114866727 10.7380952,-0.0837514245 10.4047619,0.0829152349 C10.0476191,0.225772381 9.76190477,0.582915234 9.76190477,0.963867623 L9.76190477,4.05910572 L0.95238095,4.05910572 C0.428571416,4.05910572 0,4.60672476 0,5.13053428 L0,11.0829152 C0,11.6067248 0.428571437,12.1543438 0.95238095,12.1543438 L9.76190477,12.1543438 L9.76190477,15.2495819 C9.76190477,15.6305343 10.0476191,15.9876771 10.3809524,16.1305343 C10.5,16.1781533 10.6428571,16.2019629 10.7619048,16.2019629 C11,16.2019629 11.2619048,16.1067248 11.4285714,15.9400581 L18.7142857,8.79720095 C18.9047619,8.60672476 19,8.36862952 19,8.10672476 C19,7.84482 18.952381,7.60672476 18.7619048,7.41624857 Z M11.6666667,12.9876771 L11.6666667,11.0829152 C11.6666667,10.5591057 11.3333333,10.2495819 10.8095238,10.2495819 L1.9047619,10.2495819 L1.9047619,5.96386762 L10.8095238,5.96386762 C11.3333333,5.96386762 11.6666667,5.65434381 11.6666667,5.13053428 L11.6666667,3.22577238 L16.6428571,8.10672476 L11.6666667,12.9876771 Z"
              ></path>
            </g>
          </g>
        </svg>
      </div>
      <div class="w-[45%]">
        <div class="flex flex-col gap-3">
          <div>
            P3：嘀嗒线上价格=f
            <span class="text-blue-500">(嘀嗒基础刊例价，调价系数，嘀嗒里程)</span>
          </div>
          <div>
            P2：嘀嗒基础刊例价模拟价格=f
            <span class="text-blue-500">(嘀嗒基础刊例价，嘀嗒里程)</span>
          </div>
          <div>
            {{ 'P1：DD刊例价与嘀嗒里程模拟价格=f' | replace: priceService.competitionName() }}
            <span class="text-blue-500">{{ '(DD刊例价，嘀嗒里程)' | replace: priceService.competitionName() }}</span>
          </div>
          <div>
            {{ 'P0：DD线上价格=f' | replace: priceService.competitionName() }}
            <span class="text-blue-500">{{ '(DD刊例价，DD里程)' | replace: priceService.competitionName() }}</span>
          </div>
          <div class="pt-4 text-red-500 px-[15%]">单条路线上不同变量的模拟价格</div>
        </div>
      </div>
      <div class="w-[55%] flex flex-col items-center">
        <div class="flex items-center gap-2">
          <div>
            {{ '嘀嗒与DD线上价格差异率：（P3-P0）/P0' | replace: priceService.competitionName() }}
            <span class="pl-4">=</span>
          </div>
          <div class="flex flex-col gap-1 items-center">
            <div>调价系数导致的价格差异率：（P3-P2）/P0</div>
            <div>+</div>
            <div>刊例价导致的价格差异率：（P2-P1）/P0</div>
            <div>+</div>
            <div>里程规划导致的价格差异率：（P1-P0）/P0</div>
          </div>
        </div>
        <div class="pt-6 text-red-500">差异率计算</div>
      </div>
    </div>
  </div>
</div>
