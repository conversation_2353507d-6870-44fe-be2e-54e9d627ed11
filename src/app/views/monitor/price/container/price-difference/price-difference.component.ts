import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  computed,
  DestroyRef,
  ElementRef,
  inject,
  signal,
} from '@angular/core'
import { FormsModule } from '@angular/forms'
import { HeadingComponent } from '@shared/components/heading'
import { RadioModule } from '@shared/modules/headless'
import { CityPickerComponent } from '../../components'
import { NzSelectModule } from 'ng-zorro-antd/select'
import { CaerusApiService } from '@api/caerus'
import { find, flattenDeep } from 'lodash'
import { GraphComponent } from '@shared/components/graph'
import { LineSpinComponent } from '@shared/components/line-spin'
import { Bar } from '../../lib'
import { BasicColumn } from './column-basic'
import { MultipleXAxis } from './line-multiple-x-axis'
import { QueryOutputVo } from '@api/query-engine/model'
import { LegendControlService, LegendItemClickHandler } from '@common/service'
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox'
import { NzPopoverModule } from 'ng-zorro-antd/popover'
import { BehaviorSubject, combineLatest, debounceTime, finalize, startWith } from 'rxjs'
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop'
import { QueryEngineFormService } from '@common/service/query-engine'
import { QueryEngineApiService } from '@api/query-engine'
import { GraphColumnComponent } from './graph-column/graph-column.component'
import { DatePipe } from '@angular/common'
import { addDays } from 'date-fns'
import { PriceService } from '../../price.service'
import { ReplacePipe } from '../../pipes/replace'
import { SwitchMap } from '@common/decorator'

@Component({
  selector: 'app-price-difference',
  templateUrl: './price-difference.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    HeadingComponent,
    RadioModule,
    FormsModule,
    CityPickerComponent,
    NzSelectModule,
    GraphComponent,
    LineSpinComponent,
    NzCheckboxModule,
    NzPopoverModule,
    GraphColumnComponent,
    ReplacePipe,
  ],
  providers: [LegendControlService, DatePipe],
})
export class PriceDifferenceComponent implements AfterViewInit {
  readonly apiService = inject(CaerusApiService)
  readonly labelClass = 'inline-flex items-center font-bold leading-0 whitespace-nowrap'
  readonly legendControlService = inject(LegendControlService)
  readonly destroyRef = inject(DestroyRef)
  readonly formService = inject(QueryEngineFormService)
  readonly queryService = inject(QueryEngineApiService)
  readonly priceService = inject(PriceService)
  readonly elementRef = inject(ElementRef)
  readonly datePipe = inject(DatePipe)

  c_ord_type = signal('0')

  price_type = signal(null)
  price_type_list = signal([])

  intal_list = signal([])
  intal = signal([])
  singleIntal = signal(null)
  city = signal([])
  citys = signal([])

  c_ord_type$ = toObservable(this.c_ord_type)
  city$ = toObservable(this.city)
  price_type$ = toObservable(this.price_type)
  singleIntal$ = toObservable(this.singleIntal)

  config = signal(null)

  loading = signal(false)
  errorMessage = signal(null)
  lineErrorMessage = signal(null)
  lineOption = signal(null)
  chartData = signal<QueryOutputVo>(null)
  metics = signal<any>(null)
  metics_list = signal([])
  selectMetic = new BehaviorSubject<string>(null)

  option = computed(() => {
    const chart = new Bar()
    // console.log('chart', chart.getOption())
    return chart.getOption()
  })

  ngAfterViewInit(): void {
    this.fetchConfig()
    this.fetchMetrics()
    this._subscribeChange()
    this._subscribeToSelectedMeticChange()
    this._subscribeToPriceDiffChange()
  }

  private _subscribeToPriceDiffChange() {
    this.priceService.priceDiff$.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(value => {
      // console.log(value)
      if (value) {
        const _city = find(this.citys(), ['value', value.cityName])
        if (!_city) {
          return
        }
        window.scrollTo({ top: this.elementRef.nativeElement.offsetTop, behavior: 'smooth' })
        this.c_ord_type.set(value.orderType?.key)
        const _price_type = find(this.price_type_list(), ['value', value.priceType.value])

        const _intal = find(this.intal_list(), ['value', value.miageIntal])
        // console.log('_intal', _intal)
        this.price_type.set(_price_type)
        this.city.set([_city])
        this.intal.set([_intal])
        this.singleIntal.set(_intal)
        this.getChartData()
      }
    })
  }

  private _subscribeToSelectedMeticChange() {
    this.selectMetic.subscribe(value => {
      if (value) {
        const metic = this.metics_list().find(item => item.aliasName === value)
        this.metics.set(metic)
        this.getChartData()
      }
    })
  }

  private _subscribeChange() {
    combineLatest([
      this.formService.form.valueChanges.pipe(startWith(this.formService.form.value)),
      this.priceService.competitionFilter$,
    ])
      .pipe(startWith([]), debounceTime(100), takeUntilDestroyed(this.destroyRef))
      .subscribe(() => {
        this.getChartData()
      })
  }

  fetchConfig() {
    this.apiService.fetchDimensionConfig('price_monitor_dimension').subscribe(res => {
      if (res.data) {
        // console.log('price-config', res.data['price_base'])
        const _config = res.data['price_base']
        const values = find(_config, ['extendName', 'inner_miage_intal'])?.values
        const priceOptions = find(_config, ['extendName', 'price_type'])?.values
        const ciyts = find(_config, ['extendName', 'city_name'])?.values
        this.citys.set(ciyts)
        this.intal_list.set(values)
        const _intal = find(values, ['value', '[20,30)'])
        this.intal.set([_intal])
        this.singleIntal.set(_intal)
        this.price_type_list.set(priceOptions)
        this.price_type.set(priceOptions[1])
        const bj = find(ciyts, ['value', '北京'])
        this.city.set([
          {
            key: bj?.key,
            value: bj?.value,
          },
        ])
        this.config.set(_config)
        this.getChartData()
      }
    })
  }

  fetchMetrics() {
    this.apiService.fetchMetricsConfig('price_monitor_metrics').subscribe(res => {
      if (res.data) {
        // console.log('metics', res.data['diff_reason_cardrate_amt'])
        const _metrics = res.data['diff_reason_cardrate_amt'].subMetric
        this.metics_list.set(_metrics)
        this.metics.set(_metrics[0])
        this.getChartData()
      }
    })
  }

  changeSingleIntal() {
    this.intal.set([this.singleIntal()])
    this.getChartData()
  }

  @SwitchMap()
  getChartData() {
    if (!this.metics_list() || !this.config()) {
      return
    }
    const body = this.formService.value()
    const startTime = body.dt.startTime
    const endTime = body.dt.endTime
    if (startTime === endTime) {
      body.dt = {
        startTime: this.datePipe.transform(addDays(new Date(endTime.replace(/-/g, '/')), -30), 'yyyy-MM-dd'),
        endTime,
      }
    }
    if (body.compareDt) {
      const _startTime = body.compareDt.startTime
      const _endTime = body.compareDt.endTime
      // console.log('_endTime', _endTime)
      if (_startTime === _endTime) {
        body.compareDt = {
          startTime: this.datePipe.transform(addDays(new Date(_endTime.replace(/-/g, '/')), -30), 'yyyy-MM-dd'),
          endTime: _endTime,
        }
      }
    }
    body.dimensions = [
      {
        id: null,
        extendName: 'dt',
        predefineCompareType: ['dt', 'yw', 'ym'],
      },
    ]
    body.scene = 7
    body.dataFillConfig = {
      open: 1,
      fillRangeType: 2,
      fillValue: 0,
    }
    const defaultItems = [
      {
        conditionType: 2,
        condition: 'in',
        id: null,
        extendName: 'city_name',
        value: this.city(),
        valueType: null,
      },
      {
        conditionType: 2,
        condition: 'in',
        id: null,
        extendName: 'price_type',
        value: [
          {
            key: this.price_type().key,
            value: this.price_type().value,
          },
        ],
        valueType: null,
      },
      {
        conditionType: 2,
        condition: 'in',
        id: null,
        extendName: 'c_ord_type',
        value: [
          {
            key: this.c_ord_type(),
            value: this.c_ord_type() === '0' ? '市内' : '城际',
          },
        ],
        valueType: null,
      },
    ]

    body.metrics = []
    body.filter.items = [this.priceService.competitionFilter().at(0)]
    body.metrics.push({
      extendName: this.metics()?.extendName,
      filter: {
        items: [
          ...defaultItems,
          {
            conditionType: 2,
            condition: 'in',
            id: null,
            extendName: this.c_ord_type() === '0' ? 'inner_miage_intal' : 'inter_miage_intal',
            value: [
              {
                key: this.singleIntal().key,
                value: this.singleIntal().value,
              },
            ],
            valueType: null,
          },
        ],
        type: null,
      },
    })

    this.loading.set(true)
    return this.queryService
      .search(body, 'differnce-line-chart')
      .pipe(finalize(() => this.loading.set(false)))
      .subscribe(res => {
        if (res.data) {
          // console.log('line-data', res.data)
          this.setLineChart(res.data)
        } else {
          this.lineOption.set(null)
          this.lineErrorMessage.set(res.error)
        }
      })
  }

  changeOrdType() {
    if (!this.config()) {
      return
    }
    const extendName = this.c_ord_type() === '0' ? 'inner_miage_intal' : 'inter_miage_intal'
    const values = find(this.config(), ['extendName', extendName])?.values
    let _intal
    if (this.c_ord_type() === '0') {
      _intal = find(values, ['value', '[20,30)'])
    } else {
      _intal = find(values, ['value', '[80,120)'])
    }
    this.intal_list.set(values)
    this.intal.set([_intal])
    this.singleIntal.set(_intal)
    this.getChartData()
  }

  setLineChart(data) {
    const legendItemClick = LegendItemClickHandler(this.legendControlService)
    let chart
    const body = this.formService.value()
    if (body.compareDt) {
      chart = new MultipleXAxis(data, this.priceService.competitionName())
    } else {
      chart = new BasicColumn(data, this.priceService.competitionName())
    }

    this.chartData.set(data)
    chart.plotOptions.series.events = { legendItemClick }
    this.lineOption.set(chart?.getOption() || null)
  }
}
