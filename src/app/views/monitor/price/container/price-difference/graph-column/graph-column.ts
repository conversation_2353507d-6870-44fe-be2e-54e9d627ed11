import { EventEmitter } from '@angular/core';
import { BaseHighCharts } from '@common/chart/highcharts';
import { ChartOptions, XAxisOptions, YAxisOptions } from 'highcharts';

export default class extends BaseHighCharts {

  chart: ChartOptions = {
    spacing: [20, 10, 5, 10],
    type: 'column',
  };

  xAxis: XAxisOptions = {
    categories: [],
    gridLineWidth: 0,
    lineWidth: 0,
    tickWidth: 1,
    tickColor: 'rgba(0, 0, 0, 0.2)',
    labels: {
      useHTML: true,
      rotation: 0,
      formatter: function(params) {
        return (<string>params.value).replace('价格', '<br/>价格').replace('实价', '<br/>实价');
      }
    }
  };

  yAxis: YAxisOptions = {
    title: { text: '' },
    gridLineWidth: 1,
    tickPixelInterval: 40,
    labels: {
      format: `{value}%`,
      style: {
        fontSize: '12px'
      }
    }
  };

  override plotOptions = {
    column: {
      allowPointSelect: true,
      cursor: 'pointer',
      states: {
        select: {
          enabled: false
        },
        hover: {
          brightness: 0.1
        }
      }
    }
  } as any;

  click = new EventEmitter<{ category: string, index: number, selected: boolean }>();
  

  constructor() {
    super();
    this.legend.enabled = false;
    this.tooltip.valueSuffix = '%';
    this.responsive = null;
  }

  setCategories(categories: string[]) {
    this.xAxis.categories = categories;
  }

  setSeries(series: any[]) {
    this.series = series;
  }

  override getOption() {
    const value = this;

    return {
      ...value,
      plotOptions: {
        ...value.plotOptions,
        column: {
          ...value.plotOptions.column,
          events: {
            click: (event) => {
              setTimeout(() => {
                const { category, index, selected } = event.point;

                console.log(event.point);
                
                this.click.emit({ category, index, selected });
              })
            }
          }
        }
      }
    }   
  }

}