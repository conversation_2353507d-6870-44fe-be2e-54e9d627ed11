import { AfterViewInit, ChangeDetectionStrategy, Component, DestroyRef, inject, signal } from '@angular/core';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { combineLatest, debounceTime, finalize, startWith } from 'rxjs';

import { isProd } from '@common/const';
import { Pattern } from '@common/chart/highcharts';
import { CaerusApiService } from '@api/caerus';
import { QueryEngineApiService } from '@api/query-engine';
import { SwitchMap } from '@common/decorator';
import { QueryEngineFormService } from '@common/service/query-engine';
import { LineSpinComponent } from '@shared/components/line-spin';
import { GraphComponent } from '@shared/components/graph';
import { MetricsMenuVo } from '@api/caerus/model';
import { PriceDifferenceComponent } from '../price-difference.component';
import { isNotNull, toDecimals } from '@common/function';
import { QueryOutputVo } from '@api/query-engine/model';
import { ReplacePipe } from '@views/monitor/price/pipes/replace';
import { PriceService } from '@views/monitor/price';
import GraphColumn from './graph-column';
import { LegendControlService } from '@common/service';

class MiniColumnSeries {
  constructor(
    public name: string,
    public data: Array<{ y: number; color: string | any }> = []
  ) {}
}

@Component({
  selector: 'app-graph-column',
  template: `
    <div class="flex flex-col h-96 shadow-md rounded-sm border border-neutral-100">
      <div class="px-4 pt-4 text-neutral-500 text-xs">
        说明:
        <span class="text-primary">{{'嘀嗒与DD线上价格差异率' | replace: priceService.competitionName()}}</span>
        =调价系数价导致的价格差异率+刊例价导致的价格差异率+里程规划导致的价格差异率
      </div>
      <div class="flex-1 min-h-0 relative flex items-center justify-center text-xs">
        @if (loading()) {
          <app-line-spin />
        } @else {
          @if (option()) {
            <div class="absolute inset-2">
              <app-graph [options]="option()" />
            </div>
          } @else if (errorMessage()) {
            <span>{{ errorMessage() }}</span>
          } @else {
            <span></span>
          }
        }
      </div>
    </div>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    GraphComponent, 
    LineSpinComponent,
    ReplacePipe,
  ],
  providers: [
    LegendControlService,
    ReplacePipe
  ]
})
export class GraphColumnComponent implements AfterViewInit {
  readonly destroyRef = inject(DestroyRef);
  readonly caerusApiService = inject(CaerusApiService);
  readonly formService = inject(QueryEngineFormService);
  readonly apiService = inject(QueryEngineApiService);
  readonly parent = inject(PriceDifferenceComponent);
  readonly priceService = inject(PriceService);
  readonly replacePipe = inject(ReplacePipe);

  loading = signal(false);
  option = signal<any>(null);
  errorMessage = signal(null);
  metics = signal<MetricsMenuVo[]>(null);
  metics$ = toObservable(this.metics);

  ngAfterViewInit(): void {
    this.fetchMetrics();
    this._subscribeFormChange();
  }

  private _subscribeFormChange() {
    combineLatest([
      this.metics$,
      this.parent.c_ord_type$,
      this.parent.city$,
      this.parent.price_type$,
      this.parent.singleIntal$, 
      this.formService.form.valueChanges.pipe(startWith(null)),
      this.priceService.competitionFilter$,
    ])
    .pipe(
      debounceTime(100), 
      takeUntilDestroyed(this.destroyRef)
    )
    .subscribe(([metics, c_ord_type, city, price_type]) => {
      if (isNotNull(metics, c_ord_type, city, price_type)) {
        this.query();
      }
    });
  }

  fetchMetrics() {
    this.caerusApiService.fetchMetricsConfig('price_monitor_metrics').subscribe(res => {
      if (res.data) {
        const { subMetric } = res.data['diff_reason_cardrate_amt'];

        this.metics.set(subMetric);
      }
    });
  }

  private _filterItems() {
    return [
      {
        conditionType: 2,
        condition: 'in',
        id: null,
        extendName: 'city_name',
        value: this.parent.city(),
        valueType: null,
      },
      {
        conditionType: 2,
        condition: 'in',
        id: null,
        extendName: 'price_type',
        value: [
          {
            key: this.parent.price_type().key,
            value: this.parent.price_type().value,
          },
        ],
        valueType: null,
      },
      {
        conditionType: 2,
        condition: 'in',
        id: null,
        extendName: 'c_ord_type',
        value: [
          {
            key: this.parent.c_ord_type(),
            value: this.parent.c_ord_type() === '0' ? '市内' : '城际',
          },
        ],
        valueType: null,
      },
      {
        conditionType: 2,
        condition: 'in',
        id: null,
        extendName: this.parent.c_ord_type() === '0' ? 'inner_miage_intal' : 'inter_miage_intal',
        value: [
          {
            key: this.parent.intal()[0].key,
            value: this.parent.intal()[0].value,
          },
        ],
        valueType: null,
      },
      this.priceService.competitionFilter()
    ];
  }

  @SwitchMap()
  query() {
    const body = this.formService.value();

    body.dimensions = [];
    body.metrics = this.metics().map(({ extendName }) => ({ extendName }));
    body.filter.items = this._filterItems().flat(1);
    body.scene = isProd() ? 7 : 1;
    body.dataFillConfig = {
      open: 0,
      fillRangeType: 2,
      fillValue: 0,
    };

    this.loading.set(true);
    return this.apiService
      .search(body, 'differnce-column-chart')
      .pipe(finalize(() => this.loading.set(false)))
      .subscribe(res => {
        if (res.status !== '00000') {
          return this.errorMessage.set(res.message || res.error);
        }
        this.#renderChart(res.data);
      });
  }

  #renderChart({ data }: QueryOutputVo) {
    const chart = new GraphColumn();
    const aliasNames = this.metics().map(({ aliasName }) => this.replacePipe.transform(aliasName, this.priceService.competitionName()));
    const series = [{ name: '当前期', data: [{}, {}, {}, {}] }] as Array<MiniColumnSeries>;
    const red = '#ffbf6b';
    const green = '#68bbc4';
    const blue = 'rgb(80, 135, 236)';

    data.forEach(item => {
      const keys = Object.keys(item);
      keys.forEach(key => {
        const value = item[key] === null ? null : parseFloat(item[key]);
        const y = toDecimals(value);
        const color = y > 0 ? red : green;

        switch (key) {
          case 'dida_conrtar_mean_samp_actprice_rate':
            series[0].data[0] = { y, color: blue };
            break;
          case 'dida_conrtar_diff_samp_competition_actprice_amt':
            series[0].data[1] = { y, color };
            break;
          case 'dida_conrtar_diff_samp_cardrate_amt':
            series[0].data[2] = { y, color };
            break;
          case 'dida_conrtar_dis_diff_samp_amt':
            series[0].data[3] = { y, color };
            break;
        }
      });
    });

    if (this.formService.hasDateCompare()) {
      series.push({ name: '对比期', data: [null, null, null, null] });

      data.forEach(item => {
        const keys = Object.keys(item);
        keys.forEach(key => {
          const value = item[key] === null ? null : parseFloat(item[key]);
          const y = toDecimals(value);
          const color = y > 0 ? red : green;

          switch (key) {
            case 'dida_conrtar_mean_samp_actprice_rate_DIFF':
              series[1].data[0] = { y, color: { pattern: new Pattern({ color: blue }) } };
              break;
            case 'dida_conrtar_diff_samp_competition_actprice_amt_DIFF':
              series[1].data[1] = { y, color: { pattern: new Pattern({ color }) } };
              break;
            case 'dida_conrtar_diff_samp_cardrate_amt_DIFF':
              series[1].data[2] = { y, color: { pattern: new Pattern({ color }) } };
              break;
            case 'dida_conrtar_dis_diff_samp_amt_DIFF':
              series[1].data[3] = { y, color: { pattern: new Pattern({ color }) } };
              break;
          }
        });
      });
    }

    chart.setCategories(aliasNames);
    chart.setSeries(series);
    
    chart.click.subscribe(({ category, selected }) => {
      if (selected) {
        this.parent.selectMetic.next(category);
      }
    });

    this.option.set(chart.getOption());
  }
}
