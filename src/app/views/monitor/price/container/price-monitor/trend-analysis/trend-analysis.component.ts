import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  computed,
  DestroyRef,
  ElementRef,
  inject,
  OnInit,
  signal,
} from '@angular/core'
import { FormsModule } from '@angular/forms'
import { CaerusApiService } from '@api/caerus'
import { LegendControlService, LegendItemClickHandler } from '@common/service'
import { GraphComponent } from '@shared/components/graph'
import { LineSpinComponent } from '@shared/components/line-spin'
import { RadioModule } from '@shared/modules/headless'
import { CityPickerComponent } from '@views/monitor/price/components'
import { MultipleXAxis } from '../../../lib'
import { BasicLine } from './line-basic'
import { NzSelectModule } from 'ng-zorro-antd/select'
import { find, filter, groupBy } from 'lodash'
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox'
import { NzSwitchModule } from 'ng-zorro-antd/switch'
import { QueryEngineFormService } from '@common/service/query-engine'
import { QueryEngineApiService } from '@api/query-engine'
import { finalize, debounceTime, combineLatest, startWith, single } from 'rxjs'
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop'
import { PriceService } from '@views/monitor/price/price.service'
import { SwitchMap } from '@common/decorator'
import { DateCompareComponent } from '@shared/components/date-compare'
import { DatePipe } from '@angular/common'
import { addDays, differenceInDays, subDays, format } from 'date-fns'
import { IconMapArrowRightComponent } from '@shared/modules/icons'
import { NzPopoverModule } from 'ng-zorro-antd/popover'
import { PriceComponent } from '@views/monitor/price/price.component'
import { ReplacePipe } from '@views/monitor/price/pipes/replace'
import { GridMultipleComponent } from './chart-gridmultiple.component'
import { NzModalModule } from 'ng-zorro-antd/modal'
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker'

@Component({
  selector: 'app-trend-analysis',
  imports: [
    RadioModule,
    FormsModule,
    CityPickerComponent,
    NzSelectModule,
    LineSpinComponent,
    // GraphComponent,
    NzModalModule,
    NzCheckboxModule,
    NzSwitchModule,
    IconMapArrowRightComponent,
    DateCompareComponent,
    NzPopoverModule,
    ReplacePipe,
    GridMultipleComponent,
    NzDatePickerModule,
  ],
  templateUrl: './trend-analysis.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [LegendControlService, QueryEngineFormService, DatePipe],
})
export class TrendAnalysisComponent implements AfterViewInit, OnInit {
  readonly priceService = inject(PriceService)
  readonly legendControlService = inject(LegendControlService)
  readonly apiService = inject(CaerusApiService)
  readonly formService = inject(QueryEngineFormService)
  readonly queryService = inject(QueryEngineApiService)
  readonly elementRef = inject(ElementRef)
  readonly destroyRef = inject(DestroyRef)
  readonly datePipe = inject(DatePipe)
  readonly parent = inject(PriceComponent)
  readonly labelClass = 'inline-flex items-center font-bold leading-0 whitespace-nowrap'

  querying = signal(false)
  c_ord_type = signal('0')
  price_type = signal(null)
  price_type_list = signal([])
  errorMessage = signal(null)
  option = signal(null)
  loading = signal(false)
  chartData = signal(null)
  type = signal('route-price')
  config = signal(null)
  intal = signal<any>(null)
  dida_price = signal(null)
  compare_price = signal(null)
  dida_benchmark_book_ord_amt = signal(true)
  metrics = signal(null)
  city_region = signal([null])
  // compareDt = signal(null)
  // dt = signal(null)
  city_name = signal([null])
  rhea = signal(null)
  rheaType = signal('1')
  rheaList = signal([])
  chartValue = signal(null)
  changeCitys = signal([])
  rheaValue = signal(null)
  isVisible = signal(false)
  city = signal([])
  date = signal([])
  detailLoading = signal(false)
  detailData = signal(null)

  dida_price_list = computed(() => {
    if (!this.metrics()) {
      return []
    }
    let metrics
    if (this.type() === 'route-price') {
      metrics = this.metrics()['trend_route_price'].subMetric
    } else {
      metrics = this.metrics()['trend_reference_price'].subMetric
    }
    return filter(metrics, ['tagName', 'dida_price'])
  })

  compare_price_list = computed(() => {
    if (!this.metrics()) {
      return []
    }
    let metrics
    if (this.type() === 'route-price') {
      metrics = this.metrics()['trend_route_price'].subMetric
    } else {
      metrics = this.metrics()['trend_reference_price'].subMetric
    }
    return filter(metrics, ['tagName', 'compare_price'])
  })

  intal_list = computed(() => {
    if (!this.config()) {
      return []
    }
    if (this.type() === 'route-price') {
      const extendName = this.c_ord_type() === '0' ? 'inner_miage_intal' : 'inter_miage_intal'
      return find(this.config(), ['extendName', extendName])?.values
    }
    return find(this.config(), ['extendName', 'inner_cardrate_dis'])?.values
  })

  citys = computed(() => {
    if (!this.config()) {
      return []
    }
    if (this.type() === 'route-price') {
      return find(this.config(), ['extendName', 'city_region'])?.values
    }
    return find(this.config(), ['extendName', 'city_name'])?.values
  })

  config$ = toObservable(this.config)
  metrics$ = toObservable(this.metrics)

  private _subscribeValues() {
    combineLatest([this.config$, this.metrics$]).subscribe(([a, b]) => {
      if (a && b) {
        if (this.type() === 'route-price') {
          this.intal.set(null)
        } else {
          this.intal.set(this.intal_list()[0])
        }

        if (this.type() === 'route-price') {
          this.city_region.set([find(this.citys(), ['value', '北京'])])
        } else {
          this.city_name.set([find(this.citys(), ['value', '北京'])])
        }
        setTimeout(() => {
          this.getChartData()
        })
      }
    })
  }

  ngOnInit(): void {
    const now = new Date().setHours(0, 0, 0, 0)
    this.formService.dt.patchValue({
      startTime: this.datePipe.transform(addDays(now, -30), 'yyyy-MM-dd'),
      endTime: this.datePipe.transform(addDays(now, -1), 'yyyy-MM-dd'),
    })
    this.date.set([
      this.datePipe.transform(addDays(now, -30), 'yyyy-MM-dd'),
      this.datePipe.transform(addDays(now, -1), 'yyyy-MM-dd'),
    ])
  }

  ngAfterViewInit() {
    this.fetchDimension()
    this.fetchMetrics()
    this._subscribeToQuickCompareChange()
    this._subscribeToRootFormChange()
    this._subscribeChange()
    this._subscribeValues()
    this.getRheaList()
  }

  private _subscribeToQuickCompareChange() {
    this.priceService.quickCompare$.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(compareItems => {
      console.log('[一键对比]', compareItems)

      // console.log(this.elementRef.nativeElement.offsetTop);
      if (compareItems.scroll) {
        window.scrollTo({ top: this.elementRef.nativeElement.offsetTop, behavior: 'smooth' })
      }

      if (compareItems.isReferencePrice) {
        this.type.set('published-price')
      } else {
        this.type.set('route-price')
      }

      if (compareItems.cityRegions) {
        const { cityRegions } = compareItems
        const value = cityRegions.map(({ key, value }) => ({ key, value }))

        setTimeout(() => {
          this.city_region.set(value)
        }, 0)
      }

      if (compareItems.cityName) {
        const { cityName } = compareItems
        const value = cityName.map(({ key, value }) => ({ key, value }))
        setTimeout(() => {
          this.city_name.set(value)
        }, 0)
      }

      if (compareItems.priceType) {
        const {
          priceType: { key, value },
        } = compareItems
        const price_type_item = this.price_type_list().find(item => item.key === key && item.value === value)

        this.price_type.set(price_type_item)
      }

      if (compareItems.priceTypeList) {
        const { priceTypeList } = compareItems
        const keys = priceTypeList.filter(l => l).map(l => l.key)
        const price_type_item = this.price_type_list().filter(l => {
          return keys.includes(l.key)
        })

        this.price_type.set(price_type_item[0])
      }

      if (compareItems.orderType) {
        const {
          orderType: { key },
        } = compareItems

        this.c_ord_type.set(key)
      }

      if (compareItems.miageIntal) {
        const {
          miageIntal: { key, value },
        } = compareItems
        const intal_list_item = this.intal_list().find(item => item.key === key && item.value === value)
        setTimeout(() => {
          this.intal.set(intal_list_item)
        }, 0)
      }

      if (compareItems.miageIntalList) {
        const { miageIntalList } = compareItems
        const keys = miageIntalList.filter(l => l).map(l => l.key)
        const intal = this.intal_list().filter(l => {
          return keys.includes(l.key)
        })
        setTimeout(() => {
          this.intal.set(intal[0])
        }, 0)
      }

      setTimeout(() => {
        this.getChartData()
      })
    })
  }

  private _subscribeToRootFormChange() {
    this.parent.formService.form.valueChanges
      .pipe(startWith(this.parent.formService.form.value), debounceTime(100), takeUntilDestroyed(this.destroyRef))
      .subscribe(value => {
        const { startTime, endTime } = this._syncDate(value.dt)
        this.formService.dt.patchValue(this._syncDate(value.dt))
        this.date.set([startTime, endTime])
        if (value.compareDt) {
          if (!this.formService.hasDateCompare()) {
            this.formService.addDateCompare()
          }

          this.formService.compareDt.patchValue(this._syncDate(value.compareDt))
        } else {
          this.formService.removeDateCompare()
        }
      })
  }

  private _syncDate({ startTime, endTime }: Partial<{ startTime: string; endTime: string }>) {
    const startDate = new Date(startTime.replace(/-/g, '/'))
    const endDate = new Date(endTime.replace(/-/g, '/'))
    const count = differenceInDays(startDate, endDate)

    if (count === 0) {
      return {
        startTime: format(subDays(endDate, 30), 'yyyy-MM-dd'),
        endTime,
      }
    } else {
      return { startTime, endTime }
    }
  }

  getRheaList() {
    const _city = this.type() === 'route-price' ? this.city_region() : this.city_name()
    if (!_city[0]) {
      return
    }
    const dt = this.formService.value().dt
    const city = this.type() === 'route-price' ? this.city_region()[0].value : this.city_name()[0].value
    this.apiService
      .postRheaList({
        experimentStartTime: dt.startTime,
        experimentEndTime: dt.endTime,
        cityName: city,
      })
      .subscribe(res => {
        if (res.data && res.data.length !== 0) {
          this.rheaList.set(res.data)
          this.rheaValue.set(res.data[0].experimentGroupId)
          if (this.rheaType() === '3') {
            this.getChartData()
          }
        } else {
          this.rheaList.set([])
          this.rheaValue.set(null)
          this.rheaType.set('1')
          this.getChartData()
        }
      })
  }

  changeRheaType() {
    this.getChartData()
  }

  getChangeCity() {
    const dt = this.formService.value().dt
    if (!dt.endTime) {
      return
    }
    this.apiService
      .postChangeCity({
        experimentStartTime: dt.startTime,
        experimentEndTime: dt.endTime,
      })
      .subscribe(res => {
        if (res.data) {
          const arr = res.data.filter(d => {
            return this.citys().some(c => c.value === d)
          })
          this.city.set(
            this.citys().filter(c => {
              return arr.includes(c.value)
            })
          )
          this.changeCitys.set(arr)
        } else {
          this.changeCitys.set([])
        }
      })
  }

  clickCity(item) {
    if (this.type() === 'route-price') {
      this.city_region.set([find(this.citys(), ['value', item])])
    } else {
      this.city_name.set([find(this.citys(), ['value', item])])
    }
    this.getRheaList()
    if (this.rheaType() !== '3') {
      this.getChartData()
    }
  }

  private _subscribeChange() {
    combineLatest([
      this.formService.form.valueChanges.pipe(startWith(this.formService.form.value)),
      this.priceService.competitionFilter$,
    ])
      .pipe(startWith([]), debounceTime(100), takeUntilDestroyed(this.destroyRef))
      .subscribe(() => {
        this.getChangeCity()
        this.getRheaList()
        this.getChartData()
      })
  }

  changeType() {
    this.c_ord_type.set('0')
    this.compare_price.set(this.compare_price_list()[0]?.extendName)
    this.dida_price.set(this.dida_price_list()[0]?.extendName)
    if (this.type() === 'route-price') {
      this.city_region.set([find(this.citys(), ['value', '北京'])])
    } else {
      this.city_name.set([find(this.citys(), ['value', '北京'])])
    }
    if (this.type() !== 'route-price') {
      this.intal.set(this.intal_list()[0])
    } else {
      this.intal.set(null)
    }
    this.getChartData()
  }

  clickDetail() {
    this.isVisible.set(true)
    this.detailLoading.set(true)
    this.apiService
      .postRheaLog({
        cityNameList: this.city().map(c => c.value),
        startTime: this.date()[0],
        endTime: this.date()[1],
      })
      .subscribe(res => {
        this.detailLoading.set(false)
        const obj = {}
        res.data.forEach(d => {
          if (!obj[d.dt]) {
            obj[d.dt] = []
          } else {
            obj[d.dt].push(d)
          }
        })
        Object.keys(obj).forEach(k => {
          const _obj = {}
          obj[k].forEach(o => {
            if (!_obj[o.expId]) {
              _obj[o.expId] = []
            } else {
              _obj[o.expId].push(o)
            }
          })
          obj[k] = _obj
        })
        console.log('detail', obj)
        this.detailData.set(obj)
      })
  }

  @SwitchMap()
  getChartData() {
    if (
      //!this.dt() ||
      !this.config() ||
      !this.metrics()
    ) {
      return
    }
    const cityData = this.type() === 'route-price' ? this.city_region() : this.city_name()

    if (!cityData || !this.price_type()) {
      this.option.set(null)
      this.errorMessage.set('暂无数据')
      return
    }
    if (this.type() !== 'route-price') {
      if (!this.intal()) {
        this.option.set(null)
        this.errorMessage.set('暂无数据')
        return
      }
    }
    const body = this.formService.value()

    body.dimensions = [
      {
        id: null,
        extendName: 'dt',
        predefineCompareType: ['dt', 'yw', 'ym'],
      },
    ]
    body.scene = 7
    body.dataFillConfig = {
      open: 1,
      fillRangeType: 2,
      fillValue: 0,
    }

    const items = [
      {
        conditionType: 2,
        condition: '=',
        id: null,
        extendName: 'c_ord_type',
        value: [
          {
            key: this.c_ord_type(),
            value: this.c_ord_type() === '0' ? '市内' : '城际',
          },
        ],
        valueType: null,
      },
    ]

    body.filter = {
      items,
      type: null,
    }

    const _metrics = [this.dida_price(), this.compare_price()]
    const metrics = _metrics.map(m => {
      return {
        extendName: m,
      }
    })
    if (this.dida_benchmark_book_ord_amt() && this.type() === 'route-price') {
      metrics.push({
        extendName: 'dida_benchmark_book_ord_amt',
      })
    }

    let extendName

    if (this.type() === 'route-price') {
      extendName = this.c_ord_type() === '0' ? 'inner_miage_intal' : 'inter_miage_intal'
    } else {
      extendName = 'inner_cardrate_dis'
    }

    const city =
      this.type() === 'route-price'
        ? {
            key: 'city_region',
            value: this.city_region()[0],
          }
        : {
            key: 'city_name',
            value: this.city_name()[0],
          }

    const filters = [
      city,
      {
        key: extendName,
        value: this.intal(),
      },
      {
        key: 'price_type',
        value: this.price_type(),
      },
    ].filter(a => a.value)

    const results = filters.map(f => {
      return {
        conditionType: 2,
        condition: '=',
        id: null,
        extendName: f.key,
        value: [
          {
            key: f.value.key,
            value: f.value.value,
          },
        ],
        valueType: null,
      }
    })

    if (results.length === 0) {
      body.metrics = metrics
    } else {
      metrics.forEach((m, i) => {
        const f = {
          userDefExtendName: `${m.extendName}_${i}_userDefExtendName`,
          extendName: m.extendName,
          filter: {
            items: results,
            type: null,
          },
        }
        if (
          !['dida_mean_samp_actprice_amt', 'dida_benchmark_book_ord_amt', 'dida_mean_cardrate_amt'].includes(
            f.extendName
          )
        ) {
          f.filter.items = f.filter.items.concat(this.priceService.competitionFilter())
        }
        if (f.extendName !== 'dida_benchmark_book_ord_amt' && this.rheaType() !== '2') {
          f.filter.items = f.filter.items.concat({
            conditionType: 2,
            condition: '=',
            id: null,
            extendName: 'exptgrop_id',
            value: [
              {
                key: this.rheaType() === '1' ? '0' : this.rheaValue(),
                value: this.rheaType() === '1' ? '0' : this.rheaValue(),
              },
            ],
            valueType: null,
          })
        }
        body.metrics.push(f)
      })
    }

    this.loading.set(true)
    this.legendControlService.reset()
    return this.queryService
      .search(body, 'price-chart')
      .pipe(finalize(() => this.loading.set(false)))
      .subscribe(res => {
        if (res.data) {
          const data = res.data
          // this.setLineChart(data)
          this.chartValue.set(data)
        } else {
          this.option.set(null)
          this.errorMessage.set(res.message)
        }
      })
  }

  fetchDimension() {
    this.apiService.fetchDimensionConfig('price_monitor_dimension').subscribe(res => {
      if (res.data) {
        // console.log('price-config', res.data)
        this.config.set(res.data['price_base'])
        const lists = find(res.data['price_base'], ['extendName', 'price_type'])?.values
        this.price_type_list.set(lists)
        this.price_type.set(lists[1])
      }
    })
  }

  fetchMetrics() {
    this.apiService.fetchMetricsConfig('price_monitor_metrics').subscribe(res => {
      if (res.data) {
        this.metrics.set(res.data)
        let metrics
        if (this.type() === 'route-price') {
          metrics = this.metrics()['trend_route_price'].subMetric
        } else {
          metrics = this.metrics()['trend_reference_price'].subMetric
        }
        // console.log('metrics', metrics)
        this.dida_price.set(metrics.filter(m => m.tagName === 'dida_price')[0].extendName)
        this.compare_price.set(metrics.filter(m => m.tagName === 'compare_price')[0].extendName)
      } else {
        this.metrics.set(null)
      }
    })
  }

  changeOrdType() {
    this.intal.set(this.intal_list()[0])
    this.getChartData()
  }

  setLineChart(data) {
    const legendItemClick = LegendItemClickHandler(this.legendControlService)
    let chart
    if (this.formService.hasDateCompare()) {
      chart = new MultipleXAxis(data)
    } else {
      chart = new BasicLine(data)
    }

    this.chartData.set(data)
    chart.plotOptions.series.events = { legendItemClick }
    this.option.set(chart?.getOption() || null)
  }
}
