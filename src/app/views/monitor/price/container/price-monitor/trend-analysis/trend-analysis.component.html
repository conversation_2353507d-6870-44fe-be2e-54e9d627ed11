<div class="flex flex-col gap-3 pt-4">
  <div class="flex items-center relative p-5 px-6">
    <span class="flex items-center gap-x-1.5 font-black text-base">
      <MapArrowRightIcon />
      趋势分析
    </span>

    <app-radio-group
      [(ngModel)]="type"
      (ngModelChange)="changeType()"
      class="absolute inset-1/2 top-1/2 -translate-y-1/2 -translate-x-1/2 w-60 h-8 flex items-start gap-x-0.5 p-0.5 bg-[#ebebec] rounded-lg"
    >
      <app-radio class="dida-radio-new" activeClass="active" value="route-price">路线价格</app-radio>
      <app-radio class="dida-radio-new" activeClass="active" value="published-price">刊例价格</app-radio>
      <app-radio-thumb [offsetX]="2" class="bg-linear-to-r from-[#465867] to-[#1E2C3A] rounded-md" />
    </app-radio-group>
  </div>

  <div class="flex items-center flex-wrap gap-x-3 gap-y-2">
    <app-date-compare class="-ml-8" [class.pointer-events-none]="querying()" deleteBtn showDtType="false" />
    @if (changeCitys().length !== 0) {
      <div class="text-xs">
        当前时间周期内，共有
        @for (item of changeCitys(); track item) {
          <span class="text-blue-400 cursor-pointer pr-2" (click)="clickCity(item)">{{ item }},</span>
        }
        等{{ changeCitys().length }}个城市进行了调价实验。
      </div>
    } @else {
      <div>当前时间周期内，没有城市进行了调价实验。</div>
    }
  </div>
  <div class="flex items-center flex-wrap gap-x-10 gap-y-2">
    <div class="flex items-center">
      <label [class]="labelClass">起点城市：</label>
      @if (type() === 'route-price') {
        <app-city-picker [(ngModel)]="city_region" (ngModelChange)="getRheaList(); getChartData()" />
      } @else {
        <app-city-picker useReferencePrice [(ngModel)]="city_name" (ngModelChange)="getRheaList(); getChartData()" />
      }
    </div>
    <div class="flex items-center">
      <label [class]="labelClass">订单类型：</label>
      <app-radio-group class="relative flex gap-1" [(ngModel)]="c_ord_type" (ngModelChange)="changeOrdType()">
        <app-radio class="tag-radio" activeClass="active" value="0">市内</app-radio>
        @if (type() === 'route-price') {
          <app-radio class="tag-radio" activeClass="active" value="1">城际</app-radio>
        }
      </app-radio-group>
    </div>
    <div class="flex items-center">
      <label [class]="labelClass">调价影响范围选择：</label>
      <nz-select [(ngModel)]="rheaType" class="w-[260px]" (ngModelChange)="changeRheaType()">
        <nz-option nzLabel="全部路线（含实验路线加权）" nzValue="1"></nz-option>
        <nz-option nzLabel="只看控制组路线" nzValue="2"></nz-option>
        <nz-option [nzDisabled]="rheaList().length === 0" nzLabel="只看实验路线" nzValue="3"></nz-option>
      </nz-select>
    </div>
    @if (rheaType() === '3') {
      <div class="flex items-center">
        <label [class]="labelClass">实验组选择：</label>
        <nz-select [(ngModel)]="rheaValue" class="w-[400px]" (ngModelChange)="getChartData()">
          @for (item of rheaList(); track item) {
            <nz-option [nzLabel]="item.value" [nzValue]="item.experimentGroupId"></nz-option>
          }
        </nz-select>
      </div>
    }
  </div>
  <div class="flex items-center flex-wrap gap-x-10 gap-y-2">
    <div class="flex items-center">
      <label [class]="labelClass">价格类型选择：</label>
      <app-radio-group class="relative flex gap-1" [(ngModel)]="price_type" (ngModelChange)="getChartData()">
        @for (l of price_type_list(); track l) {
          <app-radio class="tag-radio" activeClass="active" [value]="l">{{ l.showValue }}</app-radio>
        }
      </app-radio-group>
    </div>
    <div class="flex items-center">
      <label [class]="labelClass">里程：</label>
      <nz-select
        class="w-50"
        [nzPlaceHolder]="type() === 'route-price' ? '全部' : '请选择'"
        [(ngModel)]="intal"
        (ngModelChange)="getChartData()"
      >
        @for (item of intal_list(); track item) {
          <nz-option [nzLabel]="item.showValue" [nzValue]="item"></nz-option>
        }
      </nz-select>
    </div>
  </div>
  <div class="flex flex-col h-160 shadow-md rounded-sm border border-neutral-100">
    <div class="flex flex-col gap-2 px-4 pt-4">
      <div class="flex items-center gap-6">
        <div class="flex items-center">
          <label class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap">
            嘀嗒价格指标：
          </label>
          <app-radio-group class="relative flex gap-1" [(ngModel)]="dida_price" (ngModelChange)="getChartData()">
            @for (l of dida_price_list(); track l) {
              <ng-template #metricsTitleTemplate>
                {{ l?.aliasName | replace: priceService.competitionName() }}
              </ng-template>
              <ng-template #metricsContentTemplate>
                <div
                  class="leading-normal"
                  [innerHTML]="l?.bizExpression | replace: priceService.competitionName()"
                ></div>
              </ng-template>
              <app-radio
                nz-popover
                [nzPopoverMouseEnterDelay]="0.5"
                [nzPopoverTitle]="metricsTitleTemplate"
                [nzPopoverContent]="metricsContentTemplate"
                class="tag-radio"
                activeClass="active"
                [value]="l.extendName"
              >
                {{ l.aliasName | replace: priceService.competitionName() }}
              </app-radio>
            }
          </app-radio-group>
          <!-- <nz-checkbox-group [(ngModel)]="dida_price" (ngModelChange)="getChartData()">
            @for (l of dida_price_list(); track l) {
              <ng-template #metricsTitleTemplate>
                {{ l?.aliasName | replace: priceService.competitionName() }}
              </ng-template>
              <ng-template #metricsContentTemplate>
                <div
                  class="leading-normal"
                  [innerHTML]="l?.bizExpression | replace: priceService.competitionName()"
                ></div>
              </ng-template>
              <label
                nz-popover
                [nzPopoverMouseEnterDelay]="0.5"
                [nzPopoverTitle]="metricsTitleTemplate"
                [nzPopoverContent]="metricsContentTemplate"
                nz-checkbox
                class="text-xs! ml-0!"
                [nzValue]="l.extendName"
              >
                {{ l.aliasName | replace: priceService.competitionName() }}
              </label>
            }
          </nz-checkbox-group> -->
        </div>
        @if (type() === 'route-price') {
          <div class="flex items-center">
            <label class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap">
              是否展示价格参照：
            </label>
            <nz-switch
              [(ngModel)]="dida_benchmark_book_ord_amt"
              nzSize="small"
              (ngModelChange)="getChartData()"
            ></nz-switch>
            <span class="text-xs text-neutral-400 px-4">
              说明：开启后将展示
              <span class="text-red-500">嘀嗒24年3月份</span>
              的
              <span class="text-red-500">每公里均价</span>
              ，作为
              <span class="text-red-500">基准价</span>
            </span>
          </div>
        }
      </div>
      <div class="flex items-center">
        <label class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap">
          价格对比指标：
        </label>
        <app-radio-group class="relative flex gap-1" [(ngModel)]="compare_price" (ngModelChange)="getChartData()">
          @for (l of compare_price_list(); track l) {
            <ng-template #metricsTitleTemplate2>
              {{ l?.aliasName | replace: priceService.competitionName() }}
            </ng-template>
            <ng-template #metricsContentTemplate2>
              <div
                class="leading-normal"
                [innerHTML]="l?.bizExpression | replace: priceService.competitionName()"
              ></div>
            </ng-template>
            <app-radio
              nz-popover
              [nzPopoverMouseEnterDelay]="0.5"
              [nzPopoverTitle]="metricsTitleTemplate2"
              [nzPopoverContent]="metricsContentTemplate2"
              class="tag-radio"
              activeClass="active"
              [value]="l.extendName"
            >
              {{ l.aliasName | replace: priceService.competitionName() }}
            </app-radio>
          }
        </app-radio-group>
        <!-- <nz-checkbox-group [(ngModel)]="compare_price" (ngModelChange)="getChartData()">
          @for (l of compare_price_list(); track l) {
            <ng-template #metricsTitleTemplate2>
              {{ l?.aliasName | replace: priceService.competitionName() }}
            </ng-template>
            <ng-template #metricsContentTemplate2>
              <div
                class="leading-normal"
                [innerHTML]="l?.bizExpression | replace: priceService.competitionName()"
              ></div>
            </ng-template>
            <label
              nz-popover
              [nzPopoverMouseEnterDelay]="0.5"
              [nzPopoverTitle]="metricsTitleTemplate2"
              [nzPopoverContent]="metricsContentTemplate2"
              nz-checkbox
              class="text-xs! ml-0!"
              [nzValue]="l.extendName"
            >
              {{ l.aliasName | replace: priceService.competitionName() }}
            </label>
          }
        </nz-checkbox-group> -->
      </div>
    </div>

    <div class="flex-1 min-h-0 relative flex items-center justify-center text-xs">
      @if (loading()) {
        <app-line-spin />
      } @else {
        @if (chartValue()) {
          <div class="absolute inset-2">
            <!-- <app-graph [options]="option()" showAvgPlotLines showAnnotations [multiple]="1" /> -->
            <app-grid-multiple [value]="chartValue()" />
          </div>
        } @else if (errorMessage()) {
          <span>{{ errorMessage() }}</span>
        } @else {
          <span></span>
        }
      }
    </div>
  </div>
</div>
