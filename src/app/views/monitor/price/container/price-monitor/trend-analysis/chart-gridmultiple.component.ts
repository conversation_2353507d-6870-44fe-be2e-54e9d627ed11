import { ChangeDetectionStrategy, Component, computed, input } from '@angular/core'
import { ChartComponent } from '@shared/components/chart'
import { sortBy, isNumber, dropRight } from 'lodash'

type LabelPosition =
  /** 漏斗图左侧，orient 为'vertical'时有效。 */
  | 'left'
  /** 漏斗图右侧，orient 为'vertical'时有效。 */
  | 'right'
  /** 漏斗图上侧，orient 为'horizontal'时有效。 */
  | 'top'
  /** 漏斗图下侧，orient 为'horizontal'时有效。 */
  | 'bottom'
  /** 漏斗图梯形内部。 */
  | 'inside'
  /** 漏斗图梯形外部。 */
  | 'outside'
  /** 漏斗图梯形内部右侧。 */
  | 'insideRight'
  /** 漏斗图梯形内部左侧。 */
  | 'insideLeft'
  /** 漏斗图左侧上部。 */
  | 'leftTop'
  /** 漏斗图左侧下部。 */
  | 'leftBottom'
  /** 漏斗图右侧上部。 */
  | 'rightTop'
  /** 漏斗图右侧下部。 */
  | 'rightBottom'
  /** 同 'inside'。 */
  | 'inner'
  /** 同 'inside'。 */
  | 'center'

class SeriesLabel {
  show = true
  fontSize = '12px'
  color = '#000'
  position: LabelPosition
}

interface SeriesProperties {
  data: Array<{ value: number; name: string; label: SeriesLabel }>
  position: 'inside' | 'outside' | string
}

interface FormatterParams {
  componentType: 'series'
  // 系列类型
  seriesType: string
  // 系列在传入的 option.series 中的 index
  seriesIndex: number
  // 系列名称
  seriesName: string
  // 数据名，类目名
  name: string
  // 数据在传入的 data 数组中的 index
  dataIndex: number
  // 传入的原始数据项
  data: Object
  // 传入的数据值。在多数系列下它和 data 相同。在一些系列下是 data 中的分量（如 map、radar 中）
  value: number
  // 坐标轴 encode 映射信息，
  // key 为坐标轴（如 'x' 'y' 'radius' 'angle' 等）
  // value 必然为数组，不会为 null/undefined，表示 dimension index 。
  // 其内容如：
  // {
  //     x: [2] // dimension index 为 2 的数据映射到 x 轴
  //     y: [0] // dimension index 为 0 的数据映射到 y 轴
  // }
  encode: Object
  // 维度名列表
  dimensionNames: Array<String>
  // 数据的维度 index，如 0 或 1 或 2 ...
  // 仅在雷达图中使用。
  dimensionIndex: number
  // 数据图形的颜色
  color: string
  // 百分比
  percent: number
}

class Series {
  top = 'middle'
  height = '80%'
  maxSize = '100%'
  sort = 'none'
  orient = 'vertical'
  type: any
  label = {
    show: true,
    fontSize: '12px',
    color: '#000',
    position: 'outside',
    // verticalAlign: 'top'
  } as any

  labelLine = { show: true }
  data: any

  constructor({ data }: SeriesProperties) {
    this.data = data
  }
}

class GridMultiple {
  tooltip = {
    trigger: 'axis',
    axisPointer: {
      animation: false,
    },
    formatter: params => {
      const arr = sortBy(params, [
        function (m) {
          return m.seriesName
        },
      ])
      const result = []
      result.push('<table class="text-sm">')
      arr.forEach((params, index) => {
        if (index === 0) {
          result.push(`
               <tr>
                 <span class="text-[14px]">
                   ${params.name}
                 </span>
               </tr>
               <thead>
                  <tr class="border-b border-gray-200 py-2">
                   <td>指标</td>
                   <td class="px-5">当前期</td>
                   <td class="text-right">日环比</td>
                   <td class="px-5 text-right">周同比</td>
                 </tr>
               </thead>
             `)
        }
        result.push(`
             <tr>
               <td class="pr-2">
                 ${params.seriesName}:
               </td>

               <td class="text-right px-5">
                 ${
                   params.data.unit === '%'
                     ? params.data.value
                       ? params.data.value + '%'
                       : '-'
                     : Number.isFinite(params.data.value)
                       ? Intl.NumberFormat().format(params.data.value)
                       : '-'
                 }
               </td>
               <td class="text-right ${params.data.dt.diff < 0 ? 'text-green-400' : params.data.dt.diff > 0 ? 'text-red-400' : ''}">
                 ${
                   params.data.dt
                     ? params.data.unit === '%'
                       ? params.data.dt.diff
                         ? `${params.data.dt.diff}pp`
                         : '--'
                       : Number.isFinite(params.data.dt.diff)
                         ? `${params.data.dt.diff}(${params.data.dt.ratio}%)`
                         : '--'
                     : ''
                 }
                </td>
                <td class="text-right ${params.data.yw.diff < 0 ? 'text-green-400' : params.data.yw.diff > 0 ? 'text-red-400' : ''}">
                 ${
                   params.data.yw
                     ? params.data.unit === '%'
                       ? params.data.yw.diff
                         ? `${params.data.yw.diff}pp`
                         : '--'
                       : Number.isFinite(params.data.yw.diff)
                         ? `${params.data.yw.diff}(${params.data.yw.ratio}%)`
                         : '--'
                     : ''
                 }
                </td>
             </tr>
           `)
      })
      result.push('</table>')

      return result.join('')
    },
  }

  xAxis = []

  yAxis = [
    {
      type: 'value',
    },
    {
      gridIndex: 1,
      type: 'value',
    },
  ]

  grid = [
    {
      left: 60,
      right: 50,
      height: '35%',
    },
    {
      left: 60,
      right: 50,
      top: '55%',
      height: '35%',
    },
  ]

  legend = {
    data: [],
  }

  axisPointer = {
    link: [
      {
        xAxisIndex: 'all',
      },
    ],
  }

  series: Series[]

  setCategories(value: any) {
    this.legend.data = value.map(v => {
      return {
        name: v.data,
        icon: v.type === 'bar' ? 'circle' : '',
        itemStyle: {
          color: v.color,
        },
      }
    })
  }

  setXAxis(data) {
    this.xAxis = [
      {
        type: 'category',
        axisLine: { onZero: true },
        data,
        position: 'top',
        show: false,
      },
      {
        gridIndex: 1,
        type: 'category',
        axisLine: { onZero: true },
        data,
      },
    ]
  }

  setValue(values: any[]) {
    this.series = values
  }

  getOption() {
    // return JSON.parse(JSON.stringify(this));
    return this
  }
}

@Component({
  selector: 'app-grid-multiple',
  template: `
    <app-charts [options]="option()" />
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [ChartComponent],
})
export class GridMultipleComponent {
  value = input<any>({})
  showRheaLine = input<boolean>(false)

  option = computed(() => {
    const headers = this.value().headers
    const headersArr = Object.keys(headers).filter(h => h !== 'dt')
    const data = this.value().data
    const color = [
      'rgba(80,135,236,0.9)',
      'rgba(238,117,47,0.9)',
      'rgba(242,189,66,0.9)',
      '#91cc75',
      'rgba(127,131,247,0.9)',
    ]
    const arr = headersArr
      .map((h, i) => {
        const header = headers[h]
        if (!header?.aliasName) {
          console.warn(`Missing aliasName for header: ${h}`)
          return null
        }

        // 提取和处理别名名称
        const { processedAliasName, type } = this.processAliasName(header.aliasName)

        // 处理用户自定义扩展名称
        const baseName = this.extractBaseName(processedAliasName)

        // 生成最终名称
        const name = baseName.replace('<', '&lt;').replace('DD', type)

        if (headers[h].dataUnit === '%') {
          return {
            name,
            type: 'bar',
            color: '#D87B84',
            markLine: {
              label: { show: false },
              symbol: ['none', 'none'],
              data: [
                {
                  name: '水平线',
                  yAxis: 0,
                  lineStyle: {
                    color: 'red',
                    width: 3,
                  },
                },
              ],
              silent: true,
            },
            data: data.map(d => {
              return {
                value: (Number(d[h]) * 100).toFixed(2),
                dt: {
                  diff: (+Number(d[`${h}:dt_DIFF`]) * 100).toFixed(2),
                },
                yw: {
                  diff: (+Number(d[`${h}:yw_DIFF`]) * 100).toFixed(2),
                },
                itemStyle: {
                  color: Number(d[h]) < 0 ? '#5FB2BC' : '#D87B84',
                },
                unit: headers[h].dataUnit,
              }
            }),
          }
        } else {
          return {
            name,
            type: 'line',
            xAxisIndex: 1,
            yAxisIndex: 1,
            symbolSize: 2,
            color: color[i],
            data: data.map(d => {
              return {
                dt: {
                  diff: +Number(d[`${h}:dt_DIFF`]),
                  ratio: +Number(d[`${h}:dt_DIFF_RATIO`]),
                },
                yw: {
                  diff: +Number(d[`${h}:yw_DIFF`]),
                  ratio: +Number(d[`${h}:yw_DIFF_RATIO`]),
                },
                value: Number(d[h]),
                unit: headers[h].dataUnit,
              }
            }),
          }
        }
      })
      .filter(Boolean) // 过滤掉null值

    const series = arr

    const options = new GridMultiple()

    options.setCategories(
      sortBy(arr, [
        function (m) {
          return m.name
        },
      ]).map(item => ({
        data: item.name,
        color: item.color,
        type: item.type,
      }))
    )
    options.setValue(series)
    options.setXAxis(data.map(d => d.dt))
    return options.getOption()
  })

  /**
   * 处理别名名称，提取类型信息
   * @param aliasName 原始别名名称
   * @returns 处理后的别名名称和类型
   */
  private processAliasName(aliasName: string): { processedAliasName: string; type: string } {
    if (!aliasName) {
      return { processedAliasName: '', type: '' }
    }

    const parts = aliasName.split('-')
    let type = parts[parts.length - 1]
    let processedAliasName = aliasName

    // 如果最后一部分是数字，则移除它并重新获取类型
    if (isNumber(Number(type))) {
      processedAliasName = dropRight(parts).join('-')
      const newParts = processedAliasName.split('-')
      type = newParts[newParts.length - 1] || ''
    }

    return { processedAliasName, type }
  }

  /**
   * 从处理后的别名名称中提取基础名称
   * @param aliasName 处理后的别名名称
   * @returns 基础名称
   */
  private extractBaseName(aliasName: string): string {
    if (!aliasName) {
      return ''
    }

    const userDefParts = aliasName.split('_userDefExtendName')
    const lastPart = userDefParts[userDefParts.length - 1]

    if (!lastPart) {
      return ''
    }

    const dashParts = lastPart.split('-')
    return dashParts.slice(0, -1).join('-')
  }
}
