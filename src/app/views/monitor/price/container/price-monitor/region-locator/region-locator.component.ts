import { FormsModule } from '@angular/forms'
import { ChangeDetectionStrategy, Component, DestroyRef, inject, signal } from '@angular/core'
import { takeUntilDestroyed } from '@angular/core/rxjs-interop'
import { ActivatedRoute } from '@angular/router'

import { RadioModule } from '@shared/modules/headless'
import { IconMapArrowRightComponent } from '@shared/modules/icons'
import { ReferencePriceComponent } from './reference-price/reference-price.component'
import { AlertMonitorComponent } from './alert-monitor/alert-monitor.component'
import { RoutePriceComponent } from './route-price/route-price.component'

@Component({
  selector: 'app-region-locator',
  template: `
    <header class="relative flex items-center p-5 px-6">
      <span class="flex items-center gap-x-1.5 font-black text-base">
        <MapArrowRightIcon />
        区域定位
      </span>

      <app-radio-group
        [(ngModel)]="type"
        class="absolute inset-1/2 top-1/2 -translate-y-1/2 -translate-x-1/2 w-96 h-8 flex items-start gap-x-0.5 p-0.5 bg-[#ebebec] rounded-lg"
      >
        <app-radio class="dida-radio-new" activeClass="active" value="route-price">路线价格</app-radio>
        <app-radio class="dida-radio-new" activeClass="active" value="reference-price">最新刊例价</app-radio>
        <app-radio class="dida-radio-new" activeClass="active" value="alert-monitor">告警信息</app-radio>
        <app-radio-thumb [offsetX]="2" class="bg-linear-to-r from-[#465867] to-[#1E2C3A] rounded-md" />
      </app-radio-group>
    </header>

    @switch (type()) {
      @case ('route-price') {
        <app-route-price />
      }
      @case ('reference-price') {
        <app-reference-price />
      }
      @case ('alert-monitor') {
        <app-alert-monitor />
      }
    }
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    FormsModule,
    RadioModule,
    RoutePriceComponent,
    ReferencePriceComponent,
    IconMapArrowRightComponent,
    AlertMonitorComponent,
  ],
})
export class RegionLocatorComponent {
  destroyRef = inject(DestroyRef)
  type = signal<'route-price' | 'reference-price' | 'alert-monitor'>('route-price')

  constructor(private route: ActivatedRoute) {
    this.route.queryParamMap.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(paramMap => {
      if (paramMap.get('monitorId') !== null) {
        this.type.set('alert-monitor')
      }
    })
  }
}
