import { DragDropModule } from '@angular/cdk/drag-drop';
import { DecimalPipe, NgTemplateOutlet } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, Component, computed, inject, input, signal } from '@angular/core';
import { FormGroup } from '@angular/forms';
import { finalize } from 'rxjs';

import { CaerusApiService } from '@api/caerus';
import { DimensionValueMenuVo } from '@api/caerus/model';
import { QueryEngineApiService } from '@api/query-engine';
import { MetricVo, QueryOutputVo } from '@api/query-engine/model';
import { BaseHighCharts } from '@common/chart/highcharts';
import { QueryEngineFormService } from '@common/service/query-engine';
import { groupBy, sortCategoriesFn, toDecimals } from '@common/function';
import { SwitchMap } from '@common/decorator';
import { GraphComponent } from '@shared/components/graph';
import { DropdownDirective } from '@shared/directives/dropdown';
import { ValueFormatter } from '@shared/components/value-formatter';
import { LineSpinComponent } from '@shared/components/line-spin';
import { IconCloseComponent } from '@shared/modules/icons';
import { FilterItem } from '@views/monitor/price/lib';

import GraphColumn from './graph-column';


@Component({
  selector: 'app-online-price-per-km',
  template: `
    <div cdkDrag class="relative pointer-events-auto w-150 h-96 bg-white rounded-sm border border-neutral-200 shadow-1">
      <div class="flex flex-col h-full">
        <header cdkDragHandle class="header">
          <span>{{ title() }}</span>
          <CloseIcon iconBtn class="ml-auto" (click)="close()" />
        </header>

        @if (loading()) {
          <div class="flex flex-1 min-h-0">
            <app-line-spin class="m-auto scale-90" />
          </div>
        } @else {
          <div class="flex-1 min-h-0 px-2.5">
            <div class="relative h-full">
              @if (option()) {
                <app-graph [options]="option()" />
              }
            </div>
          </div>

          <div>
            <ng-template #row_1 let-value let-index="cols">
              <td>{{ value }}</td>
            </ng-template>

            <ng-template #row_2 let-value let-index="cols">
              @if (index !== 0) {
                <td>
                  <value-formatter [value]="value" suffix="%" />
                </td>
              } @else {
                <td>{{ value }}</td>
              }
            </ng-template>

            <ng-template #row_3 let-value let-index="cols">
              @if (index !== 0) {
                <td>
                  <value-formatter [value]="value" />
                </td>
              } @else {
                <td>{{ value }}</td>
              }
            </ng-template>

            <ng-template #row_4 let-value let-index="cols">
              @if (index !== 0) {
                <td>
                  <value-formatter useColor [value]="value" />
                </td>
              } @else {
                <td>{{ value }}</td>
              }
            </ng-template>

            <ng-template #row_5 let-value let-index="cols">
              @if (index !== 0) {
                <td>
                  <value-formatter useColor [value]="value" />
                </td>
              } @else {
                <td>{{ value }}</td>
              }
            </ng-template>

            <ng-template #cellTemplate let-value let-index="lines" let-cols="cols">
              @switch (index) {
                @case (0) { <ng-template *ngTemplateOutlet="row_1; context: { $implicit: value, cols }"></ng-template> }
                @case (1) { <ng-template *ngTemplateOutlet="row_2; context: { $implicit: value, cols }"></ng-template> }
                @case (2) { <ng-template *ngTemplateOutlet="row_3; context: { $implicit: value, cols }"></ng-template> }
                @case (3) { <ng-template *ngTemplateOutlet="row_4; context: { $implicit: value, cols }"></ng-template> }
                @case (4) { <ng-template *ngTemplateOutlet="row_5; context: { $implicit: value, cols }"></ng-template> }
              }
            </ng-template>

            <div class="p-2.5">
              @if (listOfRows()) {
                <table>
                  <tbody>
                    @for (items of listOfRows(); track $index; let rowIndex = $index) {
                      <tr>
                        @for (item of items; track item; let columnIndex = $index) {
                          <ng-template *ngTemplateOutlet="cellTemplate; context: { $implicit: item, lines: rowIndex, cols: columnIndex }"></ng-template>
                        }
                      </tr>
                    }
                  </tbody>
                </table>
              }
            </div>
          </div>
        }
      </div>
    </div>
  `,
  styleUrl: './online-price-per-km.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgTemplateOutlet, DragDropModule, IconCloseComponent, ValueFormatter, LineSpinComponent, GraphComponent],
  providers: [DecimalPipe],
})
export class OnlinePricePerKmComponent implements AfterViewInit {

  formService = inject(QueryEngineFormService);
  dropdownRef = inject(DropdownDirective, { optional: true });
  caerusApiService = inject(CaerusApiService);
  apiService = inject(QueryEngineApiService);
  decimalPipe = inject(DecimalPipe);

  data = input.required<{
    orderType: DimensionValueMenuVo;
    priceType: DimensionValueMenuVo;
    cityRegion: string;
  }>();

  #cityRegions = signal<DimensionValueMenuVo[]>([]);
  #metrics = signal<MetricVo[]>([
    {
      extendName: 'dida_mean_samp_actprice_amt',
      aliasName: '嘀嗒基础刊例价-每公里均价',
      tagName: 'price',
    },
    {
      extendName: 'dida_benchmark_book_ord_amt',
      aliasName: '嘀嗒基础刊例价-每公里均价（基准期）',
      tagName: 'price',
    },
    {
      extendName: 'own_c_book_ord_cnt',
      aliasName: '下单量',
      tagName: 'dau',
    },
    {
      extendName: 'own_c_book_ord_cnt',
      aliasName: '下单量',
      customType: 'proportion',
      proportionDimension: [],
      tagName: 'dau',
    },
  ]);

  loading = signal(true);
  listOfRows = signal<Array<Array<string | number>>>(null);
  option = signal<BaseHighCharts>(null);
  title = computed(() => {
    if (this.data()) {
      const { cityRegion } = this.data();
      const [region] = this.#cityRegions().filter(item => item.value === cityRegion);
      return `嘀嗒线上价格-每公里均价-${region?.value}`;
    }
    return null;
  });

  ngAfterViewInit(): void {
    this._fetchDimensionConfig(() => {
      this.query();
    });
  }

  private _fetchDimensionConfig(callback: () => void) {
    this.caerusApiService.fetchDimensionConfig('price_monitor_dimension').subscribe(res => {
      if (res.status !== '00000') {
        return console.error(res.message || res.error);
      }

      const city_regions = res.data.price_save.find(item => item.keyName === 'price_city_region');
      this.#cityRegions.set(city_regions.values);
      callback();
    });
  }

  close() {
    this.dropdownRef.close();
  }

  private _orderTypeFilter() {
    const { orderType } = this.data();
    const { value } = new FormGroup(
      new FilterItem({
        conditionType: 2,
        condition: 'in',
        extendName: orderType.extendName,
        value: [
          {
            key: orderType.key,
            value: orderType.value,
          },
        ],
      })
    );

    return value;
  }

  private _priceTypeFilter() {
    const { priceType } = this.data();
    const { value } = new FormGroup(
      new FilterItem({
        conditionType: 2,
        condition: 'in',
        extendName: priceType.extendName,
        value: [
          {
            key: priceType.key,
            value: priceType.value,
          },
        ],
      })
    );

    return value;
  }

  private _cityRegionFilter() {
    const { cityRegion } = this.data();
    const [region] = this.#cityRegions().filter(item => item.value === cityRegion);
    const { value } = new FormGroup(
      new FilterItem({
        conditionType: 2,
        condition: 'in',
        extendName: 'city_region',
        value: [
          {
            key: region?.key,
            value: region?.value,
          },
        ],
      })
    );

    return value;
  }

  private get miageExtendName() {
    const {
      orderType: { showValue },
    } = this.data();

    return showValue === '城际' ? 'inter_miage_intal' : 'inner_miage_intal';
  }

  private _dimensionItems() {
    return [
      {
        id: null,
        extendName: this.miageExtendName,
        predefineCompareType: null,
      },
    ];
  }

  private _metricsItem() {
    return this.#metrics().map(item => {
      const items = item.tagName === 'price' ? [this._priceTypeFilter(), this._orderTypeFilter()] : item.tagName === 'dau' ? [this._orderTypeFilter()] : [];

      return {
        ...item,
        userDefExtendName: item.extendName,
        filter: {
          items: items.flat(1),
        },
      };
    });
  }

  @SwitchMap()
  query() {
    const body = this.formService.value();

    body.metrics = this._metricsItem();
    body.dimensions = this._dimensionItems();
    body.filter.items = [this._cityRegionFilter()];
    body.scene = 7;
    body.dataFillConfig = {
      open: 1,
      fillRangeType: 1,
      fillValue: 1,
    };

    body.userDefExpressions = [
      {
        expression: 'dida_mean_samp_actprice_amt-dida_benchmark_book_ord_amt',
        userDefAliasName: '嘀嗒线上价格-每公里均价（较基期）',
        userDefExtendName: 'A1',
      },
    ];

    this.loading.set(true);
    return this.apiService
      .search(body)
      .pipe(finalize(() => this.loading.set(false)))
      .subscribe(res => {
        if (res.status !== '00000') {
          return console.error(res.message || res.error);
        }
        this.#renderGraph(res.data);
        this.#renderTable(res.data);
      });
  }

  #renderGraph({ data, compareData }: QueryOutputVo) {
    const obj = groupBy(data, this.miageExtendName);
    const compareObj = groupBy(compareData, this.miageExtendName);
    const keys = Object.keys(obj).sort(sortCategoriesFn);
    const chart = new GraphColumn();
    const series = [
      { name: '本期数值', data: [] },
      // { name: '较基准期数值（2024-3月份）', data: [] },
      // { name: '对比期数值', data: [] },
    ];

    Object.keys(obj)
      .sort(sortCategoriesFn)
      .filter(key => !!key)
      .forEach(key => {
        const [value] = obj[key];

        const val_1 = value['dida_mean_samp_actprice_amt'];
        // const val_2 = value['dida_benchmark_book_ord_amt']

        series[0].data.push(val_1 === null ? null : toDecimals(val_1, 1));
        // series[1].data.push(val_2 === null ? null : toDecimals(val_2, 1))

        // if (this.formService.hasDateCompare()) {
        //   const [compareValue] = compareObj[key]
        //   const val_3 = compareValue?.dida_mean_samp_actprice_amt

        //   series[2].data.push(val_3 === null ? null : toDecimals(val_3, 1))
        // }
      });

    chart.setSeries(series);
    chart.setCategories(keys);
    this.option.set(chart.getOption());
  }

  #renderTable({ data, compareData }: QueryOutputVo) {
    const obj = groupBy(data, this.miageExtendName);
    const compareObj = groupBy(compareData, this.miageExtendName);
    const rows = [['公里段'], ['下单订单数量占比'], ['嘀嗒线上价格-每公里均价'], ['较基准期数值（2024-3月份）']];

    if (this.formService.hasDateCompare()) {
      rows.push(['较对比期数值']);
    }

    Object.keys(obj)
      .sort(sortCategoriesFn)
      .filter(key => !!key)
      .forEach(key => {
        const [value] = obj[key];

        rows[0].push(value[this.miageExtendName]);
        rows[1].push(value['own_c_book_ord_cnt:proportion']);
        rows[2].push(value['dida_mean_samp_actprice_amt']);
        rows[3].push(value['A1']);

        if (this.formService.hasDateCompare()) {
          // const [compareValue] = compareObj[key]
          rows[4].push(value?.dida_mean_samp_actprice_amt_DIFF);
        }
      });

    this.listOfRows.set(rows);
  }
}
