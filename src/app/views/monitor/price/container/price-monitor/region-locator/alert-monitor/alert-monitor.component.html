<div class="relative">
  <div class="flex flex-col items-start gap-y-3 py-5 px-2">
    <div class="flex items-center gap-x-10">
      <div class="flex items-center">
        <label class="inline-flex items-center font-bold leading-0 whitespace-nowrap">监控类型：</label>
        <app-radio-group class="relative flex gap-1" [(ngModel)]="current_type" (ngModelChange)="changeCurrentType()">
          <app-radio class="tag-radio" activeClass="active" value="default">默认监控</app-radio>
          <app-radio class="tag-radio" activeClass="active" value="customize">自定义监控</app-radio>
        </app-radio-group>
      </div>

      <div class="flex items-center">
        <label class="inline-flex items-center font-bold leading-0 whitespace-nowrap">监控名称：</label>
        @if (current_type() === 'default') {
          <app-radio-group
            class="relative flex gap-1"
            [(ngModel)]="monitor_project"
            (ngModelChange)="changeProject($event)"
          >
            @for (l of default_monitor_list(); track l) {
              <app-radio
                nz-popover
                [nzPopoverMouseEnterDelay]="0.5"
                [nzPopoverTitle]="l.monitorName"
                [nzPopoverContent]="l.description"
                class="tag-radio"
                activeClass="active"
                [value]="l"
              >
                {{ l.monitorName }}
              </app-radio>
            }
          </app-radio-group>
        } @else {
          <nz-select
            [(ngModel)]="monitor_project"
            nzShowSearch
            style="width: 300px"
            (ngModelChange)="changeProject($event)"
          >
            @for (data of treeData(); track data) {
              <nz-option-group [nzLabel]="data.name">
                @for (list of data.list; track list) {
                  <nz-option nzCustomContent [nzValue]="list" [nzLabel]="list.monitorName">
                    <span nz-tooltip [nzTooltipTitle]="list.description" nzTooltipPlacement="topLeft">
                      {{ list.monitorName }}
                    </span>
                  </nz-option>
                }
              </nz-option-group>
            }
          </nz-select>
        }
      </div>

      @if (monitor_type() === 3) {
        <div class="flex items-center">
          <label class="inline-flex items-center font-bold leading-0 whitespace-nowrap">监控城市：</label>
          <app-city-picker placeholder="全部" multiple [(ngModel)]="city" (ngModelChange)="getTableData()" />
        </div>
      }
    </div>

    @if (monitor_type() !== 3) {
      <div class="flex items-center gap-x-10">
        <div class="flex items-center">
          <label class="inline-flex items-center font-bold leading-0 whitespace-nowrap">监控城市：</label>
          <app-city-picker
            placeholder="全部"
            multiple
            [useReferencePrice]="monitor_type() === 2"
            [(ngModel)]="city"
            (ngModelChange)="getTableData()"
          />
        </div>

        <div class="flex items-center">
          <label class="inline-flex items-center font-bold leading-0 whitespace-nowrap">价格类型：</label>
          <app-radio-group [(ngModel)]="price_type" class="relative flex gap-1" (ngModelChange)="getTableData()">
            <app-radio class="tag-radio" activeClass="active" [value]="null">全部</app-radio>
            @for (item of priceTypeOptions(); track $index) {
              <app-radio class="tag-radio" activeClass="active" [value]="item">{{ item.showValue }}</app-radio>
            }
          </app-radio-group>
        </div>

        <div class="flex items-center">
          <label class="inline-flex items-center font-bold leading-0 whitespace-nowrap">订单类型：</label>
          <app-radio-group class="relative flex gap-1" [(ngModel)]="c_ord_type" (ngModelChange)="changeOrdType()">
            <app-radio class="tag-radio" activeClass="active" value="0">市内</app-radio>
            @if (monitor_type() !== 2) {
              <app-radio class="tag-radio" activeClass="active" value="1">城际</app-radio>
            }
          </app-radio-group>
        </div>

        <div class="flex items-center">
          <label class="inline-flex items-center font-bold leading-0 whitespace-nowrap">公里段：</label>
          <nz-select
            nzMode="multiple"
            nzPlaceHolder="请选择"
            class="w-50"
            [(ngModel)]="intal"
            (ngModelChange)="getTableData()"
          >
            <nz-option nzLabel="全部" [nzValue]="null"></nz-option>
            @for (item of intal_list(); track item) {
              <nz-option [nzLabel]="item.showValue" [nzValue]="item"></nz-option>
            }
          </nz-select>
        </div>
      </div>
    }
  </div>
  <nz-table #rowSelectionTable nzBordered nzOuterBordered [nzLoading]="loading()" nzShowPagination [nzData]="tbData()">
    <thead>
      @for (c of column(); track c) {
        <tr>
          @for (item of c; track item) {
            @if (item.popover) {
              <ng-template #metricsTitleTemplate>
                {{ item.name }}
              </ng-template>
              <ng-template #metricsContentTemplate>
                <div class="leading-normal" [innerHTML]="item.bizExpression"></div>
              </ng-template>
              <th
                [rowSpan]="item.rowspan"
                [nzSortOrder]="null"
                [nzSortFn]="item.nzSortFn"
                [nzSortDirections]="item.nzSortDirections ?? [null]"
                [colSpan]="item.colspan"
                [class.!text-right]="item.nzSortDirections"
                [class]="item.color"
                nz-popover
                [nzCustomFilter]="item.nzSortDirections"
                [nzPopoverMouseEnterDelay]="0.5"
                [nzPopoverTitle]="metricsTitleTemplate"
                [nzPopoverContent]="metricsContentTemplate"
                [class.!text-center]="!item.nzSortDirections"
              >
                {{ item.name }}
                @if (item.nzSortDirections) {
                  <ng-container>
                    <nz-filter-trigger #target [nzActive]="getDiffValue(item.extendName)" [nzDropdownMenu]="menu">
                      <nz-icon nzType="filter" nzTheme="fill" />
                    </nz-filter-trigger>

                    <nz-dropdown-menu #menu="nzDropdownMenu">
                      <app-sub-filter
                        [title]="item.name"
                        [value]="getDiffValue(item.extendName)"
                        (valueChange)="setDiffValue(item.extendName, $event); target.hide()"
                        [extendName]="item.extendName"
                        [suffix]="item.dataUnit"
                      />
                    </nz-dropdown-menu>
                  </ng-container>
                }
              </th>
            } @else {
              <th
                [class.!text-center]="!item.nzSortDirections"
                [rowSpan]="item.rowspan"
                [nzSortOrder]="null"
                [nzSortFn]="item.nzSortFn"
                [nzCustomFilter]="item.nzSortDirections"
                [nzSortDirections]="item.nzSortDirections ?? [null]"
                [colSpan]="item.colspan"
                [class.!text-right]="item.nzSortDirections"
                [class]="item.color"
              >
                {{ item.name }}
                @if (item.nzSortDirections) {
                  <ng-container>
                    <nz-filter-trigger
                      #target2
                      [nzActive]="getDiffValue(item.extendName + item.name)"
                      [nzDropdownMenu]="menu2"
                    >
                      <nz-icon nzType="filter" nzTheme="fill" />
                    </nz-filter-trigger>

                    <nz-dropdown-menu #menu2="nzDropdownMenu">
                      <app-sub-filter
                        [title]="item.name"
                        [value]="getDiffValue(item.extendName + item.name)"
                        (valueChange)="setDiffValue(item.extendName + item.name, $event); target2.hide()"
                        [extendName]="item.extendName"
                        [suffix]="item.dataUnit"
                      />
                    </nz-dropdown-menu>
                  </ng-container>
                }
              </th>
            }
          }
        </tr>
      }
    </thead>

    <tbody>
      @for (data of rowSelectionTable.data; track data) {
        <tr>
          <!-- <td
            [nzChecked]="checkedIds().includes(data.id)"
            [nzDisabled]="checkedCity().length > 1"
            (nzCheckedChange)="onItemChecked(data.id, data, $event)"
          ></td> -->
          @for (d of extendNames(); track d) {
            @if (d.dataUnit === '%' || d.dataUnit === 'pp') {
              <td [class.!text-right]="true" [class]="renderNumberText(data[d.extendName]).color">
                {{ renderNumberText(data[d.extendName], d.multiply100).text ?? '-' }}
                @if (renderNumberText(data[d.extendName], d.multiply100).text) {
                  {{ d.dataUnit }}
                }
              </td>
            } @else {
              @if ($index === 0) {
                <td
                  nzLeft
                  class="text-blue-400 cursor-pointer"
                  [class.!text-center]="true"
                  (click)="onItemChecked(data)"
                >
                  {{ data[d.extendName] }}
                </td>
              } @else {
                <td [class.!text-center]="!d.dataUnit" [class.text-right!]="d.dataUnit">
                  {{ data[d.extendName] }}
                </td>
              }
            }
          }
        </tr>
      }
    </tbody>
  </nz-table>

  <div class="absolute bottom-5 left-0 text-xs">
    <!-- @if (checkedCity().length !== 0) {
      <span class="pr-3">已选中:</span>
      @for (city of checkedCity(); track city) {
        <nz-tag nzMode="closeable" nzColor="blue" (nzOnClose)="closeTag(city)">
          {{ monitor_type() === 2 ? city.city_name : city.city_region }}
        </nz-tag>
      }
      <button nz-button nzType="primary" nzSize="small" class="text-xs! scale-90" (click)="compare()">一键对比</button>
      <span class="ml-3">说明：为避免选择城市过多，影响判断，对比的城市数量建议不超过2个</span>
    } -->
    <span class="ml-3">说明：点击起点城市名称，可查看对应城市的价格变化趋势。</span>
  </div>
</div>
