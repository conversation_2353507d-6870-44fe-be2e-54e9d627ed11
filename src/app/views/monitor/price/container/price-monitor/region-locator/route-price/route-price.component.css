@reference "../../../../../../../../styles.css";

:host::ng-deep .ant-table.ant-table-bordered > .ant-table-container > .ant-table-body > table > thead > tr:not(:last-child) > th, 
:host::ng-deep .ant-table.ant-table-bordered > .ant-table-container > .ant-table-content > table > thead > tr:not(:last-child) > th, 
:host::ng-deep .ant-table.ant-table-bordered > .ant-table-container > .ant-table-header > table > thead > tr:not(:last-child) > th, 
:host::ng-deep .ant-table.ant-table-bordered > .ant-table-container > .ant-table-summary > table > thead > tr:not(:last-child) > th,
:host::ng-deep .ant-table-tbody > tr > td,
:host::ng-deep .ant-table-thead > tr > th {
  @apply border-b-black/10;
}


:host::ng-deep .ant-table.ant-table-bordered > .ant-table-container > .ant-table-body > table > tbody > tr > td, 
:host::ng-deep .ant-table.ant-table-bordered > .ant-table-container > .ant-table-body > table > tfoot > tr > td, 
:host::ng-deep .ant-table.ant-table-bordered > .ant-table-container > .ant-table-body > table > tfoot > tr > th, 
:host::ng-deep .ant-table.ant-table-bordered > .ant-table-container > .ant-table-body > table > thead > tr > th, 
:host::ng-deep .ant-table.ant-table-bordered > .ant-table-container > .ant-table-content > table > tbody > tr > td, 
:host::ng-deep .ant-table.ant-table-bordered > .ant-table-container > .ant-table-content > table > tfoot > tr > td, 
:host::ng-deep .ant-table.ant-table-bordered > .ant-table-container > .ant-table-content > table > tfoot > tr > th, 
:host::ng-deep .ant-table.ant-table-bordered > .ant-table-container > .ant-table-content > table > thead > tr > th, 
:host::ng-deep .ant-table.ant-table-bordered > .ant-table-container > .ant-table-header > table > tbody > tr > td, 
:host::ng-deep .ant-table.ant-table-bordered > .ant-table-container > .ant-table-header > table > tfoot > tr > td, 
:host::ng-deep .ant-table.ant-table-bordered > .ant-table-container > .ant-table-header > table > tfoot > tr > th, 
:host::ng-deep .ant-table.ant-table-bordered > .ant-table-container > .ant-table-header > table > thead > tr > th, 
:host::ng-deep .ant-table.ant-table-bordered > .ant-table-container > .ant-table-summary > table > tbody > tr > td, 
:host::ng-deep .ant-table.ant-table-bordered > .ant-table-container > .ant-table-summary > table > tfoot > tr > td, 
:host::ng-deep .ant-table.ant-table-bordered > .ant-table-container > .ant-table-summary > table > tfoot > tr > th, 
:host::ng-deep .ant-table.ant-table-bordered > .ant-table-container > .ant-table-summary > table > thead > tr > th {
  @apply border-r-black/10;
}

