<div class="flex flex-col items-start gap-y-3 py-5 px-2">
  <div class="flex items-center justify-between w-full">
    <div class="flex items-center gap-x-10">
      <div class="flex items-center">
        <label class="inline-flex items-center font-bold leading-0 whitespace-nowrap">起点城市：</label>
        <app-city-picker multiple [(ngModel)]="cityRegion" placeholder="全部" />
      </div>

      <div class="flex items-center">
        <label class="inline-flex items-center font-bold leading-0 whitespace-nowrap">价格类型：</label>
        <app-radio-group [(ngModel)]="priceType" class="relative flex gap-1">
          @for (item of priceTypeOptions(); track $index) {
            <app-radio class="tag-radio" activeClass="active" [value]="item">{{ item.showValue }}</app-radio>
          }
        </app-radio-group>
      </div>

      <div class="flex items-center">
        <label class="inline-flex items-center font-bold leading-0 whitespace-nowrap">订单类型：</label>
        <app-radio-group [(ngModel)]="orderType" class="relative flex gap-1">
          @for (item of orderTypeOptions(); track $index) {
            <app-radio class="tag-radio" activeClass="active" [value]="item">{{ item.showValue }}</app-radio>
          }
        </app-radio-group>
      </div>

      <div class="flex items-center">
        <label class="inline-flex items-center font-bold leading-0 whitespace-nowrap">公里段：</label>
        <nz-select class="w-32" [(ngModel)]="miageIntal" nzAllowClear nzPlaceHolder="全部">
          @switch (orderType()?.value) {
            @case ('城际') {
              @for (item of interMiageOptions(); track $index) {
                <nz-option [nzValue]="item" [nzLabel]="item.showValue"></nz-option>
              }
            }
            @case ('市内') {
              @for (item of innerMiageOptions(); track $index) {
                <nz-option [nzValue]="item" [nzLabel]="item.showValue"></nz-option>
              }
            }
          }
        </nz-select>
      </div>
    </div>
    <div class="text-blue-400 cursor-pointer">下载明细数据</div>
  </div>
</div>

<ng-template #overlay_1_template let-data>
  <app-online-price-per-km [data]="data" />
</ng-template>

<ng-template #overlay_2_template let-data>
  <app-price-variance-avg [data]="data" (onLinkClick)="handleLinkClick($event, data.cityRegion)" />
</ng-template>

<p *debug class="mb-2 text-xs">{{ scrollConfig() | json }}</p>

<nz-table
  #basicTable
  nzBordered
  nzOuterBordered
  [nzData]="listOfData()"
  [nzPageSize]="10"
  [nzScroll]="scrollConfig()"
  [nzLoading]="loading()"
>
  <thead>
    <tr>
      <!-- <th nzLeft nzWidth="70px" rowSpan="2" class="text-center!">选择洞察目标</th> -->
      <th nzLeft nzWidth="100px" rowspan="2" class="text-center!">起点城市</th>

      @for (item of listOfColumnsLevel_1(); track $index) {
        <th
          [class]="item.className"
          [colSpan]="item.colSpan"
          nz-popover
          nzPopoverPlacement="top"
          [nzPopoverMouseEnterDelay]="0.5"
          [nzPopoverTitle]="merticsInfoMap.get(item.key)?.aliasName | replace: priceService.competitionName()"
          [nzPopoverContent]="metricsContent_Template"
        >
          <ng-template #metricsContent_Template>
            <div
              class="leading-normal"
              [innerHTML]="merticsInfoMap.get(item.key)?.bizExpression | replace: priceService.competitionName()"
            ></div>
          </ng-template>
          {{ merticsInfoMap.get(item.key)?.aliasName | replace: priceService.competitionName() }}
          @if (item.subTitle) {
            <span class="block text-xs opacity-60">{{ item.subTitle }}</span>
          }
        </th>
      }
    </tr>

    <tr>
      @for (item of listOfColumnsLevel_2(); track $index) {
        @if (item.visible) {
          <th
            [class]="item.className"
            [class.!text-right]="true"
            [nzCustomFilter]="item?.customFilter"
            [nzSortOrder]="null"
            [nzSortDirections]="['ascend', 'descend', null]"
            [nzSortFn]="item?.sortFn"
          >
            {{ item.name }}

            @if (item.filter) {
              <ng-container>
                <nz-filter-trigger #target [nzActive]="item.filter() !== null" [nzDropdownMenu]="menu">
                  <nz-icon nzType="filter" nzTheme="fill" />
                </nz-filter-trigger>
                <nz-dropdown-menu #menu="nzDropdownMenu">
                  @if (item.filter) {
                    <app-sub-filter
                      [title]="item.title | replace: priceService.competitionName()"
                      [(value)]="item.filter"
                      [suffix]="item.suffix"
                      (valueChange)="target.hide()"
                      [extendName]="item.key"
                    />
                  }
                </nz-dropdown-menu>
              </ng-container>
            }
          </th>
        }
      }
    </tr>
  </thead>
  <tbody>
    @let miage_intal_key = orderType()?.value === '城际' ? 'inter_miage_intal' : 'inner_miage_intal';
    @for (data of basicTable.data; track $index) {
      <tr>
        <!-- <td
          nzLeft
          align="center"
          [nzChecked]="setOfCheckedId.has(data.city_region)"
          [nzDisabled]="setOfCheckedIdsMaxLength() && !setOfCheckedId.has(data.city_region)"
          (nzCheckedChange)="onItemChecked(data.city_region, $event)"
        ></td> -->
        <td nzLeft class="text-blue-400 cursor-pointer" align="center" (click)="quickCompare([data.city_region], true)">
          {{ data.city_region }}
        </td>

        <!-- 嘀嗒与DD线上价格差异率均值-整单价格 -->
        <td align="right">
          <value-formatter
            useColor
            showPrefix
            linkTip="点击查看价格差异归因"
            [useLink]="data.city_price_top_finish_ord_weight === '3' && !showMiniGraph()"
            [value]="data.dida_conrtar_mean_samp_actprice_rate"
            suffix="%"
            (onLinkClick)="handleLinkClick(this.miageIntal()?.value, data.city_region)"
          />
        </td>
        @if (showMiniGraph()) {
          <td nz-tooltip nzTooltipTitle="点击可查看各公里段详情" nzTooltipPlacement="top">
            <div class="relative h-14 -m-4 hover:bg-primary/5 transition-colors">
              @if (data[`dida_conrtar_mean_samp_actprice_rate:split:${miage_intal_key}`]) {
                <app-graph-mini-column
                  isPercent
                  isTwotone
                  class="cursor-pointer"
                  [name]="'嘀嗒与DD线上实价差异率均值' | replace: priceService.competitionName()"
                  buriedPoint
                  buriedPointCode="dida_dpm_caerus_pricediff_route_click"
                  appDropdown
                  [dropdownMenu]="overlay_2_template"
                  [dropdownMenuOutsideClosable]="false"
                  [dropdownMenuContext]="{
                    $implicit: {
                      orderType: this.orderType(),
                      priceType: this.priceType(),
                      cityRegion: data.city_region,
                      canDiff: data.city_price_top_finish_ord_weight === '3',
                    },
                  }"
                  [values]="data[`dida_conrtar_mean_samp_actprice_rate:split:${miage_intal_key}`]"
                />
              } @else {
                <span class="absolute inset-0 flex items-center justify-center">-</span>
              }
            </div>
          </td>
        }
        @if (showDiff()) {
          <td align="right">
            <value-formatter useColor [value]="data.dida_conrtar_mean_samp_actprice_rate_DIFF" suffix="pp" />
          </td>
        }

        <!-- 嘀嗒线上价格-每公里均价 -->
        <td align="right">
          <value-formatter [value]="data.dida_mean_samp_actprice_amt" />
        </td>
        @if (showMiniGraph()) {
          <td nz-tooltip nzTooltipTitle="点击可查看各公里段详情" nzTooltipPlacement="bottom">
            <div class="relative h-14 -m-4 hover:bg-primary/5 transition-colors">
              @if (data[`dida_mean_samp_actprice_amt:split:${miage_intal_key}`]) {
                <app-graph-mini-column
                  class="cursor-pointer"
                  name="嘀嗒线上实价-每公里均价"
                  buriedPoint
                  buriedPointCode="dida_dpm_caerus_didaprice_route_click"
                  appDropdown
                  [dropdownMenu]="overlay_1_template"
                  [dropdownMenuOutsideClosable]="false"
                  [dropdownMenuContext]="{
                    $implicit: {
                      orderType: this.orderType(),
                      priceType: this.priceType(),
                      cityRegion: data.city_region,
                    },
                  }"
                  [values]="data[`dida_mean_samp_actprice_amt:split:${miage_intal_key}`]"
                />
              } @else {
                <span class="absolute inset-0 flex items-center justify-center">-</span>
              }
            </div>
          </td>
        }
        <td align="right">
          <div class="flex items-center justify-end">
            <value-formatter useColor [value]="data.A1" />
            <value-formatter useColor [value]="data.A4" showBracket suffix="%" />
          </div>
        </td>
        @if (showDiff()) {
          <td align="right">
            <div class="flex items-center justify-end">
              <value-formatter useColor [value]="data.dida_mean_samp_actprice_amt_DIFF" />
              <value-formatter
                useColor
                useMultiplier="false"
                showBracket
                [value]="data.dida_mean_samp_actprice_amt_DIFF_RATIO"
                suffix="%"
              />
            </div>
          </td>
        }

        <!-- 车乘比 -->
        <td align="right">
          <value-formatter [value]="data.driver_pass_active_prop" />
        </td>
        @if (showDiff()) {
          <td align="right">
            <div class="flex items-center justify-end">
              <value-formatter useColor [value]="data.driver_pass_active_prop_DIFF" />
              <value-formatter
                useColor
                useMultiplier="false"
                showBracket
                [value]="data.driver_pass_active_prop_DIFF_RATIO"
                suffix="%"
              />
            </div>
          </td>
        }

        <!-- 乘客下单率 -->
        <td align="right">
          <value-formatter [value]="data.c_active_book_pass_rate" suffix="%" />
        </td>
        @if (showDiff()) {
          <td align="right">
            <value-formatter useColor [value]="data.c_active_book_pass_rate_DIFF" suffix="pp" />
          </td>
        }

        <!-- 车主活跃接单率 -->
        <td align="right">
          <value-formatter [value]="data.c_active_reply_driver_rate" suffix="%" />
        </td>
        <!-- <td align="right">
            <value-formatter useColor [value]="data.A2" suffix="pp" />
          </td> -->
        @if (showDiff()) {
          <td align="right">
            <value-formatter useColor [value]="data.c_active_reply_driver_rate_DIFF" suffix="pp" />
          </td>
        }

        <!-- 下单完单率(乘客) -->
        <td align="right">
          <value-formatter [value]="data.c_book_finish_pass_rate" suffix="%" />
        </td>
        <!-- <td align="right">
            <value-formatter useColor [value]="data.A3" suffix="pp" />
          </td> -->
        @if (showDiff()) {
          <td align="right">
            <value-formatter useColor [value]="data.c_book_finish_pass_rate_DIFF" suffix="pp" />
          </td>
        }

        <!-- 接单完单率(车主) -->
        <td align="right">
          <value-formatter [value]="data.c_reply_finish_driver_rate" suffix="%" />
        </td>
        @if (showDiff()) {
          <td align="right">
            <value-formatter useColor [value]="data.c_reply_finish_driver_rate_DIFF" suffix="pp" />
          </td>
        }

        <!-- 自有乘客活跃完单率(UCVR) -->
        <td align="right">
          <value-formatter [value]="data.c_own_active_finish_pass_rate" suffix="%" />
        </td>
        @if (showDiff()) {
          <td align="right">
            <value-formatter useColor [value]="data.c_own_active_finish_pass_rate_DIFF" suffix="pp" />
          </td>
        }

        <!-- 车主活跃完单率(UCVR) -->
        <td align="right">
          <value-formatter [value]="data.c_active_finish_driver_rate" suffix="%" />
        </td>
        @if (showDiff()) {
          <td align="right">
            <value-formatter useColor [value]="data.c_active_finish_driver_rate_DIFF" suffix="pp" />
          </td>
        }
      </tr>
    }
  </tbody>
</nz-table>

<footer class="absolute bottom-2 py-3">
  <!-- <div class="flex items-center text-xs">
    @if (selected().length > 0) {
      <span>已选中：</span>
      @for (item of selected(); track $index) {
        <nz-tag nzMode="closeable" nzColor="blue" (nzOnClose)="updateCheckedSet(item.city_region, false)">
          {{ item.city_region }}
        </nz-tag>
      }
      <button nz-button nzType="primary" nzSize="small" class="text-xs! scale-90" (click)="quickCompare()">
        一键对比
      </button>
      <span class="ml-3">说明：为避免选择城市过多，影响判断，对比的城市数量建议不超过2个</span>
    }
  </div> -->
</footer>
<div class="flex items-center text-xs pt-4">
  <span class="ml-3">说明：点击起点城市名称，可查看对应城市的价格变化趋势。</span>
</div>
