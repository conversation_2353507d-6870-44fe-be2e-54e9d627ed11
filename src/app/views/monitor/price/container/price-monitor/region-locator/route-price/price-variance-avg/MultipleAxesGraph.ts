import { BaseHighCharts } from '@common/chart/highcharts';
import { handleLegendItemClick } from '@common/function';
import { ChartOptions, PlotOptions, XAxisOptions, YAxisOptions } from 'highcharts';


export default class extends BaseHighCharts {

  chart: ChartOptions = {
    spacing: [20, 10, 5, 10],
    type: 'column',
  };

  xAxis: XAxisOptions = {
    categories: [],
    gridLineWidth: 0,
    lineWidth: 0,
    tickWidth: 1,
    tickColor: 'rgba(0, 0, 0, 0.2)',
    labels: {
      y: 18
    }
  };

  yAxis: YAxisOptions[] = [
    {
      max: 100,
      reversed: false,
      title: { text: '路线占比' },
      gridLineWidth: 1,
      tickPixelInterval: 40,
      labels: {
        format: '{text:.1f}%',
        style: {
          fontSize: '12px'
        }
      }
    },
    {
      reversed: false,
      title: { text: '价格差异率' },
      gridLineWidth: 1,
      tickPixelInterval: 40,
      labels: {
        format: '{text:.1f}%',
        style: {
          fontSize: '12px'
        }
      },
      opposite: true
    }
  ];


  plotOptions: PlotOptions = {
    line: {
      marker: {
        radius: 2
      }
    }
  };


  constructor() {
    super();
    this.legend.align = 'left';
    this.legend.verticalAlign = 'middle';
    this.tooltip.valueSuffix = '%';
    this.responsive = null;
  }

  setCategories(categories: string[]) {
    this.xAxis.categories = categories;
  }

  setSeries(series: any[]) {
    this.series = series;
  }

  setYAxisReverse() {
    this.yAxis = this.yAxis.map(item => {
      return {... item, reversed: true };
    })
  }

  setYAxiseMin(min: number) {
    this.yAxis = this.yAxis.map(item => {
      return {... item, min };
    })
  }

  override getOption() {
    const value = JSON.parse(JSON.stringify(this));

    value.plotOptions = {
      ...value.plotOptions,
      series: {
        events: {
          legendItemClick: handleLegendItemClick(this),
        }
      }
    }

    return value;
  }

}
