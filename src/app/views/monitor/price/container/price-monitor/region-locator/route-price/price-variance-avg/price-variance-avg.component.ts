import { DragDropModule } from '@angular/cdk/drag-drop';
import { DecimalPipe, NgTemplateOutlet } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, Component, computed, DestroyRef, inject, input, output, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormGroup } from '@angular/forms';
import { debounceTime, finalize } from 'rxjs';

import { CaerusApiService } from '@api/caerus';
import { DimensionValueMenuVo } from '@api/caerus/model';
import { QueryEngineApiService } from '@api/query-engine';
import { MetricVo, QueryOutputVo } from '@api/query-engine/model';
import { BaseHighCharts, Pattern } from '@common/chart/highcharts';
import { groupBy, isEmpty, sortCategoriesFn, toDecimals } from '@common/function';
import { QueryEngineFormService } from '@common/service/query-engine';
import { SwitchMap } from '@common/decorator';
import { GraphComponent } from '@shared/components/graph';
import { DropdownDirective } from '@shared/directives/dropdown';
import { ValueFormatter } from '@shared/components/value-formatter';
import { LineSpinComponent } from '@shared/components/line-spin';
import { IconCloseComponent } from '@shared/modules/icons';
import { FilterItem } from '@views/monitor/price/lib';
import { ReplacePipe } from '@views/monitor/price/pipes/replace';
import { PriceService } from '@views/monitor/price/price.service';
import MultipleAxesGraph from './MultipleAxesGraph';

@Component({
  selector: 'app-price-variance-avg',
  template: `
    <div cdkDrag class="relative pointer-events-auto w-190 h-125 bg-white rounded-sm border border-neutral-200 shadow-1">
      <div class="flex flex-col h-full">
        <header cdkDragHandle class="header">
          <span>{{ title() | replace: priceService.competitionName() }}</span>
          <CloseIcon iconBtn class="ml-auto" (click)="close()" />
        </header>

        @if (loading()) {
          <div class="flex flex-1 min-h-0">
            <app-line-spin class="m-auto scale-90" />
          </div>
        } @else {
          <div>
            <ng-template #row_1 let-value let-index="cols">
              <td>{{ value }}</td>
            </ng-template>

            <ng-template #row_2 let-value let-index="cols">
              @if (index !== 0) {
                <td>
                  <value-formatter [value]="value" suffix="%" />
                </td>
              } @else {
                <td>{{ value }}</td>
              }
            </ng-template>

            <ng-template #row_3 let-value let-index="cols">
              @if (index !== 0) {
                <td>
                  <value-formatter linkTip="点击查看价格差异归因" [useLink]="this.data().canDiff" useColor [value]="value" suffix="%" (onLinkClick)="handleLinkClick(index)" />
                </td>
              } @else {
                <td>{{ value }}</td>
              }
            </ng-template>

            <ng-template #row_4 let-value let-index="cols">
              @if (index !== 0) {
                <td>
                  <value-formatter useColor [value]="value" suffix="pp" />
                </td>
              } @else {
                <td>{{ value }}</td>
              }
            </ng-template>

            <ng-template #cellTemplate let-value let-index="lines" let-cols="cols">
              @switch (index) {
                @case (0) { <ng-template *ngTemplateOutlet="row_1; context: { $implicit: value, cols }"></ng-template> }
                @case (1) { <ng-template *ngTemplateOutlet="row_2; context: { $implicit: value, cols }"></ng-template> }
                @case (2) { <ng-template *ngTemplateOutlet="row_3; context: { $implicit: value, cols }"></ng-template> }
                @case (3) { <ng-template *ngTemplateOutlet="row_4; context: { $implicit: value, cols }"></ng-template> }
              }
            </ng-template>

            <div class="p-2.5">
              @if (listOfRows()) {
                <table>
                  <tbody>
                    @for (items of listOfRows(); track $index; let rowIndex = $index) {
                      <tr>
                        @for (item of items; track item; let columnIndex = $index) {
                          <ng-template *ngTemplateOutlet="cellTemplate; context: { $implicit: item, lines: rowIndex, cols: columnIndex }"></ng-template>
                        }
                      </tr>
                    }
                  </tbody>
                </table>
              }
            </div>
          </div>

          <div class="flex-1 min-h-0 px-2.5">
            <div class="relative h-full">
              @if (option()) {
                <app-graph [options]="option()" />
              }
            </div>
          </div>

          <div class="flex-1 min-h-0 px-2.5 mb-2.5">
            <div class="relative h-full">
              @if (option_2()) {
                <app-graph [options]="option_2()" />
              }
            </div>
          </div>
        }
      </div>
    </div>
  `,
  styleUrl: './price-variance-avg.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgTemplateOutlet,
    DragDropModule,
    IconCloseComponent,
    GraphComponent,
    ValueFormatter,
    LineSpinComponent,
    ReplacePipe,
  ],
  providers: [DecimalPipe, ReplacePipe,],
})
export class PriceVarianceAvgComponent implements AfterViewInit {

  dropdownRef = inject(DropdownDirective, { optional: true });
  formService = inject(QueryEngineFormService);
  caerusApiService = inject(CaerusApiService);
  apiService = inject(QueryEngineApiService);
  decimalPipe = inject(DecimalPipe);
  priceService = inject(PriceService);
  replacePipe = inject(ReplacePipe);
  destroyRef = inject(DestroyRef);

  onLinkClick = output<string>();
  data = input.required<{
    orderType: DimensionValueMenuVo;
    priceType: DimensionValueMenuVo;
    cityRegion: string;
    canDiff: boolean;
  }>();

  #cityRegions = signal<DimensionValueMenuVo[]>([]);
  #tableMetrics = signal<MetricVo[]>([
    {
      extendName: 'dida_conrtar_mean_samp_actprice_rate',
      aliasName: '嘀嗒与DD线上实价差异率均值-整单价格',
      tagName: 'price',
    },
    {
      extendName: 'c_book_ord_cnt',
      aliasName: '下单量',
      tagName: 'dau',
    },
    {
      extendName: 'c_book_ord_cnt',
      aliasName: '下单量',
      customType: 'proportion',
      proportionDimension: [],
      tagName: 'dau',
    },
  ]);

  #chartMetrics = signal<MetricVo[]>([
    {
      extendName: 'dida_conrtar_gt_samp_competition_ord_unt',
      aliasName: '嘀嗒价格>DD路线数量占比-整单价格',
      tagName: 'price',
    },
    {
      extendName: 'dida_conrtar_mean_samp_competition_actprice_rate',
      aliasName: '嘀嗒价格>DD路线价格差异率均值-整单价格',
      tagName: 'price',
    },
    {
      extendName: 'dida_conrtar_samp_competition_ord_rate',
      aliasName: '嘀嗒价格<DD路线数量占比-整单价格',
      tagName: 'price',
    },
    {
      extendName: 'dida_conrtar_mean_samp_competition_rate',
      aliasName: '嘀嗒价格<DD路线价格差异率均值-整单价格',
      tagName: 'price',
    },
  ]);

  loading = signal(true);
  listOfRows = signal<Array<Array<string | number>>>(null);
  option = signal<BaseHighCharts>(null);
  option_2 = signal<BaseHighCharts>(null);

  title = computed(() => {
    if (this.data()) {
      const { cityRegion } = this.data();
      const [region] = this.#cityRegions().filter(item => item.value === cityRegion);
      return `嘀嗒与DD线上价格差异率均值-整单价格-${region?.value}`;
    }
    return null;
  });

  ngAfterViewInit(): void {
    this._subscribeToCompetitionNameChange();
  }

  private _subscribeToCompetitionNameChange() {
    this.priceService.competitionName$.pipe(
      debounceTime(100), 
      takeUntilDestroyed(this.destroyRef)
    ).subscribe(() => {
      this._fetchDimensionConfig(() => {
        this.tableQuery();
        this.chartQuery();
      });
    })
  }

  private _fetchDimensionConfig(callback: () => void) {
    this.caerusApiService.fetchDimensionConfig('price_monitor_dimension').subscribe(res => {
      if (res.status !== '00000') {
        return console.error(res.message || res.error);
      }

      const city_regions = res.data.price_save.find(item => item.keyName === 'price_city_region');
      this.#cityRegions.set(city_regions.values);
      callback();
    });
  }

  close() {
    this.dropdownRef.close();
  }

  private _orderTypeFilter() {
    const { orderType } = this.data();
    const { value } = new FormGroup(
      new FilterItem({
        conditionType: 2,
        condition: 'in',
        extendName: orderType.extendName,
        value: [
          {
            key: orderType.key,
            value: orderType.value,
          },
        ],
      })
    );

    return value;
  }

  private _priceTypeFilter() {
    const { priceType } = this.data();
    const { value } = new FormGroup(
      new FilterItem({
        conditionType: 2,
        condition: 'in',
        extendName: priceType.extendName,
        value: [
          {
            key: priceType.key,
            value: priceType.value,
          },
        ],
      })
    );

    return value;
  }

  private _cityRegionFilter() {
    const { cityRegion } = this.data();
    const [region] = this.#cityRegions().filter(item => item.value === cityRegion);
    const { value } = new FormGroup(
      new FilterItem({
        conditionType: 2,
        condition: 'in',
        extendName: 'city_region',
        value: [
          {
            key: region?.key,
            value: region?.value,
          },
        ],
      })
    );

    return value;
  }

  private get miageExtendName() {
    const {
      orderType: { showValue },
    } = this.data();

    return showValue === '城际' ? 'inter_miage_intal' : 'inner_miage_intal';
  }

  private _dimensionItems() {
    return [
      {
        id: null,
        extendName: this.miageExtendName,
        predefineCompareType: null,
      },
    ];
  }

  private _tableMetricsItem() {
    return this.#tableMetrics().map(item => {
      const items =
        item.tagName === 'price'
          ? [this._priceTypeFilter(), this._orderTypeFilter()]
          : item.tagName === 'dau'
          ? [this._orderTypeFilter()]
          : []

      return {
        ...item,
        userDefExtendName: item.extendName,
        filter: {
          items: items.flat(1),
        },
      };
    });
  }

  tableQuery() {
    const body = this.formService.value();

    body.metrics = this._tableMetricsItem().map(item => {
      if (item.extendName !== 'c_book_ord_cnt') {
        item.filter.items = item.filter.items.concat(this.priceService.competitionFilter());
      }
      return item;
    });
    
    body.dimensions = this._dimensionItems();
    body.filter.items = [this._cityRegionFilter()];
    body.scene = 7;
    body.dataFillConfig = {
      open: 1,
      fillRangeType: 1,
      fillValue: 1,
    };

    this.apiService.search(body).subscribe(res => {
      if (res.status !== '00000') {
        return console.error(res.message || res.error);
      }
      this.#renderTable(res.data);
    });
  }

  @SwitchMap()
  chartQuery() {
    const body = this.formService.value();

    body.metrics = this.#chartMetrics();
    body.dimensions = this._dimensionItems();
    body.filter.items = [this._cityRegionFilter(), this._priceTypeFilter(), this._orderTypeFilter()];
    body.scene = 7;
    body.dataFillConfig = {
      open: 1,
      fillRangeType: 1,
      fillValue: 1,
    };

    this.loading.set(true);
    // console.clear();
    // console.log(body);
    return this.apiService
      .search(body)
      .pipe(finalize(() => this.loading.set(false)))
      .subscribe(res => {
        if (res.status !== '00000') {
          return console.error(res.message || res.error);
        }
        this.#renderGraph(res.data);
        this.#renderGraph2(res.data);
      });
  }

  #renderGraph({ data, compareData }: QueryOutputVo) {
    const obj = groupBy(data, this.miageExtendName);
    const compareObj = groupBy(compareData, this.miageExtendName);
    const keys = Object.keys(obj).sort(sortCategoriesFn);
    const chart = new MultipleAxesGraph();
    const series = [];
    const s0 = { yAxis: 0, color: 'rgba(255, 191, 107, 1)', type: 'column', name: '嘀嗒价格≥DD价格路线占比', data: [] };
    const s1 = { yAxis: 0, color: 'rgba(255, 191, 107, 0.8)', type: 'column', name: '嘀嗒价格≥DD价格路线占比-对比期', data: [] };
    const s2 = { yAxis: 1, color: 'rgba(127, 131, 247, 1)', type: 'line', name: '嘀嗒价格≥DD价格路线价格差异率均值', data: [] };
    const s3 = { yAxis: 1, color: 'rgba(127, 131, 247, 0.75)', type: 'line', name: '嘀嗒价格≥DD价格路线价格差异率均值-对比期', data: [], dashStyle: 'Dash' };

    Object.keys(obj)
      .sort(sortCategoriesFn)
      .filter(key => !!key)
      .forEach(key => {
        const [value] = obj[key];
        const val_1 = value['dida_conrtar_gt_samp_competition_ord_unt'];
        const val_3 = value['dida_conrtar_mean_samp_competition_actprice_rate'];

        s0.data.push(val_1 === null ? null : toDecimals(val_1));
        s2.data.push(val_3 === null ? null : toDecimals(val_3));

        if (this.formService.hasDateCompare()) {
          if (isEmpty(compareObj)) {
            console.error('对比期无数据');
          } else {
            const [compareValue] = compareObj[key];
            const val_2 = compareValue['dida_conrtar_gt_samp_competition_ord_unt'];
            const val_4 = compareValue['dida_conrtar_mean_samp_competition_actprice_rate'];

            s1.data.push({
              y: val_2 === null ? null : toDecimals(val_2),
              color: { pattern: new Pattern({ color: s1.color }) },
            });
            s3.data.push(val_4 === null ? null : toDecimals(val_4));
          }
        }
      });

    if (this.formService.hasDateCompare()) {
      series.push(s0, s1, s2, s3);
    } else {
      series.push(s0, s2);
    }

    const newSeries = series.map(item => {
      return {
        ...item,
        name: this.replacePipe.transform(item.name, this.priceService.competitionName())
      }
    })

    const newKeys = keys.map(item => this.replacePipe.transform(item, this.priceService.competitionName()))

    chart.xAxis.tickWidth = 0;
    chart.xAxis.labels.enabled = false;
    chart.setYAxiseMin(0);
    chart.setSeries(newSeries);
    chart.setCategories(newKeys);
    this.option.set(chart.getOption());
  }

  #renderGraph2({ data, compareData }: QueryOutputVo) {
    const obj = groupBy(data, this.miageExtendName);
    const compareObj = groupBy(compareData, this.miageExtendName);
    const keys = Object.keys(obj).sort(sortCategoriesFn);
    const chart = new MultipleAxesGraph();
    const series = [];

    const s0 = { yAxis: 0, color: 'rgba(104, 187, 196, 1)', type: 'column', name: '嘀嗒价格&lt;DD价格路线占比', data: [] };
    const s1 = { yAxis: 0, color: 'rgba(104, 187, 196, 0.8)', type: 'column', name: '嘀嗒价格&lt;DD价格路线占比-对比期', data: [] };
    const s2 = { yAxis: 1, color: 'rgba(127, 131, 247, 1)', type: 'line', name: '嘀嗒价格&lt;DD价格路线价格差导率均值', data: [] };
    const s3 = { yAxis: 1, color: 'rgba(127, 131, 247, 0.75)', type: 'line', name: '嘀嗒价格&lt;DD价格路线价格差导率均值-对比期', data: [], dashStyle: 'Dash' };

    Object.keys(obj)
      .sort(sortCategoriesFn)
      .filter(key => !!key)
      .forEach(key => {
        const [value] = obj[key];
        const val_1 = value['dida_conrtar_samp_competition_ord_rate'];
        const val_3 = value['dida_conrtar_mean_samp_competition_rate'];

        s0.data.push(val_1 === null ? null : toDecimals(val_1));
        s2.data.push(val_3 === null ? null : toDecimals(val_3));

        if (this.formService.hasDateCompare()) {
          if (isEmpty(compareObj)) {
            console.error('对比期无数据');
          } else {
            const [compareValue] = compareObj[key];
            const val_2 = compareValue['dida_conrtar_samp_competition_ord_rate'];
            const val_4 = compareValue['dida_conrtar_mean_samp_competition_rate'];

            s1.data.push({
              y: val_2 === null ? null : toDecimals(val_2),
              color: { pattern: new Pattern({ color: s1.color }) },
            });
            s3.data.push(val_4 === null ? null : toDecimals(val_4));
          }
        }
      });

    if (this.formService.hasDateCompare()) {
      series.push(s0, s1, s2, s3);
    } else {
      series.push(s0, s2);
    }

    const newSeries = series.map(item => {
      return {
        ...item,
        name: this.replacePipe.transform(item.name, this.priceService.competitionName())
      }
    })

    const newKeys = keys.map(item => this.replacePipe.transform(item, this.priceService.competitionName()))

    chart.setSeries(newSeries);
    chart.setCategories(newKeys);
    chart.setYAxisReverse();
    this.option_2.set(chart.getOption());
  }

  #renderTable({ data }: QueryOutputVo) {
    const obj = groupBy(data, this.miageExtendName);
    const rows = [['公里段'], ['下单订单数量占比'], ['嘀嗒与DD线上价格差异率均值']].map(items => {
      return items.map(item => this.replacePipe.transform(item, this.priceService.competitionName()))
    });

    if (this.formService.hasDateCompare()) {
      rows.push(['较对比期']);
    }

    Object.keys(obj)
      .sort(sortCategoriesFn)
      .filter(key => !!key)
      .forEach(key => {
        const [value] = obj[key];

        rows[0].push(value[this.miageExtendName]);
        rows[1].push(value['c_book_ord_cnt:proportion']);
        rows[2].push(value['dida_conrtar_mean_samp_actprice_rate']);

        if (this.formService.hasDateCompare()) {
          rows[3].push(value?.dida_conrtar_mean_samp_actprice_rate_DIFF);
        }
      });

    this.listOfRows.set(rows);
  }

  handleLinkClick(index: number) {
    const value = this.listOfRows()[0][index] as string;
    this.onLinkClick.emit(value);
    this.close();
  }

}
