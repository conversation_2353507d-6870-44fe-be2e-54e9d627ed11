<div class="flex flex-col gap-2 relative">
  <div class="flex items-center flex-wrap gap-x-10 gap-y-2">
    <div class="flex items-center">
      <label [class]="labelClass">起点城市：</label>
      <app-city-picker
        placeholder="全部"
        multiple
        [(ngModel)]="city_name"
        useReferencePrice
        (ngModelChange)="getTableData()"
      />
    </div>
    <div class="flex items-center">
      <label [class]="labelClass">价格类型：</label>
      <app-radio-group class="relative flex gap-1" [(ngModel)]="price_type" (ngModelChange)="getTableData()">
        @for (l of price_type_list(); track l) {
          <app-radio class="tag-radio" activeClass="active" [value]="l">{{ l.showValue }}</app-radio>
        }
      </app-radio-group>
    </div>
    <div class="flex items-center">
      <label [class]="labelClass">订单类型：</label>
      <app-radio-group class="relative flex gap-1" ngModel="0">
        <app-radio class="tag-radio" activeClass="active" value="0">市内</app-radio>
      </app-radio-group>
    </div>
  </div>
  <div class="text-xs text-neutral-400">
    说明: 下表的刊例价数据固定展示所选日期区间最后一天的价格，不受对比期选择影响；当前展示为
    <span class="text-red-500">{{ formService.value()?.dt?.endTime }}</span>
    当天的价格
  </div>
  <nz-table
    nzBordered
    #rowSelectionTable
    nzShowPagination
    [nzLoading]="loading()"
    [nzData]="tbData()"
    [nzScroll]="{ x: '7000px' }"
  >
    <thead>
      <tr>
        <!-- <th rowspan="3" nzLeft>选择洞察目标</th> -->
        <th rowspan="3" nzLeft [class.text-center!]="true">起点城市</th>
        @for (i of inner_cardrate_dis(); track i) {
          <th colspan="5" [class]="color[$index]">刊例价模拟价格-每公里均价-{{ i.showValue }}</th>
        }
      </tr>
      <tr>
        @for (i of inner_cardrate_dis(); track i) {
          <ng-template #metricsTitleTemplate>
            {{ popovercontent()?.['dida_mean_cardrate_amt']?.aliasName | replace: priceService.competitionName() }}
          </ng-template>
          <ng-template #metricsContentTemplate>
            <div
              class="leading-normal"
              [innerHTML]="
                popovercontent()['dida_mean_cardrate_amt']?.bizExpression | replace: priceService.competitionName()
              "
            ></div>
          </ng-template>
          <ng-template #metricsTitleTemplate2>
            {{ popovercontent()?.['conrtar_mean_cardrate_amt']?.aliasName | replace: priceService.competitionName() }}
          </ng-template>
          <ng-template #metricsContentTemplate2>
            <div
              class="leading-normal"
              [innerHTML]="
                popovercontent()['conrtar_mean_cardrate_amt']?.bizExpression | replace: priceService.competitionName()
              "
            ></div>
          </ng-template>
          <ng-template #metricsTitleTemplate3>
            {{
              popovercontent()?.['dida_conrtar_cardrate_pricediff']?.aliasName | replace: priceService.competitionName()
            }}
          </ng-template>
          <ng-template #metricsContentTemplate3>
            <div
              class="leading-normal"
              [innerHTML]="
                popovercontent()?.['dida_conrtar_cardrate_pricediff']?.bizExpression
                  | replace: priceService.competitionName()
              "
            ></div>
          </ng-template>
          <th
            colspan="2"
            [class]="color[$index]"
            nz-popover
            [nzPopoverMouseEnterDelay]="0.5"
            [nzPopoverTitle]="metricsTitleTemplate"
            [nzPopoverContent]="metricsContentTemplate"
          >
            嘀嗒基础刊例价
          </th>
          <th
            colspan="2"
            [class]="color[$index]"
            nz-popover
            [nzPopoverMouseEnterDelay]="0.5"
            [nzPopoverTitle]="metricsTitleTemplate2"
            [nzPopoverContent]="metricsContentTemplate2"
          >
            {{ 'DD刊例价' | replace: priceService.competitionName() }}
          </th>
          <th
            rowspan="2"
            [class]="color[$index]"
            nzCustomFilter
            [nzSortOrder]="null"
            [nzSortDirections]="['ascend', 'descend', null]"
            [nzSortFn]="i.diffSortFn"
            nz-popover
            [class.!text-right]="true"
            [nzPopoverMouseEnterDelay]="0.5"
            [nzPopoverTitle]="metricsTitleTemplate3"
            [nzPopoverContent]="metricsContentTemplate3"
          >
            {{ '嘀嗒与DD刊例价差异率' | replace: priceService.competitionName() }}
            <ng-container>
              <nz-filter-trigger #target [nzActive]="getDiffValue(i.key)" [nzDropdownMenu]="menu">
                <nz-icon nzType="filter" nzTheme="fill" />
              </nz-filter-trigger>

              <nz-dropdown-menu #menu="nzDropdownMenu">
                <app-sub-filter
                  [title]="`嘀嗒与DD刊例价差异率` | replace: priceService.competitionName()"
                  [value]="getDiffValue(i.key)"
                  suffix="%"
                  (valueChange)="setDiffValue(i.key, $event); target.hide()"
                  [extendName]="`dida_conrtar_cardrate_pricediff_${i.key}`"
                />
              </nz-dropdown-menu>
            </ng-container>
          </th>
        }
      </tr>
      <tr>
        @for (i of inner_cardrate_dis(); track i) {
          <th
            [class]="color[$index]"
            [nzSortOrder]="null"
            [nzSortDirections]="['ascend', 'descend', null]"
            nzCustomFilter
            [class.!text-right]="true"
            [nzSortFn]="i.dida_cardrate_amt_sort"
          >
            数值
            <ng-container>
              <nz-filter-trigger
                #target
                [nzActive]="getDiffValue('dida_mean_cardrate_amt_' + i.key)"
                [nzDropdownMenu]="menu2"
              >
                <nz-icon nzType="filter" nzTheme="fill" />
              </nz-filter-trigger>

              <nz-dropdown-menu #menu2="nzDropdownMenu">
                <app-sub-filter
                  [title]="`嘀嗒基础刊例价数值`"
                  [value]="getDiffValue('dida_mean_cardrate_amt_' + i.key)"
                  (valueChange)="setDiffValue('dida_mean_cardrate_amt_' + i.key, $event); target.hide()"
                  [extendName]="`dida_mean_cardrate_amt_${i.key}`"
                />
              </nz-dropdown-menu>
            </ng-container>
          </th>
          <th
            [class]="color[$index]"
            nzCustomFilter
            [nzSortOrder]="null"
            [class.!text-right]="true"
            [nzSortDirections]="['ascend', 'descend', null]"
            [nzSortFn]="i.dida_cardrate_amt_diff_sort"
          >
            日环比
            <ng-container>
              <nz-filter-trigger
                #target
                [nzActive]="getDiffValue('dida_mean_cardrate_amt_diff' + i.key)"
                [nzDropdownMenu]="menu3"
              >
                <nz-icon nzType="filter" nzTheme="fill" />
              </nz-filter-trigger>

              <nz-dropdown-menu #menu3="nzDropdownMenu">
                <app-sub-filter
                  [title]="`嘀嗒基础刊例价日环比`"
                  suffix="%"
                  [value]="getDiffValue('dida_mean_cardrate_amt_diff' + i.key)"
                  (valueChange)="setDiffValue('dida_mean_cardrate_amt_diff' + i.key, $event); target.hide()"
                  [extendName]="`dida_mean_cardrate_amt_${i.key}:dt_DIFF_RATIO`"
                />
              </nz-dropdown-menu>
            </ng-container>
          </th>
          <th
            [class]="color[$index]"
            [nzSortOrder]="null"
            nzCustomFilter
            [class.!text-right]="true"
            [nzSortDirections]="['ascend', 'descend', null]"
            [nzSortFn]="i.conrtar_cardrate_amt_sort"
          >
            数值
            <ng-container>
              <nz-filter-trigger
                #target
                [nzActive]="getDiffValue('conrtar_mean_cardrate_amt_' + i.key)"
                [nzDropdownMenu]="menu4"
              >
                <nz-icon nzType="filter" nzTheme="fill" />
              </nz-filter-trigger>

              <nz-dropdown-menu #menu4="nzDropdownMenu">
                <app-sub-filter
                  [title]="`DD刊例价数值` | replace: priceService.competitionName()"
                  [value]="getDiffValue('conrtar_mean_cardrate_amt_' + i.key)"
                  (valueChange)="setDiffValue('conrtar_mean_cardrate_amt_' + i.key, $event); target.hide()"
                  [extendName]="`conrtar_mean_cardrate_amt_${i.key}`"
                />
              </nz-dropdown-menu>
            </ng-container>
          </th>
          <th
            [class]="color[$index]"
            [nzSortOrder]="null"
            nzCustomFilter
            [class.!text-right]="true"
            [nzSortDirections]="['ascend', 'descend', null]"
            [nzSortFn]="i.conrtar_cardrate_amt_diff_sort"
          >
            日环比
            <ng-container>
              <nz-filter-trigger
                #target
                [nzActive]="getDiffValue('conrtar_mean_cardrate_amt_diff' + i.key)"
                [nzDropdownMenu]="menu5"
              >
                <nz-icon nzType="filter" nzTheme="fill" />
              </nz-filter-trigger>

              <nz-dropdown-menu #menu5="nzDropdownMenu">
                <app-sub-filter
                  [title]="`DD刊例价日环比` | replace: priceService.competitionName()"
                  suffix="%"
                  [value]="getDiffValue('conrtar_mean_cardrate_amt_diff' + i.key)"
                  (valueChange)="setDiffValue('conrtar_mean_cardrate_amt_diff' + i.key, $event); target.hide()"
                  [extendName]="`conrtar_mean_cardrate_amt_${i.key}:dt_DIFF_RATIO`"
                />
              </nz-dropdown-menu>
            </ng-container>
          </th>
        }
      </tr>
    </thead>
    <tbody>
      @for (data of rowSelectionTable.data; track data) {
        <tr>
          <!-- <td
            nzLeft
            [nzChecked]="checkedCity().includes(data.city_name)"
            (nzCheckedChange)="onItemChecked(data.city_name, $event)"
            [nzDisabled]="checkedCity().length > 1"
          ></td> -->
          <td nzLeft class="text-blue-400 cursor-pointer" [class.!text-center]="true" (click)="compare(data.city_name)">
            {{ data.city_name }}
          </td>
          @for (i of inner_cardrate_dis(); track i) {
            <td [class.!text-right]="true">{{data[`dida_mean_cardrate_amt_${i.key}`] || '-'}}</td>
            <td
              [class.!text-right]="true"
              [class]="renderNumberText(data[`dida_mean_cardrate_amt_${i.key}:dt_DIFF_RATIO`]).color"
            >
              {{renderNumberText(data[`dida_mean_cardrate_amt_${i.key}:dt_DIFF_RATIO`]).text ?? '-'}}
              @if (renderNumberText(data[`dida_mean_cardrate_amt_${i.key}:dt_DIFF_RATIO`]).text) {
                %
              }
            </td>
            <td [class.!text-right]="true">{{data[`conrtar_mean_cardrate_amt_${i.key}`] || '-'}}</td>
            <td
              [class.!text-right]="true"
              [class]="renderNumberText(data[`conrtar_mean_cardrate_amt_${i.key}:dt_DIFF_RATIO`]).color"
            >
              {{renderNumberText(data[`conrtar_mean_cardrate_amt_${i.key}:dt_DIFF_RATIO`]).text ?? '-'}}
              @if (renderNumberText(data[`conrtar_mean_cardrate_amt_${i.key}:dt_DIFF_RATIO`]).text) {
                %
              }
            </td>
            <td
              [class.!text-right]="true"
              [class]="renderNumberText(data[`dida_conrtar_cardrate_pricediff_${i.key}`]).color"
            >
              {{renderNumberText(data[`dida_conrtar_cardrate_pricediff_${i.key}`], true)?.text ?? '-'}}
              @if (renderNumberText(data[`dida_conrtar_cardrate_pricediff_${i.key}`], true)?.text) {
                %
              }
            </td>
          }
        </tr>
      }
    </tbody>
  </nz-table>
  <div class="absolute bottom-5 left-0 text-xs">
    <!-- @if (checkedCity().length !== 0) {
      <span class="pr-3">已选中:</span>
      @for (city of checkedCity(); track city) {
        <nz-tag nzMode="closeable" nzColor="blue" (nzOnClose)="closeTag(city)">{{ city }}</nz-tag>
      }
      <button nz-button nzType="primary" nzSize="small" class="text-xs! scale-90" (click)="compare()">一键对比</button>
      <span class="ml-3">说明：为避免选择城市过多，影响判断，对比的城市数量建议不超过2个</span>
    } -->
    <span class="ml-3">说明：点击起点城市名称，可查看对应城市的价格变化趋势。</span>
  </div>
</div>
