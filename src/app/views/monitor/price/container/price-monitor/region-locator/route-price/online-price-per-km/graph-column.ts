import { BaseHighCharts } from '@common/chart/highcharts';
import { ChartOptions, XAxisOptions, YAxisOptions } from 'highcharts';

export default class extends BaseHighCharts {

  chart: ChartOptions = {
    spacing: [20, 10, 5, 10],
    type: 'column',
  };

  xAxis: XAxisOptions = {
    categories: [],
    gridLineWidth: 0,
    lineWidth: 0,
    tickWidth: 1,
    tickColor: 'rgba(0, 0, 0, 0.2)',
    labels: {
      y: 18
    }
  };

  yAxis: YAxisOptions = {
    min: 0,
    title: { text: '' },
    gridLineWidth: 1,
    tickPixelInterval: 40,
    labels: {
      format: '{value:.1f}',
      style: {
        fontSize: '12px'
      }
    }
  };

  constructor() {
    super();
    this.legend.enabled = false;
    this.responsive = null;
  }

  setCategories(categories: string[]) {
    this.xAxis.categories = categories;
  }

  setSeries(series: any[]) {
    this.series = series;
  }

}