import { FormsModule } from '@angular/forms';
import { ChangeDetectionStrategy, Component, computed, inject } from '@angular/core';

import { RadioModule } from '@shared/modules/headless';
import { HeadingComponent } from '@shared/components/heading';
import { RegionLocatorComponent } from './region-locator/region-locator.component';
import { TrendAnalysisComponent } from './trend-analysis/trend-analysis.component';
import { ReplacePipe } from '../../pipes/replace';
import { PriceService } from '../../price.service';

@Component({
  selector: 'app-price-monitor',
  template: `
    <div class="px-5 mt-5">
      <app-heading [title]="'价格水位监控'" [helpTemplate]="helpTemplate" [description]="desc()" />

      <ng-template #helpTemplate>
        说明：<br />
        {{'DD相关价格数据均为使用抽样方法获得，只在70个抽样城市可查询，其他城市暂无数据。' | replace: priceService.competitionName()}}
        {{'由于DD数据源污染问题，以下数据中涉及DD线上价格的，均为使用DD刊例价和DD里程模拟修正后的价格。' | replace: priceService.competitionName()}}
      </ng-template>

      <app-region-locator />
      <app-trend-analysis />
    </div>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    FormsModule,
    RadioModule,
    HeadingComponent,
    RegionLocatorComponent,
    TrendAnalysisComponent,
    ReplacePipe,
  ],
})
export class PriceMonitorComponent {

  readonly priceService = inject(PriceService);
  
  desc = computed(() => {
    return (
      this.priceService.competitionName() === 'GD' 
        ? `<span class="text-red-400">注：GD的数据开始监控日期：2025年5月18日，目前可查询城市列表：北京、上海、深圳、杭州、成都、广州、重庆、青岛、天津和济南</span>`
        : null
    );
  })
}
