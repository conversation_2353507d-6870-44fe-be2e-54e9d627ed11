<div class="space-y-2">
  <header class="flex items-center">
    <nz-radio-group [(ngModel)]="mode">
      <label nz-radio nzValue="all">全部</label>
      <label nz-radio nzValue="owner">我负责的</label>
      <label nz-radio nzValue="subscriber">我订阅的</label>
    </nz-radio-group>
    <button class="ml-auto!" nz-button nzType="primary" nzSize="small" (click)="openModal()">新增监控</button>
  </header>

  <nz-table
    #basicTable
    nzShowSizeChanger
    [nzFrontPagination]="false"
    [nzData]="data().value()?.data?.records"
    [nzLoading]="data().isLoading()"
    [nzTotal]="data().value()?.data?.total"
    [(nzPageIndex)]="pageNo"
    [(nzPageSize)]="pageSize"
  >
    <thead>
      <tr>
        <th>监控名称</th>
        <th>对标选择</th>
        <th>监控说明</th>
        <th>监控状态</th>
        <th>是否默认展示</th>
        <th>负责人</th>
        <th>设置日期</th>
        <th>订阅</th>
        <th>操作</th>
      </tr>
    </thead>
    <tbody>
      @for (data of basicTable.data; track $index) {
        <tr>
          <td>{{data.monitorName}}</td>
          <td>{{data.competition}}</td>
          <td>{{data.description}}</td>
          <td>{{data.state ? '启用' : '关闭'}}</td>
          <td>{{data.isDefault ? '是' : '否'}}</td>
          <td [innerHTML]="transformPerson(data.personInCharge)"></td>
          <td>{{data.createTime | date: 'yyyy/MM/dd'}}</td>
          <td>
            <nz-switch [ngModel]="data.isSubscribe" [nzControl]="true" [nzLoading]="data.switchLoading" (click)="toggleSubscribe(data)"></nz-switch>
          </td>
          <td class="whitespace-nowrap">
            <button nz-button nzType="text" nzSize="small" (click)="openMonitorConfigModal(data, true)">查看</button>
            <button nz-button nzType="text" nzSize="small" [disabled]="!hasAuth(data.createBy)" (click)="openMonitorConfigModal(data)">编辑</button>
            <button nz-button nzType="text" nzSize="small" [disabled]="!hasAuth(data.createBy)" (click)="delete(data.id)">删除</button>
          </td>
        </tr>
      }
    </tbody>
  </nz-table>
</div>