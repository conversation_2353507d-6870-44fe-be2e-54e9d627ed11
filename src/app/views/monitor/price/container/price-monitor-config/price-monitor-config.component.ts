import { FormsModule } from '@angular/forms';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, computed, effect, inject, signal } from '@angular/core';
import { DatePipe } from '@angular/common';
import { NzRadioModule } from 'ng-zorro-antd/radio';
import { NzButtonComponent } from 'ng-zorro-antd/button';
import { NzModalService } from 'ng-zorro-antd/modal';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzSwitchComponent } from 'ng-zorro-antd/switch';
import { lastValueFrom, map } from 'rxjs';

import { isDev } from '@common/const';
import { CaerusApiService } from '@api/caerus';
import { PriceMonitorConfigVo } from '@api/caerus/model';
import { UserInfoService } from '@api/user-info.service';
import { PriceMonitorSettingComponent } from '../price-monitor-setting';
import { Confirmed } from '@common/decorator';
import { rxResource } from '@angular/core/rxjs-interop';



@Component({
  selector: 'app-price-monitor-config',
  templateUrl: './price-monitor-config.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    DatePipe,
    FormsModule,
    NzSwitchComponent,
    NzButtonComponent,
    NzRadioModule,
    NzTableModule,
  ],
})
export class PriceMonitorConfigComponent {

  readonly cdr = inject(ChangeDetectorRef);
  readonly modal = inject(NzModalService);
  readonly apiService = inject(CaerusApiService);
  readonly userInfoService = inject(UserInfoService);
  readonly message = inject(NzMessageService);

  pageNo = signal(1);
  pageSize = signal(10);
  subscriber = signal<string>(window.localStorage.getItem('caerus.auth.username.py'));
  mode = signal<'all' | 'owner' | 'subscriber'>('owner');

  paginationBody = computed(() => ({
    pageNo: this.pageNo(),
    pageSize: this.pageSize(),
    subscriber: this.subscriber(),
  }))

  configListResource = rxResource({
    request: () => this.paginationBody(),
    loader: ({ request }) => this.apiService.fetchMonitorConfigList(
      request.pageNo,
      request.pageSize,
      request.subscriber,
    )
  });

  ownerListResource = rxResource({
    request: () => this.paginationBody(),
    loader: ({ request }) => this.apiService.fetchMonitorConfigOwnerList(
      request.pageNo,
      request.pageSize,
      request.subscriber,
    )
  });

  subscriptionListResource = rxResource({
    request: () => this.paginationBody(),
    loader: ({ request }) => this.apiService.fetchMonitorConfigSubscriptionList(
      request.pageNo,
      request.pageSize,
      request.subscriber,
    )
  });
    

  data = computed(() => {
    switch (this.mode()) {
      case 'all':
        return this.configListResource;
      case 'owner':
        return this.ownerListResource;
      case 'subscriber':
        return this.subscriptionListResource;
    }
  })
  
  
  constructor() {
    effect(() => {
      if (this.mode()) {
        this.pageNo.set(1);
      }
    })
  }


  hasAuth(username: string) {
    return (
      isDev() ||
      username === window.localStorage.getItem('caerus.auth.username.py') ||
      username === window.localStorage.getItem('caerus.auth.username')
    )
  }


  transformPerson(value: string) {
    return value.replace(/,/g, '<br />');
  }


  openModal(): void {
    this.modal.create({
      nzTitle: '监控设置',
      nzContent: PriceMonitorSettingComponent,
      nzWidth: 600,
      nzOkText: '保存',
      nzOnOk: async ({ valid, value }) => {
        if (valid) {
          const request$ = this.apiService.saveMonitorConfig(value).pipe(
            map(res => {
              if (res.status !== '00000') {
                this.message.error(res.error || res.message);
                return false;
              }
              this.data().reload();
              return true;
            })
          );
          return lastValueFrom(request$);
        }
        return false;
      }
    })
  }


  toggleSubscribe(item: PriceMonitorConfigVo) {
    const userName = window.localStorage.getItem('caerus.auth.username.py');
    const { id: metricsMonitorId, isSubscribe } = item;

    item.switchLoading = true;

    if (isSubscribe) {
      this.modal.confirm({
        nzTitle: '取消订阅',
        nzContent: '取消订阅后，该监控触发的告警信息，不会再钉钉发送给您，是否取消订阅？',
        nzOnOk: () => {
          this.apiService.unsubscribeMonitor({ metricsMonitorId, userName }).subscribe(res => {
            if (res.status !== '00000') {
              return this.message.error(res.error || res.message);
            }
            this.data().reload();
          })
        },
        nzOnCancel: () => {
          item.switchLoading = false;
          this.cdr.markForCheck();
        }
      })
    } else {
      this.modal.confirm({
        nzTitle: '订阅监控',
        nzContent: '订阅后，该监控发送的告警信息，也会通过钉钉发送给您，是否确认订阅？',
        nzOnOk: () => {
          this.apiService.subscribeMonitor({ metricsMonitorId, userName }).subscribe(res => {
            if (res.status !== '00000') {
              return this.message.error(res.error || res.message);
            }
            this.data().reload();
          })
        },
        nzOnCancel: () => {
          item.switchLoading = false;
          this.cdr.markForCheck();
        }
      })
    }
  }


  openMonitorConfigModal(data: PriceMonitorConfigVo, isView = false): void {
    const config: any = {
      nzTitle: '监控设置',
      nzContent: PriceMonitorSettingComponent,
      nzWidth: 600,
      nzData: {
        ...data,
        isView,
      },
      nzOkText: '保存',
      nzOnOk: async ({ valid, value }) => {
        if (valid) {
          const request$ = this.apiService.updateMonitorConfig(value).pipe(
            map(res => {
              if (res.status !== '00000') {
                return false;
              }
              this.data().reload();
              return true;
            })
          );
          return lastValueFrom(request$);
        }
        return false;
      }
    };

    if (isView) {
      config.nzFooter = null;
    }

    this.modal.create(config);
  }


  @Confirmed('确认删除?')
  delete(id: number) {
    this.apiService.deleteMonitorConfig({ id }).subscribe(res => {
      if (res.status !== '00000') {
        return;
      }
      this.data().reload();
    })
  }

}
