import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { AfterViewInit, ChangeDetectionStrategy, Component, computed, DestroyRef, inject, signal } from '@angular/core';
import { ControlContainer, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { NzCheckboxComponent } from 'ng-zorro-antd/checkbox';
import { NzOptionComponent, NzSelectComponent } from 'ng-zorro-antd/select';
import { delay, map, of, startWith, take } from 'rxjs';
import * as _ from 'lodash';

import { CaerusApiService } from '@api/caerus';
import { MetricsMonitorType, PriceMonitorConfig } from '@views/monitor/price/lib';
import { Before } from '@common/decorator';
import { EventInterceptor } from '@common/function';
import { PriceMonitorSettingComponent } from '../price-monitor-setting.component';


@Component({
  selector: 'app-order-type',
  templateUrl: './order-type.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    FormsModule,
    ReactiveFormsModule,
    NzCheckboxComponent,
    NzSelectComponent,
    NzOptionComponent,
  ],
})
export class OrderTypeComponent implements AfterViewInit {

  apiService = inject(CaerusApiService);
  parentContainer = inject(ControlContainer);
  destroyRef = inject(DestroyRef);
  parent = inject(PriceMonitorSettingComponent);

  innerCardrateOptions = signal<Array<{ key: string, value: string }>>([]);
  interMiageOptions = signal<Array<{ key: string, value: string }>>([]);
  innerMiageOptions = signal<Array<{ key: string, value: string }>>([]);
  interChecked = computed(() => {
    return this.orderTypes()?.some(item => _.isEqual(item, {
      key: '1',
      value: '城际'
    }))
  });
  
  innerChecked = computed(() => {
    return this.orderTypes()?.some(item => _.isEqual(item, {
      key: '2',
      value: '市内'
    }))
  });

  orderTypes = toSignal(this.parent.form.controls.dimensionFilter.controls.c_ord_type.controls.value.valueChanges.pipe(
    startWith(this.parent.form.controls.dimensionFilter.controls.c_ord_type.controls.value.value)
  ));
  monitorType = signal<MetricsMonitorType>(null);

  get form() {
    return this.parentContainer.control as FormGroup<PriceMonitorConfig>;
  }


  get valueControl() {
    return this.form.controls.dimensionFilter.controls.c_ord_type.controls.value;
  }


  get value() {
    return this.valueControl.value;
  }


  ngAfterViewInit(): void {
    this._fetchDimensionConfig();
    this._subscribeToMonitorTypeChange();
  }


  private _subscribeToMonitorTypeChange() {
    const control = this.parent.form.controls.monitorType;

    control.valueChanges.pipe(
      startWith(control.value),
      delay(100),
      takeUntilDestroyed(this.destroyRef)
    ).subscribe(type => {
      this.monitorType.set(type);
    })
    
    control.valueChanges.pipe(
      takeUntilDestroyed(this.destroyRef)
    ).subscribe(type => {
      if (type !== this.monitorType()) {
        this.form.get('dimensionFilter.inter_miage_intal.value').patchValue([]);
        this.form.get('dimensionFilter.inner_miage_intal.value').patchValue([]);
      }

      // 如果指标类型为刊例价格指标
      // 则默认选中`市内`里程，且无法取消
      if (type === 2) {
        this._clear();
        this._add('inner');
      }

      if (type === 3) {
        this._clear();
      }
    })
  }
  

  private _subscribeToMiageChange() {
    this.form.valueChanges.pipe(
      startWith(this.form.value),
      map(value => value.dimensionFilter),
      take(1)
    ).subscribe(({ inter_miage_intal, inner_miage_intal }) => {
      this.form.controls.dimensionFilter.controls.inter_miage_intal.controls.value.patchValue(
        this.interMiageOptions().filter(({ key, value }) => (
          inter_miage_intal.value?.some(item => item.key === key && item.value === value)
        ))
      );
      
      this.form.controls.dimensionFilter.controls.inner_miage_intal.controls.value.patchValue(
        this.innerMiageOptions().filter(({ key, value }) => (
          inner_miage_intal.value?.some(item => item.key === key && item.value === value)
        ))
      );
    })
  }


  private _fetchDimensionConfig() {
    this.apiService.fetchDimensionConfig('price_monitor_dimension').subscribe(res => {
      if (res.status !== '00000') {
        return console.error(res.message || res.error);
      }
      const inter_miage_intal = res.data.price_base.find(item => item.keyName === 'inter_miage_intal');
      const inner_miage_intal = res.data.price_base.find(item => item.keyName === 'inner_miage_intal');
      const inner_cardrate_dis = res.data.price_base.find(item => item.keyName === 'inner_cardrate_dis');

      const interMiageOptions = inter_miage_intal.values.map(({ key, value }) => ({ key, value }));
      const innerMiageOptions = inner_miage_intal.values.map(({ key, value }) => ({ key, value }));
      const innerCardrateOptions = inner_cardrate_dis.values.map(({ key, value }) => ({ key, value }));
      
      this.interMiageOptions.set(interMiageOptions);
      this.innerMiageOptions.set(innerMiageOptions);
      this.innerCardrateOptions.set(innerCardrateOptions);
      this._subscribeToMiageChange();
    })
  }


  @Before(EventInterceptor)
  toggle(event: MouseEvent, type: 'inter' | 'inner') {
    const state = type === 'inter' ? this.interChecked() : this.innerChecked();
    
    state ? this._remove(type) : this._add(type);
  }


  private _getValue(type: 'inter' | 'inner') {
    return (
      type === 'inter' ? 
        { key: '1', value: '城际' } :
        { key: '2', value: '市内' }
    );
  }


  private _add(type: 'inter' | 'inner') {
    const value = this._getValue(type);

    this.valueControl.patchValue([...this.value, value]);
  }
  
  
  private _remove(type: 'inter' | 'inner') {
    const value = this._getValue(type);

    this.valueControl.patchValue(this.value.filter(item => !(_.isEqual(item, value))));
  }


  private _clear() {
    this.valueControl.patchValue([]);
  }

}
