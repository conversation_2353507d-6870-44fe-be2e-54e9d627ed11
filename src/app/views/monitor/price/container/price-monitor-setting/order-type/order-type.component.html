<div class="space-y-3 pt-1.25" [formGroup]="form">
  @switch (monitorType()) {
    @case (1) {
      <div class="flex items-start gap-x-4" formGroupName="dimensionFilter">
        <label class="relative w-15" (click)="toggle($event, 'inter')">
          <label nz-checkbox [disabled]="form.disabled" [ngModel]="interChecked()" [ngModelOptions]="{ standalone: true }" ></label>
          <span class="absolute inset-0 z-10 text-right px-2 select-none cursor-pointer">城际</span>
        </label>
    
        <div class="flex-1 min-w-0 flex items-start" formGroupName="inter_miage_intal">
          <span class="whitespace-nowrap">城际公里段：</span>
          <nz-select class="-translate-y-1.5" formControlName="value" nzMode="multiple" nzAllowClear [nzServerSearch]="true" nzPlaceHolder="全部">
            @for (item of interMiageOptions(); track $index) {
              <nz-option [nzValue]="item" [nzLabel]="item.key"></nz-option>
            }
          </nz-select>
        </div>
      </div>
    
      <div class="flex items-start gap-x-4" formGroupName="dimensionFilter">
        <label class="relative w-15" (click)="toggle($event, 'inner')">
          <label nz-checkbox [disabled]="form.disabled" [ngModel]="innerChecked()" [ngModelOptions]="{ standalone: true }" ></label>
          <span class="absolute inset-0 z-10 text-right px-2 select-none cursor-pointer">市内</span>
        </label>
    
        <div class="flex-1 min-w-0 flex items-start" formGroupName="inner_miage_intal">
          <span class="whitespace-nowrap">市内公里段：</span>
          <nz-select class="-translate-y-1.5" formControlName="value" nzMode="multiple" nzAllowClear [nzServerSearch]="true" nzPlaceHolder="全部">
            @for (item of innerMiageOptions(); track $index) {
              <nz-option [nzValue]="item" [nzLabel]="item.key"></nz-option>
            }
          </nz-select>
        </div>
      </div>
    }

    @case (2) {
      <div class="flex items-start gap-x-4" formGroupName="dimensionFilter">
        <label nz-checkbox disabled [ngModel]="true" [ngModelOptions]="{ standalone: true }">市内</label>

        <div class="flex-1 min-w-0 flex items-start" formGroupName="inner_cardrate_dis">
          <span class="whitespace-nowrap">市内公里段：</span>
          <nz-select class="-translate-y-1.5" formControlName="value" nzMode="multiple" nzAllowClear [nzServerSearch]="true" nzPlaceHolder="全部">
            @for (item of innerCardrateOptions(); track $index) {
              <nz-option [nzValue]="item" [nzLabel]="item.value"></nz-option>
            }
          </nz-select>
        </div>
      </div>
    }
  }
</div>