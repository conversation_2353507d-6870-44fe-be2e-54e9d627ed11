import { ControlContainer, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { AfterViewInit, ChangeDetectionStrategy, Component, computed, inject, viewChildren } from '@angular/core';

import { CaerusApiService } from '@api/caerus';
import { PriceMonitorConfig } from '@views/monitor/price/lib';
import { MetricFilterItemComponent } from './metric-filter-item/metric-filter-item.component';
import { SwitchComponent } from '@shared/modules/headless/switch';


@Component({
  selector: 'app-metric-threshold',
  template: `
    <div class="relative" [formGroup]="form">
      <ng-container formArrayName="metricsFilter">
        @for (item of metricsFilter.controls; track item; let i = $index) {
          <ng-container [formGroupName]="i">
            <div [hidden]='count < 2' class="absolute inset-y-0 z-20 w-13 flex items-center justify-center">
              <app-switch
                #switch
                formControlName="condition"
                trueValue="and"
                falseValue="or"
                class="px-2 py-0.5 rounded-full bg-orange-100 select-none cursor-pointer border-2 border-white font-medium text-xs" 
                activeClass="bg-blue-100!"
              >
                {{switch.checked ? 'AND' : 'OR'}}
              </app-switch>
            </div>

            <div formArrayName="subFilter" class="flex flex-col gap-y-3" [class.isAnd]="switch.checked" [class.fieldRuleListContainer]="count > 1">
              @for (sub of item.controls.subFilter.controls; track sub; let n = $index) {
                <app-metric-filter-item [formGroupName]="n" />
              }
            </div>
          </ng-container>
        }
      </ng-container>
    </div>
  `,
  styleUrl: './metric-threshold.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    ReactiveFormsModule,
    MetricFilterItemComponent,
    SwitchComponent,
  ],
})
export class MetricThresholdComponent implements AfterViewInit {

  apiService = inject(CaerusApiService);
  parentContainer = inject(ControlContainer);

  filterItems = viewChildren(MetricFilterItemComponent);

  get form() {
    return this.parentContainer.control as FormGroup<PriceMonitorConfig>;
  }


  get metricsFilter() {
    return this.form.controls.metricsFilter;
  }
  

  get count() {
    return this.metricsFilter.at(0).controls.subFilter.length;
  }

  ngAfterViewInit(): void {
  }

}
