import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>A<PERSON>y, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { AfterViewInit, ChangeDetectionStrategy, Component, computed, DestroyRef, inject, Signal, signal } from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { combineLatest, Observable, startWith } from 'rxjs';

import { CaerusApiService } from '@api/caerus';
import { MetricsMenuVo } from '@api/caerus/model';
import { ConditionItemComponent } from '@views/monitor/price/components';
import { FilterItem } from '@views/monitor/price/lib';
import { IconDeleteComponent, IconPlusCircleComponent } from '@shared/modules/icons';
import { SwitchComponent } from '@shared/modules/headless/switch';
import { PriceMonitorSettingComponent } from '../../price-monitor-setting.component';
import { ReplacePipe } from '@views/monitor/price/pipes/replace';


@Component({
  selector: 'app-metric-filter-item',
  template: `
    <div [formGroup]="form" class="space-y-2">
      <div class="flex items-center gap-x-2" [class.pl-14]="count() > 1">
        <nz-select
          nzAllowClear
          [style.max-width.px]="count() > 1 ? 233 : 288"
          formControlName="extendName"
          [nzServerSearch]="true"
        >
          @for (item of metricOptionsCopy(); track $index) {
            <nz-option nzCustomContent [nzLabel]="item.key" [nzValue]="item.value">
              <ng-template #metricsInfo>
                <div class="leading-normal" [innerHTML]="item.bizExpression"></div>
              </ng-template>
              <span nz-tooltip [nzTooltipTitle]="metricsInfo" nzTooltipPlacement="topLeft">
                {{item.key}}
              </span>
            </nz-option>
          }
        </nz-select>

        <nz-select class="max-w-24" formControlName="predefineCompareType">
          @for (item of compareOptions(); track $index) {
            <nz-option [nzValue]="item.value" [nzLabel]="item.label"></nz-option>
          }
        </nz-select>

        <div class="flex items-center gap-2 min-w-14">
          <PlusCircleIcon iconBtn [class.disabled]="form.disabled" (click)="addItem()" />

          @if (count() > 1) {
            <DeleteIcon iconBtn [class.disabled]="form.disabled" (click)="removeItem()" />
          }
        </div>
      </div>

      <div class="relative flex flex-col gap-y-2" [class.pl-14]="count() > 1">
        <div [hidden]='subFilter.controls.length < 2' class="absolute inset-y-0 z-20 w-13 flex items-center justify-center">
          <app-switch
            #switch
            formControlName="condition"
            trueValue="and"
            falseValue="or"
            class="px-2 py-0.5 rounded-full bg-orange-100 select-none cursor-pointer border-2 border-white font-medium text-xs" 
            activeClass="bg-blue-100!"
          >
            {{switch.checked ? 'AND' : 'OR'}}
          </app-switch>
        </div>

        <div formArrayName="subFilter" class="flex flex-col gap-y-3" [class.isAnd]="switch.checked" [class.fieldRuleListContainer]="subFilter.controls.length > 1">
          @for (item of subFilter.controls; track item; let i = $index) {
            <app-condition-item [formGroupName]="i" />
          }
        </div>
      </div>
    </div>
  `,
  styleUrl: './metric-filter-item.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    ReactiveFormsModule,
    NzSelectModule,
    NzToolTipModule,
    ConditionItemComponent,
    SwitchComponent,
    IconPlusCircleComponent,
    IconDeleteComponent,
  ],
  providers: [
    ReplacePipe
  ]
})
export class MetricFilterItemComponent implements AfterViewInit {

  apiService = inject(CaerusApiService);
  parentContainer = inject(ControlContainer);
  parent = inject(PriceMonitorSettingComponent);
  replacePipe = inject(ReplacePipe);
  destroyRef = inject(DestroyRef);

  count = signal(0);
  metricOptions = signal<Array<{ key: string, value: string, bizExpression: string }>>([]);
  metricOptionsCopy = computed(() => {
    return this.metricOptions().map(item => {
      return {
        ...item,
        bizExpression: this.replacePipe.transform(item.bizExpression, this.competitionName()),
        key: this.replacePipe.transform(item.key, this.competitionName()),
      };
    })
  })
  #originMetricOptions = signal<MetricsMenuVo[]>([]);
  compareOptions = signal([
    { label: '数值', value: '' },
    { label: '日环比', value: 'dt' },
    { label: '周同比', value: 'yw' },
  ]);

  suffix = signal<'%' | 'pp'>(null);
  
  tagNameMap = new Map([
    [1, '路线价格指标'],
    [2, '刊例价格指标'],
    [3, '常规指标'],
  ])

  get form() {
    return this.parentContainer.control as FormGroup<FilterItem>;
  }


  get subFilter() {
    return this.form.controls.subFilter;
  }


  get extendName() {
    return this.form.controls.extendName;
  }


  get predefineCompareType() {
    return this.form.controls.predefineCompareType;
  }

  competitionName = signal(null);
  
  ngAfterViewInit(): void {
    this._fetchMetricsConfig(() => {
      this._subscribeToMonitorTypeChange();
    });
    this._subscribeToFormChange();
    this._subscribeToParentChange();
    this._subscribeToCompetitionChange();
  }


  private _subscribeToCompetitionChange() {
    this.form.root.get('competition').valueChanges.pipe(
      startWith(this.form.root.get('competition').value),
      takeUntilDestroyed(this.destroyRef)
    ).subscribe(value => {
      this.competitionName.set(value);
    })
  }


  private _subscribeToFormChange() {
    combineLatest([
      this.extendName.valueChanges.pipe(startWith(this.extendName.value)),
      this.predefineCompareType.valueChanges.pipe(startWith(this.predefineCompareType.value)),
    ]).pipe(
      takeUntilDestroyed(this.destroyRef)
    ).subscribe(([extendName, compareType]) => {
      const { dataUnit } = this.#originMetricOptions().find(item => item.extendName === extendName) || {};
      const isRatio = compareType === 'dt' || compareType === 'yw';
      
      // console.log('[extendName change]', extendName);
      // console.log('[predefineCompareType change]', compareType);
      // console.log('[dataUnit]', dataUnit);
      
      // 需求：指标单位是％，配置同环比阈值时，数据单位是pp

      /**
       * 如果指标单位是％
       * 且 compareType是日环比、周同比
       * 则 单位应为pp
       * 否则如果 compareType是dt
       * 则 单位应为%
       */
      if (dataUnit === '%') {
        if (isRatio) {
          this.suffix.set('pp');
        } else {
          this.suffix.set('%');
        }
      } else {
        /**
         * 如果指标单位是数值
         * 且 compareType是日环比、周同比
         * 则 单位应为%
         */
        if (isRatio) {
          this.suffix.set('%');
        } else {
          this.suffix.set(null);
        }
      }

    })
  }


  private _subscribeToMonitorTypeChange() {
    const control = this.parent.form.get('monitorType');

    // 联动指标类型，切换对应的指标列表
    control.valueChanges.pipe(
      startWith(control.value),
      takeUntilDestroyed(this.destroyRef)
    ).subscribe(type => {
      const list = this.#originMetricOptions().filter(item => item.tagName === this.tagNameMap.get(type));
      const metricOptions = list.map(({ aliasName, extendName, bizExpression, ...values }) => ({ key: aliasName, value: extendName, bizExpression, ...values }));
      const notFound = metricOptions.every(item => item.value !== this.extendName.value);
      
      // 如果指标列表中找不到当前extendName，则清空extendName
      if (notFound) {
        this.extendName.patchValue(null);
      }

      this.metricOptions.set(metricOptions);
    })
  }

  
  private _subscribeToParentChange() {
    this.form.parent.valueChanges.pipe(
      startWith(this.form.parent.value),
      takeUntilDestroyed(this.destroyRef)
    ).subscribe((values: any[]) => {
      this.count.set(values.length ?? 0);
    })
  }


  private _fetchMetricsConfig(callback: () => void) {
    this.apiService.fetchMetricsConfig('price_monitor_metrics').subscribe(res => {
      const { subMetric } = res.data['price_monitor'];
      
      this.#originMetricOptions.set(subMetric);
      callback();
    })
  }


  addItem() {
    const control = new FormGroup(new FilterItem({
      conditionType: 1,
      condition: 'and',
      valueType: 'metrics',
      subFilter: [{
        conditionType: 2,
        condition: '<',
        valueType: 'metrics',
        value: [{ key: null, value: null }]
      }]
    }));
    (<FormArray>this.form.parent).push(control);
  }


  removeItem() {
    const index = this.parentContainer.name as number;

    (<FormArray>this.form.parent).removeAt(index);
  }

}
