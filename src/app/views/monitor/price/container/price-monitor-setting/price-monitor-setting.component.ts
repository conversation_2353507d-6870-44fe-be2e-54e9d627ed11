import { Async<PERSON>ip<PERSON>, JsonPipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, DestroyRef, inject, OnInit, signal } from '@angular/core';
import { FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzSwitchModule } from 'ng-zorro-antd/switch';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzRadioModule } from 'ng-zorro-antd/radio';
import { NzAvatarModule } from 'ng-zorro-antd/avatar';
import { NZ_MODAL_DATA } from 'ng-zorro-antd/modal';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { filter, map, startWith, take } from 'rxjs';
import * as _ from 'lodash';

import { CaerusApiService } from '@api/caerus';
import { PriceMonitorConfigVo } from '@api/caerus/model';
import { UraApiService, UserInfo } from '@api/ura-api.service';
import { checkForm, getImageStyle, isNotNull } from '@common/function';
import { LineSpinComponent } from '@shared/components/line-spin';
import { DebugDirective } from '@shared/directives';
import { SearchPipe } from '@shared/pipes/search';

import { OrderTypeComponent } from './order-type/order-type.component';
import { MetricThresholdComponent } from './metric-threshold/metric-threshold.component';
import { PriceMonitorConfig } from '../../lib/PriceMonitorConfig';
import { CityPickerComponent } from '../../components';


@Component({
  selector: 'app-price-monitor-setting',
  templateUrl: './price-monitor-setting.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    JsonPipe,
    AsyncPipe,
    DebugDirective,
    ReactiveFormsModule,
    NzFormModule,
    NzInputModule,
    NzRadioModule,
    NzSelectModule,
    NzSwitchModule,
    NzAvatarModule,
    CityPickerComponent,
    MetricThresholdComponent,
    OrderTypeComponent,
    LineSpinComponent,
    SearchPipe,
  ],
})
export class PriceMonitorSettingComponent implements OnInit {

  destroyRef = inject(DestroyRef);
  nzModalData = inject<PriceMonitorConfigVo & { isView: boolean }>(NZ_MODAL_DATA);
  apiService = inject(CaerusApiService);
  uraApiService = inject(UraApiService);

  loading = signal(true);
  priceTypeOptions = signal<Array<{ key: string, value: string }>>([]);
  priceTypeOptions$ = toObservable(this.priceTypeOptions);
  usersOptions = signal<UserInfo[]>([]);
  keyword = signal<string>(null);
  
  avatarStyle4x = getImageStyle(80, 80,  2);
  form: FormGroup<PriceMonitorConfig>;
  autoTips: Record<string, Record<string, string>> = {
    default: {
      required: '必填项',
    },
  };

  fetchAllUsersInfo$ = this.apiService.fetchMonitorConfig('caerus').pipe(
    map(res => res.data)
  );


  ngOnInit(): void {
    if (this.nzModalData) {
      this.form = new FormGroup(new PriceMonitorConfig(this.nzModalData));
      this._subscribeToInit();

      if (this.nzModalData.isView) {
        this.form.disable();
      }
    } else {
      // 非编辑模式下初始化表单
      const username: string = window.localStorage.getItem('caerus.auth.username.py');
      this.form = new FormGroup(new PriceMonitorConfig());
      this.form.controls.personInCharge.patchValue([username]);
    }

    this.loading.set(false);
    
    this._fetchDimensionConfig();
    this._fetchUsersInfo();
    this._subscribeToMonitorTypeChange();
  }


  private _subscribeToInit() {
    const control = this.form.get('dimensionFilter.price_type.value');

    // 监听价格监控初始化时恢复选中项
    this.priceTypeOptions$.subscribe(items => {
      control.valueChanges.pipe(
        startWith(control.value),
        filter(isNotNull),
        take(1)
      ).subscribe(values => {
        const selected = items.filter(item => {
          return values.some(value => _.isEqual(item, value));
        })

        control.reset(selected);
      })
    })
  }


  private _subscribeToMonitorTypeChange() {
    const control = this.form.get('dimensionFilter.price_type.value');
    const city_region_control = this.form.get('dimensionFilter.city_region.value');
    let origin_type = this.form.get('monitorType').value;
    
    this.form.get('monitorType').valueChanges.pipe(
      startWith(this.form.get('monitorType').value),
      takeUntilDestroyed(this.destroyRef)
    ).subscribe(type => {
      // 当指标类型为3时，需清空价格类型非空验证器
      if (type === 3) {
        control.clearValidators();
      } else {
        control.addValidators(Validators.required);
      }

      if (type !== origin_type) {
        // 当指标类型切换时需清空城市选择
        city_region_control.patchValue(null);
      }

      origin_type = type;
    })
  }


  private _fetchDimensionConfig() {
    this.apiService.fetchDimensionConfig('price_monitor_dimension').subscribe(res => {
      if (res.status !== '00000') {
        return console.error(res.message || res.error);
      }
      const price_type = res.data.price_base.find(item => item.keyName === 'price_type');
      const priceTypeOptions = price_type.values.map(({ key, value }) => ({ key, value }));
      
      this.priceTypeOptions.set(priceTypeOptions);
    })
  }


  private _fetchUsersInfo() {
    this.uraApiService.fetchAllUsersInfo().subscribe(res => {
      this.usersOptions.set(res.data);
    })
  }


  get valid(): boolean {
    if (this.form.invalid) {
      checkForm(this.form.controls);
    }
    
    return this.form.valid;
  }

  
  get value() {
    const { personInCharge, dimensionFilter, metricsFilter, state, ...value } = this.form.value;

    return {
      ...value,
      personInCharge: personInCharge.join(','),
      dimensionFilter: JSON.stringify(dimensionFilter),
      metricsFilter: JSON.stringify(metricsFilter),
      state: Number(state) as 0 | 1
    };
  }


  handleSearch(value: string) {
    this.keyword.set(value);
  }

}
