@if (loading()) {
  <div class="flex h-80">
    <app-line-spin class="m-auto" />
  </div>
}

@if (!loading() && form) {
  <div class="relative">
    <form nz-form nzLayout="horizontal" [nzAutoTips]="autoTips" [formGroup]="form">
      <div class="flex flex-col">
        <nz-form-item>
          <nz-form-label nzRequired class="w-24">监控名称</nz-form-label>
          <nz-form-control>
            <input nz-input formControlName="monitorName" placeholder="请输入" spellcheck="false" />
          </nz-form-control>
        </nz-form-item>

        
        <nz-form-item>
          <nz-form-label nzRequired class="w-24">监控负责人</nz-form-label>
          <nz-form-control>
            <nz-select formControlName="personInCharge" nzMode="multiple" nzPlaceHolder="请选择（支持全拼、首拼搜索）" nzAllowClear [nzServerSearch]="true" (nzOnSearch)="handleSearch($event)">
              @for (item of usersOptions() | search: keyword(): 'cn'; track $index) {
                <nz-option nzCustomContent [nzLabel]="item.cn" [nzValue]="item.name">
                  <div class="flex items-center gap-x-1">
                    <img class="size-6 rounded-full overflow-hidden border border-solid border-black/5" [src]="item.avatarUrl + avatarStyle4x" />
                    <span class="text-xs">{{item.cn}}</span>
                  </div>
                </nz-option>
              }
            </nz-select>
            <p class="text-xs py-1 text-neutral-400">
              监控告警时，监控负责人会默认收到钉钉通知；<br />
              如需取消，请在监控列表页点击关闭订阅按钮。
            </p>
          </nz-form-control>
        </nz-form-item>


        <nz-form-item>
          <nz-form-label nzRequired class="w-24">对标选择</nz-form-label>
          <nz-form-control>
            <div class="flex items-center h-8">
              <nz-radio-group formControlName="competition">
                <label nz-radio nzValue="DD">DD</label>
                <label nz-radio nzValue="GD">GD</label>
              </nz-radio-group>
            </div>
            @if (form.controls.competition.value === 'GD') {
              <p class="text-xs py-1 text-red-400">
                注：目前可查询城市列表：<br />
                北京、上海、深圳、杭州、成都、广州、重庆、青岛、天津和济南
              </p>
            }
          </nz-form-control>
        </nz-form-item>


        <nz-form-item>
          <nz-form-label nzRequired class="w-24">指标类型</nz-form-label>
          <nz-form-control>
            <nz-radio-group formControlName="monitorType">
              <label nz-radio [nzValue]="1">路线价格指标</label>
              <label nz-radio [nzValue]="2">刊例价格指标</label>
              <label nz-radio [nzValue]="3">常规指标</label>
            </nz-radio-group>
          </nz-form-control>
        </nz-form-item>


        <nz-form-item>
          <nz-form-label nzRequired class="w-24">指标阈值</nz-form-label>
          <nz-form-control>
            <app-metric-threshold />
          </nz-form-control>
        </nz-form-item>

        
        @if (form.controls.monitorType.value === 2) {
          <nz-form-item formGroupName="dimensionFilter" class="!pointer-events-noneflex-nowrap">
            <nz-form-label nzRequired class="min-w-24">监控城市</nz-form-label>
            <nz-form-control formGroupName="city_name" class="flex-1 min-w-0!">
              <app-city-picker formControlName="value" multiple class="w-full" useReferencePrice />
            </nz-form-control>
          </nz-form-item>
        }
        @else {
          <nz-form-item formGroupName="dimensionFilter" class="!pointer-events-noneflex-nowrap">
            <nz-form-label nzRequired class="min-w-24">监控城市</nz-form-label>
            <nz-form-control formGroupName="city_region" class="flex-1 min-w-0!">
              <app-city-picker formControlName="value" multiple class="w-full"  />
            </nz-form-control>
          </nz-form-item>
        }


        <nz-form-item [hidden]="form.controls.monitorType.value === 3" formGroupName="dimensionFilter">
          <nz-form-label nzRequired class="w-24">价格类型</nz-form-label>
          <nz-form-control formGroupName="price_type">
            <nz-select formControlName="value" nzMode="multiple" nzPlaceHolder="请选择">
              @for (item of priceTypeOptions(); track $index) {
                <nz-option [nzLabel]="item.value" [nzValue]="item"></nz-option>
              }
            </nz-select>
          </nz-form-control>
        </nz-form-item>


        <nz-form-item [hidden]="form.controls.monitorType.value === 3">
          <nz-form-label nzRequired class="w-24">订单类型</nz-form-label>
          <nz-form-control>
            <app-order-type />
          </nz-form-control>
        </nz-form-item>


        <div>
          <nz-form-item>
            <nz-form-label class="w-24">监控说明</nz-form-label>
            <nz-form-control>
              @let descriptionPlaceholderText = '请将您设置的指标、指标阈值，以及监控范围做一个简短的说明，方便后续接受告警信息，以及查看告警明细。例如：“嘀嗒与DD线上价格差异率大于20%或者小于-20%时告警，面向Top120城市，城际、市内的全部公里段生效。”';
              <textarea nz-input formControlName="description" [placeholder]="descriptionPlaceholderText" spellcheck="false" [nzAutosize]="{ minRows: 5, maxRows: 7 }"></textarea>
            </nz-form-control>
          </nz-form-item>
        </div>


        <nz-form-item>
          <nz-form-label class="w-24">钉钉通知群</nz-form-label>
          <nz-form-control>
            <nz-select formControlName="alarmImGroup" nzPlaceHolder="请选择">
              @for (item of (fetchAllUsersInfo$ | async); track $index) {
                <nz-option [nzLabel]="item" [nzValue]="item"></nz-option>
              }
            </nz-select>
            <p class="text-xs py-1 text-neutral-400">
              选择钉钉通知群后，监控告警时，会同时向指定的钉钉群发送告警信息。
            </p>
          </nz-form-control>
        </nz-form-item>


        <div>
          <nz-form-item>
            <nz-form-label class="w-24">监控频率</nz-form-label>
            <nz-form-control>
              每天早上8点监控前一日数据
            </nz-form-control>
          </nz-form-item>
        </div>

        
        <div>
          <nz-form-item>
            <nz-form-label class="w-24">监控状态</nz-form-label>
            <nz-form-control>
              <nz-switch formControlName="state" />
            </nz-form-control>
          </nz-form-item>
        </div>
      </div>
    </form>

    <div *debug class="absolute -top-20 -left-10 max-w-115 h-full overflow-auto -translate-x-full px-10 py-10 bg-white">
      <pre class="text-xs scale-80 origin-top-left">{{form.value | json}}</pre>
    </div>
  </div>
}