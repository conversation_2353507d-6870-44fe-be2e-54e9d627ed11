import { Injectable, signal } from '@angular/core'
import { toObservable } from '@angular/core/rxjs-interop'
import { DimensionValueMenuVo } from '@api/caerus/model'
import { FilterItemVo } from '@api/caerus/model/FilterItemVo'
import { Subject } from 'rxjs'

interface QuickCompareItems {
  isReferencePrice?: boolean
  cityRegions?: DimensionValueMenuVo[]
  cityName?: DimensionValueMenuVo[]
  orderType: DimensionValueMenuVo
  priceType?: DimensionValueMenuVo
  priceTypeList?: DimensionValueMenuVo[]
  miageIntal?: DimensionValueMenuVo
  miageIntalList?: DimensionValueMenuVo[]
  scroll?: boolean
}

interface PriceDiffItems {
  cityName: string
  orderType: DimensionValueMenuVo
  priceType: DimensionValueMenuVo
  miageIntal: string
}

@Injectable()
export class PriceService {
  private quickCompareSubject = new Subject<QuickCompareItems>()
  public quickCompare$ = this.quickCompareSubject.asObservable()

  private priceDiffSubject = new Subject<PriceDiffItems>()
  public priceDiff$ = this.priceDiffSubject.asObservable()

  public competitionName = signal('DD');
  public competitionName$ = toObservable(this.competitionName);
  public competitionFilter = signal<FilterItemVo[]>([]);
  public competitionFilter$ = toObservable(this.competitionFilter);

  quickCompare(value: QuickCompareItems) {
    this.quickCompareSubject.next(value)
  }

  priceDiff(value: PriceDiffItems) {
    this.priceDiffSubject.next(value)
  }
}
