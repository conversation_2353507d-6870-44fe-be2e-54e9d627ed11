import { FormsModule } from '@angular/forms'
import { AfterViewInit, ChangeDetectionStrategy, Component, DestroyRef, inject, signal } from '@angular/core'
import { ActivatedRoute } from '@angular/router'
import { takeUntilDestroyed } from '@angular/core/rxjs-interop'
import { NzModalService } from 'ng-zorro-antd/modal'
import { NzAnchorComponent, NzAnchorLinkComponent } from 'ng-zorro-antd/anchor'
import { NzIconDirective } from 'ng-zorro-antd/icon'
import { addDays, format } from 'date-fns'

import { CaerusApiService } from '@api/caerus'
import { DimensionValueMenuVo } from '@api/caerus/model'
import { QueryEngineFormService } from '@common/service/query-engine'
import { PAGE_NAME, PageEnterLeaveDirective } from '@common/directive'
import { BackTopComponent } from '@shared/components/back-top'
import { NavigationComponent } from '@shared/components/navigation'
import { DateCompareComponent } from '@shared/components/date-compare'
import { RadioModule } from '@shared/modules/headless'
import { PriceDifferenceComponent, PriceMonitorComponent, PriceMonitorConfigComponent } from './container'
import { PriceService } from './price.service'
import { FilterItemVo } from '@api/caerus/model/FilterItemVo'
import { ProgressLoaderComponent } from '@shared/components/progress-loader'


@Component({
  selector: 'app-price',
  templateUrl: './price.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: {
    class: 'block',
  },
  hostDirectives: [PageEnterLeaveDirective],
  imports: [
    FormsModule,
    NzAnchorComponent,
    NzAnchorLinkComponent,
    NzIconDirective,
    RadioModule,
    NavigationComponent,
    DateCompareComponent,
    BackTopComponent,
    PriceMonitorComponent,
    PriceDifferenceComponent,
    ProgressLoaderComponent,
  ],
  providers: [{ provide: PAGE_NAME, useValue: 'price-monitor' }, QueryEngineFormService, PriceService],
})
export class PriceComponent implements AfterViewInit {
  destroyRef = inject(DestroyRef)
  readonly modal = inject(NzModalService)
  readonly formService = inject(QueryEngineFormService)
  readonly apiService = inject(CaerusApiService);
  readonly service = inject(PriceService);

  querying = signal(false)
  yesterday = signal(addDays(new Date(), -1))
  competitionOptions = signal<DimensionValueMenuVo[]>([])
  competition = signal(null);

  constructor(private route: ActivatedRoute) {
    this.route.queryParamMap.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(paramMap => {
      const dateStr = format(this.yesterday(), 'yyyy-MM-dd')
      const { st, et, compareSt, compareEt } = paramMap['params']
      const startTime = st || dateStr
      const endTime = et || dateStr
      this.formService.dt.patchValue({
        startTime,
        endTime,
      })
      if (compareSt && compareEt) {
        this.formService.addDateCompare()
        this.formService.form.get('compareDt.startTime').patchValue(compareSt)
        this.formService.form.get('compareDt.endTime').patchValue(compareEt)
      }
    })
  }


  ngAfterViewInit(): void {
    this._fetchDimensionConfig();
  }
  

  private _fetchDimensionConfig() {
    this.apiService.fetchDimensionConfig('price_monitor_dimension').subscribe(res => {
      if (res.status !== '00000') {
        return console.error(res.message || res.error)
      }

      const competition = res.data.competition.find(item => item.keyName === 'competition')

      this.competitionOptions.set(competition.values);
      this.competition.set(this.competitionOptions()[0]);
    })
  }


  handleCompetitionChange(event: DimensionValueMenuVo) {
    const { key, value, extendName } = event;

    this.service.competitionName.set(value);
    this.service.competitionFilter.set([new FilterItemVo({
      conditionType: 2,
      condition: '=',
      id: null,
      extendName,
      valueType: null,
      value: [{ key, value }]
    })]);
  }


  openPriceConfigModal(): void {
    this.modal.create({
      nzTitle: '价格监控设置',
      nzContent: PriceMonitorConfigComponent,
      nzWidth: '80%',
      nzFooter: null,
    })
  }

}
