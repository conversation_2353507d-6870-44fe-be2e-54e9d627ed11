import { <PERSON><PERSON><PERSON>r, FormArray, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { AfterViewInit, ChangeDetectionStrategy, Component, DestroyRef, inject, signal } from '@angular/core';
import { NzOptionComponent, NzSelectComponent } from 'ng-zorro-antd/select';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { map, startWith } from 'rxjs';

import { FilterItem } from '@views/monitor/price/lib';
import { IconDeleteComponent, IconPlusCircleComponent } from '@shared/modules/icons';
import { ConditionValueComponent } from './condition-value/condition-value.component';


@Component({
  selector: 'app-condition-item',
  template: `
    <div class="flex items-center gap-2" [formGroup]="form" [class.pl-14]="count() > 1">
      <nz-select class="max-w-24 min-w-16" formControlName="condition">
        @for (item of conditionOptions(); track $index) {
          <nz-option [nzValue]="item.value" [nzLabel]="item.key"></nz-option>
        }
      </nz-select>

      <ng-container formArrayName="value">
        @for (item of value.controls; track $index; let i = $index) {
          <app-condition-value class="flex-1" [formGroupName]="i" />
        }
      </ng-container>

      <div class="flex items-center gap-2 w-14">
        <PlusCircleIcon iconBtn [class.disabled]="form.disabled" (click)="addItem()" />

        @if (count() > 1) {
          <DeleteIcon iconBtn [class.disabled]="form.disabled" (click)="removeItem()" />
        }
      </div>
    </div>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    ReactiveFormsModule,
    NzSelectComponent,
    NzOptionComponent,
    ConditionValueComponent,
    IconPlusCircleComponent,
    IconDeleteComponent,
  ],
})
export class ConditionItemComponent implements AfterViewInit {
  
  parentContainer = inject(ControlContainer);
  destroyRef = inject(DestroyRef);

  count = signal(0);
  conditionOptions = signal([
    { key: '>', value: '>' },
    { key: '<', value: '<' },
    { key: '=', value: '=' },
    { key: '>=', value: '>=' },
    { key: '<=', value: '<=' },
    { key: '!=', value: '!=' },
    // { key: 'not null', value: 'not null' },
    // { key: 'in', value: 'in' },
    // { key: 'and', value: 'and' },
    // { key: 'or', value: 'or' },
  ]);

  get form() {
    return this.parentContainer.control as FormGroup<FilterItem>;
  }


  get value() {
    return this.form.controls.value;
  }


  ngAfterViewInit(): void {
    this._subscribeToParentExtendNameChange();
    this._subscribeToParentPredefineCompareTypeChange();
    this._subscribeToParentChange();
  }


  private _subscribeToParentExtendNameChange() {
    const control = (<FormGroup<FilterItem>>this.form.parent.parent).controls.extendName;

    control.valueChanges.pipe(
      startWith(control.value),
      takeUntilDestroyed(this.destroyRef)
    ).subscribe(value => {
      this.form.controls.extendName.patchValue(value);
    })
  }


  private _subscribeToParentPredefineCompareTypeChange() {
    const control = (<FormGroup<FilterItem>>this.form.parent.parent).controls.predefineCompareType;
    control.valueChanges.pipe(
      startWith(control.value),
      takeUntilDestroyed(this.destroyRef)
    ).subscribe(value => {
      this.form.controls.predefineCompareType.patchValue(value);
    })
  }
  

  private _subscribeToParentChange() {
    const control = (<FormArray<FormGroup<FilterItem>>>this.form.parent);
    
    control.valueChanges.pipe(
      startWith(control.value),
      map((value) => value.length ?? 0),
      takeUntilDestroyed(this.destroyRef)
    ).subscribe(value => {
      this.count.set(value);
    })
  }


  addItem() {
    const control = new FormGroup(new FilterItem({
      conditionType: 2,
      condition: '<',
      valueType: 'metrics',
      value: [{ key: null, value: null }]
    }));
    (<FormArray>this.form.parent).push(control);
  }


  removeItem() {
    const index = this.parentContainer.name as number;
    (<FormArray>this.form.parent).removeAt(index);
  }

}
