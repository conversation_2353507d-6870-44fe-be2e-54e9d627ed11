import { Control<PERSON>ontainer, FormGroup, ReactiveFormsModule } from '@angular/forms';
import { AfterViewInit, ChangeDetectionStrategy, Component, DestroyRef, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

import { FilterValueItem } from '@views/monitor/price/lib';
import { InputNumberComponent } from '@shared/components/input-number';
import { MetricFilterItemComponent } from '@views/monitor/price/container/price-monitor-setting/metric-threshold/metric-filter-item';
import { SubFilterComponent } from '../../sub-filter.component';


@Component({
  selector: 'app-condition-value',
  template: `
    <ng-container [formGroup]="form">
      <app-input-number
        class="w-full"
        formControlName="value"
        [suffix]="
          parent?.suffix() ||
          parent2?.suffix()
        "
      />
    </ng-container>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    ReactiveFormsModule,
    InputNumberComponent,
  ],
})
export class ConditionValueComponent implements AfterViewInit {

  parent = inject(SubFilterComponent, { optional: true});
  parent2 = inject(MetricFilterItemComponent, { optional: true});
  parentContainer = inject(ControlContainer);
  destroyRef = inject(DestroyRef);

  get form() {
    return this.parentContainer.control as FormGroup<FilterValueItem>;
  }

  ngAfterViewInit(): void {
    this.form.controls.value.valueChanges.pipe(
      takeUntilDestroyed(this.destroyRef)
    ).subscribe(value => {
      this.form.controls.key.patchValue(value);
    })
  }

}
