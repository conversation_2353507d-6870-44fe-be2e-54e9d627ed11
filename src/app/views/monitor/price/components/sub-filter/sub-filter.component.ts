import { JsonPipe } from '@angular/common'
import { FormGroup, ReactiveFormsModule } from '@angular/forms'
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  inject,
  input,
  output,
} from '@angular/core'
import { NzButtonComponent } from 'ng-zorro-antd/button'
import { NzSelectModule } from 'ng-zorro-antd/select'

import { DebugDirective } from '@shared/directives'
import { SwitchComponent } from '@shared/modules/headless/switch'
import { FilterItemVo } from '@api/query-engine/model'
import { ConditionItemComponent } from './condition-item/condition-item.component'
import { FilterItem } from '../../lib'
import { isNotNull } from '@common/function'
import { PAGE_NAME } from '@common/directive'
import { BuriedPointService } from '@common/service'

@Component({
  selector: 'app-sub-filter',
  template: `
    @if (form) {
      <header class="mb-2 text-xs">
        选择数值范围：
        <span class="whitespace-nowrap">{{ title() }}</span>
      </header>
      <div [formGroup]="form" class="flex flex-col h-full min-h-[180px] space-y-3">
        <div class="flex-1 min-h-0 overflow-auto">
          <div class="relative flex flex-col gap-y-2">
            <div
              [hidden]="subFilter.controls.length < 2"
              class="absolute inset-y-0 z-20 w-[52px] flex items-center justify-center"
            >
              <app-switch
                #switch
                formControlName="condition"
                trueValue="and"
                falseValue="or"
                class="px-2 py-0.5 rounded-full bg-orange-100 select-none cursor-pointer border-2 border-white font-medium text-xs"
                activeClass="bg-blue-100!"
              >
                {{ switch.checked ? 'AND' : 'OR' }}
              </app-switch>
            </div>

            <div
              formArrayName="subFilter"
              class="flex flex-col gap-y-3"
              [class.isAnd]="switch.checked"
              [class.fieldRuleListContainer]="subFilter.controls.length > 1"
            >
              @for (item of subFilter.controls; track item; let i = $index) {
                <app-condition-item [formGroupName]="i" />
              }
            </div>
          </div>
        </div>

        <footer class="flex justify-end gap-x-2">
          <button nz-button nzType="primary" nzSize="small" (click)="onOk()">确定</button>
          <button nz-button nzType="default" nzSize="small" (click)="onReset()">重置</button>
        </footer>
      </div>

      <pre *debug class="text-xs scale-85 origin-top-left">{{ form.value | json }}</pre>
    }
  `,
  styleUrl: './sub-filter.component.css',
  host: {
    class: 'block w-96 p-4 overflow-auto bg-white rounded-sm shadow-1',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    JsonPipe,
    ReactiveFormsModule,
    NzButtonComponent,
    NzSelectModule,
    ConditionItemComponent,
    SwitchComponent,
    DebugDirective,
  ],
})
export class SubFilterComponent implements AfterViewInit {
  readonly buriedPointService = inject(BuriedPointService)

  cdr = inject(ChangeDetectorRef)
  extendName = input.required<string>()
  title = input<string>()
  value = input<FilterItemVo>()
  suffix = input<string>(null)
  step = input<number>(null)
  valueChange = output<FilterItemVo>()
  page_name = inject(PAGE_NAME)

  form: FormGroup<FilterItem>

  get subFilter() {
    return this.form.controls.subFilter
  }

  ngAfterViewInit(): void {
    if (this.value()) {
      this.form = new FormGroup(new FilterItem(this.value() as any))
    } else {
      this.form = new FormGroup(
        new FilterItem({
          conditionType: 1,
          condition: 'and',
          valueType: 'metrics',
          extendName: this.extendName(),
          subFilter: [
            {
              conditionType: 2,
              condition: '>',
              valueType: 'metrics',
              extendName: this.extendName(),
              value: [{ key: null, value: null }],
            },
          ],
        })
      )
    }

    this.cdr.markForCheck()
  }

  onOk() {
    if (this.form.pristine) {
      this.valueChange.emit(null)
    } else {
      const value = this.form.getRawValue()

      this.valueChange.emit(value)
    }
    this.buriedPointService.addStat('dida_dpm_caerus_Price_Contrast_click', {
      page_name: this.page_name,
    })
  }

  onReset() {
    const control = new FormGroup(
      new FilterItem({
        conditionType: 2,
        condition: '>',
        valueType: 'metrics',
        extendName: this.extendName(),
        value: [{ key: null, value: null }],
      })
    )
    this.subFilter.clear()
    this.subFilter.push(control)
    this.form.markAsPristine()
    this.valueChange.emit(null)
  }
}
