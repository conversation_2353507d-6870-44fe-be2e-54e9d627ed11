@reference "../../../../../../styles.css";

app-condition-item {
  @apply relative;
}

.fieldRuleListContainer.isAnd {
  @apply before:bg-blue-300;
}

.fieldRuleListContainer.isAnd app-condition-item:first-child {
  @apply before:!border-blue-300;
}

.fieldRuleListContainer.isAnd app-condition-item:last-child {
  @apply after:!border-blue-300;
}

.fieldRuleListContainer app-condition-item:first-child {
  @apply
    before:absolute
    before:content-['']
    before:block
    before:w-6
    before:h-4
    before:top-0
    before:left-6
    before:z-0
    before:border-b-2
    before:bg-white
    before:border-orange-300;
}

.fieldRuleListContainer app-condition-item:last-child {
  @apply
    after:absolute
    after:content-['']
    after:block
    after:w-6
    after:h-4
    after:bottom-0
    after:left-6
    after:z-0
    after:border-t-2
    after:bg-white
    after:border-orange-300;
}

.fieldRuleListContainer {
  @apply
    relative
    before:absolute
    before:content-['']
    before:block
    before:w-0.5
    before:top-0
    before:bottom-0
    before:left-6
    before:bg-orange-300;
}