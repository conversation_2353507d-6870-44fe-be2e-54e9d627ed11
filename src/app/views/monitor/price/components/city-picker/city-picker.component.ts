import { OverlayModule } from '@angular/cdk/overlay'
import {
  booleanAttribute,
  ChangeDetectionStrategy,
  Component,
  computed,
  forwardRef,
  inject,
  input,
  model,
  OnInit,
  signal,
} from '@angular/core'
import { ControlValueAccessor, FormsModule, NG_VALUE_ACCESSOR } from '@angular/forms'
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox'
import { NzIconDirective } from 'ng-zorro-antd/icon'
import { NzInputModule } from 'ng-zorro-antd/input'

import { CaerusApiService } from '@api/caerus'
import { DimensionValueMenuVo } from '@api/caerus/model'
import { groupBy, isNotUndefinedOrNotNull } from '@common/function'
import { NewBaseOverlayDirective } from '@common/directive'
import { IconChevronDownComponent, IconSortDownComponent, IconSortUpComponent } from '@shared/modules/icons'
import { SortByPipe } from '@shared/pipes/sort-by'
import { SearchPipe } from '@shared/pipes/search'

@Component({
  selector: 'app-city-picker',
  template: `
    <div cdkOverlayOrigin class="city-picker" [class.disabled]="disabled()" (click)="toggle($event)">
      <span class="text-sm select-none truncate">{{ label() || placeholder() }}</span>
      <ChevronDownIcon class="ml-auto text-neutral-400" [class.rotate-180]="visible()" />
    </div>

    <ng-template
      #overlay="cdkConnectedOverlay"
      cdkConnectedOverlay
      [cdkConnectedOverlayHasBackdrop]="false"
      [cdkConnectedOverlayOrigin]="cdkOverlayOrigin()"
      [cdkConnectedOverlayPositions]="listOfPositions"
      [cdkConnectedOverlayScrollStrategy]="scrollStrategy"
      [cdkConnectedOverlayOpen]="visible()"
      (positionChange)="onPositionChange($event)"
      (overlayOutsideClick)="handleOutside($event)"
      (detach)="close()"
    >
      <ng-template #suffixIconSearch>
        <nz-icon nzType="search" />
      </ng-template>

      <div class="py-1">
        <div
          class="flex flex-col gap-y-2 min-w-[480px] h-72 bg-white border border-neutral-200 rounded-sm shadow-xl p-2"
          [style.width.px]="cdkOverlayOrigin().elementRef.nativeElement.offsetWidth"
        >
          <header class="flex items-center gap-x-2">
            <div>
              <nz-input-group [nzSuffix]="suffixIconSearch" nzSize="small">
                <input type="text" nz-input placeholder="请输入" [(ngModel)]="keyword" nzSize="small" />
              </nz-input-group>
            </div>
          </header>

          <main class="flex-1 min-h-0 overflow-auto space-y-3 bg-neutral-100/0 rounded-sm">
            @if (useReferencePrice() === false) {
              <nz-checkbox-wrapper class="w-full">
                <dl class="grid grid-cols-4 gap-1">
                  <dt
                    class="sticky top-0 z-10 flex items-start bg-white col-span-4 border-b border-neutral-200 text-xs font-bold pb-2"
                  >
                    @if (multiple()) {
                      <label
                        nz-checkbox
                        [(ngModel)]="allRegionChecked"
                        (ngModelChange)="updateAllRegionChecked()"
                        [nzIndeterminate]="regionIndeterminate()"
                      >
                        <span class="text-xs">核心区域</span>
                      </label>
                    } @else {
                      <span class="text-xs leading-5">核心区域</span>
                    }

                    @switch (regionOrder()) {
                      @case ('desc') {
                        <SortDownIcon iconBtn class="sm translate-y-px" (click)="toggleRegionOrderState()" />
                      }
                      @case ('asc') {
                        <SortUpIcon iconBtn class="sm translate-y-px" (click)="toggleRegionOrderState()" />
                      }
                    }
                  </dt>

                  @for (
                    item of regions() | search: keyword() : 'showValue' | sortBy: sortFn : regionOrder();
                    track $index
                  ) {
                    <label
                      class="ml-0!"
                      nz-checkbox
                      [nzValue]="item"
                      [(ngModel)]="item.checked"
                      (ngModelChange)="updateRegionChecked(item)"
                    >
                      <dd class="text-xs mb-0">{{ item.showValue }}</dd>
                    </label>
                  }
                </dl>
              </nz-checkbox-wrapper>
            }

            <nz-checkbox-wrapper class="w-full">
              <dl class="grid grid-cols-4 gap-1">
                <dt
                  class="sticky top-0 z-10 flex items-start gap-x-1 bg-white col-span-4 border-b border-neutral-200 text-xs font-bold pb-2"
                >
                  @if (multiple()) {
                    <label
                      nz-checkbox
                      [(ngModel)]="allCiteisChecked"
                      (ngModelChange)="updateAllCiteisChecked()"
                      [nzIndeterminate]="citeisIndeterminate()"
                    >
                      <span class="text-xs">城市</span>
                    </label>
                  } @else {
                    <span class="text-xs leading-5">城市</span>
                  }

                  @switch (citeisOrder()) {
                    @case ('desc') {
                      <SortDownIcon iconBtn class="sm translate-y-px" (click)="toggleCiteisOrderState()" />
                    }
                    @case ('asc') {
                      <SortUpIcon iconBtn class="sm translate-y-px" (click)="toggleCiteisOrderState()" />
                    }
                  }
                </dt>

                @for (
                  item of citeis() | search: keyword() : 'showValue' | sortBy: sortFn : citeisOrder();
                  track $index
                ) {
                  <label
                    class="ml-0!"
                    nz-checkbox
                    [nzValue]="item"
                    [(ngModel)]="item.checked"
                    (ngModelChange)="updateCiteisChecked(item)"
                  >
                    <dd class="text-xs mb-0">{{ item.showValue }}</dd>
                  </label>
                }
              </dl>
            </nz-checkbox-wrapper>
          </main>
        </div>
      </div>
    </ng-template>
  `,
  styleUrl: './city-picker.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: {
    class: 'inline-block align-text-top w-46',
  },
  imports: [
    FormsModule,
    OverlayModule,
    NzCheckboxModule,
    NzInputModule,
    NzIconDirective,
    SortByPipe,
    SearchPipe,
    IconChevronDownComponent,
    IconSortDownComponent,
    IconSortUpComponent,
  ],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => CityPickerComponent),
      multi: true,
    },
  ],
})
export class CityPickerComponent extends NewBaseOverlayDirective implements OnInit, ControlValueAccessor {
  apiService = inject(CaerusApiService)
  multiple = input(false, { transform: booleanAttribute })
  useReferencePrice = input(false, { transform: booleanAttribute })
  disabled = model(false)
  maxMultipleCount = input<number>(null)
  placeholder = input<string>(null)

  single = computed(() => !this.multiple())

  keyword = signal<string>(null)
  regions = signal<Array<DimensionValueMenuVo & { checked?: boolean }>>([])
  citeis = signal<Array<DimensionValueMenuVo & { checked?: boolean }>>([])
  value = signal<Array<{ key: string; value: string }>>(null)

  label = computed(() => {
    if (this.value()) {
      return this.value()
        .map(item => item.value)
        .join('、')
    }
    return ''
  })

  regionOrder = signal<'asc' | 'desc'>('asc')
  citeisOrder = signal<'asc' | 'desc'>('asc')

  allRegionChecked = signal(false)
  regionIndeterminate = computed(() => {
    const checked = this.regions().some(item => item.checked)
    const allChecked = this.regions().every(item => item.checked)

    return checked && !allChecked
  })

  allCiteisChecked = signal(false)
  citeisIndeterminate = computed(() => {
    const checked = this.citeis().some(item => item.checked)
    const allChecked = this.citeis().every(item => item.checked)

    return checked && !allChecked
  })

  constructor() {
    super()
    this.scrollStrategy = this.scrollStrategyOptions.reposition()
  }

  ngOnInit(): void {
    this.apiService.fetchDimensionConfig('price_monitor_dimension').subscribe(res => {
      if (res.status !== '00000') {
        return console.error(res.message || res.error)
      }
      const region = res.data.price_save.find(item => item.keyName === 'price_city_region')
      const city_names = res.data.price_base.find(item => item.keyName === 'price_city_name')
      const regionGroup = groupBy<DimensionValueMenuVo>(region.values, 'showGroup')

      if (this.useReferencePrice()) {
        this.citeis.set(city_names.values)
      } else {
        this.regions.set(regionGroup[1])
        this.citeis.set(regionGroup[0])
      }

      if (this.value()) {
        this.regions.update(items =>
          items.map(item => {
            item.checked = this.value().some(({ key, value }) => item.key === key && item.value === value)

            return item
          })
        )

        this.citeis.update(items =>
          items.map(item => {
            item.checked = this.value().some(({ key, value }) => item.key === key && item.value === value)

            return item
          })
        )
      }
    })
  }

  _controlValueAccessorChangeFn: (value: any) => void = () => {}
  onTouched: any = () => {}

  registerOnChange(fn: (value: any) => void) {
    this._controlValueAccessorChangeFn = fn
  }

  registerOnTouched(fn: (value: any) => void) {
    this.onTouched = fn
  }

  writeValue(value: Array<{ key: string; value: string }>) {
    if (Array.isArray(value)) {
      value = value.filter(isNotUndefinedOrNotNull)
    }

    if (value === null) {
      this.regions.update(items => items.map(item => ({ ...item, checked: false })))
      this.citeis.update(items => items.map(item => ({ ...item, checked: false })))
    } else {
      this.regions.update(items =>
        items.map(item => {
          const checked = value.some(val => val.key === item.key && val.value === item.value)
          return { ...item, checked }
        })
      )

      this.citeis.update(items =>
        items.map(item => {
          const checked = value.some(val => val.key === item.key && val.value === item.value)
          return { ...item, checked }
        })
      )
    }

    this.value.set(value ?? null)
  }

  setDisabledState(isDisabled: boolean): void {
    this.disabled.set(isDisabled)
  }

  sortFn = (items: Array<DimensionValueMenuVo & { checked?: boolean }>, order: 'asc' | 'desc') => {
    if (items) {
      switch (order) {
        case 'desc':
          return items.sort((a, b) => b.showOrder - a.showOrder)
        default:
          return items.sort((a, b) => a.showOrder - b.showOrder)
      }
    }
    return items
  }

  updateAllRegionChecked() {
    const checked = this.allRegionChecked()

    this.regions.update(items => items.map(item => ({ ...item, checked })))
    this.updateValue()
  }

  updateRegionChecked(target: DimensionValueMenuVo & { checked?: boolean }) {
    if (this.single()) {
      this.citeis.update(items => items.map(item => ({ ...item, checked: false })))
    }

    this.regions.update(items => {
      return items.map(item => {
        if (this.multiple()) {
          return { ...item, checked: item.showValue === target.showValue ? target.checked : (item?.checked ?? false) }
        } else {
          return { ...item, checked: item.showValue === target.showValue ? target.checked : false }
        }
      })
    })
    this.updateValue()
  }

  updateAllCiteisChecked() {
    const checked = this.allCiteisChecked()

    this.citeis.update(items => items.map(item => ({ ...item, checked })))
    this.updateValue()
  }

  updateCiteisChecked(target: DimensionValueMenuVo & { checked?: boolean }) {
    if (this.single()) {
      this.regions.update(items => items.map(item => ({ ...item, checked: false })))
    }

    this.citeis.update(items => {
      return items.map(item => {
        if (this.multiple()) {
          return { ...item, checked: item.showValue === target.showValue ? target.checked : (item?.checked ?? false) }
        } else {
          return { ...item, checked: item.showValue === target.showValue ? target.checked : false }
        }
      })
    })

    this.updateValue()
  }

  toggleCiteisOrderState() {
    this.citeisOrder.update(state => (state === 'asc' ? 'desc' : 'asc'))
  }

  toggleRegionOrderState() {
    this.regionOrder.update(state => (state === 'asc' ? 'desc' : 'asc'))
  }

  updateValue() {
    const regions = this.regions().filter(item => item.checked)
    const citeis = this.citeis().filter(item => item.checked)
    const value = [...regions, ...citeis].map(({ key, value }) => ({ key, value }))

    this._controlValueAccessorChangeFn(value)
    this.value.set(value)
  }

  override toggle(event: MouseEvent): void {
    if (!this.disabled()) {
      super.toggle(event)
    }
  }
}
