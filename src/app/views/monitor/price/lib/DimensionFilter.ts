import { FormGroup } from '@angular/forms';
import { DimensionFilterItem } from './DimensionFilterItem';

export class DimensionFilter {

  /** 监控城市 */
  city_region = new FormGroup(new DimensionFilterItem({
    conditionType: 2,
    condition: 'in',
    extendName: 'city_region',
    value: []
  }));

  /** 监控城市 */
  city_name = new FormGroup(new DimensionFilterItem({
    conditionType: 2,
    condition: 'in',
    extendName: 'city_name',
    value: []
  }));
  
  
  /** 价格类型 */
  price_type = new FormGroup(new DimensionFilterItem({
    conditionType: 2,
    condition: 'in',
    extendName: 'price_type'
  }));
  
  
  /** 订单类型 */
  c_ord_type = new FormGroup(new DimensionFilterItem({
    conditionType: 2,
    condition: 'in',
    extendName: 'c_ord_type'
  }));
  

  /** 城际里程 */
  inter_miage_intal = new FormGroup(new DimensionFilterItem({
    conditionType: 2,
    condition: 'in',
    extendName: 'inter_miage_intal'
  }));
  
  
  /** 市内里程 */
  inner_miage_intal = new FormGroup(new DimensionFilterItem({
    conditionType: 2,
    condition: 'in',
    extendName: 'inner_miage_intal'
  }));

  
  /** 刊例价市内里程 */
  inner_cardrate_dis = new FormGroup(new DimensionFilterItem({
    conditionType: 2,
    condition: 'in',
    extendName: 'inner_cardrate_dis'
  }));

}