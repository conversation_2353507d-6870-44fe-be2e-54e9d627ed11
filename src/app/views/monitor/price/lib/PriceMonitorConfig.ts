import { FormArray, FormControl, FormGroup, Validators } from '@angular/forms';

import { PriceMonitorConfigVo } from '@api/caerus/model';
import { DimensionValueVo } from '@api/query-engine/model';
import { DimensionFilter } from './DimensionFilter';
import { FilterItem } from './FilterItem';


export class FilterValueItem {
  key = new FormControl<string>(null);
  value = new FormControl<string>(null);

  constructor(props?: Partial<DimensionValueVo>) {
    if (props) {
      this.key.patchValue(props.key);
      this.value.patchValue(props.value);
    }
  }
}


/**
 * 指标类型
 * - ROUTE_PRICE = 1
 * - REFERENCE_PRICE = 2
 * - NORMAL_PRICE = 3
 */
export type MetricsMonitorType = 1 | 2 | 3;

export class PriceMonitorConfig {

  /**
   * @name id
   */
  id = new FormControl<number>(null);

  
  /**
   * @name 监控名称
   */
  monitorName = new FormControl<string>(null, { validators: [Validators.required] });


  /**
   * @name 对标选择 DD/GD
   */
  competition = new FormControl<string>('DD', { validators: [Validators.required], nonNullable: true });


  /**
   * @name 指标类型
   * - 1 路线价格指标
   * - 2 刊例价格指标
   * - 3 常规指标
   */
  monitorType = new FormControl<MetricsMonitorType>(1);

  
  /**
   * @name 责任人多个
   */
  personInCharge = new FormControl<string[]>([], { validators: [Validators.required], nonNullable: true });


  /**
   * @name 维度筛选 查询服务的维度filter对象
   */
  dimensionFilter = new FormGroup(new DimensionFilter());

  
  /**
   * @name 指标筛选 查询服务的指标filter对象
   */
  metricsFilter = new FormArray<FormGroup<FilterItem>>([
    new FormGroup(new FilterItem({
      conditionType: 1,
      condition: 'and',
      valueType: 'metrics',
      subFilter: [{
        conditionType: 1,
        condition: 'and',
        valueType: 'metrics',
        subFilter: [{
          conditionType: 2,
          condition: '<',
          valueType: 'metrics',
          value: [{ key: null, value: null }]
        }]
      }]
    }))
  ]);

  
  /**
   * @name 监控说明
   */
  description = new FormControl<string>(null);

  
  /**
   * @name 钉钉通知
   */
  alarmImGroup = new FormControl<string>(null);


  /**
   * @name 监控状态
   * @default false
   */
  state = new FormControl<boolean>(false, { nonNullable: true });


  constructor(props?: PriceMonitorConfigVo) {
    !props?.id && this.id.disable();
    
    if (props) {
      console.log(props);
      this.id.patchValue(props.id);
      this.monitorName.patchValue(props.monitorName);
      this.competition.patchValue(props.competition || 'DD');
      this.monitorType.patchValue(props.monitorType);
      this.personInCharge.patchValue(props.personInCharge.split(','));
      this.description.patchValue(props.description);
      this.alarmImGroup.patchValue(props.alarmImGroup);
      this.state.patchValue(Boolean(props.state));

      if (props.metricsFilter) {
        const [value] = JSON.parse(props.metricsFilter);
        const control = new FormGroup(new FilterItem(value));

        this.metricsFilter.clear();
        this.metricsFilter.push(control);
      }

      if (props.dimensionFilter) {
        const dimensionFilter = JSON.parse(props.dimensionFilter);

        Object.keys(dimensionFilter).forEach(key => {
          if (this.dimensionFilter.get(key)) {
            this.dimensionFilter.get(key).reset(dimensionFilter[key]);
          } else {
            console.log(`The ${key} is not in dimensionFilter`);
          }
        })
      }
    }
  }
  
}