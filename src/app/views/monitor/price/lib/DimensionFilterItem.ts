import { FormControl, FormArray, FormGroup, Validators } from '@angular/forms';
import { Condition, ConditionType, ValueType, FilterItemVo } from '@api/caerus/model/FilterItemVo';
import { Util } from '@common/class';


export class DimensionFilterItem {
  
  /** 
   * @name 运算类型 逻辑运算 1, 条件运算 2
   */
  conditionType = new FormControl<ConditionType>(null);
  

  /** 
   * 运算标识
   */
  condition = new FormControl<Condition>(null);

  
  /** 
   * @name 指标id/维度id
   */
  id = new FormControl<number>(null, { nonNullable: true });

  
  /** 
   * @name 筛选值类型 指标(metrics)/维度(dimension) 
   */
  valueType = new FormControl<ValueType>(null, { nonNullable: true });


  /** 
   * @name 维度英文名
   */
  extendName = new FormControl<string>(null);


  /** 
   * @name 筛选值
   */
  value = new FormControl<Array<{ key: string, value: string }>>([]);


  /** 
   * @name 子条件 [只有逻辑运算才支持]
   */
  subFilter: FormArray<FormGroup<DimensionFilterItem>>;


  /**
   * @param {*} props 初始化参数
   * @param {boolean} required value是否为必填项，默认为`true`
   */
  constructor(props?: Partial<FilterItemVo>, required = false) {
    if (required) {
      this.value.addValidators([Validators.required, Util.isEmpty]);
    }

    if (props) {
      this.conditionType.patchValue(props.conditionType ?? null);
      this.condition.patchValue(props.condition ?? null);
      this.id.patchValue(props.id ?? null);
      this.valueType.patchValue(props.valueType ?? null);
      this.extendName.patchValue(props.extendName ?? null);
      this.value.patchValue(props.value ?? []);

      if (props.subFilter) {
        this.subFilter = new FormArray<FormGroup<DimensionFilterItem>>([]);
        
        props.subFilter.forEach(item => {
          const control = new FormGroup(new DimensionFilterItem({ ...item }));
          
          this.subFilter.push(control);
        })
      }
    }
  }

}