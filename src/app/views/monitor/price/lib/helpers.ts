import { QueryOutputHeaderVo, QueryOutputVo } from '@api/query-engine/model'
import { formatValue } from '@common/function'
import { FillDataOfTrend } from '@common/class'

function getValueByPath(obj, path) {
  const keys = path.split('.')
  let currentObject = obj

  for (let key of keys) {
    if (currentObject && currentObject[key] !== undefined) {
      currentObject = currentObject[key]
    } else {
      return path
    }
  }
  return currentObject
}

export const sortBy = (order: string[], key = 'name') => {
  return function <T extends { name: string } | any>(a: T, b: T) {
    if (!key) {
      const indexA = order.indexOf(a as any)
      const indexB = order.indexOf(b as any)

      return indexA - indexB
    } else {
      const indexA = order.indexOf(getValueByPath(a, key))
      const indexB = order.indexOf(getValueByPath(b, key))

      return indexA - indexB
    }
  }
}

export const createValueElement = (value: number, template: string) => {
  if (isNaN(value)) {
    return ``
  }

  const symbol = value > 0 ? '+' : ''
  const color = value === 0 ? 'text-neutral-500' : value > 0 ? 'text-red-600' : 'text-green-500'
  const replaceValue = `${symbol}${value.toFixed(2)}`

  return `
    <span class="${color} whitespace-nowrap">
      ${template.replace('{n}', replaceValue)}
    </span>
  `
}

export function getDateFields(headers: { [key: string]: QueryOutputHeaderVo }) {
  return Object.keys(headers).filter(key => {
    return headers[key].dateFilter === 1
  })
}

export function getNumberFields(headers: { [key: string]: QueryOutputHeaderVo }) {
  return Object.keys(headers).filter(key => {
    const { dateFilter, columnType } = headers[key]

    return dateFilter === 0 && columnType !== 'dimension'
  })
}

export function getDimensionField(headers: { [key: string]: QueryOutputHeaderVo }) {
  return Object.keys(headers).filter(key => {
    const { dateFilter, columnType } = headers[key]

    return dateFilter === 0 && columnType === 'dimension'
  })
}

export function getMetricField(headers: { [key: string]: QueryOutputHeaderVo }) {
  return Object.keys(headers).filter(key => {
    const { dateFilter, columnType } = headers[key]

    return dateFilter === 0 && columnType === 'metrics'
  })
}

export function getCategories(headers: { [key: string]: QueryOutputHeaderVo }, data: { [key: string]: string }[]) {
  if (!data) {
    return
  }

  const dateFields = getDateFields(headers)
  const categories = dateFields.map(key => data.map(item => item[key])).flat(1)

  return [...new Set(categories)]
}

/**
 *
 * @param {number} value 数值
 * @param {number} ratio 比率
 * @param {string} showType 指标类型 rate 比率，frequency 频次，num 数值型
 * @returns
 */
export function getPlotLinesLabel(value: number, ratio: number, showType: 'rate' | 'frequency' | 'num') {
  const isFinite = Number.isFinite(value)
  const labelRatio = formatValue(ratio, '({n}%)')
  const labelColor = value >= 0 ? '#fb7185' : '#34d399'

  let labelText = ''

  if (showType === 'num') {
    const labelValue = Intl.NumberFormat().format(value)

    labelText = `整体涨跌 ${isFinite ? labelValue : '-'} ${isFinite ? labelRatio : '-'}`
  } else if (showType === 'rate') {
    labelText = `整体涨跌 ${(value * 100).toFixed(2)}pp`
  } else if (showType === 'frequency') {
    labelText = `整体涨跌 ${value}`
  }

  return {
    labelText,
    labelColor,
  }
}

export function mergeResult(data: QueryOutputVo, result: QueryOutputVo) {
  if (result) {
    if (!data) {
      data = result
    } else {
      if (data.data) {
        data.data = data.data.concat(result.data)
      }

      if (data.compareData) {
        data.compareData = data.compareData.concat(result.compareData)
      }

      data.headers = { ...data.headers, ...result.headers }
    }
  }

  return data
}

export function updateDataItems(dataArray: any[], key: string, updateFn: (value?: string) => string) {
  if (Array.isArray(dataArray)) {
    return dataArray.map(item => {
      item[key] = updateFn(item[key])
      return item
    })
  }
  return dataArray
}

export const processDataGroup = (
  dataGroup: any,
  headers: any,
  dt: any,
  dtType: any,
  property?: string,
  updateFn?: (value?: string) => string
) => {
  return Object.keys(dataGroup)
    .map(key =>
      new FillDataOfTrend(dataGroup[key], headers, dt).fill(dtType).map(item => {
        if (property) {
          item[property] = updateFn(key)
        }
        return item
      })
    )
    .flat(1)
}
