import { EventEmitter } from '@angular/core'
import { createPoint } from '@common/function'
import { SeriesItem as HighChartSeriesItem } from '@common/chart/highcharts'

interface Series {
  name: string
  data: number[] | SeriesItem[]
}

export class SeriesItem extends HighChartSeriesItem {
  title: string
  y: number
  diff: number
  ratio: number
  flucuate_ratio: number
  /** 指标类型 rate 比率，frequency 频次，num 数值型  */
  unit: 'rate' | 'frequency' | 'num'
}

export function generateSeriesItem(
  name: string,
  data: any[],
  dimensionName: string,
  extendName: string,
  rate: number,
  sortFn = (a, b) => 0
) {
  return {
    name,
    data: data
      .map(item => {
        const series = new SeriesItem()
        series.title = name
        series.name = item[dimensionName]
        series.y = parseFloat((Number(item[extendName]) * rate).toFixed(2))
        series.diff = Number(item[extendName] + '_DIFF')
        return series
      })
      .sort(sortFn),
  }
}

export class Bar {
  chart = { type: 'column', spacing: [40, 20, 15, 20] }
  colors = [
    '#2478f2',
    '#2478f2',
    '#2478f2',
    '#2478f2',
    '#2478f2',
    '#2478f2',
    '#2478f2',
    '#2478f2',
    '#2478f2',
    '#2478f2',
    '#2478f2',
    '#2478f2',
    '#2478f2',
  ]
  credits = { enabled: false }

  title = {
    text: '',
  }

  subtitle = {
    text: '',
    style: {
      color: '#666',
    },
  }

  legend = {
    enabled: undefined,
  }

  xAxis = {
    categories: ['USA', 'China'],
    crosshair: true,
    accessibility: {
      description: 'Countries',
    },
  }

  yAxis = {
    min: 0,
    title: false,
  }

  series = [
    {
      name: 'Corn',
      data: [387749, 280000],
    },
  ]
  click = new EventEmitter<any>()

  plotOptions: {
    column: {
      pointPadding: 0.2
      borderWidth: 0
    }
  }

  tooltip = {
    shared: false,
    valueSuffix: '',
  }

  // setSeries(name: string, data: number[] | SeriesItem[]) {
  //   this.series = [{ name, data }]
  // }

  getOption() {
    return {
      ...this,
    }
  }
}

export function tooltipFormatterFn(params: any) {
  const { name, color, diff, ratio, flucuate_ratio, unit } = this.point as SeriesItem
  const labelColor = diff >= 0 ? '#fb7185' : '#34d399'
  const prefix = diff >= 0 ? '+' : ''
  const visible = unit === 'num'
  const suffix = unit === 'rate' ? 'pp' : ''

  return `
    <div class="text-xs p-1.5">
      <div>${name}</div>
      <div class="flex items-center gap-x-1">
        ${createPoint(color)}
        较对比期 <span style="color: ${labelColor}">${prefix}${diff}${suffix}</span>
        <span class="${visible ? '' : 'hidden'}">
          (<span style="color: ${labelColor}">${prefix}${ratio}%</span>)
        </span>
      </div>

      <div class="flex items-center gap-x-1 ${visible ? '' : 'hidden'}">
        ${createPoint(color)}
        波动占比 <span style="color: ${labelColor}">${prefix}${flucuate_ratio}%</span>
      </div>
    </div>
  `
}
