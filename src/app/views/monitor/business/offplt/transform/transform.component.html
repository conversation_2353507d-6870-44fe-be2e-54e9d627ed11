<div class="px-5 flex flex-col py-3">
  <header
    class="relative flex items-center gap-x-1 px-5 h-12 border-l-4 border-emerald-500 bg-linear-to-r from-neutral-300/40 via-white via-20%"
  >
    <span class="text-lg">新用户转化漏斗</span>
    <app-radio-group
      [(ngModel)]="detailType"
      class="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 flex items-start gap-6"
      (ngModelChange)="changeDetailType()"
    >
      <app-radio
        class="line-radio text-xs whitespace-nowrap"
        activeClass="active"
        value="activation"
        nz-popover
        nzPopoverTitle="提示"
        nzPopoverContent="新激活用户转化漏斗仅支持全国粒度查看"
        [nzPopoverTrigger]="service.area() === null ? null : 'hover'"
        [disabled]="service.area()"
      >
        新激活用户转化漏斗
      </app-radio>
      <app-radio class="line-radio text-xs whitespace-nowrap" activeClass="active" value="register">
        新注册用户转化漏斗
      </app-radio>
      @if (service.type() === 'driver') {
        <app-radio class="line-radio text-xs whitespace-nowrap" activeClass="active" value="identification">
          新认证用户转化漏斗
        </app-radio>
      }
    </app-radio-group>
  </header>
  <div class="text-xs text-neutral-400 py-2">
    说明：此处指标均为自有渠道数据；统计日期: 首层漏斗的行为时间，首层漏斗是激活的，即按激活时间统计。
  </div>
  <div class="flex flex-col gap-3">
    <div class="flex items-center gap-10">
      <app-date-filter [label]="label()" />

      <div class="flex items-center">
        <ng-template #metricsTitleTemplate5>
          {{ _config()?.timestampBizExpression?.showName }}
          <span class="text-xs opacity-30 px-1">({{ _config()?.timestampBizExpression?.aliasName }})</span>
        </ng-template>

        <ng-template #metricsContentTemplate5>
          <div class="leading-normal" [innerHTML]="_config()?.timestampBizExpression?.bizExpression"></div>
        </ng-template>
        <label
          nz-popover
          [nzPopoverMouseEnterDelay]="0.5"
          [nzPopoverTitle]="metricsTitleTemplate5"
          [nzPopoverContent]="metricsContentTemplate5"
          class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap"
        >
          转化周期：
        </label>
        <app-radio-group class="relative flex gap-1" [(ngModel)]="transform_date" (ngModelChange)="reRender()">
          @for (item of _config()?.timestamp; track item) {
            <app-radio class="tag-radio" activeClass="active" [value]="item.extendName">
              {{ item.showName }}
            </app-radio>
          }
        </app-radio-group>
      </div>
    </div>
    <div class="flex items-center gap-10 px-2">
      <div class="flex items-center">
        <ng-template #metricsTitleTemplate>
          {{ _config()?.filter?.promt_channel_source_type?.name }}
          <span class="text-xs opacity-30 px-1">({{ _config()?.filter?.promt_channel_source_type?.aliasName }})</span>
        </ng-template>

        <ng-template #metricsContentTemplate>
          <div class="leading-normal" [innerHTML]="_config()?.filter?.promt_channel_source_type?.bizExpression"></div>
        </ng-template>
        <label
          nz-popover
          [nzPopoverMouseEnterDelay]="0.5"
          [nzPopoverTitle]="metricsTitleTemplate"
          [nzPopoverContent]="metricsContentTemplate"
          class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap"
        >
          来源类型：
        </label>
        <app-radio-group
          class="relative flex gap-1"
          [(ngModel)]="promt_channel_source_type"
          (ngModelChange)="changeSource()"
        >
          <app-radio class="tag-radio" activeClass="active" [value]="null">全部</app-radio>
          @for (item of _config()?.filter?.promt_channel_source_type?.values; track item) {
            <app-radio class="tag-radio" activeClass="active" [value]="item">
              {{ item.showValue }}
            </app-radio>
          }
        </app-radio-group>
      </div>
      <div class="flex items-center">
        <ng-template #metricsTitleTemplate2>
          {{ _config()?.filter?.media_type?.name }}
          <span class="text-xs opacity-30 px-1">({{ _config()?.filter?.media_type?.aliasName }})</span>
        </ng-template>

        <ng-template #metricsContentTemplate2>
          <div class="leading-normal" [innerHTML]="_config()?.filter?.media_type?.bizExpression"></div>
        </ng-template>
        <label
          nz-popover
          [nzPopoverMouseEnterDelay]="0.5"
          [nzPopoverTitle]="metricsTitleTemplate2"
          [nzPopoverContent]="metricsContentTemplate2"
          class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap"
        >
          推广渠道类型：
        </label>
        <app-radio-group
          class="relative flex gap-1"
          [(ngModel)]="media_type"
          (ngModelChange)="activate_shop_source.set(null); reRender()"
        >
          <app-radio class="tag-radio" activeClass="active" [value]="null">全部</app-radio>
          @if (promt_channel_source_type()?.value === '推广') {
            @for (item of _config()?.filter?.media_type?.values; track item) {
              <app-radio class="tag-radio" activeClass="active" [value]="item">
                {{ item.showValue }}
              </app-radio>
            }
          }
        </app-radio-group>
      </div>
      <div class="flex items-center">
        <label class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap">
          产品渠道：
        </label>
        <app-radio-group class="relative flex gap-1" [(ngModel)]="index" (ngModelChange)="reRender()">
          @for (item of _config()?.metrics; track item) {
            <ng-template #metricsTitleTemplate>
              {{ item?.aliasName }}
            </ng-template>
            <ng-template #metricsContentTemplate>
              <div class="leading-normal" [innerHTML]="item?.bizExpression"></div>
            </ng-template>
            <app-radio
              nz-popover
              class="tag-radio"
              activeClass="active"
              [value]="$index"
              [nzPopoverMouseEnterDelay]="0.5"
              [nzPopoverTitle]="metricsTitleTemplate"
              [nzPopoverContent]="metricsContentTemplate"
            >
              {{ item.showName }}
            </app-radio>
          }
        </app-radio-group>
      </div>
      @if (detailType() === 'activation') {
        <div class="flex items-center">
          <ng-template #metricsTitleTemplateshop>
            {{ _config()?.filter?.activate_shop_source?.name }}
            <span class="text-xs opacity-30 px-1">({{ _config()?.filter?.activate_shop_source?.aliasName }})</span>
          </ng-template>

          <ng-template #metricsContentTemplateshop>
            <div class="leading-normal" [innerHTML]="_config()?.filter?.activate_shop_source?.bizExpression"></div>
          </ng-template>
          <label
            nz-popover
            [nzPopoverMouseEnterDelay]="0.5"
            [nzPopoverTitle]="metricsTitleTemplateshop"
            [nzPopoverContent]="metricsContentTemplateshop"
            class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap"
          >
            应用商店来源：
          </label>
          <app-radio-group class="relative flex gap-1" [(ngModel)]="activate_shop_source" (ngModelChange)="reRender()">
            <app-radio class="tag-radio" activeClass="active" [value]="null">全部</app-radio>
            @if (media_type() === null || media_type()?.value === '商店') {
              @for (item of _config()?.filter.activate_shop_source?.values; track item) {
                <app-radio class="tag-radio" activeClass="active" [value]="item">
                  {{ item.showValue }}
                </app-radio>
              }
            }
          </app-radio-group>
        </div>
      }
    </div>
    <div class="grid grid-cols-12 gap-x-5 mb-5">
      <div class="col-span-4">
        <div class="flex flex-col h-96 shadow-md rounded-sm border border-neutral-100 relative">
          @if (
            service.area() &&
            ((detailType() === 'activation' && service.type() !== 'passenger' && (index() === 0 || index() === 1)) ||
              (service.type() === 'passenger' && index() === 0))
          ) {
            <div class="text-xs text-neutral-400 pt-4 absolute px-4">
              说明：由于大部分用户的激活行为定位不到城市，区域视角下的激活漏斗数据不可用，下期会把区域视角下的激活漏斗改为注册漏斗。
            </div>
          }

          <div class="w-full">
            @if (funnel_loading()) {
              <div class="pt-25">
                <app-line-spin />
              </div>
            } @else {
              <app-chart-funnel [value]="funnelSeries()" (click)="clickChart($event)" />
            }
          </div>
        </div>
      </div>
      <div class="col-span-8">
        <div class="flex flex-col h-96 shadow-md rounded-sm border border-neutral-100">
          <div class="flex items-center gap-6 px-4 py-3">
            <div>已选指标：【{{ currentMetric() }}】</div>
            <div class="flex items-center">
              <label class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap">
                来源渠道：
              </label>
              <div>
                <nz-checkbox-group [(ngModel)]="checkValue" (ngModelChange)="_getChartData()">
                  <label nz-checkbox class="text-xs! ml-0!" [nzValue]="null">全部</label>
                  @for (item of _config()?.filter?.promt_channel_source_type?.values; track item) {
                    <label nz-checkbox class="text-xs! ml-0!" [nzValue]="item">
                      {{ item.showValue }}
                    </label>
                  }
                </nz-checkbox-group>
              </div>
            </div>
          </div>
          <div class="text-xs text-neutral-400 px-4">
            说明：点击左侧漏斗的彩色条，可自动选择彩色条对应的指标和下一级指标，以及两级指标的转化率。点击图例默认单选，长按shift键点击可多选。
          </div>
          <div class="flex-1 min-h-0 relative flex items-center justify-center text-xs">
            @if (loading()) {
              <app-line-spin />
            } @else {
              @if (option()) {
                <app-graph class="absolute inset-2" [options]="option()" showAnnotations showAvgPlotLines />
              } @else if (errorMessage()) {
                <span class="text-slate-600 text-xs">{{ errorMessage() }}</span>
              }
            }
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
