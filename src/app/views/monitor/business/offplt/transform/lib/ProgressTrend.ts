import { XAxisOptions, YAxisOptions } from 'highcharts';
import {
  getCategories,
  getNumberFields,
  getPercentFields,
  isEveryElementPercent,
} from '@common/chart';
import { BaseHighCharts, SeriesItem } from '@common/chart/highcharts';
import {
  createPoint,
  createValueElement,
  isEmpty,
  toDecimals,
} from '@common/function';
import { QueryOutputHeaderVo, QueryOutputVo } from '@api/query-engine/model';
import { flattenDeep } from 'lodash';

function tooltipFormatter(that: ProgressTrend) {
  return function () {
    const result = [];
    const map = new Map();
    const params = this.points.sort(that.sortFn);

    params.forEach((item) => {
      if (map.has(item.series.name)) {
        const arr = map.get(item.series.name) as any[];
        arr.push(item);
        arr.reverse();
      } else {
        map.set(item.series.name, [item]);
      }
    });

    const merged = [...map.values()].flat(1);
    const sort1 = [null, '_自然', '_推广'];
    const sort2 = [null, '率'];
    const arr1 = sort1.map((s) => {
      return merged.filter((m) => {
        if (s) {
          return m.series.name.includes(s);
        } else {
          return (
            !m.series.name.includes('_自然') && !m.series.name.includes('_推广')
          );
        }
      });
    });
    const arr2 = arr1.map((a) => {
      return sort2.map((s) => {
        return a.filter((m) => {
          if (s) {
            return m.series.name.includes(s);
          } else {
            return !m.series.name.includes('率');
          }
        });
      });
    });
    result.push('<table class="text-sm">');
    flattenDeep(arr2).forEach((point, index) => {
      const {
        series: {
          name: seriesName,
          yAxis: { index: yAxisIndex },
        },
        y: value,
        color,
      } = point;
      const { isPercent } = that.series.find(
        (item) => item.name === seriesName
      );
      const categorie = point.x;
      const previousItem = merged[index - 1];
      const isDateStr = /\d{4}-\d{2}-\d{2}/.test(categorie);
      const day = new Date(categorie.replace(/-/g, '/')).getDay();
      const weeks = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
      const week = isDateStr && weeks[day];
      const isCompletion = /完成度$/.test(seriesName);

      if (previousItem?.series.name === seriesName) {
        const currentValue = value;
        const previousValue = previousItem.y;
        const ratioValue = toDecimals(
          (currentValue - previousValue) / previousValue
        );
        const diffValue = currentValue - previousValue;

        result.push(`
          <tr>
            <td class="flex items-center" style="color:${color}">
              ${createPoint(color)}
              (
                ${categorie}
                ${week ? '<span class="flex-1 min-w-0 w-1"></span>' : ''}
                ${week || ''}
              )
            </td>
            <td class="pr-2" style="color:${color}">${seriesName}: </td>
            <td class="text-right">
              ${
                yAxisIndex === 1 || isPercent
                  ? Number.isFinite(value)
                    ? value + '%'
                    : '-'
                  : Number.isFinite(value)
                  ? Intl.NumberFormat().format(value)
                  : '-'
              }
            </td>
          </tr>
        `);
      } else {
        result.push(`
          <tr>
            <td class="flex items-center" style="color:${color}">
              ${createPoint(color)}
              (${categorie}${
          week ? '<span class="flex-1 min-w-0 w-1"></span>' : ''
        }${week || ''})
            </td>
            <td class="pr-2" style="color:${color}">${seriesName}: </td>
            <td class="text-right ${
              isCompletion && value < 100 ? 'text-red-500' : ''
            }">
              ${
                yAxisIndex === 1 || isPercent
                  ? Number.isFinite(value)
                    ? value + '%'
                    : '-'
                  : Number.isFinite(value)
                  ? Intl.NumberFormat().format(value)
                  : '-'
              }
            </td>
            <td></td>
          </tr>
        `);
      }
    });
    result.push('</table>');

    return result.join('');
  };
}

class xAxisItem {
  categories: string[];
  opposite: boolean;
  tickInterval = 1;
  tickWidth = 1;
  tickColor = '#ccd6eb';
  lineColor = '#ccd6eb';
  gridLineColor = '#e6e6e6';
  crosshair = true;
  linkedTo: number;
  labels = {
    useHTML: true,
    formatter: function () {
      if (/\d{4}-\d{2}-\d{2}/.test(this.value)) {
        const day = new Date(this.value.replace(/-/g, '/')).getDay();
        if (day === 0 || day === 6) {
          return `<span class="text-primary font-semibold">${this.value}</span>`;
        }
      }

      return this.value;
    },
  };

  constructor({ categories, opposite }: Partial<xAxisItem>) {
    categories && (this.categories = categories);
    opposite && (this.opposite = opposite);
  }
}

export class ProgressTrend extends BaseHighCharts {
  xAxis: XAxisOptions = {};
  yAxis: YAxisOptions[] = [
    {
      title: { text: '' },
      labels: { format: undefined },
      gridLineWidth: 1,
      gridLineColor: '#e6e6e6',
    },
  ];

  colors = [
    'rgba(80,135,236,0.7)',
    'rgba(104,187,196,0.7)',
    'rgba(88,165,92,0.7)',
    'rgba(242,189,66,0.7)',
    'rgba(238,117,47,0.7)',
    'rgba(176,102,93,0.7)',
    'rgba(228,196,119,0.7)',
    'rgba(163,194,251,0.7)',
    'rgba(160,208,213,0.7)',
    'rgba(152,179,226,0.7)',
    'rgba(222,134,143,0.7)',
    'rgba(244,206,152,0.7)',
    'rgba(180,200,217,0.7)',
    'rgba(147,210,243,0.7)',
    'rgba(64,149,229,0.7)',
    'rgba(127,131,247,0.7)',
    '#E99D42',
    '#CBA43F',
    '#BFBF3D',
    '#81B337',
    '#347CAF',
    '#377F7F',
    '#FCCA00',
    '#B886F8',
    '#A16222',
    '#5087EC',
    '#68BBC4',
    '#58A55C',
    '#F2BD42',
    '#EE752F',
    '#B0665D',
    '#E4C477',
    '#A3C2FB',
    '#A0D0D5',
    '#98B3E2',
    '#DE868F',
    '#F4CE98',
    '#B4C8D9',
    '#93D2F3',
    '#4095E5',
    '#7F83F7',
  ];

  plotOptions = {
    series: {
      turboThreshold: *********,
      marker: {
        radius: 2,
        symbol: 'circle',
      },
    } as any,
  };

  seriesSortFn = (a: SeriesItem, b: SeriesItem) => {
    if (a.name.includes('目标') && !b.name.includes('目标')) {
      return -1; // a排在前面
    }
    if (!a.name.includes('目标') && b.name.includes('目标')) {
      return 1; // b排在前面
    }

    if (a.name.includes('完成度') && !b.name.includes('完成度')) {
      return 1; // a排在后面
    }
    if (!a.name.includes('完成度') && b.name.includes('完成度')) {
      return -1; // b排在后面
    }

    return 0; // 如果没有关键字的区别，维持原有顺序
  };

  /**
   * Constructs a new instance of the LineMultipleXAxis class.
   *
   * @param properties - The properties used to initialize the LineMultipleXAxis instance.
   * @throws {string} Throws an error if the x-axis is empty.
   */
  constructor(properties: QueryOutputVo, debug?: boolean) {
    super();

    const { headers, data } = properties;
    const xaxis = getCategories(headers, data);
    const _series = this.getSeries(headers, data, 0);

    const sort1 = [null, '_自然', '_推广'];
    const sort2 = [null, '率'];
    const arr1 = sort1.map((s) => {
      return _series.filter((m) => {
        if (s) {
          return m.name.includes(s);
        } else {
          return !m.name.includes('_自然') && !m.name.includes('_推广');
        }
      });
    });
    const arr2 = arr1.map((a) => {
      return sort2.map((s) => {
        return a.filter((m) => {
          if (s) {
            return m.name.includes(s);
          } else {
            return !m.name.includes('率');
          }
        });
      });
    });
    const series = flattenDeep(arr2);
    const [percentFields] = getPercentFields(headers);
    const isEveryFieldPercent = isEveryElementPercent(headers);

    if (debug) {
      debugger;
    }

    if (isEmpty(xaxis)) {
      throw 'x轴不能为空';
    }

    if (isEveryFieldPercent) {
      this.yAxis = [
        {
          title: { text: '' },
          labels: { format: '{text}%' },
          gridLineWidth: 1,
          gridLineColor: '#e6e6e6',
        },
      ];
    } else if (percentFields) {
      this.yAxis.push({
        title: { text: '' },
        labels: { format: '{text}%' },
        gridLineWidth: 1,
        gridLineColor: '#e6e6e6',
        opposite: true,
      });
    }

    this.setCategories([xaxis]);
    this.setSeries(series);

    this.legend.verticalAlign = 'top';
    this.responsive = null;
  }

  getSeries(
    headers: { [key: string]: QueryOutputHeaderVo },
    data: { [key: string]: string }[],
    index?: number
  ) {
    const numberFields = getNumberFields(headers);
    const isEveryFieldPercent = isEveryElementPercent(headers);
    const source = ['当前', '对比'];

    if (!data) {
      throw `${source[index] ?? ''}日期无数据`;
    }

    return numberFields.map((key) => {
      const { aliasName, dataUnit } = headers[key];
      const series = new SeriesItem();

      if (index > 0) {
        series.dashStyle = 'Dash';
        series.lineWidth = 1;
        series.linkedTo = `${key}`;
      }

      if (dataUnit === '%') {
        series.isPercent = true;
        series.type = 'column';
      } else {
        series.type = 'line';
        series.zIndex = 2;
        if (aliasName.endsWith('目标')) {
          series.lineWidth = 1;
          series.dashStyle = 'ShortDash';
        }
      }

      if (dataUnit === '%' && !isEveryFieldPercent) {
        series.yAxis = 1;
      }

      series.xAxis = index;
      series.name = aliasName;
      series.data = data.map((item) => {
        const value = Number(item[key]);

        if (item[key] === null) {
          return null;
        }

        if (dataUnit === '%') {
          return toDecimals(value);
        }

        return value;
      });

      return series;
    });
  }

  /**
   * Sets the categories for the multiple x-axis.
   *
   * @param values - The array of string arrays representing the categories.
   */
  setCategories(values: string[][]): void {
    values.forEach((categories, index) => {
      const item = new xAxisItem({
        categories,
        opposite: index > 0,
      });

      if (index > 0) {
        item.linkedTo = 0;
      }

      this.xAxis = item;
    });
  }

  override getOption() {
    const value = this;

    return {
      ...value,
      tooltip: {
        ...value.tooltip,
        formatter: tooltipFormatter(this),
      },
      plotOptions: {
        ...value.plotOptions,
        series: {
          ...value.plotOptions.series,
        },
      },
    };
  }
}
