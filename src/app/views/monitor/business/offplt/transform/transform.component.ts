import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  computed,
  DestroyRef,
  inject,
  input,
  OnChanges,
  signal,
} from '@angular/core'
import { QueryEngineFormService } from '@common/service/query-engine'
import { DateFilterComponent } from '../../components'
import { RadioModule } from '@shared/modules/headless'
import { FormsModule } from '@angular/forms'
import { ChartFunnelComponent } from '@shared/components/chart'
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox'
import { BaseHighCharts } from '@common/chart/highcharts'
import { QueryOutputVo } from '@api/query-engine/model'
import { ProgressTrend } from './lib'
import { LegendControlService, LegendItemClickHandler } from '@common/service'
import { LineSpinComponent } from '@shared/components/line-spin'
import { GraphComponent } from '@shared/components/graph'
import { BusinessService } from '../../business.service'
import { combineLatest, debounceTime, finalize, startWith } from 'rxjs'
import { takeUntilDestroyed } from '@angular/core/rxjs-interop'
import { QueryEngineApiService } from '@api/query-engine'
import { NzPopoverModule } from 'ng-zorro-antd/popover'
import { flattenDeep } from 'lodash'

@Component({
  selector: 'app-transform',
  imports: [
    DateFilterComponent,
    RadioModule,
    FormsModule,
    ChartFunnelComponent,
    NzCheckboxModule,
    LineSpinComponent,
    GraphComponent,
    NzPopoverModule,
  ],
  templateUrl: './transform.component.html',
  styleUrl: './transform.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [QueryEngineFormService, LegendControlService],
})
export class TransformComponent implements AfterViewInit, OnChanges {
  readonly legendControlService = inject(LegendControlService)
  readonly service = inject(BusinessService)
  readonly formService = inject(QueryEngineFormService)
  readonly destroyRef = inject(DestroyRef)
  readonly queryService = inject(QueryEngineApiService)

  config = input(null)

  detailType = signal('activation')
  transform_date = signal(null)
  funnelSeries = signal([])
  loading = signal(false)
  errorMessage = signal(null)
  option = signal<BaseHighCharts>(null)
  promt_channel_source_type = signal(null)
  media_type = signal(null)
  activate_shop_source = signal(null)
  index = signal(0)
  funnel_loading = signal(false)
  checkValue = signal([null])
  currentMetricIndex = signal(0)
  currentMetric = signal('')
  dtType = signal('dt')
  _config = signal(null)

  label = computed(() => {
    const type = this.service.type()
    const detailType = this.detailType()
    const index = this.index()

    if (detailType === 'register') return '注册日期'

    if (type === 'driver' && detailType === 'identification' && index === 0) return '认证日期'

    if (detailType === 'activation' && index <= 1) return '激活日期'

    if (type !== 'driver' && index === 0) return '激活日期'

    if (detailType === 'activation' || type !== 'driver') return '注册日期'

    return '意向日期'
  })

  ngAfterViewInit(): void {
    this._subscribeChange()
  }

  ngOnChanges(): void {
    if (this.config()) {
      this.detailType.set('activation')
      this._config.set(this.config()[this.detailType()])
      this.transform_date.set(this._config().timestamp[0]?.extendName)
    }
  }

  changeDetailType() {
    if (!this.config()) {
      return
    }
    this._config.set(this.config()[this.detailType()])
    this.media_type.set(null)
    this.promt_channel_source_type.set(null)
    this.index.set(0)
    this.checkValue.set([null])
    this.activate_shop_source.set(null)
    this.reRender()
  }

  _getFunnelData() {
    if (!this._config()) {
      return
    }
    this.funnel_loading.set(true)
    const body = this.formService.value()
    const area = this.service.area()

    body.queryType = 'compare'
    body.dataFillConfig = {
      open: 0,
      fillRangeType: 1,
      fillValue: 1,
    }
    body.dimensions = []
    const filters = [area, this.promt_channel_source_type(), this.media_type(), this.activate_shop_source()]
    const items = filters
      .filter(f => f)
      .map(f => {
        return {
          conditionType: 2,
          condition: '=',
          id: null,
          extendName: f.extendName,
          value: [
            {
              key: f.key,
              value: f.value,
            },
          ],
          valueType: null,
        }
      })
    body.filter = {
      items,
      type: null,
    }
    const metrics = this._config().metrics[this.index()]
    console.log('metrics==', metrics)
    const arr = [metrics.level[this.currentMetricIndex()].userDefAliasName]
    if (this.currentMetricIndex() + 1 < metrics.level.length) {
      arr.push(metrics.level[this.currentMetricIndex() + 1].userDefAliasName)
      arr.push(metrics.level[this.currentMetricIndex() + 1].rateAliasName)
    }
    this.currentMetric.set(arr.join('，'))
    body.metrics = metrics.level.map((l, i) => {
      return {
        extendName: metrics.extendName,
        userDefExtendName: `${metrics.extendName}_${i + 1}`,
        userDefAliasName: l.userDefAliasName,
        filter: {
          items: [
            {
              conditionType: 2,
              condition: '>=',
              extendName: this.transform_date(),
              value: [
                {
                  key: `${i + 1}`,
                  value: `${i + 1}`,
                },
              ],
              valueType: null,
            },
          ],
          type: null,
        },
      }
    })
    this.queryService
      .search(body, 'Funnel-data')
      .pipe(finalize(() => this.funnel_loading.set(false)))
      .subscribe(res => {
        // console.log('Funnel-data', res);
        if (res.data && res.data.data.length !== 0) {
          const data = res.data.data[0]
          const FunnelData = metrics.level.map((l, i) => {
            return {
              name: l.userDefAliasName,
              leftLabel: l.userDefAliasName,
              rightLabel: l.rateAliasName === '' ? l.userDefAliasName : l.rateAliasName,
              value: Number(data[`${metrics.extendName}_${i + 1}`]),
            }
          })
          this.funnelSeries.set(FunnelData)
        } else {
          this.funnelSeries.set([])
        }
      })
  }

  _getChartData() {
    if (!this._config()) {
      return
    }
    this.loading.set(true)
    const body = this.formService.value()
    const area = this.service.area()

    body.queryType = 'compare'
    body.dataFillConfig = {
      open: 0,
      fillRangeType: 1,
      fillValue: 1,
    }
    body.dimensions = [
      {
        id: null,
        extendName: this.dtType(),
        predefineCompareType: this.dtType() === 'yw' ? null : ['ym', 'year'],
      },
    ]
    const filters = [area, this.media_type(), this.activate_shop_source()]
    const items = filters
      .filter(f => f)
      .map(f => {
        return {
          conditionType: 2,
          condition: '=',
          id: null,
          extendName: f.extendName,
          value: [
            {
              key: f.key,
              value: f.value,
            },
          ],
          valueType: null,
        }
      })
    body.filter = {
      items,
      type: null,
    }
    const metrics = this._config().metrics[this.index()]
    const arr = metrics.level.slice(this.currentMetricIndex(), this.currentMetricIndex() + 2)
    if (arr.length !== 1) {
      body.userDefExpressions = flattenDeep(
        this.checkValue().map((check, i) => {
          return {
            userDefAliasName: `${arr[arr.length - 1].rateAliasName}_${check ? check.value : '全部'}`,
            userDefExtendName: `${metrics.chartExtendName}_rate_${i + 1}_${check ? check.key : 'all'}`,
            expression: `${metrics.chartExtendName}_2_${
              check ? check.key : 'all'
            }/${metrics.chartExtendName}_1_${check ? check.key : 'all'}`,
          }
        })
      )
    }
    body.metrics = flattenDeep(
      this.checkValue().map(check => {
        return arr.map((item, i) => {
          return {
            extendName: metrics.chartExtendName,
            userDefExtendName: `${metrics.chartExtendName}_${i + 1}_${check ? check.key : 'all'}`,
            userDefAliasName: `${item.userDefAliasName}_${check ? check.value : '全部'}`,
            filter: {
              items: check
                ? [
                    {
                      conditionType: 2,
                      condition: '=',
                      id: null,
                      extendName: check.extendName,
                      value: [
                        {
                          key: check.key,
                          value: check.value,
                        },
                      ],
                      valueType: null,
                    },
                    {
                      conditionType: 2,
                      condition: '>=',
                      extendName: this.transform_date(),
                      value: [
                        {
                          key: item.levelNum,
                          value: item.levelNum,
                        },
                      ],
                      valueType: null,
                    },
                  ]
                : [
                    {
                      conditionType: 2,
                      condition: '>=',
                      extendName: this.transform_date(),
                      value: [
                        {
                          key: item.levelNum,
                          value: item.levelNum,
                        },
                      ],
                      valueType: null,
                    },
                  ],
              type: null,
            },
          }
        })
      })
    )

    this.legendControlService.reset()
    this.queryService
      .search(body, 'transform-chart')
      .pipe(finalize(() => this.loading.set(false)))
      .subscribe(res => {
        // console.log('transform-chart-data', res);
        if (res.data) {
          this._setChartData(res.data)
        } else {
          this.option.set(null)
          this.errorMessage.set(res.message)
        }
      })
  }

  reRender() {
    this._getFunnelData()
    this._getChartData()
  }

  clickChart(params) {
    if (params.data) {
      this.currentMetricIndex.set(params.dataIndex)
      const metrics = this._config().metrics[this.index()]
      const arr = [metrics.level[this.currentMetricIndex()].userDefAliasName]
      if (this.currentMetricIndex() + 1 < metrics.level.length) {
        arr.push(metrics.level[this.currentMetricIndex() + 1].userDefAliasName)
        arr.push(metrics.level[this.currentMetricIndex() + 1].rateAliasName)
      }
      this.currentMetric.set(arr.join('，'))
      this._getChartData()
    }
  }

  private _subscribeChange() {
    combineLatest([this.formService.form.valueChanges])
      .pipe(debounceTime(100), takeUntilDestroyed(this.destroyRef))
      .subscribe(([a]) => {
        this.dtType.set(a.dtType)
        this._getFunnelData()
        this._getChartData()
      })

    this.service.type$.pipe(debounceTime(100), takeUntilDestroyed(this.destroyRef)).subscribe(() => {
      this.promt_channel_source_type.set(null)
      this.media_type.set(null)
      this.index.set(0)
      if (this.service.area() !== null) {
        this.detailType.set('register')
      } else {
        this.detailType.set('activation')
      }

      setTimeout(() => {
        this.reRender()
      }, 800)
    })

    this.service.area$.pipe(debounceTime(100), takeUntilDestroyed(this.destroyRef)).subscribe(a => {
      if (a !== null) {
        if (this.detailType() === 'activation') {
          this.detailType.set('register')
          this.changeDetailType()
        } else {
          this._getFunnelData()
          this._getChartData()
        }
      } else {
        this._getFunnelData()
        this._getChartData()
      }
    })
  }

  changeSource() {
    this.checkValue.set([this.promt_channel_source_type()])
    this.media_type.set(null)
    this.reRender()
  }

  private _setChartData(data: QueryOutputVo) {
    try {
      const chart = new ProgressTrend(data)
      const legendItemClick = LegendItemClickHandler(this.legendControlService)
      chart.plotOptions.series.events = { legendItemClick }
      this.option.set(chart?.getOption() || null)
    } catch (e) {
      this.option.set(null)
      this.errorMessage.set(e)
      console.error(e)
    }
  }
}
