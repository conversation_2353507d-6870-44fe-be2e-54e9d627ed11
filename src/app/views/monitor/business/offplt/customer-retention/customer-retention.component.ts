import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  computed,
  DestroyRef,
  inject,
  input,
  signal,
} from '@angular/core'
import { FormsModule } from '@angular/forms'
import { RadioModule } from '@shared/modules/headless'
import { DateFilterComponent } from '../../components'
import { QueryEngineFormService } from '@common/service/query-engine'
import { NzPopoverModule } from 'ng-zorro-antd/popover'
import { BusinessService } from '../../business.service'
import { combineLatest, debounceTime, finalize } from 'rxjs'
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop'
import { CaerusApiService } from '@api/caerus'
import { GraphComponent } from '@shared/components/graph'
import { LineSpinComponent } from '@shared/components/line-spin'
import { QueryOutputVo } from '@api/query-engine/model'
import { ProgressTrend } from './lib'
import { LegendControlService, LegendItemClickHandler } from '@common/service'
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox'
import { RetentionTableComponent } from './retention-table/retention-table.component'
import { QueryEngineApiService } from '@api/query-engine'
import { flattenDeep } from 'lodash'
import { differenceInDays, format, subDays } from 'date-fns'

@Component({
  selector: 'app-customer-retention',
  imports: [
    RadioModule,
    FormsModule,
    DateFilterComponent,
    NzPopoverModule,
    GraphComponent,
    LineSpinComponent,
    NzCheckboxModule,
    RetentionTableComponent,
  ],
  templateUrl: './customer-retention.component.html',
  styles: ``,
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [QueryEngineFormService, LegendControlService],
})
export class CustomerRetentionComponent implements AfterViewInit {
  readonly service = inject(BusinessService)
  readonly destroyRef = inject(DestroyRef)
  readonly apiService = inject(CaerusApiService)
  readonly legendControlService = inject(LegendControlService)
  readonly formService = inject(QueryEngineFormService)
  readonly queryService = inject(QueryEngineApiService)

  media_type = signal(null)
  media_type$ = toObservable(this.media_type)
  config = signal(null)
  type = signal('firfin')
  type$ = toObservable(this.type)
  c_thilev_channel = signal(null)
  c_thilev_channel$ = toObservable(this.c_thilev_channel)
  promt_channel_source_type = signal(null)
  promt_channel_source_type$ = toObservable(this.promt_channel_source_type)
  activate_shop_source = signal(null)
  activate_shop_source$ = toObservable(this.activate_shop_source)
  index = signal(null)
  _renten = signal(null)
  loading = signal(false)
  option = signal(null)
  errorMessage = signal(null)
  checkValue = signal([null])
  endTime = signal(null)

  label = computed(() => {
    if (this.type() === 'new_activate') {
      return '激活日期'
    }
    if (this.type() === 'new_reg') {
      return '注册日期'
    }
    if (this.type() === 'new_identification') {
      return '认证日期'
    }
    if (this.service.type() === 'driver') {
      if (this.type() === 'firfin') {
        return '接单日期'
      }
    }
    return '下单日期'
  })

  title = computed(() => {
    const _type = this.service.type() === 'passenger' ? '乘客' : '车主'
    if (this.type() === 'firfin') {
      return `首单${_type}`
    } else if (this.type() === 'new_activate') {
      return `新激活${_type}`
    } else if (this.type() === 'new_reg') {
      return `新注册${_type}`
    } else {
      return `新认证${_type}`
    }
  })

  tooltip = computed(() => {
    if (!this.endTime()) {
      return ''
    }
    const yesterday = subDays(new Date(), 1)
    const diffDays = differenceInDays(yesterday, new Date(this.endTime()))
    const arr = ['当日', '次日', '3日内', '7日内', '15日内', '30日内', '60日内', '90日内']
    const obj = {
      0: 0,
      1: 1,
      3: 2,
      7: 3,
      15: 4,
      30: 5,
      60: 6,
      90: 7,
    }
    console.log('diffDays', diffDays)
    if (diffDays > 90 || diffDays === 90) {
      return [
        `提示：截至昨日（${format(yesterday, 'yyyy-MM-dd')}），所选日期范围已全部到达留存周期对应的时间节点，留存数据展示的是最终结果。`,
      ]
    }
    let index
    const keys = Object.keys(obj)
    for (let i = 0; i <= keys.length; i++) {
      if (Number(keys[i]) > diffDays) {
        index = obj[keys[i]]
        break
      }
    }

    const first = arr.filter((a, i) => i < index)
    const end = arr.filter((a, i) => i > index || i === index)
    return [
      `提示：截至昨日（${format(yesterday, 'yyyy-MM-dd')}），所选日期范围未全部到达留存周期对应的时间节点，部分留存周期的数据尚在更新中，并非最终结果；`,
      `已更新完毕的：${first.join('，')}，`,
      `尚在更新中的：${end.join('，')}。`,
    ]
  })

  ngAfterViewInit(): void {
    this.subscribeChanges()
  }

  fetchConfig() {
    const key =
      this.service.type() === 'passenger'
        ? 'business_monitoring_promotion_detail_pass_foot_v2'
        : 'business_monitoring_promotion_detail_driver_foot_v2'
    this.apiService.fetchConfig(key).subscribe(res => {
      if (res.data) {
        console.log('relation', res.data.reten)
        this._renten.set(res.data.reten)
        this.config.set(res.data.reten[this.type()])
        this.getChartData()
      }
    })
  }

  changeSource() {
    this.checkValue.set([this.promt_channel_source_type()])
    this.media_type.set(null)
    this.getChartData()
  }

  changeMediaType() {
    this.activate_shop_source.set(null)
    this.getChartData()
    if (this.media_type() !== null) {
      this.checkValue.set([this.promt_channel_source_type()])
    }
  }

  changeType() {
    if (!this.config()) {
      return
    }

    this.config.set(this._renten()[this.type()])
    this.promt_channel_source_type.set(null)

    this.media_type.set(null)
    this.index.set(0)
    this.checkValue.set([null])
    this.activate_shop_source.set(null)
    if (this.type() == 'new_activate') {
      this.c_thilev_channel.set(this.config().filter.c_thilev_channel.values[0])
    } else {
      this.c_thilev_channel.set(null)
    }
    this.getChartData()
  }

  subscribeChanges() {
    combineLatest([this.service.area$, this.formService.form.valueChanges])
      .pipe(debounceTime(100), takeUntilDestroyed(this.destroyRef))
      .subscribe(([a, b]) => {
        console.log('bbbb', b)
        if (b.dt && b.dt.endTime) {
          this.endTime.set(b.dt.endTime)
        }

        if (a !== null && this.type() === 'new_activate') {
          this.type.set('firfin')
        }
        this.getChartData()
      })

    this.service.type$.pipe(debounceTime(100), takeUntilDestroyed(this.destroyRef)).subscribe(() => {
      this.type.set('firfin')
      this.fetchConfig()
    })
  }

  getChartData() {
    if (!this.config()) {
      return
    }
    const body = this.formService.value()
    body.dimensions = []
    body.dataFillConfig = {
      open: 1,
      fillRangeType: 2,
      fillValue: 0,
    }
    body.queryType = 'compare'
    const filterArr = [this.service.area(), this.c_thilev_channel(), this.activate_shop_source(), this.media_type()]
    const items = filterArr
      .filter(n => n)
      .map(f => {
        return {
          conditionType: 2,
          condition: '=',
          id: null,
          extendName: f.extendName,
          value: [
            {
              key: f.key,
              value: f.value,
            },
          ],
          valueType: null,
        }
      })
    if (this.type() === 'firfin' && this.service.type() === 'passenger') {
      items.push({
        conditionType: 2,
        condition: '=',
        id: null,
        extendName: 'firfin_book_firlev_channel',
        value: [
          {
            key: '自有渠道',
            value: '自有渠道',
          },
        ],
        valueType: null,
      })
    }
    body.filter = {
      items,
      type: null,
    }
    const filterMetrics = this.config().metrics.filter(m => m.tag !== 'base')
    const _checkedValue = this.checkValue().filter(c => c)
    const _filter = this.checkValue().map(c => {
      if (!c) {
        return {}
      }
      return {
        conditionType: 2,
        condition: '=',
        id: null,
        extendName: 'promt_channel_source_type',
        value: [
          {
            key: c.key,
            value: c.value,
          },
        ],
        valueType: null,
      }
    })
    const result = _filter.map(f => {
      return filterMetrics.map(mm => {
        return {
          extendName: mm.extendName,
          filter: {
            items: f.value ? [f] : [],
            type: null,
          },
          userDefExtendName: f.value ? `${mm.extendName}_${f.value[0].key}` : mm.extendName,
        }
      })
    })
    const metrics = flattenDeep(result)
    console.log('metrics', metrics)
    body.metrics = metrics
    const tag_1d = filterMetrics.filter(m => m.tag === '1d').map(m => m.extendName)
    const tag_2d = filterMetrics.filter(m => m.tag === '2d').map(m => m.extendName)
    const tag_3d = filterMetrics.filter(m => m.tag === '3d').map(m => m.extendName)
    const tag_7d = filterMetrics.filter(m => m.tag === '7d').map(m => m.extendName)
    const tag_15d = filterMetrics.filter(m => m.tag === '15d').map(m => m.extendName)
    const tag_30d = filterMetrics.filter(m => m.tag === '30d').map(m => m.extendName)
    const tag_60d = filterMetrics.filter(m => m.tag === '60d').map(m => m.extendName)
    const tag_90d = filterMetrics.filter(m => m.tag === '90d').map(m => m.extendName)
    const tag_metrics = [tag_1d, tag_2d, tag_3d, tag_7d, tag_15d, tag_30d, tag_60d, tag_90d]

    this.legendControlService.reset()
    this.loading.set(true)
    this.queryService
      .search(body, 'customer-chart-data')
      .pipe(finalize(() => this.loading.set(false)))
      .subscribe(res => {
        console.log('customer-chart-data', res)
        if (res.data) {
          if (res.data.data.length !== 0) {
            const _data = res.data.data[0]
            const result = []
            tag_metrics.forEach(metrics => {
              let temp = {}
              metrics.forEach(sub => {
                if (sub.includes('ltv')) {
                  temp['c_finish_pass'] = _data[sub]
                  if (_checkedValue.length !== 0) {
                    _checkedValue.forEach(c => {
                      temp[`c_finish_pass_${c.key}`] = _data[`${sub}_${c.key}`]
                    })
                  }
                } else if (sub.includes('freten_pass_rate')) {
                  temp['freten_pass_rate'] = _data[sub]
                  if (_checkedValue.length !== 0) {
                    _checkedValue.forEach(c => {
                      temp[`freten_pass_rate_${c.key}`] = _data[`${sub}_${c.key}`]
                    })
                  }
                } else if (sub.includes('freten_driver_rate')) {
                  temp['freten_driver_rate'] = _data[sub]
                  if (_checkedValue.length !== 0) {
                    _checkedValue.forEach(c => {
                      temp[`freten_driver_rate_${c.key}`] = _data[`${sub}_${c.key}`]
                    })
                  }
                } else if (sub.includes('areten_rate')) {
                  temp['areten_rate'] = _data[sub]
                  if (_checkedValue.length !== 0) {
                    _checkedValue.forEach(c => {
                      temp[`areten_rate_${c.key}`] = _data[`${sub}_${c.key}`]
                    })
                  }
                } else if (sub.includes('adriv_c_finish_driver_prop')) {
                  temp['adriv_c_finish_driver_prop'] = _data[sub]
                  if (_checkedValue.length !== 0) {
                    _checkedValue.forEach(c => {
                      temp[`adriv_c_finish_driver_prop_${c.key}`] = _data[`${sub}_${c.key}`]
                    })
                  }
                } else if (sub.includes('bdriv_c_finish_driver_prop')) {
                  temp['bdriv_c_finish_driver_prop'] = _data[sub]
                  if (_checkedValue.length !== 0) {
                    _checkedValue.forEach(c => {
                      temp[`bdriv_c_finish_driver_prop_${c.key}`] = _data[`${sub}_${c.key}`]
                    })
                  }
                } else {
                  temp['cdriv_c_finish_driver_prop'] = _data[sub]
                  if (_checkedValue.length !== 0) {
                    _checkedValue.forEach(c => {
                      temp[`cdriv_c_finish_driver_prop_${c.key}`] = _data[`${sub}_${c.key}`]
                    })
                  }
                }
              })
              result.push(temp)
            })
            console.log('result', result)
            let obj = {}
            tag_90d.forEach(h => {
              if (h.includes('ltv')) {
                obj['c_finish_pass'] = {
                  ...res.data.headers[h],
                  aliasName: `${this.title()}LTV_N_全部`,
                  extendName: 'c_finish_pass',
                }
                if (_checkedValue.length !== 0) {
                  _checkedValue.forEach(c => {
                    obj[`c_finish_pass_${c.key}`] = {
                      ...res.data.headers[`${h}_${c.key}`],
                      aliasName: `${this.title()}LTV_N_${c.key === '0' ? '自然' : '推广'}`,
                      extendName: `c_finish_pass_${c.key}`,
                    }
                  })
                }
              } else if (h.includes('freten_pass_rate')) {
                obj['freten_pass_rate'] = {
                  ...res.data.headers[h],
                  aliasName: `${this.title()}完单留存率_全部`,
                  extendName: 'freten_pass_rate',
                }
                if (_checkedValue.length !== 0) {
                  _checkedValue.forEach(c => {
                    obj[`freten_pass_rate_${c.key}`] = {
                      ...res.data.headers[`${h}_${c.key}`],
                      aliasName: `${this.title()}完单留存率_${c.key === '0' ? '自然' : '推广'}`,
                      extendName: `freten_pass_rate_${c.key}`,
                    }
                  })
                }
              } else if (h.includes('freten_driver_rate')) {
                obj['freten_driver_rate'] = {
                  ...res.data.headers[h],
                  aliasName: `${this.title()}完单留存率_全部`,
                  extendName: 'freten_driver_rate',
                }
                if (_checkedValue.length !== 0) {
                  _checkedValue.forEach(c => {
                    obj[`freten_driver_rate_${c.key}`] = {
                      ...res.data.headers[`${h}_${c.key}`],
                      aliasName: `${this.title()}完单留存率_${c.key === '0' ? '自然' : '推广'}`,
                      extendName: `freten_driver_rate_${c.key}`,
                    }
                  })
                }
              } else if (h.includes('areten_rate')) {
                obj['areten_rate'] = {
                  ...res.data.headers[h],
                  aliasName: `${this.title()}活跃留存率_全部`,
                  extendName: 'areten_rate',
                }
                if (_checkedValue.length !== 0) {
                  _checkedValue.forEach(c => {
                    obj[`areten_rate_${c.key}`] = {
                      ...res.data.headers[`${h}_${c.key}`],
                      aliasName: `${this.title()}活跃留存率_${c.key === '0' ? '自然' : '推广'}`,
                      extendName: `areten_rate_${c.key}`,
                    }
                  })
                }
              } else if (h.includes('adriv_c_finish_driver_prop')) {
                obj['adriv_c_finish_driver_prop'] = {
                  ...res.data.headers[h],
                  aliasName: `A车主占比_全部`,
                  extendName: 'adriv_c_finish_driver_prop',
                }
                if (_checkedValue.length !== 0) {
                  _checkedValue.forEach(c => {
                    obj[`adriv_c_finish_driver_prop_${c.key}`] = {
                      ...res.data.headers[`${h}_${c.key}`],
                      aliasName: `A车主占比_${c.key === '0' ? '自然' : '推广'}`,
                      extendName: `adriv_c_finish_driver_prop_${c.key}`,
                    }
                  })
                }
              } else if (h.includes('bdriv_c_finish_driver_prop')) {
                obj['bdriv_c_finish_driver_prop'] = {
                  ...res.data.headers[h],
                  aliasName: `B车主占比_全部`,
                  extendName: 'bdriv_c_finish_driver_prop',
                }
                if (_checkedValue.length !== 0) {
                  _checkedValue.forEach(c => {
                    obj[`bdriv_c_finish_driver_prop_${c.key}`] = {
                      ...res.data.headers[`${h}_${c.key}`],
                      aliasName: `B车主占比_${c.key === '0' ? '自然' : '推广'}`,
                      extendName: `bdriv_c_finish_driver_prop_${c.key}`,
                    }
                  })
                }
              } else {
                obj['cdriv_c_finish_driver_prop'] = {
                  ...res.data.headers[h],
                  aliasName: `C车主占比_全部`,
                  extendName: 'cdriv_c_finish_driver_prop',
                }
                if (_checkedValue.length !== 0) {
                  _checkedValue.forEach(c => {
                    obj[`cdriv_c_finish_driver_prop_${c.key}`] = {
                      ...res.data.headers[`${h}_${c.key}`],
                      aliasName: `C车主占比_${c.key === '0' ? '自然' : '推广'}`,
                      extendName: `cdriv_c_finish_driver_prop_${c.key}`,
                    }
                  })
                }
              }
            })
            console.log('obj', obj)
            res.data.data = result
            res.data.headers = obj
            this._setChartData(res.data)
          } else {
            this.option.set(null)
            this.errorMessage.set('暂无数据')
          }
        } else {
          this.option.set(null)
          this.errorMessage.set(res.message)
        }
      })
  }

  private _setChartData(data: QueryOutputVo) {
    try {
      const chart = new ProgressTrend(data)
      const legendItemClick = LegendItemClickHandler(this.legendControlService)
      chart.plotOptions.series.events = { legendItemClick }
      this.option.set(chart?.getOption() || null)
    } catch (e) {
      console.error(e)
    }
  }
}
