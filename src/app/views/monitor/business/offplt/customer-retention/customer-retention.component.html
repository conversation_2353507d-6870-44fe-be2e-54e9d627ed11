<div class="px-5 flex flex-col py-3 gap-3">
  <header
    class="relative flex items-center gap-x-1 px-5 h-12 border-l-4 border-emerald-500 bg-linear-to-r from-neutral-300/40 via-white via-20%"
  >
    <span class="text-lg">新用户留存</span>
    @if (service.type() === 'passenger') {
      <span class="text-xs text-neutral-400 ml-0.5">说明：此处指标均为自有渠道数据。</span>
    }
    <app-radio-group
      [(ngModel)]="type"
      class="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 flex items-start gap-6"
      (ngModelChange)="changeType()"
    >
      <app-radio class="line-radio text-xs whitespace-nowrap" activeClass="active" value="firfin">
        首完单{{ service.type() === 'passenger' ? '乘客' : '车主' }}留存
      </app-radio>
      <app-radio
        nz-popover
        nzPopoverTitle="提示"
        nzPopoverContent="新激活用户留存仅支持全国粒度查看"
        [nzPopoverTrigger]="service.area() === null ? null : 'hover'"
        class="line-radio text-xs whitespace-nowrap"
        activeClass="active"
        value="new_activate"
        [disabled]="service.area() !== null"
      >
        新激活用户留存
      </app-radio>
      <app-radio class="line-radio text-xs whitespace-nowrap" activeClass="active" value="new_reg">
        新注册用户留存
      </app-radio>
      @if (service.type() === 'driver') {
        <app-radio class="line-radio text-xs whitespace-nowrap" activeClass="active" value="new_identification">
          新认证车主留存
        </app-radio>
      }
    </app-radio-group>
  </header>

  <div class="flex items-center gap-x-10 gap-y-2 flex-wrap">
    <app-date-filter [label]="label()" [showDtType]="false" />
    <div class="flex items-center">
      <ng-template #metricsTitleTemplate>
        {{ config()?.filter?.promt_channel_source_type?.name }}
        <span class="text-xs opacity-30 px-1">({{ config()?.filter?.promt_channel_source_type?.aliasName }})</span>
      </ng-template>

      <ng-template #metricsContentTemplate>
        <div class="leading-normal" [innerHTML]="config()?.filter?.promt_channel_source_type?.bizExpression"></div>
      </ng-template>
      <label
        nz-popover
        [nzPopoverMouseEnterDelay]="0.5"
        [nzPopoverTitle]="metricsTitleTemplate"
        [nzPopoverContent]="metricsContentTemplate"
        class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap"
      >
        来源类型：
      </label>
      <app-radio-group
        class="relative flex gap-1"
        [(ngModel)]="promt_channel_source_type"
        (ngModelChange)="changeSource()"
      >
        <app-radio class="tag-radio" activeClass="active" [value]="null">全部</app-radio>
        @for (item of config()?.filter?.promt_channel_source_type?.values; track item) {
          <app-radio class="tag-radio" activeClass="active" [value]="item">
            {{ item.showValue }}
          </app-radio>
        }
      </app-radio-group>
    </div>
    <div class="flex items-center">
      <ng-template #metricsTitleTemplate2>
        {{ config()?.filter?.media_type?.name }}
        <span class="text-xs opacity-30 px-1">({{ config()?.filter?.media_type?.aliasName }})</span>
      </ng-template>

      <ng-template #metricsContentTemplate2>
        <div class="leading-normal" [innerHTML]="config()?.filter?.media_type?.bizExpression"></div>
      </ng-template>
      <label
        nz-popover
        [nzPopoverMouseEnterDelay]="0.5"
        [nzPopoverTitle]="metricsTitleTemplate2"
        [nzPopoverContent]="metricsContentTemplate2"
        class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap"
      >
        推广渠道类型：
      </label>
      <app-radio-group class="relative flex gap-1" [(ngModel)]="media_type" (ngModelChange)="changeMediaType()">
        <app-radio class="tag-radio" activeClass="active" [value]="null">全部</app-radio>
        @if (promt_channel_source_type()?.value === '推广') {
          @for (item of config()?.filter?.media_type?.values; track item) {
            <app-radio class="tag-radio" activeClass="active" [value]="item">
              {{ item.showValue }}
            </app-radio>
          }
        }
      </app-radio-group>
    </div>
    <div class="flex items-center">
      <ng-template #metricsTitleTemplate3>
        {{ config()?.filter?.c_thilev_channel?.name }}
        <span class="text-xs opacity-30 px-1">({{ config()?.filter?.c_thilev_channel?.aliasName }})</span>
      </ng-template>

      <ng-template #metricsContentTemplate3>
        <div class="leading-normal" [innerHTML]="config()?.filter?.c_thilev_channel?.bizExpression"></div>
      </ng-template>
      <label
        class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap"
        nz-popover
        [nzPopoverMouseEnterDelay]="0.5"
        [nzPopoverTitle]="metricsTitleTemplate3"
        [nzPopoverContent]="metricsContentTemplate3"
      >
        产品渠道：
      </label>
      <app-radio-group class="relative flex gap-1" [(ngModel)]="c_thilev_channel" (ngModelChange)="getChartData()">
        @if (type() !== 'new_activate') {
          <app-radio class="tag-radio" activeClass="active" [value]="null">全部</app-radio>
        }
        @for (item of config()?.filter?.c_thilev_channel?.values; track item) {
          <app-radio class="tag-radio" activeClass="active" [value]="item">
            {{ item.showValue }}
          </app-radio>
        }
      </app-radio-group>
    </div>
    @if (type() === 'new_activate') {
      <div class="flex items-center">
        <ng-template #metricsTitleTemplateshop>
          {{ config()?.filter?.activate_shop_source?.name }}
          <span class="text-xs opacity-30 px-1">({{ config()?.filter?.activate_shop_source?.aliasName }})</span>
        </ng-template>

        <ng-template #metricsContentTemplateshop>
          <div class="leading-normal" [innerHTML]="config()?.filter?.activate_shop_source?.bizExpression"></div>
        </ng-template>
        <label
          nz-popover
          [nzPopoverMouseEnterDelay]="0.5"
          [nzPopoverTitle]="metricsTitleTemplateshop"
          [nzPopoverContent]="metricsContentTemplateshop"
          class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap"
        >
          应用商店来源：
        </label>
        <app-radio-group
          class="relative flex gap-1"
          [(ngModel)]="activate_shop_source"
          (ngModelChange)="getChartData()"
        >
          <app-radio class="tag-radio" activeClass="active" [value]="null">全部</app-radio>
          @if (media_type() === null || media_type()?.value === '商店') {
            @for (item of config()?.filter?.activate_shop_source?.values; track item) {
              <app-radio class="tag-radio" activeClass="active" [value]="item">
                {{ item.showValue }}
              </app-radio>
            }
          }
        </app-radio-group>
      </div>
    }
  </div>
  <div class="flex flex-col h-96 shadow-md rounded-sm border border-neutral-100 relative">
    <div class="flex px-2 gap-1 py-2 items-center">
      <div class="flex items-center">
        <label class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap">
          渠道对比：
        </label>
        <div>
          <nz-checkbox-group [(ngModel)]="checkValue" (ngModelChange)="getChartData()">
            <label nz-checkbox class="text-xs! ml-0!" [nzValue]="null" [nzDisabled]="media_type() !== null">全部</label>
            @for (item of config()?.filter?.promt_channel_source_type?.values; track item) {
              <label nz-checkbox class="text-xs! ml-0!" [nzValue]="item" [nzDisabled]="media_type() !== null">
                {{ item.showValue }}
              </label>
            }
          </nz-checkbox-group>
        </div>
      </div>
      <div class="text-xs text-neutral-400 ml-0.5 flex-wrap flex-1">
        @for (i of tooltip(); track i) {
          <div>{{ i }}</div>
        }
      </div>
    </div>

    <div class="flex-1 min-h-0 relative flex items-center justify-center text-xs">
      @if (loading()) {
        <app-line-spin />
      } @else {
        @if (option()) {
          <app-graph class="absolute inset-2" [options]="option()" showAnnotations showAvgPlotLines />
        } @else if (errorMessage()) {
          <span class="text-slate-600 text-xs">{{ errorMessage() }}</span>
        }
      }
    </div>
  </div>

  <app-retention-table />
</div>
