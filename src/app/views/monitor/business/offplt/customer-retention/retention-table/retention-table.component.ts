import { AfterViewInit, ChangeDetectionStrategy, Component, computed, DestroyRef, inject, signal } from '@angular/core'
import { combineLatest, debounceTime, finalize, startWith } from 'rxjs'
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop'
import { NzPopoverModule } from 'ng-zorro-antd/popover'
import { NzTableModule } from 'ng-zorro-antd/table'

import { groupBy } from '@common/function'
import { CaerusApiService } from '@api/caerus'
import { QueryEngineApiService } from '@api/query-engine'
import { QueryEngineFormService } from '@common/service/query-engine'
import { SwitchMap } from '@common/decorator'
import { BusinessService } from '@views/monitor/business'
import { ValueFormatter } from '@shared/components/value-formatter'
import { CustomerRetentionComponent } from '../customer-retention.component'

@Component({
  selector: 'app-retention-table',
  template: `
    <nz-table
      #basicTable
      nzBordered
      nzOuterBordered
      nzSize="small"
      nzTableLayout="fixed"
      [nzData]="listOfData()"
      [nzPageSize]="10"
      [nzScroll]="scrollConfig()"
      [nzLoading]="loading()"
    >
      <thead>
        <tr>
          @for (item of listOfColumnsLevel_1(); track $index) {
            <th
              class="text-center! bg-neutral-100!"
              [nzLeft]="item?.isLeft || false"
              [rowSpan]="item?.rowspan"
              [colspan]="item?.colspan ?? null"
              nz-popover
              nzPopoverPlacement="top"
              [nzPopoverMouseEnterDelay]="0.5"
              [nzPopoverTitle]="$index === 1 ? item.showName : null"
              [nzPopoverContent]="$index === 1 ? metricsTopContentTemplate : null"
            >
              <ng-template #metricsTopContentTemplate>
                <div class="leading-normal" [innerHTML]="item?.bizExpression"></div>
              </ng-template>
              {{ item.showName }}
            </th>
          }
        </tr>

        <tr>
          @for (item of listOfColumnsLevel_2(); track $index) {
            <th
              class="text-right! bg-neutral-100!"
              [nzLeft]="item?.isLeft || false"
              [attr.data-tag]="item.tag"
              nz-popover
              nzPopoverPlacement="top"
              [nzPopoverMouseEnterDelay]="0.5"
              [nzPopoverTitle]="item.showName"
              [nzPopoverContent]="metricsContent_Template"
            >
              <ng-template #metricsContent_Template>
                <div class="leading-normal" [innerHTML]="item?.bizExpression"></div>
              </ng-template>
              {{ item.showName }}
            </th>
          }
        </tr>
      </thead>

      <tbody>
        @if (firstRowData()) {
          <tr class="text-center">
            <td nzLeft>合计</td>
            @for (item of metrics(); track $index) {
              @let key = item.extendName;
              @let dataUnit = this.metricsMap.get(key)?.dataUnit;
              @let isPercent = dataUnit === '%';
              @let isUndefined = firstRowData()[key] === undefined;
              @let value = firstRowData()[key] || null;

              @if (isUndefined) {
                <td class="text-right" [nzLeft]="$index === 0"></td>
              } @else {
                <td class="text-right bg-[#d9e6ff]/50" [nzLeft]="$index === 0">
                  <value-formatter [value]="value" [suffix]="isPercent ? '%' : null" />
                </td>
              }
            }
          </tr>
        }
        @for (data of basicTable.data; track data) {
          <tr class="text-center">
            <td nzLeft>{{ data.dt }}</td>
            @for (item of metrics(); track $index) {
              @let key = item.extendName;
              @let dataUnit = this.metricsMap.get(key)?.dataUnit;
              @let isPercent = dataUnit === '%';
              @let isUndefined = data[key] === undefined;
              @let value = data[key] || null;

              @if (isUndefined) {
                <td class="text-right" [nzLeft]="$index === 0"></td>
              } @else {
                <td class="text-right bg-[#d9e6ff]/50" [nzLeft]="$index === 0">
                  <value-formatter [value]="value" [suffix]="isPercent ? '%' : null" />
                </td>
              }
            }
          </tr>
        }
      </tbody>
    </nz-table>
  `,
  styleUrl: './retention-table.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NzTableModule, NzPopoverModule, ValueFormatter],
})
export class RetentionTableComponent implements AfterViewInit {
  readonly destroyRef = inject(DestroyRef)
  readonly caerusApiService = inject(CaerusApiService)
  readonly apiService = inject(QueryEngineApiService)
  readonly parent = inject(CustomerRetentionComponent)
  readonly formService = inject(QueryEngineFormService)
  readonly service = inject(BusinessService)

  loading = signal(false)
  listOfData = signal([])
  firstRowData = signal<any>({})
  metricsMap = new Map()
  reten = signal(null)
  tagMap = new Map([
    ['1d', '当日'],
    ['2d', '次日'],
    ['3d', '3日内'],
    ['7d', '7日内'],
    ['15d', '15日内'],
    ['30d', '30日内'],
    ['60d', '60日内'],
    ['90d', '90日内'],
  ])

  scrollConfig = computed(() => {
    const count = this.metrics()?.length

    if (count) {
      return { x: `${count * 145}px` }
    }

    return null
  })

  metrics = computed(() => {
    const type = this.parent.type()
    if (type && this.reten()) {
      const { metrics } = this.reten()[type] || {}

      return metrics
    }
    return null
  })

  metrics$ = toObservable(this.metrics)

  listOfColumnsLevel_1 = computed<any>(() => {
    const result = [{ rowspan: 2, showName: '日期', isLeft: true }] as any[]

    if (this.metrics()) {
      const metrics = this.metrics()
      const groupObj = groupBy(metrics, 'tag') as any
      const [base] = groupObj.base

      result.push({ rowspan: 2, isLeft: true, ...base })

      Object.keys(groupObj).forEach(key => {
        if (key !== 'base') {
          const items = groupObj[key]
          const colspan = items.length
          const showName = this.tagMap.get(key)

          result.push({ colspan, showName })
        }
      })
    }

    return result
  })

  listOfColumnsLevel_2 = computed(() => {
    const type = this.parent.type()
    const result = []

    if (type && this.metrics()) {
      const metrics = this.metrics()
      const groupObj = groupBy(metrics, 'tag') as any

      Object.keys(groupObj).forEach((key, index) => {
        if (key !== 'base') {
          const items = groupObj[key]
          items.isLeft = index === 0

          result.push(...items)
        }
      })

      return result
    }
    return []
  })

  ngAfterViewInit(): void {
    this._subscribeToTypeChange()
    this._subscribeToParentFilterChange()
  }

  private _subscribeToTypeChange() {
    this.service.type$.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(() => {
      this._fetchConfig()
    })
  }

  private _subscribeToParentFilterChange() {
    combineLatest([
      this.metrics$,
      this.parent.type$,
      this.parent.media_type$,
      this.parent.c_thilev_channel$,
      this.parent.promt_channel_source_type$,
      this.parent.activate_shop_source$,
      this.service.area$,
      this.formService.form.valueChanges.pipe(startWith(null)),
    ])
      .pipe(debounceTime(300), takeUntilDestroyed(this.destroyRef))
      .subscribe(([metrics]) => {
        if (metrics) {
          this.query()
        }
      })
  }

  private _fetchConfig() {
    const key =
      this.service.type() === 'passenger'
        ? 'business_monitoring_promotion_detail_pass_foot_v2'
        : 'business_monitoring_promotion_detail_driver_foot_v2'

    this.caerusApiService.fetchConfig(key).subscribe(res => {
      if (res.status !== '00000') {
        return console.error(res.error || res.message)
      }

      if (res.data) {
        const { reten } = res.data

        this.reten.set(reten)
      }
    })
  }

  private _promtChannelSourceTypeFilter() {
    if (this.parent.promt_channel_source_type()) {
      const { key, value } = this.parent.promt_channel_source_type()

      return [
        {
          conditionType: 2,
          condition: '=',
          id: null,
          extendName: 'promt_channel_source_type',
          value: [{ key, value }],
          valueType: null,
        },
      ]
    }
    return []
  }

  private _activateShopSourcefilter() {
    if (this.parent.activate_shop_source()) {
      const { key, value } = this.parent.activate_shop_source()

      return [
        {
          conditionType: 2,
          condition: '=',
          id: null,
          extendName: 'activate_shop_source',
          value: [{ key, value }],
          valueType: null,
        },
      ]
    }
    return []
  }

  private _mediaTypeFilter() {
    if (this.parent.media_type()) {
      const { key, value } = this.parent.media_type()

      return [
        {
          conditionType: 2,
          condition: '=',
          id: null,
          extendName: 'media_type',
          value: [{ key, value }],
          valueType: null,
        },
      ]
    }
    return []
  }

  private _thilevChannelFilter() {
    if (this.parent.c_thilev_channel()) {
      const { key, value } = this.parent.c_thilev_channel()
      const extendName = this.service.type() === 'passenger' ? 'c_thilev_channel' : 'c_reply_propp_name'

      return [
        {
          conditionType: 2,
          condition: '=',
          id: null,
          extendName,
          value: [{ key, value }],
          valueType: null,
        },
      ]
    }
    return []
  }

  private _globalAreaFilter() {
    if (this.service.area()) {
      const { key, value, extendName } = this.service.area()

      return [
        {
          conditionType: 2,
          condition: '=',
          id: null,
          extendName,
          value: [{ key, value }],
          valueType: null,
        },
      ]
    }
    return []
  }

  private _channelFilter() {
    if (this.parent.type() === 'firfin' && this.service.type() === 'passenger') {
      return [
        {
          conditionType: 2,
          condition: '=',
          id: null,
          valueType: null,
          extendName: 'firfin_book_firlev_channel',
          value: [
            {
              key: '自有渠道',
              value: '自有渠道',
            },
          ],
        },
      ]
    }
    return []
  }

  @SwitchMap()
  query() {
    const body = this.formService.value()

    body.extraDimensionGroups = [[]]
    body.dimensions[0].predefineCompareType = []
    body.metrics = this.metrics()

    body.filter.items = [
      this._channelFilter(),
      this._mediaTypeFilter(),
      this._promtChannelSourceTypeFilter(),
      this._activateShopSourcefilter(),
      this._thilevChannelFilter(),
      this._globalAreaFilter(),
    ].flat(1)

    body.scene = 4
    body.dataFillConfig = {
      open: 0,
      fillRangeType: 1,
      fillValue: 1,
    }

    this.loading.set(true)

    return this.caerusApiService
      .searchForOffpltPromtReten(body, 'retention-table')
      .pipe(finalize(() => this.loading.set(false)))
      .subscribe(res => {
        if (res.status !== '00000') {
          return console.error(res.error || res.message)
        }

        if (res.data?.data) {
          const { headers } = res.data
          const first = res.data.data.find(item => item['dt'] === '整体')
          const data = res.data.data.filter(item => item !== first)

          this.listOfData.set(data || [])
          this.firstRowData.set(first)

          Object.keys(headers).forEach(key => {
            this.metricsMap.set(key, headers[key])
          })
        }
      })
  }
}
