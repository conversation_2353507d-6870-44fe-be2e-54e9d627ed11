<app-date type="offplt" />
<div class="flex justify-center bg-white">
  <div class="max-w-(--breakpoint-2xl) flex-1 min-w-0">
    <div class="p-5">
      <header class="relative flex items-center gap-x-1 px-5 h-12">
        <app-radio-group
          [(ngModel)]="service.type"
          (ngModelChange)="changeType()"
          class="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-80 flex items-start gap-x-0.5 p-0.5 bg-[#ebebec] rounded-lg"
        >
          <app-radio class="dida-radio" activeClass="dida-radio-active" value="passenger">乘客视角</app-radio>
          <app-radio class="dida-radio" activeClass="dida-radio-active" value="driver">车主视角</app-radio>
        </app-radio-group>
      </header>
    </div>

    @defer (on viewport) {
      <app-overview [config]="config()?.overview" />
    } @placeholder {
      <div class="relative w-full aspect-16/8">
        <app-progress-loader />
      </div>
    }

    @defer (on viewport) {
      <app-area-ranking [config]="config()?.area_rank" />
    } @placeholder {
      <div class="relative w-full aspect-[16/6.6]">
        <app-progress-loader />
      </div>
    }

    @defer (on viewport) {
      <app-transform [config]="config()?.transfer" />
    } @placeholder {
      <div class="relative w-full aspect-16/6">
        <app-progress-loader />
      </div>
    }

    @defer (on viewport) {
      <app-customer-retention />
    } @placeholder {
      <div class="relative w-full aspect-16/12">
        <app-progress-loader />
      </div>
    }

    @defer (on viewport) {
      <app-detail />
    } @placeholder {
      <div class="relative w-full aspect-16/11">
        <app-progress-loader />
      </div>
    }
  </div>
</div>
