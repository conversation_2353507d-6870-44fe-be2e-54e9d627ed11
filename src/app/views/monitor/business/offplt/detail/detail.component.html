<div class="px-5 flex flex-col gap-2 py-3">
  <header
    class="relative flex items-center gap-x-1 px-5 h-12 border-l-4 border-emerald-500 bg-linear-to-r from-neutral-300/40 via-white via-20%"
  >
    <span class="text-lg">推广渠道效果详情</span>
    <app-radio-group
      [(ngModel)]="detailType"
      class="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 flex items-start gap-6"
    >
      <app-radio class="line-radio text-xs whitespace-nowrap" activeClass="active" value="core">推广完单规模</app-radio>

      <app-radio
        class="line-radio text-xs whitespace-nowrap"
        activeClass="active"
        value="new_activate"
        nz-popover
        nzPopoverTitle="提示"
        nzPopoverContent="新激活用户转化与留存仅支持全国粒度查看"
        [nzPopoverTrigger]="service.area() === null ? null : 'hover'"
        [disabled]="service.area() !== null"
      >
        新激活用户转化
      </app-radio>
      <app-radio class="line-radio text-xs whitespace-nowrap" activeClass="active" value="new_reg">
        新注册用户转化
      </app-radio>
      @if (service.type() === 'driver') {
        <app-radio class="line-radio text-xs whitespace-nowrap" activeClass="active" value="new_identification">
          新认证用户转化
        </app-radio>
      }
    </app-radio-group>
  </header>
  <span class="text-xs text-neutral-400 ml-0.5">说明：此处指标均为自有渠道数据</span>
  <app-core [detailType]="detailType()" [config]="config()" />
</div>
