import { AfterViewInit, ChangeDetectionStrategy, Component, DestroyRef, inject, input, signal } from '@angular/core'
import { FormsModule } from '@angular/forms'
import { PopoverModule, RadioModule } from '@shared/modules/headless'
import { CoreComponent } from './core/core.component'
import { BusinessService } from '../../business.service'
import { debounceTime } from 'rxjs'
import { takeUntilDestroyed } from '@angular/core/rxjs-interop'
import { NzPopoverModule } from 'ng-zorro-antd/popover'
import { CaerusApiService } from '@api/caerus'
@Component({
  selector: 'app-detail',
  imports: [FormsModule, RadioModule, CoreComponent, NzPopoverModule],
  templateUrl: './detail.component.html',
  styles: ``,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class DetailComponent implements AfterViewInit {
  readonly service = inject(BusinessService)
  readonly destroyRef = inject(DestroyRef)
  readonly apiService = inject(CaerusApiService)

  config = signal(null)
  detailType = signal('core')

  ngAfterViewInit(): void {
    this.fetchConfig()
    this.service.type$.pipe(debounceTime(100), takeUntilDestroyed(this.destroyRef)).subscribe(() => {
      this.fetchConfig()
      this.detailType.set('core')
    })
  }

  fetchConfig() {
    const key =
      this.service.type() === 'passenger'
        ? 'business_monitoring_promotion_detail_pass_foot_v2'
        : 'business_monitoring_promotion_detail_driver_foot_v2'
    this.apiService.fetchConfig(key).subscribe(res => {
      if (res.data) {
        console.log('detail-config', res.data.detail)
        this.config.set(res.data.detail)
      }
    })
  }
}
