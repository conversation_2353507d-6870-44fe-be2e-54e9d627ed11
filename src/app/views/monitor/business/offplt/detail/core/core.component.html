<div>
  <div class="flex items-center justify-between pb-3">
    <div class="flex items-center gap-3 flex-wrap">
      <app-date-filter label="日期范围" type="ym" />
      @if (detailType() !== 'core') {
        <div class="flex items-center">
          <ng-template #metricsTitleTemplate5>
            {{ config()[detailType()]?.timestampBizExpression?.showName }}
            <span class="text-xs opacity-30 px-1">
              ({{ config()[detailType()]?.timestampBizExpression?.aliasName }})
            </span>
          </ng-template>

          <ng-template #metricsContentTemplate5>
            <div
              class="leading-normal"
              [innerHTML]="config()[detailType()]?.timestampBizExpression?.bizExpression"
            ></div>
          </ng-template>
          <label
            nz-popover
            [nzPopoverMouseEnterDelay]="0.5"
            [nzPopoverTitle]="metricsTitleTemplate5"
            [nzPopoverContent]="metricsContentTemplate5"
            class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap"
          >
            转化周期：
          </label>
          <app-radio-group
            class="relative flex gap-1"
            [(ngModel)]="transform_date"
            (ngModelChange)="renderTransferColumn()"
          >
            @for (item of config()[detailType()]?.timestamp; track item) {
              <app-radio class="tag-radio" activeClass="active" [value]="item.extendName">
                {{ item.showName }}
              </app-radio>
            }
          </app-radio-group>
        </div>
        <div class="flex items-center pl-3">
          <label class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap">
            产品渠道：
          </label>
          <app-radio-group class="relative flex gap-1" [(ngModel)]="index" (ngModelChange)="renderTransferColumn()">
            @for (item of config()[detailType()]?.metrics; track item) {
              <ng-template #metricsTitleTemplate>
                {{ item?.aliasName }}
              </ng-template>
              <ng-template #metricsContentTemplate>
                <div class="leading-normal" [innerHTML]="item?.bizExpression"></div>
              </ng-template>
              <app-radio
                nz-popover
                class="tag-radio"
                activeClass="active"
                [value]="$index"
                [nzPopoverMouseEnterDelay]="0.5"
                [nzPopoverTitle]="metricsTitleTemplate"
                [nzPopoverContent]="metricsContentTemplate"
              >
                {{ item.showName }}
              </app-radio>
            }
          </app-radio-group>
        </div>
      } @else {
        <div class="flex items-center">
          <label class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap">
            区域粒度：
          </label>
          <app-radio-group
            class="relative flex gap-1"
            [(ngModel)]="regionType"
            (ngModelChange)="renderTransferColumn()"
          >
            <app-radio
              class="tag-radio"
              activeClass="active"
              value="city_bio_region"
              [disabled]="service.area() && service.area()?.extendName !== 'city_bio_region'"
            >
              按区域
            </app-radio>
            <app-radio
              class="tag-radio"
              activeClass="active"
              value="province_name"
              [disabled]="
                service.area() &&
                (service.area()?.extendName === 'city_name' || service.area()?.extendName === 'is_top20_city')
              "
            >
              按省份
            </app-radio>
            <app-radio class="tag-radio" activeClass="active" value="city_name">按城市</app-radio>
          </app-radio-group>
        </div>
      }
    </div>

    <div class="flex gap-3">
      <app-choose-show-field [list]="showFieldList()" (listChange)="listChange($event)" />
      <button nz-button (click)="download()">
        <span nz-icon nzType="download" nzTheme="outline"></span>
        下载
      </button>
    </div>
  </div>
  <nz-table
    #fixedTable
    [nzData]="listOfData()"
    nzTableLayout="fixed"
    [nzLoading]="loading()"
    [(nzPageIndex)]="pageIndex"
    [nzScroll]="{ x: '1800px' }"
  >
    <thead>
      <tr>
        <th nzLeft nzWidth="200">日期</th>
        @for (d of showColumn(); track $index) {
          @if (d.useInFilter) {
            <th nzCustomFilter>
              {{ d.name }}
              <nz-filter-trigger
                [nzDropdownMenu]="menu"
                [nzVisible]="visible()[d.extendName]"
                (nzVisibleChange)="visibleChange($event)"
                [nzActive]="checked()[d.extendName] && checked()[d.extendName].length !== 0"
              >
                <nz-icon nzType="filter" />
              </nz-filter-trigger>
              <nz-dropdown-menu #menu="nzDropdownMenu">
                <div
                  class="flex flex-col gap-1 py-2 px-3 bg-white w-[260px] h-75 overflow-y-auto"
                  style="border: solid 1px #ccc"
                >
                  <div class="flex items-center gap-2">
                    <button nz-button nzType="primary" nzSize="small" (click)="saveFilter(d.extendName)">确定</button>
                    <nz-input-group [nzSuffix]="suffixIconSearch">
                      <input type="text" nz-input placeholder="输入关键字" />
                    </nz-input-group>
                    <ng-template #suffixIconSearch>
                      <nz-icon nzType="search" />
                    </ng-template>
                  </div>
                  <div class="px-6 pt-1">
                    <div>
                      <label
                        nz-checkbox
                        [ngModel]="allChecked()[d.extendName]"
                        (ngModelChange)="updateAllChecked($event, d.extendName)"
                      >
                        全部
                      </label>
                    </div>

                    <nz-checkbox-group
                      [ngModel]="checked()[d.extendName]"
                      (ngModelChange)="updateSingleChecked($event, d.extendName)"
                    >
                      <nz-row>
                        @for (value of d.values; track $index) {
                          <nz-col nzSpan="24">
                            <label nz-checkbox [nzValue]="value">
                              {{ value.showValue }}
                            </label>
                          </nz-col>
                        }
                      </nz-row>
                    </nz-checkbox-group>
                  </div>
                </div>
              </nz-dropdown-menu>
            </th>
          } @else {
            <th [nzSortFn]="d.sortFn" [nzSortDirections]="d.sortDirections" [(nzSortOrder)]="d.sortOrder">
              {{ d.name }}
            </th>
          }
        }
      </tr>
    </thead>
    <tbody>
      @for (data of fixedTable.data; track data) {
        <tr>
          @if (dtType() === 'dt') {
            <td nzLeft>{{ data.dt }}</td>
          } @else if (dtType() === 'ym') {
            <td nzLeft>{{ data.ym }}</td>
          } @else {
            <td nzLeft>{{ data.yw }}</td>
          }
          @for (item of showColumn(); track item) {
            @if (item.extendName.includes('rate')) {
              <td>
                {{ data[item.extendName] ? (data[item.extendName] * 100).toFixed(2) + '%' : '0' }}
              </td>
            } @else {
              <td>
                {{ data[item.extendName] ?? '-' }}
              </td>
            }
          }
        </tr>
      }
    </tbody>
  </nz-table>
</div>
