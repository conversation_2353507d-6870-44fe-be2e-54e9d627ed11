import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
  inject,
  input,
  OnChanges,
  signal,
} from '@angular/core'
import { FormsModule } from '@angular/forms'
import { DateFilterComponent, ChooseShowFieldComponent } from '../../../components'
import { QueryEngineFormService } from '@common/service/query-engine'
import { NzButtonModule } from 'ng-zorro-antd/button'
import { NzIconModule } from 'ng-zorro-antd/icon'
import { NzDropDownModule } from 'ng-zorro-antd/dropdown'
import { NzTableModule } from 'ng-zorro-antd/table'
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox'
import { NzGridModule } from 'ng-zorro-antd/grid'
import { NzInputModule } from 'ng-zorro-antd/input'
import { find } from 'lodash'
import { RadioModule } from '@shared/modules/headless'
import { combineLatest, debounceTime, finalize, startWith } from 'rxjs'
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop'
import { BusinessService } from '../../../business.service'
import { QueryEngineApiService } from '@api/query-engine'
import { NzPopoverModule } from 'ng-zorro-antd/popover'
import { SwitchMap } from '@common/decorator'
import { CaerusApiService } from '@api/caerus'

@Component({
  selector: 'app-core',
  imports: [
    DateFilterComponent,
    FormsModule,
    NzButtonModule,
    NzIconModule,
    ChooseShowFieldComponent,
    NzDropDownModule,
    NzTableModule,
    NzCheckboxModule,
    NzGridModule,
    NzInputModule,
    RadioModule,
    NzPopoverModule,
  ],
  templateUrl: './core.component.html',
  styles: ``,
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [QueryEngineFormService],
})
export class CoreComponent implements AfterViewInit, OnChanges {
  readonly service = inject(BusinessService)
  readonly formService = inject(QueryEngineFormService)
  readonly destroyRef = inject(DestroyRef)
  readonly queryService = inject(QueryEngineApiService)
  readonly apiService = inject(CaerusApiService)

  detailType = input('core')
  detailType$ = toObservable(this.detailType)
  config = input(null)
  index = signal(0)

  listOfData = signal([])
  transform_date = signal('td')

  visible = signal({})
  checked = signal<any>({})
  allChecked = signal({})
  data = signal([])
  showFieldList = signal([])
  time = signal(null)
  loading = signal(false)
  showColumn = signal([])
  pageIndex = signal(1)
  dtType = signal('ym')
  _body = signal(null)
  checkedShadow = signal({})
  allCheckedShadow = signal({})
  regionType = signal('city_bio_region')
  region_config = signal([])
  region_config$ = toObservable(this.region_config)

  ngOnChanges(): void {
    if (this.config()) {
      this.index.set(0)
      this.regionType.set('city_bio_region')
      this.renderTransferColumn()
    }
  }

  ngAfterViewInit(): void {
    this.getDimensionConfig()
    this._subscribeChange()
  }

  getDimensionConfig() {
    this.apiService.fetchDimensionConfig('dim_region').subscribe(res => {
      this.region_config.set(res.data['region'])
    })
  }

  renderTransferColumn() {
    if (!this.region_config() || !this.config()) {
      return
    }
    const _config = this.config()[this.detailType()]
    this.checked.set({})
    this.allChecked.set({})
    // console.log('show', _config)
    if (this.detailType() === 'core') {
      const arr = _config.metrics.map((m, index) => {
        return {
          ...m,
          name: m.showName,
          defaultShow: true,
          sortOrder: index === 0 ? 'descend' : null,
          sortDirections: ['ascend', 'descend', null],
          sortFn: (a, b) => a[m.extendName] - b[m.extendName],
        }
      })
      const region = {
        name:
          this.regionType() === 'city_bio_region' ? '区域' : this.regionType() === 'province_name' ? '省份' : '城市',
        defaultShow: true,
        useInFilter: true,
        extendName: this.regionType(),
        values: find(this.region_config(), ['extendName', this.regionType()])?.values,
      }
      this.data.set([region, ..._config.dimensions, ...arr])
    } else {
      const arr = []
      _config.metrics[this.index()].level.forEach((m, index) => {
        arr.push({
          ...m,
          name: m.userDefAliasName,
          extendName: `${_config.metrics[this.index()].extendName}_${m.levelNum}`,
          defaultShow: true,
          sortOrder: index === 0 ? 'descend' : null,
          sortDirections: ['ascend', 'descend', null],
          sortFn: (a, b) =>
            a[`${_config.metrics[this.index()].extendName}_${m.levelNum}`] -
            b[`${_config.metrics[this.index()].extendName}_${m.levelNum}`],
        })
        if (m.rateAliasName && m.rateAliasName !== '') {
          arr.push({
            ...m,
            name: m.rateAliasName,
            extendName: `${_config.metrics[this.index()].extendName}_rate_${m.levelNum}`,
            defaultShow: true,
            sortOrder: index === 0 ? 'descend' : null,
            sortDirections: ['ascend', 'descend', null],
            sortFn: (a, b) =>
              a[`${_config.metrics[this.index()].extendName}_rate_${m.levelNum}`] -
              b[`${_config.metrics[this.index()].extendName}_rate_${m.levelNum}`],
          })
        }
      })
      const otherMetrics = find(_config.timestamp, ['extendName', this.transform_date()])?.otherMetrics
      otherMetrics.forEach(m => {
        arr.push({
          ...m,
          name: m.showName,
          defaultShow: true,
          sortOrder: null,
          sortDirections: ['ascend', 'descend', null],
          sortFn: (a, b) => a[m.extendName] - b[m.extendName],
        })
      })
      this.data.set([..._config.dimensions, ...arr])
    }
    this.showFieldList.set(
      this.data().map(d => {
        return {
          label: d.name,
          value: d.extendName,
          show: d.defaultShow ?? false,
        }
      })
    )
    this.showColumn.set(this.data().filter(d => d.defaultShow))
    if (this.detailType() === 'core') {
      this._getTableData()
    } else {
      this._getTransferTableData()
    }
  }

  @SwitchMap()
  _getTransferTableData() {
    if (!this.time() || !this.config()) {
      return
    }
    this.loading.set(true)
    this.pageIndex.set(1)
    const area = this.service.area()
    const body = this.formService.value()
    body.dt = this.time()
    body.dataFillConfig = {
      open: 0,
      fillRangeType: 2,
      fillValue: 0,
    }
    const _dimensions = [
      {
        id: null,
        extendName: this.dtType(),
      },
      ...this.config()[this.detailType()]?.dimensions,
    ]
    body.dimensions = _dimensions.map(d => ({
      extendName: d.extendName,
    }))
    const items = []
    if (area) {
      items.push({
        conditionType: 2,
        condition: '=',
        id: null,
        extendName: area.extendName,
        value: [
          {
            key: area.key,
            value: area.value,
          },
        ],
        valueType: null,
      })
    }
    const checkedArr = Object.keys(this.checked())
    if (checkedArr.length !== 0) {
      checkedArr.forEach(check => {
        if (this.checked()[check].length !== 0) {
          items.push({
            conditionType: 2,
            condition: 'in',
            id: null,
            extendName: check,
            value: this.checked()[check].map(c => {
              return {
                key: c.key,
                value: c.value,
              }
            }),
            valueType: null,
          })
        }
      })
    }
    body.filter = {
      items,
      type: null,
    }
    const metrics = this.config()[this.detailType()]?.metrics[this.index()]
    body.metrics = metrics.level.map((l, i) => {
      return {
        extendName: metrics.extendName,
        userDefExtendName: `${metrics.extendName}_${i + 1}`,
        userDefAliasName: l.userDefAliasName,
        filter: {
          items: [
            {
              conditionType: 2,
              condition: '>=',
              extendName: this.transform_date(),
              value: [
                {
                  key: l.levelNum,
                  value: l.levelNum,
                },
              ],
              valueType: null,
            },
          ],
          type: null,
        },
      }
    })
    const otherMetrics = find(this.config()[this.detailType()].timestamp, [
      'extendName',
      this.transform_date(),
    ])?.otherMetrics
    otherMetrics.forEach(m => {
      body.metrics.push({
        extendName: m.extendName,
        filter: {
          items: [],
          type: null,
        },
        userDefExtendName: m.extendName,
      })
    })
    body.userDefExpressions = metrics.level
      .filter(l => l.levelNum !== 1)
      .map(l => {
        return {
          expression: `${metrics.extendName}_${l.levelNum}/${metrics.extendName}_${l.levelNum - 1}`,
          userDefExtendName: `${metrics.extendName}_rate_${l.levelNum}`,
          userDefAliasName: l.rateAliasName,
        }
      })
    this._body.set(body)
    this.queryService
      .search(body, 'transfer-table-data')
      .pipe(finalize(() => this.loading.set(false)))
      .subscribe(res => {
        console.log('transfer-table--data', res)
        if (res.data) {
          this.listOfData.set(res.data.data)
        } else {
          this.listOfData.set([])
        }
      })
  }

  @SwitchMap()
  _getTableData() {
    if (!this.time() || !this.config() || this.region_config()?.length === 0) {
      return
    }
    this.loading.set(true)
    this.pageIndex.set(1)
    const area = this.service.area()
    const body = this.formService.value()
    body.dataFillConfig = {
      open: 0,
      fillRangeType: 2,
      fillValue: 0,
    }
    body.dt = this.time()
    const _dimensions = [
      {
        id: null,
        extendName: this.dtType(),
      },
      ...this.config()[this.detailType()]?.dimensions,
      find(this.region_config(), ['extendName', this.regionType()]),
    ]
    body.dimensions = _dimensions.map(d => ({
      extendName: d.extendName,
    }))
    body.metrics = this.config()[this.detailType()]?.metrics
    const items = []
    if (area) {
      items.push({
        conditionType: 2,
        condition: '=',
        id: null,
        extendName: area.extendName,
        value: [
          {
            key: area.key,
            value: area.value,
          },
        ],
        valueType: null,
      })
    }
    const checkedArr = Object.keys(this.checked())
    if (checkedArr.length !== 0) {
      checkedArr.forEach(check => {
        if (this.checked()[check].length !== 0) {
          items.push({
            conditionType: 2,
            condition: 'in',
            id: null,
            extendName: check,
            value: this.checked()[check].map(c => {
              return {
                key: c.key,
                value: c.value,
              }
            }),
            valueType: null,
          })
        }
      })
    }
    body.filter = {
      items,
      type: null,
    }
    this._body.set(body)
    this.queryService
      .search(body, 'table-data')
      .pipe(finalize(() => this.loading.set(false)))
      .subscribe(res => {
        if (res.data) {
          this.listOfData.set(res.data.data)
        } else {
          this.listOfData.set([])
        }
      })
  }

  _subscribeChange() {
    combineLatest([this.service.area$.pipe(startWith(null)), this.formService.form.valueChanges])
      .pipe(debounceTime(100), takeUntilDestroyed(this.destroyRef))
      .subscribe(([a, b]) => {
        // console.log('bbbb', a, b)
        if (b.dt.startTime && b.dt.endTime) {
          this.time.set(b.dt)
        }

        this.dtType.set(b.dtType)
        if (this.detailType() === 'core') {
          this._getTableData()
        } else {
          this._getTransferTableData()
        }

        if (this.regionType() === 'city_bio_region') {
          if (a && a.extendName === 'province_name') {
            this.regionType.set('province_name')
          }
          if (a && (a.extendName === 'city_name' || a.extendName === 'is_top20_city')) {
            this.regionType.set('city_name')
          }
        }
        if (this.regionType() === 'province_name') {
          if (a && (a.extendName === 'city_name' || a.extendName === 'is_top20_city')) {
            this.regionType.set('city_name')
          }
        }
        if (this.regionType() === 'city_name') {
          if (a && a.extendName === 'province_name') {
            this.regionType.set('province_name')
          }
        }
      })

    this.service.type$.pipe(debounceTime(100), takeUntilDestroyed(this.destroyRef)).subscribe(() => {
      this.index.set(0)
      // this.dtType.set('ym');
    })
    this.region_config$.pipe(debounceTime(100), takeUntilDestroyed(this.destroyRef)).subscribe(() => {
      this.renderTransferColumn()
    })
  }

  listChange(list) {
    const arr = this.data().map(d => {
      return {
        ...d,
        defaultShow: find(list, ['value', d.extendName])?.show ?? false,
      }
    })
    this.data.set(arr)
    this.showColumn.set(this.data().filter(d => d.defaultShow))
  }

  updateAllChecked(v, name) {
    let values = []
    if (v) {
      values = find(this.data(), ['extendName', name])?.values
    }
    this.checked.set({
      ...this.checked(),
      [name]: values,
    })
  }

  updateSingleChecked(value, name) {
    this.checked.set({
      ...this.checked(),
      [name]: value,
    })
    const length = find(this.data(), ['extendName', name])?.values.length
    if (this.checked()[name]?.length === length) {
      this.allChecked.set({
        ...this.allChecked(),
        [name]: true,
      })
    } else {
      this.allChecked.set({
        ...this.allChecked(),
        [name]: false,
      })
    }
    console.log('checked', this.checked())
  }

  saveFilter(name) {
    if (this.detailType() === 'core') {
      this._getTableData()
    } else {
      this._getTransferTableData()
    }
    this.checkedShadow.set(this.checked())
    this.allCheckedShadow.set(this.allChecked())
    this.visible.set({
      [name]: false,
    })
  }

  visibleChange(v) {
    if (!v) {
      this.checked.set(this.checkedShadow())
      this.allChecked.set(this.allCheckedShadow())
    }
  }

  download() {
    this.queryService.exportToExcel(this._body()).subscribe(() => {})
  }
}
