import { ChangeDetectionStrategy, Component, DestroyRef, inject, input, OnChanges, signal } from '@angular/core'
import { FormsModule } from '@angular/forms'
import { NzRadioModule } from 'ng-zorro-antd/radio'
import { RadioModule } from '@shared/modules/headless'
import { NzPopoverModule } from 'ng-zorro-antd/popover'
import { IconMapArrowRightComponent, IconSuccessFillComponent } from '@shared/modules/icons'
import { DecimalPipe } from '@angular/common'
import { ProgressComponent } from '../../components'
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox'
import { QueryOutputVo } from '@api/query-engine/model'
import { ProgressTrend } from './lib'
import { LegendControlService, LegendItemClickHandler } from '@common/service'
import { QueryEngineFormService } from '@common/service/query-engine'
import { BaseHighCharts } from '@common/chart/highcharts'
import { LineSpinComponent } from '@shared/components/line-spin'
import { GraphComponent } from '@shared/components/graph'
import { BusinessService } from '../../business.service'
import { NzGridModule } from 'ng-zorro-antd/grid'
import { combineLatest, debounceTime, finalize, startWith } from 'rxjs'
import { takeUntilDestroyed } from '@angular/core/rxjs-interop'
import { QueryEngineApiService } from '@api/query-engine'
import { flattenDeep } from 'lodash'
import { SwitchMap } from '@common/decorator'
import { getMonthFirstAndLastDay } from '@common/class'

@Component({
  selector: 'app-overview',
  imports: [
    NzRadioModule,
    FormsModule,
    RadioModule,
    NzPopoverModule,
    IconSuccessFillComponent,
    DecimalPipe,
    ProgressComponent,
    NzCheckboxModule,
    LineSpinComponent,
    GraphComponent,
    NzGridModule,
    IconMapArrowRightComponent,
  ],
  templateUrl: './overview.component.html',
  styleUrl: `./overview.component.css`,
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [LegendControlService, QueryEngineFormService],
})
export class OverviewComponent implements OnChanges {
  readonly legendControlService = inject(LegendControlService)
  readonly service = inject(BusinessService)
  readonly destroyRef = inject(DestroyRef)
  readonly formService = inject(QueryEngineFormService)
  readonly queryService = inject(QueryEngineApiService)

  config = input(null)

  dt = signal('accumulate')
  promt_channel_source_type = signal(null)
  c_thilev_channel = signal(null)
  media_type = signal(null)

  index = signal(0)
  loading = signal(false)
  errorMessage = signal(null)
  option = signal<BaseHighCharts>(null)
  checkValue = signal([null])
  card_loading = signal(true)
  card_data = signal(null)

  radioMap = {
    day: ':dt_DIFF_RATIO',
    week: ':yw_DIFF_RATIO',
    month: ':ym_DIFF_RATIO',
    year: ':year_DIFF_RATIO',
  }

  ngOnChanges(): void {
    if (this.config()) {
      this._subscribeChange()
    }
  }

  private _subscribeChange() {
    combineLatest([this.service.deadline$.pipe(startWith(null)), this.service.area$.pipe(startWith(null))])
      .pipe(debounceTime(100), takeUntilDestroyed(this.destroyRef))
      .subscribe(() => {
        this._getCardData()
        this._getChartData()
      })

    this.service.type$.pipe(debounceTime(100), takeUntilDestroyed(this.destroyRef)).subscribe(() => {
      this.promt_channel_source_type.set(null)
      this.media_type.set(null)
      this.index.set(0)
      this.c_thilev_channel.set(null)
      this.dt.set('accumulate')
    })
  }

  reRender() {
    this._getCardData()
    this._getChartData()
  }

  changeSource() {
    this.checkValue.set([this.promt_channel_source_type()])
    this.media_type.set(null)
    this._getCardData()
    this._getChartData()
  }

  @SwitchMap()
  _getCardData() {
    const deadline = this.service.deadline()
    const area = this.service.area()
    if (!deadline) {
      return
    }
    this.card_loading.set(true)
    const body = this.formService.value()
    body.dt = {
      startTime: deadline,
      endTime: deadline,
    }
    body.dimensions = [
      {
        id: null,
        extendName: 'dt',
        predefineCompareType: this.dt() === 'accumulate' ? ['ym', 'year'] : ['dt', 'yw'],
      },
    ]
    body.dataFillConfig = {
      open: 1,
      fillRangeType: 2,
      fillValue: 0,
    }
    body.metrics = flattenDeep(
      this.config().cardList.map(c => {
        return c[this.dt()].map(item => {
          return {
            extendName: item.extendName,
          }
        })
      })
    )
    const filters = [area, this.promt_channel_source_type(), this.c_thilev_channel(), this.media_type()]
    const items = filters
      .filter(f => f)
      .map(f => {
        return {
          conditionType: 2,
          condition: '=',
          id: null,
          extendName: f.extendName,
          value: [
            {
              key: f.key,
              value: f.value,
            },
          ],
          valueType: null,
        }
      })
    body.filter = {
      items,
      type: null,
    }
    this.queryService
      .search(body, 'offplt-overview')
      .pipe(finalize(() => this.card_loading.set(false)))
      .subscribe(res => {
        // console.log('card--data', res);
        if (!res.data) {
          this.card_data.set(null)
          return
        }
        if (res?.data?.data.length !== 0) {
          this.card_data.set(res.data.data[0])
        } else {
          this.card_data.set(null)
        }
      })
  }

  _getChartData() {
    const deadline = this.service.deadline()
    const area = this.service.area()
    if (!deadline) {
      return
    }
    if (!this.config()?.cardList) {
      return
    }
    this.loading.set(true)
    const body = this.formService.value()

    const [startTime, endTime] = getMonthFirstAndLastDay(deadline)
    body.dt = {
      startTime,
      endTime,
    }
    body.dimensions = [
      {
        id: null,
        extendName: 'dt',
        predefineCompareType: this.dt() === 'accumulate' ? ['ym', 'year'] : ['dt', 'yw'],
      },
    ]
    body.dataFillConfig = {
      open: 1,
      fillRangeType: 2,
      fillValue: 0,
    }
    body.queryType = 'compare'
    body.metrics = flattenDeep(
      this.checkValue().map(check => {
        return this.config()?.cardList[this.index()][this.dt()].map(item => {
          return {
            extendName: item.extendName,
            userDefAliasName: `${item.aliasName}${check ? `_${check.value}` : ''}`,
            filter: {
              items: check
                ? [
                    {
                      conditionType: 2,
                      condition: '=',
                      id: null,
                      extendName: check.extendName,
                      value: [
                        {
                          key: check.key,
                          value: check.value,
                        },
                      ],
                      valueType: null,
                    },
                  ]
                : [],
              type: null,
            },
          }
        })
      })
    )
    const filters = [area, this.c_thilev_channel(), this.media_type()]
    const items = filters
      .filter(f => f)
      .map(f => {
        return {
          conditionType: 2,
          condition: '=',
          id: null,
          extendName: f.extendName,
          value: [
            {
              key: f.key,
              value: f.value,
            },
          ],
          valueType: null,
        }
      })
    body.filter = {
      items,
      type: null,
    }
    this.legendControlService.reset()
    this.queryService
      .search(body, 'offplt-overview-chart')
      .pipe(finalize(() => this.loading.set(false)))
      .subscribe(res => {
        // console.log('chart--data', res);
        if (res.data) {
          this._setChartData(res.data)
        } else {
          this.option.set(null)
          this.errorMessage.set(res.message)
        }
      })
  }

  renderNum(extandName, time) {
    const data = this.card_data() ? this.card_data()[`${extandName}${this.radioMap[time]}`] : null
    if (!data) {
      return {
        color: '#000',
        text: '-',
      }
    }
    if (Number(data) < 0) {
      return {
        color: 'text-green-500',
        text: `${Number(data)}%`,
      }
    }
    return {
      color: 'text-red-500',
      text: `+${Number(data)}%`,
    }
  }

  private _setChartData(data: QueryOutputVo) {
    try {
      const chart = new ProgressTrend(data)
      if (this.media_type() || this.service.area() || this.c_thilev_channel()) {
        chart.legend.enabled = false
      }
      const legendItemClick = LegendItemClickHandler(this.legendControlService)
      chart.plotOptions.series.events = { legendItemClick }
      this.option.set(chart?.getOption() || null)
    } catch (e) {
      this.option.set(null)
      this.errorMessage.set(e)
      console.error(e)
    }
  }
}
