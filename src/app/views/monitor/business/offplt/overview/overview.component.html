<div class="px-5 flex flex-col gap-5 py-3">
  <header
    class="relative flex items-center justify-between gap-x-2 px-5 h-12 border-l-4 border-emerald-500 bg-linear-to-r from-neutral-300/40 via-white via-20%"
  >
    <div class="flex items-center gap-x-2">
      <span class="text-lg">推广规模概览</span>
      @if (service.type() === 'passenger') {
        <span class="text-xs text-neutral-400 ml-0.5">
          说明：此处指标均为自有渠道数据；统计日期: 注册指标按注册时间，首单和召回指标按乘客下单时间。
        </span>
      } @else {
        <span class="text-xs text-neutral-400 ml-0.5">
          说明：统计日期: 认证指标按认证时间，首单和召回指标按车主接单时间。
        </span>
      }
    </div>
    <!-- <nz-radio-group
      class="scale-90 origin-right"
      [(ngModel)]="dt"
      nzButtonStyle="solid"
      nzSize="small"
      (ngModelChange)="reRender()"
    >
      <label
        style="width: 80px"
        class="text-center"
        nz-radio-button
        nzValue="accumulate"
        >本月累计</label
      >
      <label
        nz-radio-button
        nzValue="yesterday"
        style="width: 80px"
        class="text-center"
        >昨日
      </label>
    </nz-radio-group> -->
  </header>
  <span class="flex items-center gap-x-1.5 font-black text-base">
    <MapArrowRightIcon />
    推广规模指标完成度
  </span>

  <div class="flex items-center flex-wrap gap-x-5 gap-y-2 px-5">
    <div class="flex items-center">
      <label class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap">
        统计口径：
      </label>
      <app-radio-group class="relative flex gap-1" [(ngModel)]="dt" (ngModelChange)="reRender()">
        <app-radio class="tag-radio" activeClass="active" value="accumulate">当月累计</app-radio>
        <app-radio class="tag-radio" activeClass="active" value="yesterday">昨日</app-radio>
      </app-radio-group>
    </div>
    <div class="flex items-center">
      <ng-template #metricsTitleTemplate>
        {{ config()?.filter?.promt_channel_source_type?.name }}
        <span class="text-xs opacity-30 px-1">({{ config()?.filter?.promt_channel_source_type?.aliasName }})</span>
      </ng-template>

      <ng-template #metricsContentTemplate>
        <div class="leading-normal" [innerHTML]="config()?.filter?.promt_channel_source_type?.bizExpression"></div>
      </ng-template>
      <label
        nz-popover
        [nzPopoverMouseEnterDelay]="0.5"
        [nzPopoverTitle]="metricsTitleTemplate"
        [nzPopoverContent]="metricsContentTemplate"
        class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap"
      >
        来源类型：
      </label>
      <app-radio-group
        class="relative flex gap-1"
        [(ngModel)]="promt_channel_source_type"
        (ngModelChange)="changeSource()"
      >
        <app-radio class="tag-radio" activeClass="active" [value]="null">全部</app-radio>
        @for (item of config()?.filter?.promt_channel_source_type?.values; track item) {
          <app-radio class="tag-radio" activeClass="active" [value]="item">
            {{ item.showValue }}
          </app-radio>
        }
      </app-radio-group>
    </div>
    <div class="flex items-center">
      <ng-template #metricsTitleTemplate2>
        {{ config()?.filter?.media_type?.name }}
        <span class="text-xs opacity-30 px-1">({{ config()?.filter?.media_type?.aliasName }})</span>
      </ng-template>

      <ng-template #metricsContentTemplate2>
        <div class="leading-normal" [innerHTML]="config()?.filter?.media_type?.bizExpression"></div>
      </ng-template>
      <label
        nz-popover
        [nzPopoverMouseEnterDelay]="0.5"
        [nzPopoverTitle]="metricsTitleTemplate2"
        [nzPopoverContent]="metricsContentTemplate2"
        class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap"
      >
        推广渠道类型：
      </label>
      <app-radio-group class="relative flex gap-1" [(ngModel)]="media_type" (ngModelChange)="reRender()">
        <app-radio class="tag-radio" activeClass="active" [value]="null">全部</app-radio>
        @if (promt_channel_source_type()?.value === '推广') {
          @for (item of config()?.filter?.media_type?.values; track item) {
            <app-radio class="tag-radio" activeClass="active" [value]="item">
              {{ item.showValue }}
            </app-radio>
          }
        }
      </app-radio-group>
    </div>
    <div class="flex items-center">
      <ng-template #metricsTitleTemplate3>
        {{ config()?.filter?.c_thilev_channel?.name }}
        <span class="text-xs opacity-30 px-1">({{ config()?.filter?.c_thilev_channel?.aliasName }})</span>
      </ng-template>

      <ng-template #metricsContentTemplate3>
        <div class="leading-normal" [innerHTML]="config()?.filter?.c_thilev_channel?.bizExpression"></div>
      </ng-template>
      <label
        nz-popover
        [nzPopoverMouseEnterDelay]="0.5"
        [nzPopoverTitle]="metricsTitleTemplate3"
        [nzPopoverContent]="metricsContentTemplate3"
        class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap"
      >
        产品渠道：
      </label>
      <app-radio-group class="relative flex gap-1" [(ngModel)]="c_thilev_channel" (ngModelChange)="reRender()">
        <app-radio class="tag-radio" activeClass="active" [value]="null">全部</app-radio>
        @for (item of config()?.filter?.c_thilev_channel?.values; track item) {
          <app-radio class="tag-radio" activeClass="active" [value]="item">
            {{ item.showValue }}
          </app-radio>
        }
      </app-radio-group>
    </div>
  </div>

  <app-radio-group
    class="grid grid-cols-1 grid-flow-row md:grid-cols-3 lg:grid-cols-5 gap-5"
    [(ngModel)]="index"
    (ngModelChange)="_getChartData()"
  >
    @for (item of config()?.cardList; track $index) {
      @if (this.card_loading()) {
        <app-line-spin />
      } @else {
        <ng-template #metricsTitleTemplate>
          {{ item?.showName }}
          <span class="text-xs opacity-30 px-1">({{ item?.aliasName }})</span>
        </ng-template>

        <ng-template #metricsContentTemplate>
          <div class="leading-normal" [innerHTML]="item?.bizExpression"></div>
        </ng-template>

        <app-radio
          nz-popover
          class="card-radio"
          activeClass="active"
          [nzPopoverMouseEnterDelay]="0.5"
          [nzPopoverTitle]="metricsTitleTemplate"
          [nzPopoverContent]="metricsContentTemplate"
          [value]="$index"
        >
          @let metric = dt() === 'accumulate' ? item.accumulateMetric.extendName : item.yesterdayMetric.extendName;
          @let target = dt() === 'accumulate' ? item.accumulateTarget.extendName : item.yesterdayTarget.extendName;
          <div class="flex flex-col justify-center text-center py-3">
            <h2 class="text-lg">
              {{ item?.showName }}
            </h2>
            <h3 class="text-base">{{ card_data()?.[metric] | number }}</h3>
            <div class="flex items-center">
              @if (dt() === 'accumulate') {
                <div class="w-[55%]">
                  @let month = renderNum(metric, 'month');
                  月环比：
                  <span [class]="month.color">
                    {{ month.text }}
                  </span>
                </div>
                <div>
                  @let year = renderNum(metric, 'year');
                  年同比：
                  <span [class]="year.color">
                    {{ year.text }}
                  </span>
                </div>
              } @else {
                <div class="w-[55%]">
                  @let day = renderNum(metric, 'day');
                  日环比：
                  <span [class]="day.color">
                    {{ day.text }}
                  </span>
                </div>
                <div>
                  @let week = renderNum(metric, 'week');
                  周同比：
                  <span [class]="week.color">
                    {{ week.text }}
                  </span>
                </div>
              }
            </div>
            @if (!media_type() && !service.area() && !c_thilev_channel()) {
              <div class="w-full pt-2">
                <app-progress
                  [style]="3"
                  [value]="card_data()?.[metric]"
                  [total]="card_data()?.[target]"
                  label="完成度"
                  [budgetLabel]="dt() === 'yesterday' ? '昨日目标' : 'MTD目标'"
                />
              </div>
            }
          </div>
          <SuccessFillIcon *radioChecked class="absolute right-3 top-3 text-blue-600 text-xl" />
        </app-radio>
      }
    }
  </app-radio-group>
  <div class="flex flex-col h-96 shadow-md rounded-sm border border-neutral-100 p-4">
    <div class="flex items-center gap-6 justify-between">
      <!-- <div class="flex items-center">
        <label
          class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap"
        >
          指标选择：
        </label>
        <app-radio-group
          class="relative flex gap-1"
          [(ngModel)]="index"
          (ngModelChange)="_getChartData()"
        >
          @for(item of config()?.cardList; track item) {
          <ng-template #metricsTitleTemplate>
            {{ item?.showName }}
            <span class="text-xs opacity-30 px-1">({{ item?.aliasName }})</span>
          </ng-template>

          <ng-template #metricsContentTemplate>
            <div class="leading-normal" [innerHTML]="item?.bizExpression"></div>
          </ng-template>
          <app-radio
            class="tag-radio"
            activeClass="active"
            [value]="$index"
            nz-popover
            [nzPopoverMouseEnterDelay]="0.5"
            [nzPopoverTitle]="metricsTitleTemplate"
            [nzPopoverContent]="metricsContentTemplate"
          >
            {{ item.showName }}
          </app-radio>
          }
        </app-radio-group>
      </div> -->
      <div class="flex items-center">
        <label class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap">
          来源渠道：
        </label>
        <nz-checkbox-group [(ngModel)]="checkValue" (ngModelChange)="_getChartData()">
          <label nz-checkbox class="text-xs! ml-0!" [nzValue]="null">全部</label>
          @for (item of config()?.filter?.promt_channel_source_type?.values; track item) {
            <label nz-checkbox class="text-xs! ml-0!" [nzValue]="item">
              {{ item.showValue }}
            </label>
          }
        </nz-checkbox-group>
      </div>
      <div class="text-xs text-neutral-400 px-4">说明：点击图例默认单选，长按shift键点击可多选。</div>
    </div>
    <div class="flex-1 min-h-0 relative flex items-center justify-center text-xs">
      @if (loading()) {
        <app-line-spin />
      } @else {
        @if (option()) {
          <app-graph class="absolute inset-2" [options]="option()" showAnnotations showAvgPlotLines />
        } @else if (errorMessage()) {
          <span class="text-slate-600 text-xs">{{ errorMessage() }}</span>
        }
      }
    </div>
  </div>
</div>
