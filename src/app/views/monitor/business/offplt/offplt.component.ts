import { AfterViewInit, ChangeDetectionStrategy, Component, inject, signal } from '@angular/core'
import { DateComponent } from '../components'
import { RadioModule } from '@shared/modules/headless'
import { FormsModule } from '@angular/forms'
import { OverviewComponent } from './overview/overview.component'
import { AreaRankingComponent } from './area-ranking/area-ranking.component'
import { TransformComponent } from './transform/transform.component'
import { DetailComponent } from './detail/detail.component'
import { BusinessService } from '../business.service'
import { CaerusApiService } from '@api/caerus'
import { PAGE_NAME, PageEnterLeaveDirective } from '@common/directive'
import { CustomerRetentionComponent } from './customer-retention/customer-retention.component'
import { ProgressLoaderComponent } from '@shared/components/progress-loader'

@Component({
  selector: 'app-offplt',
  imports: [
    DateComponent,
    RadioModule,
    FormsModule,
    OverviewComponent,
    TransformComponent,
    DetailComponent,
    CustomerRetentionComponent,
    AreaRankingComponent,
    ProgressLoaderComponent,
  ],
  templateUrl: './offplt.component.html',
  host: {
    'class': 'block relative',
  },
  hostDirectives: [PageEnterLeaveDirective],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [{ provide: PAGE_NAME, useValue: window.location.pathname }],
})
export class OffpltComponent {
  readonly service = inject(BusinessService)
  readonly apiService = inject(CaerusApiService)

  config = signal(null)

  fetchConfig(key) {
    this.apiService.fetchConfig(key).subscribe(res => {
      if (res.data) {
        console.log('pass', res.data)
        this.config.set(res.data)
      }
    })
  }

  changeType() {
    const key =
      this.service.type() === 'passenger'
        ? 'business_monitoring_promotion_detail_pass_v2'
        : 'business_monitoring_promotion_detail_driver_v2'
    this.fetchConfig(key)
  }
}
