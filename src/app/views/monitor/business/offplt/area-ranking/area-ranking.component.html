<div class="px-5 py-3">
  <div class="flex items-center gap-x-1.5 font-black text-base px-5">
    <MapArrowRightIcon />
    推广规模区域排行
  </div>

  <div class="flex items-center flex-wrap gap-x-5 gap-y-5 p-5">
    <div class="w-full flex items-center">
      <label class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap">
        日期范围：
      </label>
      <app-date-compare compareBtn deleteBtn class="px-0" [dateLabel]="null" />
    </div>

    <div class="flex items-center">
      <label class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap">
        区域粒度：
      </label>
      <app-radio-group class="flex gap-1" [(ngModel)]="area">
        <div class="flex gap-1 flex-wrap items-start">
          <app-radio class="tag-radio" activeClass="active" [disabled]="regionDisabled()" value="city_bio_region">
            按区域
          </app-radio>
          <app-radio class="tag-radio" activeClass="active" [disabled]="provinceDisabled()" value="province_name">
            按省份
          </app-radio>
          <app-radio class="tag-radio" activeClass="active" value="city_name">按城市</app-radio>
        </div>
      </app-radio-group>
    </div>

    <div class="flex items-center">
      <label class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap">
        来源类型：
      </label>
      <app-radio-group class="relative flex gap-1" [(ngModel)]="promt_channel_source_type">
        <app-radio class="tag-radio" activeClass="active" [value]="null">全部</app-radio>
        @for (item of promtChannelSourceTypeOptions(); track $index) {
          <app-radio class="tag-radio" activeClass="active" [value]="item">{{ item.showValue }}</app-radio>
        }
      </app-radio-group>
    </div>

    <div class="flex items-center">
      <label class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap">
        推广渠道类型：
      </label>
      <app-radio-group class="relative flex gap-1" [(ngModel)]="media_type">
        <app-radio class="tag-radio" activeClass="active" [value]="null">全部</app-radio>
        @if (promt_channel_source_type()?.value === '推广') {
          @for (item of mediaTypeOptions(); track $index) {
            <app-radio class="tag-radio" activeClass="active" [value]="item">{{ item.showValue }}</app-radio>
          }
        }
      </app-radio-group>
    </div>

    <div class="flex items-center w-full">
      <div class="flex items-center">
        <label class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap">
          产品渠道：
        </label>
        <app-radio-group class="relative flex gap-1" [(ngModel)]="c_thilev_channel">
          <app-radio class="tag-radio" activeClass="active" [value]="null">全部</app-radio>
          @for (item of thilevChannelOptions(); track $index) {
            <app-radio class="tag-radio" activeClass="active" [value]="item">{{ item.showValue }}</app-radio>
          }
        </app-radio-group>
      </div>

      <button class="ml-auto!" nz-button (click)="download()">
        <span nz-icon nzType="download" nzTheme="outline"></span>
        下载
      </button>
    </div>
  </div>

  <nz-table
    #basicTable
    nzBordered
    nzOuterBordered
    nzSize="small"
    nzTableLayout="fixed"
    [nzData]="listOfDisplayData()"
    [nzPageSize]="10"
    [nzLoading]="loading()"
  >
    <thead>
      <tr>
        <th nzWidth="130px" rowspan="2" nzLeft class="bg-[#e8f4ff]! text-center!">
          @switch (area()) {
            @case ('city_bio_region') {
              区域
            }
            @case ('province_name') {
              省份
            }
            @case ('city_name') {
              城市
            }
          }

          <nz-filter-trigger [(nzVisible)]="visible" [nzActive]="searchValue()?.length > 0" [nzDropdownMenu]="menu">
            <SearchIcon />
          </nz-filter-trigger>

          <nz-dropdown-menu #menu="nzDropdownMenu">
            <div class="ant-table-filter-dropdown">
              <div class="search-box">
                <input type="text" nz-input autofocus placeholder="请输入" [(ngModel)]="searchValue" />
                <button nz-button nzSize="small" nzType="primary" (click)="search()" class="search-button">搜索</button>
                <button nz-button nzSize="small" (click)="reset()">重置</button>
              </div>
            </div>
          </nz-dropdown-menu>
        </th>

        @for (item of listOfColumnsLevel_1(); track $index) {
          <th
            class="bg-[#e8f4ff]! text-center!"
            [colspan]="item.colspan"
            nz-popover
            nzPopoverPlacement="top"
            [nzPopoverMouseEnterDelay]="0.5"
            [nzPopoverTitle]="item.name"
            [nzPopoverContent]="metricsContent_Template"
          >
            <ng-template #metricsContent_Template>
              <div class="leading-normal" [innerHTML]="item?.bizExpression"></div>
            </ng-template>
            {{ item.name }}
          </th>
        }
      </tr>

      <tr>
        @for (item of listOfColumnsLevel_2(); track $index) {
          @if (item.visible) {
            <th
              class="bg-[#e8f4ff]! text-right!"
              [nzSortOrder]="item.sortOrder"
              [nzSortDirections]="item.sortDirections"
              [nzSortFn]="item.sortFn"
            >
              {{ item.name }}
            </th>
          }
        }
      </tr>
    </thead>
    <tbody>
      @if (firstRowData()) {
        <tr>
          <td nzLeft align="center">
            @switch (area()) {
              @case ('city_bio_region') {
                <a (click)="drill(firstRowData().city_bio_region)">全国</a>
              }
              @case ('province_name') {
                <a (click)="drill(firstRowData().province_name)">全国</a>
              }
              @case ('city_name') {
                <a (click)="drill(firstRowData().city_name)">全国</a>
              }
            }
          </td>

          <ng-template *ngTemplateOutlet="columnsTemplate; context: { $implicit: firstRowData() }"></ng-template>
        </tr>
      }

      @for (data of basicTable.data; track $index) {
        <tr>
          <td nzLeft align="center" class="bg-[#fafafa]!">
            @switch (area()) {
              @case ('city_bio_region') {
                <a (click)="drill(data.city_bio_region)">{{ data.city_bio_region }}</a>
              }
              @case ('province_name') {
                <a (click)="drill(data.province_name)">{{ data.province_name }}</a>
              }
              @case ('city_name') {
                <a (click)="drill(data.city_name)">{{ data.city_name }}</a>
              }
            }
          </td>

          <ng-template *ngTemplateOutlet="columnsTemplate; context: { $implicit: data }"></ng-template>
        </tr>
      }
    </tbody>
  </nz-table>
</div>

<ng-template #columnsTemplate let-data>
  @for (item of config().metrics; track $index) {
    @let key = item.extendName;
    <td align="right">
      <value-formatter [value]="data[key]" />
    </td>
    @if (showDiff()) {
      <td>
        <div class="flex items-center justify-end">
          <value-formatter useColor [value]="`${data[key+'_DIFF']}`" />
          <value-formatter useColor showBracket useMultiplier="false" [value]="`${data[key+'_DIFF_RATIO']}`" suffix="%" />
        </div>
      </td>
    }
  }
</ng-template>
