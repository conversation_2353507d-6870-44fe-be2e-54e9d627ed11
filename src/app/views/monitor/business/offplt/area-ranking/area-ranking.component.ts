import { FormsModule } from '@angular/forms'
import { DatePipe, NgTemplateOutlet } from '@angular/common'
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  computed,
  DestroyRef,
  effect,
  inject,
  input,
  signal,
} from '@angular/core'
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop'
import { combineLatest, debounceTime, finalize, startWith } from 'rxjs'
import { NzButtonModule } from 'ng-zorro-antd/button'
import { NzIconModule } from 'ng-zorro-antd/icon'
import { NzPopoverModule } from 'ng-zorro-antd/popover'
import { NzDropDownModule } from 'ng-zorro-antd/dropdown'
import { NzInputModule } from 'ng-zorro-antd/input'
import { NzTableModule } from 'ng-zorro-antd/table'

import { SwitchMap } from '@common/decorator'
import { QueryEngineApiService } from '@api/query-engine'
import { getMonthFirstAndLastDay, Time } from '@common/class'
import { QueryEngineFormService } from '@common/service/query-engine'
import { isUndefinedOrNull } from '@common/function'
import { DimensionValueMenuVo, MetricsMenuVo } from '@api/caerus/model'
import { RadioModule } from '@shared/modules/headless'
import { DateCompareComponent } from '@shared/components/date-compare'
import { IconMapArrowRightComponent, IconSearchComponent } from '@shared/modules/icons'
import { ValueFormatter } from '@shared/components/value-formatter'
import { SearchPipe } from '@shared/pipes/search'
import { BusinessService } from '../../business.service'

@Component({
  selector: 'app-area-ranking',
  templateUrl: './area-ranking.component.html',
  styleUrl: './area-ranking.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    FormsModule,
    NgTemplateOutlet,
    NzInputModule,
    NzButtonModule,
    NzIconModule,
    NzPopoverModule,
    NzTableModule,
    NzDropDownModule,
    RadioModule,
    IconMapArrowRightComponent,
    IconSearchComponent,
    DateCompareComponent,
    ValueFormatter,
  ],
  providers: [DatePipe, QueryEngineFormService, SearchPipe],
})
export class AreaRankingComponent implements AfterViewInit {
  readonly datePipe = inject(DatePipe)
  readonly destroyRef = inject(DestroyRef)
  readonly apiService = inject(QueryEngineApiService)
  readonly formService = inject(QueryEngineFormService)
  readonly service = inject(BusinessService)
  readonly searchPipe = inject(SearchPipe)

  config = input<any>(null)
  mediaTypeOptions = signal<DimensionValueMenuVo[]>([])
  promtChannelSourceTypeOptions = signal<DimensionValueMenuVo[]>([])
  thilevChannelOptions = signal<DimensionValueMenuVo[]>([])

  area = signal<string>('city_bio_region')
  media_type = signal<any>(null)
  promt_channel_source_type = signal<any>(null)
  c_thilev_channel = signal<any>(null)

  area$ = toObservable(this.area)
  media_type$ = toObservable(this.media_type)
  promt_channel_source_type$ = toObservable(this.promt_channel_source_type)
  c_thilev_channel$ = toObservable(this.c_thilev_channel)
  config$ = toObservable(this.config)

  loading = signal(true)
  listOfData = signal([])
  listOfDisplayData = signal([])
  merticsInfoMap = new Map<string, MetricsMenuVo>([])
  firstRowData = signal<any>({})
  searchValue = signal<string>(null)
  visible = signal<boolean>(null)

  showDiff = computed(() => {
    return !!this.formService.form$()?.compareDt
  })

  // 大区 city_bio_region > 省份 province_name >城市 city_name
  regionDisabled = computed(() => {
    const { extendName } = this.service.area() || {}

    if (extendName === 'city_bio_region' || isUndefinedOrNull(extendName)) {
      return false
    }

    return true
  })

  provinceDisabled = computed(() => {
    const { extendName } = this.service.area() || {}

    if (extendName === 'city_bio_region' || extendName === 'province_name' || isUndefinedOrNull(extendName)) {
      return false
    }

    return true
  })

  listOfColumnsLevel_1 = computed(() => {
    const colspan = this.showDiff() ? 2 : 1

    if (this.config()) {
      const keys = this.config().metrics.map(item => item.extendName)

      return keys.map(key => {
        return {
          colspan,
          name: this.merticsInfoMap.get(key)?.aliasName,
          bizExpression: this.merticsInfoMap.get(key)?.bizExpression,
        }
      })
    }

    return []
  })

  listOfColumnsLevel_2 = computed(() => {
    if (this.config()) {
      return this.config()
        .metrics.map((item, index) => {
          const key = item.extendName
          return [
            {
              name: '总计数值',
              visible: true,
              sortOrder: index === 0 ? 'descend' : null,
              sortFn: (a: any, b: any) => a[key] - b[key],
              sortDirections: ['ascend', 'descend', null],
            },
            {
              name: '较对比期',
              visible: this.showDiff(),
              sortOrder: null,
              sortFn: (a: any, b: any) => a[key + '_DIFF'] - b[key + '_DIFF'],
              sortDirections: ['ascend', 'descend', null],
            },
          ]
        })
        .flat(1)
    }
  })

  constructor() {
    effect(() => {
      const { extendName } = this.service.area() || {}

      if (extendName) {
        if (extendName === 'is_top20_city') {
          this.area.set('city_name')
        } else {
          this.area.set(extendName)
        }
      }
    })

    effect(() => {
      if (this.config()) {
        const { filter, metrics } = this.config()
        const { media_type, promt_channel_source_type, c_thilev_channel } = filter

        this.mediaTypeOptions.set(media_type.values)
        this.promtChannelSourceTypeOptions.set(promt_channel_source_type.values)
        this.thilevChannelOptions.set(c_thilev_channel.values)

        metrics.forEach(item => {
          this.merticsInfoMap.set(item.extendName, item)
        })
      }
    })
  }

  ngAfterViewInit(): void {
    this._subscribeToFormChange()
    this._subscribeToDateTypeChange()
    this._subscribeToDateChange()
  }

  private _subscribeToDateChange() {
    this.service.month$.pipe(debounceTime(100), takeUntilDestroyed(this.destroyRef)).subscribe(() => {
      this.formService.dtType.patchValue('ym')
    })
  }

  private _subscribeToDateTypeChange() {
    combineLatest([this.service.deadline$, this.formService.dtType.valueChanges])
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(([deadline, type]) => {
        if (deadline) {
          if (type === 'ym') {
            this._updateDateRangeForYm()
          } else if (type === 'dt') {
            this._updateDateRangeForDt()
          } else if (type === 'yw') {
            this._updateDateRangeForYw()
          }
        }
      })
  }

  private _isAfterDeadline() {
    const dtValue = this.datePipe.transform(this.service.month(), 'yyyy-MM-dd')
    const [, endTime] = getMonthFirstAndLastDay(dtValue)
    const endDate = new Date(endTime.replace(/-/g, '/'))
    const deadDate = this.service.deadDate()

    return endDate > deadDate
  }

  private _updateDateRangeForDt() {
    const deadline = this.service.deadline()
    const dtValue = this.datePipe.transform(this.service.month(), 'yyyy-MM-dd')

    if (this._isAfterDeadline()) {
      const [deadlineStartTime] = getMonthFirstAndLastDay(deadline)
      this.formService.dt.patchValue({ startTime: deadlineStartTime, endTime: deadline })
    } else {
      const [startTime, endTime] = getMonthFirstAndLastDay(dtValue)
      this.formService.dt.patchValue({ startTime, endTime })
    }
  }

  private _updateDateRangeForYw() {
    const deadline = this.service.deadline()
    const dtValue = this.datePipe.transform(this.service.month(), 'yyyy-MM-dd')

    if (this._isAfterDeadline()) {
      const [deadlineStartWeek] = getMonthFirstAndLastDay(deadline).map(item =>
        this.datePipe.transform(item, 'YYYY-ww')
      )
      const deadlineEndWeek = this.datePipe.transform(deadline, 'YYYY-ww')

      this.formService.dt.patchValue({ startTime: deadlineStartWeek, endTime: deadlineEndWeek })
    } else {
      const [startTime, endTime] = getMonthFirstAndLastDay(dtValue).map(item =>
        this.datePipe.transform(item, 'YYYY-ww')
      )

      this.formService.dt.patchValue({ startTime, endTime })
    }
  }

  private _updateDateRangeForYm() {
    const deadline = this.service.deadline()
    const dtValue = this.datePipe.transform(this.service.month(), 'yyyy-MM')

    if (this._isAfterDeadline()) {
      const [deadlineStartTime] = getMonthFirstAndLastDay(deadline).map(item =>
        this.datePipe.transform(item, 'yyyy-MM')
      )

      this.formService.dt.patchValue({ startTime: deadlineStartTime, endTime: deadlineStartTime })
    } else {
      this.formService.dt.patchValue({ startTime: dtValue, endTime: dtValue })
    }
  }

  private _subscribeToFormChange() {
    combineLatest([
      this.config$,
      this.area$,
      this.service.area$,
      this.media_type$,
      this.promt_channel_source_type$,
      this.c_thilev_channel$,
      this.formService.form.valueChanges.pipe(startWith(this.formService.form.value)),
    ])
      .pipe(startWith(null), debounceTime(100), takeUntilDestroyed(this.destroyRef))
      .subscribe(() => {
        if (this.config()) {
          this.query()
        }
      })
  }

  private _mediaTypeFilter() {
    if (this.media_type()) {
      const { key, value } = this.media_type()

      return [
        {
          conditionType: 2,
          condition: '=',
          id: null,
          extendName: 'media_type',
          value: [{ key, value }],
          valueType: null,
        },
      ]
    }
    return []
  }

  private _promtChannelSourceTypeFilter() {
    if (this.promt_channel_source_type()) {
      const { key, value } = this.promt_channel_source_type()

      return [
        {
          conditionType: 2,
          condition: '=',
          id: null,
          extendName: 'promt_channel_source_type',
          value: [{ key, value }],
          valueType: null,
        },
      ]
    }
    return []
  }

  private _thilevChannelFilter() {
    if (this.c_thilev_channel()) {
      const { key, value, extendName } = this.c_thilev_channel()

      return [
        {
          conditionType: 2,
          condition: '=',
          id: null,
          extendName,
          value: [{ key, value }],
          valueType: null,
        },
      ]
    }
    return []
  }

  private _globalAreaFilter() {
    if (this.service.area()) {
      const { key, value, extendName } = this.service.area()

      return [
        {
          conditionType: 2,
          condition: '=',
          id: null,
          extendName,
          value: [{ key, value }],
          valueType: null,
        },
      ]
    }
    return []
  }

  private _body() {
    const body = this.formService.value()

    body.extraDimensionGroups = [[]]
    body.dimensions = [{ extendName: this.area() }]
    body.metrics = this.config().metrics

    body.filter.items = [
      this._mediaTypeFilter(),
      this._promtChannelSourceTypeFilter(),
      this._thilevChannelFilter(),
      this._globalAreaFilter(),
    ].flat(1)

    body.scene = 4
    body.dataFillConfig = {
      open: 1,
      fillRangeType: 1,
      fillValue: 1,
    }

    return body
  }

  @SwitchMap()
  query() {
    this.loading.set(true)

    return this.apiService
      .search(this._body(), 'area-ranking-table')
      .pipe(finalize(() => this.loading.set(false)))
      .subscribe(res => {
        this.listOfData.set([])

        if (res.status !== '00000') {
          return console.error(res.error || res.message)
        }

        if (res.data?.data) {
          const first = res.data.data.find(
            item => (item['city_bio_region'] || item['province_name'] || item['city_name']) === '整体'
          )
          const data = res.data.data.filter(item => item !== first)

          this.listOfData.set(data || [])
          this.listOfDisplayData.set(data || [])
          this.firstRowData.set(first)
        }
      })
  }

  reset(): void {
    this.searchValue.set(null)
    this.search()
  }

  search(): void {
    const key = this.area()
    const list = this.searchPipe.transform(this.listOfData(), this.searchValue(), key)

    this.visible.set(false)
    this.listOfDisplayData.set(list)
  }

  drill(value: string) {
    this.service.customArea.set(value)
  }

  download() {
    this.apiService.exportToExcel(this._body()).subscribe()
  }
}
