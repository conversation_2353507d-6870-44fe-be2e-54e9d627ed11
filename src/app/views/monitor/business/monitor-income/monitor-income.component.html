<div class="flex flex-col gap-5 py-3">
  <!-- <header
    class="relative flex items-center gap-x-1 px-5 h-12 border-l-4 border-emerald-500 bg-linear-to-r from-neutral-300/40 via-white via-20%"
  >
    <span class="text-xl mr-1">收入&成本</span>
    <app-radio-group
      [(ngModel)]="pageType"
      class="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-60 flex items-start gap-x-0.5 p-0.5 bg-[#ebebec] rounded-lg"
    >
      <app-radio
        class="dida-radio"
        activeClass="dida-radio-active"
        value="income"
      >
        收入监控
      </app-radio>
      <app-radio
        class="dida-radio"
        activeClass="dida-radio-active"
        value="onplt"
      >
        费用核销
      </app-radio>
    </app-radio-group>
  </header> -->

  <!-- @if(pageType() ==='income'){ -->
  <div class="relative">
    <h2 class="font-bold text-lg absolute left-0 top-0">收入监控</h2>
    <div class="flex justify-center">
      <app-radio-group [(ngModel)]="type" (ngModelChange)="changeType()" class="flex items-start px-3">
        <app-radio class="line-radio text-xs whitespace-nowrap" activeClass="active" value="finance">财务口径</app-radio>
        <app-radio class="line-radio text-xs whitespace-nowrap" activeClass="active" value="business">业务口径</app-radio>
      </app-radio-group>
    </div>
  </div>

  <div class="flex items-center flex-wrap gap-x-5 gap-y-2 px-5">
    <div class="flex items-center">
      <label class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap">订单类型：</label>
      <app-radio-group class="relative flex gap-1" [(ngModel)]="c_ord_type" (ngModelChange)="search()">
        <app-radio class="tag-radio-new" activeClass="active" [value]="null">全部</app-radio>
        @for (item of filters()?.c_ord_type?.values; track item) {
          <app-radio class="tag-radio-new" activeClass="active" [value]="item.key">
            {{ item.value }}
          </app-radio>
        }
        <app-radio-thumb class="rounded-xs bg-primary" />
      </app-radio-group>
    </div>

    <div class="flex items-center">
      <label class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap">一级业务渠道：</label>
      <app-radio-group class="relative flex gap-1" [(ngModel)]="c_firlev_channel" (ngModelChange)="changeFirlev()">
        <app-radio class="tag-radio-new" activeClass="active" [value]="null">全部</app-radio>
        @for (item of filters()?.c_firlev_channel?.values; track item) {
          <app-radio class="tag-radio-new" activeClass="active" [value]="item.key">
            {{ item.value }}
          </app-radio>
        }
        <app-radio-thumb class="rounded-xs bg-primary" />
      </app-radio-group>
    </div>

    <div class="flex items-center">
      <label class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap">二级业务渠道：</label>
      <app-radio-group class="relative flex gap-1" [(ngModel)]="c_seclev_channel" (ngModelChange)="search()">
        <app-radio class="tag-radio-new" activeClass="active" [value]="null">全部</app-radio>
        @for (item of c_seclev_channel_list(); track item) {
          <app-radio class="tag-radio-new" activeClass="active" [value]="item.key">
            {{ item.value }}
          </app-radio>
          <app-radio-thumb class="rounded-xs bg-primary" />
        }
      </app-radio-group>
    </div>

    <div class="flex items-center">
      <label class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap">是否站点拼车：</label>
      <app-radio-group class="relative flex gap-1" [(ngModel)]="is_site_pkg_book" (ngModelChange)="search()">
        <app-radio class="tag-radio-new" activeClass="active" [value]="null">全部</app-radio>
        @for (item of filters()?.is_site_pkg_book?.values; track item) {
          <app-radio class="tag-radio-new" activeClass="active" [value]="item.key">
            {{ item.value }}
          </app-radio>
        }
        <app-radio-thumb class="rounded-xs bg-primary" />
      </app-radio-group>
    </div>
  </div>

  <div class="flex items-center gap-x-5 w-full">
    @if (this.loading()) {
      <div class="w-full mx-auto">
        <app-line-spin />
      </div>
    } @else {
      <app-radio-group class="flex items-center flex-wrap w-full justify-between" [(ngModel)]="index" (ngModelChange)="clickCard($event)">
        @for (item of card_list(); track item) {
          <ng-template #metricsTitleTemplate>
            {{ item?.showName }}
            <span class="text-xs opacity-30 px-1">({{ item?.aliasName }})</span>
          </ng-template>
          <app-radio nz-popover [nzPopoverTitle]="metricsTitleTemplate" [nzPopoverContent]="item?.bizExpression" class="radio w-[24%]" activeClass="active" [value]="$index">
            <div class="py-4 flex flex-col gap-4 px-[11%]">
              <h2 class="flex items-center justify-center gap-x-1 font-bold text-base text-inherit">
                {{ item.showName }}
              </h2>
              <div class="flex flex-col gap-1">
                <div class="text-center">
                  昨日:
                  <span class="text-lg font-bold">
                    @let yeaterday = this.card_list_data()[item.yeaterday.extendName];
                    @if (yeaterday) {
                      {{ yeaterday | number }}
                    } @else {
                      -
                    }
                  </span>
                </div>
                <div class="flex items-center">
                  <div class="w-[60%]">
                    日环比:
                    @let day = renderNum(item.yeaterday.extendName, 'day');
                    @if (day) {
                      <span [class]="day.color">{{ day.text }}</span>
                    } @else {
                      -
                    }
                  </div>
                  <div>
                    周同比:
                    @let week = renderNum(item.yeaterday.extendName, 'week');
                    @if (week) {
                      <span [class]="week.color">{{ week.text }}</span>
                    } @else {
                      -
                    }
                  </div>
                </div>
              </div>
              <div class="flex flex-col gap-1 pt-1">
                <div class="text-center">
                  本月累计:
                  <span class="text-lg font-bold">
                    @let accumulate = this.card_list_data()[item.accumulate.extendName];
                    @if (accumulate) {
                      {{ accumulate | number }}
                    } @else {
                      -
                    }
                  </span>
                </div>
                <div class="flex items-center">
                  <div class="w-[60%]">
                    月环比:
                    @let month = renderNum(item.accumulate.extendName, 'month');
                    @if (month) {
                      <span [class]="month.color">{{ month.text }}</span>
                    } @else {
                      -
                    }
                  </div>
                  <div>
                    年同比:
                    @let year = renderNum(item.accumulate.extendName, 'year');
                    @if (year) {
                      <span [class]="year.color">{{ year.text }}</span>
                    } @else {
                      -
                    }
                  </div>
                </div>
              </div>
            </div>
            <SuccessFillIcon *radioChecked class="absolute right-3 top-3 text-blue-600 text-xl" />
          </app-radio>
        }
      </app-radio-group>
    }
  </div>

  <div class="flex flex-col gap-3">
    <div class="flex items-center flex-wrap gap-x-5 gap-y-2 px-5">
      <div class="flex items-center">
        <label class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap">选择数据：</label>
        <app-radio-group class="relative flex gap-1" [(ngModel)]="chartType" (ngModelChange)="getChartData()">
          <app-radio class="tag-radio-new" activeClass="active" value="yesterday">昨日</app-radio>
          <app-radio class="tag-radio-new" activeClass="active" value="accumulate">本月累计</app-radio>
          <app-radio-thumb class="rounded-xs bg-primary" />
        </app-radio-group>
      </div>
      <div class="flex items-center">
        <label class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap">选择指标：</label>
        @for (item of card_list(); track item) {
          <label nz-checkbox class="ml-0! text-xs!" [(ngModel)]="item.checked" (ngModelChange)="getChartData()">
            {{ item.showName }}
          </label>
        }
      </div>
      <div class="text-red-500 text-[12px] pt-1">说明: 这里按单日展示时，星期会自动对齐</div>
    </div>
    <div class="relative h-125">
      @if (chartLoading()) {
        <app-line-spin />
      } @else {
        <div class="absolute inset-y-0 inset-x-4">
          @if (option()) {
            <app-graph [options]="option()" />
          }
        </div>
      }
    </div>
  </div>
  <!-- } -->
  <!-- @else{
  <app-onplt-extra />
  <app-offplt-promt />
  } -->
</div>
