import { FormsModule } from '@angular/forms';
import {
  ChangeDetectionStrategy,
  Component,
  signal,
  inject,
  computed,
  AfterViewInit,
} from '@angular/core';
import { RadioModule } from '@shared/modules/headless';
import { CaerusApiService } from '@api/caerus';
import { DecimalPipe } from '@angular/common';
import { GraphComponent } from '@shared/components/graph';
import { LineSpinComponent } from '@shared/components/line-spin';
import { LegendControlService, LegendItemClickHandler } from '@common/service';
import { BaseHighCharts } from '@common/chart/highcharts';
import { QueryOutputVo } from '@api/query-engine/model';
import { IncomeTrend } from '../lib/IncomeTrend';
import { QueryEngineApiService } from '@api/query-engine';
import { finalize } from 'rxjs';
import { BusinessService } from '../business.service';
import { NzSkeletonModule } from 'ng-zorro-antd/skeleton';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { QueryEngineFormService } from '@common/service/query-engine';
import { getMonthFirstAndLastDay } from '@common/class';
import { SwitchMap } from '@common/decorator';
import { NzMessageService } from 'ng-zorro-antd/message';
import { IconSuccessFillComponent } from '@shared/modules/icons';
import { NzPopoverModule } from 'ng-zorro-antd/popover';

@Component({
  selector: 'app-monitor-income',
  templateUrl: './monitor-income.component.html',
  styleUrl: './monitor-income.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    FormsModule,
    RadioModule,
    DecimalPipe,
    GraphComponent,
    NzSkeletonModule,
    NzCheckboxModule,
    LineSpinComponent,
    IconSuccessFillComponent,
    NzPopoverModule,
  ],
  providers: [LegendControlService, QueryEngineFormService],
})
export class MonitorIncomeComponent implements AfterViewInit {
  constructor(private message: NzMessageService) {}
  readonly apiService = inject(CaerusApiService);
  readonly legendControlService = inject(LegendControlService);
  readonly queryService = inject(QueryEngineApiService);
  readonly formService = inject(QueryEngineFormService);
  readonly service = inject(BusinessService);

  index = signal(0);
  c_ord_type = signal(null);
  c_firlev_channel = signal(null);
  is_site_pkg_book = signal(null);
  c_seclev_channel = signal(null);
  filters = signal<any>({});
  data = signal<any>(null);
  option = signal<BaseHighCharts>(null);
  type = signal('finance');
  loading = signal(false);
  card_list_data = signal({});
  chartType = signal('accumulate');
  chartLoading = signal(false);
  pageType = signal('income');

  radioMap = {
    day: ':dt_DIFF_RATIO',
    week: ':yw_DIFF_RATIO',
    month: ':ym_DIFF_RATIO',
    year: ':year_DIFF_RATIO',
  };

  card_list = computed(() => {
    if (this.data()) {
      let _list = [];
      if (this.type() === 'finance') {
        _list = this.data().financeCardList;
      } else {
        _list = this.data().businessCardList;
      }
      _list.forEach((_l, i) => {
        _l.checked = i === 0;
      });
      return _list;
    }
    return [];
  });

  c_seclev_channel_list = computed(() => {
    if (this.c_firlev_channel() === null) {
      return this.filters()?.c_seclev_channel?.values;
    }
    return this.filters()?.c_seclev_channel?.values.filter((v) => {
      return v.relValue === this.c_firlev_channel();
    });
  });

  renderNum(extandName, time) {
    const data = this.card_list_data()[`${extandName}${this.radioMap[time]}`];
    if (!data) {
      return null;
    }
    if (Number(data) < 0) {
      return {
        color: 'text-green-500',
        text: `${Number(data)}%`,
      };
    }
    return {
      color: 'text-red-500',
      text: `+${Number(data)}%`,
    };
  }

  renderFilter() {
    const obj = {
      c_ord_type: this.c_ord_type(),
      c_firlev_channel: this.c_firlev_channel(),
      is_site_pkg_book: this.is_site_pkg_book(),
      c_seclev_channel: this.c_seclev_channel(),
    };
    const items = Object.keys(obj).map((o) => {
      if (!obj[o]) {
        return null;
      }
      const value = this.filters()[o].values.filter((v) => {
        return v.key === obj[o];
      })[0];
      return {
        conditionType: 2,
        condition: '=',
        id: null,
        extendName: value.extendName,
        value: [
          {
            key: value.key,
            value: value.value,
          },
        ],
        valueType: null,
      };
    });
    return items.filter((i) => i);
  }

  private _setChartData(data: QueryOutputVo) {
    try {
      const chart = new IncomeTrend(data);
      const legendItemClick = LegendItemClickHandler(this.legendControlService);
      chart.plotOptions.series.events = { legendItemClick };
      this.option.set(chart?.getOption() || null);
    } catch (e) {
      console.error(e);
    }
  }

  @SwitchMap()
  getChartData(): void {
    const deadline = this.service.deadline();
    if (!deadline) {
      return;
    }
    if (this.card_list().length === 0) {
      return;
    }
    const [startTime, endTime] = getMonthFirstAndLastDay(deadline);
    const body = this.formService.value();
    body.dt = { startTime, endTime };
    if (this.chartType() === 'yesterday') {
      body.compareDt = {
        startTime: 'now-1M-weekAlign',
        endTime: 'now-1M-weekAlign',
      };
    } else {
      body.compareDt = {
        startTime: 'now-1M-normal',
        endTime: 'now-1M-normal',
      };
    }
    let key = 'yeaterday';
    if (this.chartType() !== 'yesterday') {
      key = 'accumulate';
    }
    const metrics = this.card_list()
      .filter((c) => c.checked)
      .map((c) => {
        return c[key];
      });
    body.metrics = metrics;
    body.dimensions = [
      {
        extendName: 'dt',
      },
    ];
    body.filter = {
      items: this.renderFilter(),
      type: null,
    };
    body.dataFillConfig = {
      open: 1,
      fillRangeType: 1,
      fillValue: 1,
    };
    this.chartLoading.set(true);
    this.legendControlService.reset();
    this.queryService
      .search(body, 'monitor-income-chart')
      .pipe(finalize(() => this.chartLoading.set(false)))
      .subscribe((res) => {
        console.log('chart====', res.data);
        if (res.data) {
          this._setChartData(res.data);
        } else {
          this.message.create('error', res.message);
          this.option.set(null);
        }
      });
  }

  ngAfterViewInit(): void {
    this.apiService.fetchConfig('business_monitoring_inc').subscribe((res) => {
      if (res.data) {
        console.log('card', res.data);
        this.filters.set(res.data.filter);
        this.data.set(res.data);
        // console.log('res', res.data);
        this.subscribeDeadline();
      }
    });
  }

  private subscribeDeadline() {
    this.service.deadline$.subscribe((value) => {
      console.log('subscribeDeadline', value);
      if (value) {
        this.getCardData();
        this.getChartData();
      }
    });
  }

  @SwitchMap()
  private getCardData() {
    const deadline = this.service.deadline();
    if (!deadline) {
      return;
    }
    if (this.card_list().length === 0) {
      return;
    }
    console.log('renderFilter', this.renderFilter());
    const body = this.formService.value();
    body.dt = {
      startTime: deadline,
      endTime: deadline,
    };
    body.dimensions = [
      {
        id: null,
        extendName: 'dt',
        predefineCompareType: ['yw', 'ym', 'dt', 'year'],
      },
    ];
    body.metrics = this.card_list()
      .map((l) => {
        return l.accumulate;
      })
      .concat(
        this.card_list().map((l) => {
          return l.yeaterday;
        })
      );
    body.dataFillConfig = {
      open: 1,
      fillRangeType: 2,
      fillValue: 1,
    };
    body.filter = {
      items: this.renderFilter(),
      type: null,
    };

    this.loading.set(true);
    this.queryService
      .search(body, 'monitor-income')
      .pipe(finalize(() => this.loading.set(false)))
      .subscribe((res) => {
        console.log('income====', res.data);
        if (res.data && res.data.data.length !== 0) {
          this.card_list_data.set(res.data.data[0]);
        } else {
          this.card_list_data.set({});
        }
      });
  }

  changeType() {
    this.chartType.set('accumulate');
    this.index.set(0);
    this.search();
  }

  changeFirlev() {
    this.c_seclev_channel.set(null);
    this.search();
  }

  clickCard(index) {
    if (this.card_list().length) {
      this.card_list().forEach((c, i) => {
        if (i === index) {
          c.checked = true;
        } else {
          c.checked = false;
        }
      });
      this.getChartData();
    }
  }

  search() {
    this.getCardData();
    this.getChartData();
  }
}
