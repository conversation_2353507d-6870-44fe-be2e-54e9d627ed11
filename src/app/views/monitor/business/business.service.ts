import { computed, Injectable, linkedSignal, signal } from '@angular/core';
import { toObservable } from '@angular/core/rxjs-interop';
import { addDays } from 'date-fns';
import { BusinessConfig } from './lib/BusinessConfig';
import { DimensionValueMenuVo } from '@api/caerus/model';

@Injectable()
export class BusinessService {
  readonly yesterday = signal(addDays(new Date(), -1));
  readonly today = signal(new Date());

  config = signal<BusinessConfig>(null);
  config$ = toObservable(this.config);

  month = linkedSignal({
    source: () => this.today(),
    computation: (source) => {
      if (source.getDate() === 1) {
        return this.yesterday()
      }
      return this.today();
    }
  });
  month$ = toObservable(this.month);

  /** 截至日期 */
  deadline = signal<string>(null);
  deadline$ = toObservable(this.deadline);
  deadDate = computed(() => this.deadline() ? new Date(this.deadline().replace(/-/g, '/')) : null);

  area = signal<DimensionValueMenuVo>(null);
  area$ = toObservable(this.area);
  customArea = signal<string>(null);

  type = signal<string>('passenger');
  type$ = toObservable(this.type);
}
