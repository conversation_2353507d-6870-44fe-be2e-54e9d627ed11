<!-- <div class="sticky top-0 z-40 py-2 mb-1 bg-white shadow-lg">
  <div class="flex items-center justify-center px-5">
    <div
      class="max-w-(--breakpoint-2xl) flex-1 min-w-0 flex items-center gap-x-2 text-xs"
    >
      <nz-date-picker
        [(ngModel)]="service.month"
        nzMode="month"
        [nzFormat]="'yyyy/MM'"
        [nzDisabledDate]="disabledMonth"
      />
      <span class="ml-4 whitespace-nowrap"
        >日期截至：{{ service.deadline() }}</span
      >
    </div>
  </div>
</div> -->
<app-date type="overview" />
<div class="flex justify-center bg-white">
  <div class="max-w-(--breakpoint-2xl) flex-1 min-w-0">
    <div class="p-5">
      <div
        id="anchor-resume"
        class="bg-neutral-100 rounded-sm py-3 px-5 text-neutral-400 text-xs leading-5"
      >
        <h4 class="flex items-center gap-x-1.5">
          <CaliberIcon /> <span class="text-sm font-bold">盯盘摘要：</span>
          <p class="font-normal">
            说明：除特别标注外，摘要信息中的指标均为业务口径；摘要中，
            <span class="text-blue-500">蓝色表示指标实际数值，</span>
            <span class="text-red-500">红色表示进度健康，</span>
            <span class="text-green-500">绿色表示进度预警。</span>
          </p>
        </h4>

        <div class="px-6">
          @if (loading()) {
          <div class="flex items-center justify-center h-[168px]">
            <app-line-spin />
          </div>
          } @else { @if (resume()) {
          <div
            class="text-neutral-500 text-sm/6"
            [innerHTML]="resume() | safe : 'html'"
          ></div>
          } @else if (errorMessage()) {
          <div class="flex items-center justify-center h-[168px]">
            <span class="text-slate-600 text-xs">{{ errorMessage() }}</span>
          </div>
          } }
        </div>
      </div>
    </div>
    <aside class="px-5">
      <span class="text-xs">
        口径说明：
        <span class="font-bold text-red-400">业务口径</span>是指
        <span class="text-red-400">按下单时间统计</span
        >的指标数值，按业务口径统计的
        <span class="text-red-400">日完单量</span>，
        <span class="text-red-400">T-1数据</span>比最终回刷数据
        <span class="font-bold text-red-400">少22%</span>左右；
        <span class="font-bold">MTD是指当月截至昨日的累计值；</span
        >选择历史月份时，昨日指本月最后1天。
      </span>
    </aside>
    <app-monitor-target
      id="anchor-target"
      (onTypeChange)="typeChange($event)"
    />
  </div>
  <div class="tools-bar pointer-events-none">
    <div class="btn pointer-events-auto" (click)="showModal()">
      <OkrIcon iconBtn class="xxl text-3xl text-[#FFA400]" />
      <span class="label">目标管理</span>
    </div>

    <div class="my-auto flex flex-col justify-center pointer-events-auto">
      <nz-anchor
        [nzBounds]="200"
        [nzTargetOffset]="0"
        [nzContainer]="parent.layoutRef().nativeElement"
      >
        @if(currentType() === 'market'){
        <nz-link nzTitle="摘要" nzHref="#anchor-resume"></nz-link>
        <nz-link nzTitle="目标" nzHref="#anchor-target"></nz-link>
        <nz-link nzTitle="收入" nzHref="#anchor-income"></nz-link>
        <nz-link nzTitle="营销" nzHref="#anchor-driver"></nz-link>
        <nz-link nzTitle="费用" nzHref="#anchor-offplt"></nz-link>
        <nz-link nzTitle="构成" nzHref="#anchor-trend"></nz-link>
        }@else{
        <nz-link nzTitle="摘要" nzHref="#anchor-resume"></nz-link>
        <nz-link nzTitle="目标" nzHref="#anchor-target"></nz-link>
        <nz-link nzTitle="构成" nzHref="#anchor-trend"></nz-link>
        }
      </nz-anchor>
    </div>

    <app-back-top />
  </div>
  <nz-modal
    [(nzVisible)]="isVisible"
    nzTitle="目标管理"
    (nzOnCancel)="handleCancel()"
    [nzFooter]="null"
    [nzWidth]="'80%'"
  >
    <ng-container *nzModalContent>
      <app-monitor-target-manage />
    </ng-container>
  </nz-modal>
</div>
