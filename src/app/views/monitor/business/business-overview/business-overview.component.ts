import { DatePipe } from '@angular/common'
import { FormsModule } from '@angular/forms'
import { AfterViewInit, ChangeDetectionStrategy, Component, DestroyRef, inject, signal } from '@angular/core'
import { differenceInCalendarMonths, addMonths } from 'date-fns'
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker'
import { takeUntilDestroyed } from '@angular/core/rxjs-interop'
import { NzModalModule } from 'ng-zorro-antd/modal'
import { NzAnchorModule } from 'ng-zorro-antd/anchor'
import { filter, finalize } from 'rxjs'

import { isNotNull } from '@common/function'
import { CaerusApiService } from '@api/caerus'
import { QueryEngineApiService } from '@api/query-engine'
import { PAGE_NAME, PageEnterLeaveDirective } from '@common/directive'
import { IconCaliberComponent, IconOkrComponent } from '@shared/modules/icons'
import { LineSpinComponent } from '@shared/components/line-spin'
import { BackTopComponent } from '@shared/components/back-top'
import { SafePipe } from '@shared/pipes/safe'
import { MonitorTargetComponent } from '../monitor-target/monitor-target.component'
import { MonitorTargetManageComponent } from '../monitor-target-manage/monitor-target-manage.component'
import { BusinessService } from '../business.service'
import { BusinessComponent } from '../business.component'
import { DateComponent } from '../components'

@Component({
  selector: 'app-business-overview',
  templateUrl: './business-overview.component.html',
  styles: ``,
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: {
    class: 'relative block',
  },
  hostDirectives: [PageEnterLeaveDirective],
  imports: [
    FormsModule,
    NzAnchorModule,
    NzDatePickerModule,
    MonitorTargetComponent,
    MonitorTargetManageComponent,
    BackTopComponent,
    LineSpinComponent,
    IconCaliberComponent,
    IconOkrComponent,
    NzModalModule,
    SafePipe,
    DateComponent,
  ],
  providers: [{ provide: PAGE_NAME, useValue: 'business-monitoring' }, DatePipe],
})
export class BusinessOverviewComponent implements AfterViewInit {
  readonly datePipe = inject(DatePipe)
  readonly destroyRef = inject(DestroyRef)
  readonly service = inject(BusinessService)
  readonly queryEngineApiService = inject(QueryEngineApiService)
  readonly apiService = inject(CaerusApiService)
  readonly parent = inject(BusinessComponent)

  resume = signal<string>(null)
  errorMessage = signal<string>(null)
  isVisible = signal(false)
  loading = signal(false)
  currentType = signal('market')

  typeChange(t) {
    this.currentType.set(t)
  }

  disabledMonth = (current: Date): boolean => {
    // Can not select days before today and today
    const startDateForMonth = new Date().setDate(1)
    const dateRight = addMonths(startDateForMonth, 1)

    return differenceInCalendarMonths(current, dateRight) > -1
  }

  handleCancel() {
    this.isVisible.set(false)
  }

  showModal() {
    this.isVisible.set(true)
  }

  ngAfterViewInit(): void {
    // this.subscribeToMonthChange();
    this.subscribeDeadline()
  }

  private subscribeDeadline() {
    this.service.deadline$.subscribe(value => {
      if (value) {
        this.fetchBusinessMonitorResume(value)
      }
    })
  }

  private subscribeToMonthChange() {
    this.service.month$.pipe(filter(isNotNull), takeUntilDestroyed(this.destroyRef)).subscribe(value => {
      console.log('subscribeToMonthChange')
      const month = this.datePipe.transform(value, 'yyyy-MM')

      this.queryEngineApiService.fetchUpdateTime(month).subscribe(res => {
        if (res.status === '00000') {
          this.service.deadline.set(res.data.dt)
          this.fetchBusinessMonitorResume(res.data.dt)
        }
      })
    })
  }

  private fetchBusinessMonitorResume(dt: string) {
    this.loading.set(true)
    this.errorMessage.set(null)
    this.apiService
      .fetchBusinessMonitorResume(dt)
      .pipe(finalize(() => this.loading.set(false)))
      .subscribe(res => {
        if (res.status !== '00000') {
          console.error(res.error || res.message)
          this.errorMessage.set(res.error || res.message)
        }

        if (res.data) {
          this.resume.set(res.data)
        }
      })
  }
}
