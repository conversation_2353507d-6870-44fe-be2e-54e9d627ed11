export interface Metric {
  extendName: string;
  showName?: string;
  aliasName?: string;
  bizExpression?: string;
  value?: string;
  isRate?: boolean;
  order?: number;
}

export interface CardList {
  showName: string;
  aliasName: string;
  subName: string;
  prediction?: boolean;
  showDayBeforeYesterday?: boolean;
  bizExpression: string;
  yesterdayRate: Metric;
  yesterdayTarget: Metric;
  accumulateMetric: Metric;
  accumulateTarget: Metric;
  yesterdayMetric: Metric;
  accumulateRate: Metric;

  beforeYesterMetric: Metric;
  beforeYesterTarget: Metric;

  yesterday: Metric[];
  accumulate: Metric[];
  cardDisplay?: boolean;
}

interface TargetConfig {
  name: string;
  cardList: CardList[];
  monthProgress: Metric[];
}

interface TrendConfig {
  name: string;
  accumulate: Metric[];
  yeaterday: Metric[];
}

export interface ProgressItem {
  extendName: string;
  showName: string;
  order: number;
}

interface StructureItem {
  accumulate: Metric[];
  yesterday: Metric[];
  showName: string;
  aliasName?: string;
  bizExpression?: string;
}

interface BusinessTrendItem {
  app: StructureItem;
  own: StructureItem;
  wx: StructureItem;
  showName: string;
}

export interface BusinessConfig {
  overview: {
    name: string;
    finOrdRateTrend: TrendConfig;
    offpltPromtOrdCnt: { name: string; cardList: CardList[] };
    offpltPromtOrdCntNew: { name: string; cardList: CardList[] };
    onpltExtraOrdCnt: { name: string; cardList: CardList[] };
    target: TargetConfig;
  };

  business: {
    name: string;
    target: TargetConfig;
    finOrdRateTrend: {
      book: BusinessTrendItem;
      finish: BusinessTrendItem;
      reply: BusinessTrendItem;
      name: string;
    };
    orderStructure: {
      book: StructureItem;
      finish: StructureItem;
      reply: StructureItem;
      name: string;
    };
    progress: {
      bookProgress: ProgressItem[];
      finishProgress: ProgressItem[];
      replyProgress: ProgressItem[];
    };
  };

  sitePkg: {
    name: string;
    target: TargetConfig;
    finOrdRateTrend: {
      book: BusinessTrendItem;
      finish: BusinessTrendItem;
      reply: BusinessTrendItem;
      name: string;
    };
    orderStructure: {
      book: StructureItem;
      finish: StructureItem;
      reply: StructureItem;
      name: string;
    };
    progress: {
      bookProgress: ProgressItem[];
      finishProgress: ProgressItem[];
      replyProgress: ProgressItem[];
    };
  };
}
