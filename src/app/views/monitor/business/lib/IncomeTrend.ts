import { XAxisOptions, YAxisOptions } from 'highcharts';
import {
  getCategories,
  getNumberFields,
  getPercentFields,
  isEveryElementPercent,
} from '@common/chart';
import { BaseHighCharts, SeriesItem } from '@common/chart/highcharts';
import {
  createPoint,
  createValueElement,
  fillEmpty,
  getMaxLength,
  handleLegendItemClick,
  isEmpty,
  toDecimals,
} from '@common/function';
import { QueryOutputHeaderVo, QueryOutputVo } from '@api/query-engine/model';

function tooltipFormatter(that: IncomeTrend) {
  return function () {
    const result = [];
    const map = new Map();
    const params = this.points.sort(that.sortFn);

    params.forEach((item) => {
      if (map.has(item.series.name)) {
        const arr = map.get(item.series.name) as any[];
        arr.push(item);
        arr.reverse();
      } else {
        map.set(item.series.name, [item]);
      }
    });

    const merged = [...map.values()].flat(1);

    result.push('<table class="text-sm">');
    merged.forEach((point, index) => {
      const {
        series: {
          name: seriesName,
          yAxis: { index: yAxisIndex },
        },
        y: value,
        color,
      } = point;
      const { isPercent } = that.series.find(
        (item) => item.name === seriesName
      );
      const categorie = point.x;
      const previousItem = merged[index - 1];
      const isDateStr = /\d{4}-\d{2}-\d{2}/.test(categorie);
      const day = new Date(categorie.replace(/-/g, '/')).getDay();
      const weeks = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
      const week = isDateStr && weeks[day];

      if (previousItem?.series.name === seriesName) {
        const currentValue = value;
        const previousValue = previousItem.y;
        const ratioValue = toDecimals(
          (currentValue - previousValue) / previousValue
        );
        const diffValue = currentValue - previousValue;

        result.push(`
          <tr>
            <td class="flex items-center" style="color:${color}">
              ${createPoint(color)}
              (${categorie}${
          week ? '<span class="flex-1 min-w-0 w-1"></span>' : ''
        }${week || ''})
            </td>
            <td class="pr-2" style="color:${color}">${seriesName}: </td>
            <td class="text-right">
              ${
                yAxisIndex === 1 || isPercent
                  ? Number.isFinite(value)
                    ? value + '%'
                    : '-'
                  : Number.isFinite(value)
                  ? Intl.NumberFormat().format(value)
                  : '-'
              }
            </td>
            <td>
              ${
                yAxisIndex === 1 || isPercent
                  ? createValueElement(diffValue, '(较对比期 {n}pp)')
                  : Number.isFinite(ratioValue)
                  ? createValueElement(ratioValue, '(较对比期 {n}%)')
                  : '(较对比期 --)'
              }
            </td>
          </tr>
        `);
      } else {
        result.push(`
          <tr>
            <td class="flex items-center" style="color:${color}">
              ${createPoint(color)}
              (${categorie}${
          week ? '<span class="flex-1 min-w-0 w-1"></span>' : ''
        }${week || ''})
            </td>
            <td class="pr-2" style="color:${color}">${seriesName}: </td>
            <td class="text-right">
              ${
                yAxisIndex === 1 || isPercent
                  ? Number.isFinite(value)
                    ? value + '%'
                    : '-'
                  : Number.isFinite(value)
                  ? Intl.NumberFormat().format(value)
                  : '-'
              }
            </td>
            <td></td>
          </tr>
        `);
      }
    });
    result.push('</table>');

    return result.join('');
  };
}

class xAxisItem {
  categories: string[];
  opposite: boolean;
  tickInterval = 1;
  tickWidth = 1;
  tickColor = '#ccd6eb';
  lineColor = '#ccd6eb';
  gridLineColor = '#e6e6e6';
  crosshair = true;
  linkedTo: number;
  labels = {
    useHTML: true,
    formatter: function () {
      if (/\d{4}-\d{2}-\d{2}/.test(this.value)) {
        const day = new Date(this.value.replace(/-/g, '/')).getDay();
        if (day === 0 || day === 6) {
          return `<span class="text-primary font-semibold">${this.value}</span>`;
        }
      }

      return this.value;
    },
  };

  constructor({ categories, opposite }: Partial<xAxisItem>) {
    categories && (this.categories = categories);
    opposite && (this.opposite = opposite);
  }
}

export class IncomeTrend extends BaseHighCharts {
  xAxis: XAxisOptions[] = [];
  yAxis: YAxisOptions[] = [
    {
      title: { text: '' },
      labels: { format: undefined },
      gridLineWidth: 1,
      gridLineColor: '#e6e6e6',
    },
  ];

  plotOptions = {
    series: {
      turboThreshold: 999999999,
      marker: {
        radius: 2,
        symbol: 'circle',
      },
    } as any,
  };

  /**
   * Constructs a new instance of the LineMultipleXAxis class.
   *
   * @param properties - The properties used to initialize the LineMultipleXAxis instance.
   * @throws {string} Throws an error if the x-axis is empty.
   */
  constructor(properties: QueryOutputVo) {
    super();

    const { headers, data, compareData } = properties;
    const primary_xaxis = getCategories(headers, data);
    const secondary_xaxis = getCategories(headers, compareData);
    const primary_series = this.getSeries(headers, data, 0);
    const secondary_series = this.getSeries(headers, compareData, 1);
    const max_xaxis_length = getMaxLength(primary_xaxis, secondary_xaxis);
    const [percentFields] = getPercentFields(headers);
    const isEveryFieldPercent = isEveryElementPercent(headers);

    if (isEmpty(primary_xaxis)) {
      throw 'x轴不能为空';
    }

    if (isEveryFieldPercent) {
      this.yAxis = [
        {
          title: { text: '' },
          labels: { format: '{text}%' },
          gridLineWidth: 1,
          gridLineColor: '#e6e6e6',
        },
      ];
    } else if (percentFields) {
      this.yAxis.push({
        title: { text: '' },
        labels: { format: '{text}%' },
        gridLineWidth: 1,
        gridLineColor: '#e6e6e6',
        opposite: true,
      });
    }

    this.setCategories([
      fillEmpty(max_xaxis_length)(primary_xaxis),
      fillEmpty(max_xaxis_length)(secondary_xaxis),
    ]);

    primary_series.forEach((item, index) => {
      primary_series[index].data.length = max_xaxis_length;
    })

    secondary_series.forEach((item, index) => {
      secondary_series[index].data.length = max_xaxis_length;
    })

    this.setSeries([...primary_series, ...secondary_series]);
    // this.colors.length = this.series.length / 2;
    this.colors.length = Math.ceil(this.series.length / 2);
    this.legend.verticalAlign = 'top';
    this.chart.type = 'spline';
  }

  getSeries(
    headers: { [key: string]: QueryOutputHeaderVo },
    data: { [key: string]: string }[],
    index?: number
  ) {
    const numberFields = getNumberFields(headers);
    const isEveryFieldPercent = isEveryElementPercent(headers);
    const source = ['当前', '对比'];

    if (!data) {
      throw `${source[index] ?? ''}日期无数据`;
    }

    return numberFields.map((key) => {
      const { aliasName, dataUnit } = headers[key];
      const series = new SeriesItem();

      if (index > 0) {
        series.dashStyle = 'Dash';
        series.lineWidth = 1;
        series.linkedTo = `${key}`;
      }

      if (dataUnit === '%') {
        series.isPercent = true;
      }

      if (dataUnit === '%' && !isEveryFieldPercent) {
        series.yAxis = 1;
      }

      series.xAxis = index;
      series.name = aliasName;
      series.data = data.map((item) => {
        const value = Number(item[key]);

        if (item[key] === null) {
          return null;
        }

        if (dataUnit === '%') {
          return toDecimals(value);
        }

        return value;
      });

      return series;
    });
    // .filter((item) => {
    //   return item.data.every((value) => Number.isFinite(value));
    // });
  }

  /**
   * Sets the categories for the multiple x-axis.
   *
   * @param values - The array of string arrays representing the categories.
   */
  setCategories(values: string[][]): void {
    values.forEach((categories, index) => {
      const item = new xAxisItem({
        categories,
        opposite: index > 0,
      });

      if (index > 0) {
        item.linkedTo = 0;
      }

      this.xAxis.push(item);
    });
  }

  override getOption() {
    const value = this;

    return {
      ...value,
      tooltip: {
        ...value.tooltip,
        formatter: tooltipFormatter(this),
      },
      plotOptions: {
        ...value.plotOptions,
        series: {
          ...value.plotOptions.series,
          // events: {
          //   legendItemClick: handleLegendItemClick(this),
          // }
        },
      },
    };
  }
}
