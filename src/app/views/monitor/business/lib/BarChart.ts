import { QueryOutputHeaderVo, QueryOutputVo } from '@api/query-engine/model';
import { BaseHighCharts, SeriesItem } from '@common/chart/highcharts';
import { getMonthProgress } from '@common/class';
import { createPoint, createValueElement, toDecimals } from '@common/function';
import { XAxisOptions, YAxisOptions } from 'highcharts';


function tooltipFormatter(that: BarChart) {
  return function (params) {
    const result = [];
    const series = params.chart.series[0].data.map(({ category, y }) => {
      return { category, y }
    });

    result.push('<table class="text-sm">');
    
    this.points.forEach((point, index) => {
      const { color, x: category, y: value, } = point;
      const diff = parseFloat((value - series[0].y).toFixed(2));

      result.push(`
        <tr>
          <td class="flex items-center" style="color:${color}">
            ${createPoint(color)}
            ${category}：
          </td>
          <td class="text-right">
            ${value}%
          </td>
          <td>
            ${
              category !== series[0].category 
                ? createValueElement(diff, '(较日期进度 {n}pp)')
                : ''
            }
          </td>
        </tr>
      `);
    });
    result.push('</table>');

    return result.join('');
  };
}


export class BarChart extends BaseHighCharts {

  xAxis: XAxisOptions = {
    categories: [],
    gridLineWidth: 0,
    lineWidth: 1,
    tickWidth: 1,
  };
  
  yAxis: YAxisOptions = {
    title: { text: '' },
    labels: { format: '{text}%' },
    gridLineWidth: 0,
  };

  colors: string[] = ['#5087ec'];
  responsive = null;

  plotOptions = {
    bar: {
      dataLabels: {
        enabled: true,
        format: '{point.y}%'
      },
      groupPadding: 0.1
    }
  }

  categorySortFn = (a, b) => 0;
  seriesSortFn = (a, b) => 0;

  constructor(
    properties: QueryOutputVo, 
    deadline: string, 
    categorySortFn: (a, b) => number,
    seriesSortFn: (a, b) => number,
  ) {
    super();

    this.categorySortFn = categorySortFn;
    this.seriesSortFn = seriesSortFn;

    const { headers, data } = properties;
    const categories = this.getCategories(headers);
    const series = this.getSeries(data, deadline);

    this.chart.type = 'bar';
    this.legend.enabled = false;
    this.setSeries([series]);
    this.setCategories(categories);
  }


  getCategories(headers: { [key: string]: QueryOutputHeaderVo }) {
    const categories = Object.keys(headers).map(key => {
      const { aliasName, extendName } = headers[key];

      return {
        aliasName,
        extendName
      }
    });

    const aliasNames = categories
      .sort(this.categorySortFn)
      .map(item => item.aliasName);
      
    return [
      '日期进度', 
      ...new Set(aliasNames)
    ];
  }
  

  getSeries(data: { [key: string]: string }[], deadline: string) {
    const series = new SeriesItem();
    const progress = getMonthProgress(deadline);
    
    series.data = Object.keys(data[0]).sort(this.seriesSortFn).map(key => {
      return parseFloat((Number(data[0][key]) * 100).toFixed(2));
    })
    
    series.data.unshift(progress);
    series.xAxis = 0;
    series.name = '进度';
    return series
  }


  setCategories(categories: string[]) {
    this.xAxis.categories = categories;
  }


  override getOption() {
    const value = this;

    return {
      ...value,
      tooltip: {
        ...value.tooltip,
        formatter: tooltipFormatter(this),
      }
    };
  }

}