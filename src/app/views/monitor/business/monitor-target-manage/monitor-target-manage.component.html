<div class="w-full px-6">
  <div class="flex justify-center">
    <app-radio-group
      [(ngModel)]="currentType"
      (ngModelChange)="changeType()"
      class="flex items-start px-3"
    >
      <app-radio
        class="line-radio text-xs whitespace-nowrap"
        activeClass="active"
        value="detail"
      >
        目标详情
      </app-radio>
      <app-radio
        class="line-radio text-xs whitespace-nowrap"
        activeClass="active"
        value="upload"
      >
        目标上传
      </app-radio>
      <app-radio
        class="line-radio text-xs whitespace-nowrap"
        activeClass="active"
        value="budget"
      >
        预算详情
      </app-radio>
    </app-radio-group>
  </div>
  @if(currentType() === 'detail'){
  <div class="flex flex-col gap-2">
    <div>
      <span class="pr-2 font-bold text-[14px]">时间:</span>
      <nz-date-picker
        nzMode="month"
        [(ngModel)]="date"
        [nzAllowClear]="false"
        (ngModelChange)="getMonitorManageDetail()"
      ></nz-date-picker>
    </div>
    <nz-table
      #fixedTable
      [nzData]="detailData()"
      [nzFrontPagination]="false"
      [nzScroll]="{ y: '400px', x: '4600px' }"
      [nzLoading]="loading()"
    >
      <thead>
        <tr>
          <th nzWidth="130px" nzLeft>日期</th>
          <th>大盘自有下单量</th>
          <th>APP下单量</th>
          <th>微信小程序下单量</th>
          <th>支付宝小程序下单量</th>
          <th>站点拼车下单量</th>
          <th>非站点拼车下单量</th>
          <th>大盘自有接单量</th>
          <th>APP接单量</th>
          <th>微信小程序接单量</th>
          <th>支付宝小程序接单量</th>
          <th>站点拼车接单量</th>
          <th>非站点拼车接单量</th>
          <th>大盘自有完单量</th>
          <th>端内额外完单量</th>
          <th>端内乘客额外完单量</th>
          <th>端内车主额外完单量</th>
          <th>端外推广完单量</th>
          <th>端外乘客推广完单量</th>
          <th>端外车主推广完单量</th>
          <th>端外推广完单乘客数</th>
          <th>端外推广完单车主数</th>
          <th>自有-自然完单量（预估值）</th>
          <th>APP完单量</th>
          <th>微信小程序完单量</th>
          <th>支付宝小程序完单量</th>
          <th>站点拼车完单量</th>
          <th>非站点拼车完单量</th>
          <th>新注册用户-自然</th>
          <th>新注册用户-推广</th>
          <th>首单乘客数-自然</th>
          <th>首单乘客数-推广</th>
          <th>首单乘客完单量-自然</th>
          <th>首单乘客完单量-推广</th>
          <th>召回乘客数-自然</th>
          <th>召回乘客数-推广</th>
          <th>召回乘客完单量-自然</th>
          <th>召回乘客完单量-推广</th>
          <th>新认证车主数-自然</th>
          <th>新认证车主数-推广</th>
          <th>首单车主数-自然</th>
          <th>首单车主数-推广</th>
          <th>首单车主完单量-自然</th>
          <th>首单车主完单量-推广</th>
          <th>召回车主数-自然</th>
          <th>召回车主数-推广</th>
          <th>召回车主完单量-自然</th>
          <th>召回车主完单量-推广</th>
        </tr>
      </thead>
      <tbody>
        @for (data of fixedTable.data; track data) { @if($index === 0){
        <tr class="sticky top-0 z-999 bg-white">
          <td nzLeft>{{ data.dt }}</td>
          <td class="font-bold bg-yellow-100">
            {{ data.cownTargetBookOrdCnt }}
          </td>
          <td>{{ data.capTargetBookOrdCnt }}</td>
          <td>{{ data.cwxminipgTargetBookOrdCnt }}</td>
          <td>{{ data.caliminipgTargetBookOrdCnt }}</td>
          <td>{{ data.csitepkgTargetBookOrdCnt }}</td>
          <td>{{ data.cnonsitepkgTargetBookOrdCnt }}</td>
          <td class="font-bold bg-yellow-100">
            {{ data.cownTargetReplyOrdCnt }}
          </td>
          <td>{{ data.capTargetReplyOrdCnt }}</td>
          <td>{{ data.cwxminipgTargetReplyOrdCnt }}</td>
          <td>{{ data.caliminipgTargetReplyOrdCnt }}</td>
          <td>{{ data.csitepkgTargetReplyOrdCnt }}</td>
          <td>{{ data.cnonsitepkgTargetReplyOrdCnt }}</td>
          <td class="font-bold bg-yellow-100">
            {{ data.cownTargetFinishOrdCnt }}
          </td>
          <td>{{ data.conpltExtraTargetFinishOrdCnt }}</td>
          <td>{{ data.cpassOnpltExtraFinishFinishOrdCnt }}</td>
          <td>{{ data.cdriverOnpltExtraTargetFinishOrdCnt }}</td>
          <td>{{ data.coffpltPromtTargetFinishOrdCnt }}</td>
          <td>{{ data.cpassOffpltPromtTargetFinishOrdCnt }}</td>
          <td>{{ data.cdriverOffpltPromtTargetFinishOrdCnt }}</td>
          <td>{{ data.ctargetOffpltNewcFinishPassUnt }}</td>
          <td>{{ data.ctargetOffpltNewcFinishDriverUnt }}</td>
          <td>{{ data.cownNaturalTargetFinishOrdCnt }}</td>
          <td>{{ data.capTargetFinishOrdCnt }}</td>
          <td>{{ data.cwxminipgTargetFinishOrdCnt }}</td>
          <td>{{ data.caliminipgTargetFinishOrdCnt }}</td>
          <td>{{ data.csitepkgTargetFinishOrdCnt }}</td>
          <td>{{ data.cnonsitepkgTargetFinishOrdCnt }}</td>
          <td>{{ data.ctdTargetNewRegUvOwn }}</td>
          <td>{{ data.ctdTargetNewRegUvPromt }}</td>
          <td>{{ data.ctdTargetFordPassUvOwn }}</td>
          <td>{{ data.ctdTargetFordPassUvPromt }}</td>
          <td>{{ data.ctdTargetFordPassFinishOrdCntOwn }}</td>
          <td>{{ data.ctdTargetFordPassFinishOrdCntPromt }}</td>
          <td>{{ data.ctdTargetRecallPassUvOwn }}</td>
          <td>{{ data.ctdTargetRecallPassUvPromt }}</td>
          <td>{{ data.ctdTargetRecallPassFinishOrdCntOwn }}</td>
          <td>{{ data.ctdTargetRecallPassFinishOrdCntPromt }}</td>
          <td>{{ data.ctdTargetNewCeredUvOwn }}</td>
          <td>{{ data.ctdTargetNewCeredUvPromt }}</td>
          <td>{{ data.ctdTargetFordDriverUvOwn }}</td>
          <td>{{ data.ctdTargetFordDriverUvPromt }}</td>
          <td>{{ data.ctdTargetFordDriverFinishOrdCntOwn }}</td>
          <td>{{ data.ctdTargetFordDriverFinishOrdCntPromt }}</td>
          <td>{{ data.ctdTargetRecallDriverUvOwn }}</td>
          <td>{{ data.ctdTargetRecallDriverUvPromt }}</td>
          <td>{{ data.ctdTargetRecallDriverFinishOrdCntOwn }}</td>
          <td>{{ data.ctdTargetRecallDriverFinishOrdCntPromt }}</td>
        </tr>
        }@else{
        <tr>
          <td nzLeft>{{ data.dt }}</td>
          <td class="font-bold bg-yellow-100">
            {{ data.cownTargetBookOrdCnt }}
          </td>
          <td>{{ data.capTargetBookOrdCnt }}</td>
          <td>{{ data.cwxminipgTargetBookOrdCnt }}</td>
          <td>{{ data.caliminipgTargetBookOrdCnt }}</td>
          <td>{{ data.csitepkgTargetBookOrdCnt }}</td>
          <td>{{ data.cnonsitepkgTargetBookOrdCnt }}</td>
          <td class="font-bold bg-yellow-100">
            {{ data.cownTargetReplyOrdCnt }}
          </td>
          <td>{{ data.capTargetReplyOrdCnt }}</td>
          <td>{{ data.cwxminipgTargetReplyOrdCnt }}</td>
          <td>{{ data.caliminipgTargetReplyOrdCnt }}</td>
          <td>{{ data.csitepkgTargetReplyOrdCnt }}</td>
          <td>{{ data.cnonsitepkgTargetReplyOrdCnt }}</td>
          <td class="font-bold bg-yellow-100">
            {{ data.cownTargetFinishOrdCnt }}
          </td>
          <td>{{ data.conpltExtraTargetFinishOrdCnt }}</td>
          <td>{{ data.cpassOnpltExtraFinishFinishOrdCnt }}</td>
          <td>{{ data.cdriverOnpltExtraTargetFinishOrdCnt }}</td>
          <td>{{ data.coffpltPromtTargetFinishOrdCnt }}</td>
          <td>{{ data.cpassOffpltPromtTargetFinishOrdCnt }}</td>
          <td>{{ data.cdriverOffpltPromtTargetFinishOrdCnt }}</td>
          <td>{{ data.ctargetOffpltNewcFinishPassUnt }}</td>
          <td>{{ data.ctargetOffpltNewcFinishDriverUnt }}</td>
          <td>{{ data.cownNaturalTargetFinishOrdCnt }}</td>
          <td>{{ data.capTargetFinishOrdCnt }}</td>
          <td>{{ data.cwxminipgTargetFinishOrdCnt }}</td>
          <td>{{ data.caliminipgTargetFinishOrdCnt }}</td>
          <td>{{ data.csitepkgTargetFinishOrdCnt }}</td>
          <td>{{ data.cnonsitepkgTargetFinishOrdCnt }}</td>
          <td>{{ data.ctdTargetNewRegUvOwn }}</td>
          <td>{{ data.ctdTargetNewRegUvPromt }}</td>
          <td>{{ data.ctdTargetFordPassUvOwn }}</td>
          <td>{{ data.ctdTargetFordPassUvPromt }}</td>
          <td>{{ data.ctdTargetFordPassFinishOrdCntOwn }}</td>
          <td>{{ data.ctdTargetFordPassFinishOrdCntPromt }}</td>
          <td>{{ data.ctdTargetRecallPassUvOwn }}</td>
          <td>{{ data.ctdTargetRecallPassUvPromt }}</td>
          <td>{{ data.ctdTargetRecallPassFinishOrdCntOwn }}</td>
          <td>{{ data.ctdTargetRecallPassFinishOrdCntPromt }}</td>
          <td>{{ data.ctdTargetNewCeredUvOwn }}</td>
          <td>{{ data.ctdTargetNewCeredUvPromt }}</td>
          <td>{{ data.ctdTargetFordDriverUvOwn }}</td>
          <td>{{ data.ctdTargetFordDriverUvPromt }}</td>
          <td>{{ data.ctdTargetFordDriverFinishOrdCntOwn }}</td>
          <td>{{ data.ctdTargetFordDriverFinishOrdCntPromt }}</td>
          <td>{{ data.ctdTargetRecallDriverUvOwn }}</td>
          <td>{{ data.ctdTargetRecallDriverUvPromt }}</td>
          <td>{{ data.ctdTargetRecallDriverFinishOrdCntOwn }}</td>
          <td>{{ data.ctdTargetRecallDriverFinishOrdCntPromt }}</td>
        </tr>
        } }
      </tbody>
    </nz-table>
  </div>
  }@else if(currentType() === 'upload'){
  <div class="flex flex-col gap-3">
    <div class="flex gap-3">
      <button nz-button nzType="primary" (click)="downloadTemplate()">
        模板下载
      </button>
      @if(canUpload()){
      <label
        class="cursor-pointer h-[32px] py-[4px] px-[15px] text-center"
        style="border: solid 1px #d9d9d9"
      >
        <input
          #fileRef
          type="file"
          class="sr-only"
          (change)="upload($event)"
          accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel"
        />
        目标上传
      </label>
      }
    </div>
    <nz-table
      #basicTable
      [nzData]="targetData()"
      [nzFrontPagination]="false"
      [nzScroll]="{ y: '400px' }"
      [nzLoading]="historyLoading()"
    >
      <thead>
        <tr>
          <th>月份</th>
          <th>上传时间</th>
          <th>上传人</th>
          <th>历史上传文件</th>
        </tr>
      </thead>
      <tbody>
        @for (data of basicTable.data; track data) {
        <tr>
          <td>{{ data.ym }}</td>
          <td>{{ data.operateTime | date : "yyyy-MM-dd HH:mm:ss" }}</td>
          <td>{{ data.operator }}</td>
          <td>
            <a (click)="downloadHistoryFile(data.id, data.fileName)">{{
              data.fileName
            }}</a>
          </td>
        </tr>
        }
      </tbody>
    </nz-table>
    <div class="flex justify-end">
      <nz-pagination
        [nzPageIndex]="current()"
        [nzTotal]="total()"
        (nzPageIndexChange)="pageChange($event)"
      ></nz-pagination>
    </div>
  </div>
  }@else{
  <div class="flex flex-col gap-3">
    @if(canUpload()){
    <div>
      <button nz-button nzType="primary" (click)="showModal()">添加预算</button>
    </div>
    }
    <nz-table
      #basicTable
      [nzData]="budgetData()"
      [nzFrontPagination]="false"
      [nzScroll]="{ y: '400px' }"
      [nzLoading]="budgetLoading()"
    >
      <thead>
        <tr>
          <th>月份</th>
          <th>端内预算</th>
          <th>端内乘客预算</th>
          <th>端内车主预算</th>
          <th>端外预算</th>
          <th>端外乘客预算</th>
          <th>端外车主预算</th>
          @if(canUpload()){
          <th>操作</th>
          }
        </tr>
      </thead>
      <tbody>
        @for (data of basicTable.data; track data) {
        <tr>
          <td>{{ data.ym }}</td>
          <td class="font-bold bg-yellow-100">
            {{ data.ctmOnpltTargetCaverFee }}
          </td>
          <td>{{ data.ctmOnpltPassTargetCaverFee }}</td>
          <td>{{ data.ctmOnpltDriverTargetCaverFee }}</td>
          <td class="font-bold bg-yellow-100">
            {{ data.ctmOffpltTargetPromtFee }}
          </td>
          <td>{{ data.ctmOffpltPassTargetPromtFee }}</td>
          <td>{{ data.ctmOffpltDriverTargetPromtFee }}</td>
          @if(canUpload()){
          <td>
            <a (click)="edit(data)">编辑</a>
          </td>
          }
        </tr>
        }
      </tbody>
    </nz-table>
    <nz-modal
      [(nzVisible)]="isVisible"
      nzTitle="预算填报"
      (nzOnCancel)="handleCancel()"
      (nzOnOk)="handleOk()"
      [nzWidth]="800"
      [nzOkLoading]="saveLoading()"
    >
      <ng-container *nzModalContent>
        <form nz-form [formGroup]="validateForm">
          <nz-form-item>
            <nz-form-label [nzSm]="8" [nzXs]="24" nzRequired>
              月份
            </nz-form-label>
            <nz-form-control [nzSm]="16" [nzXs]="24" nzErrorTip="请选择月份!">
              <nz-date-picker
                [nzDisabled]="budgetType() === 'edit'"
                nzMode="month"
                formControlName="ym"
              ></nz-date-picker>
            </nz-form-control>
          </nz-form-item>
          <nz-form-item>
            <nz-form-label nzRequired [nzSm]="8" [nzXs]="24">
              端内预算
            </nz-form-label>
            <nz-form-control nzErrorTip="请输入预算!" [nzSm]="16" [nzXs]="24">
              <nz-input-number
                nzPlaceHolder="请输入整数,单位元"
                style="width: 200px"
                nzMin="0"
                formControlName="ctmOnpltTargetCaverFee"
                [nzPrecision]="0"
                [nzFormatter]="formatterNum"
                [nzParser]="parserNum"
              />
            </nz-form-control>
          </nz-form-item>
          <nz-form-item>
            <nz-form-label nzRequired [nzSm]="8" [nzXs]="24">
              端内乘客预算
            </nz-form-label>
            <nz-form-control nzErrorTip="请输入预算!" [nzSm]="16" [nzXs]="24">
              <nz-input-number
                nzPlaceHolder="请输入整数,单位元"
                style="width: 200px"
                nzMin="0"
                formControlName="ctmOnpltPassTargetCaverFee"
                [nzPrecision]="0"
                [nzFormatter]="formatterNum"
                [nzParser]="parserNum"
              />
            </nz-form-control>
          </nz-form-item>
          <nz-form-item>
            <nz-form-label nzRequired [nzSm]="8" [nzXs]="24">
              端内车主预算
            </nz-form-label>
            <nz-form-control nzErrorTip="请输入预算!" [nzSm]="16" [nzXs]="24">
              <nz-input-number
                nzPlaceHolder="请输入整数,单位元"
                style="width: 200px"
                nzMin="0"
                formControlName="ctmOnpltDriverTargetCaverFee"
                [nzPrecision]="0"
                [nzFormatter]="formatterNum"
                [nzParser]="parserNum"
              />
            </nz-form-control>
          </nz-form-item>

          <nz-form-item>
            <nz-form-label nzRequired [nzSm]="8" [nzXs]="24">
              端外预算
            </nz-form-label>
            <nz-form-control nzErrorTip="请输入预算!" [nzSm]="16" [nzXs]="24">
              <nz-input-number
                nzPlaceHolder="请输入整数,单位元"
                style="width: 200px"
                nzMin="0"
                formControlName="ctmOffpltTargetPromtFee"
                [nzPrecision]="0"
                [nzFormatter]="formatterNum"
                [nzParser]="parserNum"
              />
            </nz-form-control>
          </nz-form-item>
          <nz-form-item>
            <nz-form-label nzRequired [nzSm]="8" [nzXs]="24">
              端外乘客预算
            </nz-form-label>
            <nz-form-control nzErrorTip="请输入预算!" [nzSm]="16" [nzXs]="24">
              <nz-input-number
                nzPlaceHolder="请输入整数,单位元"
                style="width: 200px"
                nzMin="0"
                formControlName="ctmOffpltPassTargetPromtFee"
                [nzPrecision]="0"
                [nzFormatter]="formatterNum"
                [nzParser]="parserNum"
              />
            </nz-form-control>
          </nz-form-item>
          <nz-form-item>
            <nz-form-label nzRequired [nzSm]="8" [nzXs]="24">
              端外车主预算
            </nz-form-label>
            <nz-form-control nzErrorTip="请输入预算!" [nzSm]="16" [nzXs]="24">
              <nz-input-number
                nzPlaceHolder="请输入整数,单位元"
                style="width: 200px"
                nzMin="0"
                formControlName="ctmOffpltDriverTargetPromtFee"
                [nzPrecision]="0"
                [nzFormatter]="formatterNum"
                [nzParser]="parserNum"
              />
            </nz-form-control>
          </nz-form-item>
        </form>
      </ng-container>
    </nz-modal>
  </div>
  }
</div>
