import { ChangeDetectionStrategy, Component, signal, inject, ElementRef, viewChild, computed } from '@angular/core'
import { FormsModule } from '@angular/forms'
import { NzRadioModule } from 'ng-zorro-antd/radio'
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker'
import { NzTableModule } from 'ng-zorro-antd/table'
import { NzButtonModule } from 'ng-zorro-antd/button'
import { CaerusApiService } from '@api/caerus'
import { NzUploadModule } from 'ng-zorro-antd/upload'
import { isNumber } from '@common/function'
import { DatePipe } from '@angular/common'
import { finalize } from 'rxjs'
import { NzMessageService } from 'ng-zorro-antd/message'
import { NzPaginationModule } from 'ng-zorro-antd/pagination'
import { UserInfoService } from '@api/user-info.service'
import { isDev } from '@common/const'
import { find } from 'lodash'
import { NzModalModule } from 'ng-zorro-antd/modal'
import { Subject } from 'rxjs'
import { FormBuilder, ReactiveFormsModule, Validators } from '@angular/forms'
import { NzFormModule } from 'ng-zorro-antd/form'
import { NzInputNumberModule } from 'ng-zorro-antd/input-number'
import { RadioModule } from '@shared/modules/headless'

@Component({
  selector: 'app-monitor-target-manage',
  imports: [
    NzRadioModule,
    FormsModule,
    NzDatePickerModule,
    NzTableModule,
    NzButtonModule,
    NzUploadModule,
    DatePipe,
    NzPaginationModule,
    NzModalModule,
    ReactiveFormsModule,
    NzFormModule,
    NzInputNumberModule,
    RadioModule,
  ],
  templateUrl: './monitor-target-manage.component.html',
  styles: ``,
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [DatePipe],
})
export class MonitorTargetManageComponent {
  constructor(
    private message: NzMessageService,
    private fb: FormBuilder
  ) {}
  readonly apiService = inject(CaerusApiService)
  readonly userInfo = inject(UserInfoService)
  readonly datePipe = inject(DatePipe)
  private destroy$ = new Subject<void>()

  fileRef = viewChild<ElementRef<HTMLInputElement>>('fileRef')
  currentType = signal('detail')
  date = signal(new Date().setDate(new Date().getDate() - 1))
  targetData = signal([])
  detailData = signal<any>([])
  loading = signal(false)
  current = signal(1)
  total = signal(0)
  historyLoading = signal(false)
  budgetData = signal([])
  budgetLoading = signal(false)
  isVisible = signal(false)
  saveLoading = signal(false)
  budgetType = signal(null)
  currentbudgetData = signal<any>(null)

  canUpload = computed(() => {
    if (isDev()) {
      return true
    }
    const menu = find(this.userInfo.config(), ['url', '/monitor/business'])
    console.log('menu', menu)
    if (menu) {
      const button = find(menu.button, ['url', '/targetupload'])
      if (button) {
        return true
      }
      return false
    }
    return false
  })

  reduceTotal(_data, key) {
    let sum = 0
    _data.forEach(el => {
      if (isNumber(el[key])) {
        sum += el[key]
      }
    })
    return sum
  }

  getMonitorManageDetail() {
    this.loading.set(true)
    const month = this.datePipe.transform(this.date(), 'yyyy-MM')
    this.apiService
      .getMonitorManageDetail(month)
      .pipe(finalize(() => this.loading.set(false)))
      .subscribe((res: any) => {
        console.log('getMonitorManageDetail', res)
        if (res.data) {
          let _obj = {}
          if (res.data.length !== 0) {
            const arr = Object.keys(res.data[0])
            const keys = ['dt', 'id', 'operator', 'updateTime', 'createTime']
            arr.forEach(item => {
              if (!keys.includes(item)) {
                _obj[item] = this.reduceTotal(res.data, item)
              }
            })
          }
          const obj = {
            dt: '累计',
            ..._obj,
          }

          res.data.unshift(obj)
          this.detailData.set(res.data)
          console.log('_obj', this.detailData())
        } else {
          this.detailData.set([])
        }
      })
  }

  getMonitorUploadHistory() {
    this.historyLoading.set(true)
    this.apiService
      .getMonitorUploadHistory(this.current(), 10)
      .pipe(finalize(() => this.historyLoading.set(false)))
      .subscribe((res: any) => {
        console.log('getMonitorUploadHistory', res.data)
        if (res.data && res.data.records) {
          this.targetData.set(res.data.records)
          this.total.set(res.data.total)
        } else {
          this.targetData.set([])
        }
      })
  }

  getBudgetData() {
    this.budgetLoading.set(true)
    this.apiService
      .getBudgetList()
      .pipe(finalize(() => this.budgetLoading.set(false)))
      .subscribe(res => {
        console.log('budget', res)
        if (res.data) {
          this.budgetData.set(res.data)
        } else {
          this.budgetData.set([])
        }
      })
  }

  ngAfterViewInit(): void {
    this.getMonitorManageDetail()
  }

  changeType() {
    if (this.currentType() === 'detail') {
      this.getMonitorManageDetail()
    } else if (this.currentType() === 'upload') {
      this.getMonitorUploadHistory()
    } else {
      this.getBudgetData()
    }
  }

  downloadTemplate() {
    this.apiService.getTemplateDownload('业务盯盘-目标上传模板', '1').subscribe()
  }

  downloadHistoryFile(_id, name) {
    this.apiService.getHistoryFileDownload(_id, name.split('.')[0]).subscribe()
  }

  upload(event) {
    const file = (event.target as HTMLInputElement).files[0]
    const formData = new FormData()
    formData.append('file', file)
    const id = this.message.loading('上传中', {
      nzDuration: 0,
    }).messageId
    this.apiService.uploadTargetFile(formData).subscribe(res => {
      console.log('upload', res)
      this.message.remove(id)
      if (res.body.status === '00000') {
        this.message.success('上传成功')
        this.getMonitorUploadHistory()
      } else {
        this.message.error(`上传失败, ${res.body.message}`)
      }
      this.fileRef().nativeElement.value = ''
    })
  }

  pageChange(page) {
    this.current.set(page)
    this.getMonitorUploadHistory()
  }

  showModal() {
    this.budgetType.set('add')
    this.isVisible.set(true)
  }

  handleOk() {
    console.log('submit', this.validateForm.value)
    if (this.validateForm.valid) {
      this.saveLoading.set(true)
      const data = {
        ...this.validateForm.value,
        ym: this.datePipe.transform(this.validateForm.value.ym, 'yyyy-MM'),
      }
      if (this.budgetType() === 'add') {
        this.apiService
          .addBudget(data)
          .pipe(finalize(() => this.saveLoading.set(false)))
          .subscribe(res => {
            console.log('add', res)
            if (res.status !== '00000') {
              this.message.error(res.message)
              return
            }
            this.message.success('添加成功')
            this.handleCancel()
            this.getBudgetData()
          })
      } else {
        this.apiService
          .updateBudget({
            ...data,
            id: this.currentbudgetData().id,
          })
          .pipe(finalize(() => this.saveLoading.set(false)))
          .subscribe(res => {
            console.log('add', res)
            if (res.status !== '00000') {
              this.message.error(res.message)
              return
            }
            this.message.success('编辑成功')
            this.handleCancel()
            this.getBudgetData()
          })
      }
    } else {
      Object.values(this.validateForm.controls).forEach(control => {
        if (control.invalid) {
          control.markAsDirty()
          control.updateValueAndValidity({ onlySelf: true })
        }
      })
    }
  }

  handleCancel() {
    this.isVisible.set(false)
    this.validateForm.reset()
    Object.values(this.validateForm.controls).forEach(control => {
      control.clearValidators()
    })
  }

  validateForm = this.fb.group({
    ym: this.fb.control<Date | null>(null, [Validators.required]),
    ctmOnpltTargetCaverFee: this.fb.control(null, [Validators.required]),
    ctmOnpltPassTargetCaverFee: this.fb.control(null, [Validators.required]),
    ctmOnpltDriverTargetCaverFee: this.fb.control(null, [Validators.required]),
    ctmOffpltTargetPromtFee: this.fb.control(null, [Validators.required]),
    ctmOffpltPassTargetPromtFee: this.fb.control(null, [Validators.required]),
    ctmOffpltDriverTargetPromtFee: this.fb.control(null, [Validators.required]),
  })

  ngOnDestroy(): void {
    this.destroy$.next()
    this.destroy$.complete()
  }

  edit(_data) {
    this.currentbudgetData.set(_data)
    this.budgetType.set('edit')
    this.isVisible.set(true)
    this.validateForm.controls.ym.setValue(_data.ym)
    this.validateForm.controls.ctmOnpltTargetCaverFee.setValue(_data.ctmOnpltTargetCaverFee)
    this.validateForm.controls.ctmOnpltPassTargetCaverFee.setValue(_data.ctmOnpltPassTargetCaverFee)
    this.validateForm.controls.ctmOnpltDriverTargetCaverFee.setValue(_data.ctmOnpltDriverTargetCaverFee)
    this.validateForm.controls.ctmOffpltTargetPromtFee.setValue(_data.ctmOffpltTargetPromtFee)
    this.validateForm.controls.ctmOffpltPassTargetPromtFee.setValue(_data.ctmOffpltPassTargetPromtFee)
    this.validateForm.controls.ctmOffpltDriverTargetPromtFee.setValue(_data.ctmOffpltDriverTargetPromtFee)
  }

  formatterNum(value) {
    console.log('ffff', value)
    return `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
  }

  parserNum(value) {
    const ppp = value.replace(/\$\s?|(,*)/g, '')
    return Number(ppp)
  }
}
