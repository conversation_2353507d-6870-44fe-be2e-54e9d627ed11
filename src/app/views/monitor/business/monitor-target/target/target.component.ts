import { ChangeDetectionStrategy, Component, DestroyRef, inject, input, signal } from '@angular/core';
import { toObservable, takeUntilDestroyed, rxResource } from '@angular/core/rxjs-interop';
import { combineLatest, filter, startWith, tap } from 'rxjs';

import { sortBy } from '@common/chart';
import { isNotNull } from '@common/function';
import { QueryEngineFormService } from '@common/service/query-engine';
import { BaseHighCharts } from '@common/chart/highcharts';
import { QueryOutputVo } from '@api/query-engine/model';
import { QueryEngineApiService } from '@api/query-engine';
import { BarChart, ProgressItem } from '@views/monitor/business/lib';
import { BusinessService } from '@views/monitor/business';
import { LineSpinComponent } from '@shared/components/line-spin';
import { GraphComponent } from '@shared/components/graph';


@Component({
  selector: 'app-target',
  template: `
    <div class="shadow-md rounded-sm border border-neutral-100">
      <h4 class="font-bold text-center pt-3">{{title()}}</h4>
      <div class="relative flex items-center justify-center min-h-96 text-xs">
        @if (queryResource.isLoading()) {
          <app-line-spin />
        } @else {
          @if (option()) {
            <app-graph class="absolute inset-5" [options]="option()" />
          } @else if (queryResource.error()) {
            <span class="text-slate-600 text-xs">{{queryResource.error()}}</span>
          }
        }
      </div>
    </div>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: {
    'data-id': 'month-progress'
  },
  imports: [
    LineSpinComponent,
    GraphComponent
  ],
  providers: [
    QueryEngineFormService,
  ],
})
export class TargetComponent {

  readonly destroyRef = inject(DestroyRef);
  readonly apiService = inject(QueryEngineApiService);
  readonly formService = inject(QueryEngineFormService);
  readonly service = inject(BusinessService);
  
  title = input.required<string>();
  metrics = input.required<ProgressItem[]>();

  option = signal<BaseHighCharts>(null);
  metrics$ = toObservable(this.metrics);

  queryBody = signal(this.formService.value());
  queryResource = rxResource({
    request: () => this.queryBody(),
    loader: ({ request }) => this.apiService.search(request, 'target').pipe(
      tap((res) => {
        this._setChartData(res.data, request.dt.endTime);
      })
    )
  })

  ngAfterViewInit(): void {
    this._subscribeToDeadlineChange();
  }

  private _subscribeToDeadlineChange() {
    combineLatest([
      this.service.deadline$,
      this.metrics$,
    ]).pipe(
      startWith(null),
      filter(isNotNull),
      takeUntilDestroyed(this.destroyRef)
    ).subscribe(([deadline, metrics]) => {
      if (deadline && metrics) {
        this.formService.setMetrics(metrics);
        this.formService.dt.patchValue({ startTime: deadline, endTime: deadline });
        this._setQueryBody();
      }
    })
  }

  private _setQueryBody() {
    const body = this.formService.value();
    
    body.dimensions = [];
    body.scene = 1;
    body.dataFillConfig = {
      open: 1,
      fillRangeType: 2,
      fillValue: 1
    }

    this.queryBody.set(body);
  }

  private _setChartData(data: QueryOutputVo, deadline: string) {
    try {
      const order = this.metrics().map(item => item.extendName);
      const chart = new BarChart(
        data, 
        deadline, 
        sortBy(order, 'extendName'), 
        sortBy(order, null)
      );
      
      this.option.set(chart?.getOption() || null);
    } catch (e) {
      console.error(e);
    }
  }

}
