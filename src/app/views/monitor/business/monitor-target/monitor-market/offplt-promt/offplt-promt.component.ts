import { FormsModule } from '@angular/forms'
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  computed,
  DestroyRef,
  inject,
  signal,
} from '@angular/core'
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop'
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox'
import { NzPopoverModule } from 'ng-zorro-antd/popover'
import { combineLatest, debounceTime, filter, finalize } from 'rxjs'

import { SwitchMap } from '@common/decorator'
import { getMonthFirstAndLastDay } from '@common/class'
import { isEmpty, isNotNull, isObject } from '@common/function'
import { QueryEngineFormService } from '@common/service/query-engine'
import { LegendControlService, LegendItemClickHandler } from '@common/service'
import { BaseHighCharts } from '@common/chart/highcharts'
import { QueryOutputVo } from '@api/query-engine/model'
import { QueryEngineApiService } from '@api/query-engine'
import { RadioModule } from '@shared/modules/headless'
import { GraphComponent } from '@shared/components/graph'
import { LineSpinComponent } from '@shared/components/line-spin'
import { CardList, Metric, ProgressTrend } from '@views/monitor/business/lib'
import { IconSuccessFillComponent } from '@shared/modules/icons'
import { CardComponent } from '@views/monitor/business/components'
import { BusinessService } from '@views/monitor/business'
import { Router } from '@angular/router'

class MetricItem {
  id = null
  aliasName = null
  extendName: string
  type = null
  customType = null
  proportionDimension = []
  constructor(props?: Partial<MetricItem>) {
    if (props) {
      Object.assign(this, { ...props })
    }
  }
}

@Component({
  selector: 'app-offplt-promt',
  template: `
    <h3 class="font-bold text-base">
      端外推广完单（业务口径）
      <span class="text-center pt-3 cursor-pointer text-blue-500" style="font-size:15px" (click)="toOffPlt()">
        查看更多端外推广内容
      </span>
    </h3>
    <div class="flex flex-auto gap-5 h-105">
      <div class="flex flex-wrap min-w-150 max-w-150 h-full gap-y-5">
        @if (metricErrorMessage()) {
          <div class="h-full content-center text-center text-xs bg-neutral-100">
            {{ metricErrorMessage() }}
          </div>
        } @else {
          <app-radio-group class="grid grid-cols-2 h-full gap-2" [(ngModel)]="index" (ngModelChange)="select($event)">
            @for (item of list(); track $index) {
              <ng-template #metricsTitleTemplate>
                {{ item?.showName }}
                <span class="text-xs opacity-30 px-1">({{ item?.aliasName }})</span>
              </ng-template>

              <app-radio
                nz-popover
                class="card-radio content-center"
                activeClass="active"
                [nzPopoverPlacement]="'top'"
                [nzPopoverMouseEnterDelay]="0.5"
                [nzPopoverTitle]="metricsTitleTemplate"
                [nzPopoverContent]="item?.bizExpression"
                [value]="$index + 1"
              >
                <app-card [data]="item" [subName]="false" />
                <SuccessFillIcon *radioChecked class="absolute right-3 top-3 text-blue-600 text-xl" />
              </app-radio>
            }
          </app-radio-group>
        }
      </div>
      <div class="flex-1 min-w-0 flex flex-col h-full shadow-md rounded-sm border border-neutral-100 relative">
        <span class="absolute -top-6 text-xs text-neutral-400 font-normal">
          说明：点击图例默认单选，长按shift键点击可多选。
        </span>
        <div class="flex gap-x-5 items-center pt-2">
          <app-radio-group [(ngModel)]="type" class="flex items-start px-3">
            <app-radio class="line-radio text-xs whitespace-nowrap" activeClass="active" value="yesterday">
              昨日目标
            </app-radio>
            <app-radio class="line-radio text-xs whitespace-nowrap" activeClass="active" value="accumulate">
              本月MTD目标
            </app-radio>
          </app-radio-group>

          <div>
            @for (item of metricList(); track $index) {
              <ng-template #metricsTitleTemplate>
                {{ item?.showName }}
                <span class="text-xs opacity-30 px-1">({{ item?.showName }})</span>
              </ng-template>

              <label
                nz-popover
                nz-checkbox
                class="ml-0! text-xs!"
                [nzPopoverMouseEnterDelay]="0.5"
                [nzPopoverTitle]="metricsTitleTemplate"
                [nzPopoverContent]="item?.bizExpression"
                [(ngModel)]="item.checked"
                (ngModelChange)="onMetricsChange()"
              >
                {{ item.showName }}
              </label>
            }
          </div>
        </div>

        <div class="flex-1 min-h-0 relative flex items-center justify-center text-xs">
          @if (loading()) {
            <app-line-spin />
          } @else {
            @if (option()) {
              <app-graph class="absolute inset-2" [options]="option()" showAnnotations showAvgPlotLines />
            } @else if (errorMessage()) {
              <span class="text-slate-600 text-xs">{{ errorMessage() }}</span>
            }
          }
        </div>
      </div>
    </div>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: {
    class: 'block mb-5',
  },
  imports: [
    FormsModule,
    RadioModule,
    NzCheckboxModule,
    NzPopoverModule,
    CardComponent,
    LineSpinComponent,
    IconSuccessFillComponent,
    GraphComponent,
  ],
  providers: [QueryEngineFormService, LegendControlService],
})
export class OffpltPromtComponent implements AfterViewInit {
  readonly cdr = inject(ChangeDetectorRef)
  readonly destroyRef = inject(DestroyRef)
  readonly apiService = inject(QueryEngineApiService)
  readonly legendControlService = inject(LegendControlService)
  readonly formService = inject(QueryEngineFormService)
  readonly service = inject(BusinessService)
  readonly router = inject(Router)

  index = signal(0)
  type = signal('accumulate')
  type$ = toObservable(this.type)
  loading = signal(false)
  errorMessage = signal<string>(null)
  metricErrorMessage = signal<string>(null)
  option = signal<BaseHighCharts>(null)

  list = signal<CardList[]>([])
  metricList = signal<
    Array<{
      showName: string
      bizExpression: string
      metrics: Metric[]
      checked?: boolean
    }>
  >([])

  items = computed(() => {
    return this.service.config()?.overview.offpltPromtOrdCntNew?.cardList || []
  })

  metrics = computed(() => {
    return this.items()
      .map(item => {
        return [...item.accumulate, ...item.yesterday]
      })
      .flat(1)
  })

  toOffPlt() {
    this.router.navigateByUrl('monitor/business/offplt')
  }

  ngAfterViewInit(): void {
    this._subscribeToConfigChange()
    this._subscribeToDeadlineChange()
  }

  private _subscribeToConfigChange() {
    combineLatest([this.service.config$, this.service.deadline$, this.type$])
      .pipe(debounceTime(300), takeUntilDestroyed(this.destroyRef))
      .subscribe(([config]) => {
        if (config) {
          console.log('cccc', config)
          const metrics = config.overview.offpltPromtOrdCntNew.cardList
            // .filter(item => item.cardDisplay)
            .map((item, index) => {
              return {
                showName: item.showName,
                bizExpression: item.bizExpression,
                metrics: item[this.type()],
                checked: isEmpty(this.metricList()) ? index === 0 : this.metricList()[index].checked,
              }
            })

          this.metricList.set(metrics)
          this.queryChartData()
        }
      })
  }

  private _subscribeToDeadlineChange() {
    this.service.deadline$.pipe(filter(isNotNull), takeUntilDestroyed(this.destroyRef)).subscribe(deadline => {
      const startTime = deadline
      const endTime = deadline
      const metrics = this.metrics()

      this.formService.setMetrics(metrics)
      this.formService.dt.patchValue({ startTime, endTime })
      this.query()
    })
  }

  @SwitchMap()
  query() {
    const body = this.formService.value()

    body.scene = 1
    body.dataFillConfig = {
      open: 1,
      fillRangeType: 2,
      fillValue: 0,
    }

    this.loading.set(true)
    this.metricErrorMessage.set(null)

    return this.apiService
      .search(body, 'offplt-promt-metrics')
      .pipe(finalize(() => this.loading.set(false)))
      .subscribe(res => {
        if (res.status !== '00000') {
          console.error(res.error || res.message)
          this.metricErrorMessage.set(res.error || res.message)
        }

        if (res.data) {
          this._updateMetrics(res.data.data[0])
        }
      })
  }

  @SwitchMap()
  queryChartData() {
    const body = this.formService.value()
    const deadline = this.service.deadline()
    const [startTime, endTime] = getMonthFirstAndLastDay(deadline)
    const selectedMetrics = this.metricList().filter(item => item.checked)

    body.dt = { startTime, endTime }
    body.metrics = selectedMetrics
      .map(item => {
        return item.metrics
          .filter(item => {
            if (selectedMetrics.length > 1) {
              return item.isRate
            }
            return true
          })
          .map(child => {
            return new MetricItem({ extendName: child.extendName })
          })
      })
      .flat(1)

    body.scene = 1
    body.dataFillConfig = {
      open: 1,
      fillRangeType: 1,
      fillValue: 1,
    }

    this.loading.set(true)
    this.errorMessage.set(null)
    this.option.set(null)

    return this.apiService
      .search(body, 'offplt-promt-chart')
      .pipe(finalize(() => this.loading.set(false)))
      .subscribe(res => {
        if (res.status !== '00000') {
          console.error(res.error || res.message)
          this.errorMessage.set(res.error || res.message)
        }

        if (res.data) {
          this.legendControlService.reset()
          this._setChartData(res.data)
        }
      })
  }

  private _setChartData(data: QueryOutputVo) {
    try {
      const chart = new ProgressTrend(data)
      const legendItemClick = LegendItemClickHandler(this.legendControlService)
      chart.plotOptions.series.events = { legendItemClick }
      this.option.set(chart?.getOption() || null)
    } catch (e) {
      console.error(e)
    }
  }

  private _updateMetrics(obj: { [key: string]: string }) {
    this.service.config.update(config => {
      config.overview.offpltPromtOrdCntNew.cardList = config.overview.offpltPromtOrdCntNew.cardList.map(item => {
        Object.keys(item).forEach(key => {
          if (isObject(item[key])) {
            const { extendName } = item[key]

            item[key].value = obj[extendName]
          }
        })
        return item
      })
      return config
    })

    const { cardList } = this.service.config()?.overview?.offpltPromtOrdCntNew || {}
    const value = (cardList || []).filter(item => item.cardDisplay)

    this.list.set([])

    setTimeout(() => {
      this.list.set(value)
      console.log('list==', this.list())
    })
  }

  select(index: number) {
    this.metricList.update(items => {
      return items.map((item, n) => {
        return {
          ...item,
          checked: index === n,
        }
      })
    })
    this.queryChartData()
  }

  onMetricsChange() {
    this.queryChartData()
  }
}
