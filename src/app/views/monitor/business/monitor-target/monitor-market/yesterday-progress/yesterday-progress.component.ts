import { FormsModule } from '@angular/forms';
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
  effect,
  inject,
  signal,
} from '@angular/core';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NzPopoverModule } from 'ng-zorro-antd/popover';
import { combineLatest, debounceTime, finalize } from 'rxjs';

import { isEmpty } from '@common/function';
import { getMonthFirstAndLastDay } from '@common/class';
import { SwitchMap } from '@common/decorator';
import { LegendControlService, LegendItemClickHandler } from '@common/service';
import { BaseHighCharts } from '@common/chart/highcharts';
import { QueryEngineFormService } from '@common/service/query-engine';
import { QueryEngineApiService } from '@api/query-engine';
import { GraphComponent } from '@shared/components/graph';
import { LineSpinComponent } from '@shared/components/line-spin';
import { RadioModule } from '@shared/modules/headless';
import { BusinessService } from '@views/monitor/business';
import { Metric, ProgressTrend } from '@views/monitor/business/lib';
import { QueryOutputVo } from '@api/query-engine/model';

class MetricItem {
  id = null;
  aliasName = null;
  extendName: string;
  type = null;
  customType = null;
  proportionDimension = [];
  constructor(props?: Partial<MetricItem>) {
    if (props) {
      Object.assign(this, { ...props });
    }
  }
}

@Component({
  selector: 'app-yesterday-progress',
  template: `
    <div class="col-span-8">
      <h3 class="font-bold text-base">每日目标完成进度（业务口径）</h3>
      <div class="mb-2 text-xs text-neutral-400 font-normal">
        说明：点击图例默认单选，长按shift键点击可多选。
      </div>
      <div
        class="flex flex-col h-96 shadow-md rounded-sm border border-neutral-100"
      >
        <div class="flex gap-x-5 items-center pt-2">
          <app-radio-group [(ngModel)]="type" class="flex items-start px-3">
            <app-radio
              class="line-radio text-xs whitespace-nowrap"
              activeClass="active"
              value="yesterday"
              >昨日目标</app-radio
            >
            <app-radio
              class="line-radio text-xs whitespace-nowrap"
              activeClass="active"
              value="accumulate"
              >本月MTD目标</app-radio
            >
          </app-radio-group>

          <div>
            @for (item of metrics(); track $index) {
            <ng-template #metricsTitleTemplate>
              {{ item?.showName }}
              <span class="text-xs opacity-30 px-1"
                >({{ item?.aliasName }})</span
              >
            </ng-template>

            <label
              nz-popover
              nz-checkbox
              class="ml-0! text-xs!"
              [nzPopoverMouseEnterDelay]="0.5"
              [nzPopoverTitle]="metricsTitleTemplate"
              [nzPopoverContent]="item?.bizExpression"
              [(ngModel)]="item.checked"
              (ngModelChange)="onMetricsChange()"
              >{{ item.showName }}</label
            >
            }
          </div>
        </div>

        <div
          class="flex-1 min-h-0 relative flex items-center justify-center text-xs"
        >
          @if (loading()) {
          <app-line-spin />
          } @else { @if (option()) {
          <app-graph
            class="absolute inset-2"
            [options]="option()"
            showAnnotations
            showAvgPlotLines
          />
          } @else if (errorMessage()) {
          <span class="text-slate-600 text-xs">{{ errorMessage() }}</span>
          } }
        </div>
      </div>
    </div>
  `,
  host: {
    class: 'contents',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    FormsModule,
    RadioModule,
    NzCheckboxModule,
    NzPopoverModule,
    LineSpinComponent,
    GraphComponent,
  ],
  providers: [LegendControlService, QueryEngineFormService],
})
export class YesterdayProgressComponent implements AfterViewInit {
  readonly destroyRef = inject(DestroyRef);
  readonly legendControlService = inject(LegendControlService);
  readonly apiService = inject(QueryEngineApiService);
  readonly formService = inject(QueryEngineFormService);
  readonly service = inject(BusinessService);

  type = signal('accumulate');
  type$ = toObservable(this.type);
  option = signal<BaseHighCharts>(null);
  errorMessage = signal<string>(null);
  loading = signal(false);
  metrics = signal<
    Array<{
      showName: string;
      aliasName: string;
      bizExpression: string;
      metrics: Metric[];
      checked?: boolean;
    }>
  >([]);
  metrics$ = toObservable(this.metrics);

  ngAfterViewInit(): void {
    combineLatest([this.service.config$, this.service.deadline$, this.type$])
      .pipe(debounceTime(300), takeUntilDestroyed(this.destroyRef))
      .subscribe(([config, deadline]) => {
        if (config && deadline) {
          const metrics = config.overview.target.cardList.map((item, index) => {
            return {
              showName: item.showName,
              aliasName: item.aliasName,
              bizExpression: item.bizExpression,
              metrics: item[this.type()],
              checked: isEmpty(this.metrics())
                ? index === 0
                : this.metrics()[index].checked,
            };
          });

          this.metrics.set(metrics);
          this.query();
        }
      });
  }

  @SwitchMap()
  query() {
    const body = this.formService.value();
    const deadline = this.service.deadline();
    const [startTime, endTime] = getMonthFirstAndLastDay(deadline);
    const selectedMetrics = this.metrics().filter((item) => item.checked);

    if (!deadline) {
      return;
    }

    body.dt = { startTime, endTime };
    body.metrics = selectedMetrics
      .map((item) => {
        return item.metrics
          .filter((item) => {
            if (selectedMetrics.length > 1) {
              return item.isRate;
            }
            return true;
          })
          .map((child) => {
            return new MetricItem({ extendName: child.extendName });
          });
      })
      .flat(1);

    body.scene = 1;
    body.dataFillConfig = {
      open: 1,
      fillRangeType: 1,
      fillValue: 1,
    };

    this.loading.set(true);
    return this.apiService
      .search(body, 'yesterday-progress')
      .pipe(finalize(() => this.loading.set(false)))
      .subscribe((res) => {
        if (res.status !== '00000') {
          console.error(res.error || res.message);
          this.errorMessage.set(res.error || res.message);
        }

        if (res.data) {
          this.legendControlService.reset();
          this._setChartData(res.data);
        }
      });
  }

  private _setChartData(data: QueryOutputVo) {
    try {
      const chart = new ProgressTrend(data);
      const legendItemClick = LegendItemClickHandler(this.legendControlService);
      chart.plotOptions.series.events = { legendItemClick };
      this.option.set(chart?.getOption() || null);
    } catch (e) {
      console.error(e);
    }
  }

  select(index: number) {
    this.metrics.update((items) => {
      return items.map((item, n) => {
        return {
          ...item,
          checked: index === n,
        };
      });
    });
    this.query();
  }

  onMetricsChange() {
    this.query();
  }
}
