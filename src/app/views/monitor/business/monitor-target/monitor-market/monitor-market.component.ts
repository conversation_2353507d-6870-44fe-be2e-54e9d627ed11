import { ChangeDetectionStrategy, Component, viewChild } from '@angular/core';

import { TargetMetricsComponent } from './target-metrics/target-metrics.component';
import { YesterdayProgressComponent } from './yesterday-progress/yesterday-progress.component';
import { MonthProgressComponent } from './month-progress/month-progress.component';
import { FinishOrderTrendComponent } from './finish-order-trend/finish-order-trend.component';
import { OffpltPromtComponent } from './offplt-promt/offplt-promt.component';
import { OnpltExtraComponent } from './onplt-extra/onplt-extra.component';
import { MonitorIncomeComponent } from '../../monitor-income/monitor-income.component';
import { IncomeOnpltExtraComponent } from './income-onplt-extra/income-onplt-extra.component';
import { IncomeOffpltPromtComponent } from './income-offplt-promt/income-offplt-promt.component';

@Component({
  selector: 'app-monitor-market',
  template: `
    <app-target-metrics id="anchor-order" />

    <div class="grid grid-cols-12 gap-x-5 mb-5">
      <app-yesterday-progress />
      <app-month-progress />
    </div>
    <app-monitor-income id="anchor-income" />
    <app-onplt-extra id="anchor-driver" />
    <app-offplt-promt />
    <app-income-onplt-extra id="anchor-offplt" />
    <app-income-offplt-promt />
    <app-finish-order-trend id="anchor-trend" />
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    TargetMetricsComponent,
    MonthProgressComponent,
    YesterdayProgressComponent,
    FinishOrderTrendComponent,
    OffpltPromtComponent,
    OnpltExtraComponent,
    MonitorIncomeComponent,
    IncomeOnpltExtraComponent,
    IncomeOffpltPromtComponent,
  ],
})
export class MonitorMarketComponent {
  yesterdayProgressRef = viewChild(YesterdayProgressComponent);
}
