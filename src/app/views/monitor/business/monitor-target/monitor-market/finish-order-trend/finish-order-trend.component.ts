import { FormsModule } from '@angular/forms';
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  computed,
  DestroyRef,
  inject,
  signal,
} from '@angular/core';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { combineLatest, filter, finalize } from 'rxjs';

import { SwitchMap } from '@common/decorator';
import { getMonthFirstAndLastDay } from '@common/class';
import { BaseHighCharts } from '@common/chart/highcharts';
import { QueryEngineFormService } from '@common/service/query-engine';
import { LegendControlService, LegendItemClickHandler } from '@common/service';
import { isNotNull } from '@common/function';
import { QueryOutputVo } from '@api/query-engine/model';
import { LineSpinComponent } from '@shared/components/line-spin';
import { QueryEngineApiService } from '@api/query-engine';
import { RadioModule } from '@shared/modules/headless';
import { GraphComponent } from '@shared/components/graph';
import { BusinessService } from '../../../business.service';
import { IncomeTrend } from '../../../lib/IncomeTrend';

@Component({
  selector: 'app-finish-order-trend',
  template: `
    <div class="mb-5 mt-5">
      <h2 class="font-bold text-lg">订单结构</h2>
      <h3 class="font-bold text-base">
        完单占比趋势（业务口径）
        <span class="ml-1 text-xs text-neutral-400 font-normal"
          >说明：点击图例默认单选，长按shift键点击可多选。</span
        >
      </h3>
      <div class="shadow-md rounded-sm border border-neutral-100">
        <app-radio-group [(ngModel)]="type" class="flex items-start px-3 pt-2">
          <app-radio
            class="line-radio text-xs"
            activeClass="active"
            value="yesterday"
            >昨日占比</app-radio
          >
          <app-radio
            class="line-radio text-xs"
            activeClass="active"
            value="accumulate"
            >本月累计占比</app-radio
          >
        </app-radio-group>

        <div
          class="relative flex items-center justify-center h-100 text-xs"
        >
          @if (loading()) {
          <app-line-spin />
          } @else { @if (option()) {
          <app-graph
            class="absolute inset-3"
            [options]="option()"
            showAnnotations
            showAvgPlotLines
          />
          } @else if (errorMessage()) {
          <span class="text-slate-600 text-xs">{{ errorMessage() }}</span>
          } }
        </div>
      </div>
    </div>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [FormsModule, RadioModule, LineSpinComponent, GraphComponent],
  providers: [LegendControlService, QueryEngineFormService],
})
export class FinishOrderTrendComponent implements AfterViewInit {
  readonly destroyRef = inject(DestroyRef);
  readonly legendControlService = inject(LegendControlService);
  readonly apiService = inject(QueryEngineApiService);
  readonly formService = inject(QueryEngineFormService);
  readonly service = inject(BusinessService);

  loading = signal(false);
  errorMessage = signal<string>(null);
  type = signal('accumulate');

  metrics = computed(() => {
    return this.service?.config()?.overview?.finOrdRateTrend[this.type()];
  });

  metrics$ = toObservable(this.metrics);

  option = signal<BaseHighCharts>(null);

  ngAfterViewInit(): void {
    this.formService.addDateCompare();
    this._subscribeToDeadlineChange();
  }

  private _subscribeToDeadlineChange() {
    combineLatest([this.service.deadline$, this.metrics$])
      .pipe(filter(isNotNull), takeUntilDestroyed(this.destroyRef))
      .subscribe(([deadline, metrics]) => {
        if (deadline && metrics) {
          const [startTime, endTime] = getMonthFirstAndLastDay(deadline);

          this.formService.setMetrics(metrics);
          this.formService.dt.patchValue({ startTime, endTime });
          this.formService.compareDt.patchValue({
            startTime: 'now/1M',
            endTime: 'now/1M',
          });
          this.query();
        }
      });
  }

  @SwitchMap()
  query() {
    const body = this.formService.value();

    body.dataFillConfig = {
      open: 1,
      fillRangeType: 2,
      fillValue: 1,
    };

    this.loading.set(true);
    this.option.set(null);
    return this.apiService
      .search(body, 'finish-order-trend')
      .pipe(finalize(() => this.loading.set(false)))
      .subscribe((res) => {
        if (res.status !== '00000') {
          this.errorMessage.set(res.error || res.message);
        }

        if (res.data) {
          this.legendControlService.reset();
          this._setChartData(res.data);
        }
      });
  }

  private _setChartData(data: QueryOutputVo) {
    try {
      const chart = new IncomeTrend(data);
      const legendItemClick = LegendItemClickHandler(this.legendControlService);
      chart.plotOptions.series.events = { legendItemClick };
      this.option.set(chart?.getOption() || null);
    } catch (e) {
      console.error(e);
    }
  }
}
