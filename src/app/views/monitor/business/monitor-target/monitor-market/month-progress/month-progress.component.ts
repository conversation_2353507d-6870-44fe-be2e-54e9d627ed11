import {
  ChangeDetectionStrategy,
  Component,
  computed,
  DestroyRef,
  inject,
  signal,
} from '@angular/core';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { combineLatest, filter, finalize } from 'rxjs';

import { sortBy } from '@common/chart';
import { SwitchMap } from '@common/decorator';
import { QueryEngineApiService } from '@api/query-engine';
import { BaseHighCharts } from '@common/chart/highcharts';
import { QueryEngineFormService } from '@common/service/query-engine';
import { isNotNull } from '@common/function';
import { GraphComponent } from '@shared/components/graph';
import { LineSpinComponent } from '@shared/components/line-spin';
import { QueryOutputVo } from '@api/query-engine/model';
import { BusinessService } from '../../../business.service';
import { BarChart } from '../../../lib/BarChart';

@Component({
  selector: 'app-month-progress',
  template: `
    <div class="col-span-4">
      <h3 class="font-bold text-base">月度总目标完成进度（业务口径 ）</h3>
      <div class="mb-2 text-xs text-neutral-400 font-normal">
        说明：月度总目标完成进度，即本月累计值与本月总目标之间的比值
      </div>
      <div
        class="relative flex items-center justify-center h-96 text-xs shadow-md rounded-sm border border-neutral-100"
      >
        @if (loading()) {
        <app-line-spin />
        } @else { @if (option()) {
        <app-graph
          class="absolute inset-5"
          [options]="option()"
          showAnnotations
          showAvgPlotLines
        />
        } @else if (errorMessage()) {
        <span class="text-slate-600 text-xs">{{ errorMessage() }}</span>
        } }
      </div>
    </div>
  `,
  host: {
    class: 'contents',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [LineSpinComponent, GraphComponent],
  providers: [QueryEngineFormService],
})
export class MonthProgressComponent {
  readonly destroyRef = inject(DestroyRef);
  readonly apiService = inject(QueryEngineApiService);
  readonly formService = inject(QueryEngineFormService);
  readonly service = inject(BusinessService);

  loading = signal(true);
  errorMessage = signal<string>(null);
  option = signal<BaseHighCharts>(null);

  metrics = computed(() => {
    return this.service?.config()?.overview?.target?.monthProgress || [];
  });

  metrics$ = toObservable(this.metrics);

  ngAfterViewInit(): void {
    this._subscribeToDeadlineChange();
  }

  private _subscribeToDeadlineChange() {
    combineLatest([this.service.deadline$, this.metrics$])
      .pipe(filter(isNotNull), takeUntilDestroyed(this.destroyRef))
      .subscribe(([deadline, metrics]) => {
        if (deadline && metrics) {
          this.formService.setMetrics(metrics);
          this.formService.dt.patchValue({
            startTime: deadline,
            endTime: deadline,
          });
          this.query();
        }
      });
  }

  @SwitchMap()
  query() {
    const body = this.formService.value();

    body.dimensions = [];
    body.scene = 1;
    body.dataFillConfig = {
      open: 1,
      fillRangeType: 2,
      fillValue: 1,
    };

    this.loading.set(true);
    this.option.set(null);
    return this.apiService
      .search(body, 'month-progress')
      .pipe(finalize(() => this.loading.set(false)))
      .subscribe((res) => {
        if (res.status !== '00000') {
          this.errorMessage.set(res.error || res.message);
        }

        if (res.data) {
          this._setChartData(res.data, body.dt.endTime);
        }
      });
  }

  private _setChartData(data: QueryOutputVo, deadline: string) {
    try {
      const order = this.metrics().map((item) => item.extendName);
      const chart = new BarChart(
        data,
        deadline,
        sortBy(order, 'extendName'),
        sortBy(order, null)
      );

      this.option.set(chart?.getOption() || null);
    } catch (e) {
      console.error(e);
    }
  }
}
