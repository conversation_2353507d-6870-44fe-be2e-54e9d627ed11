import { DecimalPipe } from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  inject,
  signal,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { QueryOutputVo } from '@api/query-engine/model';
import { BaseHighCharts } from '@common/chart/highcharts';
import { LegendControlService, LegendItemClickHandler } from '@common/service';
import { GraphComponent } from '@shared/components/graph';
import { LineSpinComponent } from '@shared/components/line-spin';
import { RadioModule } from '@shared/modules/headless';
import { CaerusApiService } from '@api/caerus';
import { QueryEngineFormService } from '@common/service/query-engine';
import { finalize } from 'rxjs';
import { QueryEngineApiService } from '@api/query-engine';
import { IconSuccessFillComponent } from '@shared/modules/icons';
import { NzPopoverModule } from 'ng-zorro-antd/popover';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { getMonthFirstAndLastDay } from '@common/class';
import { ProgressTrend } from '../../../lib';
import { ProgressComponent } from '../../../components';
import { BusinessService } from '../../../business.service';
import { SwitchMap } from '@common/decorator';

@Component({
  selector: 'app-income-onplt-extra',
  imports: [
    DecimalPipe,
    FormsModule,
    RadioModule,
    LineSpinComponent,
    GraphComponent,
    NzCheckboxModule,
    ProgressComponent,
    IconSuccessFillComponent,
    NzPopoverModule,
  ],
  providers: [
    LegendControlService,
    QueryEngineApiService,
    QueryEngineFormService,
  ],
  templateUrl: './income-onplt-extra.component.html',
  styleUrl: './income-onplt-extra.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class IncomeOnpltExtraComponent implements AfterViewInit {
  readonly apiService = inject(CaerusApiService);
  readonly service = inject(BusinessService);
  readonly formService = inject(QueryEngineFormService);
  readonly legendControlService = inject(LegendControlService);
  readonly queryService = inject(QueryEngineApiService);

  type = signal('accumulate');
  option = signal<BaseHighCharts>(null);
  errorMessage = signal<string>(null);
  loading = signal(false);
  card_loading = signal(false);
  card_list = signal([]);
  card_data = signal({});
  index = signal(-1);

  ngAfterViewInit(): void {
    this.apiService.fetchConfig('business_monitoring_inc').subscribe((res) => {
      if (res.data) {
        console.log('aaaaaa', res.data);
        res.data.onpltExtraOrdCnt.cardList[0].checked = true;
        this.card_list.set(res.data.onpltExtraOrdCnt.cardList);
        this.subscribeDeadline();
      }
    });
    // this._setChartData(mock);
  }

  private subscribeDeadline() {
    this.service.deadline$.subscribe((value) => {
      if (value) {
        this.getCardData();
        this.getChartData();
      }
    });
  }

  @SwitchMap()
  getChartData() {
    const deadline = this.service.deadline();
    if (!deadline) {
      return;
    }
    const [startTime, endTime] = getMonthFirstAndLastDay(deadline);
    const body = this.formService.value();
    body.dt = {
      startTime,
      endTime,
    };
    body.dataFillConfig = {
      open: 1,
      fillRangeType: 1,
      fillValue: 1,
    };
    body.dimensions = [
      {
        extendName: 'dt',
      },
    ];
    const metrics = [];
    const checkedList = this.card_list().filter((c) => c.checked);

    checkedList.forEach((c) => {
      c[this.type()].forEach((l) => {
        if (this.type() === 'accumulate' && checkedList.length > 1) {
          if (l.isRate) {
            metrics.push({
              extendName: l.extendName,
            });
          }
        } else {
          metrics.push({
            extendName: l.extendName,
          });
        }
      });
    });
    body.metrics = metrics;
    this.loading.set(true);
    this.legendControlService.reset();
    this.queryService
      .search(body, 'onplt-extra-chart')
      .pipe(finalize(() => this.loading.set(false)))
      .subscribe((res) => {
        console.log('onplt-extra-chart', res);
        if (res.data) {
          this._setChartData(res.data);
        } else {
          this.option.set(null);
          this.errorMessage.set(res.message);
        }
      });
  }

  @SwitchMap()
  getCardData() {
    const deadline = this.service.deadline();
    if (!deadline) {
      return;
    }
    const body = this.formService.value();
    body.dt = {
      startTime: deadline,
      endTime: deadline,
    };
    body.dataFillConfig = {
      open: 1,
      fillRangeType: 2,
      fillValue: 1,
    };
    body.dimensions = [
      {
        extendName: 'dt',
      },
    ];
    body.metrics = this.card_list()
      .map((l) => {
        return l.yesterdayMetric;
      })
      .concat(
        this.card_list().map((l) => {
          return l.accumulateMetric;
        })
      )
      .concat(
        this.card_list().map((l) => {
          return l.accumulateTarget;
        })
      );
    this.card_loading.set(true);
    this.queryService
      .search(body, 'onplt-extra')
      .pipe(finalize(() => this.card_loading.set(false)))
      .subscribe((res) => {
        if (res.data && res.data.data && res.data.data.length !== 0) {
          this.card_data.set(res.data.data[0]);
          console.log('onplt-extra', this.card_data());
        } else {
          this.card_data.set({});
        }
      });
  }

  private _setChartData(data: QueryOutputVo) {
    try {
      const chart = new ProgressTrend(data);
      const legendItemClick = LegendItemClickHandler(this.legendControlService);
      chart.plotOptions.series.events = { legendItemClick };
      this.option.set(chart?.getOption() || null);
    } catch (e) {
      console.error(e);
    }
  }

  changeRadio(index) {
    if (index === -1) {
      return;
    }
    this.card_list().forEach((c, i) => {
      if (i === index) {
        c.checked = true;
      } else {
        c.checked = false;
      }
    });
    this.getChartData();
  }
}
