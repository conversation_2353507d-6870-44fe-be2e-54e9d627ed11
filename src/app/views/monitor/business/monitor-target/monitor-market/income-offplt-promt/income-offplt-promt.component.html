<h3 class="font-bold text-base mt-5">
  端外推广费用
  <span class="ml-1 text-xs text-neutral-400 font-normal"
    >说明：此处优惠券核销要求业务完单；且按实际核销时间统计，对应乘客侧的支付时间和车主侧的给车主打款时间。</span
  >
</h3>
<div class="grid grid-cols-12 gap-5 h-100">
  <app-radio-group
    class="col-span-5 grid grid-cols-2 grid-rows-2 gap-5"
    [(ngModel)]="index"
    (ngModelChange)="changeRadio($event)"
  >
    @for(item of card_list();track item){ @if(this.card_loading()){
    <app-line-spin />
    }@else{
    <ng-template #metricsTitleTemplate>
      {{ item?.showName }}
      <span class="text-xs opacity-30 px-1">({{ item?.aliasName }})</span>
    </ng-template>
    <app-radio
      nz-popover
      [nzPopoverTitle]="metricsTitleTemplate"
      [nzPopoverContent]="item?.bizExpression"
      class="radio"
      [class]="$index === 0 ? 'col-span-2' : ''"
      activeClass="active"
      [value]="$index"
    >
      <div class="flex flex-col items-center justify-around h-full p-2">
        <h2
          class="flex items-center justify-center gap-x-1 font-bold text-base text-inherit"
        >
          {{ item?.showName }}
          <!-- <span class="text-xs font-normal">{{ item?.subName }}</span> -->
        </h2>
        <div class="flex items-center justify-center gap-x-1 w-full">
          <div
            class="flex-1 min-w-0 flex items-center justify-center text-xs"
            [class]="$index !== 0 ? 'flex-col' : ''"
          >
            昨日费用：
            <span class="font-bold text-lg">{{
              card_data()[item.yesterdayMetric.extendName] | number
            }}</span>
          </div>
          <div
            class="flex-1 min-w-0 flex items-center justify-center text-xs"
            [class]="$index !== 0 ? 'flex-col' : ''"
          >
            本月累计费用：
            <span class="font-bold text-lg">{{
              card_data()[item.accumulateMetric.extendName] | number
            }}</span>
          </div>
        </div>
        <div class="w-full">
          <app-progress
            [style]="2"
            [value]="card_data()[item.accumulateMetric.extendName]"
            [total]="card_data()[item.accumulateTarget.extendName]"
          />
        </div>
      </div>
      <SuccessFillIcon
        *radioChecked
        class="absolute right-3 top-3 text-blue-600 text-xl"
      />
    </app-radio>
    } }
  </app-radio-group>

  <div class="col-span-7 border border-neutral-100 rounded-sm shadow-sm">
    <div class="flex flex-col h-full">
      <div class="flex gap-x-5 items-center pt-2">
        <app-radio-group
          [(ngModel)]="type"
          class="flex items-start px-3"
          (ngModelChange)="getChartData()"
        >
          <app-radio
            class="line-radio text-xs whitespace-nowrap"
            activeClass="active"
            value="yesterday"
            >昨日费用</app-radio
          >
          <app-radio
            class="line-radio text-xs whitespace-nowrap"
            activeClass="active"
            value="accumulate"
            >本月累计费用</app-radio
          >
        </app-radio-group>
        <div>
          @for (item of card_list(); track $index) {
          <ng-template #metricsTitleTemplate>
            {{ item?.showName }}
            <span class="text-xs opacity-30 px-1">({{ item?.aliasName }})</span>
          </ng-template>
          <label
            nz-popover
            nz-checkbox
            class="ml-0! text-xs!"
            [nzPopoverMouseEnterDelay]="0.5"
            [nzPopoverTitle]="metricsTitleTemplate"
            [nzPopoverContent]="item?.bizExpression"
            [(ngModel)]="item.checked"
            (ngModelChange)="getChartData()"
          >
            {{ item.showName }}
          </label>
          }
        </div>
      </div>

      <div
        class="flex-1 min-h-0 relative flex items-center justify-center text-xs"
      >
        @if (loading()) {
        <app-line-spin />
        } @else { @if (option()) {
        <app-graph
          class="absolute inset-2"
          [options]="option()"
          showAnnotations
          showAvgPlotLines
        />
        } @else if (errorMessage()) {
        <span class="text-slate-600 text-xs">{{ errorMessage() }}</span>
        } }
      </div>
    </div>
  </div>
</div>
