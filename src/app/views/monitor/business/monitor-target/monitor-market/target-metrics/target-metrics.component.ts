import { NgClass } from '@angular/common';
import { FormsModule } from '@angular/forms';
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  computed,
  DestroyRef,
  inject,
  signal,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { NzPopoverModule } from 'ng-zorro-antd/popover';
import { filter, finalize } from 'rxjs';

import { SwitchMap } from '@common/decorator';
import { isNotNull, isObject } from '@common/function';
import { QueryEngineApiService } from '@api/query-engine';
import { IconSuccessFillComponent } from '@shared/modules/icons';
import { QueryEngineFormService } from '@common/service/query-engine';
import { LineSpinComponent } from '@shared/components/line-spin';
import { RadioModule } from '@shared/modules/headless';
import { MonitorMarketComponent } from '../monitor-market.component';
import { BusinessService } from '../../../business.service';
import { CardComponent } from '../../../components';
import { CardList } from '../../../lib/BusinessConfig';

@Component({
  selector: 'app-target-metrics',
  template: `
    <h2 class="font-bold text-lg">订单达成</h2>
    <div class="mb-5">
      @if (loading()) {
      <div class="flex items-center justify-center h-[424px]">
        <app-line-spin />
      </div>
      } @else { @if (errorMessage()) {
      <div class="flex items-center justify-center h-[424px]">
        {{ errorMessage() }}
      </div>
      } @else {
      <app-radio-group
        class="grid grid-cols-1 grid-flow-row md:grid-cols-2 lg:grid-cols-3 gap-5"
        [(ngModel)]="index"
        (ngModelChange)="onIndexChange($event)"
      >
        @for (item of list1(); track $index) {
        <ng-template #metricsTitleTemplate>
          {{ item?.showName }}
          <span class="text-xs opacity-30 px-1">({{ item?.aliasName }})</span>
        </ng-template>

        <ng-template #metricsContentTemplate>
          <div class="leading-normal" [innerHTML]="item?.bizExpression"></div>
        </ng-template>

        <app-radio
          nz-popover
          class="card-radio"
          activeClass="active"
          [nzPopoverMouseEnterDelay]="0.5"
          [nzPopoverTitle]="metricsTitleTemplate"
          [nzPopoverContent]="metricsContentTemplate"
          [ngClass]="{
            'bg-orange-50!': $index < 3,
            'bg-white!': $index > 2,
          }"
          [value]="$index"
        >
          <app-card [data]="item" />
          <SuccessFillIcon
            *radioChecked
            class="absolute right-3 top-3 text-blue-600 text-xl"
          />
        </app-radio>
        }
      </app-radio-group>
      @if(index() !== 0 && index() !== 1){
      <app-radio-group
        class="grid grid-cols-1 grid-flow-row md:grid-cols-2 lg:grid-cols-3 gap-5 pt-6 px-[4%]"
        [(ngModel)]="index"
        (ngModelChange)="onIndexChange($event)"
      >
        @for (item of list2(); track $index) {
        <ng-template #metricsTitleTemplate>
          {{ item?.showName }}
          <span class="text-xs opacity-30 px-1">({{ item?.aliasName }})</span>
        </ng-template>

        <ng-template #metricsContentTemplate>
          <div class="leading-normal" [innerHTML]="item?.bizExpression"></div>
        </ng-template>

        <app-radio
          nz-popover
          class="card-radio"
          activeClass="active"
          [nzPopoverMouseEnterDelay]="0.5"
          [nzPopoverTitle]="metricsTitleTemplate"
          [nzPopoverContent]="metricsContentTemplate"
          [ngClass]="{
            'bg-white!': true,
          }"
          [value]="$index + 3"
        >
          <app-card [data]="item" />
          <SuccessFillIcon
            *radioChecked
            class="absolute right-3 top-3 text-blue-600 text-xl"
          />
        </app-radio>
        }
      </app-radio-group>
      } } }
    </div>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgClass,
    FormsModule,
    NzPopoverModule,
    RadioModule,
    CardComponent,
    IconSuccessFillComponent,
    LineSpinComponent,
  ],
  providers: [QueryEngineFormService],
})
export class TargetMetricsComponent implements AfterViewInit {
  readonly destroyRef = inject(DestroyRef);
  readonly apiService = inject(QueryEngineApiService);
  readonly formService = inject(QueryEngineFormService);
  readonly service = inject(BusinessService);
  readonly parent = inject(MonitorMarketComponent);

  loading = signal(false);
  errorMessage = signal<string>(null);

  index = signal(2);
  list1 = signal<CardList[]>([]);
  list2 = signal<CardList[]>([]);
  items = computed(() => {
    return this.service.config()?.overview?.target?.cardList || [];
  });

  metrics = computed(() => {
    return this.items()
      .map((item) => {
        return [...item.accumulate, ...item.yesterday];
      })
      .flat(1);
  });

  ngAfterViewInit(): void {
    this._subscribeToDeadlineChange();
  }

  private _subscribeToDeadlineChange() {
    this.service.deadline$
      .pipe(filter(isNotNull), takeUntilDestroyed(this.destroyRef))
      .subscribe((deadline) => {
        const startTime = deadline;
        const endTime = deadline;
        const metrics = this.metrics();

        this.formService.setMetrics(metrics);
        this.formService.dt.patchValue({ startTime, endTime });
        this.query();
      });
  }

  @SwitchMap()
  query() {
    const body = this.formService.value();

    body.dimensions[0].predefineCompareType = ['dt'];
    body.scene = 1;
    body.dataFillConfig = {
      open: 1,
      fillRangeType: 2,
      fillValue: 0,
    };

    this.loading.set(true);
    this.errorMessage.set(null);

    return this.apiService
      .search(body, 'target-metrics')
      .pipe(finalize(() => this.loading.set(false)))
      .subscribe((res) => {
        if (res.status !== '00000') {
          console.error(res.error || res.message);
          this.errorMessage.set(res.error || res.message);
        }

        if (res.data) {
          this._updateMetrics(res.data.data[0]);
        }
      });
  }

  private _updateMetrics(obj: { [key: string]: string }) {
    this.service.config.update((config) => {
      config.overview.target.cardList = config.overview.target.cardList.map(
        (item) => {
          Object.keys(item).forEach((key) => {
            if (isObject(item[key])) {
              const { extendName } = item[key];

              item[key].value = obj[extendName];

              if (item.showDayBeforeYesterday) {
                if (key === 'yesterdayMetric') {
                  item.beforeYesterMetric = {
                    extendName: extendName + ':dt_COMPARE_VALUE',
                    value: obj[extendName + ':dt_COMPARE_VALUE'],
                  };
                }

                if (key === 'yesterdayTarget') {
                  item.beforeYesterTarget = {
                    extendName: extendName + ':dt_COMPARE_VALUE',
                    value: obj[extendName + ':dt_COMPARE_VALUE'],
                  };
                }
              }
            }
          });
          return item;
        }
      );
      return config;
    });

    const value = this.service.config()?.overview?.target?.cardList || [];
    console.log('data====', value);
    this.list1.set([]);
    this.list2.set([]);

    setTimeout(() => {
      this.list1.set(
        value.filter((v, i) => {
          return i < 3;
        })
      );
      this.list2.set(
        value.filter((v, i) => {
          return i > 2;
        })
      );
    });
  }

  onIndexChange(value: number) {
    this.parent.yesterdayProgressRef().select(value);
  }
}
