import { FormsModule } from '@angular/forms';
import { AfterViewInit, ChangeDetectionStrategy, Component, inject, output, signal } from '@angular/core';

import { CaerusApiService } from '@api/caerus';
import { QueryEngineApiService } from '@api/query-engine';
import { RadioModule } from '@shared/modules/headless';
import { MonitorMarketComponent } from './monitor-market/monitor-market.component';
import { MonitorChannelComponent } from './monitor-channel/monitor-channel.component';
import { MonitorSiteComponent } from './monitor-site/monitor-site.component';
import { BusinessConfig } from '../lib/BusinessConfig';
import { BusinessService } from '../business.service';


@Component({
  selector: 'app-monitor-target',
  template: `
    <div class="p-5">
      <header class="relative flex items-center gap-x-1 px-5 h-12 ">
        <!-- <span class="text-xl">目标监控</span> -->

        <app-radio-group [(ngModel)]="type" (ngModelChange)="changeType()" class="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-80 flex items-start gap-x-0.5 p-0.5 bg-[#ebebec] rounded-lg">
          <app-radio class="dida-radio" activeClass="dida-radio-active" value="market">自有大盘监控</app-radio>
          <app-radio class="dida-radio" activeClass="dida-radio-active" value="channel">分业务渠道监控</app-radio>
          <app-radio class="dida-radio" activeClass="dida-radio-active" value="site">站点拼车监控</app-radio>
        </app-radio-group>
      </header>

      <div class="h-5"></div>

      @switch (type()) {
        @case ('market') { <app-monitor-market /> }
        @case ('channel') { <app-monitor-channel /> }
        @case ('site') { <app-monitor-site /> }
      }
    </div>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [FormsModule, RadioModule, MonitorMarketComponent, MonitorChannelComponent, MonitorSiteComponent],
})
export class MonitorTargetComponent implements AfterViewInit {

  readonly apiService = inject(CaerusApiService);
  readonly queryEngineApiService = inject(QueryEngineApiService);
  readonly service = inject(BusinessService);
  
  onTypeChange = output<string>();
  
  type = signal('market');

  changeType() {
    this.onTypeChange.emit(this.type());
  }

  ngAfterViewInit() {
    this.fetchConfig();
  }

  private fetchConfig() {
    this.apiService.fetchConfig<BusinessConfig>('business_monitoring_target').subscribe(res => {
      if (res.status === '00000') {
        this.service.config.set(res.data);
      }
    });
  }

}
