import { NgClass } from '@angular/common';
import { FormsModule } from '@angular/forms';
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  computed,
  DestroyRef,
  inject,
  signal,
} from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { NzPopoverModule } from 'ng-zorro-antd/popover';
import { filter, finalize } from 'rxjs';

import { SwitchMap } from '@common/decorator';
import { isNotNull, isObject } from '@common/function';
import { QueryEngineApiService } from '@api/query-engine';
import { QueryEngineFormService } from '@common/service/query-engine';
import { LineSpinComponent } from '@shared/components/line-spin';
import { RadioModule } from '@shared/modules/headless';
import { BusinessService } from '../../../business.service';
import { CardComponent } from '../../../components';
import { CardList } from '../../../lib/BusinessConfig';

@Component({
  selector: 'app-target-metrics',
  template: `
    <h2 class="font-bold text-lg">订单达成</h2>
    <div class="mb-5">
      @if (loading()) {
      <div class="flex items-center justify-center h-[168px]">
        <app-line-spin />
      </div>
      } @else {
      <div
        class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 grid-rows-2 gap-5"
      >
        @for (item of list(); track $index) {
        <ng-template #metricsTitleTemplate>
          {{ item?.showName }}
          <span class="text-xs opacity-30 px-1">({{ item?.aliasName }})</span>
        </ng-template>

        <app-card
          nz-popover
          nz-checkbox
          class="border border-slate-300/80"
          [nzPopoverMouseEnterDelay]="0.5"
          [nzPopoverTitle]="metricsTitleTemplate"
          [nzPopoverContent]="item?.bizExpression"
          [ngClass]="{
                'bg-blue-50/50': $index < 3,
                'bg-orange-50/50': $index > 2,
              }"
          [data]="item"
        />
        }
      </div>
      }
    </div>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgClass,
    FormsModule,
    NzPopoverModule,
    RadioModule,
    CardComponent,
    LineSpinComponent,
  ],
  providers: [QueryEngineFormService],
})
export class TargetMetricsComponent implements AfterViewInit {
  readonly destroyRef = inject(DestroyRef);
  readonly apiService = inject(QueryEngineApiService);
  readonly formService = inject(QueryEngineFormService);
  readonly service = inject(BusinessService);

  loading = signal(false);
  errorMessage = signal<string>(null);

  index = signal(0);
  list = signal<CardList[]>([]);
  items = computed(() => {
    return this.service.config()?.business?.target?.cardList || [];
  });

  metrics = computed(() => {
    return this.items()
      .map((item) => {
        return [...item.accumulate, ...item.yesterday];
      })
      .flat(1);
  });

  ngAfterViewInit(): void {
    this._subscribeToDeadlineChange();
  }

  private _subscribeToDeadlineChange() {
    this.service.deadline$
      .pipe(filter(isNotNull), takeUntilDestroyed(this.destroyRef))
      .subscribe((deadline) => {
        const startTime = deadline;
        const endTime = deadline;
        const metrics = this.metrics();

        this.formService.setMetrics(metrics);
        this.formService.dt.patchValue({ startTime, endTime });
        this.query();
      });
  }

  @SwitchMap()
  query() {
    const body = this.formService.value();

    body.scene = 1;
    body.dataFillConfig = {
      open: 1,
      fillRangeType: 2,
      fillValue: 0,
    };

    this.loading.set(true);
    this.errorMessage.set(null);

    return this.apiService
      .search(body, 'channel-target-metrics')
      .pipe(finalize(() => this.loading.set(false)))
      .subscribe((res) => {
        console.log('res.data', res.data);
        if (res.status !== '00000') {
          console.error(res.error || res.message);
          this.errorMessage.set(res.error || res.message);
        }

        if (res.data) {
          this._updateMetrics(res.data.data[0]);
        }
      });
  }

  private _updateMetrics(obj: { [key: string]: string }) {
    this.service.config.update((config) => {
      config.business.target.cardList = config.business.target.cardList.map(
        (item) => {
          Object.keys(item).forEach((key) => {
            if (isObject(item[key])) {
              const { extendName } = item[key];

              item[key].value = obj[extendName];
            }
          });
          return item;
        }
      );
      return config;
    });

    const value = this.service.config()?.business?.target?.cardList || [];
    console.log('vvvvvv', value);
    this.list.set([]);

    setTimeout(() => {
      this.list.set(value);
    });
  }
}
