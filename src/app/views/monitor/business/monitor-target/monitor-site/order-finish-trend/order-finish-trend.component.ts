import { FormsModule } from '@angular/forms';
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  computed,
  DestroyRef,
  inject,
  signal,
} from '@angular/core';
import { combineLatest, debounceTime, finalize } from 'rxjs';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NzPopoverModule } from 'ng-zorro-antd/popover';

import { SwitchMap } from '@common/decorator';
import { getMonthFirstAndLastDay } from '@common/class';
import { LegendControlService, LegendItemClickHandler } from '@common/service';
import { isEmpty, isNotUndefined } from '@common/function';
import { QueryEngineFormService } from '@common/service/query-engine';
import { QueryEngineApiService } from '@api/query-engine';
import { BaseHighCharts } from '@common/chart/highcharts';
import { BusinessService } from '@views/monitor/business';
import { LineSpinComponent } from '@shared/components/line-spin';
import { Metric, ProgressTrend } from '@views/monitor/business/lib';
import { GraphComponent } from '@shared/components/graph';
import { RadioModule } from '@shared/modules/headless';
import { QueryOutputVo } from '@api/query-engine/model';

class MetricItem {
  id = null;
  aliasName = null;
  extendName: string;
  type = null;
  customType = null;
  proportionDimension = [];
  constructor(props?: Partial<MetricItem>) {
    if (props) {
      Object.assign(this, { ...props });
    }
  }
}

@Component({
  selector: 'app-order-finish-trend',
  template: `
    <h3 class="font-bold text-base">
      每日目标完成进度（业务口径）
      <span class="ml-1 text-xs text-neutral-400 font-normal"
        >说明：点击图例默认单选，长按shift键点击可多选。</span
      >
    </h3>
    <div class="shadow-md rounded-sm border border-neutral-100 mb-5">
      <div class="flex gap-x-5 items-center pt-5 px-5">
        <div class="flex items-center">
          <label
            class="inline-flex items-center font-bold leading-0 whitespace-nowrap"
            >目标选择：</label
          >
          <app-radio-group class="relative flex gap-1" [(ngModel)]="type">
            <app-radio
              class="tag-radio-new"
              activeClass="active"
              value="yesterday"
              >昨日目标</app-radio
            >
            <app-radio
              class="tag-radio-new"
              activeClass="active"
              value="accumulate"
              >本月MTD目标</app-radio
            >
            <app-radio-thumb class="rounded-xs bg-primary" />
          </app-radio-group>
        </div>

        <div class="flex items-center">
          <label
            class="inline-flex items-center font-bold leading-0 whitespace-nowrap"
            >指标选择：</label
          >
          <app-radio-group class="relative flex gap-1" [(ngModel)]="metricType">
            @for(item of metricsList(); track $index) {
            <app-radio
              class="tag-radio-new"
              activeClass="active"
              [value]="$index"
              >{{ item.showName }}</app-radio
            >
            }
            <app-radio-thumb class="rounded-xs bg-primary" />
          </app-radio-group>
        </div>

        <div>
          <label
            class="inline-flex items-center font-bold leading-0 whitespace-nowrap"
            >渠道选择：</label
          >
          @for (item of metrics(); track $index) {
          <label
            nz-checkbox
            class="ml-0! text-xs!"
            [(ngModel)]="item.checked"
            (ngModelChange)="onMetricsChange()"
            >{{ item.showName }}</label
          >
          }
        </div>
      </div>

      <div class="relative flex items-center justify-center h-100 text-xs">
        @if (loading()) {
        <app-line-spin />
        } @else { @if (option()) {
        <app-graph
          class="absolute inset-5"
          [options]="option()"
          showAnnotations
          showAvgPlotLines
        />
        } @else if (errorMessage()) {
        <span class="text-slate-600 text-xs">{{ errorMessage() }}</span>
        } }
      </div>
    </div>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    FormsModule,
    RadioModule,
    NzCheckboxModule,
    NzPopoverModule,
    LineSpinComponent,
    GraphComponent,
  ],
  providers: [LegendControlService, QueryEngineFormService],
})
export class OrderFinishTrendComponent implements AfterViewInit {
  readonly destroyRef = inject(DestroyRef);
  readonly legendControlService = inject(LegendControlService);
  readonly apiService = inject(QueryEngineApiService);
  readonly formService = inject(QueryEngineFormService);
  readonly service = inject(BusinessService);

  loading = signal(false);
  errorMessage = signal<string>(null);
  type = signal('accumulate');
  type$ = toObservable(this.type);
  metricType = signal(2);
  metricType$ = toObservable(this.metricType);
  channel = signal(0);
  option = signal<BaseHighCharts>(null);

  metricsList = computed(() => {
    const { book, reply, finish } =
      this.service?.config()?.sitePkg?.finOrdRateTrend || {};

    if (isNotUndefined(book, reply, finish)) {
      return [book, reply, finish];
    }

    return [];
  });

  channelList = computed(() => {
    const { app, wx, own } = this.metricsList()[this.metricType()] || {};

    if (isNotUndefined(app, wx, own)) {
      return [app, wx, own];
    }

    return [];
  });

  channelList$ = toObservable(this.channelList);
  metrics = signal<
    Array<{ showName: string; metrics: Metric[]; checked?: boolean }>
  >([]);
  metrics$ = toObservable(this.metrics);

  ngAfterViewInit(): void {
    this._subscribeToChannelListChange();
    this._subscribeToChange();
  }

  private _subscribeToChannelListChange() {
    combineLatest([this.channelList$, this.type$])
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(([list]) => {
        const metrics = list.map((item, index) => {
          return {
            showName: item.showName,
            metrics: item[this.type()],
            checked: isEmpty(this.metrics())
              ? index === 1
              : this.metrics()[index].checked,
          };
        });
        this.metrics.set(metrics);
      });
  }

  private _subscribeToChange() {
    combineLatest([this.service.deadline$, this.metricType$, this.type$])
      .pipe(debounceTime(300), takeUntilDestroyed(this.destroyRef))
      .subscribe(() => {
        this.query();
      });
  }

  @SwitchMap()
  query() {
    const body = this.formService.value();
    const deadline = this.service.deadline();
    const [startTime, endTime] = getMonthFirstAndLastDay(deadline);
    const selectedMetrics = this.metrics().filter((item) => item.checked);

    body.dt = { startTime, endTime };
    body.metrics = selectedMetrics
      .map((item) => {
        return item.metrics
          .filter((item) => {
            if (selectedMetrics.length > 1) {
              return item.isRate;
            }
            return true;
          })
          .map((child) => {
            return new MetricItem({ extendName: child.extendName });
          });
      })
      .flat(1);

    body.scene = 1;
    body.dataFillConfig = {
      open: 1,
      fillRangeType: 1,
      fillValue: 1,
    };

    this.loading.set(true);
    this.option.set(null);

    return this.apiService
      .search(body, 'order-finish-trend')
      .pipe(finalize(() => this.loading.set(false)))
      .subscribe((res) => {
        if (res.status !== '00000') {
          this.errorMessage.set(res.error || res.message);
        }

        if (res.data) {
          this.legendControlService.reset();
          this._setChartData(res.data);
        }
      });
  }

  private _setChartData(data: QueryOutputVo) {
    try {
      const chart = new ProgressTrend(data);
      const legendItemClick = LegendItemClickHandler(this.legendControlService);
      chart.plotOptions.series.events = { legendItemClick };
      this.option.set(chart?.getOption() || null);
    } catch (e) {
      console.error(e);
    }
  }

  onMetricsChange() {
    this.query();
  }
}
