import { FormsModule } from '@angular/forms';
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  computed,
  DestroyRef,
  inject,
  signal,
} from '@angular/core';
import { combineLatest, debounceTime, finalize } from 'rxjs';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';

import { SwitchMap } from '@common/decorator';
import { getMonthFirstAndLastDay } from '@common/class';
import { LegendControlService, LegendItemClickHandler } from '@common/service';
import { isNotEmpty, isNotUndefined } from '@common/function';
import { QueryEngineFormService } from '@common/service/query-engine';
import { QueryEngineApiService } from '@api/query-engine';
import { BaseHighCharts } from '@common/chart/highcharts';
import { BusinessService } from '@views/monitor/business';
import { IncomeTrend } from '@views/monitor/business/lib/IncomeTrend';
import { LineSpinComponent } from '@shared/components/line-spin';
import { GraphComponent } from '@shared/components/graph';
import { RadioModule } from '@shared/modules/headless';
import { QueryOutputVo } from '@api/query-engine/model';

@Component({
  selector: 'app-order-structure',
  template: `
    <h2 class="font-bold text-lg">
      订单结构（业务口径）
      <span class="ml-1 text-xs text-neutral-400 font-normal"
        >说明：点击图例默认单选，长按shift键点击可多选。</span
      >
    </h2>
    <div class="shadow-md rounded-sm border border-neutral-100">
      <div class="flex gap-x-5 items-center pt-5 px-5">
        <div class="flex items-center">
          <label
            class="inline-flex items-center font-bold leading-0 whitespace-nowrap"
            >目标选择：</label
          >
          <app-radio-group class="relative flex gap-1" [(ngModel)]="type">
            <app-radio
              class="tag-radio-new"
              activeClass="active"
              value="yesterday"
              >昨日占比</app-radio
            >
            <app-radio
              class="tag-radio-new"
              activeClass="active"
              value="accumulate"
              >本月累计占比</app-radio
            >
            <app-radio-thumb class="rounded-xs bg-primary" />
          </app-radio-group>
        </div>

        <div class="flex items-center">
          <label
            class="inline-flex items-center font-bold leading-0 whitespace-nowrap"
            >指标选择：</label
          >
          <app-radio-group class="relative flex gap-1" [(ngModel)]="metricType">
            @for(item of metricsList(); track $index) {
            <app-radio
              class="tag-radio-new"
              activeClass="active"
              [value]="$index"
              >{{ item.showName }}</app-radio
            >
            }
            <app-radio-thumb class="rounded-xs bg-primary" />
          </app-radio-group>
        </div>
      </div>

      <div class="relative flex items-center justify-center h-125 text-xs">
        @if (loading()) {
        <app-line-spin />
        } @else { @if (option()) {
        <app-graph
          class="absolute inset-5"
          [options]="option()"
          showAnnotations
          showAvgPlotLines
        />
        } @else if (errorMessage()) {
        <span class="text-slate-600 text-xs">{{ errorMessage() }}</span>
        } }
      </div>
    </div>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [FormsModule, RadioModule, LineSpinComponent, GraphComponent],
  providers: [LegendControlService, QueryEngineFormService],
})
export class OrderStructureComponent implements AfterViewInit {
  readonly destroyRef = inject(DestroyRef);
  readonly legendControlService = inject(LegendControlService);
  readonly apiService = inject(QueryEngineApiService);
  readonly formService = inject(QueryEngineFormService);
  readonly service = inject(BusinessService);

  loading = signal(false);
  errorMessage = signal<string>(null);
  type = signal('accumulate');
  metricType = signal(2);
  option = signal<BaseHighCharts>(null);

  metricsList = computed(() => {
    const { book, reply, finish } =
      this.service?.config()?.sitePkg?.orderStructure || {};

    if (isNotUndefined(book, reply, finish)) {
      return [book, reply, finish];
    }
    return [];
  });

  metrics = computed(() => {
    if (isNotEmpty(this.metricsList())) {
      return this.metricsList()[this.metricType()][this.type()];
    }
    return [];
  });

  type$ = toObservable(this.type);
  metrics$ = toObservable(this.metrics);

  ngAfterViewInit(): void {
    this.formService.addDateCompare();

    combineLatest([this.service.deadline$, this.metrics$, this.type$])
      .pipe(debounceTime(300), takeUntilDestroyed(this.destroyRef))
      .subscribe(([deadline, metrics]) => {
        const [startTime, endTime] = getMonthFirstAndLastDay(deadline);

        this.formService.setMetrics(metrics);
        this.formService.dt.patchValue({ startTime, endTime });
        this.formService.compareDt.patchValue({
          startTime: 'now/1M',
          endTime: 'now/1M',
        });
        this.query();
      });
  }

  @SwitchMap()
  query() {
    const body = this.formService.value();

    body.dataFillConfig = {
      open: 1,
      fillRangeType: 2,
      fillValue: 1,
    };

    this.loading.set(true);
    this.option.set(null);

    return this.apiService
      .search(body, 'order-structure')
      .pipe(finalize(() => this.loading.set(false)))
      .subscribe((res) => {
        if (res.status !== '00000') {
          this.errorMessage.set(res.error || res.message);
        }

        if (res.data) {
          this.legendControlService.reset();
          this._setChartData(res.data);
        }
      });
  }

  private _setChartData(data: QueryOutputVo) {
    try {
      const chart = new IncomeTrend(data);
      const legendItemClick = LegendItemClickHandler(this.legendControlService);
      chart.plotOptions.series.events = { legendItemClick };
      this.option.set(chart?.getOption() || null);
    } catch (e) {
      console.error(e);
    }
  }
}
