import { ChangeDetectionStrategy, Component } from '@angular/core';
import { TargetMetricsComponent } from './target-metrics/target-metrics.component';
import { MonthProgressComponent } from './month-progress/month-progress.component';
import { OrderFinishTrendComponent } from './order-finish-trend/order-finish-trend.component';
import { OrderStructureComponent } from './order-structure/order-structure.component';

@Component({
  selector: 'app-monitor-site',
  template: `
    <app-target-metrics />
    <app-order-finish-trend />
    <app-month-progress />
    <app-order-structure id="anchor-trend" />
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    TargetMetricsComponent,
    MonthProgressComponent,
    OrderFinishTrendComponent,
    OrderStructureComponent,
  ],
})
export class MonitorSiteComponent {}
