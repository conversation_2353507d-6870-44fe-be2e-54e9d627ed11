import { ChangeDetectionStrategy, Component, computed, DestroyRef, inject, signal } from '@angular/core';
import { toObservable, takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { combineLatest, filter, finalize } from 'rxjs';

import { isNotNull } from '@common/function';
import { QueryEngineFormService } from '@common/service/query-engine';
import { BaseHighCharts } from '@common/chart/highcharts';
import { SwitchMap } from '@common/decorator';
import { QueryOutputVo } from '@api/query-engine/model';
import { QueryEngineApiService } from '@api/query-engine';
import { GraphComponent } from '@shared/components/graph';
import { LineSpinComponent } from '@shared/components/line-spin';
import { BusinessService } from '@views/monitor/business';
import { BarChart } from '@views/monitor/business/lib';
import { sortBy } from '@common/chart';


@Component({
  selector: 'app-reply-target',
  template: `
    <div class="shadow-md rounded-sm border border-neutral-100">
      <h4 class="font-bold text-center pt-3">接单目标</h4>
      <div class="relative flex items-center justify-center min-h-96 text-xs">
        @if (loading()) {
          <app-line-spin />
        } @else {
          @if (option()) {
            <app-graph class="absolute inset-5" [options]="option()" />
          } @else if (errorMessage()) {
            <span class="text-slate-600 text-xs">{{errorMessage()}}</span>
          }
        }
      </div>
    </div>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    LineSpinComponent,
    GraphComponent
  ],
  providers: [
    QueryEngineFormService,
  ],
})
export class ReplyTargetComponent {

  readonly destroyRef = inject(DestroyRef);
  readonly apiService = inject(QueryEngineApiService);
  readonly formService = inject(QueryEngineFormService);
  readonly service = inject(BusinessService);
  
  loading = signal(true);
  errorMessage = signal<string>(null);
  option = signal<BaseHighCharts>(null);

  metrics = computed(() => {
    return this.service?.config()?.sitePkg?.progress?.replyProgress || [];
  })

  metrics$ = toObservable(this.metrics);

  ngAfterViewInit(): void {
    this._subscribeToDeadlineChange();
  }


  private _subscribeToDeadlineChange() {
    combineLatest([
      this.service.deadline$,
      this.metrics$,
    ]).pipe(
      filter(isNotNull),
      takeUntilDestroyed(this.destroyRef)
    ).subscribe(([deadline, metrics]) => {
      if (deadline && metrics) {
        this.formService.setMetrics(metrics);
        this.formService.dt.patchValue({ startTime: deadline, endTime: deadline });
        this.query();
      }
    })
  }


  @SwitchMap()
  query() {
    const body = this.formService.value();
    
    body.dimensions = [];
    body.scene = 1;
    body.dataFillConfig = {
      open: 1,
      fillRangeType: 2,
      fillValue: 1
    }

    this.loading.set(true);
    this.option.set(null);
    return this.apiService.search(body, 'reply-target').pipe(
      finalize(() => this.loading.set(false))
    ).subscribe(res => {
      if (res.status !== '00000') {
        this.errorMessage.set(res.error || res.message);
      }

      if (res.data) {
        this._setChartData(res.data, body.dt.endTime);
      }
    })
  }


  private _setChartData(data: QueryOutputVo, deadline: string) {
    try {
      const order = this.metrics().map(item => item.extendName);
      const chart = new BarChart(
        data, 
        deadline, 
        sortBy(order, 'extendName'), 
        sortBy(order, null)
      );
      
      this.option.set(chart?.getOption() || null);
    } catch (e) {
      console.error(e);
    }
  }

}
