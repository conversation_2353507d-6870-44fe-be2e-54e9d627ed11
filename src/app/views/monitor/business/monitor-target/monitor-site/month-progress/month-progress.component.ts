import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { TargetComponent } from '../../target/target.component';
import { BusinessService } from '@views/monitor/business';

@Component({
  selector: 'app-month-progress',
  template: `
    <div class="mb-5">
      <h3 class="font-bold text-base">月度目标完成进度（业务口径）</h3>
      <div class="grid grid-cols-3 gap-x-5">
        <app-target title="下单目标" [metrics]="this.service?.config()?.sitePkg?.progress?.bookProgress" />
        <app-target title="接单目标" [metrics]="this.service?.config()?.sitePkg?.progress?.replyProgress" />
        <app-target title="完单目标" [metrics]="this.service?.config()?.sitePkg?.progress?.finishProgress" />
      </div>
    </div>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    TargetComponent,
  ],
})
export class MonthProgressComponent {

  readonly service = inject(BusinessService);
  
}
