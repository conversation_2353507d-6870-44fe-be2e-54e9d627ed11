import { takeUntilDestroyed } from '@angular/core/rxjs-interop'
import { NavigationEnd, Router, RouterLink, RouterOutlet } from '@angular/router'
import { ChangeDetectionStrategy, Component, DestroyRef, ElementRef, inject, signal, viewChild } from '@angular/core'
import { CdkScrollableModule } from '@angular/cdk/scrolling'
import { NzLayoutModule } from 'ng-zorro-antd/layout'
import { NzMenuModule } from 'ng-zorro-antd/menu'
import { NzToolTipModule } from 'ng-zorro-antd/tooltip'
import { NzIconModule } from 'ng-zorro-antd/icon'
import { filter } from 'rxjs'

import { NavigationComponent } from '@shared/components/navigation'
import { BusinessService } from './business.service'

@Component({
  selector: 'app-business',
  templateUrl: './business.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    RouterOutlet,
    RouterLink,
    CdkScrollableModule,
    NzLayoutModule,
    NzToolTipModule,
    NzMenuModule,
    NzIconModule,
    NavigationComponent,
  ],
  providers: [BusinessService],
})
export class BusinessComponent {
  readonly router = inject(Router)
  readonly destroyRef = inject(DestroyRef)

  layoutRef = viewChild('layout', { read: ElementRef })
  isCollapsed = signal(false)

  constructor() {
    this.router.events
      .pipe(
        filter(event => event instanceof NavigationEnd),
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe((event: NavigationEnd) => {
        if (this.layoutRef()) {
          this.layoutRef().nativeElement.scrollTo({ top: 0 })
        }
      })
  }

  toggle() {
    this.isCollapsed.update(state => !state)
  }
}
