<app-navigation />

<nz-layout>
  <nz-sider
    nzCollapsible
    [(nzCollapsed)]="isCollapsed"
    [nzTrigger]="null"
    nzTheme="light"
    [nzCollapsedWidth]="50"
    [nzWidth]="140"
  >
    <ul nz-menu nzTheme="light" nzMode="inline" class="h-full">
      <div
        (click)="toggle()"
        class="py-2 text-center cursor-pointer hover:bg-neutral-50 transition-colors border-r border-[#f0f0f0]"
      >
        <span nz-icon [nzType]="isCollapsed() ? 'menu-unfold' : 'menu-fold'"></span>
      </div>
      <li
        [nz-tooltip]="isCollapsed() ? '盯盘总览' : ''"
        nzTooltipPlacement="right"
        nz-menu-item
        nzMatchRouter
        nzMatchRouterExact
        routerLink="./"
      >
        <span nz-icon nzType="appstore" nzTheme="outline"></span>
        <span>盯盘总览</span>
      </li>
      <li
        [nz-tooltip]="isCollapsed() ? '端外推广' : ''"
        nzTooltipPlacement="right"
        nz-menu-item
        nzMatchRouter
        nzMatchRouterExact
        routerLink="./offplt"
      >
        <span nz-icon nzType="fork" nzTheme="outline"></span>
        <span>端外推广</span>
      </li>
    </ul>
  </nz-sider>
  <nz-layout #layout cdkScrollable class="h-[calc(100vh_-_64px)] overflow-auto">
    <router-outlet />
  </nz-layout>
</nz-layout>
