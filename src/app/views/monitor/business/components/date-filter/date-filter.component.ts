import { DatePipe } from '@angular/common'
import { ReactiveFormsModule } from '@angular/forms'
import {
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
  inject,
  input,
  OnChanges,
  signal,
  SimpleChanges,
} from '@angular/core'
import { takeUntilDestroyed } from '@angular/core/rxjs-interop'
import { NzButtonModule } from 'ng-zorro-antd/button'
import { NzSelectModule } from 'ng-zorro-antd/select'
import { startWith } from 'rxjs'

import { getMonthFirstAndLastDay } from '@common/class'
import { DtType, QueryEngineFormService } from '@common/service/query-engine'
import { BusinessService } from '../../business.service'
import { DatePicker } from './date-picker'
import { WeekPicker } from './week-picker'
import { MonthPicker } from './month-picker'

@Component({
  selector: 'app-date-filter',
  template: `
    <form [formGroup]="formService.form">
      <div class="flex items-center gap-x-1 px-1">
        <label class="inline-flex text-xs items-center justify-end min-w-16 font-bold leading-5 whitespace-nowrap">
          {{ label() }}：
        </label>
        @if (showDtType()) {
          <nz-select formControlName="dtType" class="w-24">
            @for (item of unitOptions(); track $index) {
              <nz-option [nzLabel]="item.label" [nzValue]="item.value"></nz-option>
            }
          </nz-select>
        }

        <ng-container formGroupName="dt">
          @switch (dtType()) {
            @case ('dt') {
              <date-picker />
            }
            @case ('yw') {
              <week-picker />
            }
            @case ('ym') {
              <month-picker />
            }
          }
        </ng-container>
      </div>
    </form>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [ReactiveFormsModule, NzButtonModule, NzSelectModule, DatePicker, WeekPicker, MonthPicker],
  providers: [DatePipe],
})
export class DateFilterComponent implements OnChanges {
  label = input('激活日期')
  type = input('dt')
  showDtType = input(true)
  readonly destroyRef = inject(DestroyRef)
  readonly formService = inject(QueryEngineFormService)
  readonly datePipe = inject(DatePipe)
  readonly service = inject(BusinessService)

  unitOptions = signal([
    { label: '按单日', value: 'dt' },
    { label: '按周', value: 'yw' },
    { label: '按月', value: 'ym' },
  ])

  dtType = signal<DtType>(null)

  ngOnChanges(changes: SimpleChanges): void {
    if (this.type() === 'ym') {
      this.formService.dtType.patchValue('ym')
      const month = this.datePipe.transform(new Date(), 'yyyy-MM')
      this.formService.dt.patchValue({
        startTime: month,
        endTime: month,
      })
    }
  }

  ngAfterViewInit(): void {
    const dt = this.formService.form.controls.dt
    const dtType = this.formService.form.controls.dtType

    dtType.valueChanges.pipe(startWith(dtType.value), takeUntilDestroyed(this.destroyRef)).subscribe(type => {
      this.dtType.set(type)
    })

    this.service.deadline$.subscribe(res => {
      if (this.type() === 'ym') {
        if (this.dtType() === 'dt') {
          const [startTime] = getMonthFirstAndLastDay(res)
          dt.reset({ startTime, endTime: res })
        }
        if (this.dtType() === 'ym') {
          const month = this.datePipe.transform(res, 'yyyy-MM')
          dt.reset({ startTime: month, endTime: month })
        }
        if (this.dtType() === 'yw') {
          dt.reset({ startTime: 'week-4w', endTime: 'week-0w' })
        }
      } else {
        const [startTime] = getMonthFirstAndLastDay(res)
        dt.reset({ startTime, endTime: res })
      }
    })

    dtType.valueChanges.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(type => {
      switch (type) {
        case 'dt':
          dt.reset({ startTime: 'now-14d', endTime: 'now-1d' })
          break
        case 'yw':
          dt.reset({ startTime: 'week-4w', endTime: 'week-0w' })
          break
        case 'ym':
          dt.reset({ startTime: 'month-y1', endTime: 'month' })
          break
      }
    })
  }
}
