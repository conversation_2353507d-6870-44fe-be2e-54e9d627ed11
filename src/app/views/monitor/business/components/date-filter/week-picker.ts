import { DatePipe } from '@angular/common';
import {
  AfterViewInit,
  booleanAttribute,
  ChangeDetectionStrategy,
  Component,
  computed,
  DestroyRef,
  effect,
  inject,
  input,
  signal,
} from '@angular/core';
import { ControlContainer, FormGroup, FormsModule } from '@angular/forms';
import { startWith, take } from 'rxjs';

import { CompareTime, Time } from '@common/class';
import { QueryDt } from '@common/service/query-engine';
import { RangePicker } from './range-picker';

@Component({
  selector: 'week-picker',
  template: `
    <range-picker
      class="block min-w-56 h-8"
      [(ngModel)]="value"
      [rangeMap]="rangeMap()"
      mode="week"
    />
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [FormsModule, RangePicker],
  providers: [DatePipe],
})
export class WeekPicker implements AfterViewInit {
  readonly datePipe = inject(DatePipe);
  readonly parentContainer = inject(ControlContainer);
  readonly destroyRef = inject(DestroyRef);

  rangeMap = computed(() => {
    return this.baseRangeMap();
  });

  baseRangeMap = () => {
    const result = new Map();

    result.set('近4周', new Time('week-4w', 'week-0w').toString());
    result.set('近8周', new Time('week-8w', 'week-0w').toString());
    result.set('近12周', new Time('week-12w', 'week-0w').toString());
    result.set('近52周', new Time('week-52w', 'week-0w').toString());
    result.set('近104周', new Time('week-104w', 'week-0w').toString());
    result.set('本月', new Time('week-M1', 'week-0w').toString());
    result.set('今年', new Time('week-y1', 'week-0w').toString());
    result.set('近2年', new Time('week-y2', 'week-0w').toString());

    return result;
  };

  value = signal<string>(null);

  constructor() {
    effect(() => {
      if (this.value()) {
        const [startTime, endTime] = this.value().split(',');

        this.form.controls.startTime.patchValue(startTime);
        this.form.controls.endTime.patchValue(endTime);
      }
    });
  }

  ngAfterViewInit(): void {
    this.form.valueChanges
      .pipe(
        startWith(this.form.getRawValue())
        // take(1)
      )
      .subscribe((value) => {
        if (value) {
          const { startTime, endTime } = value;

          if (startTime && endTime) {
            this.value.set(`${startTime},${endTime}`);
          }
        }
      });
  }

  get form() {
    return this.parentContainer.control as FormGroup<QueryDt>;
  }
}
