import { DatePipe } from '@angular/common';
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  computed,
  DestroyRef,
  effect,
  inject,
  signal,
} from '@angular/core';
import { ControlContainer, FormGroup, FormsModule } from '@angular/forms';
import { startWith } from 'rxjs';

import { Time } from '@common/class';
import { QueryDt } from '@common/service/query-engine';
import { RangePicker } from './range-picker';

@Component({
  selector: 'date-picker',
  template: `
    <range-picker
      mode="date"
      class="block min-w-56 h-8"
      [(ngModel)]="value"
      [rangeMap]="rangeMap()"
    />
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [FormsModule, RangePicker],
  providers: [DatePipe],
})
export class DatePicker implements AfterViewInit {
  readonly datePipe = inject(DatePipe);
  readonly parentContainer = inject(ControlContainer);
  readonly destroyRef = inject(DestroyRef);

  rangeMap = computed(() => {
    return this.baseRangeMap();
  });

  baseRangeMap = () => {
    const result = new Map();

    result.set('近7天', new Time('now-7d', 'now-1d').toString());
    result.set('近14天', new Time('now-14d', 'now-1d').toString());
    result.set('近30天', new Time('now-30d', 'now-1d').toString());
    result.set('近60天', new Time('now-60d', 'now-1d').toString());
    result.set('近90天', new Time('now-90d', 'now-1d').toString());
    result.set('近180天', new Time('now-180d', 'now-1d').toString());
    result.set('近365天', new Time('now-365d', 'now-1d').toString());
    result.set('近730天', new Time('now-730d', 'now-1d').toString());
    result.set('本月', new Time('now/M', 'now-1d').toString());
    result.set('上月', new Time('now-2M', 'now-1M').toString());
    result.set('今年', new Time('now/y', 'now-1d').toString());
    result.set('近2年', new Time('now-2y', 'now-1d').toString());

    return result;
  };

  value = signal<string>(null);

  constructor() {
    effect(() => {
      if (this.value()) {
        const [startTime, endTime] = this.value().split(',');

        this.form.controls.startTime.patchValue(startTime);
        this.form.controls.endTime.patchValue(endTime);
      }
    });
  }

  ngAfterViewInit(): void {
    this.form.valueChanges
      .pipe(
        startWith(this.form.getRawValue())
        // take(1)
      )
      .subscribe((value) => {
        if (value) {
          const { startTime, endTime } = value;

          if (startTime && endTime) {
            this.value.set(`${startTime},${endTime}`);
          }
        }
      });
  }

  get form() {
    return this.parentContainer.control as FormGroup<QueryDt>;
  }
}
