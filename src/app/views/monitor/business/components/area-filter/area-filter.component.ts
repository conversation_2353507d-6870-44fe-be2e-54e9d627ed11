import { FormsModule } from '@angular/forms';
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  computed,
  effect,
  inject,
  input,
  signal,
} from '@angular/core';

import { RadioModule } from '@shared/modules/headless';
import { DimensionValueMenuVo } from '@api/caerus/model';
import { MoreAreaSelectComponent } from './more-area-select/more-area-select.component';
import { CaerusApiService } from '@api/caerus';
import { BusinessService } from '../../business.service';

@Component({
  selector: 'app-area-filter',
  template: `
    @if (data()) {
    <app-radio-group class="relative flex gap-1" [(ngModel)]="value">
      <div class="flex gap-1 flex-wrap items-start">
        <app-radio class="tag-radio" activeClass="active" [value]="null"
          >全部</app-radio
        >
        @for (item of defaultShowCitys(); track $index) {
        <app-radio class="tag-radio" activeClass="active" [value]="item">{{
          item.showValue
        }}</app-radio>
        }

        <app-more-area-select [options]="moreCitys()" [(area)]="value" />
      </div>
    </app-radio-group>
    }
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [FormsModule, RadioModule, MoreAreaSelectComponent],
})
export class AreaFilterComponent implements AfterViewInit {
  readonly apiService = inject(CaerusApiService);
  readonly service = inject(BusinessService);

  data = signal<DimensionValueMenuVo[]>(null);
  value = signal<DimensionValueMenuVo>(null);

  defaultShowCitys = computed(() => {
    if (this.data()) {
      return this.data().filter((item) => item.defaultShow === 1);
    }
    return [];
  });

  moreCitys = computed(() => {
    if (this.data()) {
      return this.data()
        .filter((item) => item.defaultShow === 0)
        .sort((a, b) => a.showOrder - b.showOrder);
    }
    return [];
  });

  constructor() {
    effect(() => {
      if (this.data()) {
        const value = this.service.area()?.value;
        const city = this.data().find((item) => {
          if (this.service.customArea()) {
            return item.value === this.service.customArea();
          }
          return item.value === value;
        });

        this.value.set(city ?? null);
      }
    });

    effect(() => {
      this.service.area.set(this.value());
      this.service.customArea.set(null);
    });
  }

  ngAfterViewInit(): void {
    this.fetchHomeCityData();
  }

  private fetchHomeCityData() {
    this.apiService.fetchHomeCityData().subscribe((res) => {
      if (!res?.data) {
        return;
      }
      this.data.set(res.data);
    });
  }
}
