import { DecimalPipe, NgTemplateOutlet } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  effect,
  input,
} from '@angular/core';
import { IndicatorProgressComponent } from '@shared/components/indicator';
import { CardList } from '../../lib/BusinessConfig';

@Component({
  selector: 'app-card',
  template: `
    <div class="flex flex-col gap-y-1 h-full text-center text-neutral-700">
      <h2
        class="flex items-center justify-center gap-x-1 font-bold text-base text-inherit"
      >
        {{ data()?.showName }}
        @if(subName()){
        <span class="text-xs font-normal">{{ data()?.subName }}</span>
        }
      </h2>

      @if (data()?.showDayBeforeYesterday) {
      <div class="flex-1 flex items-center h-15">
        <div class="flex-1 min-w-0">
          <p>
            前日：<span class="font-bold text-lg">{{
              data()?.beforeYesterMetric?.value | number
            }}</span>
          </p>
          <p class="text-xs text-neutral-400">
            前日目标<ng-template
              *ngTemplateOutlet="isPredictionTemplate"
            ></ng-template
            >：{{ data()?.beforeYesterTarget?.value | number }}
          </p>
        </div>
        <div
          class="flex items-center text-xs px-2.5 max-h-15 overflow-hidden"
        >
          完成度：
          <app-indicator-progress
            circle
            [fractionDigits]="1"
            svgTextSize="14px"
            [value]="+data()?.beforeYesterMetric?.value"
            [total]="+data()?.beforeYesterTarget?.value"
          />
        </div>
      </div>
      }

      <div class="flex-1 flex items-center h-15">
        <div class="flex-1 min-w-0">
          <p>
            昨日：<span class="font-bold text-lg">{{
              data()?.yesterdayMetric?.value | number
            }}</span>
          </p>
          <p class="text-xs text-neutral-400">
            昨日目标<ng-template
              *ngTemplateOutlet="isPredictionTemplate"
            ></ng-template
            >：{{ data()?.yesterdayTarget?.value | number }}
          </p>
        </div>
        <div
          class="flex items-center text-xs px-2.5 max-h-15 overflow-hidden"
        >
          完成度：
          <app-indicator-progress
            circle
            [fractionDigits]="1"
            svgTextSize="14px"
            [value]="+data()?.yesterdayMetric?.value"
            [total]="+data()?.yesterdayTarget?.value"
          />
        </div>
      </div>

      <div class="flex-1 flex items-center h-15">
        <div class="flex-1 min-w-0">
          <p>
            本月累计：<span class="font-bold text-lg">{{
              data()?.accumulateMetric?.value | number
            }}</span>
          </p>
          <p class="text-xs text-neutral-400">
            本月MTD目标<ng-template
              *ngTemplateOutlet="isPredictionTemplate"
            ></ng-template
            >：{{ data()?.accumulateTarget?.value | number }}
          </p>
        </div>
        <div
          class="flex items-center text-xs px-2.5 max-h-15 overflow-hidden"
        >
          MTD完成度：
          <app-indicator-progress
            circle
            [fractionDigits]="1"
            svgTextSize="14px"
            [value]="+data()?.accumulateMetric?.value"
            [total]="+data()?.accumulateTarget?.value"
          />
        </div>
      </div>
    </div>

    <ng-template #isPredictionTemplate>
      @if (data()?.prediction) {
      <span class="text-red-500">(预测)</span>
      }
    </ng-template>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: {
    class: `flex items-start justify-center w-full h-full rounded-lg px-3.5 py-3`,
  },
  imports: [DecimalPipe, NgTemplateOutlet, IndicatorProgressComponent],
})
export class CardComponent {
  label = input<string>();
  data = input<CardList>();
  subName = input<boolean>(true);

  constructor() {
    effect(() => {
      if (this.data().showName === '自有完单量') {
        console.log(this.data());
      }
    });
  }
}
