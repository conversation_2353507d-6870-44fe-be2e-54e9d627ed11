<button
  nz-button
  nz-dropdown
  [nzDropdownMenu]="menu"
  nzTrigger="click"
  [(nzVisible)]="visible"
>
  选择展示字段
</button>
<nz-dropdown-menu #menu="nzDropdownMenu">
  <div
    class="flex flex-col gap-1 py-2 px-3 bg-white w-[260px]"
    style="border: solid 1px #ccc"
  >
    <div class="flex items-center gap-2">
      <button nz-button nzType="primary" nzSize="small" (click)="save()">
        确定
      </button>
      <nz-input-group [nzSuffix]="inputClearTpl">
        <input
          type="text"
          nz-input
          placeholder="输入关键字"
          [(ngModel)]="searchVal"
        />
      </nz-input-group>
      <ng-template #inputClearTpl>
        @if (searchVal()) {
        <span
          nz-icon
          class="ant-input-clear-icon"
          nzTheme="fill"
          nzType="close-circle"
          (click)="clear()"
        ></span>
        }
      </ng-template>
    </div>
    <div class="px-6 pt-1">
      <div>
        <label
          nz-checkbox
          [ngModel]="allChecked()"
          (ngModelChange)="updateAllChecked($event)"
        >
          全部
        </label>
      </div>

      <nz-checkbox-group
        [(ngModel)]="value"
        (ngModelChange)="updateSingleChecked()"
      >
        <nz-row>
          @for(l of filterList();track $index){
          <nz-col nzSpan="24">
            <label nz-checkbox [nzValue]="l.value">{{ l.label }}</label>
          </nz-col>
          }
        </nz-row>
      </nz-checkbox-group>
    </div>
  </div>
</nz-dropdown-menu>
