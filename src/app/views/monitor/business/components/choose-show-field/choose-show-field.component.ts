import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  computed,
  effect,
  input,
  OnChanges,
  output,
  signal,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { NzGridModule } from 'ng-zorro-antd/grid';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { NzInputModule } from 'ng-zorro-antd/input';

@Component({
  selector: 'app-choose-show-field',
  imports: [
    NzDropDownModule,
    FormsModule,
    NzButtonModule,
    NzInputModule,
    NzIconModule,
    NzCheckboxModule,
    NzGridModule,
  ],
  templateUrl: './choose-show-field.component.html',
  styles: ``,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ChooseShowFieldComponent implements OnChanges {
  list = input<any[]>([]);
  listChange = output<any>();

  value = signal([]);
  allChecked = signal(false);
  searchVal = signal(null);
  visible = signal(false);

  ngOnChanges(): void {
    const showList = this.list().filter((l) => l.show);
    this.value.set(showList.map((l) => l.value));

    if (
      this.value().length === this.list().length &&
      this.list().length !== 0
    ) {
      this.allChecked.set(true);
    }
  }

  filterList = computed(() => {
    if (this.searchVal() === '' || !this.searchVal()) {
      return this.list();
    }
    return this.list().filter((l) => {
      return l.label.includes(this.searchVal());
    });
  });

  clear() {
    this.searchVal.set(null);
  }

  updateAllChecked(v): void {
    const arr = v ? this.list().map((item) => item.value) : [];
    this.value.set(arr);
  }

  updateSingleChecked() {
    this.allChecked.set(this.value().length === this.list().length);
  }

  save() {
    const arr = this.list().map((l) => {
      return {
        ...l,
        show: this.value().includes(l.value),
      };
    });
    this.visible.set(false);
    this.listChange.emit(arr);
  }
}
