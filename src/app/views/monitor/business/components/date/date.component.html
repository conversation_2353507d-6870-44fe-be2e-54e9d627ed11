<div class="sticky top-0 z-40 py-2 mb-1 bg-white shadow-lg">
  <div class="flex items-center px-10">
    <div
      class="max-w-(--breakpoint-2xl) flex-1 min-w-0 flex items-center gap-x-2 text-xs"
    >
      <div>
        <nz-date-picker
          [(ngModel)]="service.month"
          nzMode="month"
          [nzFormat]="'yyyy/MM'"
          [nzDisabledDate]="disabledMonth"
        />
        <span class="ml-4 whitespace-nowrap">
          日期截至：{{ service.deadline() }}
        </span>
      </div>
      @if(type() === 'offplt'){
      <div class="flex items-center pl-10">
        <label
          class="inline-flex items-center font-bold leading-0 whitespace-nowrap"
        >
          区域：
        </label>
        <app-area-filter />
      </div>
      <button nz-button nzType="default" (click)="reset()">重置</button>
      }
    </div>
  </div>
</div>
