import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
  inject,
  input,
} from '@angular/core';
import { BusinessService } from '../../business.service';
import { differenceInCalendarMonths, addMonths } from 'date-fns';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { FormsModule } from '@angular/forms';
import { filter } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { isNotNull } from '@common/function';
import { DatePipe } from '@angular/common';
import { QueryEngineApiService } from '@api/query-engine';
import { AreaFilterComponent } from '../area-filter';
import { NzButtonComponent } from 'ng-zorro-antd/button';
import { getMonthFirstAndLastDay } from '@common/class';

@Component({
  selector: 'app-date',
  imports: [
    NzDatePickerModule,
    FormsModule,
    AreaFilterComponent,
    NzButtonComponent,
  ],
  templateUrl: './date.component.html',
  styles: ``,
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [QueryEngineApiService, DatePipe],
})
export class DateComponent implements AfterViewInit {
  type = input('overview');
  readonly service = inject(BusinessService);
  readonly destroyRef = inject(DestroyRef);
  readonly datePipe = inject(DatePipe);
  readonly queryEngineApiService = inject(QueryEngineApiService);

  disabledMonth = (current: Date): boolean => {
    // Can not select days before today and today
    const startDateForMonth = new Date().setDate(1);
    const dateRight = addMonths(startDateForMonth, 1);

    return differenceInCalendarMonths(current, dateRight) > -1;
  };

  ngAfterViewInit(): void {
    this.subscribeToMonthChange();
  }

  private subscribeToMonthChange() {
    this.service.month$
      .pipe(filter(isNotNull), takeUntilDestroyed(this.destroyRef))
      .subscribe((value) => {
        const month = this.datePipe.transform(value, 'yyyy-MM');
        // console.log('month', month);
        if (this.type() === 'overview') {
          this.queryEngineApiService
            .fetchUpdateTimeModel2(month)
            .subscribe((res) => {
              if (res.status === '00000') {
                this.service.deadline.set(res.data.dt);
                this._updateMonth();
              }
            });
        } else {
          this.queryEngineApiService.fetchUpdateTime(month).subscribe((res) => {
            if (res.status === '00000') {
              this.service.deadline.set(res.data.dt);
              this._updateMonth();
            }
          });
        }
      });
  }


  private _isEqualDeadline() {
    const endDate = this.service.month().setDate(1);
    const deadDate = this.service.deadDate().setDate(1);

    return endDate > deadDate;
  }


  private _updateMonth() {
    const deadline = this.service.deadline();
    const value = new Date(deadline?.replace(/-/g, '/'));

    if (!deadline) {
      throw new Error('deadline is undefined');
    }

    if (this._isEqualDeadline() && deadline) {
      this.service.month.set(value);
    }
  }


  reset() {
    this.service.area.set(null);
  }

}
