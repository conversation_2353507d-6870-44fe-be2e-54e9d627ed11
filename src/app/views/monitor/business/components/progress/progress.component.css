@reference "../../../../../../styles.css";

.my-container.style_1 {
  @apply grid grid-cols-12 grid-flow-row gap-1;
}

.my-container.style_1 .label-wrapper {
  @apply col-span-2 col-start-2 text-xs/none;
}

.my-container.style_1 .progress-wrapper {
  @apply col-start-4 col-end-11 translate-y-0.5;
}

.my-container.style_1 .budget-wrapper {
  @apply col-start-11 text-xs leading-none;
}

.my-container.style_2 {
  @apply grid grid-cols-12 grid-flow-row gap-1;
}

.my-container.style_2 .label-wrapper {
  @apply col-span-6 col-start-2 text-xs;
}

.my-container.style_2 .progress-wrapper {
  @apply col-span-10 col-start-2 row-start-2;
}

.my-container.style_2 .budget-wrapper {
  @apply col-span-2 col-start-10 text-right;
}

.my-container.style_3 {
  @apply grid grid-cols-12 grid-flow-row gap-1;
}

.my-container.style_3 .label-wrapper {
  @apply col-span-3 col-start-2 text-xs/none mt-0.5 text-left;
}

.my-container.style_3 .progress-wrapper {
  @apply col-start-2 col-end-12 translate-y-0.5 row-start-2;
}

.my-container.style_3 .budget-wrapper {
  @apply col-span-2 col-start-10 text-right;
}
