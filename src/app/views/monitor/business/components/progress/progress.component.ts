import { DecimalPipe } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  computed,
  input,
} from '@angular/core';

@Component({
  selector: 'app-progress',
  template: `
    <div
      class="my-container"
      [class.style_1]="style() === 1"
      [class.style_2]="style() === 2"
      [class.style_3]="style() === 3"
    >
      <div class="label-wrapper">{{ label() }}:</div>
      <div class="progress-wrapper">
        <div class="bg-neutral-200 overflow-hidden h-2.5 mb-1 rounded-lg">
          <div
            class="bg-primary max-w-full h-full"
            [style.width.%]="progress()"
          ></div>
        </div>
        <div class="text-right text-xs text-neutral-400">
          <p class="scale-85 origin-right m-0 inline-block">
            {{ budgetLabel() }}:
            <span class="font-bold">{{ total() | number }}</span>
          </p>
        </div>
      </div>
      <div class="budget-wrapper font-bold">
        {{ progress() !== null ? progress() + '%' : null }}
      </div>
    </div>
  `,
  styleUrl: './progress.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [DecimalPipe],
})
export class ProgressComponent {
  value = input(0);
  total = input(0);
  fractionDigits = input(1);
  budgetLabel = input('本月预算');
  label = input('累计核销进度');
  style = input(1);

  progress = computed(() => {
    let value = 0;

    value =
      parseFloat(
        ((this.value() / this.total()) * 100).toFixed(this.fractionDigits())
      ) || 0;

    return Number.isFinite(value) ? value : null;
  });
}
