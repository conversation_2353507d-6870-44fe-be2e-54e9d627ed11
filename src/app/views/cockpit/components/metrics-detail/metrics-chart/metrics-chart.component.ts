import { ChangeDetectionStrategy, Component, booleanAttribute, computed, input } from '@angular/core';
import { groupBy, handleLegendItemClick } from '@common/function';
import { GraphComponent } from '@shared/components/graph';
import { BaseGraph } from '@shared/components/graph/graph-base';
import { TrendVo } from '@views/cockpit/models';


export class MiniAreaSeries {
  public name: string;
  public fillColor: string;
  public zIndex: number;
  public data: Array<number> = [];
  public fillOpacity = 0.2;
}


class Area extends BaseGraph<MiniAreaSeries> {

  // override legend = new Legend();

  chart = {
    // spacing: [0, 0, 0, 0],
    // margin: [0, 0, 24, 0],
    backgroundColor: 'rgba(0,0,0,0)',
    type: 'areaspline',
    zoomType: 'x',
  }

  plotOptions = {
    areaspline: {
      turboThreshold: 999999999,
      marker: {
        enabled: true,
        radius: 2,
        fillColor: '#FFFFFF',
        lineWidth: 1,
        lineColor: null,
      }
    }
  };


  xAxis = {
    categories: [],
    title: {
      enabled: false,
      text: "",
      margin: 30,
      style: { fontSize: "12px", color: "#24273E" }
    },
    scrollbar: { enabled: false },
    gridLineWidth: 0,
    gridLineDashStyle: "Solid",
    gridLineColor: "#e6e6e6",
    lineColor: "#ccd6eb",
    lineWidth: 1,
    tickColor: "#ccd6eb",
    tickWidth: 1,
    crosshair: true,
    // index: 0,
    // isX: true,
    labels: {
      enabled: true
    }
  };


  yAxis = [
    {
      title: { enabled: false },
      gridLineWidth: 0,
      lineColor: "#ccd6eb",
      lineWidth: 1,
      tickColor: "#ccd6eb",
      tickWidth: 1,
      labels: {
        format: "{value:,.0f}",
        x: -15,
        y: null,
        style: { fontSize: "10px", color: "#24273E" }
      },
      index: 0
    }
  ];


  constructor() {
    super();
    this.legend.verticalAlign = 'top';
    this.tooltip.outside = false;
  }

  
  setCategories(values: string[]) {
    this.xAxis.categories = values;
  }
  
  override getOption() {
    const value = JSON.parse(JSON.stringify(this));

    value.plotOptions = {
      ...value.plotOptions,
      series: {
        events: {
          legendItemClick: handleLegendItemClick(this),
        }
      }
    }

    return value;
  }
}



@Component({
  selector: 'app-metrics-chart',
  template: `
    @if (option()) {
      <app-graph [options]="option()" />
    }
  `,
  host: {
    'class': 'block relative h-full'
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    GraphComponent,
  ],
})
export class MetricsChartComponent {

  themes = input<string[]>(['#ff9900', '#65789b', '#22c55e', '#5b8ff9', '#61ddaa', '#f6bd16', '#7262fd', '#78d3f8', '#9661bc', '#f6903d', '#008685', '#f08bb4']);
  value = input<TrendVo[] | Array<{ [key: string]: TrendVo[] }>>([]);
  trendTilte = input<string[]>([]);
  isPercent = input(false, { transform: booleanAttribute });

  
  option = computed(() => {
    const options = new Area();
    let series = [];

    if (this.value()?.length > 0) {
      if (this.value().at(0).year !== undefined) {
        series = series.concat(this.genSeries(this.value() as any));
      } else {
        this.value()
          .sort((a, b) => {
            const aK = Object.keys(a).at(0);
            const bK = Object.keys(b).at(0);

            return aK === 'A0' ? -1 : bK === 'A1' ? 1 : 0
          })
          .forEach(item => {
            Object.keys(item).forEach(k => {
              const suffix = k === 'A0' ? '-大盘' : '';

              series = series.concat(this.genSeries(item[k], suffix));
            })
          })
      }

      series.reverse();
    }
    
    if (this.isPercent()) {
      options.tooltip.valueSuffix = '%';
      options.yAxis.at(0).labels.format = '{value}%';
    }

    options.setTheme(this.themes());
    options.setCategories(this.trendTilte());
    options.setValue(series);

    return options.getOption();
  });


  genSeries(data: TrendVo[], suffix = '') {
    const values = groupBy(data, 'year') as {[key: number]: TrendVo[]};
      
    return Object.keys(values).map((key, index) => {
      const sorted = values[key]
        .sort((a, b) => a.month - b.month)
        .sort((a, b) => {
          if (/-/.test(a.month) && /-/.test(b.month)) {
            // 将日期字符串转换为可以比较的格式
            const dateA = new Date(`${a.year}/${a.month.replace('-', '/')}`);
            const dateB = new Date(`${b.year}/${b.month.replace('-', '/')}`);

            if (
              a.year % 4 !== 0 && a.month === '02-29' &&
              b.year % 4 !== 0 && b.month === '03-01'
            ) {
              return -1
            }

            return (
              dateA < dateB ? -1 : dateA > dateB ? 1 : 0
            )
          } else {
            // 将日期字符串转换为可以比较的格式
            const dateA = new Date(`${a.year}/${a.month}`);
            const dateB = new Date(`${b.year}/${b.month}`);

            return (
              dateA < dateB ? -1 : dateA > dateB ? 1 : 0
            )
          }
        })

      const data = sorted.map(item => item.value);
      const item = new MiniAreaSeries();

      item.name = key + suffix;
      item.zIndex = 20 + index;
      item.data = data;
      
      return item;
    })
  }
  
  
}
