import { Async<PERSON>ip<PERSON>, DatePipe, NgClass } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, Component, computed, inject, input, signal } from '@angular/core';
// import { DragDropModule } from '@angular/cdk/drag-drop';
import { OverlayModule } from '@angular/cdk/overlay';
import { AnimationEvent } from '@angular/animations';
import { FormsModule } from '@angular/forms';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { differenceInCalendarDays, addDays, differenceInCalendarMonths, addMonths } from 'date-fns';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { finalize } from 'rxjs';

import { Animations } from '@common/animation';
import { ModalService, ModalVisibleState } from '@core/modal';
import { IconCloseComponent } from '@shared/modules/icons';
import { CaerusApiService } from '@api/caerus';
import { BaseDataOutput } from '@views/cockpit/models';
import { CockpitService } from '@views/cockpit/services';
import { MetricsDetailOutput, MetricsDetailTrendVo } from '@api/caerus/model';
import { IndicatorModule } from '@shared/components/indicator';
import { IncrementPipe } from '@shared/pipes/increment';
import { MetricsChartComponent } from './metrics-chart/metrics-chart.component';
import { LineSpinComponent } from '@shared/components/line-spin';
import { RadioModule } from '@shared/modules/headless';
import { SwitchMap } from '@common/decorator';
import { toNumber } from '@common/function';


@Component({
  selector: 'app-metrics-detail',
  template: `
    <div [@opacityAnimation]="visible()" class="modal-backdrop"></div>

    <div class="modal">
      <div class="absolute inset-0 z-0" (click)="close()"></div>
      <div [@modalOutToInAnimation]="visibleState()" (@modalOutToInAnimation.done)="animationDone($event)" class="relative z-10 m-auto">
        <div cdkDragBoundary=".modal" cdkDrag class="modal-content max-w-unset!">
          <header cdkDragHandle class="modal-header">
            <h3 class="modal-title">{{title()}}</h3>

            <div class="flex items-center gap-x-3 text-xs pl-6">
              <nz-select class="w-28" [(ngModel)]="dateType" (ngModelChange)="onDateTypeChange($event)">
                @for (item of unitOptions(); track $index) {
                  <nz-option [nzLabel]="item.label" [nzValue]="item.value" [nzDisabled]="item.disabled"></nz-option>
                }
              </nz-select>

              @if (dateType() === '月') {
                <nz-date-picker [nzShowToday]="false" [nzMode]="dateMode()" [nzFormat]="dateFormat()" [nzDisabledDate]="disabledMonth" [(ngModel)]="date" (ngModelChange)="fetchMetricsOverviewDetail()" />
              }
              @else {
                <nz-date-picker [nzShowToday]="false" [nzMode]="dateMode()" [nzFormat]="dateFormat()" [nzDisabledDate]="disabledDate" [(ngModel)]="date" (ngModelChange)="fetchMetricsOverviewDetail()" />
              }

              <span>
                <label class="font-bold">区域：</label> {{service.area()}}
              </span>
              
              <span>
                <label class="font-bold">订单类型：</label> {{service.rideTypeMap.get(service.rideType())}}
              </span>

              @if (targetVisible()) {
                <label nz-checkbox [(ngModel)]="showTotal" (ngModelChange)="fetchMetricsOverviewDetail()">大盘对比</label>
              }
            </div>

            <div class="flex-1"></div>
            
            <CloseIcon iconBtn class="xl" (click)="close()" />
          </header>
          
          <div class="modal-body">
            @if (metrics().length > 1) {
              <app-radio-group class="relative flex items-start gap-x-0.5 pt-2" [(ngModel)]="index" (ngModelChange)="fetchMetricsOverviewDetail()">
                @for (metric of metrics(); track metric) {
                  <app-radio class="line-radio" [value]="$index">{{metric.aliasName}}</app-radio>
                }
                <app-radio-thumb class="border-primary border-b-2" />
              </app-radio-group>
            }

            <div class="flex items-center justify-center gap-x-1">
              <app-indicator-content class="max-w-1/4 pt-7 pb-5">
                <app-indicator-value-group class="justify-center">
                  @if (metrics()[index()]?.percent) {
                    <app-indicator-value value="{{data()?.value ? data()?.value + '%' : data()?.value ?? '-'}}" />
                  } @else {
                    <app-indicator-value [value]="(data()?.value | increment | async) ?? '-'" />
                  }
                  <app-indicator-subtitle>{{data()?.avgType}}</app-indicator-subtitle>
                </app-indicator-value-group>
                <app-indicator-compare-group gap="gap-1">
                  <app-indicator-compare iconVisible [label]="quarterLabel()" [value]="data()?.quarterOnQuarterGrowth" />
                  <app-indicator-compare iconVisible [label]="yearLabel()"    [value]="data()?.yearOnYearGrowth" />
                </app-indicator-compare-group>
              </app-indicator-content>

              @if (targetVisible()) {
                <div class="w-px h-14 bg-neutral-300 rotate-12"></div>

                <app-indicator-content class="max-w-1/4 pt-7 pb-5">
                  <app-indicator-value-group class="justify-center">
                    @if (metrics()[index()]?.percent) {
                      <app-indicator-value value="{{data()?.totalValue ? data()?.totalValue + '%' : data()?.totalValue ?? '-'}}" />
                    } @else {
                      <app-indicator-value [value]="(data()?.totalValue | increment | async) ?? '-'" />
                    }
                    <app-indicator-subtitle>{{data()?.avgType}}<br>大盘表现</app-indicator-subtitle>
                  </app-indicator-value-group>
                  <app-indicator-compare-group gap="gap-1">
                    <app-indicator-compare iconVisible [label]="quarterLabel()" [value]="data()?.totalQuarterOnQuarterGrowth" />
                    <app-indicator-compare iconVisible [label]="yearLabel()"    [value]="data()?.totalYearOnYearGrowth" />
                  </app-indicator-compare-group>
                </app-indicator-content>

                @if (data()?.totalRate !== null) {
                  <div class="text-lg">=</div>

                  <app-indicator-content class="max-w-1/4 pt-7 pb-5">
                    <app-indicator-value-group class="justify-center">
                      <app-indicator-value value="{{data()?.totalRate ? data()?.totalRate + '%' : data()?.totalRate ?? '-'}}" />
                      <app-indicator-subtitle>大盘占比</app-indicator-subtitle>
                    </app-indicator-value-group>
                    <app-indicator-compare-group gap="gap-1" class="invisible">
                      <app-indicator-compare iconVisible label="" [value]="null" />
                      <app-indicator-compare iconVisible label="" [value]="null" />
                    </app-indicator-compare-group>
                  </app-indicator-content>
                }
              }
            </div>
            
            
            <div class="relative flex w-256 h-80">
              <div class="absolute inset-0 flex items-center justify-center bg-white/70 transition-opacity opacity-0 -z-10" [ngClass]="{ 'opacity-100 pointer-events-auto z-50': querying() }">
                <app-line-spin />
              </div>

              @if (noData()) {
                <div class="flex flex-col items-center gap-y-3 m-auto">
                  <img src="assets/images/svg/empty.svg">
                  <span class="text-slate-500">无数据</span>
                </div>
              }
              
              @if (trendVo()) {
                <div class="absolute inset-0">
                  <app-metrics-chart [value]="trendVo()" [isPercent]="metrics()[index()]?.percent" [trendTilte]="trendTilte()" />
                </div>
              }
            </div>

            <div class="text-center text-neutral-400 text-xs">说明：长按鼠标框选一段区域，可对该区域内的图形放大展示；放大展示后点击重置可恢复</div>
          </div>
        </div>
      </div>
    </div>
  `,
  styles: [
    `
      :host ::ng-deep .ant-checkbox-wrapper > span + span {
        transform: scale(0.85);
        transform-origin: left center;
      }
    `
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: {
    'class': 'flex w-screen h-screen'
  },
  animations: [
    ...Animations.OpacityAnimation,
    ...Animations.ModalOutToInAnimation,
  ],
  imports: [
    NgClass,
    AsyncPipe,
    FormsModule,
    OverlayModule,
    DragDropModule,
    NzSelectModule,
    NzDatePickerModule,
    NzCheckboxModule,
    IndicatorModule,
    RadioModule,
    LineSpinComponent,
    MetricsChartComponent,
    IconCloseComponent,
    IncrementPipe,
  ],
  providers: [
    DatePipe,
  ]
})
export class MetricsDetailComponent implements AfterViewInit {

  readonly datePipe = inject(DatePipe);
  readonly modalService = inject(ModalService);
  readonly apiService = inject(CaerusApiService);
  readonly service = inject(CockpitService);

  title = input<string>();
  metrics = input<BaseDataOutput[]>();
  width = input<string | number>();
  supports = input([
    ['day', 'week', 'month']
  ]);
  
  index = signal(0);
  readonly today = signal(new Date());
  dateType = signal<'日' | '周' | '月'>('月');
  date = signal(this.service.month());
  showTotal = signal<boolean>(null);

  targetVisible = computed(() => {
    return (
      this.service.area() !== '全国' ||
      this.service.rideType() !== 'ALL'
    );
  });

  quarterLabel = computed(() => {
    switch (this.dateType()) {
      case '日':
        return '日环比';
      case '周':
        return '周环比';
      case '月':
        return '月环比';
    }
  })

  yearLabel = computed(() => {
    switch (this.dateType()) {
      case '日':
        return '周同比';
      case '周':
        return '年同比';
      case '月':
        return '年同比';
    }
  })

  dateMode = computed(() => {
    switch(this.dateType()) {
      case '日':
        return null;
      case '周':
        return 'week';
      case '月':
        return 'month';
    }
  })

  dateFormat = computed(() => {
    switch(this.dateType()) {
      case '日':
        return 'yyyy/MM/dd';
      case '周':
        return 'YYYY第ww周';
      case '月':
        return 'yyyy/MM';
    }
  })

  dateValue = computed(() => {
    switch(this.dateType()) {
      case '日':
        return this._formatValue('yyyy-MM-dd');
      case '周':
        return this._formatValue('YYYY-ww');
      case '月':
        return this._formatValue('yyyy-MM');
    }
  })
  
  visible = signal(false);
  visibleState = signal<ModalVisibleState>('invisible');
  trendVo = signal<MetricsDetailTrendVo[]>([]);
  trendTilte = signal<string[]>([]);
  data = signal<MetricsDetailOutput>(null);
  querying = signal(false);
  noData = signal(false);

  metricsKey = computed(() => {
    return this.metrics()[this.index()];
  })

  unitOptions = computed(() => {
    const supports = this.supports()[this.index()];
    
    return [
      { label: '日', value: '日', disabled: !supports.includes('day') },
      { label: '周', value: '周', disabled: !supports.includes('week') },
      { label: '月', value: '月', disabled: !supports.includes('month') },
    ];
  });

  
  ngAfterViewInit(): void {
    this.visible.set(true);
    this.visibleState.set('visible');
    this.showTotal.set(this.targetVisible());
    this.fetchMetricsOverviewDetail();
  }


  private _formatValue(format: string) {
    return this.datePipe.transform(this.date(), format);
  }


  disabledDate = (current: Date): boolean => {
    // Can not select days before today and today
    return differenceInCalendarDays(current, this.today()) > -1;
  }


  disabledMonth = (current: Date): boolean => {
    // Can not select days before today and today
    const startDateForMonth = new Date().setDate(1);
    const dateRight = addMonths(startDateForMonth, 1);
    
    return differenceInCalendarMonths(current, dateRight) > -1;
  }


  animationDone(event: AnimationEvent): void {
    if (event.fromState === 'visible' && event.toState === 'leave') {
      this.modalService.close();
    }
  }
  

  close() {
    this.visible.set(false);
    this.visibleState.set('leave');
  }


  onDateTypeChange(value: '日' | '周' | '月') {
    const now = new Date().setHours(0, 0, 0, 0);

    switch (value) {
      case '月':
        this.date.set(this.service.month());
        break;
      case '周':
      case '日':
        this.date.set(addDays(now, -1));
        break;
      default:
        this.date.set(null);
        break;
    }
    
    this.fetchMetricsOverviewDetail();
  }


  @SwitchMap()
  fetchMetricsOverviewDetail() {
    const date = this.dateValue();
    const area = this.service.area();
    const rideType = this.service.rideType();
    const source = this.service.sourceType();
    const { metricsKey } = this.metricsKey();
    const dateType = this.dateType();
    const showTotal = Number(this.showTotal());
    const body = { dateType, metricsKey, rideType, source, date, area, showTotal };

    this.trendVo.set(null);
    this.querying.set(true);
    this.noData.set(false);
    return this.apiService.fetchMetricsOverviewDetail(body).pipe(
      finalize(() => this.querying.set(false))
    ).subscribe(res => {
      if (res.data) {
        let { trendTilte, trendVo } = res.data;
        const titles = trendTilte.map(item => {
          switch (dateType) {
            case '周':
              return `第${item}周`;
            case '月':
              return `${item}月`
          }
          return item;
        })

        const yearSet = new Set([...trendVo.map(item => item.year).map(toNumber)]);
        const hasLeapYear = [...yearSet].some(item => item % 4 === 0);

        if (dateType === '日' && hasLeapYear) {
          trendVo = trendVo.filter(item => {
            if (parseFloat(item.year) % 4 !== 0) {
              return item.month !== '02-29';
            }
            return true;
          });
          [...yearSet].filter(y => y % 4 !== 0).forEach(y => {
            trendVo.push({ year: y.toString(), month: '02-29', value: null })
          })
        }
        
        this.data.set(res.data);
        this.trendTilte.set(titles);
        this.trendVo.set(trendVo);
      } else {
        console.warn(res)
        this.noData.set(true);
      }
    })
  }
  
}
