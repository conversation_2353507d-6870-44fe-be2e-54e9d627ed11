import { ControlValueAccessor, FormsModule, NG_VALUE_ACCESSOR } from '@angular/forms';
import { AfterViewInit, ChangeDetectionStrategy, Component, computed, forwardRef, inject, signal } from '@angular/core';

import { RadioModule } from '@shared/modules/headless';
import { DimensionValueMenuVo } from '@api/caerus/model';
import { MoreAreaSelectComponent } from './more-area-select/more-area-select.component';
import { CockpitService } from '@views/cockpit/services';
import { CaerusApiService } from '@api/caerus';

@Component({
  selector: 'app-area-filter',
  template: `
    @if (data()) {
      <app-radio-group class="relative flex gap-1" [ngModel]="value()" (ngModelChange)="onValueChange($event)">
        <div class="flex gap-1 flex-wrap items-start">
          @for (item of defaultShowCitys(); track $index) {
            <app-radio class="tag-radio" activeClass="active" [value]="item">{{ item?.showValue || '全部' }}</app-radio>
          }
          <app-more-area-select [options]="moreCitys()" [area]="value()" (areaChange)="onValueChange($event)" />
        </div>
      </app-radio-group>
    }
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [FormsModule, RadioModule, MoreAreaSelectComponent],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => AreaFilterComponent),
      multi: true
    },
  ]
})
export class AreaFilterComponent implements AfterViewInit, ControlValueAccessor {
  readonly apiService = inject(CaerusApiService);
  readonly service = inject(CockpitService);

  data = signal<DimensionValueMenuVo[]>(null);
  value = signal<DimensionValueMenuVo | string>(null);

  defaultShowCitys = computed(() => {
    return [
      null,
      this.data()?.filter(item => item.defaultShow === 1) || []
    ].flat(1);
  });

  moreCitys = computed(() => {
    if (this.data()) {
      return this.data()
        .filter(item => item.defaultShow === 0)
        .sort((a, b) => a.showOrder - b.showOrder);
    }
    return [];
  });

  ngAfterViewInit(): void {
    this.fetchHomeCityData();
  }

  _controlValueAccessorChangeFn: (value: any) => void = () => {};
  onTouched: any = () => {};
  

  registerOnChange(fn: (value: any) => void) {
    this._controlValueAccessorChangeFn = fn;
  }

  
  registerOnTouched(fn: (value: any) => void) {
    this.onTouched = fn;
  }
  

  writeValue(value: string) {
    if (value === '全国' || !value) {
      this.value.set(null);
    } else {
      const city = this.defaultShowCitys().find(item => item?.value === value);
      
      if (city) {
        this.value.set(city);
      } else {
        this.value.set(value);
      }
    }
  }


  onValueChange(value: DimensionValueMenuVo) {
    this.value.set(value);
    this._controlValueAccessorChangeFn(value);
  }

  private fetchHomeCityData() {
    this.apiService.fetchHomeCityData().subscribe(res => {
      if (!res?.data) {
        return;
      }
      this.data.set(res.data);
    });
  }
}
