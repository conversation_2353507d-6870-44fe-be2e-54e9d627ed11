import { Injectable, computed, linkedSignal, signal } from '@angular/core';
import { differenceInCalendarDays, addDays } from 'date-fns';
import { _pad } from '@common/function';

@Injectable({
  providedIn: 'root'
})
export class CockpitService {

  readonly yesterday = signal(addDays(new Date(), -1));
  readonly today = signal(new Date());

  area = signal('全国');

  rideType = signal<'ALL' | '城际' | '市内'>('ALL');
  rideTypeMap = new Map([
    ['ALL', '全部'],
    ['城际', '城际'],
    ['市内', '市内'],
  ])
  
  /** 渠道来源 */
  sourceType = signal<'all' | 'self' | 'tencent'>('all');
  sourceMap = new Map([
    ['all', '全部渠道'],
    ['self', '自有渠道'],
    ['tencent', '腾讯外输'],
  ])

  month = linkedSignal({
    source: () => this.today(),
    computation: (source) => {
      if (source.getDate() === 1) {
        return this.yesterday()
      }
      return this.today();
    }
  });
  dateMonth = computed(() => new Date(this.month()).getMonth() + 1);

  date = computed(() => {
    const today = this.month();
    const year  = today.getFullYear();
    const month = today.getMonth() + 1;

    return `${year}-${_pad(month)}`;
  });

  disabledDate = (current: Date): boolean => {
    // Can not select days before today and today
    return differenceInCalendarDays(current, this.today()) > 0;
  }

}
