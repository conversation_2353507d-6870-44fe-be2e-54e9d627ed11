import { Injectable } from '@angular/core';
import { BehaviorSubject } from 'rxjs';


interface VisibleContext {
  index: number;
  isShowAll: boolean;
}


@Injectable()
export class RelationControlService {

  visibleChange = new BehaviorSubject<VisibleContext>(null);
  seriesIndexsChange = new BehaviorSubject<number[]>([]);
  showType = new BehaviorSubject<'number' | 'ratio'>(null);

  emit({ index, isShowAll }: VisibleContext) {
    this.visibleChange.next({ index, isShowAll });
  }

  dispatch(indexs: number[]) {
    this.seriesIndexsChange.next(indexs);
  }

}
