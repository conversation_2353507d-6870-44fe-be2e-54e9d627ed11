import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';

import { API_URL } from '@common/const';
import { ResultBody } from '@common/interface';
import { BaseUri, Body, Get, Post } from '@common/decorator';
import { BaseApiService } from '@common/service';
import { OverviewMetricsOutput, InternalMktTargetOutput, InternalMktInput, PromotionRankInput, ExternalPromotionSourceOutput, FunnelPassengerOutput, BaseInput, FunnelPassengerTransformTrendVo, BaseTrendInput, FunnelOrderOutput, FunnelOrderTransformTrendVo, PromotionTrendInput, PromotionMetricsTrendVo, ExternalPromotionQualityOutput, BaseDataOutput, FluctuationInput, FluctuationOverviewTrend, FluctuationDetailOutput, FlowTrendOutput, FlowOutput, FlowTrendInput, TengxunFunnelOrderOutput } from '../models';
import { ExternalPromotionTargetOutput, ExternalPromotionInput } from '../models';
import { DataUpdateVo } from '../models/data-update.vo';
import { DauTrendVo } from '../models/dau-trend.vo';


@Injectable({
  providedIn: 'root'
})
@BaseUri(`${API_URL}/data`)
export class CockpitApiService extends BaseApiService {
  
  /**
   * @see {@link https://demeter.didapinche.com/test/api/caerus/doc.html#/default/数据接口/getCommonUsingPOST 数据更新时间}
   * @returns 
   */
  @Post('/update/time')
  fetchCommon(): Observable<ResultBody<DataUpdateVo>> {
    return null;
  }
  
  
  /**
   * @see {@link https://demeter.didapinche.com/test/api/caerus/doc.html#/default/数据接口/getKeyMetricsOverviewUsingPOST 总览关键指标}
   * @returns 
   */
  @Post('/metrics/overview')
  fetchMetricsOverview(@Body() body: BaseInput): Observable<ResultBody<OverviewMetricsOutput>> {
    return null;
  }


  /**
   * @see {@link https://demeter.didapinche.com/test/api/caerus/doc.html#/default/数据接口/getExternalPromotionTargetUsingPOST 端外推广目标达成}
   * @param {ExternalPromotionInput} body 端外推广参数
   * @returns 
   */
  @Post('/external/promotion/target')
  fetchExternalPromotionTarget(@Body() body: ExternalPromotionInput): Observable<ResultBody<ExternalPromotionTargetOutput>> {
    return null;
  }


  /**
   * @see {@link https://demeter.didapinche.com/test/api/caerus/doc.html#/default/数据接口/getExternalPromotionTargetTrendUsingPOST 端外推广目标达成趋势图}
   * @param {PromotionTrendInput} body 端外推广参数
   * @returns 
   */
  @Post('/external/promotion/target/trend')
  fetchExternalPromotionTargetTrend(@Body() body: PromotionTrendInput): Observable<ResultBody<PromotionMetricsTrendVo[]>> {
    return null;
  }


  /**
   * @see {@link https://demeter.didapinche.com/test/api/caerus/doc.html#/default/数据接口/getExternalPromotionQualityUsingPOST 端外推广质量分析}
   * @param {ExternalPromotionInput} body 端外推广参数
   * @returns 
   */
  @Post('/external/promotion/quality')
  fetchExternalPromotionQuality(@Body() body: ExternalPromotionInput): Observable<ResultBody<ExternalPromotionQualityOutput>> {
    return null;
  }
  

  /**
   * @see {@link https://demeter.didapinche.com/test/api/caerus/doc.html#/default/数据接口/getExternalPromotionSourceRankUsingPOST 端外推广渠道排行}
   * @param {PromotionRankInput} body 端外推广参数
   * @returns 
   */
  @Post('/external/promotion/source/rank')
  fetchExternalPromotionSourceRank(@Body() body: PromotionRankInput): Observable<ResultBody<ExternalPromotionSourceOutput>> {
    return null;
  }


  /**
   * @see {@link https://demeter.didapinche.com/test/api/caerus/doc.html#/default/数据接口/getInternalMktTargetUsingPOST 端内业务目标达成}
   * @param {InternalMktInput} body 端内营销参数
   * @returns 
   */
  @Post('/internal/mkt/target')
  fetchInternalMktTarget(@Body() body: InternalMktInput): Observable<ResultBody<InternalMktTargetOutput>> {
    return null;
  }


  /**
   * @see {@link https://demeter.didapinche.com/test/api/caerus/doc.html#/default/数据接口/getInternalMktQualityUsingPOST 端内质量分析}
   * @param {InternalMktInput} body 端内营销参数
   * @returns 
   */
  @Post('/internal/mkt/quality')
  fetchInternalMktQuality(@Body() body: InternalMktInput): Observable<ResultBody<BaseDataOutput[]>> {
    return null;
  }


  /**
   * @see {@link https://pass.didapinche.com/harley/v3/project/api?pid=20&type=2&treeId=1312&env=93 DAU趋势}
   * @param {BaseTrendInput} body 
   * @returns 
   */
  @Post('/dau/trend')
  fetchDauTrend(@Body() body: BaseTrendInput): Observable<ResultBody<DauTrendVo[]>> {
    return null;
  }


  /**
   * @see {@link https://demeter.didapinche.com/test/api/caerus/doc.html#/default/数据接口/getFunnelPassengerUsingPOST 乘客转化漏斗}
   * @param {BaseInput} body 
   * @returns 
   */
  @Post('/funnel/passenger')
  fetchFunnelPassenger(@Body() body: BaseInput): Observable<ResultBody<FunnelPassengerOutput>> {
    return null;
  }


  /**
   * @see {@link https://demeter.didapinche.com/test/api/caerus/doc.html#/default/数据接口/getFunnelPassengerTrendUsingPOST 乘客转化漏斗转化趋势}
   * @param {BaseTrendInput} body 
   * @returns 
   */
  @Post('/funnel/passenger/trend')
  fetchFunnelPassengerTrend(@Body() body: BaseTrendInput): Observable<ResultBody<FunnelPassengerTransformTrendVo[]>> {
    return null;
  }


  /**
   * @see {@link https://demeter.didapinche.com/test/api/caerus/doc.html#/default/数据接口/getFunnelPassengerUsingPOST 乘客转化漏斗}
   * @param {BaseInput} body 
   * @returns 
   */
  @Post('/funnel/order')
  fetchFunnelOrder(@Body() body: BaseInput): Observable<ResultBody<FunnelOrderOutput>> {
    return null;
  }


  /**
   * @see {@link https://demeter.didapinche.com/test/api/caerus/doc.html#/default/数据接口/getFunnelOrderTrendUsingPOST 乘客转化漏斗转化趋势}
   * @param {BaseTrendInput} body 
   * @returns 
   */
  @Post('/funnel/order/trend')
  fetchFunnelOrderTrend(@Body() body: BaseTrendInput): Observable<ResultBody<FunnelOrderTransformTrendVo[]>> {
    return null;
  }


  /**
   * @see {@link https://pass.didapinche.com/harley/v3/project/api?pid=20&type=2&treeId=1771&env=93 腾讯渠道转化漏斗（按订单视角）}
   * @param {BaseInput} body 
   * @returns 
   */
  @Post('/tengxun/funnel/order')
  fetchTencentFunnelOrder(@Body() body: BaseInput): Observable<ResultBody<TengxunFunnelOrderOutput>> {
    return null;
  }


  /**
   * @see {@link https://pass.didapinche.com/harley/v3/project/api?pid=20&type=2&treeId=1772&env=93 腾讯渠道转化漏斗（按订单视角）转化趋势}
   * @param {BaseTrendInput} body 
   * @returns 
   */
  @Post('/tengxun/funnel/order/trend')
  fetchTencentFunnelOrderTrend(@Body() body: BaseTrendInput): Observable<ResultBody<FunnelOrderTransformTrendVo[]>> {
    return null;
  }


  /**
   * @see {@link https://pass.didapinche.com/harley/v3/project/api?pid=20&type=2&treeId=1774&env=93 腾讯渠道影响分析-流入流出 }
   * @param {BaseInput} body 
   * @returns 
   */
  @Post('/tengxun/influence/flow')
  fetchTencentInfluenceFlow(@Body() body: BaseInput): Observable<ResultBody<FlowOutput>> {
    return null;
  }


  /**
   * @see {@link https://pass.didapinche.com/harley/v3/project/api?pid=20&type=2&treeId=1775&env=93 腾讯渠道影响分析趋势-流入流出 }
   * @param {BaseTrendInput} body 
   * @returns 
   */
  @Post('/tengxun/influence/flow/trend')
  fetchTencentInfluenceFlowTrend(@Body() body: FlowTrendInput): Observable<ResultBody<FlowTrendOutput[]>> {
    return null;
  }


  /**
   * @see {@link https://pass.didapinche.com/harley/v3/project/api?pid=20&type=2&treeId=1756&env=93 波动概览}
   * @param {FluctuationInput} body 
   */
  @Post('/fluctuation/overview')
  fetchFluctuationOverview(@Body() body: FluctuationInput): Observable<ResultBody<FluctuationOverviewTrend>> {
    return null;
  }


  /**
   * @see {@link https://pass.didapinche.com/harley/v3/project/api?pid=20&type=2&treeId=1757&env= 波动明细}
   * @param {FluctuationInput} body 
   * @returns 波动明细数据
   */
  @Post('/fluctuation/detail')
  fetchFluctuationDetail(@Body() body: FluctuationInput): Observable<ResultBody<FluctuationDetailOutput>> {
    return null;
  }


  @Get('/api/meta/data/clickhouse')
  test(): Observable<any> {
    return null;
  }

}