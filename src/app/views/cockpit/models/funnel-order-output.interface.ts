import { BaseDataOutput, BaseTrendVo } from './base-output.interface';


export interface FunnelOrderTransformItem {

  /** 下单完单率 */
  orderFinishRate: BaseDataOutput;

  /** 接单完单率 */
  replyFinishRate: BaseDataOutput;

  /** 下单接单率 */
  orderReplyRate: BaseDataOutput;

}


export interface FunnelOrderVo {

  /** 下单量 */
  orderCnt: FunnelOrderTransformItem;

  /** 接单量 */
  orderReplyCal: FunnelOrderVo;

  /** 完单量 */
  orderFinishCal: FunnelOrderTransformTrendVo[];
  
}


export interface FunnelOrderTransformTrendVo extends BaseTrendVo {
  
  /**	下单完单率*/
  orderFinishRate: number;
  
  /**	接单完单率*/
  replyFinishRate: number;

  /**	下单接单率 */
  orderReplyRate: number;

}


export interface FunnelOrderOutput {

  /** 当前转化趋势 */
  curTrendItem: FunnelOrderTransformItem;

  /** 乘客转换漏斗 */
  funnel: FunnelOrderVo;

  /** 转化趋势 */
  trend: FunnelOrderTransformTrendVo[];

}
