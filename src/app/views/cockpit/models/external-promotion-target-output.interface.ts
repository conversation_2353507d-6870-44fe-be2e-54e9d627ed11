import { BaseDataOutput, BaseInput, BaseTrendInput, BaseTrendVo } from './base-output.interface';


/**
 * 端外推广参数
 */
export class ExternalPromotionInput extends BaseInput {

  /**
   * 类型
   * - 拉新 (new)
   * - 召回 (recall)
   */
  source?: 'new' | 'recall';

}


export interface PromotionTrendInput extends BaseTrendInput {

  /**
   * 类型
   * - 乘客 (passenger)
   * - 车主 (driver)
   */
  type: 'passenger' | 'driver';

  /**
   * 类型
   * - 拉新 (new)
   * - 召回 (recall)
   */
  source?: 'new' | 'recall';

}


export interface ExternalPromotionTargetOutput {
  
  /** 车主 */
  driver: BaseDataOutput & {
    /** 当月自然量 */
    natureValue: number;	
    
    /** 当月推广量 */
    promotionValue: number;
  };
  
  /** 乘客 */
  passenger: BaseDataOutput & {
    /** 当月自然量 */
    natureValue: number;	
    
    /** 当月推广量 */
    promotionValue: number;
  };

}


export interface PromotionMetricsTrendVo extends BaseTrendVo {

  /** 自然量 */
  natureValue: number;

  /** 付费占比 */
  paymentRatio: number;

  /** 推广量 */
  promotionValue: number;

  /** 乘客数、车主数 */
  value: number;

}


export interface ExternalPromotionQualityOutput {

  /** 乘客CAC */
  passengerCac: BaseDataOutput;

  /** 乘客ROI */
  passengerRoi: BaseDataOutput;

  /** 乘客推广费用 */
  passengerPromotionFee: BaseDataOutput;

  /** 车主CAC */
  driverCac: BaseDataOutput;

  /** 车主ROI */
  driverRoi: BaseDataOutput;

  /** 车主推广费用 */
  driverPromotionFee: BaseDataOutput;

}