import { BaseOutput } from './base-output.interface';

export interface FluctuationOutput extends BaseOutput {

  /** 大盘总体值 */
  totalValue: number;

  /** 波动占比 */
  fluctuationRatio: number;

  /** 增幅 */
  increase: number;

}


export interface FluctuationOverviewInfo extends FluctuationOutput {

  /** 维度数据 */
  dimenValue: string;
  
}


export interface FluctuationOverviewTrend {

  /** 大盘增幅 */
  totalIncrease: number;

  /** 大盘同环比 */
  totalYearOrMonthGrowth: number;

  /** 正向数据 */
  forward: FluctuationOverviewInfo[];

  /** 负向数据 */
  negative: FluctuationOverviewInfo[];

}


export interface MetricsInfoVo {
  metricsKey: string;
  metricsName: string;
  metricsAliasName: string;
  metricsDetail: string;

  /**
   * 平均方式
   */
  avgType: string;
  
  /**
   * 平均方式枚举值 1 月日均，2 月人均，3 去重，4 当月去重
   */
  avgTypeValue: number;
  
  /**
   * 小数点位保留规则 0(取整)，2 (保留2位小数), 101 (转化为百分比后，保留1位小数)
   */
  roundType: number;
}


export interface FluctuationDetailInfo {

  /** 维度数据 */
  dimenValue: string;

  /** 完单量 */
  finishOrderCnt: FluctuationOutput;
  finishOrderCntIncrease: number;
  finishOrderCntFluctuationRatio: number;
  finishOrderYearOrMonthGrowth: number;
  finishOrderCntValue: number;

  /** DAU */
  dau: FluctuationOutput;
  dauIncrease: number;
  dauValue: number;

  /** 下单率 */
  bookDauRate: FluctuationOutput;
  bookDauRateIncrease: number;
  bookDauRateValue: number;

  /** 人均下单频次 */
  passAvgBookCnt: FluctuationOutput;
  passAvgBookCntIncrease: number;
  passAvgBookCntValue: number;

  /** 下单接单率 */
  bookReplyRate: FluctuationOutput;
  bookReplyRateIncrease: number;
  bookReplyRateValue: number;

  /** 接单完单率 */
  replyFinishRate: FluctuationOutput;
  replyFinishRateIncrease: number;
  replyFinishRateValue: number;

  /** 车主接单频次 */
  driAvgReplyCnt: FluctuationOutput;
  driAvgReplyCntIncrease: number;
  driAvgReplyCntValue: number;
  
}


export interface FluctuationDetailOutput {
  
  title: MetricsInfoVo[];

  list: FluctuationDetailInfo[];

}
