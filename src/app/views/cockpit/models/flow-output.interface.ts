import { BaseOutput, BaseTrendInput, BaseTrendVo } from './base-output.interface';
import { FunnelOrderTransformTrendVo, FunnelOrderVo } from './funnel-order-output.interface';


export interface FlowOutput {

  /** 腾讯流入自有完单用户数 */
  tengxunToOwnFinishOrderCnt: BaseOutput;

  /** 自有完单用户数 */
  ownFinishOrderCnt: BaseOutput;

  /** 腾讯流入自有占比 */
  tengxunToOwnFinishOrderRate: number;

  /** 腾讯流入自有月环比占比 */
  tengxunToOwnFinishOrderMonthRate: number;

  /** 腾讯流入自有年同比占比 */
  tengxunToOwnFinishOrderYearRate: number;

  /** 自有流出腾讯完单用户数 */
  ownToTengxunFinishOrderCnt: BaseOutput;

  /** 腾讯完单用户数 */
  tengxunFinishOrderCnt: BaseOutput;

  /** 自有流出占腾讯占比 */
  ownToTengxunFinishOrderRate: number;

  /** 自有流出占腾讯月环比占比 */
  ownToTengxunFinishOrderMonthRate: number;

  /** 自有流出占腾讯年同比占比 */
  ownToTengxunFinishOrderYearRate: number;

}


export interface FlowTrendOutput extends BaseTrendVo {

  /** 流入/流出用户数 */
  flowFinishOrderCnt: number;

  /** 完单用户数 */
  finishOrderCnt: number;

  /** 占比 */
  flowFinishOrderRate: number;

}


/** 流入流出参数 */
export interface FlowTrendInput extends BaseTrendInput {

  /** 流入流出类型，1：流出，2：流出 */
  type: number;

}


export interface BaseTargetOutput extends BaseOutput {

  /**
   * 目标值-分子，这里是当月总体值，
   * 当月整体值【用来跟目标值做完成率,使用前要判断是否为null】
   */
  sumValue: number;
  
  /**
   * 目标值
   */
  targetValue: number;

  /**
   * 达成率
   */
  achievement: number;

}


export interface TengxunFunnelOrderTransformItem extends BaseTrendVo {

  /**
   * 下单完单率
   */
  orderFinishRate: BaseTargetOutput;

  /**
   * 接单完单率
   */
  replyFinishRate: BaseTargetOutput;
  
  /**
   * 下单接单率
   */
  orderReplyRate: BaseTargetOutput;

}


export interface TengxunFunnelOrderOutput {

  /**
     * 订单转换漏斗
     */
  funnel: FunnelOrderVo;

  /**
   * 当前转化趋势
   */
  curTrendItem: TengxunFunnelOrderTransformItem;

  /**
   * 转化趋势
   */
  trend: FunnelOrderTransformTrendVo[];

}