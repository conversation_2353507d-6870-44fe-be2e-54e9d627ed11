import { BaseDataOutput } from './base-output.interface';


export interface OverviewMetricsOutput {

  /** 佣金收入 */
  commissionAmtRevenue: BaseDataOutput;

  /** 额外完单 */
  extraFinishOrderCnt: BaseDataOutput;
  
  /** 完单车主数 */
  finishDriverUnt: BaseDataOutput;
  
  /** 车主人均完单次数 */
  finishFrqDriverAvg: BaseDataOutput;

  /** 乘客人均完单次数 */
  finishFrqPassengerAvg: BaseDataOutput;

  /** 完单量 */
  finishOrderCnt: BaseDataOutput;

  /** 营销费用 */
  marketingExpenses: BaseDataOutput;

  /** 新增车主数 */
  newDriverUnt: BaseDataOutput;

  /** 新增乘客数 */
  newPassengerUnt: BaseDataOutput;

  /** 下单量 */
  orderCnt: BaseDataOutput;

  /** 下单完单率 */
  orderFinishRate: BaseDataOutput;

  /** 接单完单率 */
  replyFinishRate: BaseDataOutput;

  /** 腾讯外输佣金收入 */
  tencentCommissionAmtRevenue: BaseDataOutput;

  /** 腾讯外输完单车主数 */
  tencentFinishDriverUnt: BaseDataOutput;

  /** 腾讯外输完单量 */
  tencentFinishOrderCnt: BaseDataOutput;
  
  /** 腾讯外输下单量 */
  tencentOrderCnt: BaseDataOutput;

  /** 腾讯外输新增乘客数 */
  tencentNewAddPassengerUnt: BaseDataOutput;

  /** 自有渠道完单量 */
  selfFinishOrderCnt: BaseDataOutput;

  /** 自有渠道下单量 */
  selfOrderCnt: BaseDataOutput;

  /** 自有渠道完单车主数 */
  selfFinishDriverUnt: BaseDataOutput;

  /** 自有渠道佣金收入 */
  selfCommissionAmtRevenue: BaseDataOutput;

  /** 自有渠道营销费用 */
  selfMarketingExpenses: BaseDataOutput;


}