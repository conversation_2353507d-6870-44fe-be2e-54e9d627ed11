import { BaseDataOutput, BaseInput } from './base-output.interface';


interface ExtraFinishOrderOutput extends BaseDataOutput {

  /** 大盘总体值 */
  totalValue: number;

}

export interface InternalMktTargetItem {

  /** 额外完单 */
  extraFinishOrderCnt: ExtraFinishOrderOutput;

  /** 人均完单次数 */
  finishFrqAvg: BaseDataOutput;

}


/**
 * 端内营销参数
 */
export class InternalMktInput extends BaseInput {

  /**
   * 类型
   * - 大盘总体 (all)
   * - 乘客 (passenger)
   * - 车主 (driver)
   */
  type?: 'all' | 'passenger' | 'driver';

}


export interface InternalMktTargetOutput {

  /**	大盘整体 */
  all: InternalMktTargetItem;
  
  /** 车主视角 */
  driver: InternalMktTargetItem;
  
  /** 乘客视角 */
  passenger: InternalMktTargetItem;

}
