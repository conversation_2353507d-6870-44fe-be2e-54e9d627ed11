import { BaseDataOutput } from './base-output.interface';


interface FunnelPassengerTransformItem {

  /** DAU/MAU (用户粘性) */
  customerStickiness: BaseDataOutput;

  /** 下单完单率（用户） */
  orderFinishRate: BaseDataOutput;

  /** 接单完单率（用户） */
  replyFinishRate: BaseDataOutput;

  ucvr: BaseDataOutput;

}


interface FunnelPassengerOrderVo {

  /**	dau */
  dau: number;

  /**	下单人数 */
  orderCal: number;

  /**	完单人数 */
  orderCompleteCal: number;

  /**	接单人数 */
  orderReceiveCal: number;
  
}


export interface FunnelPassengerTransformTrendVo {

  /**	DAU/MAU (用户粘性) */
  customerStickiness: number;
  
  /**	月份 */
  month: string;
  
  /**	下单完单率（用户）*/
  orderFinishRate: number;
  
  /**	接单完单率（用户）*/
  replyFinishRate: number;

  /** */
  ucvr: number;
  
  /**	年份	 */
  year: string;

}


export interface FunnelPassengerOutput {

  /** 当前转化趋势 */
  curTrendItem: FunnelPassengerTransformItem;

  /** 乘客转换漏斗 */
  funnel: FunnelPassengerOrderVo;

  /** 转化趋势 */
  trend: FunnelPassengerTransformTrendVo[];

}
