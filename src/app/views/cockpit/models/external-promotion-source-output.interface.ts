import { BaseTrendInput } from './base-output.interface';


export class PromotionRankInput extends BaseTrendInput {

  /**
   * 类型
   * - 拉新 (new)
   * - 召回 (recall)
   */
  source?: 'new' | 'recall';

}


export class ExternalPromotionSourceVo {

  /** cac */
  cac: number;

  /** 推广费用 */
  fee: number;

  /** 排行 */
  rank: number;

  /** roi */
  roi: number;

  /** 来源 */
  source: string;

  /** 整体乘客或者车主数 */
  totalValue: number;

  /** 乘客或者车主数 */
  value: number;

}


export class ExternalPromotionSourceOutput {
  
  /**
   * 车主
   */
  driver: ExternalPromotionSourceVo[];

  /**
   * 乘客
   */
  passenger: ExternalPromotionSourceVo[];

}
