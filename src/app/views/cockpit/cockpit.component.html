<app-navigation>
  <ng-container ngProjectAs="[extra]">
    @if (commonData()) {
      <span class="text-xs">指标数据已更新至 {{commonData()?.totalUpdateTime | date: 'yyyy-MM-dd'}}	</span>
    } @else {
      <app-skeleton class="w-56 h-4" />
    }
  </ng-container>
</app-navigation>


<div class="sticky top-0 z-40 py-2 mb-1 bg-white" [class.shadow-lg]="isScrollDown()">
  <div class="flex justify-center">
    <div class="max-w-(--breakpoint-2xl) flex-1 min-w-0 flex items-center gap-x-8 text-xs px-5">
      <nz-date-picker nzMode="month" [nzFormat]="'yyyy/MM'" [nzDisabledDate]="disabledMonth" [(ngModel)]="service.month" />

      <div class="flex items-center">
        <label class="inline-flex items-center font-bold leading-0 whitespace-nowrap">区域：</label>
        <app-area-select [(ngModel)]="service.area" (ngModelChange)="this.service.area.set($event?.value || '全国')" />
      </div>

      <div class="flex items-center">
        <label class="inline-flex items-center font-bold leading-0 whitespace-nowrap">订单类型：</label>
        <app-radio-group class="relative flex gap-1" [(ngModel)]="service.rideType">
          <app-radio class="tag-radio-new" activeClass="active" value="ALL">全部</app-radio>
          <app-radio class="tag-radio-new" activeClass="active" value="城际">城际</app-radio>
          <app-radio class="tag-radio-new" activeClass="active" value="市内">市内</app-radio>
          <app-radio-thumb class="rounded-xs bg-primary" />
        </app-radio-group>
      </div>

      <button nz-button nzType="default" (click)="reset()">重置</button>
    </div>
    <div class="w-17.5" ></div>
  </div>

  <app-ribbon *queryRouteParam="'showMessage'" class="block translate-y-2">
    <app-ribbon-item>
      <div showFullText style="max-width: calc(100% - 100px);">
        <span class="h-10 leading-[40px]">尊敬的用户：5月13日Careus发版，计划于17点～21点停服，平台数据呈现可能会受到影响。若有疑问或建议，可联系DPM王有良、陈星晶或何倩反馈，谢谢您的配合～</span>
      </div>
      <a class="ribbon-link" (click)="open()">查看详情</a>
    </app-ribbon-item>
    
    <app-ribbon-item>
      <div showFullText style="max-width: calc(100% - 100px);">
        <span class="h-10 leading-[40px]">特别说明：端内营销和转化效率乘客侧只考察自有渠道的数据表现。</span>
      </div>
      <a class="ribbon-link">查看详情</a>
    </app-ribbon-item>
  </app-ribbon>
</div>

<div class="flex justify-center">
  <div class="max-w-(--breakpoint-2xl) flex-1 min-w-0">
    <app-metrics-overview id="anchor-overview" />
  
    @if (service.sourceType() === 'tencent') {
      <app-funnel-order id="anchor-funnel-order" />
      <app-influence-flow id="anchor-influence-flow" />
    }
    @else {
      @defer (on viewport) {
        <app-external-promotion id="anchor-external" />
      } @placeholder {
        <div id="anchor-external" class="relative w-full aspect-16/10">
          <app-progress-loader />
        </div>
      }

    
      @defer (on viewport) {
        <app-internal-marketing id="anchor-internal" />
      } @placeholder {
        <div id="anchor-internal" class="relative w-full aspect-16/8">
          <app-progress-loader />
        </div>
      }


      @defer (on viewport) {
        <app-conversion id="anchor-conversion" />
      } @placeholder {
        <div id="anchor-conversion" class="relative w-full aspect-16/11">
          <app-progress-loader />
        </div>
      }
    }
  </div>


  <div class="tools-bar">
    <app-selfhelp-btn />

    <div class="flex-1 flex flex-col justify-center">
      <nz-anchor [nzBounds]="200" [nzTargetOffset]="50">
        @if (service.sourceType() !== 'tencent') {
          <nz-link nzTitle="总览" nzHref="#anchor-overview"></nz-link>
          <nz-link nzTitle="外投" nzHref="#anchor-external"></nz-link>
          <nz-link nzTitle="营销" nzHref="#anchor-internal"></nz-link>
          <nz-link nzTitle="转化" nzHref="#anchor-conversion"></nz-link>
        }
        @else {
          <nz-link nzTitle="总览" nzHref="#anchor-overview"></nz-link>
          <nz-link nzTitle="转化" nzHref="#anchor-funnel-order"></nz-link>
          <nz-link nzTitle="流转" nzHref="#anchor-influence-flow"></nz-link>
        }
      </nz-anchor>
    </div>

    <app-back-top />
  </div>
</div>