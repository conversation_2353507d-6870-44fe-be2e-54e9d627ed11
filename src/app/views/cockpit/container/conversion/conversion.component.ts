import { FormsModule } from '@angular/forms';
import { AfterViewInit, ChangeDetectionStrategy, Component, computed, inject } from '@angular/core';
import { NzPopoverModule } from 'ng-zorro-antd/popover';

import { CockpitService } from '@views/cockpit/services';
import { BuriedPointService, BuriedPointActionType } from '@common/service';
import { HeadingComponent } from '@shared/components/heading';
import { IconTransferComponent } from '@shared/modules/icons';
import { FunnelOrderComponent } from './funnel-order/funnel-order.component';
import { DauTrendComponent } from './dau-trend/dau-trend.component';
import { FunnelUserComponent } from './funnel-user/funnel-user.component';


@Component({
  selector: 'app-conversion',
  templateUrl: './conversion.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    FormsModule,
    NzPopoverModule,
    HeadingComponent,
    IconTransferComponent,
    DauTrendComponent,
    FunnelUserComponent,
    FunnelOrderComponent,
  ],
})
export class ConversionComponent implements AfterViewInit {

  service = inject(CockpitService);
  buriedPointService = inject(BuriedPointService);

  /** 是否为全部区域 */
  isAllRideType = computed(() => {
    return this.service.rideType() === 'ALL';
  })
  

  ngAfterViewInit(): void {
    console.log('埋点上报: 曝光 -> 转化效率');

    this.buriedPointService.addStat('dida_dpm_caerus_subject_view', {
      source: 'conversion',
      action_type: BuriedPointActionType.VIEW,
    });
  }

}
