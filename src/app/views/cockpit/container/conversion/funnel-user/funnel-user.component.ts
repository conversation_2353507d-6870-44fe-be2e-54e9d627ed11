import { ChangeDetectionStrategy, Component, signal } from '@angular/core';
import { FormsModule } from '@angular/forms';

import { RadioModule } from '@shared/modules/headless';
import { FunnelPassengerComponent } from './funnel-passenger/funnel-passenger.component';
import { FunnelDriverComponent } from './funnel-driver/funnel-driver.component';


@Component({
  selector: 'app-funnel-user',
  templateUrl: './funnel-user.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    FormsModule,
    RadioModule,
    FunnelPassengerComponent,
    FunnelDriverComponent,
  ],
})
export class FunnelUserComponent {

  type = signal<'passenger' | 'driver'>('passenger');
  
}
