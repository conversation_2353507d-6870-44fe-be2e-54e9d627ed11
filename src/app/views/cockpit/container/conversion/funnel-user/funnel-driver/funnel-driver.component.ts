import { FormsModule } from '@angular/forms';
import { ChangeDetectionStrategy, Component, computed, effect, inject, signal } from '@angular/core';
import { NgClass, NgTemplateOutlet } from '@angular/common';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { finalize } from 'rxjs';

import { SwitchMap } from '@common/decorator';
import { IndicatorModule } from '@shared/components/indicator';
import { ChartFunnelComponent } from '@shared/components/chart';
import { groupBy, isUndefined, transformValueSuffix } from '@common/function';
import { GraphSplineComponent, LineSeries } from '@shared/components/graph';
import { BaseInput, BaseTrendInput, TrendDate, TrendDateOptions } from '@views/cockpit/models';
import { RelationControlService } from '@views/cockpit/services';
import { SkeletonComponent } from '@shared/components/skeleton';
import { IconLoadingComponent } from '@shared/modules/icons';
import { RadioModule } from '@shared/modules/headless';
import { CockpitService } from '@views/cockpit/services';
import { FunnelDriverOutput, FunnelDriverTransformTrendVo } from '@api/caerus/model';
import { CaerusApiService } from '@api/caerus';


@Component({
  selector: 'app-funnel-driver',
  templateUrl: './funnel-driver.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgClass,
    NgTemplateOutlet,
    FormsModule,
    NzSelectModule,
    IndicatorModule,
    RadioModule,
    ChartFunnelComponent,
    GraphSplineComponent,
    IconLoadingComponent,
    SkeletonComponent,
  ],
  providers: [
    RelationControlService,
  ],
})
export class FunnelDriverComponent {

  service = inject(CockpitService);
  apiService = inject(CaerusApiService);
  relationControlService = inject(RelationControlService);
  data = signal<FunnelDriverOutput>(null);
  trendDate = signal<TrendDate>(1);
  trendDateOptions = TrendDateOptions;
  funnelSeries = signal([]);
  lineSeries = signal<LineSeries[]>([]);
  categories = signal<string[]>([]);
  isShowAll = signal<boolean>(true);
  activeIndex = signal<number>(null);
  trendLoading = signal(false);
  loading = signal(false);

  isAllRideType = computed(() => {
    return this.service.rideType() === 'ALL';
  })

  items = computed(() => {
    if (this.data()) {
      const { ucvr, replyRate, replyFinishRate } = this.data().curTrendItem;

      // if (this.isAllRideType()) {
        return [ucvr, replyRate, replyFinishRate];
      // }
    }
    return [];
  })

  constructor() {
    effect(() => {
      const date = this.service.date();
      const area = this.service.area();
      const rideType = this.service.rideType();

      this.loading.set(true);
      this.fetchFunnelDriver({ date, area, rideType });
    });

    effect(() => {
      const date = this.service.date();
      const area = this.service.area();
      const rideType = this.service.rideType();
      const trendDate = this.trendDate();
      
      this.trendLoading.set(true);
      this.fetchFunnelDriverTrend({ date, trendDate, area, rideType });
    });
  }


  @SwitchMap()
  private fetchFunnelDriver(body: BaseInput) {
    return this.apiService.fetchFunnelDriver(body).pipe(
      finalize(() => {
        setTimeout(() => {
          this.loading.set(false);
        }, 0);
      })
    ).subscribe(res => {
      if (!res.data) {
        return;
      }

      const { funnel: { dau, orderReceiveCal, orderCompleteCal }, curTrendItem } = res.data;
      const { ucvr, replyRate, replyFinishRate } = curTrendItem;
      const series = [
        { name: '下单量', leftLabel: '接单车主数',  rightLabel: '车主接单率', value: orderReceiveCal },
        { name: '接单量', leftLabel: '完单车主数',  rightLabel: '接单完单率(车主)', value: orderCompleteCal },
      ];

      if (this.isAllRideType()) {
        series.unshift({ name: 'DAU', leftLabel: '车主DAU', rightLabel: 'DAU', value: dau },);
      }

      res.data.curTrendItem.ucvr.value = transformValueSuffix(ucvr.value);
      res.data.curTrendItem.replyRate.value = transformValueSuffix(replyRate.value);
      res.data.curTrendItem.replyFinishRate.value = transformValueSuffix(replyFinishRate.value);
      
      this.funnelSeries.set(series);
      this.data.set(res.data);
    });
  }


  @SwitchMap()
  private fetchFunnelDriverTrend(body: BaseTrendInput) {
    return this.apiService.fetchFunnelDriverTrend(body).pipe(
      finalize(() => {
        setTimeout(() => {
          this.trendLoading.set(false);
        }, 0);
      })
    ).subscribe(res => {
      if (!res.data) {
        return;
      }

      const values = groupBy<FunnelDriverTransformTrendVo>(res.data, 'year');
      const series = [];
      const years = [] as string[];
      const seriesObj = {
        ucvr: [],
        replyFinishRate: [],
        replyRate: [],
      };

      Object.keys(values).forEach(key => {
        const items = values[key].sort((a, b) => Number(a.month) - Number(b.month));
        const ucvr = items.map(item => isUndefined(item?.ucvr) ? null : item?.ucvr);
        const replyFinishRate = items.map(item => isUndefined(item?.replyFinishRate) ? null : item?.replyFinishRate);
        const replyRate = items.map(item => isUndefined(item?.replyRate) ? null : item?.replyRate);

        years.push(...items.map(item => `${item.year}年${item.month}月`));        
        seriesObj.ucvr.push(...ucvr);
        seriesObj.replyFinishRate.push(...replyFinishRate);
        seriesObj.replyRate.push(...replyRate);
      })

      if (this.isAllRideType()) {
        series.push(new LineSeries('车主访问完单率(UCVR)', seriesObj.ucvr));
      }
      series.push(new LineSeries('车主接单率', seriesObj.replyRate));
      series.push(new LineSeries('接单完单率(车主)', seriesObj.replyFinishRate));

      this.categories.set(years);
      this.lineSeries.set(series);
    })
  }


  handleClick(index: number) {
    if (index === this.activeIndex()) {
      this.isShowAll.update(state => !state);
    } else {
      this.isShowAll.set(false);
    }

    if (this.isShowAll()) {
      this.activeIndex.set(null);
    } else {
      this.activeIndex.set(index);
    }

    this.relationControlService.emit({ index, isShowAll: this.isShowAll() });
  }

}
