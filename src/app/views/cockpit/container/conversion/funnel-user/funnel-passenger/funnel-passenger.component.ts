import { FormsModule } from '@angular/forms';
import { ChangeDetectionStrategy, Component, computed, effect, inject, signal } from '@angular/core';
import { NgClass, NgTemplateOutlet } from '@angular/common';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { finalize } from 'rxjs';

import { SwitchMap } from '@common/decorator';
import { IndicatorModule } from '@shared/components/indicator';
import { ChartFunnelComponent } from '@shared/components/chart';
import { groupBy, isUndefined, transformValueSuffix } from '@common/function';
import { GraphSplineComponent, LineSeries } from '@shared/components/graph';
import { BaseInput, BaseTrendInput, FunnelPassengerOutput, FunnelPassengerTransformTrendVo, TrendDate, TrendDateOptions } from '@views/cockpit/models';
import { RelationControlService } from '@views/cockpit/services';
import { SkeletonComponent } from '@shared/components/skeleton';
import { IconLoadingComponent } from '@shared/modules/icons';
import { RadioModule } from '@shared/modules/headless';
import { CockpitService } from '@views/cockpit/services';
import { CockpitApiService } from '@views/cockpit/api';



@Component({
  selector: 'app-funnel-passenger',
  templateUrl: './funnel-passenger.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgClass,
    NgTemplateOutlet,
    FormsModule,
    NzSelectModule,
    IndicatorModule,
    RadioModule,
    ChartFunnelComponent,
    GraphSplineComponent,
    IconLoadingComponent,
    SkeletonComponent,
  ],
  providers: [
    RelationControlService,
  ]
})
export class FunnelPassengerComponent {

  service = inject(CockpitService);
  apiService = inject(CockpitApiService);
  relationControlService = inject(RelationControlService);
  data = signal<FunnelPassengerOutput>(null);
  trendDate = signal<TrendDate>(1);
  trendDateOptions = TrendDateOptions;
  funnelSeries = signal([]);
  lineSeries = signal<LineSeries[]>([]);
  categories = signal<string[]>([]);
  isShowAll = signal<boolean>(true);
  activeIndex = signal<number>(null);
  trendLoading = signal(false);
  loading = signal(false);

  isAllRideType = computed(() => {
    return this.service.rideType() === 'ALL';
  })

  items = computed(() => {
    if (this.data()) {
      const { ucvr, orderFinishRate, replyFinishRate, customerStickiness } = this.data().curTrendItem;

      if (this.isAllRideType()) {
        return [ucvr, orderFinishRate, replyFinishRate, customerStickiness];
      } else {
        return [orderFinishRate, replyFinishRate];
      }
    }
    return [];
  })

  constructor() {
    effect(() => {
      const date = this.service.date();
      const area = this.service.area();
      const rideType = this.service.rideType();

      this.loading.set(true);
      this.fetchFunnelPassenger({ date, area, rideType });
    });

    effect(() => {
      const date = this.service.date();
      const area = this.service.area();
      const rideType = this.service.rideType();
      const trendDate = this.trendDate();
      
      this.trendLoading.set(true);
      this.fetchFunnelPassengerTrend({ date, trendDate, area, rideType });
    });
  }


  @SwitchMap()
  private fetchFunnelPassengerTrend(body: BaseTrendInput) {
    return this.apiService.fetchFunnelPassengerTrend(body).pipe(
      finalize(() => {
        setTimeout(() => {
          this.trendLoading.set(false);
        }, 0);
      })
    ).subscribe(res => {
      if (!res.data) {
        return;
      }

      const values = groupBy<FunnelPassengerTransformTrendVo>(res.data, 'year');
      const series = [];
      const years = [] as string[];
      const seriesObj = {
        customerStickiness: [],
        orderFinishRate: [],
        replyFinishRate: [],
        ucvr: [],
      };

      Object.keys(values).forEach(key => {
        const items = values[key].sort((a, b) => Number(a.month) - Number(b.month));
        const customerStickiness = items.map(item => isUndefined(item?.customerStickiness) ? null : item?.customerStickiness);
        const orderFinishRate = items.map(item => isUndefined(item?.orderFinishRate) ? null : item?.orderFinishRate);
        const replyFinishRate = items.map(item => isUndefined(item?.replyFinishRate) ? null : item?.replyFinishRate);
        const ucvr = items.map(item => isUndefined(item?.ucvr) ? null : item?.ucvr);

        years.push(...items.map(item => `${item.year}年${item.month}月`));        
        seriesObj.customerStickiness.push(...customerStickiness);
        seriesObj.orderFinishRate.push(...orderFinishRate);
        seriesObj.replyFinishRate.push(...replyFinishRate);
        seriesObj.ucvr.push(...ucvr);        
      })

      if (this.isAllRideType()) {
        series.push(new LineSeries('乘客访问完单率(UCVR)', seriesObj.ucvr));
      }
      series.push(new LineSeries('下单完单率(乘客)', seriesObj.orderFinishRate));
      series.push(new LineSeries('接单完单率(乘客)', seriesObj.replyFinishRate));

      if (this.isAllRideType()) {
        series.push(new LineSeries('DAU/MAU(用户黏性)', seriesObj.customerStickiness));
      }

      this.categories.set(years);
      this.lineSeries.set(series);
    })
  }

  
  @SwitchMap()
  private fetchFunnelPassenger(body: BaseInput) {
    return this.apiService.fetchFunnelPassenger(body).pipe(
      finalize(() => {
        setTimeout(() => {
          this.loading.set(false);
        }, 0);
      })
    ).subscribe(res => {
      if (!res.data) {
        return;
      }
      
      const { funnel: { dau, orderCal, orderReceiveCal, orderCompleteCal}, curTrendItem } = res.data;
      const { ucvr, replyFinishRate, orderFinishRate, customerStickiness } = curTrendItem;
      const series = [
        { name: '下单量', leftLabel: '下单乘客数',  rightLabel: '乘客下单率', value: orderCal },
        { name: '接单量', leftLabel: '被接单乘客数',  rightLabel: '下单接单率(乘客)', value: orderReceiveCal },
        { name: '完单量', leftLabel: '完单乘客数',  rightLabel: '接单完单率(乘客)', value: orderCompleteCal },
      ];

      if (this.isAllRideType()) {
        series.unshift({ name: 'DAU',   leftLabel: '乘客DAU', rightLabel: 'DAU',      value: dau },);
      }

      res.data.curTrendItem.ucvr.value = transformValueSuffix(ucvr.value);
      res.data.curTrendItem.orderFinishRate.value = transformValueSuffix(orderFinishRate.value);
      res.data.curTrendItem.replyFinishRate.value = transformValueSuffix(replyFinishRate.value);
      res.data.curTrendItem.customerStickiness.value = transformValueSuffix(customerStickiness.value);
      
      this.funnelSeries.set(series);
      this.data.set(res.data);
    })
  }


  handleClick(index: number) {
    if (index === this.activeIndex()) {
      this.isShowAll.update(state => !state);
    } else {
      this.isShowAll.set(false);
    }

    if (this.isShowAll()) {
      this.activeIndex.set(null);
    } else {
      this.activeIndex.set(index);
    }

    this.relationControlService.emit({ index, isShowAll: this.isShowAll() });
  }

}
