<div class="flex flex-col gap-2.5 p-5">
  <header class="px-5 relative">
    <h3 class="mb-0 font-black">用户转化漏斗</h3>
    
    <div class="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2">
      <app-radio-group [(ngModel)]="type" class="w-80 flex items-start gap-x-0.5 p-0.5 bg-[#ebebec] rounded-lg">
        <app-radio class="dida-radio-new" activeClass="active" value="passenger">乘客视角</app-radio>
        <app-radio class="dida-radio-new" activeClass="active" value="driver">车主视角</app-radio>
        <app-radio-thumb [offsetX]="2" class="bg-linear-to-r from-[#465867] to-[#1E2C3A] rounded-md" />
      </app-radio-group>
    </div>
  </header>

  @switch (type()) {
    @case ('passenger') { <app-funnel-passenger /> }
    @case ('driver') { <app-funnel-driver /> }
  }
</div>