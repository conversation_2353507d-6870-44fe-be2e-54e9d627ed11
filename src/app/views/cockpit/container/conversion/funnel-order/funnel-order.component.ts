import { NgClass, NgTemplateOutlet } from '@angular/common';
import {
  ChangeDetectionStrategy,
  Component,
  computed,
  effect,
  inject,
  signal,
} from '@angular/core';
import { FormsModule } from '@angular/forms';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { finalize } from 'rxjs';

import { SwitchMap } from '@common/decorator';
import { groupBy, isUndefined, transformValueSuffix } from '@common/function';
import { IndicatorModule } from '@shared/components/indicator';
import { ChartFunnelComponent } from '@shared/components/chart';
import {
  BaseInput,
  BaseTrendInput,
  FunnelOrderOutput,
  FunnelOrderTransformItem,
  FunnelOrderTransformTrendVo,
  TrendDate,
  TrendDateOptions,
} from '@views/cockpit/models';
import { GraphSplineComponent, LineSeries } from '@shared/components/graph';
import { RelationControlService } from '@views/cockpit/services';
import { SkeletonComponent } from '@shared/components/skeleton';
import { IconLoadingComponent } from '@shared/modules/icons';
import { CockpitApiService } from '@views/cockpit/api';
import { CockpitService } from '@views/cockpit/services';

@Component({
  selector: 'app-funnel-order',
  templateUrl: './funnel-order.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgClass,
    NgTemplateOutlet,
    FormsModule,
    NzSelectModule,
    IndicatorModule,
    ChartFunnelComponent,
    GraphSplineComponent,
    IconLoadingComponent,
    SkeletonComponent,
  ],
  providers: [RelationControlService],
})
export class FunnelOrderComponent {
  service = inject(CockpitService);
  apiService = inject(CockpitApiService);
  relationControlService = inject(RelationControlService);
  data = signal<FunnelOrderOutput>(null);
  trendDate = signal<TrendDate>(1);
  trendDateOptions = TrendDateOptions;
  funnelSeries = signal([]);
  lineSeries = signal<LineSeries[]>([]);
  categories = signal<string[]>([]);
  isShowAll = signal<boolean>(true);
  activeIndex = signal<number>(null);
  trendLoading = signal(false);
  loading = signal(false);

  items = computed(() => {
    if (this.data()) {
      const { orderFinishRate, replyFinishRate, orderReplyRate } =
        this.data().curTrendItem;

      return [orderFinishRate, replyFinishRate, orderReplyRate];
    }
    return [];
  });

  constructor() {
    effect(() => {
      const date = this.service.date();
      const area = this.service.area();
      const rideType = this.service.rideType();

      this.loading.set(true);
      this.fetchFunnelOrder({ date, area, rideType });
    });

    effect(() => {
      const date = this.service.date();
      const area = this.service.area();
      const rideType = this.service.rideType();
      const trendDate = this.trendDate();

      this.trendLoading.set(true);
      this.fetchFunnelOrderTrend({ date, trendDate, area, rideType });
    });
  }

  @SwitchMap()
  private fetchFunnelOrderTrend(body: BaseTrendInput) {
    return this.apiService
      .fetchFunnelOrderTrend(body)
      .pipe(
        finalize(() => {
          setTimeout(() => {
            this.trendLoading.set(false);
          }, 0);
        })
      )
      .subscribe((res) => {
        if (!res.data) {
          return;
        }

        const values = groupBy<FunnelOrderTransformTrendVo>(res.data, 'year');
        const series = [];
        const years = [] as string[];
        const seriesObj: { [key in keyof FunnelOrderTransformItem]: number[] } =
          {
            orderFinishRate: [],
            replyFinishRate: [],
            orderReplyRate: [],
          };

        Object.keys(values).forEach((key) => {
          const items = values[key].sort(
            (a, b) => Number(a.month) - Number(b.month)
          );
          const orderFinishRate = items.map((item) =>
            isUndefined(item?.orderFinishRate) ? null : item?.orderFinishRate
          );
          const replyFinishRate = items.map((item) =>
            isUndefined(item?.replyFinishRate) ? null : item?.replyFinishRate
          );
          const orderReplyRate = items.map((item) =>
            isUndefined(item?.orderReplyRate) ? null : item?.orderReplyRate
          );

          years.push(...items.map((item) => `${item.year}年${item.month}月`));
          seriesObj.orderFinishRate.push(...orderFinishRate);
          seriesObj.replyFinishRate.push(...replyFinishRate);
          seriesObj.orderReplyRate.push(...orderReplyRate);
        });

        series.push(new LineSeries('下单完单率', seriesObj.orderFinishRate));
        series.push(new LineSeries('接单完单率', seriesObj.replyFinishRate));
        series.push(new LineSeries('下单接单率', seriesObj.orderReplyRate));

        this.categories.set(years);
        this.lineSeries.set(series);
      });
  }

  @SwitchMap()
  private fetchFunnelOrder(body: BaseInput) {
    return this.apiService
      .fetchFunnelOrder(body)
      .pipe(
        finalize(() => {
          setTimeout(() => {
            this.loading.set(false);
          }, 0);
        })
      )
      .subscribe((res) => {
        if (!res.data) {
          return;
        }

        const {
          funnel: { orderCnt, orderReplyCal, orderFinishCal },
          curTrendItem,
        } = res.data;
        const { orderFinishRate, replyFinishRate, orderReplyRate } =
          curTrendItem;
        const series = [
          {
            name: '下单量',
            leftLabel: '下单量',
            rightLabel: '下单量',
            value: orderCnt,
          },
          {
            name: '接单量',
            leftLabel: '接单量',
            rightLabel: '下单接单率',
            value: orderReplyCal,
          },
          {
            name: '完单量',
            leftLabel: '完单量',
            rightLabel: '接单完单率',
            value: orderFinishCal,
          },
        ];

        res.data.curTrendItem.orderFinishRate.value = transformValueSuffix(
          orderFinishRate.value
        );
        res.data.curTrendItem.replyFinishRate.value = transformValueSuffix(
          replyFinishRate.value
        );
        res.data.curTrendItem.orderReplyRate.value = transformValueSuffix(
          orderReplyRate.value
        );

        this.funnelSeries.set(series);
        console.log('funnelSeries', this.funnelSeries());
        this.data.set(res.data);
      });
  }

  handleClick(index: number) {
    if (index === this.activeIndex()) {
      this.isShowAll.update((state) => !state);
    } else {
      this.isShowAll.set(false);
    }

    if (this.isShowAll()) {
      this.activeIndex.set(null);
    } else {
      this.activeIndex.set(index);
    }

    this.relationControlService.emit({ index, isShowAll: this.isShowAll() });
  }
}
