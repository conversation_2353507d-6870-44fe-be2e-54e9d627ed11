import { NgClass } from '@angular/common';
import { ChangeDetectionStrategy, Component, effect, inject, signal } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { finalize } from 'rxjs';

import { SwitchMap } from '@common/decorator';
import { CockpitService } from '@views/cockpit/services';
import { groupBy, isUndefined } from '@common/function';
import { IconLoadingComponent } from '@shared/modules/icons';
import { GraphSplineComponent, LineSeries } from '@shared/components/graph';
import { BaseTrendInput, TrendDate, TrendDateOptions } from '@views/cockpit/models';
import { CockpitApiService } from '@views/cockpit/api';
import { DauTrendVo } from '@views/cockpit/models';


@Component({
  selector: 'app-dau-trend',
  template: `
    <div class="relative h-96">
      <div [ngClass]="{'opacity-0 pointer-events-none': !loading()}" class="absolute inset-0 z-10 transition-opacity flex items-center justify-center w-full h-full rounded-2xl bg-neutral-50 text-neutral-400 text-lg">
        <LoadingIcon />
      </div>

      <div class="px-3 absolute right-0 top-3 z-10">
        <nz-select [(ngModel)]="trendDate" nzSize="small">
          @for (item of trendDateOptions; track $index) {
            <nz-option [nzValue]="item.value" [nzLabel]="item.label"></nz-option>
          }
        </nz-select>
      </div>

      <app-graph-spline suffix="万" [categories]="categories()" [value]="lineSeries()" />
    </div>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgClass,
    FormsModule,
    NzSelectModule,
    GraphSplineComponent,
    IconLoadingComponent,
  ],
})
export class DauTrendComponent {

  service = inject(CockpitService);
  apiService = inject(CockpitApiService);

  trendDate = signal<TrendDate>(1);
  trendDateOptions = TrendDateOptions;
  lineSeries = signal<LineSeries[]>([]);
  categories = signal<string[]>([]);
  loading = signal(false);


  constructor() {
    effect(() => {
      const date = this.service.date();
      const area = this.service.area();
      const rideType = this.service.rideType();
      const trendDate = this.trendDate();

      this.loading.set(true);
      this.fetchDauTrend({ date, trendDate, area, rideType });
    });
  }


  @SwitchMap()
  private fetchDauTrend(body: BaseTrendInput) {
    return this.apiService.fetchDauTrend(body).pipe(
      finalize(() => {
        setTimeout(() => {
          this.loading.set(false);
        }, 0);
      })
    ).subscribe(res => {
      if (!res.data) {
        return;
      }

      const values = groupBy<DauTrendVo>(res.data, 'year');
      const series = [];
      const years = [] as string[];
      const seriesObj = {
        dau: [],
        passengerDau: [],
        driverDau: [],
      };

      Object.keys(values).forEach(key => {
        const items = values[key].sort((a, b) => Number(a.month) - Number(b.month));
        const dau = items.map(item => isUndefined(item?.dau) ? null : item?.dau);
        const passengerDau = items.map(item => isUndefined(item?.passengerDau) ? null : item?.passengerDau);
        const driverDau = items.map(item => isUndefined(item?.driverDau) ? null : item?.driverDau);

        years.push(...items.map(item => `${item.year}年${item.month}月`));        
        seriesObj.dau.push(...dau);
        seriesObj.passengerDau.push(...passengerDau);
        seriesObj.driverDau.push(...driverDau);
      });

      series.push(new LineSeries('DAU', seriesObj.dau));
      series.push(new LineSeries('乘客DAU', seriesObj.passengerDau));
      series.push(new LineSeries('车主DAU', seriesObj.driverDau));

      this.categories.set(years);
      this.lineSeries.set(series);
    })
  }


}
