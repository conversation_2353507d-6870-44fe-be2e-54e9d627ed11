import { AsyncPipe, NgClass, NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, Injector, computed, effect, inject, signal } from '@angular/core';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { finalize } from 'rxjs';

import { trace } from '@common/const';
import { ModalService } from '@core/modal';
import { ReplaceByPipe } from '@shared/pipes/replace-by';
import { SkeletonComponent } from '@shared/components/skeleton';
import { GraphMiniAreaComponent } from '@shared/components/graph';
import { IconCollectComponent, IconCooperationComponent, IconExpandComponent, IconOrderComponent, IconTaxiComponent, IconUsersComponent } from '@shared/modules/icons';
import { IndicatorModule } from '@shared/components/indicator';
import { MetricsDetailComponent } from '@views/cockpit/components/metrics-detail';
import { BaseDataOutput, BaseInput, OverviewMetricsOutput } from '@views/cockpit/models';
import { CockpitService } from '@views/cockpit/services';
import { IncrementPipe } from '@shared/pipes/increment';
import { CockpitApiService } from '@views/cockpit/api';
import { SwitchMap } from '@common/decorator';
import { BuriedPointService } from '@common/service';


@Component({
  selector: 'app-core-indicators',
  templateUrl: './core-indicators.component.html',
  styleUrl: './core-indicators.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgClass,
    AsyncPipe,
    NgTemplateOutlet,
    NzToolTipModule,
    IndicatorModule,
    IconExpandComponent,
    IconTaxiComponent,
    IconOrderComponent,
    IconCooperationComponent,
    IconCollectComponent,
    IconUsersComponent,
    GraphMiniAreaComponent,
    SkeletonComponent,
    ReplaceByPipe,
    IncrementPipe,
  ],
})
export class CoreIndicatorsComponent {

  readonly injector = inject(Injector);
  readonly service = inject(CockpitService);
  readonly apiService = inject(CockpitApiService);
  readonly buriedPointService = inject(BuriedPointService);
  readonly modalService = inject(ModalService);

  data = signal<OverviewMetricsOutput>(null);
  loading = signal(true);
  today = new Date();

  targetVisible = computed(() => {
    return (
      this.service.area() === '全国' &&
      this.service.rideType() === 'ALL'
    );
  })
  
  items = computed(() => {
    let arr = [];

    if (this.data()) {
      const {
        finishOrderCnt, orderCnt, finishDriverUnt, commissionAmtRevenue, marketingExpenses,
        tencentFinishOrderCnt, tencentOrderCnt, tencentFinishDriverUnt, tencentCommissionAmtRevenue, tencentNewAddPassengerUnt,
        selfFinishOrderCnt, selfOrderCnt, selfFinishDriverUnt, selfCommissionAmtRevenue, selfMarketingExpenses,
      } = this.data();
      
      switch (this.service.sourceType()) {
        case 'all':
          arr = [finishOrderCnt, orderCnt, finishDriverUnt, commissionAmtRevenue, marketingExpenses];
          break;
        case 'tencent':
          arr = [tencentFinishOrderCnt, tencentOrderCnt, tencentFinishDriverUnt, tencentCommissionAmtRevenue, tencentNewAddPassengerUnt];
          break;
        case 'self':
          arr = [selfFinishOrderCnt, selfOrderCnt, selfFinishDriverUnt, selfCommissionAmtRevenue, selfMarketingExpenses];
          break;
        default:
          arr = [];
          break;
      }
    }
    
    if (
      this.service.area() !== '全国' ||
      this.service.rideType() !== 'ALL'
    ) {
      return arr.slice(0,4);
    }
    
    return arr;
  })


  constructor() {
    effect(() => {
      const date = this.service.date();
      const area = this.service.area();
      const rideType = this.service.rideType();
      const source = this.service.sourceType();      

      this.loading.set(true);
      this.fetchMetricsOverview({ date, source, area, rideType });
    })
  }


  @SwitchMap()
  private fetchMetricsOverview(body: BaseInput) {
    return this.apiService.fetchMetricsOverview(body).pipe(
      finalize(() => {
        setTimeout(() => {
          this.loading.set(false);
        }, 0);
      })
    ).subscribe(res => {
      if (!res.data) {
        return;
      }

      this.data.set(res.data);
    })
  }


  replaceFn = (title: string) => {
    if (title) {
      return title.replace(/(\(腾讯外+.\)$)|(\(自有渠+.\)$)/, '');
    }
    return title;
  }
  
  
  openMetricsDetail(data: BaseDataOutput) {
    const componentRef = this.modalService.open(MetricsDetailComponent, null, this.injector);
    const area = this.service.area();
    const ride_type = this.service.rideType();
    const source_type = this.service.sourceType();
    const card_name = data.aliasName;

    componentRef.setInput('title', card_name);
    componentRef.setInput('metrics', [ data ]);

    trace(`埋点上报: 点击 -> 概览卡片右上角 -> ${card_name}`);
    this.buriedPointService.addStat('dida_dpm_caerus_card_enlarge_click', {
      card_name,
      source_type,
      area,
      ride_type,
    });
  }
  
}
