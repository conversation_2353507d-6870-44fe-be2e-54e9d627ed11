<div class="relative">
  <div [ngClass]="{'opacity-0 pointer-events-none z-0': !loading(), 'z-20': loading()}" class="absolute inset-0 flex gap-x-3 w-full overflow-x-auto transition-opacity">
    <ng-container *ngTemplateOutlet="skeleton"></ng-container>
    <ng-container *ngTemplateOutlet="skeleton"></ng-container>
    <ng-container *ngTemplateOutlet="skeleton"></ng-container>
    <ng-container *ngTemplateOutlet="skeleton"></ng-container>
    @if (targetVisible()) {
      <ng-container *ngTemplateOutlet="skeleton"></ng-container>
    }
  </div>

  <app-indicator-card-group gap="gap-x-3" class="relative z-10 w-full min-h-64.5 overflow-x-auto">
    @for (item of items(); track item) {
      <ng-container *ngTemplateOutlet="cardTemplate; context: { $implicit: item, index: $index }"></ng-container>
    }
  </app-indicator-card-group>
</div>


<ng-template #iconTemplate let-index>
  @switch (index) {
    @case (0) { <CooperationIcon /> }
    @case (1) { <OrderIcon /> }
    @case (2) { <TaxiIcon /> }
    @case (3) { <CollectIcon /> }
    @case (4) { <UsersIcon /> }
  }
</ng-template>


<ng-template #cardTemplate let-target let-index="index">
  <app-indicator-card class="snap-start scroll-mx-4 min-w-64! flex-1 p-3.5 bg-neutral-100 rounded-2xl">
    <app-indicator-header>
      <app-indicator-avatar class="text-4xl leading-[0]">
        <ng-container *ngTemplateOutlet="iconTemplate; context: { $implicit: index }"></ng-container>
      </app-indicator-avatar>
      <ng-template #metricsTitleTemplate>{{target?.aliasName}} <span class="text-xs opacity-30 px-1">({{target?.metricsName}})</span></ng-template>
      <app-indicator-title nzPopoverPlacement="topLeft" [nzPopoverTitle]="metricsTitleTemplate" [nzPopoverContent]="target?.metricsDescription">{{target?.aliasName | replaceBy: replaceFn}}</app-indicator-title>
      <app-indicator-subtitle>{{target?.avgType}}</app-indicator-subtitle>
      <span class="flex-1 min-w-0"></span>
      <ExpandIcon class="expand-btn" iconBtn nz-tooltip="查看" (click)="openMetricsDetail(target)" />
    </app-indicator-header>
    <app-indicator-content>
      <app-indicator-value [value]="(target?.value | increment | async) ?? '-'" />
      <app-indicator-compare-group gap="gap-1">
        <app-indicator-compare label="月环比" [value]="target?.quarterOnQuarterGrowth" />
        <app-indicator-compare label="年同比" [value]="target?.yearOnYearGrowth" />
      </app-indicator-compare-group>
      <app-indicator-graph class="block h-20 bg-neutral-200/0 rounded-lg">
        <app-graph-mini-area [value]="target?.trendVo" />
      </app-indicator-graph>
    </app-indicator-content>
  </app-indicator-card>
</ng-template>


<ng-template #skeleton>
  <app-indicator-card class="snap-start scroll-mx-4 min-w-64! flex-1 p-3.5 bg-neutral-100 rounded-2xl">
    <app-indicator-header>
      <app-indicator-avatar>
        <app-skeleton circle class="w-9 h-9" />
      </app-indicator-avatar>
      <app-indicator-title>
        <app-skeleton class="w-24 h-5" />
      </app-indicator-title>
      <span class="flex-1 min-w-0"></span>
    </app-indicator-header>
    <app-indicator-content>
      <app-skeleton class="w-1/2 h-7.5 mx-auto" />
      <app-indicator-compare-group>
        <app-skeleton class="flex-1 h-4" />
        <app-skeleton class="flex-1 h-4" />
      </app-indicator-compare-group>
      <div class="flex gap-x-2">
        <app-skeleton class="w-9 h-3" />
        <div class="flex-1">
          <app-skeleton class="h-3" />
          <div class="h-4 flex justify-end items-center">
            <app-skeleton class="w-14 h-2" />
          </div>
        </div>
      </div>
      <app-indicator-graph class="block h-20 bg-neutral-200/0 rounded-lg">
        <app-skeleton class="w-full h-full" />
      </app-indicator-graph>
    </app-indicator-content>
  </app-indicator-card>
</ng-template>