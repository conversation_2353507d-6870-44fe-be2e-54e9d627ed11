import { ChangeDetectionStrategy, Component } from '@angular/core';
import { NzPopoverModule } from 'ng-zorro-antd/popover';

import { IconHelpFillComponent } from '@shared/modules/icons';
import { FluctuateItemComponent } from './fluctuate-item/fluctuate-item.component';


@Component({
  selector: 'app-fluctuate-overview',
  template: `
    <header class="flex items-center gap-x-1 p-5">
      <span class="text-nowrap font-black">波动概览</span>
      <HelpFillIcon nz-popover nzPopoverPlacement="right" [nzPopoverArrowPointAtCenter]="true" [nzPopoverContent]="helpTemplate" class="text-neutral-400 cursor-help" />
    </header>

    <ng-template #helpTemplate>
      <div class="text-xs leading-normal">
        <div>
          波动概览模块展示的均为不同维度下，月日均完单量指标的涨跌幅和对大盘的影响（即波动占比），月环比、年同比为涨跌幅的计算口径。<br /><br />
          以成都为例来说明波动占比：<br />
          成都的月环比波动占比=（成都本月完单量-成都上月完单量）/大盘上月的完单量；<br /><br />
          假如成都的波动占比按照月环比计算是+15%，意味着在其他城市不变的情况下，成都相对上个月的完单量波动，会让大盘比上个月增长15%；<br />
        </div>
      </div>
    </ng-template>


    <div class="relative">
      <div class="grid grid-cols-2 xl:grid-cols-4 gap-8">
        <app-fluctuate-item type="1" description="说明：点击图中的数据条，可下钻到对应的区域进行查看。" label="核心区域波动" />
        <app-fluctuate-item type="4" description="说明：点击图中的数据条，可下钻到对应的省份进行查看。" label="省份波动(top 5)" />
        <app-fluctuate-item type="2" description="说明：点击图中的数据条，可下钻到对应的城市进行查看。" label="城市波动(top 5)" />
        <app-fluctuate-item type="3" description="说明：点击图中的数据条，可下钻到对应的订单类型进行查看。" label="城际/市内波动" />
      </div>
    </div>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NzPopoverModule,
    IconHelpFillComponent,
    FluctuateItemComponent,
  ],
})
export class FluctuateOverviewComponent {

}
