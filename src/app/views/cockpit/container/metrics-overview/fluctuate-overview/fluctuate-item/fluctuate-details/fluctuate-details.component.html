<div [@opacityAnimation]="visible()" class="modal-backdrop"></div>

<div class="modal">
  <div class="absolute inset-0 z-0" (click)="close()"></div>
  <div [@modalOutToInAnimation]="visibleState()" (@modalOutToInAnimation.done)="animationDone($event)" class="relative z-10 m-auto">
    <div cdkDragBoundary=".modal" cdkDrag class="modal-content max-w-unset!">
      <header cdkDragHandle class="modal-header">
        <h3 class="modal-title">{{title()}}</h3>
        <span class="text-xs text-neutral-400 scale-90">{{subTitle()}}</span>
        <div class="flex-1"></div>
        <CloseIcon iconBtn class="xl" (click)="close()" />
      </header>
      
      <div class="modal-body space-y-3 max-h-125 overflow-auto">
        <div class="bg-neutral-100 rounded-sm py-3 px-5 text-neutral-400 text-xs leading-5">
          <h4 class="flex items-center gap-x-1.5">
            <CaliberIcon /> 波动占比说明：
          </h4>
      
          <div class="px-6">
            <p>波动占比=（A维度本期完单量-A维度上期完单量）/大盘上期的完单量，反映的是单个维度，如城市、区域、订单类型（城际/市内）等，在某一时间段的波动对大盘的影响；</p>
            <p>波动占比使用完单量作为衡量指标，支持月环比和年同比两种计算口径，以城市维度为例，成都的月环比波动占比=（成都本月完单量-成都上月完单量）/大盘上月的完单量；</p>
            <p class="text-primary">假如成都的波动占比按照月环比计算是+15%，意味着在其他城市不变的情况下，成都相对上个月的完单量波动，会让大盘比上个月增长15%；</p>
          </div>
        </div>

        <div>
          <span class="whitespace-nowrap">波动参照：</span>
          <nz-radio-group [(ngModel)]="dateType">
            <label nz-radio nzValue="month">月环比</label>
            <label nz-radio nzValue="year">年同比</label>
          </nz-radio-group>
        </div>
        
        <div class="w-256 h-150">
          <div class="relative w-4/5 h-full mx-auto">
            @if (option()) {
              <app-graph [options]="option()" />
            }
          </div>
        </div>
      </div>
    </div>
  </div>
</div>