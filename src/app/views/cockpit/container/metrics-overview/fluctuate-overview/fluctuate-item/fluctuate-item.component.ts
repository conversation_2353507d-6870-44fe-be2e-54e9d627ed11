import { Activated<PERSON>out<PERSON>, Router } from '@angular/router';
import { ChangeDetectionStrategy, Component, DestroyRef, effect, inject, Injector, input, model, signal } from '@angular/core';
import { NzRadioModule } from 'ng-zorro-antd/radio';
import { FormsModule } from '@angular/forms';

import { ModalService } from '@core/modal';
import { CockpitService } from '@views/cockpit/services';
import { CockpitApiService } from '@views/cockpit/api';
import { FluctuationInput } from '@views/cockpit/models';
import { GraphComponent, NegativeStack, NegativeStackSeries } from '@shared/components/graph';
import { SkeletonComponent } from '@shared/components/skeleton';
import { IndicatorModule } from '@shared/components/indicator';
import { fillEmpty, getMaxLength } from '@common/function';
import { FluctuateDrillService } from '@views/cockpit/services';
import { BuriedPointService } from '@common/service';
import { FluctuateDetailsComponent } from './fluctuate-details/fluctuate-details.component';


@Component({
  selector: 'app-fluctuate-item',
  template: `
    <app-indicator-card class="flex-1">
      <app-indicator-header>
        <app-indicator-title fontSize="text-sm">{{label()}}</app-indicator-title>
        <app-indicator-subtitle class="flex-1" scale="scale-100">
          @if (type() === '4' || type() === '2') {
            <a class="link text-sm" (click)="handleMore()">更多</a>
          }
        </app-indicator-subtitle>
        
        <app-indicator-extra class="pr-2">
          <nz-radio-group class="scale-90 origin-right" [(ngModel)]="dateType" nzButtonStyle="solid" nzSize="small">
            <label nz-radio-button nzValue="month">月环比</label>
            <label nz-radio-button nzValue="year">年同比</label>
          </nz-radio-group>
        </app-indicator-extra>
      </app-indicator-header>

      <app-indicator-graph class="relative flex items-center justify-center h-56 pb-2 rounded-lg border border-neutral-200 overflow-hidden">
        @if (option()) {
          <app-graph [options]="option()" />
        }
        @else {
          <app-skeleton class="w-full h-full" />
        }

        <div class=" absolute bottom-1 z-10 text-xs text-center text-neutral-400 scale-75 truncate">
          {{description()}}
        </div>
      </app-indicator-graph>
    </app-indicator-card>
  `,
  styleUrl: './fluctuate-item.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: {
    'class': 'contents'
  },
  imports: [
    FormsModule,
    NzRadioModule,
    IndicatorModule,
    SkeletonComponent,
    GraphComponent,
  ],
})
export class FluctuateItemComponent {

  readonly injector = inject(Injector);
  readonly service = inject(CockpitService);
  readonly apiService = inject(CockpitApiService);
  readonly buriedPointService = inject(BuriedPointService);
  readonly fluctuateDrillService = inject(FluctuateDrillService);
  readonly modalService = inject(ModalService);
  readonly destroyRef = inject(DestroyRef);
  readonly route = inject(ActivatedRoute);
  readonly router = inject(Router);

  label = input.required<string>();
  dateType = model<'month' | 'year'>('month');
  type = input.required<'1' | '2' | '3' | '4'>();
  description = input<string>();
  option = signal<NegativeStack>(null);

  constructor() {
    effect(() => {
      const date = this.service.date();
      const area = this.service.area();
      const rideType = this.service.rideType();
      const type = this.type();
      const dateType = this.dateType();
      const dataType = 'top5';
      
      this.fetchFluctuationOverview({ date, area, rideType, type, dateType, dataType });
    })

    effect(() => {
      this.buriedPointService.addStat('dida_dpm_caerus_bore_fluctuation', {
        caliber: this.dateType(),
        source_type: this.type(),
        page_name: 'home',
      });
    })
  }


  fetchFluctuationOverview(body: FluctuationInput) {
    this.option.set(null);

    return this.apiService.fetchFluctuationOverview(body).subscribe(res => {
      if (!res.data) { return console.log(res);}
      
      const chart = new NegativeStack();
      const { negative, forward } = res.data;
      const negativeCategories = negative.map(item => item.dimenValue);
      const forwardCategories  = forward.map(item => item.dimenValue);
      const negativeIncrease   = negative.map(item => item.increase);
      const forwardIncrease    = forward.map(item => item.increase);
      const negativeData       = negative.map(item => ({ y: item.increase, fluctuationRatio: item.fluctuationRatio, ratio: this.dateType() === 'month' ? item.quarterOnQuarterGrowth : item.yearOnYearGrowth }));
      const forwardData        = forward.map(item => ({ y: item.increase, fluctuationRatio: item.fluctuationRatio, ratio: this.dateType() === 'month' ? item.quarterOnQuarterGrowth : item.yearOnYearGrowth }));
      const max = Math.max(
        Math.abs(negativeIncrease[0]), 
        forwardIncrease[0]
      ); // 有时降幅绝对值可能会比涨幅绝对值大，所以需要使用max获取最大值

      chart.yAxis.min = -(max);
      chart.yAxis.max = max;
      
      if (this.type() === '3') {
        chart.setTheme(['#5087ec', '#de868f']);
        chart.plotOptions.series.stacking = undefined;
      }

      const maxLength = getMaxLength(negativeCategories, forwardCategories);

      chart.setCategories(0, fillEmpty(maxLength)(negativeCategories));
      chart.setCategories(1, fillEmpty(maxLength)(forwardCategories));

      chart.setValue([
        new NegativeStackSeries('完单量降幅', negativeData, 0),
        new NegativeStackSeries('完单量涨幅', forwardData,  1),
      ]);

      this.option.set(chart.getOption());

      chart.click.subscribe(({ name: value }) => {
        if (this.type() === '3') {
          this.fluctuateDrillService.rideType.set(value as any);
        } else {
          this.fluctuateDrillService.areaName.set(value);
        }
        
        window.scrollTo({ top: 0, behavior: 'smooth' });

        this.buriedPointService.addStat('dida_dpm_caerus_drill_fluctuation', {
          source_type: this.type(),
          area_name: value,
          page_name: 'home',
          enter_type: 'graph'
        });
      })
    })
  }


  handleMore() {
    const componentRef = this.modalService.open(FluctuateDetailsComponent, null, this.injector)
    const type = this.type();
    const title = type === '4' ? '省份波动' : '城市波动';
    const dateType = this.dateType();

    if (type === '2') {
      componentRef.setInput('subTitle', '注：此处仅展示完单量波动排名前20的城市。');
    }

    componentRef.setInput('title', title);
    componentRef.setInput('dateType', dateType);
    componentRef.setInput('type', type);
  }

}
