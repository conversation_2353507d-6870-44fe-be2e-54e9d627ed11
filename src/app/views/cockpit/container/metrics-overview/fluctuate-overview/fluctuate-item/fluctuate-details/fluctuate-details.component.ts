import { OverlayModule } from '@angular/cdk/overlay';
import { AfterViewInit, ChangeDetectionStrategy, Component, DestroyRef, effect, inject, input, model, signal } from '@angular/core';
import { AnimationEvent } from '@angular/animations';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { NzRadioModule } from 'ng-zorro-antd/radio';
import { FormsModule } from '@angular/forms';
import { take } from 'rxjs';

import { Animations } from '@common/animation';
import { ModalService, ModalVisibleState } from '@core/modal';
import { GraphComponent, NegativeStack, NegativeStackSeries } from '@shared/components/graph';
import { IconCaliberComponent, IconCloseComponent } from '@shared/modules/icons';
import { CockpitService, FluctuateDrillService } from '@views/cockpit/services';
import { getMaxLength, fillEmpty, isNotUndefinedOrNotNull, transformValueSuffix } from '@common/function';
import { FluctuationInput } from '@views/cockpit/models';
import { CockpitApiService } from '@views/cockpit/api';


@Component({
  selector: 'app-fluctuate-details',
  templateUrl: './fluctuate-details.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: {
    'class': 'flex w-screen h-screen'
  },
  animations: [
    ...Animations.OpacityAnimation,
    ...Animations.ModalOutToInAnimation,
  ],
  imports: [
    DragDropModule,
    OverlayModule,
    FormsModule,
    NzRadioModule,
    IconCloseComponent,
    GraphComponent,
    IconCaliberComponent,
],
})
export class FluctuateDetailsComponent implements AfterViewInit {

  readonly destroyRef = inject(DestroyRef);
  readonly modalService = inject(ModalService);
  readonly fluctuateDrillService = inject(FluctuateDrillService);
  readonly apiService = inject(CockpitApiService);
  readonly service = inject(CockpitService);
  
  title = input<string>();
  subTitle = input<string>();
  type = input<string>();
  dateType = model<'month' | 'year'>('month');
  
  visible = signal(false);
  visibleState = signal<ModalVisibleState>('invisible');
  option = signal<NegativeStack>(null);


  constructor() {
    effect(() => {
      const date = this.service.date();
      const area = this.service.area();
      const rideType = this.service.rideType();
      const type = this.type();
      const dateType = this.dateType();
      const dataType = 'top20';
      
      this.fetchFluctuationOverview({ date, area, rideType, type, dateType, dataType });
    })
  }


  ngAfterViewInit(): void {
    this.visible.set(true);
    this.visibleState.set('visible');
  }


  fetchFluctuationOverview(body: FluctuationInput) {
    this.option.set(null);

    return this.apiService.fetchFluctuationOverview(body).subscribe(res => {
      if (!res.data) { return console.log(res);}
      
      const chart = new NegativeStack();
      const { negative, forward, totalIncrease, totalYearOrMonthGrowth } = res.data;
      const negativeCategories = negative.map(item => item.dimenValue);
      const forwardCategories  = forward.map(item => item.dimenValue);
      // const negativeIncrease   = negative.map(item => item.increase);
      // const forwardIncrease    = forward.map(item => item.increase);
      const negativeData       = negative.map(item => ({ y: item.increase, fluctuationRatio: item.fluctuationRatio, ratio: this.dateType() === 'month' ? item.quarterOnQuarterGrowth : item.yearOnYearGrowth }));
      const forwardData        = forward.map(item => ({ y: item.increase, fluctuationRatio: item.fluctuationRatio, ratio: this.dateType() === 'month' ? item.quarterOnQuarterGrowth : item.yearOnYearGrowth }));
      // const max = Math.max(
      //   Math.abs(totalIncrease ?? 0),
      //   Math.abs(negativeIncrease[0] ?? 0), 
      //   forwardIncrease[0] ?? 0,
      // ); // 有时降幅绝对值可能会比涨幅绝对值大，所以需要使用max获取最大值

      // if (negative.length > 0) {
      //   chart.yAxis.min = -(max);
      // }

      // if (forward.length > 0) {
      //   chart.yAxis.max = max;
      // }

      const maxLength = getMaxLength(negativeCategories, forwardCategories);

      chart.setCategories(0, fillEmpty(maxLength)(negativeCategories));
      chart.setCategories(1, fillEmpty(maxLength)(forwardCategories));

      chart.setValue([
        new NegativeStackSeries('完单量降幅', negativeData, 0),
        new NegativeStackSeries('完单量涨幅', forwardData,  1),
      ]);

      if (isNotUndefinedOrNotNull(totalIncrease)) {
        const trendText  = totalIncrease >= 0 ? '涨幅' : '降幅';
        const labelTitle = `大盘完单量${trendText}`;
        const labelValue = Intl.NumberFormat().format(totalIncrease);
        const labelRatio = `(${transformValueSuffix(totalYearOrMonthGrowth)})`;
        const labelText  = `${labelTitle} ${labelValue} ${labelRatio}`;
        const labelColor = totalIncrease >= 0 ? '#fb7185' : '#34d399';

        chart.subtitle.text = labelText;
        chart.subtitle.style.color = labelColor;
      }

      chart.tooltip.outside = false;
      this.option.set(chart.getOption());

      chart.click.pipe(
        take(1)
      ).subscribe(({ name }) => {
        this.drill(name);
      })
    })
  }


  drill(value: string) {
    // value = value.replace('省', '');
    this.fluctuateDrillService.areaName.set(value);
    this.close();
  }
  
  
  animationDone(event: AnimationEvent): void {
    if (event.fromState === 'visible' && event.toState === 'leave') {
      this.modalService.close();
    }
  }
  

  close() {
    this.visible.set(false);
    this.visibleState.set('leave');
  }
  
}
