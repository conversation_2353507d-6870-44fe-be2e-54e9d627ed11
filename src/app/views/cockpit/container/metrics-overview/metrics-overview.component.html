<div class="px-5">
  <app-heading [title]="'总览'" [helpTemplate]="helpTemplate">
    <MoreSquareIcon ngProjectAs="[icon]" class="text-2xl" />
    <app-radio-group ngProjectAs="[tab]" [(ngModel)]="service.sourceType" class="relative w-80 flex items-start gap-x-0.5 p-0.5 bg-[#ebebec] rounded-lg">
      <app-radio class="dida-radio-new" activeClass="active" value="all">全部渠道</app-radio>
      <app-radio class="dida-radio-new" activeClass="active" value="self">自有渠道</app-radio>
      <app-radio class="dida-radio-new" activeClass="active" value="tencent">腾讯外输</app-radio>
      <app-radio-thumb [offsetX]="2" class="bg-linear-to-r from-[#465867] to-[#1E2C3A] rounded-md" />
    </app-radio-group>
  </app-heading>

  <header class="relative flex items-center p-5">
    <span class="font-black text-base">核心指标</span>
  </header>

  <app-core-indicators />

  @if (service.sourceType() !== 'tencent') {
    <app-key-indicators />

    @if (fluctuateVisible()) {
      <app-fluctuate-overview />
    }
  }
</div>

<ng-template #helpTemplate>
  <div>
    <p class="font-semibold">驾驶舱展示主要分为两个模块：</p>
    <p>（1）公司核心指标总览：总览涉及到的指标有：完单量、下单量、完单车主数、收入、费用。</p>
    <p>（2）各业务线的核心目标达成情况：主要考察的是端外推广、端内营销和转化效率三个模块。</p>
  </div>
  <br>

  <div>
    <p class="font-semibold">特别说明：</p>
    <p>（1）除了人均指标（如人均完单频次、完单车主数等）外，指标使用的均为月日均指标，年同比和月环比就是在这个基础上进行计算的。</p>
    <p>（2）完成率的计算方式，统一采用月累计的方式进行计算，这样更容易观测目标达成的实际进度。</p>
  </div>
</ng-template>