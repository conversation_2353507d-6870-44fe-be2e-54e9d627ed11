import { FormsModule } from '@angular/forms';
import { AfterViewInit, ChangeDetectionStrategy, Component, computed, effect, inject } from '@angular/core';
import { DatePipe } from '@angular/common';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzPopoverModule } from 'ng-zorro-antd/popover';

import { CockpitService } from '@views/cockpit/services';
import { BuriedPointActionType, BuriedPointService } from '@common/service';
import { IconMoreSquareComponent } from '@shared/modules/icons';
import { HeadingComponent } from '@shared/components/heading';
import { RadioModule } from '@shared/modules/headless';

import { CoreIndicatorsComponent } from './core-indicators/core-indicators.component';
import { FluctuateOverviewComponent } from './fluctuate-overview/fluctuate-overview.component';
import { KeyIndicatorsComponent } from './key-indicators/key-indicators.component';


@Component({
  selector: 'app-metrics-overview',
  templateUrl: './metrics-overview.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    FormsModule,
    NzToolTipModule,
    NzPopoverModule,
    RadioModule,
    HeadingComponent,
    IconMoreSquareComponent,
    CoreIndicatorsComponent,
    KeyIndicatorsComponent,
    FluctuateOverviewComponent,
  ],
  providers: [
    DatePipe,
  ]
})
export class MetricsOverviewComponent implements AfterViewInit {

  service = inject(CockpitService);
  buriedPointService = inject(BuriedPointService);

  fluctuateVisible = computed(() => {
    return (
      this.service.area() === '全国' &&
      this.service.rideType() === 'ALL'
    );
  })


  constructor() {
    effect(() => {
      this.buriedPointService.addStat('dida_dpm_caerus_page_view', {
        source: 'overview',
        action_type: BuriedPointActionType.VIEW,
        params_date: this.service.date()
      });
    });
  }


  ngAfterViewInit(): void {
    console.log('埋点上报: 曝光 -> 指标概览');

    this.buriedPointService.addStat('dida_dpm_caerus_subject_view', {
      source: 'overview',
      action_type: BuriedPointActionType.VIEW,
    });
  }
  
}
