<header class="flex items-center gap-x-1 p-5">
  <span class="text-nowrap font-black">业务方向关键指标</span>
  <span class="text-xs text-neutral-400 ml-0.5">注：乘客指标为自有渠道，车主指标为全渠道</span>
</header>

<ng-template #iconsTemplate let-index>
  @switch (index) {
    @case (0) { <PromotionIcon /> }
    @case (1) { <MarketingIcon /> }
    @case (2) { <TransferIcon /> }
  }
</ng-template>


<div class="relative">
  <div class="flex gap-x-3 indicator-cards">
    @if (metricsResource.isLoading()) {
      <ng-container *ngTemplateOutlet="skeletonGroup"></ng-container>
      <ng-container *ngTemplateOutlet="skeletonGroup; context: {$implicit: { column: 3 }}"></ng-container>
      <ng-container *ngTemplateOutlet="skeletonGroup"></ng-container>
    }
    @else {
      @for (arr of items(); track arr; let i = $index) {
        <app-indicator-card>
          <app-indicator-header class="text-white">
            <app-indicator-avatar class="text-3xl leading-0">
              <ng-container *ngTemplateOutlet="iconsTemplate; context: { $implicit: i }"></ng-container>
            </app-indicator-avatar>
            <app-indicator-title>{{titles[i]}}</app-indicator-title>
            <span class="flex-1 min-w-0"></span>
            <ExpandIcon class="expand-btn" iconBtn nz-tooltip="查看" (click)="openMetricsDetail(titles[i], arr, i)" />
          </app-indicator-header>
          <app-indicator-content-group>
            @for (item of arr; track item) {
              <app-indicator-content>
                <ng-container *ngTemplateOutlet="indicatorTemplate; context: {$implicit: item}"></ng-container>
              </app-indicator-content>
            }
          </app-indicator-content-group>
        </app-indicator-card>
      }
    }
  </div>
</div>


<ng-template #indicatorTemplate let-target>
  <app-indicator-value-group vertical>
    <ng-template #metricsTitleTemplate>{{target?.aliasName}} <span class="text-xs opacity-30 px-1">({{target?.metricsName}})</span></ng-template>
    <div class="relative">
      <app-indicator-title class="text-xs xl:text-sm scale-70" nzPopoverPlacement="topLeft" [nzPopoverTitle]="metricsTitleTemplate" [nzPopoverContent]="target?.metricsDescription">
        {{target?.aliasName}}
      </app-indicator-title>
      <span class="absolute right-0 translate-x-full -translate-y-1/2 top-1/2 text-xs scale-75 opacity-50">{{target?.avgType}}</span>
    </div>
    @if (target?.percent) {
      <app-indicator-value value="{{target?.value ? target?.value + '%' : target?.value ?? '-'}}" />
    } @else {
      <app-indicator-value [value]="target?.value ?? '-'" />
    }
  </app-indicator-value-group>
  <app-indicator-compare-group vertical>
    <app-indicator-compare label="月环比" [value]="target?.quarterOnQuarterGrowth" />
    <app-indicator-compare label="年同比" [value]="target?.yearOnYearGrowth" />
  </app-indicator-compare-group>
  <app-indicator-graph class="flex items-center justify-center w-full h-5 rounded-lg" />
</ng-template>


<ng-template #skeletonGroup let-data>
  <app-indicator-card>
    <app-indicator-header>
      <app-indicator-avatar>
        <app-skeleton circle class="w-7.5 h-7.5" />
      </app-indicator-avatar>
      <app-indicator-title class="text-white">
        <app-skeleton class="w-24 h-4.5" />
      </app-indicator-title>
    </app-indicator-header>
    <app-indicator-content-group>
      <ng-container *ngTemplateOutlet="skeleton"></ng-container>
      <ng-container *ngTemplateOutlet="skeleton"></ng-container>
      @if (data?.column === 3) {
        <ng-container *ngTemplateOutlet="skeleton"></ng-container>
      }
    </app-indicator-content-group>
  </app-indicator-card>
</ng-template>


<ng-template #skeleton>
  <app-indicator-content>
    <app-indicator-value-group vertical>
      <app-indicator-title class="scale-70 text-white">
        <app-skeleton class="w-24 h-4.5" />
      </app-indicator-title>
      <app-skeleton class="w-8/12 h-7.5 mx-auto" />
    </app-indicator-value-group>
    <app-indicator-compare-group vertical>
      <app-skeleton class="w-10/12 h-4" />
      <app-skeleton class="w-10/12 h-4" />
    </app-indicator-compare-group>
    <app-indicator-graph class="flex items-center justify-center h-20 rounded-lg">
      <app-skeleton class="h-full aspect-square" />
    </app-indicator-graph>
  </app-indicator-content>
</ng-template>

