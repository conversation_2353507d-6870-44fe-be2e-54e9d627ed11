import { NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, Injector, computed, inject } from '@angular/core';
import { NzPopoverModule } from 'ng-zorro-antd/popover';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { rxResource } from '@angular/core/rxjs-interop';
import { map } from 'rxjs';

import { trace } from '@common/const';
import { ModalService } from '@core/modal';
import { BuriedPointService } from '@common/service';
import { SkeletonComponent } from '@shared/components/skeleton';
import { IconExpandComponent, IconMarketingComponent, IconPromotionComponent, IconTransferComponent } from '@shared/modules/icons';
import { IndicatorModule } from '@shared/components/indicator';
import { BaseDataOutput } from '@views/cockpit/models';
import { MetricsDetailComponent } from '@views/cockpit/components/metrics-detail';
import { CockpitService } from '@views/cockpit/services';
import { CockpitApiService } from '@views/cockpit/api';


@Component({
  selector: 'app-key-indicators',
  templateUrl: './key-indicators.component.html',
  styleUrl: './key-indicators.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgTemplateOutlet,
    NzPopoverModule,
    NzToolTipModule,
    IndicatorModule,
    IconPromotionComponent,
    IconMarketingComponent,
    IconTransferComponent,
    IconExpandComponent,
    SkeletonComponent,
  ],
})
export class KeyIndicatorsComponent {
  
  readonly injector = inject(Injector);
  readonly service = inject(CockpitService);
  readonly buriedPointService = inject(BuriedPointService);
  readonly apiService = inject(CockpitApiService);
  readonly modalService = inject(ModalService);


  titles = ['端外推广', '端内营销', '转化效率 (订单视角)'];
  
  targetVisible = computed(() => {
    return (
      this.service.area() === '全国' &&
      this.service.rideType() === 'ALL'
    );
  })
  
  externalItems = computed(() => {
    if (this.metricsResource.value()) {
      const { newPassengerUnt, newDriverUnt } = this.metricsResource.value();

      return [newPassengerUnt, newDriverUnt];
    }
    return [];
  });

  internalItems = computed(() => {
    if (this.metricsResource.value()) {
      const { extraFinishOrderCnt, finishFrqPassengerAvg, finishFrqDriverAvg } = this.metricsResource.value();

      if (
        this.service.area() !== '全国' ||
        this.service.rideType() !== 'ALL'
      ) {
        return [finishFrqPassengerAvg, finishFrqDriverAvg];
      } else {
        return [extraFinishOrderCnt, finishFrqPassengerAvg, finishFrqDriverAvg];
      }
    }
    return [];
  });

  conversionItems = computed(() => {
    if (this.metricsResource.value()) {
      const { orderFinishRate, replyFinishRate } = this.metricsResource.value();

      orderFinishRate.percent = true;
      replyFinishRate.percent = true;
      return [orderFinishRate, replyFinishRate];
    }
    return [];
  });

  items = computed(() => {
    return [
      this.externalItems(),
      this.internalItems(),
      this.conversionItems(),
    ]
  })

  requestBody = computed(() => ({
    date: this.service.date(),
    rideType: this.service.rideType(),
    area: this.service.area(),
    source: 'all', 
  }))

  metricsResource = rxResource({
    request: () => this.requestBody(),
    loader: ({ request }) => this.apiService.fetchMetricsOverview(request).pipe(
      map(res => res.data)
    )
  })

  openMetricsDetail(title, metrics: BaseDataOutput[], index: number) {
    const componentRef = this.modalService.open(MetricsDetailComponent, null, this.injector);
    const area = this.service.area();
    const ride_type = this.service.rideType();
    const source_type = this.service.sourceType();
    const supports = [
      ['day', 'week', 'month'],
      ['day', 'week', 'month'],
    ];

    if (
      index === 1 &&
      area === '全国' &&
      ride_type === 'ALL'
    ) {
      supports.unshift(['month']);
    }

    componentRef.setInput('title', title);
    componentRef.setInput('metrics', metrics);
    componentRef.setInput('supports', supports);

    trace(`埋点上报: 点击 -> 概览卡片右上角 -> ${title}`);
    this.buriedPointService.addStat('dida_dpm_caerus_card_enlarge_click', {
      card_name: title,
      source_type,
      area,
      ride_type,
    });
  }

}
