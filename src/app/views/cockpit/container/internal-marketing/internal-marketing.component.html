<div class="p-5 pb-0">
  <app-heading [title]="'端内营销'" description="注：乘客指标为自有渠道，车主指标为全渠道">
    <MarketingIcon ngProjectAs="[icon]" class="text-2xl" />
    <ng-container ngProjectAs="[tab]">
      @if (targetVisible()) {
        <app-radio-group  [(ngModel)]="type" class="relative w-80 flex items-start gap-x-0.5 p-0.5 bg-[#ebebec] rounded-lg">
          <app-radio class="dida-radio-new" activeClass="active" value="all">自有整体</app-radio>
          <app-radio class="dida-radio-new" activeClass="active" value="passenger">乘客视角</app-radio>
          <app-radio class="dida-radio-new" activeClass="active" value="driver">车主视角</app-radio>
          <app-radio-thumb [offsetX]="2" class="bg-linear-to-r from-[#465867] to-[#1E2C3A] rounded-md" />
        </app-radio-group>
      }
    </ng-container>
  </app-heading>
  
  <header class="p-5 pb-0">业务目标达成</header>

  <div class="grid grid-cols-2 gap-x-3">
    @if (filtered()) {
      <!-- 如果已经筛选 -->
      <div *ngLet="originData()?.passenger?.finishFrqAvg as target">
        <div class="flex items-center justify-center gap-x-8 max-w-140 mx-auto h-16 px-5">
          <div class="flex flex-col gap-y-1 translate-y-1">
            <div class="flex items-center justify-between">
              <ng-template #metricsTitleTemplate>{{target?.aliasName}} <span class="text-xs opacity-30 px-1">({{target?.metricsName}})</span></ng-template>
              <app-indicator-title fontSize="text-xs" nzPopoverPlacement="topLeft" [nzPopoverTitle]="metricsTitleTemplate" [nzPopoverContent]="target?.metricsDescription">
                <span class="text-sm font-semibold mr-1">{{target?.aliasName}}</span>
                <span class="inline-block text-neutral-400 scale-75 origin-left">{{target?.avgType}}</span>
              </app-indicator-title>
              <app-indicator-value fontSize="text-lg" class="min-w-24 text-right" [value]="target?.value" />
            </div>

            <app-indicator-compare-group>
              <app-indicator-compare justifyContent="justify-between" label="月环比" [value]="target?.quarterOnQuarterGrowth" />
              <app-indicator-compare justifyContent="justify-between" label="年同比" [value]="target?.yearOnYearGrowth" />
            </app-indicator-compare-group>
          </div>
        </div>

        <div class="relative block w-full h-64">
          <div [ngClass]="{'opacity-0 pointer-events-none': !loading()}" class="absolute inset-0 z-10 transition-opacity rounded-lg bg-neutral-50">
            <ng-container *ngTemplateOutlet="loadingTemplate"></ng-container>
          </div>
          <app-graph-area [themes]="['rgba(93,201,154,1)', '#65789b59']" [value]="target?.trendVo" />
        </div>
      </div>

      <div *ngLet="originData()?.driver?.finishFrqAvg as target">
        <div class="flex items-center justify-center gap-x-8 max-w-140 mx-auto h-16 px-5">
          <div class="flex flex-col gap-y-1 translate-y-1">
            <div class="flex items-center justify-between">
              <ng-template #metricsTitleTemplate>{{target?.aliasName}} <span class="text-xs opacity-30 px-1">({{target?.metricsName}})</span></ng-template>
              <app-indicator-title fontSize="text-xs" nzPopoverPlacement="topLeft" [nzPopoverTitle]="metricsTitleTemplate" [nzPopoverContent]="target?.metricsDescription">
                <span class="text-sm font-semibold mr-1">{{target?.aliasName}}</span>
                <span class="inline-block text-neutral-400 scale-75 origin-left">{{target?.avgType}}</span>
              </app-indicator-title>
              <app-indicator-value fontSize="text-lg" class="min-w-24 text-right" [value]="target?.value" />
            </div>

            <app-indicator-compare-group>
              <app-indicator-compare justifyContent="justify-between" label="月环比" [value]="target?.quarterOnQuarterGrowth" />
              <app-indicator-compare justifyContent="justify-between" label="年同比" [value]="target?.yearOnYearGrowth" />
            </app-indicator-compare-group>
          </div>
        </div>

        <div class="relative block w-full h-64">
          <div [ngClass]="{'opacity-0 pointer-events-none': !loading()}" class="absolute inset-0 z-10 transition-opacity rounded-lg bg-neutral-50">
            <ng-container *ngTemplateOutlet="loadingTemplate"></ng-container>
          </div>
          <app-graph-area [themes]="['rgba(93,201,154,1)', '#65789b59']" [value]="target?.trendVo" />
        </div>
      </div>
    }
    @else {
      <div *ngLet="data()?.extraFinishOrderCnt as target">
        <div class="flex items-center justify-center gap-x-8 max-w-160 mx-auto h-16 px-5">
          <div class="flex flex-col gap-y-1 translate-y-1">
            <div class="flex items-center justify-between w-full">
              <ng-template #metricsTitleTemplate>{{target?.aliasName}} <span class="text-xs opacity-30 px-1">({{target?.metricsName}})</span></ng-template>
              <app-indicator-title fontSize="text-xs" nzPopoverPlacement="topLeft" [nzPopoverTitle]="metricsTitleTemplate" [nzPopoverContent]="target?.metricsDescription">
                <span class="text-sm font-semibold mr-1">{{target?.aliasName}}</span>
                <span class="inline-block text-neutral-400 scale-75 origin-left">{{target?.avgType}}</span>
              </app-indicator-title>
              <app-indicator-value fontSize="text-lg" class="min-w-24 text-right" [value]="target?.value" />
            </div>

            <app-indicator-compare-group>
              <app-indicator-compare justifyContent="justify-between" label="月环比" [value]="target?.quarterOnQuarterGrowth" />
              <app-indicator-compare justifyContent="justify-between" label="年同比" [value]="target?.yearOnYearGrowth" />
            </app-indicator-compare-group>
          </div>

          <app-indicator-graph class="relative flex items-center justify-center h-20 rounded-lg">
            <ng-template #infoTemplate>
              <div class="flex flex-col">
                <span>展示端内营销带来的额外完单为大盘贡献的比例，</span>
                <span>其中分母为大盘整体的完单量。</span>  
              </div>
            </ng-template>
            <span class="absolute inset-x-0 top-0 z-10 -translate-y-2 translate-x-2 text-center text-xs inline-flex items-center gap-x-1">
              为大盘贡献 <HelpFillIcon nz-popover nzPopoverPlacement="right" [nzPopoverContent]="infoTemplate" class="text-neutral-400 cursor-help" />
            </span>
            <app-indicator-progress circle [value]="target?.value" [total]="target?.totalValue" />
          </app-indicator-graph>
        </div>
    
        <div class="relative block w-full h-64">
          <div [ngClass]="{'opacity-0 pointer-events-none': !loading()}" class="absolute inset-0 z-10 transition-opacity rounded-lg bg-neutral-50">
            <ng-container *ngTemplateOutlet="loadingTemplate"></ng-container>
          </div>
          <app-graph-area [themes]="['rgba(93,201,154,1)', '#65789b59']" [value]="target?.trendVo" />
        </div>
      </div>
      

      <div *ngLet="data()?.finishFrqAvg as target">
        <div class="flex items-center justify-center gap-x-8 max-w-140 mx-auto h-16 px-5">
          <div class="flex flex-col gap-y-1 translate-y-1">
            <div class="flex items-center justify-between">
              <ng-template #metricsTitleTemplate>{{target?.aliasName}} <span class="text-xs opacity-30 px-1">({{target?.metricsName}})</span></ng-template>
              <app-indicator-title fontSize="text-xs" nzPopoverPlacement="topLeft" [nzPopoverTitle]="metricsTitleTemplate" [nzPopoverContent]="target?.metricsDescription">
                <span class="text-sm font-semibold mr-1">{{target?.aliasName}}</span>
                <span class="inline-block text-neutral-400 scale-75 origin-left">{{target?.avgType}}</span>
              </app-indicator-title>
              <app-indicator-value fontSize="text-lg" class="min-w-24 text-right" [value]="target?.value" />
            </div>

            <app-indicator-compare-group>
              <app-indicator-compare justifyContent="justify-between" label="月环比" [value]="target?.quarterOnQuarterGrowth" />
              <app-indicator-compare justifyContent="justify-between" label="年同比" [value]="target?.yearOnYearGrowth" />
            </app-indicator-compare-group>
          </div>
        </div>

        <div class="relative block w-full h-64">
          <div [ngClass]="{'opacity-0 pointer-events-none': !loading()}" class="absolute inset-0 z-10 transition-opacity rounded-lg bg-neutral-50">
            <ng-container *ngTemplateOutlet="loadingTemplate"></ng-container>
          </div>
          <app-graph-area [themes]="['rgba(93,201,154,1)', '#65789b59']" [value]="target?.trendVo" />
        </div>
      </div>
    }
  </div>
</div>

<app-quality-analysis />

<ng-template #loadingTemplate>
  <div class="flex items-center justify-center w-full h-full text-neutral-400 text-lg">
    <LoadingIcon />
  </div>
</ng-template>