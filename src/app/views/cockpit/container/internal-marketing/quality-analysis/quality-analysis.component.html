<div class="px-5 pb-5">
  <header class="min-w-full p-5 font-black">质量分析</header>

  <div class="relative">
    <div [ngClass]="{'opacity-0 pointer-events-none z-0': !loading(), 'z-20': loading()}" class="absolute inset-0 flex gap-x-3 w-full overflow-x-auto transition-opacity">
      @for (item of data(); track item) {
        <ng-container *ngTemplateOutlet="skeleton"></ng-container>
      }
    </div>
    <app-indicator-card-group gap="gap-x-3" class="relative z-10 w-full overflow-x-auto" [class.justify-center]="data()?.length <= 4">
      @for (item of data(); track item) {
        <app-indicator-card class="scroll-mx-5 min-w-56! max-w-[25%] w-72 p-3.5 bg-neutral-100 rounded-2xl">
          <app-indicator-header>
            <ng-template #metricsTitleTemplate>{{item?.aliasName}} <span class="text-xs opacity-30 px-1">({{item?.metricsName}})</span></ng-template>
            <app-indicator-title nzPopoverPlacement="topLeft" [nzPopoverTitle]="metricsTitleTemplate" [nzPopoverContent]="item?.metricsDescription">{{item.aliasName}}</app-indicator-title>
            <app-indicator-subtitle>{{item?.avgType}}</app-indicator-subtitle>
          </app-indicator-header>
          <app-indicator-content>
            @if (item?.unit) {
              <app-indicator-value-group class="justify-center" alignItems="items-end" gap="gap-x-0">
                <app-indicator-value class="leading-[0.9]!" [value]="item?.value" />
                <app-indicator-value-unit>{{item?.unit}}</app-indicator-value-unit>
              </app-indicator-value-group>
            } @else {
              @if (item?.percent) {
                <app-indicator-value value="{{item?.value ? item?.value + '%' : item?.value ?? '-'}}" />
              } @else {
                <app-indicator-value [value]="item?.value ?? '-'" />
              }
            }
            <app-indicator-compare-group>
              <app-indicator-compare label="月环比" [value]="item?.quarterOnQuarterGrowth" />
              <app-indicator-compare label="年同比" [value]="item?.yearOnYearGrowth" />
            </app-indicator-compare-group>

            <app-indicator-graph class="block h-20">
              <app-graph-mini-area [value]="item?.trendVo" [isPercent]="item?.percent" />
            </app-indicator-graph>
          </app-indicator-content>
        </app-indicator-card>
      }
    </app-indicator-card-group>
  </div>
</div>


<ng-template #skeleton>
  <app-indicator-card class="snap-start scroll-mx-4 min-w-64! flex-1 p-3.5 bg-neutral-100 rounded-2xl">
    <app-indicator-header>
      <app-indicator-title>
        <app-skeleton class="w-24 h-5" />
      </app-indicator-title>
      <span class="flex-1 min-w-0"></span>
    </app-indicator-header>
    <app-indicator-content>
      <app-skeleton class="w-1/2 h-7.5 mx-auto" />
      <app-indicator-compare-group>
        <app-skeleton class="flex-1 h-4" />
        <app-skeleton class="flex-1 h-4" />
      </app-indicator-compare-group>
      <div class="flex gap-x-2">
        <app-skeleton class="w-9 h-3" />
        <div class="flex-1">
          <app-skeleton class="h-3" />
          <div class="h-4 flex justify-end items-center">
            <app-skeleton class="w-14 h-2" />
          </div>
        </div>
      </div>
      <app-indicator-graph class="block h-20">
        <app-skeleton class="w-full h-full" />
      </app-indicator-graph>
    </app-indicator-content>
  </app-indicator-card>
</ng-template>