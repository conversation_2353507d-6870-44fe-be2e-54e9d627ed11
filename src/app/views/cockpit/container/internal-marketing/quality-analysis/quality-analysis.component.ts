import { ChangeDetectionStrategy, Component, computed, effect, inject, signal } from '@angular/core';
import { NgClass, NgTemplateOutlet } from '@angular/common';
import { finalize } from 'rxjs';

import { SwitchMap } from '@common/decorator';
import { CockpitService } from '@views/cockpit/services';
import { IndicatorModule } from '@shared/components/indicator';
import { GraphMiniAreaComponent } from '@shared/components/graph';
import { BaseDataOutput, InternalMktInput } from '@views/cockpit/models';
import { SkeletonComponent } from '@shared/components/skeleton';
import { CockpitApiService } from '@views/cockpit/api';

import { InternalMarketingComponent } from '../internal-marketing.component';


@Component({
  selector: 'app-quality-analysis',
  templateUrl: './quality-analysis.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgClass,
    NgTemplateOutlet,
    IndicatorModule,
    GraphMiniAreaComponent,
    SkeletonComponent,
  ],
})
export class QualityAnalysisComponent {

  service = inject(CockpitService);
  parent = inject(InternalMarketingComponent);
  apiService = inject(CockpitApiService);
  data = signal<BaseDataOutput[]>(null);
  loading = signal(false);

  targetVisible = computed(() => {
    return (
      this.service.area() === '全国' &&
      this.service.rideType() === 'ALL'
    );
  });
  
  constructor() {
    effect(() => {
      const date = this.service.date();
      const area = this.service.area();
      const rideType = this.service.rideType();
      const type = this.parent.type();
      
      this.loading.set(true);
      this.fetchInternalMktQuality({ date, type, area, rideType });
    });
  }


  @SwitchMap()
  private fetchInternalMktQuality(body: InternalMktInput) {
    return this.apiService.fetchInternalMktQuality(body).pipe(
      finalize(() => {
        setTimeout(() => {
          this.loading.set(false);
        }, 0);
      })
    ).subscribe(res => {
      if (!res.data) {
        return;
      }

      this.data.set(res.data);
    })
  }

}
