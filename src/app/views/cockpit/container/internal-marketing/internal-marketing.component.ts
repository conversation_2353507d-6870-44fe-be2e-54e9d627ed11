import { FormsModule } from '@angular/forms';
import { NgClass, NgTemplateOutlet } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, Component, computed, effect, inject, signal } from '@angular/core';
import { NzRadioModule } from 'ng-zorro-antd/radio';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzPopoverModule } from 'ng-zorro-antd/popover';
import { finalize } from 'rxjs';

import { trace } from '@common/const';
import { SwitchMap } from '@common/decorator';
import { RadioModule } from '@shared/modules/headless';
import { CockpitService } from '@views/cockpit/services';
import { IndicatorModule } from '@shared/components/indicator';
import { GraphAreaComponent } from '@shared/components/graph';
import { InternalMktInput, InternalMktTargetOutput } from '@views/cockpit/models';
import { IconLoadingComponent, IconMarketingComponent } from '@shared/modules/icons';
import { QualityAnalysisComponent } from './quality-analysis/quality-analysis.component';
import { BuriedPointActionType, BuriedPointService } from '@common/service';
import { HeadingComponent } from '@shared/components/heading';
import { CockpitApiService } from '@views/cockpit/api';
import { NgLetDirective } from '@shared/directives';



@Component({
  selector: 'app-internal-marketing',
  templateUrl: './internal-marketing.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgClass,
    NgTemplateOutlet,
    NzPopoverModule,
    FormsModule,
    NzRadioModule,
    NzToolTipModule,
    RadioModule,
    IndicatorModule,
    NgLetDirective,
    HeadingComponent,
    IconLoadingComponent,
    IconMarketingComponent,
    QualityAnalysisComponent,
    GraphAreaComponent,
  ],
})
export class InternalMarketingComponent implements AfterViewInit {

  service = inject(CockpitService);
  buriedPointService = inject(BuriedPointService);
  apiService = inject(CockpitApiService);

  type = signal<'all' | 'passenger' | 'driver'>('all');
  originData = signal<InternalMktTargetOutput>(null);
  loading = signal(false);

  data = computed(() => {
    if (this.originData()) {
      return this.originData()[this.type()];
    }
  });

  targetVisible = computed(() => {
    return (
      this.service.area() === '全国' &&
      this.service.rideType() === 'ALL'
    );
  });

  filtered = computed(() => {
    return (
      this.service.area() !== '全国' ||
      this.service.rideType() !== 'ALL'
    )
  });
  
  constructor() {
    effect(() => {
      trace(`埋点上报: tab点击 -> 端内营销 -> ${this.type()}`);

      this.buriedPointService.addStat('dida_dpm_caerus_tab_click', {
        source: 'internal_marketing',
        action_type: BuriedPointActionType.CLICK,
        source_type: this.type()
      });
    })

    effect(() => {
      const date = this.service.date();
      const area = this.service.area();
      const rideType = this.service.rideType();
      const type = this.type();

      this.loading.set(true);
      this.fetchInternalMktTarget({ date, type, area, rideType });
    })
  }


  ngAfterViewInit(): void {
      trace('埋点上报: 曝光 -> 端内营销');
      
      this.buriedPointService.addStat('dida_dpm_caerus_subject_view', {
      source: 'internal_marketing',
      action_type: BuriedPointActionType.VIEW,
    });
  }


  @SwitchMap()
  private fetchInternalMktTarget(body: InternalMktInput) {
    return this.apiService.fetchInternalMktTarget(body).pipe(
      finalize(() => {
        setTimeout(() => {
          this.loading.set(false);
        }, 0);
      })
    ).subscribe(res => {
      if (!res.data) {
        return;
      }
            
      this.originData.set(res.data);
    })
  }
  
}
