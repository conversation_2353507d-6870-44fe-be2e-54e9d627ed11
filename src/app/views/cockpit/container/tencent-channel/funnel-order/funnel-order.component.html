<div class="flex flex-col gap-2.5 p-5">
  <header class="relative flex items-center gap-x-2 px-5 h-12 border-l-4 border-emerald-500 bg-linear-to-r from-neutral-300/40 via-white via-20%">
    <TransferIcon class="text-2xl" />
    <span class="text-lg">转化效率</span>
  </header>

  <header class="px-5">
    <p>腾讯外输乘客转化漏斗（按订单视角）</p>
  </header>

  <div class="flex gap-5">
    <div class="relative h-96 py-10 box-content aspect-square rounded-2xl">
      <div [ngClass]="{'opacity-0 pointer-events-none': !loading()}" class="absolute inset-0 z-10 transition-opacity flex items-center justify-center w-full h-full rounded-2xl bg-neutral-50 text-neutral-400 text-lg">
        <LoadingIcon />
      </div>
      <app-chart-funnel [value]="funnelSeries()" />
    </div>

    <div class="flex-1 min-w-0 flex flex-col gap-y-2.5">
      <div class="relative" [style.height.px]="targetVisible() ? 174 : 138">
        <div [ngClass]="{'opacity-0 pointer-events-none z-0': !loading(), 'z-20': loading()}" class="absolute inset-0 flex transition-opacity bg-neutral-50 rounded-2xl">
          <LoadingIcon class="m-auto" />
        </div>
        
        <app-indicator-card-group gap="gap-x-4" class="relative z-10 w-full overflow-x-auto">
          @for (item of (items()); track $index) {
            <app-indicator-card (click)="handleClick($index)" class="snap-start scroll-mx-4 min-w-52! max-w-1/4 px-6 py-4 border border-neutral-300 rounded-2xl transition-colors hover:bg-neutral-100 cursor-pointer select-none" [class.bg-neutral-100]="activeIndex() === $index">
              <app-indicator-header>
                <ng-template #metricsTitleTemplate>{{item?.aliasName}} <span class="text-xs opacity-30 px-1">({{item?.metricsName}})</span></ng-template>
                <app-indicator-title fontSize="text-sm text-[#455063] font-light" nzPopoverPlacement="topLeft" [nzPopoverTitle]="metricsTitleTemplate" [nzPopoverContent]="item?.metricsDescription">{{item?.aliasName}}</app-indicator-title>
              </app-indicator-header>
              <app-indicator-content>
                <app-indicator-value textAlign="text-left text-[#111b34]" [value]="item?.value" />
                <app-indicator-compare-group vertical>
                  <app-indicator-compare justifyContent="justify-start" label="月环比" [value]="item?.quarterOnQuarterGrowth" />
                  <app-indicator-compare justifyContent="justify-start" label="年同比" [value]="item?.yearOnYearGrowth" />
                </app-indicator-compare-group>
              </app-indicator-content>
            </app-indicator-card>
          }
        </app-indicator-card-group>
      </div>

      <div class="flex-1 min-h-0 flex flex-col relative">
        <div [ngClass]="{'opacity-0 pointer-events-none': !trendLoading()}" class="absolute inset-0 z-10 transition-opacity flex items-center justify-center w-full h-full rounded-2xl bg-neutral-50 text-neutral-400 text-lg">
          <LoadingIcon />
        </div>

        <div class="relative flex-1 min-h-0">
          <div class="flex justify-end px-3 absolute inset-x-0 top-3 z-10">
            <nz-select [(ngModel)]="trendDate" nzSize="small">
              @for (item of trendDateOptions; track $index) {
                <nz-option [nzValue]="item.value" [nzLabel]="item.label"></nz-option>
              }
            </nz-select>
          </div>

          <app-graph-spline suffix="%" [categories]="categories()" [value]="lineSeries()" />
        </div>
      </div>
    </div>
  </div>
</div>