import { FormsModule } from '@angular/forms';
import { ChangeDetectionStrategy, Component, computed, effect, inject, signal } from '@angular/core';
import { NgClass } from '@angular/common';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { finalize } from 'rxjs';

import { SwitchMap } from '@common/decorator';
import { IconLoadingComponent, IconTransferComponent } from '@shared/modules/icons';
import { ChartFunnelComponent } from '@shared/components/chart';
import { GraphSplineComponent, LineSeries } from '@shared/components/graph';
import { groupBy, isUndefined, transformValueSuffix } from '@common/function';
import { BaseInput, BaseTrendInput, FunnelOrderTransformTrendVo, TengxunFunnelOrderOutput, TrendDate, TrendDateOptions } from '@views/cockpit/models';
import { RelationControlService } from '@views/cockpit/services';
import { IndicatorModule } from '@shared/components/indicator';
import { CockpitApiService } from '@views/cockpit/api';
import { CockpitService } from '@views/cockpit/services';


@Component({
  selector: 'app-funnel-order',
  templateUrl: './funnel-order.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgClass,
    FormsModule,
    NzSelectModule,
    IndicatorModule,
    IconLoadingComponent,
    IconTransferComponent,
    ChartFunnelComponent,
    GraphSplineComponent,
  ],
  providers: [
    RelationControlService,
  ],
})
export class FunnelOrderComponent {

  service = inject(CockpitService);
  apiService = inject(CockpitApiService);
  relationControlService = inject(RelationControlService);
  data = signal<TengxunFunnelOrderOutput>(null);
  trendDate = signal<TrendDate>(1);
  trendDateOptions = TrendDateOptions;
  funnelSeries = signal([]);
  lineSeries = signal<LineSeries[]>([]);
  categories = signal<string[]>([]);
  isShowAll = signal<boolean>(true);
  activeIndex = signal<number>(null);
  trendLoading = signal(false);
  loading = signal(false);

  items = computed(() => {
    if (this.data()) {
      const { orderFinishRate, replyFinishRate, orderReplyRate } = this.data().curTrendItem;

      console.log({ orderFinishRate, replyFinishRate, orderReplyRate });
      
      return [orderFinishRate, replyFinishRate, orderReplyRate];
    }
    return [];
  });

  targetVisible = computed(() => {
    return (
      this.service.area() === '全国' &&
      this.service.rideType() === 'ALL'
    );
  });

  constructor() {
    effect(() => {
      const date = this.service.date();
      const area = this.service.area();
      const rideType = this.service.rideType();

      this.loading.set(true);
      this.fetchTencentFunnelOrder({ date, area, rideType });
    });

    effect(() => {
      const date = this.service.date();
      const area = this.service.area();
      const rideType = this.service.rideType();
      const trendDate = this.trendDate();
      
      this.trendLoading.set(true);
      this.fetchTencentFunnelOrderTrend({ date, trendDate, area, rideType });
    });
  }


  private fetchTencentFunnelOrderTrend(body: BaseTrendInput) {
    this.apiService.fetchTencentFunnelOrderTrend(body).pipe(
      finalize(() => {
        setTimeout(() => {
          this.trendLoading.set(false);
        }, 0);
      })
    ).subscribe(res => {
      if (!res.data) {
        return;
      }

      const values = groupBy<FunnelOrderTransformTrendVo>(res.data, 'year');
      const series = [];
      const years = [] as string[];
      const seriesObj = {
        orderFinishRate: [],
        replyFinishRate: [],
        orderReplyRate: [],
      };

      Object.keys(values).forEach(key => {
        const items = values[key].sort((a, b) => Number(a.month) - Number(b.month));
        const orderFinishRate = items.map(item => isUndefined(item?.orderFinishRate) ? null : item?.orderFinishRate);
        const replyFinishRate = items.map(item => isUndefined(item?.replyFinishRate) ? null : item?.replyFinishRate);
        const orderReplyRate = items.map(item => isUndefined(item?.orderReplyRate) ? null : item?.orderReplyRate);

        years.push(...items.map(item => `${item.year}年${item.month}月`));        
        seriesObj.orderFinishRate.push(...orderFinishRate);
        seriesObj.replyFinishRate.push(...replyFinishRate);
        seriesObj.orderReplyRate.push(...orderReplyRate);        
      })

      series.push(new LineSeries('下单完单率', seriesObj.orderFinishRate));
      series.push(new LineSeries('接单完单率', seriesObj.replyFinishRate));
      series.push(new LineSeries('下单接单率', seriesObj.orderReplyRate));

      this.categories.set(years);
      this.lineSeries.set(series);
    });
  }


  @SwitchMap()
  private fetchTencentFunnelOrder(body: BaseInput) {
    return this.apiService.fetchTencentFunnelOrder(body).pipe(
      finalize(() => {
        setTimeout(() => {
          this.loading.set(false);
        }, 0);
      })
    ).subscribe(res => {
      if (!res.data) {
        return;
      }

      const { funnel: { orderCnt, orderFinishCal, orderReplyCal }, curTrendItem } = res.data;
      const { orderFinishRate, orderReplyRate, replyFinishRate } = curTrendItem;
      const series = [
        { name: '下单量', leftLabel: '下单量', rightLabel: '下单量', value: orderCnt },
        { name: '接单量', leftLabel: '接单量', rightLabel: '下单接单率', value: orderReplyCal },
        { name: '完单量', leftLabel: '完单量', rightLabel: '接单完单率', value: orderFinishCal },
      ];

      res.data.curTrendItem.orderFinishRate.value = transformValueSuffix(orderFinishRate.value);
      res.data.curTrendItem.orderReplyRate.value = transformValueSuffix(orderReplyRate.value);
      res.data.curTrendItem.replyFinishRate.value = transformValueSuffix(replyFinishRate.value);

      this.funnelSeries.set(series);
      this.data.set(res.data);
    });
  }

  
  handleClick(index: number) {
    if (index === this.activeIndex()) {
      this.isShowAll.update(state => !state);
    } else {
      this.isShowAll.set(false);
    }

    if (this.isShowAll()) {
      this.activeIndex.set(null);
    } else {
      this.activeIndex.set(index);
    }

    this.relationControlService.emit({ index, isShowAll: this.isShowAll() });
  }

}
