import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconFlowComponent } from '@shared/modules/icons';
import { InfluenceFlowItemComponent } from './influence-flow-item/influence-flow-item.component';


@Component({
  selector: 'app-influence-flow',
  templateUrl: './influence-flow.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    IconFlowComponent,
    InfluenceFlowItemComponent,
  ],
})
export class InfluenceFlowComponent {

  

}
