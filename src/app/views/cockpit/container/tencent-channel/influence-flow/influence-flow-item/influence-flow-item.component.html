<div class="flex flex-col gap-2.5 p-5">
  <header class="px-5">
    <p
      [class.bg-[#de868f]]="type() === 'out'"
      [class.bg-[#4095e5]]="type() === 'in'"
      class="flex items-center justify-center gap-x-1 w-3/5 h-7.5 mx-auto rounded-sm text-white text-xs"
    >
      @switch (type()) {
        @case ('in') {<ng-container *ngTemplateOutlet="inIconTemplate"></ng-container>}
        @case ('out') {<ng-container *ngTemplateOutlet="outIconTemplate"></ng-container>}
      }
      
      {{label()}}
    </p>
  </header>

  <app-indicator-card-group class="p-3.5 py-6 rounded-md ring-1 ring-neutral-200">
    @if (data()) {
      @if (type() === 'in') {
        <ng-container *ngTemplateOutlet="indicatorTemplate; context: { $implicit: data().tengxunToOwnFinishOrderCnt }"></ng-container>
        <div class="w-px h-24 bg-neutral-200 rotate-12"></div>
        <ng-container *ngTemplateOutlet="indicatorTemplate; context: { $implicit: data().ownFinishOrderCnt }"></ng-container>
        <div class="text-lg">=</div>
        <ng-container *ngTemplateOutlet="resultTemplate; context: { $implicit: flowIn() }"></ng-container>
      } @else {
        <ng-container *ngTemplateOutlet="indicatorTemplate; context: { $implicit: data().ownToTengxunFinishOrderCnt}"></ng-container>
        <div class="w-px h-24 bg-neutral-200 rotate-12"></div>
        <ng-container *ngTemplateOutlet="indicatorTemplate; context: { $implicit: data().tengxunFinishOrderCnt }"></ng-container>
        <div class="text-lg">=</div>
        <ng-container *ngTemplateOutlet="resultTemplate; context: { $implicit: flowOut() }"></ng-container>
      }
    }
  </app-indicator-card-group>
  
  <div class="relative h-96">
    <div class="px-3 absolute right-0 top-1 z-10">
      <nz-select [(ngModel)]="trendDate" nzSize="small">
        @for (item of trendDateOptions; track $index) {
          <nz-option [nzValue]="item.value" [nzLabel]="item.label"></nz-option>
        }
      </nz-select>
    </div>

    <app-graph-combination [categories]="categories()" [value]="series()" />
  </div>
</div>


<ng-template #indicatorTemplate let-target>
  <app-indicator-card>
    <app-indicator-content>
      <app-indicator-value-group vertical>
        <ng-template #metricsTitleTemplate>{{target?.aliasName}} <span class="text-xs opacity-30 px-1">({{target?.metricsName}})</span></ng-template>
        <app-indicator-title class="scale-70" [nzPopoverTitle]="metricsTitleTemplate" [nzPopoverContent]="target?.metricsDescription">
          {{target.aliasName}}
          <span class="text-xs scale-75 origin-bottom opacity-50">{{target.avgType}}</span>
        </app-indicator-title>
        <app-indicator-value-group class="justify-center" alignItems="items-end" gap="gap-x-1">
          <app-indicator-value class="leading-[0.9]!" [value]="target?.value ?? '-'" />
        </app-indicator-value-group>
      </app-indicator-value-group>
      <app-indicator-compare-group vertical>
        <app-indicator-compare label="月环比" [value]="target?.quarterOnQuarterGrowth" />
        <app-indicator-compare label="年同比" [value]="target?.yearOnYearGrowth" />
      </app-indicator-compare-group>
    </app-indicator-content>
  </app-indicator-card>
</ng-template>


<ng-template #resultTemplate let-target>
  <app-indicator-card>
    <app-indicator-content>
      <app-indicator-value-group vertical>
        <app-indicator-title class="scale-70">{{target?.aliasName}}</app-indicator-title>
        <app-indicator-value-group class="justify-center" alignItems="items-end" gap="gap-x-1">
          <app-indicator-value class="leading-[0.9]!" [value]="target?.value ?? '-'" />
        </app-indicator-value-group>
      </app-indicator-value-group>
      <app-indicator-compare-group vertical>
        <app-indicator-compare label="月环比" symbol="pp" [value]="target?.quarterOnQuarterGrowth" />
        <app-indicator-compare label="年同比" symbol="pp" [value]="target?.yearOnYearGrowth" />
      </app-indicator-compare-group>
    </app-indicator-content>
  </app-indicator-card>
</ng-template>


<ng-template #inIconTemplate>
  <svg class="inline-flex h-em w-em text-base" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
    <g>
      <path d="M10.5 7V2.5C10.5 1.67157 11.1716 1 12 1C12.8284 1 13.5 1.67157 13.5 2.5V7H13.4146C13.2087 6.4174 12.6531 6 12 6C11.3469 6 10.7913 6.4174 10.5854 7H10.5Z" fill="currentColor"></path>
      <path d="M10.5 7H5C3.34315 7 2 8.34315 2 10V20C2 21.6569 3.34315 23 5 23H19C20.6569 23 22 21.6569 22 20V10C22 8.34315 20.6569 7 19 7H13.5V13.3787L14.9393 11.9393C15.5251 11.3536 16.4749 11.3536 17.0607 11.9393C17.6464 12.5251 17.6464 13.4749 17.0607 14.0607L13.0607 18.0607C12.4749 18.6464 11.5251 18.6464 10.9393 18.0607L6.93934 14.0607C6.35355 13.4749 6.35355 12.5251 6.93934 11.9393C7.52513 11.3536 8.47487 11.3536 9.06066 11.9393L10.5 13.3787V7Z" fill="currentColor"></path>
    </g>
  </svg>
</ng-template>

<ng-template #outIconTemplate>
  <svg class="inline-flex h-em w-em text-base" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
    <g>
      <path d="M11.079 0.815972C11.6207 0.394676 12.3792 0.394676 12.9209 0.815972L17.4209 4.31597C18.0748 4.82458 18.1926 5.76699 17.684 6.42091C17.1754 7.07483 16.233 7.19263 15.579 6.68403L13.5 5.067V7.5C13.5 8.32843 12.8284 9 12 9C11.1716 9 10.5 8.32843 10.5 7.5V5.06692L8.42086 6.68403C7.76694 7.19263 6.82452 7.07483 6.31592 6.42091C5.80731 5.76699 5.92512 4.82458 6.57904 4.31597L11.079 0.815972Z" fill="currentColor"></path>
      <path d="M2 11C2 9.34315 3.34315 8 5 8H10.5V14.5C10.5 15.3284 11.1716 16 12 16C12.8284 16 13.5 15.3284 13.5 14.5V8H19C20.6569 8 22 9.34315 22 11V20C22 21.6569 20.6569 23 19 23H5C3.34315 23 2 21.6569 2 20V11Z" fill="currentColor"></path>
    </g>
  </svg>
</ng-template>