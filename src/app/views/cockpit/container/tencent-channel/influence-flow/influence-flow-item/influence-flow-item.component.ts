import { FormsModule } from '@angular/forms';
import { NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, effect, inject, input, signal } from '@angular/core';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { finalize } from 'rxjs';

import { CockpitService } from '@views/cockpit/services';
import { groupBy, isUndefined, transformValueSuffix } from '@common/function';
import { BaseInput, FlowOutput, FlowTrendInput, FlowTrendOutput, TrendDate, TrendDateOptions } from '@views/cockpit/models';
import { CombinationSeries, GraphCombinationComponent } from '@shared/components/graph';
import { IndicatorModule } from '@shared/components/indicator';
import { CockpitApiService } from '@views/cockpit/api';


@Component({
  selector: 'app-influence-flow-item',
  templateUrl: './influence-flow-item.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    FormsModule,
    NgTemplateOutlet,
    NzSelectModule,
    IndicatorModule,
    GraphCombinationComponent,
  ],
})
export class InfluenceFlowItemComponent {

  type = input.required<'in' | 'out'>();

  label = computed(() => {
    return this.type() === 'in' ? '流入' : '流出';
  });

  service = inject(CockpitService);
  apiService = inject(CockpitApiService);
  trendDate = signal<TrendDate>(1);
  trendDateOptions = TrendDateOptions;
  trendLoading = signal(false);
  data = signal<FlowOutput>(null);
  series = signal<CombinationSeries[]>([]);
  categories = signal<string[]>([]);
  loading = signal(false);
  flowIn = signal(null);
  flowOut = signal(null);

  constructor() {
    effect(() => {
      const date = this.service.date();
      const area = this.service.area();
      const rideType = this.service.rideType();

      this.loading.set(true);
      this.fetchTencentInfluenceFlow({ date, area, rideType });
    });

    effect(() => {
      const date = this.service.date();
      const area = this.service.area();
      const rideType = this.service.rideType();
      const trendDate = this.trendDate();
      const type = this.type() === 'in' ? 1 : 2;
      
      this.loading.set(true);
      this.fetchTencentInfluenceFlowTrend({date, area, rideType, type, trendDate});
    });
  }

  
  private fetchTencentInfluenceFlow(body: BaseInput) {
    this.apiService.fetchTencentInfluenceFlow(body).pipe(
      finalize(() => {
        setTimeout(() => {
          this.loading.set(false);
        }, 0);
      })
    ).subscribe(res => {
      if (!res.data) {
        return;
      }

      this.data.set(res.data);
      this.flowIn.set({
        aliasName: '腾讯流入占自有占比',
        value: transformValueSuffix(res.data.tengxunToOwnFinishOrderRate),
        quarterOnQuarterGrowth: res.data.tengxunToOwnFinishOrderMonthRate,
        yearOnYearGrowth: res.data.tengxunToOwnFinishOrderYearRate
      });

      this.flowOut.set({
        aliasName: '自有流出占腾讯占比',
        value: transformValueSuffix(res.data.ownToTengxunFinishOrderRate),
        quarterOnQuarterGrowth: res.data.ownToTengxunFinishOrderMonthRate,
        yearOnYearGrowth: res.data.ownToTengxunFinishOrderYearRate
      });
    });
  }


  private fetchTencentInfluenceFlowTrend(body: FlowTrendInput) {
    this.apiService.fetchTencentInfluenceFlowTrend(body).pipe(
      finalize(() => {
        setTimeout(() => {
          this.loading.set(false);
        }, 0);
      })
    ).subscribe(res => {
      if (!res.data) {
        return;
      }
      
      const { years, series } = this.handleTrends(res.data);

      this.categories.set(years);
      this.series.set(series);
    });
  }


  handleTrends(data: FlowTrendOutput[]) {
    const values = groupBy<FlowTrendOutput>(data, 'year');
    const series = [];
    const years = [] as string[];
    const seriesObj: {[key in keyof Partial<FlowTrendOutput>]: number[]} = {
      flowFinishOrderCnt: [],
      finishOrderCnt: [],
      flowFinishOrderRate: [],
    };

    Object.keys(values).forEach(key => {
      const items = values[key].sort((a, b) => Number(a.month) - Number(b.month));
      const flowFinishOrderCnt = items.map(item => isUndefined(item?.flowFinishOrderCnt) ? null : item?.flowFinishOrderCnt);
      const finishOrderCnt = items.map(item => isUndefined(item?.finishOrderCnt) ? null : item?.finishOrderCnt);
      const flowFinishOrderRate = items.map(item => isUndefined(item?.flowFinishOrderRate) ? null : item?.flowFinishOrderRate);

      years.push(...items.map(item => `${item.year}年${item.month}月`));        
      seriesObj.flowFinishOrderCnt.push(...flowFinishOrderCnt);
      seriesObj.finishOrderCnt.push(...finishOrderCnt);
      seriesObj.flowFinishOrderRate.push(...flowFinishOrderRate);
    });

    const cntName  = this.type() === 'in' ? '腾讯渠道流入用户' : '自有渠道流出用户';
    const rateName = this.type() === 'in' ? '腾讯流入占自有占比' : '自有流出占腾讯占比';

    series.push(new CombinationSeries(cntName, 'column', 0, 2, seriesObj.flowFinishOrderCnt));
    series.push(new CombinationSeries(rateName, 'line', 1, 3, seriesObj.flowFinishOrderRate, undefined, { valueSuffix: '%' }));

    return { years, series };
  }

}
