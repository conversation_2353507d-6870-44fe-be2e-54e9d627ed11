import { DecimalPipe, NgFor } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ChangeDetectionStrategy, Component, computed, effect, inject, signal } from '@angular/core';
import { NzRadioModule } from 'ng-zorro-antd/radio';
import { NzProgressModule } from 'ng-zorro-antd/progress';
import { NzTableModule } from 'ng-zorro-antd/table';
import { finalize } from 'rxjs';

import { trace } from '@common/const';
import { RadioModule } from '@shared/modules/headless';
import { CockpitService } from '@views/cockpit/services';
import { ExternalPromotionSourceOutput, PromotionRankInput } from '@views/cockpit/models';
import { ExternalPromotionComponent } from '../external-promotion.component';
import { BuriedPointActionType, BuriedPointService } from '@common/service';
import { CockpitApiService } from '@views/cockpit/api';
import { SwitchMap } from '@common/decorator';


@Component({
  selector: 'app-promotion-rank',
  templateUrl: './promotion-rank.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgFor,
    FormsModule,
    NzTableModule,
    NzProgressModule,
    NzRadioModule,
    RadioModule,
    DecimalPipe,
  ],
})
export class PromotionRankComponent {

  service = inject(CockpitService);
  parent = inject(ExternalPromotionComponent);
  buriedPointService = inject(BuriedPointService);
  apiService = inject(CockpitApiService);
  data = signal<ExternalPromotionSourceOutput>(null);
  type = signal<'passenger' | 'driver'>('passenger');
  typeCn = computed(() => this.type() === 'driver' ? '车主' : '乘客');
  loading = signal(false);

  list = computed(() => {
    if (this.data()) {
      return this.data()[this.type()];
    }
    return [];
  });

  
  constructor() {
    effect(() => {
      trace(`埋点上报: tab点击 -> 端外推广排行榜 -> ${this.typeCn()}`);
      
      this.buriedPointService.addStat('dida_dpm_caerus_tab_click', {
        source: 'external_promotion_rank',
        action_type: BuriedPointActionType.CLICK,
        source_type: this.type()
      });
    })
    
    effect(() => {
      const date = this.service.date();
      const area = this.service.area();
      const rideType = this.service.rideType();
      const source = this.parent.type();

      this.loading.set(true);
      this.fetchExternalPromotionSourceRank({date, area, rideType, source});
    });
  }


  @SwitchMap()
  private fetchExternalPromotionSourceRank(body: PromotionRankInput) {
    return this.apiService.fetchExternalPromotionSourceRank(body).pipe(
      finalize(() => {
        setTimeout(() => {
          this.loading.set(false);
        }, 0);
      })
    ).subscribe(res => {
      if (!res.data) {
        return;
      }
      
      this.data.set(res.data);
    })
  }

}
