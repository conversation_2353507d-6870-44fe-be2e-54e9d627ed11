<ng-template #tableHeader>
  <div class="flex justify-center">
    <app-radio-group [(ngModel)]="type" class="relative w-52 flex items-start gap-x-0.5 p-0.5 bg-[#ebebec] rounded-lg">
      <app-radio class="dida-radio-new" activeClass="active" value="passenger">乘客</app-radio>
      <app-radio class="dida-radio-new" activeClass="active" value="driver">车主</app-radio>
      <app-radio-thumb [offsetX]="2" class="bg-linear-to-r from-[#465867] to-[#1E2C3A] rounded-md" />
    </app-radio-group>
  </div>
</ng-template>

<nz-table #basicTable class="w-full" nzBordered [nzTitle]="tableHeader" [nzPageSize]="5" [nzLoading]="loading()" nzSize="small" [nzData]="list()">
  <thead>
    <tr>
      <th nzWidth="80px">排名</th>
      <th nzWidth="130px">付费渠道来源</th>
      <th nzWidth="160px">首单{{typeCn()}}数 (月日均)</th>
      <th>首单{{typeCn()}}数占比（对比付费推广量）</th>
      <th nzWidth="130px">CAC（元）</th>
      <th nzWidth="130px">ROI_30</th>
      <th nzWidth="130px">推广费用（元）</th>
    </tr>
  </thead>
  <tbody>
    <tr *ngFor="let data of basicTable.data">
      <td>{{data.rank}}</td>
      <td>{{data.source}}</td>
      <td class="text-right">
        {{data.value | number}}
      </td>
      <td>
        <div class="w-full pr-6">
          <nz-progress [nzPercent]="((data.value / data.totalValue * 100) | number: '1.0-2')" nzStatus="active"></nz-progress>
        </div>
      </td>
      <td class="text-right">{{data.cac || '-'}}</td>
      <td class="text-right">{{(data.roi | number) || '-'}}</td>
      <td class="text-right">{{(data.fee | number) || '-'}}</td>
    </tr>
  </tbody>
</nz-table>