import { FormsModule } from '@angular/forms';
import { AfterViewInit, ChangeDetectionStrategy, Component, computed, effect, inject, model } from '@angular/core';
import { NzPopoverModule } from 'ng-zorro-antd/popover';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzTableModule } from 'ng-zorro-antd/table';

import { trace } from '@common/const';
import { RadioModule } from '@shared/modules/headless';
import { BuriedPointActionType, BuriedPointService } from '@common/service';
import { IconPromotionComponent } from '@shared/modules/icons';
import { IndicatorModule } from '@shared/components/indicator';
import { HeadingComponent } from '@shared/components/heading';
import { CockpitService } from '@views/cockpit/services';

import { PromotionTargetComponent } from './promotion-target/promotion-target.component';
import { PromotionQualityComponent } from './promotion-quality/promotion-quality.component';
import { PromotionRankComponent } from './promotion-rank/promotion-rank.component';


@Component({
  selector: 'app-external-promotion',
  templateUrl: './external-promotion.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    FormsModule,
    NzSelectModule,
    NzTableModule,
    NzPopoverModule,
    RadioModule,
    IndicatorModule,
    HeadingComponent,
    IconPromotionComponent,
    PromotionTargetComponent,
    PromotionQualityComponent,
    PromotionRankComponent,
  ],
})
export class ExternalPromotionComponent implements AfterViewInit {

  buriedPointService = inject(BuriedPointService);
  service = inject(CockpitService);

  type = model<'new' | 'recall'>('new');
  typeCn = computed(() => this.type() === 'new' ? '拉新' : '召回');

  targetVisible = computed(() => {
    return (
      this.service.area() === '全国' &&
      this.service.rideType() === 'ALL'
    );
  });
  
  constructor() {
    effect(() => {
      trace(`埋点上报: tab点击 -> 端外推广 -> ${this.typeCn()}`);

      this.buriedPointService.addStat('dida_dpm_caerus_tab_click', {
        source: 'external_promotion',
        action_type: BuriedPointActionType.CLICK,
        source_type: this.type()
      });
    })
  }


  ngAfterViewInit(): void {
    trace('埋点上报: 曝光 -> 端外推广');
    
    this.buriedPointService.addStat('dida_dpm_caerus_subject_view', {
      source: 'external_promotion',
      action_type: BuriedPointActionType.VIEW,
    });
  }

}
