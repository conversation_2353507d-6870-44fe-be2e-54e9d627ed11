<div class="flex gap-x-3">
  <div class="flex-1 flex flex-col gap-2.5">
    <header class="p-5 pb-0 font-black">质量分析（预估）</header>

    <div class="relative">
      <ng-container *ngTemplateOutlet="loadingTemplate"></ng-container>
      <app-indicator-card-group class="p-3.5 py-6 rounded-2xl bg-linear-to-b from-sky-500/30 to-sky-50/70">
        @for (item of passengerList(); track item) {
          <app-indicator-card>
            <app-indicator-content>
              <app-indicator-value-group vertical>
                <ng-template #metricsTitleTemplate>{{item?.aliasName}} <span class="text-xs opacity-30 px-1">({{item?.metricsName}})</span></ng-template>
                <app-indicator-title class="scale-70" [nzPopoverTitle]="metricsTitleTemplate" [nzPopoverContent]="item?.metricsDescription">
                  {{item.aliasName}}
                  <span class="text-xs scale-75 origin-bottom opacity-50">{{item.avgType}}</span>
                </app-indicator-title>
                <app-indicator-value-group class="justify-center" alignItems="items-end" gap="gap-x-1">
                  <app-indicator-value class="leading-[0.9]!" [value]="item?.value ?? '-'" />
                  @if (
                    item?.aliasName === '乘客推广费用' ||
                    item?.aliasName === '乘客新流CAC' ||
                    item?.aliasName === '乘客召回CAC'
                  ) {
                    <app-indicator-value-unit>元</app-indicator-value-unit>
                  }
                </app-indicator-value-group>
              </app-indicator-value-group>
              <app-indicator-compare-group vertical>
                <app-indicator-compare label="月环比" [value]="item?.quarterOnQuarterGrowth" />
                <app-indicator-compare label="年同比" [value]="item?.yearOnYearGrowth" />
              </app-indicator-compare-group>
              <app-indicator-graph class="block h-20 bg-neutral-200/0 rounded-lg">
                <app-graph-mini-area [value]="item?.trendVo" />
              </app-indicator-graph>
            </app-indicator-content>
          </app-indicator-card>
        }
      </app-indicator-card-group>
    </div>
  </div>

  <div class="flex-1 flex flex-col gap-2.5">
    <header class="p-5 pb-0">&nbsp;</header>

    <div class="relative">
      <ng-container *ngTemplateOutlet="loadingTemplate"></ng-container>
      <app-indicator-card-group class="p-3.5 py-6 rounded-2xl bg-linear-to-b from-orange-500/30 to-orange-50/70">
        @for (item of driverList(); track item) {
          <app-indicator-card>
            <app-indicator-content>
              <app-indicator-value-group vertical>
                <ng-template #metricsTitleTemplate>{{item?.aliasName}} <span class="text-xs opacity-30 px-1">({{item?.metricsName}})</span></ng-template>
                <app-indicator-title class="scale-70" [nzPopoverTitle]="metricsTitleTemplate" [nzPopoverContent]="item?.metricsDescription">
                  {{item.aliasName}}
                  <span class="text-xs scale-75 origin-bottom opacity-50">{{item.avgType}}</span>
                </app-indicator-title>
                <app-indicator-value-group class="justify-center" alignItems="items-end" gap="gap-x-1">
                  <app-indicator-value class="leading-[0.9]!" [value]="item?.value ?? '-'" />
                  @if (
                    item?.aliasName === '车主推广费用' ||
                    item?.aliasName === '车主新流CAC' ||
                    item?.aliasName === '车主召回CAC'
                  ) {
                    <app-indicator-value-unit>元</app-indicator-value-unit>
                  }
                </app-indicator-value-group>
              </app-indicator-value-group>
              <app-indicator-compare-group vertical>
                <app-indicator-compare label="月环比" [value]="item?.quarterOnQuarterGrowth" />
                <app-indicator-compare label="年同比" [value]="item?.yearOnYearGrowth" />
              </app-indicator-compare-group>
              <app-indicator-graph class="block h-20 bg-neutral-200/0 rounded-lg">
                <app-graph-mini-area [value]="item?.trendVo" />
              </app-indicator-graph>
            </app-indicator-content>
          </app-indicator-card>
        }
      </app-indicator-card-group>
    </div>
  </div>
</div>


<ng-template #loadingTemplate>
  <div [ngClass]="{'opacity-0 pointer-events-none': !loading()}" class="absolute inset-0 z-10 transition-opacity flex items-center justify-center text-neutral-400 text-lg bg-neutral-50 rounded-2xl">
    <LoadingIcon />
  </div>
</ng-template>