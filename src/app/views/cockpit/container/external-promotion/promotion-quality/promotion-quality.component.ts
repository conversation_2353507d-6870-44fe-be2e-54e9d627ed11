import { NgClass, NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, effect, inject, signal } from '@angular/core';
import { finalize } from 'rxjs';

import { SwitchMap } from '@common/decorator';
import { CockpitService } from '@views/cockpit/services';
import { CockpitApiService } from '@views/cockpit/api';
import { IndicatorModule } from '@shared/components/indicator';
import { ExternalPromotionInput, ExternalPromotionQualityOutput } from '@views/cockpit/models';
import { GraphMiniAreaComponent } from '@shared/components/graph';
import { IconLoadingComponent } from '@shared/modules/icons';

import { ExternalPromotionComponent } from '../external-promotion.component';


@Component({
  selector: 'app-promotion-quality',
  templateUrl: './promotion-quality.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgClass,
    NgTemplateOutlet,
    IndicatorModule,
    IconLoadingComponent,
    GraphMiniAreaComponent,
  ],
})
export class PromotionQualityComponent {

  service = inject(CockpitService);
  parent = inject(ExternalPromotionComponent);
  apiService = inject(CockpitApiService);
  data = signal<ExternalPromotionQualityOutput>(null);
  loading = signal(false);

  passengerList = computed(() => {
    if (!this.data()) {return []; }

    const { passengerCac, passengerRoi, passengerPromotionFee } = this.data();
    return [passengerCac, passengerRoi, passengerPromotionFee];
  })

  driverList = computed(() => {
    if (!this.data()) {return []; }

    const { driverCac, driverRoi, driverPromotionFee } = this.data();
    return [driverCac, driverRoi, driverPromotionFee];
  })


  constructor() {
    effect(() => {
      const date = this.service.date();
      const area = this.service.area();
      const rideType = this.service.rideType();
      const source = this.parent.type();

      this.loading.set(true);
      this.fetchExternalPromotionQuality({date, area, rideType, source});
    });
  }


  @SwitchMap()
  private fetchExternalPromotionQuality(body: ExternalPromotionInput) {
    return this.apiService.fetchExternalPromotionQuality(body).pipe(
      finalize(() => {
        setTimeout(() => {
          this.loading.set(false);
        }, 0);
      })
    ).subscribe(res => {
      if (!res.data) {
        return;
      }
      
      this.data.set(res.data);
    })
  }
  
}
