<div [ngClass]="{'opacity-0 pointer-events-none': !loading()}" class="absolute inset-0 z-10 flex items-center justify-center text-neutral-400 text-lg bg-neutral-50 rounded-lg transition-opacity">
  <LoadingIcon />
</div>

<!-- <div class="flex justify-end px-3 pb-1">
  <nz-select [(ngModel)]="trendDate" nzSize="small">
    @for (item of trendDateOptions; track $index) {
      <nz-option [nzValue]="item.value" [nzLabel]="item.label"></nz-option>
    }
  </nz-select>
</div> -->

<div class="relative flex-1 min-h-0">
  <app-graph-combination [themes]="themes()" [categories]="categories()" [value]="series()" />
</div>