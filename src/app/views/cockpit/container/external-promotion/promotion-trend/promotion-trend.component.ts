import { NgClass } from '@angular/common';
import { ChangeDetectionStrategy, Component, effect, inject, input, signal } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { finalize } from 'rxjs';

import { CockpitApiService } from '@views/cockpit/api';
import { CockpitService } from '@views/cockpit/services';
import { CombinationSeries, GraphCombinationComponent } from '@shared/components/graph';
import { PromotionMetricsTrendVo, PromotionTrendInput, TrendDate } from '@views/cockpit/models';
import { IconLoadingComponent } from '@shared/modules/icons';
import { groupBy, isUndefined } from '@common/function';

import { ExternalPromotionComponent } from '../external-promotion.component';


@Component({
  selector: 'app-promotion-trend',
  templateUrl: './promotion-trend.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: {
    'class': 'flex flex-col h-full'
  },
  imports: [
    NgClass,
    FormsModule,
    NzSelectModule,
    IconLoadingComponent,
    GraphCombinationComponent,
  ],
})
export class PromotionTrendComponent {

  service = inject(CockpitService);
  parent = inject(ExternalPromotionComponent);
  apiService = inject(CockpitApiService);
  
  themes = input<string[]>([]);
  type = input<'passenger' | 'driver'>();

  trendDate = signal<TrendDate>(1);
  trendDateOptions = [
    { label: '过去一年', value: 1 },
    { label: '过去两年', value: 2 },
  ];
  series = signal<CombinationSeries[]>([]);
  categories = signal<string[]>([]);
  loading = signal(false);

  constructor() {
    effect(() => {
      const date = this.service.date();
      const area = this.service.area();
      const rideType = this.service.rideType();
      const source = this.parent.type();
      const trendDate = this.trendDate();
      const type = this.type();
      
      this.loading.set(true);
      this.fetchExternalPromotionTargetTrend({date, area, rideType, source, type, trendDate});
    });
  }


  private fetchExternalPromotionTargetTrend(body: PromotionTrendInput) {
    this.apiService.fetchExternalPromotionTargetTrend(body).pipe(
      finalize(() => {
        setTimeout(() => {
          this.loading.set(false);
        }, 0);
      })
    ).subscribe(res => {
      if (!res.data) {
        return;
      }

      const { years, series } = this.handleTrends(res.data);

      this.categories.set(years);
      this.series.set(series);
    })
  }


  handleTrends(data: PromotionMetricsTrendVo[]) {    
    const values = groupBy<PromotionMetricsTrendVo>(data, 'year');
    const series = [];
    const years = [] as string[];
    const seriesObj: {[key in keyof Partial<PromotionMetricsTrendVo>]: number[]} = {
      natureValue: [],
      paymentRatio: [],
      promotionValue: [],
      value: [],
    };

    Object.keys(values).forEach(key => {
      const items = values[key].sort((a, b) => Number(a.month) - Number(b.month));
      const natureValue = items.map(item => isUndefined(item?.natureValue) ? null : item?.natureValue);
      const paymentRatio = items.map(item => isUndefined(item?.paymentRatio) ? null : item?.paymentRatio);
      const promotionValue = items.map(item => isUndefined(item?.promotionValue) ? null : item?.promotionValue);
      const value = items.map(item => isUndefined(item?.value) ? null : item?.value);

      years.push(...items.map(item => `${item.year}年${item.month}月`));        
      seriesObj.natureValue.push(...natureValue);
      seriesObj.paymentRatio.push(...paymentRatio);
      seriesObj.promotionValue.push(...promotionValue); 
      seriesObj.value.push(...value);
    });

    const name = this.type() === 'passenger' ? '乘客' : '车主';
    const type = this.parent.type() === 'new' ? '新增' : '召回';
    
    series.push(new CombinationSeries(`${type}${name}数`, 'line', 0, 3, seriesObj.value));
    series.push(new CombinationSeries('自然量', 'line', 0, 3, seriesObj.natureValue));
    series.push(new CombinationSeries('推广量', 'line', 0, 3, seriesObj.promotionValue));
    series.push(new CombinationSeries('付费占比', 'column', 1, 2, seriesObj.paymentRatio, undefined, { valueSuffix: '%' }));

    return {years, series};
  }
  
}
