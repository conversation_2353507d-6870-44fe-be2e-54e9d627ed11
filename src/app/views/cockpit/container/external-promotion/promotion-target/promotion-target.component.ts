import { ChangeDetectionStrategy, Component, computed, effect, inject, signal } from '@angular/core';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { finalize } from 'rxjs';

import { SwitchMap } from '@common/decorator';
import { CockpitService } from '@views/cockpit/services';
import { CockpitApiService } from '@views/cockpit/api';
import { IconLoadingComponent } from '@shared/modules/icons';
import { ExternalPromotionInput, ExternalPromotionTargetOutput } from '@views/cockpit/models';
import { IndicatorModule } from '@shared/components/indicator';
import { NgLetDirective } from '@shared/directives';

import { PromotionTrendComponent } from '../promotion-trend/promotion-trend.component';
import { ExternalPromotionComponent } from '../external-promotion.component';


@Component({
  selector: 'app-promotion-target',
  templateUrl: './promotion-target.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NzSelectModule,
    IndicatorModule,
    IconLoadingComponent,
    PromotionTrendComponent,
    NgLetDirective,
  ],
})
export class PromotionTargetComponent {
  
  service = inject(CockpitService);
  parent = inject(ExternalPromotionComponent);
  apiService = inject(CockpitApiService);
  data = signal<ExternalPromotionTargetOutput>(null);
  loading = signal(false);

  targetVisible = computed(() => {
    return (
      this.service.area() === '全国' &&
      this.service.rideType() === 'ALL'
    );
  });


  constructor() {
    effect(() => {
      const date = this.service.date();
      const area = this.service.area();
      const rideType = this.service.rideType();
      const source = this.parent.type();

      this.loading.set(true);
      this.fetchExternalPromotionTarget({ date, area, rideType, source });
    });
  }


  @SwitchMap()
  private fetchExternalPromotionTarget(body: ExternalPromotionInput) {
    return this.apiService.fetchExternalPromotionTarget(body).pipe(
      finalize(() => {
        setTimeout(() => {
          this.loading.set(false);
        }, 0);
      })
    ).subscribe(res => {
      if (!res.data) {
        return;
      }

      this.data.set(res.data);
    })
  }

}
