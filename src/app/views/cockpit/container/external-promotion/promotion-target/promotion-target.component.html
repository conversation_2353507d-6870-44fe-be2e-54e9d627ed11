<div class="flex gap-x-3">
  <div class="flex-1 flex flex-col gap-2.5">
    <header class="relative m-5 mb-0">
      <div class="absolute inset-0 flex items-center justify-center opacity-50">乘客视角</div>
      业务目标达成
    </header>
    <div>
      <div *ngLet="data()?.passenger as target" class="max-w-135 mx-auto mb-2.5 h-16 flex items-center justify-center gap-x-3">
        <div class="flex flex-col gap-y-1 translate-y-1">
          <div class="flex items-center">
            <ng-template #metricsTitleTemplate>{{target?.aliasName}} <span class="text-xs opacity-30 px-1">({{target?.metricsName}})</span></ng-template>
            <app-indicator-title fontSize="text-xs" nzPopoverPlacement="topLeft" [nzPopoverTitle]="metricsTitleTemplate" [nzPopoverContent]="target?.metricsDescription">
              <span class="text-sm font-semibold mr-1">{{target?.aliasName}}</span>
              <span class="inline-block text-neutral-400 scale-75 origin-left">{{target?.avgType}}</span>
            </app-indicator-title>
            <app-indicator-value fontSize="text-lg" class="min-w-24 text-right" [value]="target?.value" />
          </div>

          <app-indicator-compare-group>
            <app-indicator-compare justifyContent="justify-between" label="月环比" [value]="target?.quarterOnQuarterGrowth" />
            <app-indicator-compare justifyContent="justify-between" label="年同比" [value]="target?.yearOnYearGrowth" />
          </app-indicator-compare-group>
        </div>
      </div>

      <div class="relative h-60">
        <app-promotion-trend type="passenger" />
      </div>
    </div>
  </div>
  
  
  <div class="flex-1 flex flex-col gap-2.5">
    <header class="relative m-5 mb-0">
      <div class="absolute inset-0 flex items-center justify-center opacity-50">车主视角</div>
      &nbsp;
    </header>
    <div>
      <div *ngLet="data()?.driver as target" class="max-w-135 mx-auto mb-2.5 h-16 flex items-center justify-center gap-x-3">
        <div class="flex flex-col gap-y-1 translate-y-1">
          <div class="flex items-center">
            <ng-template #metricsTitleTemplate>{{target?.aliasName}} <span class="text-xs opacity-30 px-1">({{target?.metricsName}})</span></ng-template>
            <app-indicator-title fontSize="text-xs" nzPopoverPlacement="topLeft" [nzPopoverTitle]="metricsTitleTemplate" [nzPopoverContent]="target?.metricsDescription">
              <span class="text-sm font-semibold mr-1">{{target?.aliasName}}</span>
              <span class="inline-block text-neutral-400 scale-75 origin-left">{{target?.avgType}}</span>
            </app-indicator-title>
            <app-indicator-value fontSize="text-lg" class="min-w-24 text-right" [value]="target?.value" />
          </div>

          <app-indicator-compare-group>
            <app-indicator-compare justifyContent="justify-between" label="月环比" [value]="target?.quarterOnQuarterGrowth" />
            <app-indicator-compare justifyContent="justify-between" label="年同比" [value]="target?.yearOnYearGrowth" />
          </app-indicator-compare-group>
        </div>
      </div>

      <div class="relative h-60">
        <app-promotion-trend type="driver" [themes]="['#ff9900', '#65789b59']" />
      </div>
    </div>
  </div>
</div>


<ng-template #loadingTemplate>
  <div class="flex items-center justify-center w-full h-full text-neutral-400 text-lg">
    <LoadingIcon />
  </div>
</ng-template>