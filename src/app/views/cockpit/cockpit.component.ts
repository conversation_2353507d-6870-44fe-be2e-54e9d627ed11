import { DatePipe } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { AfterViewInit, ChangeDetectionStrategy, Component, OnDestroy, Renderer2, effect, inject, signal } from '@angular/core';
import { NzDrawerService } from 'ng-zorro-antd/drawer';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzAnchorModule } from 'ng-zorro-antd/anchor';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzIconModule } from 'ng-zorro-antd/icon';
import { differenceInCalendarMonths, addMonths } from 'date-fns';

import { trace } from '@common/const';
import { RibbonModule } from '@shared/modules/ribbon';
import { ModalService, ModalComponent } from '@core/modal';
import { PAGE_NAME, PageEnterLeaveDirective } from '@common/directive';
import { BuriedPointActionType, BuriedPointService } from '@common/service';
import { BackTopComponent } from '@shared/components/back-top';
import { SelfhelpBtnComponent } from '@shared/components/selfhelp-btn';
import { ProgressLoaderComponent } from '@shared/components/progress-loader';
import { NavigationComponent } from '@shared/components/navigation';
import { SkeletonComponent } from '@shared/components/skeleton';
import { QueryRouteParamDirective } from '@shared/directives/query-route-param';
import { RadioModule } from '@shared/modules/headless/radio';
import { ShowFullTextDirective } from '@shared/directives/show-full-text';
import { AreaSelectComponent } from '@shared/components/area-select';
import { ConversionComponent, MetricsOverviewComponent, ExternalPromotionComponent, InternalMarketingComponent } from './container';
import { FunnelOrderComponent, InfluenceFlowComponent } from './container/tencent-channel';
import { CockpitService, FluctuateDrillService } from './services';
import { DataUpdateVo } from './models/data-update.vo';
import { CockpitApiService } from './api';


@Component({
  selector: 'app-cockpit',
  templateUrl: './cockpit.component.html',
  host: {
    class: 'block'
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    PageEnterLeaveDirective,
  ],
  imports: [
    FormsModule,
    NzIconModule,
    NzAnchorModule,
    NzToolTipModule,
    NzDatePickerModule,
    NzButtonModule,
    RibbonModule,
    RadioModule,
    MetricsOverviewComponent,
    ExternalPromotionComponent,
    InternalMarketingComponent,
    ConversionComponent,
    FunnelOrderComponent,
    InfluenceFlowComponent,
    ProgressLoaderComponent,
    NavigationComponent,
    SkeletonComponent,
    AreaSelectComponent,
    BackTopComponent,
    SelfhelpBtnComponent,
    ShowFullTextDirective,
    QueryRouteParamDirective,
    DatePipe,
  ],
  providers: [
    { provide: PAGE_NAME, useValue: 'home' },
    DatePipe,
  ]
})
export class CockpitComponent implements AfterViewInit, OnDestroy {

  readonly router = inject(Router);
  readonly route = inject(ActivatedRoute);
  readonly datePipe = inject(DatePipe);
  readonly renderer2 = inject(Renderer2);
  readonly buriedPointService = inject(BuriedPointService);
  readonly fluctuateDrillService = inject(FluctuateDrillService);
  readonly apiService = inject(CockpitApiService);
  readonly service = inject(CockpitService);

  readonly modalService = inject(ModalService);
  readonly drawerService = inject(NzDrawerService);

  isScrollDown = signal(false);
  commonData = signal<DataUpdateVo>(null);

  readonly areaItems = [
    '全国', '广东', '江浙沪', '川渝', '京津冀', '东北三省', '山东', 
    'top20城市', '北京', '上海', '广州', 
  ];

  private _listenScrollFn: () => void;
  

  constructor() {
    effect(() => {
      const area = this.service.area();
      const ride_type = this.service.rideType();
      const source_type = this.service.sourceMap.get(this.service.sourceType());
      
      this.buriedPointService.addStat('dida_dpm_caerus_fliter_enquire', {
        area,
        ride_type,
        source_type,
      });
    })
    
    effect(() => {
      trace(`埋点上报: tab点击 -> 概览 -> ${this.service.sourceType()}`);
      
      this.buriedPointService.addStat('dida_dpm_caerus_tab_click', {
        source: 'overview',
        action_type: BuriedPointActionType.CLICK,
        source_type: this.service.sourceType()
      });

      if (this.service.month()) {
        this.fetchCommon();
      }
    })

    effect(() => {
      const value = this.fluctuateDrillService.areaName();

      if (value) {
        this.service.area.set(value);
      }
    });

    effect(() => {
      const value = this.fluctuateDrillService.rideType();

      if (value) {
        this.service.rideType.set(value);
      }
    });
  }

  
  ngAfterViewInit(): void {
    if (!this.service.rideType()) {
      this.service.rideType.set('ALL');
    }

    this._subscribeToScroll();
  }
  

  ngOnDestroy(): void {
    this._listenScrollFn();
    this.fluctuateDrillService.reset();
  }


  private fetchCommon() {
    this.apiService.fetchCommon().subscribe(res => {
      if (!res.data) {
        return;
      }

      setTimeout(() => {
        this.commonData.set(res.data);
      }, 0)
    });
  }


  disabledMonth = (current: Date): boolean => {
    // Can not select days before today and today
    const startDateForMonth = new Date().setDate(1);
    const dateRight = addMonths(startDateForMonth, 1);
    
    return differenceInCalendarMonths(current, dateRight) > -1;
  }


  open() {
    const componentRef = this.modalService.open(ModalComponent, null);

    componentRef.setInput('title', 'Caerus本期上线需求如下');
    componentRef.setInput('content', `
      <ul class="list-decimal px-4">
        <li>波动分析模块：波动区域部分增加点击传参跳转价格监控模块的功能；增加表格数据下载与数值范围筛选功能。</li>
        <li>自助分析模块：增加新激活用户、新注册用户、新认证车主等指标，放在端外推广专题下，点击【恢复初始设置按钮】或者在自选指标中勾选，均可找到。</li>
        <li>Caerus价格监控部分，在区域定位的表格中，增加了数值范围筛选的功能，用户可以通过设置指标的数值范围，搜索到符合要求的区域或城市。</li>
        <li>乘客侧、车主侧都增加首单乘客、首单车主、新激活用户、新注册用户、新认证车主的完单留存、佣金贡献（LTV）数据，车主侧还额外关注30、60、90天后车主ABC身份的转化情况；</li>
        <li>为了避免过度聚合导致数据失真，价格差异归因【只支持分城市、分公里段】数据查看，不支持城市、公里段的聚合数据。</li>
        <li>区域定位-【嘀嗒与DD线上价格差异率迷你图】与价格差异归因做了联动设置，用户点击城市对应公里段的价格差异率数值，可传参滚动到价格差异归因；</li>
        <li>区域定位，用户选择了具体公里段，也可以点击城市对应的【嘀嗒与DD线上价格差异率】数值，可传参滚动到价格差异归因；</li>
        <li>区域定位表格中的迷你图，都是可点击的；点击后会弹出对应的弹窗，下钻到公里段粒度给出相关指标的明细数据；</li>
        <li>以嘀嗒与DD线上价格差异率的迷你图为例，点击下钻到公里段粒度后，可以查看各公里段的订单占比和价格差异；还可以看各公里段我们价格高、低的路线数量占比，以及高、低的程度；</li>
        <li>乘客、车主新增推广规模区域排行模块；</li>
        感谢大家对Caerus的支持，如有任何问题或者建议，欢迎随时反馈~ <br />
      </ul>
    `);
  }


  private _subscribeToScroll() {
    this._listenScrollFn = this.renderer2.listen(window, 'scroll', () => {
      let st = window.scrollY;
      
      this.isScrollDown.set(st > 64);
    })
  }


  reset() {
    this.service.area.set('全国');
    this.service.rideType.set('ALL');
    this.fluctuateDrillService.reset();
    this.buriedPointService.addStat('dida_dpm_caerus_reset_click', {
      action_type: BuriedPointActionType.CLICK,
    });
  }

}
