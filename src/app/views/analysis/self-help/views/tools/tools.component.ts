import { FormsModule } from '@angular/forms';
import { JsonPipe, NgClass, NgIf } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, Component, DestroyRef, HostListener, computed, effect, inject, input, output, signal, viewChild } from '@angular/core';
import { CdkDragDrop, DragDropModule } from '@angular/cdk/drag-drop';
import { ActivatedRoute, RouterLink } from '@angular/router';
import { Clipboard } from '@angular/cdk/clipboard';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { Subscription, debounceTime, filter, finalize, lastValueFrom, map, shareReplay, skip, take, tap, timeout } from 'rxjs';
import { NzRadioModule } from 'ng-zorro-antd/radio';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzModalService } from 'ng-zorro-antd/modal';
import { NzDrawerRef } from 'ng-zorro-antd/drawer';
import { NzAlertModule } from 'ng-zorro-antd/alert';

import { isDev, trace, VERSION } from '@common/const';
import { Before } from '@common/decorator';
import { CaerusApiService } from '@api/caerus';
import { AnalysisChartDetailVo } from '@api/caerus/model';
import { EventInterceptor, moveItemInFormArray, sleep, sortCategoriesFn3, toNumber } from '@common/function';
import { getProportionFields } from '@common/chart';
import { BuriedPointService, LegendControlService, LegendItemClickHandler } from '@common/service';
import { BaseHighCharts } from '@common/chart/highcharts';
import { PAGE_NAME, PageEnterLeaveDirective } from '@common/directive';
import { UserInfoService } from '@api/user-info.service';
import { QueryOutputVo } from '@api/query-engine/model';
import { QueryEngineApiService } from '@api/query-engine';
import { CheckboxModule, RadioModule, BreadcrumbModule } from '@shared/modules/headless';
import { QueryEngineFormService } from '@common/service/query-engine';
import { IconCopyComponent, IconDownloadComponent, IconPlayComponent } from '@shared/modules/icons';
import { GraphComponent } from '@shared/components/graph';
import { LineSpinComponent } from '@shared/components/line-spin';
import { DebugDirective } from '@shared/directives';
import { ModalService, ModalComponent } from '@core/modal';
import { ShowFullTextDirective } from '@shared/directives/show-full-text';
import { RibbonModule } from '@shared/modules/ribbon';

import { BarStack, BarStackMultipleXAxis } from '../../model/bar';
import { AxesControlComponent, ComparisonFilterItemComponent, NumericTableComponent } from '../../components';
import { ComparisonFilterComponent } from '../../components/comparison-filter/comparison-filter.component';
import { CommonFilterComponent } from '../../components/common-filter/common-filter.component';
import { DateFilterComponent, DimensionFilterComponent } from '../../components/filters';
import { ChartEditorComponent } from '../dashboard/components/modal';
import { BasicLine, MultipleXAxis } from '../../model/line';


@Component({
  selector: 'app-tools',
  templateUrl: './tools.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: {
    class: 'bg-white',
  },
  hostDirectives: [PageEnterLeaveDirective],
  imports: [
    NgClass,
    JsonPipe,
    RouterLink,
    DragDropModule,
    FormsModule,
    NzAlertModule,
    NzToolTipModule,
    NzButtonModule,
    NzRadioModule,
    NzSpinModule,
    RadioModule,
    CheckboxModule,
    BreadcrumbModule,
    RibbonModule,
    CommonFilterComponent,
    ComparisonFilterComponent,
    ComparisonFilterItemComponent,
    DimensionFilterComponent,
    AxesControlComponent,
    DateFilterComponent,
    LineSpinComponent,
    NumericTableComponent,
    IconPlayComponent,
    IconDownloadComponent,
    IconCopyComponent,
    // IconGraphColumnStackComponent,
    // IconGraphLineComponent,
    GraphComponent,
    ShowFullTextDirective,
    DebugDirective,
  ],
  providers: [
    { provide: PAGE_NAME, useValue: 'self-help' },
    QueryEngineFormService,
    LegendControlService,
    JsonPipe
  ],
})
export class ToolsComponent implements AfterViewInit {

  readonly modal = inject(NzModalService);
  readonly route = inject(ActivatedRoute);
  readonly jsonPipe = inject(JsonPipe);
  readonly clipboard = inject(Clipboard);
  readonly destroyRef = inject(DestroyRef);
  readonly apiService = inject(QueryEngineApiService);
  readonly caerusApiService = inject(CaerusApiService);
  readonly formService = inject(QueryEngineFormService);
  readonly modalService = inject(ModalService);
  readonly drawerRef = inject(NzDrawerRef, { optional: true });
  readonly buriedPointService = inject(BuriedPointService);
  readonly page_name = inject(PAGE_NAME);
  readonly legendControlService = inject(LegendControlService);
  readonly userInfoService = inject(UserInfoService);
  readonly message = inject(NzMessageService);

  comparisonFilterRef = viewChild(ComparisonFilterComponent);
  commonFilterRef = viewChild(CommonFilterComponent);

  queryId = input<string>();
  subjectPath = input<string>();
  dashboardPath = computed(() => {
    const path = this.subjectPath();
    if (path) {
      return path.split(',').map(toNumber);
    }
    return [];
  });
  data = signal<Partial<AnalysisChartDetailVo>>({});
  checking = signal(false);
  querying = signal(false);
  sourceType = signal<'trend' | 'constitute' | 'comparison'>('trend');
  sourceType$ = toObservable(this.sourceType);
  beforeSourceType = signal<'trend' | 'constitute' | 'comparison'>(null);
  chartType = signal<'line' | 'column'>('line');
  chartType$ = toObservable(this.chartType);
  showType = signal<'number' | 'ratio'>('number');
  showType$ = toObservable(this.showType);

  /** @name 排序类型 默认 | 基准期｜对比期 */
  orderType = signal<'base' | 'compare'>(null);

  /** @name 排序方式 默认 | 倒序｜正序 */
  orderBy = signal<'desc' | 'asc'>('desc');

  option = signal<BaseHighCharts>(null);
  option$ = toObservable(this.option);
  chartRawData = signal<QueryOutputVo>(null);
  chartData = signal<QueryOutputVo>(null);
  noData = signal(false);
  errorMessage = signal<string>(null);
  isTimeout = signal(false);
  searchSubscription: Subscription;
  chart: BaseHighCharts = null;

  isCreate = computed(() => this.queryId() === undefined);
  isEdit = computed(() => !this.isCreate());

  analysisType = computed(() => {
    const arr = ['trend', 'constitute', 'comparison'];
    const index = arr.indexOf(this.sourceType());
    return index + 1;
  });

  downloadBtnVisible = computed(() => {
    const menus = this.userInfoService.menu();
    if (menus) {
      const { frontButton } = menus.find(menu => menu.url === '/analysis/self-help') || {};

      return frontButton?.some(item => item.name === '下载');
    }
    return false;
  });

  showTypeVisible = computed(() => {
    if (this.chartRawData()) {
      const { headers } = this.chartRawData();
      const [proportionFields] = getProportionFields(headers);

      return proportionFields?.length > 0;
    }
    return false;
  });

  isShare = computed(() => {
    return this.isEdit();
  });

  hasAuth = computed(() => {
    const username = this.data().createBy;
    return isDev() || username === window.localStorage.getItem('caerus.auth.username.py') || username === window.localStorage.getItem('caerus.auth.username');
  });

  dimension$ = this.caerusApiService.fetchDimension('menu-dimension').pipe(
    map(res => res.data),
    shareReplay(1)
  );

  metrics$ = this.caerusApiService.fetchMetrics().pipe(
    map(res => res.data),
    shareReplay(1)
  );

  query$ = output();

  hasChangeLog(version: string) {
    const changelog = window.localStorage.getItem('caerus.self-help.changelog');

    if (changelog && changelog.includes(version)) {
      return false
    }

    return true;
  }

  constructor() {
    this.formService.dt.reset({
      startTime: 'now-14d',
      endTime: 'now-1d',
    });

    effect(() => {
      if (this.sourceType() !== 'constitute') {
        this.formService.dimensions.clear();
      }

      if (this.sourceType()) {
        this.option.set(null);
        this.chartRawData.set(null);
      }
    });

    effect(() => {
      const data = this.chartRawData();
      const chartType = this.chartType();

      if (data && chartType) {
        this.option.set(null);
        this.legendControlService.setShowType(null);
        // this.showType.set(null);
        setTimeout(() => {
          this._setChartData();
        });
      }
    });

    effect(() => {
      const showType = this.showType();

      if (this.option()) {
        this.legendControlService.setShowType(showType);
      }
    });

    effect(() => {
      if (this.sourceType() === 'constitute') {
        trace(`埋点上报：自助分析工具-图表筛选项被点击`, {
          showType: this.showType() || '全部',
          chartType: this.chartType(),
        });

        this.buriedPointService.addStat('dida_dpm_caerus_Selfhelp_enquire_click', {
          page_name: this.page_name,
          showType: this.showType() || '全部',
          chartType: this.chartType(),
        });
      }
    });

    /**
     * 排序方式变化时修改图表排序函数
     */
    effect(() => {
      const orderType = this.orderType();
      const orderBy = this.orderBy();

      if (this.chart) {
        if (orderType === null) {
          this.chart.sortFn = sortCategoriesFn3('series.name');
        } else {
          this.chart.sortFn = (a, b) => {
            if (orderBy === 'asc') {
              return a.y - b.y;
            } else if (orderBy === 'desc') {
              return b.y - a.y;
            }

            return 0;
          };
        }
      }
    });
  }

  ngAfterViewInit(): void {
    this.isEdit() && this.fetchChartDetail();
    this._subscribeToChartTypeChange();
    this._subscribeToShowTypeChange();
    this._subscribeToSourceTypeChange();
    this._subscribeToFormChange();
    this.caerusApiService.fetchMenuMetricsTag().subscribe();
  }

  private fetchChartDetail() {
    this.caerusApiService.fetchAnalysisChartDetail(this.queryId()).subscribe(async res => {
      if (res.status !== '00000') {
        return this.message.error(res.error || res.message);
      }

      const { analysisType, chartShowConfig } = res.data;
      const arr = ['trend', 'constitute', 'comparison'];

      this.data.set(res.data);
      this.sourceType.set(arr[analysisType - 1] as any);

      if (chartShowConfig) {
        let { showType, chartType, orderType, orderBy } = JSON.parse(chartShowConfig) || {};

        if (chartType === 'line') {
          showType = null;
        }
        
        chartType && this.chartType.set(chartType);
        showType && this.showType.set(showType);
        orderType && this.orderType.set(orderType);
        orderBy && this.orderBy.set(orderBy);
      }

      await sleep(500);

      (
        this.sourceType() === 'comparison'
          ? this.comparisonFilterRef()
          : this.commonFilterRef()
      ).metrics$.pipe(
        filter(metrics => metrics.length > 0),
        take(1)
      ).subscribe(async () => {
        this.formService.init(res.data.chartConfig);
        await sleep(100);
        this.query();
      });
    });
  }

  private _subscribeToChartTypeChange() {
    // this.chartType$.pipe(skip(2), takeUntilDestroyed(this.destroyRef)).subscribe(() => {
    //   this.showType.set('number');
    // });
  }

  private _subscribeToShowTypeChange() {
    this.showType$.pipe(takeUntilDestroyed(this.destroyRef)).subscribe((type) => {
      if (type === 'ratio') {
        this.chartType.set('line');
      }
    })
  }

  private _subscribeToSourceTypeChange() {
    this.sourceType$.pipe(
      takeUntilDestroyed(this.destroyRef)
    ).subscribe(type => {
      const a = ['constitute', 'trend'];

      if (
        (a.includes(type) && this.beforeSourceType() === 'comparison') || 
        (a.includes(this.beforeSourceType()) && type === 'comparison')
      ) {
        this.formService.filterItems.clear();
        this.formService.metrics.clear();
      }

      // 如果从构成分析切换到趋势分析，需要重置showType，否则将会出现无数据的现象
      if (
        this.beforeSourceType() === 'constitute' &&
        type === 'trend'
      ) {
        this.showType.set(null);
      }

      this.beforeSourceType.set(type);
    });
  }

  private _subscribeToFormChange() {
    this.formService.form.valueChanges
      .pipe(
        // skip(11),
        tap(() => {
          setTimeout(() => {
            this.checking.set(true);
          }, 0);
        }),
        debounceTime(100),
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe(() => {
        this.fetchSelectList();
      });
  }

  private fetchSelectList() {
    const body = this.formService.getSelectedList();

    this.apiService
      .fetchSelectList(body, 'select-list')
      .pipe(finalize(() => this.checking.set(false)))
      .subscribe(res => {
        if (!res.data) {
          return console.error(res);
        }

        this.formService.support.set(res.data);
      });
  }

  private _updateSortFn() {
    const orderType = this.orderType();
    const orderBy = this.orderBy();

    if (this.chart) {
      if (orderType === null) {
        this.chart.sortFn = sortCategoriesFn3('series.name');
      } else {
        this.chart.sortFn = (a, b) => {
          if (orderBy === 'asc') {
            return a.y - b.y;
          } else if (orderBy === 'desc') {
            return b.y - a.y;
          }

          return 0;
        };
      }
    }
  }

  private _setChartData() {
    const data = this.chartRawData();
    const legendItemClick = LegendItemClickHandler(this.legendControlService);
    const dtType = this.formService.dtType.value;

    try {
      if (data.data === null) {
        throw '当前日期无数据';
      }

      if (this.formService.hasDateCompare() && data.compareData === null) {
        throw '对比日期无数据';
      }

      this.chart = null;

      if (this.sourceType() === 'trend' || this.sourceType() === 'comparison') {
        if (this.formService.hasDateCompare()) {
          this.chart = new MultipleXAxis({ ...data }, dtType);
          this.chartData.set({ ...data });
        } else {
          this.chart = new BasicLine({ ...data }, dtType);
          this.chartData.set({ ...data });
        }
        this.chart.plotOptions.series.events = { legendItemClick };
      }

      if (this.sourceType() === 'constitute') {
        if (this.formService.hasDateCompare()) {
          this.chart = new BarStackMultipleXAxis({ ...data }, this.chartType(), dtType);
          this.chartData.set({ ...data });
        } else {
          this.chart = new BarStack({ ...data }, this.chartType(), dtType);
          this.chartData.set({ ...data });
        }

        this.chart.plotOptions = {
          series: {
            events: { legendItemClick },
          },
        };
      }

      this.option.set(this.chart?.getOption() || null);
      this._updateSortFn();
    } catch (e: any) {
      console.warn(e);
      this.errorMessage.set(e);
    }
  }

  public formValid() {
    if (this.sourceType() === 'comparison') {
      if (this.formService.metrics.length < 1) {
        this.message.warning('您尚未添加对比项');
        return false;
      }
    }

    if (this.formService.dt.invalid) {
      this.message.warning('请选择日期');
      return false;
    }

    if (this.formService.compareDt?.invalid) {
      this.message.warning('请选择对比日期');
      return false;
    }

    if (this.sourceType() === 'constitute' && this.formService.dimensions.length < 1) {
      this.message.warning('请先选择构成维度');
      return false;
    }

    if (this.formService.metrics.length < 1) {
      this.message.warning('请先选择指标');
      return false;
    }

    if (this.formService.metrics.length > 10 && this.sourceType() !== 'comparison') {
      this.message.warning('指标过多会影响查询速度，建议指标选择不超过10个');
      return false;
    }

    if (this.formService.metrics.length > 15 && this.sourceType() === 'comparison') {
      this.message.warning('指标过多会影响查询速度，建议指标选择不超过15个');
      return false;
    }

    return true;
  }

  drop(event: CdkDragDrop<any[]>) {
    moveItemInFormArray(this.formService.metrics, event.previousIndex, event.currentIndex);
  }

  query(isFromButtonEvent?: boolean) {
    const body = this.formService.value();

    if (isFromButtonEvent) {
      this.query$.emit();
    }

    if (this.comparisonFilterRef()) {
      this.comparisonFilterRef().restore();
    }

    if (this.sourceType() === 'constitute') {
      const metrics_copy = body.metrics.map(item => {
        return {
          ...item,
          customType: 'proportion',
          proportionDimension: [{ extendName: body.dtType }],
        };
      });
      body.metrics = body.metrics.concat(metrics_copy);
    }
    
    if (this.sourceType() === 'comparison') {
      body.queryType = 'compare';
    }

    body.dataFillConfig = {
      open: 1,
      fillRangeType: 1,
      fillValue: 1,
    };

    if (this.formValid()) {
      this.option.set(null);
      this.chartRawData.set(null);
      this.chartData.set(null);
      this.querying.set(true);
      this.noData.set(false);
      this.isTimeout.set(false);
      this.legendControlService.reset();
      this.legendControlService.legends.set(null);

      this.searchSubscription = this.apiService
        .search(body, 'self-help')
        .pipe(
          finalize(() => this.querying.set(false)),
          timeout(5 * 1000 * 60)
        )
        .subscribe({
          next: res => {
            if (res.status !== '00000') {
              res.message && console.warn(`${res.message}`);
              res.error && console.warn(`${res.error}`);
            }

            if (res.data) {
              this.chartRawData.set(res.data);
            } else {
              this.noData.set(true);
            }
          },
          error: err => {
            console.error(err);
            this.isTimeout.set(true);
          },
        });

      trace(`埋点上报: 自助分析 -> 查询按钮点击`);
      this.buriedPointService.addStat('dida_dpm_caerus_Selfhelp_enquire_click', {
        source: body,
        has_date_compare: this.formService.hasDateCompare(),
        source_type: this.sourceType(),
        dimensions: body.dimensions,
        filters: body.filter,
        metrics: body.metrics,
        compareDt: body.compareDt,
        dt: body.dt,
      });
    }
  }

  download() {
    const body = this.formService.value();

    if (this.formValid()) {
      trace(`埋点上报: 自助分析 -> 下载按钮点击`);
      this.apiService.exportToExcel(body).subscribe(() => {
        // 下载埋点
        this.buriedPointService.addStat('dida_dpm_caerus_Selfhelp_download_click', {
          source: body,
          has_date_compare: this.formService.hasDateCompare(),
          source_type: this.sourceType(),
          dimensions: body.dimensions,
          filters: body.filter,
          metrics: body.metrics,
          compareDt: body.compareDt,
          dt: body.dt,
        });
      });
    }
  }

  restore() {
    this.formService.filterItems.clear();
    this.formService.metrics.clear();
  }

  close() {
    this.drawerRef.close();
  }

  copy() {
    const value = this.formService.value();
    this.clipboard.copy(this.jsonPipe.transform(value));
    this.message.success('已复制');
  }

  @Before(EventInterceptor)
  @Before(ctx => ctx.formValid())
  @HostListener('document:keydown.Meta.S', ['$event'])
  @HostListener('document:keydown.Control.S', ['$event'])
  handleSave() {
    this.modal.create({
      nzTitle: '保存到看板',
      nzContent: ChartEditorComponent,
      nzWidth: 600,
      nzData: {
        dashboardPath: this.dashboardPath(),
        ...this.data(),
        analysisType: this.analysisType(),
        chartConfig: JSON.stringify(this.formService.value()),
        chartShowConfig: JSON.stringify({
          showType: this.showType(),
          chartType: this.chartType(),
          orderType: this.orderType(),
          orderBy: this.orderBy(),
        }),
      },
      nzOnOk: async ({ valid, value }) => {
        if (valid) {
          const request$ = this.caerusApiService.saveAnalysisChart(value).pipe(
            map(res => {
              if (res.status !== '00000') {
                this.message.error(res.error || res.message);
              }
              if (res.status === '00000') {
                this.message.success('保存成功');
                return true;
              }
              return false;
            })
          );

          return lastValueFrom(request$);
        }

        return false;
      },
    });
  }

  @Before(EventInterceptor)
  @Before(ctx => ctx.formValid())
  @HostListener('document:keydown.shift.Meta.S', ['$event'])
  @HostListener('document:keydown.shift.Control.S', ['$event'])
  handleSaveAs() {
    const { id, ...data } = this.data();

    this.modal.create({
      nzTitle: '另存为',
      nzContent: ChartEditorComponent,
      nzWidth: 600,
      nzData: {
        dashboardPath: this.dashboardPath(),
        data,
        analysisType: this.analysisType(),
        chartConfig: JSON.stringify(this.formService.value()),
        chartShowConfig: JSON.stringify({
          showType: this.showType(),
          chartType: this.chartType(),
          orderType: this.orderType(),
          orderBy: this.orderBy(),
        }),
      },
      nzOnOk: async ({ valid, value }) => {
        if (valid) {
          const request$ = this.caerusApiService.saveAnalysisChart(value).pipe(
            map(res => {
              if (res.status !== '00000') {
                this.message.error(res.error || res.message);
              }
              if (res.status === '00000') {
                this.message.success('保存成功');
                return true;
              }
              return false;
            })
          );

          return lastValueFrom(request$);
        }

        return false;
      },
    });
  }


  open() {
    const componentRef = this.modalService.open(ModalComponent, null);
    
    componentRef.setInput('title', '自助分析本期上线指标如下');
    componentRef.setInput('content', `
      <ul class="list-decimal px-4">
        <li>本期上线了<strong>【日留存】</strong>和<strong>【周期转化】</strong>两个主题的指标，分别用于探讨首单用户日粒度的下、接、完留存率，以及在指定时间周期内的激活-注册、注册-完单等的用户转化情况。</li>
        <li>特别提示：<span class="text-red-500">本期上线</span>的两个主题下<span class="text-red-500">的所有指标</span>，都需要跟<strong>【留存/转化周期】</strong>维度配合使用，<span class="text-red-500">需要先指定【留存/转化周期】后，才可选择</span>。</li>
        <div class="py-3">
          <img src="assets/images/release/1.9.1.png" class="w-5/6 mx-auto" />
        </div>
      </ul>
    `);
  }


  markRead(version: string) {
    const changelog = window.localStorage.getItem('caerus.self-help.changelog');

    if (!changelog) {
      window.localStorage.setItem('caerus.self-help.changelog', version);
    } else {
      const arr = changelog.split(',');
      arr.push(version);
      window.localStorage.setItem('caerus.self-help.changelog', arr.join(','));
    }
  }
}
