<app-ribbon class="block shadow-sm" (onClose)="markRead('1.9.1')">
  @if (hasChangeLog('1.9.1')) {
    <app-ribbon-item>
      <div showFullText style="max-width: calc(100% - 100px);">
        <span class="h-10 content-center">
          尊敬的用户，Caerus自助分析新上线了<strong>【日留存】</strong>和<strong>【周期转化】</strong>两个主题的系列指标，用户使用时<span class="text-red-500 font-semibold">需要先指定【留存/转化周期】</span>，才可选择。
        </span>
      </div>
      <a class="ribbon-link" (click)="open()">查看详情</a>
    </app-ribbon-item>
  }
</app-ribbon>

<div *debug cdkDrag cdkDragBoundary="body" class="absolute z-50 left-0">
  <div class="relative min-w-52 text-xs bg-white/70 backdrop-blur-sm p-4 rounded-sm shadow-1 text-neutral-600 cursor-text">
    <CopyIcon class="absolute! right-2 top-2" iconBtn (click)="copy()" />
    <div class="absolute inset-x-0 top-0 h-5 cursor-move" cdkDragHandle></div>
    <pre class="text-xs/tight scale-75 origin-top-left">{{ formService.form.value | json }}</pre>
  </div>
</div>

<div
  class="flex flex-col gap-y-5 p-5 max-w-(--breakpoint-2xl) mx-auto"
  [class.pointer-events-none]="querying()"
>
  @if (isEdit()) {
    <app-breadcrumb>
      <app-breadcrumb-item>
        <a class="text-neutral-400" routerLink="/analysis/self-help/dashboard">我的图表</a>
      </app-breadcrumb-item>
      <app-breadcrumb-item>
        <a class="text-neutral-400" routerLink="/analysis/self-help/dashboard" [queryParams]="{ subject: data()?.subjectId }">
          {{ data()?.subjectName || "&nbsp;" }}
        </a>
      </app-breadcrumb-item>
      <app-breadcrumb-item>
        <a class="text-neutral-400" [routerLink]="['/analysis/self-help/dashboard/', data()?.dashboardId]">
          {{ data()?.dashboardName || "&nbsp;" }}
        </a>
      </app-breadcrumb-item>
      <app-breadcrumb-item>{{ data()?.chartName || "&nbsp;" }}</app-breadcrumb-item>
    </app-breadcrumb>
  }

  <div class="flex items-center justify-center">
    <div class="flex-1 min-w-0 flex items-center justify-center">
      <app-radio-group class="relative w-[432px] flex items-start gap-x-0.5 p-0.5 bg-[#ebebec] rounded-lg" [(ngModel)]="sourceType">
        <app-radio class="dida-radio-new" activeClass="active" value="trend">趋势分析</app-radio>
        <app-radio class="dida-radio-new" activeClass="active" value="constitute">构成分析</app-radio>
        <app-radio class="dida-radio-new" activeClass="active" value="comparison">对比分析</app-radio>
        <app-radio-thumb [offsetX]="2" class="bg-linear-to-r from-[#465867] to-[#1E2C3A] rounded-md" />
      </app-radio-group>
    </div>
  </div>

  <app-date-filter />

  <nz-spin [nzSpinning]="checking()">
    @if (sourceType() === 'constitute') {
      <div
        class="relative -mb-px p-2 bg-sky-50 border border-sky-200"
        [class.pointer-events-none]="querying()"
      >
        <app-dimension-filter />
      </div>
    } @if (sourceType() === 'comparison') {
      <app-comparison-filter
        [count]="formService.metrics.controls.length"
        (add)="formService.addMetrics($event)"
      />
    } @else {
      <app-common-filter />
    }

    <div class="flex items-center gap-x-2 px-7 py-4 border border-t-0 border-neutral-200">
      @if (sourceType() === 'comparison') {
        <div
          class="flex gap-2 flex-wrap"
          cdkDropList
          cdkDropListOrientation="mixed"
          (cdkDropListDropped)="drop($event)"
        >
          @for (item of formService.metrics.controls; track $index) {
            <app-comparison-filter-item cdkDrag [metric]="item.value" [index]="$index">
              <div *cdkDragPlaceholder class="min-w-40 border border-dashed border-primary rounded-xs bg-primary/20"></div>
            </app-comparison-filter-item>
          }
        </div>
      }
      <div class="flex-1 min-w-0"></div>
      @if (querying()) {
        <button [class.pointer-events-auto]="querying()" nz-button nzType="default" nzSize="small" (click)="searchSubscription.unsubscribe()" nzDanger>
          中止
        </button>
      } @else {
        <button nz-button nzType="primary" nzSize="small" (click)="query(true)">
          <PlayIcon class="text-xs mr-0.5" /> 查询
        </button>
        @if (downloadBtnVisible()) {
          <button nz-button nzType="default" nzSize="small" (click)="download()">
            <DownloadIcon class="text-xs mr-0.5" /> 下载
          </button>
        }
      }

      <button nz-button nzType="default" nzSize="small" (click)="restore()">
        @switch (sourceType()) {
          @case ('comparison') { 清空对比项 }
          @default { 重置 }
        }
      </button>
      @if (!isEdit() || hasAuth()) {
        <button nz-button nzType="primary" nzSize="small" nzGhost nz-tooltip="保存查询条件，并在指定的看板中生成图表" nzTooltipPlacement="topRight" (click)="handleSave()">
          保存
        </button>
      } @if (isShare()) {
        <button nz-button nzType="primary" nzSize="small" nzGhost nz-tooltip="保存当前查询条件，在指定的看板中生成新图表" nzTooltipPlacement="topRight" (click)="handleSaveAs()">
          另存为
        </button>
      }
    </div>
  </nz-spin>

  @if (isTimeout()) {
    <nz-alert nzBanner nzMessage='您查询的数据已超时，以"乘客数"、“车主数”、"UCVR"一类字样结尾的指标需要较长的查询时间，建议缩短查询周期或者减少同时查询这些指标的个数' nzCloseable></nz-alert>
  } @if (option()) {
    <div class="flex items-center gap-1 text-neutral-400 text-xs">
      <div class="flex items-center gap-x-2 mr-3">
        <span class="font-bold text-neutral-600">数据提示排序规则:</span>
        <label class="flex items-center gap-x-1 cursor-pointer">
          <input type="radio" name="orderType" [(ngModel)]="orderType" [value]="null" /> 默认
        </label>
        <label class="flex items-center gap-x-1 cursor-pointer">
          <input type="radio" name="orderType" [(ngModel)]="orderType" value="base" /> 按当前期数值排序
        </label>
      </div>
  
      @if (orderType() !== null) {
        <div>
          <div class="flex items-center gap-x-2 mr-3">
            <span class="font-bold text-neutral-600">排序方式:</span>
            <label class="flex items-center gap-x-1 cursor-pointer">
              <input type="radio" name="orderBy" [(ngModel)]="orderBy" value="desc" /> 倒序
            </label>
            <label class="flex items-center gap-x-1 cursor-pointer">
              <input type="radio" name="orderBy" [(ngModel)]="orderBy" value="asc" /> 正序
            </label>
          </div>
        </div>
      }
  
      <div class="mx-auto"></div>

      @if (sourceType() === 'constitute') {
        @if (showTypeVisible()) {
          <div class="flex items-center gap-x-2 mr-3">
            <span class="font-bold text-neutral-600">数据选择:</span>
            <label class="flex items-center gap-x-1 cursor-pointer">
              <input type="radio" name="showType" [(ngModel)]="showType" value="number" />只看数值
            </label>
            <label class="flex items-center gap-x-1 cursor-pointer">
              <input type="radio" name="showType" [(ngModel)]="showType" value="ratio" />只看占比
            </label>
            <label class="flex items-center gap-x-1 cursor-pointer">
              <input type="radio" name="showType" [(ngModel)]="showType" [value]="null" />全部展示
            </label>
          </div>
        }
  
        <div class="flex items-center gap-x-2 mr-3">
          @let columnDisabled = showType() === 'ratio';
          <span class="font-bold text-neutral-600">图表样式:</span>
          <label class="flex items-center gap-x-1 cursor-pointer">
            <input type="radio" name="chartType" [(ngModel)]="chartType" value="line" />折线图
          </label>
          <label class="flex items-center gap-x-1 cursor-pointer" [nz-tooltip]="columnDisabled ? '占比仅支持折线图样式' : null" nzTooltipPlacement="topRight">
            <input type="radio" name="chartType" [(ngModel)]="chartType" value="column" [disabled]="columnDisabled" />柱状图
          </label>
        </div>
      }

      <!-- || sourceType() === 'comparison' -->
      @if (sourceType() === 'trend') {
        <app-axes-control />
      }
    </div>

    <div class="flex items-center gap-1 text-neutral-400 text-xs -mt-4">
      <div>说明:</div>
      @if (this.formService.hasDateCompare()) {
        <div>对比期存在时，实线/纯色柱是当前期数值，虚线/阴影柱是对比期数值;</div>
      }
      <div>点击图例默认单选，<span class="text-primary">长按shift键</span>点击可多选。 </div>
    </div>
  }

  <div class="relative flex items-center justify-center h-140">
    <div
      class="absolute inset-0 flex items-center justify-center bg-white/70 transition-opacity opacity-0 -z-10"
      [ngClass]="{ 'opacity-100 pointer-events-auto z-50': querying() }"
    >
      <app-line-spin />
    </div>

    @if (querying()) {} @else if (noData()) {
      <div class="flex flex-col items-center gap-y-3">
        <img src="assets/images/svg/empty.svg" />
        <span class="text-slate-500">无数据</span>
      </div>
    } @else if (option()) {
      <div class="absolute inset-y-0 inset-x-4">
        <!-- showEveryAnnotations -->
        <app-graph [options]="option()" [sourceType]="sourceType()" showAvgPlotLines showAnnotations yAxisFormatEnabled />
      </div>
    } @else if (errorMessage()) {
      <span class="text-slate-600 text-xs">{{ errorMessage() }}</span>
    } @else {
      <span class="text-slate-600 text-xs">请选择指标进行查询</span>
    }
  </div>

  @if (option()) {
    <app-numeric-table [data]="chartData()" [sourceType]="sourceType()" />
  }
</div>

<div class="h-72"></div>
