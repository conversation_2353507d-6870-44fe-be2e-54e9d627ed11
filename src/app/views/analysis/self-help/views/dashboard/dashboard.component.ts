import { FormsModule } from '@angular/forms';
import { DatePipe, NgTemplateOutlet } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, Component, DestroyRef, inject, model, signal } from '@angular/core';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { combineLatest, debounceTime, lastValueFrom, map } from 'rxjs';
import { ActivatedRoute, Router } from '@angular/router';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { NzPaginationModule } from 'ng-zorro-antd/pagination';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzModalService } from 'ng-zorro-antd/modal';
import { NzSpinModule } from 'ng-zorro-antd/spin';

import { CaerusApiService } from '@api/caerus';
import { AnalysisDashboardOutput, AnalysisSubject } from '@api/caerus/model';
import {
  IconDeleteComponent,
  IconEditComponent,
  IconFilePlusComponent,
  IconFolderComponent,
  IconMoreComponent,
  IconReportComponent,
  IconSearchComponent,
  IconTagComponent,
  IconFileEditComponent,
  IconSquareDottedPlusComponent,
  IconUserGroupComponent,
  IconFluctuateComponent,
  IconFileTimeComponent,
  IconFolderSharedComponent,
  IconChart3DComponent,
  IconChartBarComponent,
  IconChartLineComponent,
  IconCityComponent,
  IconCouponComponent,
  IconFengKongComponent,
  IconGraphBarComponent,
  IconGraphPieComponent,
  IconKefuComponent,
  IconMapComponent,
  IconOrderComponent,
  IconReportAnalyseComponent,
  IconReportDataComponent,
  IconTaxiComponent,
  IconTodoListComponent,
  IconSafeComponent,
  IconLabComponent,
} from '@shared/modules/icons';
import { RadioModule } from '@shared/modules/headless';
import { CardModule } from '@shared/modules/card';
import { DashboardInfoComponent, DashboardSubjectComponent, SubjectInfoComponent } from './components/modal';
import { PAGE_NAME, PageEnterLeaveDirective } from '@common/directive';
import { Before, Confirmed } from '@common/decorator';
import { EventInterceptor } from '@common/function';
import { isDev } from '@common/const';


@Component({
  selector: 'app-dashboard',
  template: `
    <header class="sticky top-0 z-10 flex flex-col flex-wrap px-5 py-5 bg-white shadow-md gap-y-4">
      <div class="flex gap-x-2 w-full mr-auto">
        <app-radio-group class="flex gap-x-2 max-w-full h-15" [(ngModel)]="subject">
          <app-radio class="subject-radio" activeClass="active" [value]="undefined">
            <FolderIcon class="radio-icon" />
            <span>全部</span>
          </app-radio>

          @for (item of subjectListResource.value()?.data; track item) {
            <app-radio class="group subject-radio" activeClass="active" value="{{ item.id }}">
              <ng-container *ngTemplateOutlet="iconTemplate; context: { $implicit: item.subjectLogo }"></ng-container>

              <span>{{ item.subjectName }}</span>

              <MoreIcon class="sm more-btn" iconBtn nz-dropdown nzPlacement="bottomRight" [nzDropdownMenu]="menu" />
              <nz-dropdown-menu #menu="nzDropdownMenu">
                <ul nz-menu nzSelectable class="w-26">
                  <li nz-menu-item nzDanger (click)="handleDeleteSubject(item.id)">删除</li>
                  <li nz-menu-item (click)="handleEditSubject(item)">编辑主题</li>
                </ul>
              </nz-dropdown-menu>
            </app-radio>
          }

          <ng-template #iconTemplate let-type>
            @switch (type) {  
              @case ('1') { <ReportIcon class="radio-icon text-slate-300" /> }
              @case ('2') { <UserGroupIcon class="radio-icon" [style]="2" /> }
              @case ('3') { <FluctuateIcon class="radio-icon" /> }
              @case ('4') { <FileTimeIcon class="radio-icon" /> }
              @case ('5') { <TagIcon class="radio-icon" /> }
              @case ('folder-shared') { <FolderSharedIcon class="radio-icon" /> }
              @case ('taxi') { <TaxiIcon class="radio-icon" [style]="2" /> }
              @case ('city-1') { <CityIcon class="radio-icon" [style]="1" /> }
              @case ('city-2') { <CityIcon class="radio-icon" [style]="2" /> }
              @case ('map-1') { <MapIcon class="radio-icon" [style]="1" /> }
              @case ('map-2') { <MapIcon class="radio-icon" [style]="2" /> }
              @case ('map-3') { <MapIcon class="radio-icon" [style]="3" /> }
              @case ('order') { <OrderIcon class="radio-icon" [style]="2" /> }
              @case ('fengkong-1') { <FengKongIcon class="radio-icon" [style]="1" /> }
              @case ('fengkong-2') { <FengKongIcon class="radio-icon" [style]="2" /> }
              @case ('safe') { <SafeIcon class="radio-icon" /> }
              @case ('kefu') { <KefuIcon class="radio-icon" /> }
              @case ('graph-pie') { <GraphPieIcon class="radio-icon" /> }
              @case ('graph-bar') { <GraphBarIcon class="radio-icon" /> }
              @case ('chart-3d') { <Chart3DIcon class="radio-icon" /> }
              @case ('chart-bar') { <ChartBarIcon class="radio-icon" /> }
              @case ('chart-line') { <ChartLineIcon class="radio-icon" /> }
              @case ('lab') { <LabIcon class="radio-icon" /> }
              @case ('report-data') { <ReportDataIcon class="radio-icon" /> }
              @case ('report-analyse') { <ReportAnalyseIcon class="radio-icon" /> }
              @case ('todo-list') { <TodoListIcon class="radio-icon" /> }
              @case ('coupon') { <CouponIcon class="radio-icon" /> }
            }
          </ng-template>
        </app-radio-group>

        <div class="subject-radio hover:bg-blue-50 hover:shadow-sm" (click)="handleCreateSubject()">
          <SquareDottedPlusIcon class="radio-icon" />
          <span>新建主题</span>
        </div>
      </div>
      <div class="flex items-center gap-x-2">
        <div class="space-x-2 mr-auto">
          <nz-input-group nzSearch [nzPrefix]="suffixIconSearch" class="w-80!">
            <input type="text" [(ngModel)]="keyword" nz-input placeholder="搜索看板名称或创建人" />
          </nz-input-group>
          <!-- <label nz-checkbox class="mr-auto!" [(ngModel)]="onlyMyCreate">只看我创建的</label> -->
        </div>
        <button nz-button nzType="primary" (click)="handleCreateDashboard()">
          <FilePlusIcon class="mr-0.5" />
          新建看板
        </button>
        <!-- <button nz-button nzType="default"> <TagIcon class="mr-0.5" /> 批量更换主题</button> -->
      </div>
      <ng-template #suffixIconSearch>
        <SearchIcon />
      </ng-template>
    </header>

    <nz-spin [nzSpinning]="dashboardListResource.isLoading()" style="min-height: 500px;">
      <div class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4 gap-5 p-5">
        @for (item of dashboardListResource.value()?.data?.records; track item.id) {
          <app-card class="group bg-white py-2 cursor-pointer" (click)="gotoDashboard(item.id)">
            <app-card-header>
              <app-card-title class="text-center">{{ item.dashboardName }}</app-card-title>
              <app-card-description class="absolute right-3 -translate-y-0.5">
                @if (!hasAuth(item.createByCn)) {
                  <FileEditIcon iconBtn nz-tooltip="编辑主题" class="opacity-0 group-hover:opacity-100 transition-opacity" (click)="handleEditShareDashboard($event, item)" />
                }
                @if (hasAuth(item.createByCn)) {
                  <EditIcon iconBtn nz-tooltip="编辑看板" class="opacity-0 group-hover:opacity-100 transition-opacity" (click)="handleEditOwnDashboard($event, item)" />
                  <DeleteIcon iconBtn nz-tooltip="删除看板" class="opacity-0 group-hover:opacity-100 transition-opacity" (click)="handleDeleteDashboard($event, item.id)" />
                }
              </app-card-description>
            </app-card-header>
            <app-card-content>
              <div class="w-full">
                <table class="mx-auto w-full">
                  <tr>
                    <td [width]="80">看板说明：</td>
                    <td class="relative max-w-[calc(100%_-_80px)]">
                      <span class="absolute inset-0 inline-block truncate" nz-tooltip="{{ item.dashboardInfo }}">
                        {{ item.dashboardInfo }}
                      </span>
                    </td>
                  </tr>
                  <tr>
                    <td>所属主题：</td>
                    <td>{{ item.subjectName }}</td>
                  </tr>
                  <tr>
                    <td>创建人：</td>
                    <td>{{ item.createByCn }}</td>
                  </tr>
                  <tr>
                    <td>创建时间：</td>
                    <td>{{ item.createTime | date: 'yyyy-MM-dd' }}</td>
                  </tr>
                </table>
              </div>
            </app-card-content>
          </app-card>
        }
      </div>
      <div class="flex justify-end px-4">
        <nz-pagination
          [(nzPageIndex)]="page"
          [nzTotal]="dashboardListResource.value()?.data?.total"
          [nzHideOnSinglePage]="true"
          [nzPageSize]="pageSize()"
        />
      </div>
    </nz-spin>
  `,
  styleUrl: './dashboard.component.css',
  hostDirectives: [PageEnterLeaveDirective],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    DatePipe,
    NgTemplateOutlet,
    FormsModule,
    NzSpinModule,
    NzInputModule,
    NzCheckboxModule,
    NzDropDownModule,
    NzButtonModule,
    NzToolTipModule,
    CardModule,
    RadioModule,
    IconDeleteComponent,
    IconEditComponent,
    IconSearchComponent,
    IconFilePlusComponent,
    IconReportComponent,
    IconFileEditComponent,
    IconMoreComponent,

    IconFolderComponent,
    IconUserGroupComponent,
    IconFluctuateComponent,
    IconFileTimeComponent,
    IconTagComponent,
    IconSquareDottedPlusComponent,

    IconFolderSharedComponent,
    IconTaxiComponent,
    IconCityComponent,
    IconMapComponent,
    IconOrderComponent,
    IconFengKongComponent,
    IconSafeComponent,
    IconKefuComponent,
    IconGraphPieComponent,
    IconGraphBarComponent,
    IconChart3DComponent,
    IconChartBarComponent,
    IconChartLineComponent,
    IconLabComponent,
    IconReportDataComponent,
    IconReportAnalyseComponent,
    IconTodoListComponent,
    IconCouponComponent,
    NzPaginationModule,
  ],
  providers: [{ provide: PAGE_NAME, useValue: window.location.pathname }],
})
export class DashboardComponent implements AfterViewInit {
  
  readonly router = inject(Router);
  readonly route = inject(ActivatedRoute);
  readonly modal = inject(NzModalService);
  readonly message = inject(NzMessageService);
  readonly apiService = inject(CaerusApiService);
  readonly destroyRef = inject(DestroyRef);

  subject = model<string>(null);
  subject$ = toObservable(this.subject);
  keyword = signal<string>(null);
  keyword$ = toObservable(this.keyword);

  page = signal(1);
  pageSize = signal(20);
  subjectListResource = this.apiService.fetchAnalysisSubjectList2();
  dashboardListResource = this.apiService.fetchAnalysisDashboardListPage2(
    this.keyword,
    this.subject,
    this.page,
    this.pageSize
  );

  ngAfterViewInit(): void {
    this._subscribeToParamsChange();
  }

  hasAuth(username: string) {
    return (
      isDev() || 
      username === window.localStorage.getItem('caerus.auth.username.py') || 
      username === window.localStorage.getItem('caerus.auth.username')
    );
  }

  private _subscribeToParamsChange() {
    combineLatest([this.keyword$, this.subject$])
      .pipe(debounceTime(500), takeUntilDestroyed(this.destroyRef))
      .subscribe(() => {
        this.page.set(1);
      });
  }

  gotoDashboard(id: number) {
    const queryParams = {} as any;

    if (this.subject()) {
      queryParams.subject = this.subject();
    }

    this.router.navigate(['../dashboard/', id], { relativeTo: this.route, queryParams });
  }

  handleCreateDashboard() {
    this.modal.create({
      nzTitle: '新建看板',
      nzContent: DashboardInfoComponent,
      nzWidth: 600,
      nzData: {
        subjectId: this.subject() ? +this.subject() : null,
      },
      nzOnOk: async ({ valid, value }) => {
        if (valid) {
          const request$ = this.apiService.createAnalysisDashboard(value).pipe(
            map(res => {
              if (res.status !== '00000') {
                this.message.error(res.error || res.message);
              }
              if (res.status === '00000') {
                this.dashboardListResource.reload();
                return true;
              }
              return false;
            })
          );

          return lastValueFrom(request$);
        }

        return false;
      },
    });
  }

  @Before(EventInterceptor)
  handleEditShareDashboard(event: MouseEvent, data: AnalysisDashboardOutput) {
    this.modal.create({
      nzTitle: '编辑看板',
      nzContent: DashboardSubjectComponent,
      nzWidth: 600,
      nzData: data,
      nzOnOk: async ({ valid, value }) => {
        if (valid) {
          const request$ = this.apiService
            .moveAnalysisDashboard({
              subjectId: value.subjectId,
              dashboardIdList: [data.id],
            })
            .pipe(
              map(res => {
                if (res.status !== '00000') {
                  this.message.error(res.error || res.message);
                }
                if (res.status === '00000') {
                  this.dashboardListResource.reload();
                  return true;
                }
                return false;
              })
            );

          return lastValueFrom(request$);
        }

        return false;
      },
    });
  }

  @Before(EventInterceptor)
  handleEditOwnDashboard(event: MouseEvent, data: AnalysisDashboardOutput) {
    this.modal.create({
      nzTitle: '编辑看板',
      nzContent: DashboardInfoComponent,
      nzWidth: 600,
      nzData: data,
      nzOnOk: async ({ valid, value }) => {
        if (valid) {
          const request$ = this.apiService.updateAnalysisDashboard(value).pipe(
            map(res => {
              if (res.status !== '00000') {
                this.message.error(res.error || res.message);
              }
              if (res.status === '00000') {
                this.dashboardListResource.reload();
                return true;
              }
              return false;
            })
          );

          return lastValueFrom(request$);
        }

        return false;
      },
    });
  }

  handleCreateSubject() {
    this.modal.create({
      nzTitle: '新建主题',
      nzContent: SubjectInfoComponent,
      nzWidth: 650,
      nzOnOk: async ({ valid, value }) => {
        if (valid) {
          const request$ = this.apiService.createAnalysisSubject(value).pipe(
            map(res => {
              if (res.status !== '00000') {
                this.message.error(res.error || res.message);
              }
              if (res.status === '00000') {
                this.subjectListResource.reload();
                return true;
              }
              return false;
            })
          );

          return lastValueFrom(request$);
        }

        return false;
      },
    });
  }

  handleEditSubject(data: AnalysisSubject) {
    this.modal.create({
      nzTitle: '编辑主题',
      nzContent: SubjectInfoComponent,
      nzWidth: 650,
      nzData: data,
      nzOnOk: async ({ valid, value }) => {
        if (valid) {
          const request$ = this.apiService.updateAnalysisSubject(value).pipe(
            map(res => {
              if (res.status !== '00000') {
                this.message.error(res.error || res.message);
              }
              if (res.status === '00000') {
                this.subjectListResource.reload();
                return true;
              }
              return false;
            })
          );

          return lastValueFrom(request$);
        }

        return false;
      },
    });
  }

  @Confirmed('确定删除吗?')
  handleDeleteSubject(id: number) {
    this.apiService.removeAnalysisSubject({ id }).subscribe(res => {
      if (res.status !== '00000') {
        this.message.error(res.error || res.message);
      }

      if (res.status === '00000') {
        this.message.success('删除成功');
        this.subjectListResource.reload();
        this.subject.set(undefined);
      }
    });
  }

  @Before(EventInterceptor)
  @Confirmed('确定删除吗?')
  handleDeleteDashboard(event: MouseEvent, id: number) {
    this.apiService.removeAnalysisDashboard({ id }).subscribe(res => {
      if (res.status !== '00000') {
        this.message.error(res.error || res.message);
      }

      if (res.status === '00000') {
        this.message.success('删除成功');
        this.dashboardListResource.reload();
      }
    });
  }
}
