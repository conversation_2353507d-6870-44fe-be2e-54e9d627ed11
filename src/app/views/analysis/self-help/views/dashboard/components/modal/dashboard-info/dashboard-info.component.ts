import { <PERSON><PERSON><PERSON><PERSON><PERSON>, JsonPipe } from '@angular/common';
import { FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { AfterViewInit, ChangeDetectionStrategy, Component, inject } from '@angular/core';
import {NZ_MODAL_DATA } from 'ng-zorro-antd/modal';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzRadioModule } from 'ng-zorro-antd/radio';
import { map } from 'rxjs';

import { CaerusApiService } from '@api/caerus';
import { AnalysisDashboardInput, AnalysisDashboardOutput } from '@api/caerus/model';
import { DebugDirective } from '@shared/directives';
import { checkForm } from '@common/function';
import { isDev } from '@common/const';


class DashboardInfoForm {

  /** 主键 */
  id = new FormControl<number>(null);

  /** 看板名称 */
  dashboardName = new FormControl<string>(null, { validators: [ Validators.required ] });
  
  /** 看板主题 */
  subjectId = new FormControl<number>(null, { validators: [ Validators.required ] });

  /** 看板说明 */
  dashboardInfo = new FormControl<string>(null);

  /** 
   * 权限类型 
   * - 1 公共
   * - 2 私有
   */
  shareType = new FormControl<1 | 2>(2, { nonNullable: true });

}


@Component({
  selector: 'app-dashboard-info',
  templateUrl: './dashboard-info.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    JsonPipe,
    AsyncPipe,
    ReactiveFormsModule,
    NzFormModule,
    NzInputModule,
    NzSelectModule,
    NzRadioModule,
    DebugDirective,
  ],
})
export class DashboardInfoComponent implements AfterViewInit {

  readonly apiService = inject(CaerusApiService);
  readonly nzModalData: AnalysisDashboardOutput = inject(NZ_MODAL_DATA);

  form = new FormGroup(new DashboardInfoForm());
  subjectList$ = this.apiService.fetchAnalysisSubjectList().pipe(
    map(res => res.data)
  );

  ngAfterViewInit(): void {
    if (this.nzModalData) {
      this.form.reset(this.nzModalData);

      if (this.nzModalData.createBy) {
        if (!this.hasAuth(this.nzModalData.createBy)) {
          this.form.controls.subjectId.reset();
          this.form.controls.dashboardInfo.disable();
          this.form.controls.dashboardName.disable();
          this.form.controls.shareType.disable();
        }
      }
    } else {
      this.form.controls.id.disable();    
    }
  }


  hasAuth(username: string) {
    return (
      isDev() ||
      username === window.localStorage.getItem('caerus.auth.username.py') ||
      username === window.localStorage.getItem('caerus.auth.username')
    );
  }


  get valid(): boolean {
    if (this.form.invalid) {
      checkForm(this.form.controls);
    }
    
    return this.form.valid;
  }

  
  get value() {
    if (this.nzModalData) {
      return this.form.getRawValue();
    }
    return this.form.value as Required<AnalysisDashboardInput>;
  }
  
}
