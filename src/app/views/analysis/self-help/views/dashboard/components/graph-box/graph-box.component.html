@if (dragEnabled()) {
  <div class="absolute inset-0 z-10 pointer-events-auto"></div>
}
<app-card class="flex flex-col h-full" appIntersection (visible)="query()">
  <app-card-header>
    <app-card-title class="h-6">{{data()?.chartName}}</app-card-title>
    <app-card-extra>
      @if (deleteMode()) {
        <label nz-checkbox [ngModel]="selected()" (ngModelChange)="toggleSelect()"></label>
      }
      @else {
        @let username = data()?.createBy;
        @let visible = hasAuth(username);
        @if (visible) {
          <EditIcon iconBtn nz-tooltip="编辑图表信息" (click)="handleChartInfoEdit()" />
        }
        <EditContainerIcon iconBtn strokeWidth="1.7" nz-tooltip="编辑查询条件" [routerLink]="['./query', data()?.id]" />
        <FullscreenIcon iconBtn (click)="enterFullScreen()" nz-tooltip="全屏" />
        <DownloadIcon iconBtn nz-tooltip="下载" (click)="download()" />
        @if (visible) {
          <DeleteIcon iconBtn (click)="handleDelete()" nz-tooltip="删除" />
        }
      }
    </app-card-extra>
  </app-card-header>
  <app-card-content class="flex-1 min-h-0 text-center content-center">
    <div class="truncate text-xs text-left" nz-popover [nzPopoverContent]="contentTemplate" nzPopoverPlacement="bottomLeft">{{filterLabelStr()}}</div>
    <ng-template #contentTemplate>
      <div [innerHTML]="filterLabel()"></div>
    </ng-template>
    <div class="relative flex items-center justify-center h-full">
      @if (querying()) {
        <div class="absolute inset-0 flex items-center justify-center transition-opacity opacity-0 -z-10" [ngClass]="{ 'opacity-100 pointer-events-auto z-50': querying() }">
          <app-line-spin />
        </div>
      }
      @else if (noData()) {
        <div class="flex flex-col items-center gap-y-3">
          <img src="assets/images/svg/empty.svg">
          <span class="text-slate-500">无数据</span>
        </div>
      }
      @else if (option()) {
        <div class="absolute inset-0">
          <app-graph [options]="option()" [sourceType]="analysisTypeMap.get(this.data().analysisType)" showAvgPlotLines showAnnotations yAxisFormatEnabled />
        </div>
      }
      @else if (errorMessage()) {
        <span class="text-slate-600 text-xs">{{errorMessage()}}</span>
      }
    </div>
    
  </app-card-content>
</app-card>


<ng-template #fullScreenContainerTpl>
  <div class="flex flex-col bg-white w-screen h-screen">
    <header class="flex items-center justify-between h-16 px-7 shadow-md">
      <div class="flex flex-col flex-1 min-w-0">
        <span class="font-bold text-base">{{data()?.chartName}}</span>
      </div>
      <a nz-button nzType="text" (click)="exitFullScreen()">
        <FullscreenExitIcon class="text-[#A3ABB0] mr-1" />
        退出全屏
      </a>
    </header>
    <main class="flex-1 p-7">
      <div class="relative flex w-full h-full">
        <ng-template [cdkPortalOutlet]="domPortal()"></ng-template>
      </div>
    </main>
  </div>
</ng-template>