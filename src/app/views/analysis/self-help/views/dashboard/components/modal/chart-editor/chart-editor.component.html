<form nz-form nzLayout="vertical" [nzAutoTips]="autoTips" [formGroup]="form">
  <div class="flex flex-col">
    <nz-form-item>
      <nz-form-label>所属看板</nz-form-label>
      <nz-form-control>
        <nz-radio-group formControlName="saveType">
          <label nz-radio [nzValue]="1">已有看板</label>
          <label nz-radio [nzValue]="0">新增看板</label>
        </nz-radio-group>
      </nz-form-control>
    </nz-form-item>

    @if (form.controls.saveType.value === 1) {
    <nz-form-item>
      <nz-form-label nzRequired>看板名称</nz-form-label>
      <nz-form-control>
        <!-- <nz-cascader [nzOptions]="tree$ | async" [nzShowSearch]="true" formControlName="dashboardPath" /> -->
        <nz-tree-select
          nzPlaceHolder="请选择看板名称"
          [nzNodes]="tree()"
          nzShowSearch
          formControlName="dashboardPath"
          [nzExpandedKeys]="expandKey()"
          [nzHideUnMatched]="true"
        ></nz-tree-select>
      </nz-form-control>
    </nz-form-item>

    <nz-form-item>
      <nz-form-label nzRequired>图表名称</nz-form-label>
      <nz-form-control>
        <input
          nz-input
          formControlName="chartName"
          placeholder="请输入"
          spellcheck="false"
        />
      </nz-form-control>
    </nz-form-item>
    } @else {
    <nz-form-item>
      <nz-form-label nzRequired>看板名称</nz-form-label>
      <nz-form-control>
        <input
          nz-input
          formControlName="dashboardName"
          placeholder="请输入"
          spellcheck="false"
        />
      </nz-form-control>
    </nz-form-item>

    <nz-form-item>
      <nz-form-label>看板主题</nz-form-label>
      <nz-form-control>
        <nz-select formControlName="subjectId" nzPlaceHolder="请选择">
          @for (item of subjectList(); track item) {
          <nz-option
            [nzLabel]="item.subjectName"
            [nzValue]="item.id"
          ></nz-option>
          }
        </nz-select>
      </nz-form-control>
    </nz-form-item>

    <nz-form-item>
      <nz-form-label nzRequired>图表名称</nz-form-label>
      <nz-form-control>
        <input
          nz-input
          formControlName="chartName"
          placeholder="请输入"
          spellcheck="false"
        />
      </nz-form-control>
    </nz-form-item>
    }

    <nz-form-item>
      <nz-form-label>查询条件</nz-form-label>
      <nz-form-control>
        <div class="text-xs leading-normal text-neutral-500">
          <span [innerHTML]="filterLabel()"></span>
        </div>
      </nz-form-control>
    </nz-form-item>

    <pre *debug class="text-xs mt-2">{{ form?.value | json }}</pre>
  </div>
</form>
