<form nz-form nzLayout="vertical" [formGroup]="form">
  <div class="flex flex-col">
    <div class="flex items-center gap-4">
      <nz-form-item class="flex-1">
        <nz-form-label nzRequired>看板名称</nz-form-label>
        <nz-form-control>
          <input nz-input formControlName="dashboardName" placeholder="请输入" />
        </nz-form-control>
      </nz-form-item>

      <nz-form-item class="flex-1">
        <nz-form-label nzRequired>看板主题</nz-form-label>
        <nz-form-control>
          <nz-select formControlName="subjectId" nzPlaceHolder="请选择">
            @for (item of (subjectList$ | async); track item) {
              <nz-option [nzLabel]="item.subjectName" [nzValue]="item.id"></nz-option>
            }
          </nz-select>
        </nz-form-control>
      </nz-form-item>
    </div>

    <div>
      <nz-form-item>
        <nz-form-label>看板说明</nz-form-label>
        <nz-form-control>
          <textarea nz-input formControlName="dashboardInfo" placeholder="请输入" spellcheck="false" [nzAutosize]="{ minRows: 4, maxRows: 5 }"></textarea>
        </nz-form-control>
      </nz-form-item>
    </div>

    <!-- <div>
      <nz-form-item>
        <nz-form-label>权限管理</nz-form-label>
        <nz-form-control>
          <nz-radio-group formControlName="shareType">
            <label nz-radio [nzValue]="2">仅我可见</label>
            <label nz-radio [nzValue]="1">所有人可见（仅查看）</label>
          </nz-radio-group>
        </nz-form-control>
      </nz-form-item>
    </div> -->
  </div>

  <pre *debug class="text-xs">{{form?.value | json}}</pre>
</form>