import { <PERSON>sonPip<PERSON> } from '@angular/common';
import { FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { AfterViewInit, ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { NZ_MODAL_DATA } from 'ng-zorro-antd/modal';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { RadioModule } from '@shared/modules/headless';
import { DebugDirective } from '@shared/directives';
import { IconReportComponent, IconTagComponent, IconTaxiComponent, IconMapComponent, IconCityComponent, IconOrderComponent, IconFengKongComponent, IconSafeComponent, IconUserGroupComponent, IconKefuComponent, IconGraphPieComponent, IconChart3DComponent, IconGraphBarComponent, IconFolderSharedComponent, IconCouponComponent, IconReportDataComponent, IconTodoListComponent, IconChartBarComponent, IconChartLineComponent, IconReportAnalyseComponent, IconFileTimeComponent, IconFluctuateComponent } from '@shared/modules/icons';
import { checkForm } from '@common/function';
import { AnalysisSubject, AnalysisSubjectInput } from '@api/caerus/model';
import { IconLabComponent } from '@shared/modules/icons/icon-lab.component';


class DashboardSubjectForm {

  /** 主键 */
  id = new FormControl<number>(null);

  /** 主题名称 */
  subjectName = new FormControl<string>(null, { validators: [ Validators.required ] });

  /** 主题图标 */
  subjectLogo = new FormControl<string>('1', { nonNullable: true });
  
}


@Component({
  selector: 'app-subject-info',
  template: `
    <form nz-form nzLayout="vertical" [nzAutoTips]="autoTips" [formGroup]="form">
      <div class="flex flex-col">
        <div>
          <nz-form-item>
            <nz-form-label nzRequired>主题名称</nz-form-label>
            <nz-form-control>
              <input nz-input formControlName="subjectName" placeholder="请输入" />
            </nz-form-control>
          </nz-form-item>
        </div>

        <div>
          <nz-form-item>
            <nz-form-label>主题图标</nz-form-label>
            <nz-form-control>
              <app-radio-group class="flex flex-wrap gap-3" formControlName="subjectLogo">
                <app-radio class="subject-radio" activeClass="active" value="1">
                  <ReportIcon class="radio-icon" />
                </app-radio>
                <app-radio class="subject-radio" activeClass="active" value="2">
                  <UserGroupIcon class="radio-icon" [style]="2" />
                </app-radio>
                <app-radio class="subject-radio" activeClass="active" value="3">
                  <FluctuateIcon class="radio-icon" />
                </app-radio>
                <app-radio class="subject-radio" activeClass="active" value="4">
                  <FileTimeIcon class="radio-icon" />
                </app-radio>
                <app-radio class="subject-radio" activeClass="active" value="5">
                  <TagIcon class="radio-icon" />
                </app-radio>

                <app-radio class="subject-radio" activeClass="active" value="folder-shared">
                  <FolderSharedIcon class="radio-icon" />
                </app-radio>

                <app-radio class="subject-radio" activeClass="active" value="taxi">
                  <TaxiIcon class="radio-icon" [style]="2" />
                </app-radio>

                <app-radio class="subject-radio" activeClass="active" value="city-1">
                  <CityIcon class="radio-icon" [style]="1" />
                </app-radio>
                <app-radio class="subject-radio" activeClass="active" value="city-2">
                  <CityIcon class="radio-icon" [style]="2" />
                </app-radio>

                <app-radio class="subject-radio" activeClass="active" value="map-1">
                  <MapIcon class="radio-icon" [style]="1" />
                </app-radio>
                <app-radio class="subject-radio" activeClass="active" value="map-2">
                  <MapIcon class="radio-icon" [style]="2" />
                </app-radio>
                <app-radio class="subject-radio" activeClass="active" value="map-3">
                  <MapIcon class="radio-icon" [style]="3" />
                </app-radio>
                <app-radio class="subject-radio" activeClass="active" value="order">
                  <OrderIcon class="radio-icon" [style]="2" />
                </app-radio>

                <app-radio class="subject-radio" activeClass="active" value="fengkong-1">
                  <FengKongIcon class="radio-icon" [style]="1" />
                </app-radio>
                <app-radio class="subject-radio" activeClass="active" value="fengkong-2">
                  <FengKongIcon class="radio-icon" [style]="2" />
                </app-radio>

                <app-radio class="subject-radio" activeClass="active" value="safe">
                  <SafeIcon class="radio-icon" />
                </app-radio>

                <app-radio class="subject-radio" activeClass="active" value="kefu">
                  <KefuIcon class="radio-icon" />
                </app-radio>
                
                <app-radio class="subject-radio" activeClass="active" value="graph-pie">
                  <GraphPieIcon class="radio-icon" />
                </app-radio>
                <app-radio class="subject-radio" activeClass="active" value="graph-bar">
                  <GraphBarIcon class="radio-icon" />
                </app-radio>
                <app-radio class="subject-radio" activeClass="active" value="chart-3d">
                  <Chart3DIcon class="radio-icon" />
                </app-radio>
                <app-radio class="subject-radio" activeClass="active" value="chart-bar">
                  <ChartBarIcon class="radio-icon" />
                </app-radio>
                <app-radio class="subject-radio" activeClass="active" value="chart-line">
                  <ChartLineIcon class="radio-icon" />
                </app-radio>

                <app-radio class="subject-radio" activeClass="active" value="lab">
                  <LabIcon class="radio-icon" />
                </app-radio>

                <app-radio class="subject-radio" activeClass="active" value="report-data">
                  <ReportDataIcon class="radio-icon" />
                </app-radio>

                <app-radio class="subject-radio" activeClass="active" value="report-analyse">
                  <ReportAnalyseIcon class="radio-icon" />
                </app-radio>

                <app-radio class="subject-radio" activeClass="active" value="todo-list">
                  <TodoListIcon class="radio-icon" />
                </app-radio>

                <app-radio class="subject-radio" activeClass="active" value="coupon">
                  <CouponIcon class="radio-icon" />
                </app-radio>

              </app-radio-group>
            </nz-form-control>
          </nz-form-item>
        </div>

      </div>
      <pre *debug class="text-xs">{{form?.value | json}}</pre>
    </form>
  `,
  styleUrl: './subject-info.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    JsonPipe,
    ReactiveFormsModule,
    NzFormModule,
    NzInputModule,
    RadioModule,
    IconReportComponent,
    IconUserGroupComponent,
    IconTaxiComponent,
    IconTagComponent,
    IconMapComponent,
    IconCityComponent,
    IconOrderComponent,
    IconFengKongComponent,
    IconKefuComponent,
    IconSafeComponent,
    IconGraphPieComponent,
    IconGraphBarComponent,
    IconChart3DComponent,
    IconChartBarComponent,
    IconChartLineComponent,
    IconLabComponent,
    IconFolderSharedComponent,
    IconCouponComponent,
    IconReportDataComponent,
    IconReportAnalyseComponent,
    IconTodoListComponent,
    IconFluctuateComponent,
    IconFileTimeComponent,
    DebugDirective,
  ],
})
export class SubjectInfoComponent implements AfterViewInit {

  readonly nzModalData: AnalysisSubject = inject(NZ_MODAL_DATA);

  form = new FormGroup(new DashboardSubjectForm());
  autoTips: Record<string, Record<string, string>> = {
    default: {
      required: '必填项'
    }
  };

  ngAfterViewInit(): void {
    if (this.nzModalData) {
      this.form.reset(this.nzModalData);
    } else {
      this.form.controls.id.disable();
    }
  }

  get valid(): boolean {
    if (this.form.invalid) {
      checkForm(this.form.controls);
    }
    
    return this.form.valid;
  }

  
  get value() {
    return this.form.value as Required<AnalysisSubjectInput>;
  }

}
