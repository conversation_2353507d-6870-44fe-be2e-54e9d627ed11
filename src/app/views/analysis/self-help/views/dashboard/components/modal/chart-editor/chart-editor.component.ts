import { JsonPipe, AsyncPipe } from '@angular/common';
import {
  FormControl,
  FormGroup,
  ReactiveFormsModule,
  Validators,
} from '@angular/forms';
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
  inject,
  signal,
} from '@angular/core';
import { NZ_MODAL_DATA } from 'ng-zorro-antd/modal';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzCascaderModule } from 'ng-zorro-antd/cascader';
import { NzRadioModule } from 'ng-zorro-antd/radio';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { lastValueFrom, map, startWith } from 'rxjs';

import { CaerusApiService } from '@api/caerus';
import {
  Analysis<PERSON>hart,
  AnalysisChartInput,
  AnalysisSubject,
  DimensionMenuVo,
} from '@api/caerus/model';
import { QueryInputVo } from '@api/query-engine/model';
import { CompareTime, MyValidators, Time } from '@common/class';
import { checkForm, isNotEmpty } from '@common/function';
import { DebugDirective } from '@shared/directives';
import { isDev } from '@common/const';
import { NzTreeSelectModule } from 'ng-zorro-antd/tree-select';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { findIndex } from 'lodash';

class AnalysisChartForm {
  id = new FormControl<number>(null);
  saveType = new FormControl<1 | 0>(1, { nonNullable: true });
  dashboardPath = new FormControl<any>(null);
  dashboardName = new FormControl<string>(null, {
    validators: [Validators.required],
  });
  chartName = new FormControl<string>(null, {
    validators: [Validators.required, MyValidators.maxLength(20)],
  });
  subjectId = new FormControl<number>(null, {
    validators: [Validators.required],
  });
  analysisType = new FormControl<1 | 2 | 3>(null);
  chartConfig = new FormControl<string>(null);
  chartShowConfig = new FormControl<string>(null);
}

@Component({
  selector: 'app-chart-editor',
  templateUrl: './chart-editor.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    JsonPipe,
    ReactiveFormsModule,
    NzFormModule,
    NzInputModule,
    NzRadioModule,
    NzCascaderModule,
    NzSelectModule,
    DebugDirective,
    NzTreeSelectModule,
    NzSpinModule,
  ],
})
export class ChartEditorComponent implements AfterViewInit {
  readonly apiService = inject(CaerusApiService);
  readonly nzModalData: AnalysisChart = inject(NZ_MODAL_DATA);
  readonly destroyRef = inject(DestroyRef);

  tree = signal<any>([]);
  expandKey = signal([]);
  filterLabel = signal<string>(null);
  form = new FormGroup(new AnalysisChartForm());

  getTreeData() {
    this.apiService.fetchAnalysisDashboardTree().subscribe((res) => {
      if (!res?.data) {
        this.tree.set([]);
      } else {
        const arr = res.data.map((item) => {
          const children = item.subList.map((sub) => {
            return {
              key: sub.id,
              title: sub.name,
              isLeaf: true,
            };
          });
          if (children.length === 0) {
            return {
              key: item.id,
              title: item.name,
              isLeaf: true,
              disabled: true,
            };
          }
          return {
            key: item.id,
            title: item.name,
            selectable: false,
            children,
          };
        });
        this.tree.set(arr);
        this.expandKey.set([res.data[0].id]);
        if (res.data[0].subList.length !== 0) {
          this.form.controls.dashboardPath.setValue(res.data[0].subList[0].id);
        }
      }
    });
  }

  subjectList = signal<AnalysisSubject[]>([]);

  autoTips: Record<string, Record<string, string>> = {
    default: {
      required: '必填项',
    },
  };

  async ngAfterViewInit(): Promise<void> {
    this._subscribeToSaveTypeChange();
    this._fetchAnalysisSubjectList();
    await this._init();
    this.getTreeData();
  }

  private _fetchAnalysisSubjectList() {
    this.apiService.fetchAnalysisSubjectList().subscribe((res) => {
      if (res.data) {
        this.subjectList.set(res.data);
      }
    });
  }

  private _subscribeToSaveTypeChange() {
    this.form.controls.saveType.valueChanges
      .pipe(
        startWith(this.form.controls.saveType.value),
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe((value) => {
        if (value === 1) {
          this.form.controls.dashboardPath.enable();
          this.form.controls.dashboardName.disable();
          this.form.controls.subjectId.disable();
        } else {
          this.form.controls.dashboardName.reset();
          this.form.controls.dashboardPath.disable();
          this.form.controls.dashboardName.enable();
          this.form.controls.subjectId.enable();

          if (!this.form.controls.subjectId.value) {
            this.form.controls.subjectId.setValue(this.subjectList()[0]?.id);
          }
        }
      });
  }

  hasAuth(username: string) {
    return (
      isDev() ||
      username === window.localStorage.getItem('caerus.auth.username.py') ||
      username === window.localStorage.getItem('caerus.auth.username')
    );
  }

  private async _init() {
    const { id, chartConfig, analysisType } = this.nzModalData;
    const { dt, compareDt, filter, metrics, dimensions } = JSON.parse(
      chartConfig
    ) as QueryInputVo;

    let dimension: DimensionMenuVo;
    const dimensionList = await lastValueFrom(
      this.apiService
        .fetchDimension('menu-dimension')
        .pipe(map((res) => res.data))
    );

    if (Array.isArray(dimensions) && dimensions.length > 1) {
      dimensions.shift();
      dimension = dimensionList.find(
        (item) => item.extendName === dimensions[0].extendName
      );
    }

    const isArea = [
      'city_bio_region',
      'is_top20_city',
      'city_name',
      'province_name',
    ].includes(dimensions[0].extendName);
    const dtStr = `当前期=${
      Time.format(dt.startTime, dt.endTime) ?? `${dt.startTime}~${dt.endTime}`
    }`;
    const compareStr = compareDt
      ? `对比期=${
          CompareTime.format(compareDt.startTime, compareDt.endTime) ??
          `${compareDt.startTime}~${compareDt.endTime}`
        }`
      : '';
    const filterStr =
      filter.items.length > 0
        ? `维度值=${filter.items.map((item) => item.value[0].value).join(', ')}`
        : '';
    const metricsStr = `指标=${metrics
      .map((item) => item.aliasName)
      .join(', ')}`;
    const dimensionStr = isArea
      ? '构成维度=区域'
      : dimension
      ? `构成维度=${dimension.aliasName}`
      : '';
    const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'.split('');
    const labelArr = [dtStr, compareStr];
    const comparison = () => {
      return metrics.map((metric, index) => {
        const { aliasName, filter } = metric;
        const values = filter.items.map((items) => {
          return items.value.map((item) => {
            return item.value;
          });
        });

        const labels = [aliasName, ...values].flat(1).join('-');
        return `${letters[index]}:${labels}`;
      });
    };

    const comparisonStr = isNotEmpty(comparison)
      ? `对比项=${comparison().join(', ')}`
      : '';

    !id && this.form.controls.id.disable();
    this.form.reset(this.nzModalData);

    if (id && !this.hasAuth(this.nzModalData?.createBy)) {
      this.form.controls.dashboardPath.reset();
    }

    switch (analysisType) {
      case 1:
        labelArr.push(filterStr);
        labelArr.push(metricsStr);
        break;
      case 2:
        labelArr.push(dimensionStr);
        labelArr.push(filterStr);
        labelArr.push(metricsStr);
        break;
      case 3:
        labelArr.push(comparisonStr);
        break;
    }

    this.filterLabel.set(labelArr.filter(isNotEmpty).join('<br />'));
  }

  get valid(): boolean {
    if (this.form.invalid) {
      checkForm(this.form.controls);
    }

    return this.form.valid;
  }

  get value() {
    const arr = [];
    const filterData = this.tree().filter((t) => t.children);
    for (let i = 0; i < filterData.length; i++) {
      if (
        findIndex(filterData[i].children, [
          'key',
          this.form.value.dashboardPath,
        ]) !== -1
      ) {
        arr.push(filterData[i].key);
        break;
      }
    }
    arr.push(this.form.value.dashboardPath);
    this.form.value.dashboardPath = arr;
    return this.form.value as Required<AnalysisChartInput>;
  }
}
