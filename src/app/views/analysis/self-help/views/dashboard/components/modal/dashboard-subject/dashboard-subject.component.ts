import { AsyncPipe, JsonPipe } from '@angular/common';
import { FormControl, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { AfterViewInit, ChangeDetectionStrategy, Component, inject } from '@angular/core';
import {NZ_MODAL_DATA } from 'ng-zorro-antd/modal';
import { NzFormModule } from 'ng-zorro-antd/form';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzRadioModule } from 'ng-zorro-antd/radio';
import { map } from 'rxjs';

import { CaerusApiService } from '@api/caerus';
import { AnalysisDashboardInput, AnalysisDashboardOutput } from '@api/caerus/model';
import { DebugDirective } from '@shared/directives';
import { checkForm } from '@common/function';
import { isDev } from '@common/const';

class DashboardInfoForm {

  /** 看板主题 */
  subjectId = new FormControl<number>(null, { validators: [ Validators.required ] });

}

@Component({
  selector: 'app-dashboard-subject',
  templateUrl: './dashboard-subject.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    JsonPipe,
    AsyncPipe,
    ReactiveFormsModule,
    NzFormModule,
    NzInputModule,
    NzSelectModule,
    NzRadioModule,
    DebugDirective,
  ],
})
export class DashboardSubjectComponent implements AfterViewInit {

  readonly apiService = inject(CaerusApiService);
  readonly nzModalData: AnalysisDashboardOutput = inject(NZ_MODAL_DATA);

  form = new FormGroup(new DashboardInfoForm());
  subjectList$ = this.apiService.fetchAnalysisSubjectList().pipe(
    map(res => res.data)
  );

  ngAfterViewInit(): void {
  }


  get valid(): boolean {
    if (this.form.invalid) {
      checkForm(this.form.controls);
    }
    
    return this.form.valid;
  }

  
  get value() {
    return this.form.value;
  }
  
}
