header {
  animation: shrink-container linear both;
}

h2 {
  animation: shrink-name linear both;
}

ul {
  animation: shrink-footer linear both;
  overflow: hidden;
}

header,
h2,
ul {
  animation-timeline: scroll();
  animation-range: 0 150px;
}

@keyframes shrink-container {
  from {
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
    font-size: 14px;
  }
  to {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
    font-size: 12px;
  }
}

@keyframes shrink-footer {
  from {
    height: 22px;
    opacity: 1;
  }
  70% {
    opacity: 0;
  }
  to {
    height: 0;
    opacity: 0;
  }
}

@keyframes shrink-name {
  from {
    font-size: 1.5rem;
    margin: 8px 0;
  }
  to {
    font-size: 1rem;
    margin: 4px 0;
  }
}
