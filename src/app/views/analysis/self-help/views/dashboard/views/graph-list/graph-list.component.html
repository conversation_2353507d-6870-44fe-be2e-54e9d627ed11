@if (dashboardResource.isLoading()) {
  <div class="flex w-full h-screen">
    <app-line-spin class="m-auto" />
  </div>
}
@else {
  @let dashboard = dashboardResource.value();
  <header class="sticky top-0 z-10 flex flex-col md:flex-row md:items-center gap-x-3 gap-y-2 px-5 bg-white shadow-md">
    <div class="flex-1 min-h-full md:min-w-0">
      <app-breadcrumb>
        <app-breadcrumb-item routerLink="/analysis/self-help/dashboard">我的图表</app-breadcrumb-item>
        <app-breadcrumb-item routerLink="/analysis/self-help/dashboard" [queryParams]="{ subject: dashboard.subjectId }">
          {{ dashboard.subjectName || '&nbsp;' }}
        </app-breadcrumb-item>
      </app-breadcrumb>
      <h2 class="leading-none">{{ dashboard.dashboardName || '&nbsp;' }}</h2>
      <ul class="flex-1 min-h-0 flex items-center gap-x-4">
        <li class="flex items-center gap-x-1.5 text-gray-400">
          <UserFillIcon />
          {{ dashboard.createByCn || '&nbsp;' }}
        </li>
        <li class="flex items-center gap-x-1.5 text-gray-400 truncate" [title]="'更新时间：' + (dashboard.updateTime | date: 'yyyy-MM-dd HH:mm:ss')">
          <CalendarIcon />
          {{ dashboard.createTime | date: 'yyyy-MM-dd HH:mm:ss' }}
        </li>
      </ul>
    </div>

    <div class="flex items-center gap-x-3">
      @if (dragEnabled()) {
        <span class="text-red-500">说明:将鼠标放在图表上，长按左键可拖动图表，更换图表位置</span>
        <button nz-button nzType="primary" (click)="handleSaveChartSort()">
          <DeleteIcon class="mr-1" />
          保存
        </button>

        <button nz-button nzType="default" (click)="cancelDrag()">
          取消
        </button>
      } @else {
        <nz-input-group [nzSuffix]="suffixIconSearch" class="w-44!">
          <input style="font-size: 12px;" type="text" spellcheck="false" nz-input placeholder="请输入(支持拼音、首拼)" [(ngModel)]="keyword" />
        </nz-input-group>

        @if (mode() === 'default') {
          <button nz-button nzType="default" (click)="mode.set('delete')">
            <DeleteIcon class="mr-1" /> 批量删除
          </button>

          @if (hasAuth(dashboard.createBy)) {
            <button nz-button [nzType]="dragDisabled() ? 'default' : 'primary'" (click)="toggleDragState()">
              <SwapIcon class="mr-1" />
              更换位置
            </button>
          }
        }

        @if (mode() === 'delete') {
          <button nz-button nzType="default" nzDanger (click)="handleBatchDelete()" [disabled]="selectedList().length === 0">
            <DeleteIcon class="mr-1" /> 立即删除
          </button>

          <button nz-button nzType="default" (click)="mode.set('default'); selectedList.set([])">
            取消
          </button>
        }
      }
    </div>
  </header>

  <div class="relative grid grid-cols-1 lg:grid-cols-2 gap-4 p-4" cdkDropList cdkDropListOrientation="mixed" (cdkDropListDropped)="drop($event)">
    @if (chartResource.isLoading()) {
      <div class="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-2/3 flex flex-col items-center justify-center gap-y-3 col-span-2 h-full">
        <app-line-spin />
      </div>
    }
    @else {
      @for (item of chartResource.value() | search: keyword() : 'chartName'; track item.id) {
        <app-graph-box
          cdkDrag
          cdkDragBoundary="body"
          [class.only-one]="$count === 1"
          [deleteMode]="mode() === 'delete'"
          [selected]="isSelected(item)"
          [dragEnabled]="dragEnabled()"
          [cdkDragDisabled]="dragDisabled()"
          [data]="item"
          (remove)="chartResource.reload()"
          (onSelect)="toggleSelect(item)"
        >
          <div *cdkDragPlaceholder class="aspect-auto xl:aspect-16/10 border border-dashed border-primary rounded-xs bg-primary/20"></div>
        </app-graph-box>
      } @empty {
        <div class="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-2/3 flex flex-col items-center justify-center gap-y-3 col-span-2 h-full">
          <img src="assets/images/svg/empty.svg" />
          <span class="text-slate-500">无数据</span>
          <button nz-button nzType="primary" [routerLink]="['/analysis/self-help']" [queryParams]="{ subjectPath: dashboard.subjectId + ',' + dashboard.id }">新建图表</button>
        </div>
      }
      @if (chartResource.value().length < 5) {
        <div class="h-screen max-lg:hidden"></div>
      }
    }

  </div>
}

<ng-template #suffixIconSearch>
  @switch (true) {
    @case (!!keyword()) {
      <CloseFillIcon class="ant-input-clear-icon" (click)="keyword.set(null)" />
    }
    @default {
      <SearchIcon />
    }
  }
</ng-template>