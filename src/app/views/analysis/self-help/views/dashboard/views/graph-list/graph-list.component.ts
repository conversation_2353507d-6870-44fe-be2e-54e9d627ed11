import { DatePipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, DestroyRef, inject, input, signal } from '@angular/core';
import { CdkDragDrop, DragDropModule, moveItemInArray } from '@angular/cdk/drag-drop';
import { rxResource } from '@angular/core/rxjs-interop';
import { FormsModule } from '@angular/forms';
import { RouterLink } from '@angular/router';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzModalService } from 'ng-zorro-antd/modal';
import { map, tap } from 'rxjs';

import { isDev } from '@common/const';
import { Confirmed } from '@common/decorator';
import { PAGE_NAME, PageEnterLeaveDirective } from '@common/directive';
import { AnalysisChart, AnalysisChartSortInput } from '@api/caerus/model';
import { CaerusApiService } from '@api/caerus';
import { BreadcrumbModule } from '@shared/modules/headless';
import { IconCalendarComponent, IconCloseFillComponent, IconDeleteComponent, IconSearchComponent, IconSwapComponent, IconUserFillComponent } from '@shared/modules/icons';
import { LineSpinComponent } from '@shared/components/line-spin';
import { SearchPipe } from '@shared/pipes/search';
import { GraphBoxComponent } from '../../components';


@Component({
  selector: 'app-graph-list',
  templateUrl: './graph-list.component.html',
  styleUrl: './graph-list.component.css',
  host: {
    class: 'block',
  },
  hostDirectives: [PageEnterLeaveDirective],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    RouterLink,
    FormsModule,
    DragDropModule,
    NzButtonModule,
    NzInputModule,
    BreadcrumbModule,
    GraphBoxComponent,
    LineSpinComponent,
    IconSwapComponent,
    IconCalendarComponent,
    IconDeleteComponent,
    IconUserFillComponent,
    IconCloseFillComponent,
    IconSearchComponent,
    SearchPipe,
    DatePipe
  ],
  providers: [{ provide: PAGE_NAME, useValue: window.location.pathname }],
})
export class GraphListComponent {
  readonly modal = inject(NzModalService);
  readonly message = inject(NzMessageService);
  readonly apiService = inject(CaerusApiService);
  readonly destroyRef = inject(DestroyRef);

  id = input<string>();
  keyword = signal<string>(null);
  chartList = signal<AnalysisChart[]>([]);
  chartSort = signal<AnalysisChartSortInput[]>([]);
  loading = signal(true);
  dragDisabled = signal(true);
  dragEnabled = computed(() => !this.dragDisabled());
  selectedList = signal<AnalysisChart[]>([]);
  mode = signal<'default' | 'delete'>('default');

  chartResource = rxResource({
    request: () => this.id(),
    loader: ({ request }) => this.apiService.fetchAnalysisChartList({ dashboardId: request }, 'new-chart-list').pipe(
      map(res => res.data.sort((a, b) => b.sort - a.sort)),
      tap(() => {
        this._initChartSort();
      })
    )
  })

  dashboardResource = rxResource({
    request: () => this.id(),
    loader: ({ request }) => this.apiService.fetchAnalysisDashboardDetail(request).pipe(
      map(res => res.data)
    )
  })

  hasAuth(username: string) {
    return (
      isDev() || 
      username === window.localStorage.getItem('caerus.auth.username.py') || 
      username === window.localStorage.getItem('caerus.auth.username')
    );
  }

  private _initChartSort() {
    const sorts = this.chartList()
      .map(({ id: chartId, sort }) => ({ chartId, sort }))
      .sort((a, b) => b.sort - a.sort);

    this.chartSort.set(sorts);
  }

  private _updateChartSort() {
    const count = this.chartList().length;
    const sorts = this.chartList().map((item, index) => {
      return {
        chartId: item.id,
        sort: count - index,
      };
    });

    this.chartSort.set(sorts);
  }

  drop(event: CdkDragDrop<AnalysisChart[]>) {
    moveItemInArray(this.chartResource.value(), event.previousIndex, event.currentIndex);
    this._updateChartSort();
  }

  toggleDragState() {
    this.keyword.set(null);
    this.dragDisabled.update(state => !state);
  }

  cancelDrag() {
    this.dragDisabled.set(true);
    this.chartResource.reload();
  }

  toggleSelect(data: AnalysisChart) {
    if (this.isSelected(data)) {
      this.selectedList.update(items => {
        return items.filter(item => item !== data);
      })
    } else {
      this.selectedList.update(items => {
        return items.concat(data);
      })
    }
  }

  isSelected(data: AnalysisChart) {
    return this.selectedList().some(item => item === data);
  }

  handleSaveChartSort() {
    this.apiService.updateAnalysisChartSort(this.chartSort()).subscribe(res => {
      if (res.status !== '00000') {
        return this.message.error(res.error || res.message);
      }
      this.message.success('操作成功');
      this.dragDisabled.set(true);
    });
  }


  @Confirmed('确定删除吗?')
  handleBatchDelete() {
    const ids = this.selectedList().map(item => item.id);

    this.apiService.removeAnalysisChart(ids).subscribe(res => {
      if (res.status !== '00000') {
        this.message.error(res.error);
      }

      if (res.status === '00000') {
        res.data 
          ? this.message.success('删除成功') 
          : this.message.error('删除失败');

        this.mode.set('default');
        this.selectedList.set([]);
        this.chartResource.reload();
      }
    })
  }

}
