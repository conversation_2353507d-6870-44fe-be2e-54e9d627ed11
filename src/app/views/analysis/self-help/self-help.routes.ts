import { Routes } from '@angular/router';

export const ROUTES: Routes = [
  {
    path: '',
    loadComponent: () => import('./self-help.component').then(mod => mod.SelfHelpComponent),
    children: [
      {
        path: '',
        loadComponent: () => import('./views/tools/tools.component').then(mod => mod.ToolsComponent),
      },
      {
        path: 'dashboard',
        loadComponent: () => import('./views/dashboard/dashboard.component').then(mod => mod.DashboardComponent),
      },
      {
        path: 'dashboard/:id',
        loadComponent: () => import('./views/dashboard/views/graph-list/graph-list.component').then(mod => mod.GraphListComponent),
      },
      {
        path: 'dashboard/:dashboardId/query/:queryId',
        loadComponent: () => import('./views/tools/tools.component').then(mod => mod.ToolsComponent),
      }
    ]
  },
];
