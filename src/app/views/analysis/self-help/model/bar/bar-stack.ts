import { XAxisOptions, YAxisOptions } from 'highcharts';
import { DtType } from '@common/service/query-engine';
import { QueryOutputHeaderVo, QueryOutputVo } from '@api/query-engine/model';
import { createPoint, createValueElement, groupBy, isNotEmpty, isNotUndefined, toDecimals, getDateStr, getWeekByDate } from '@common/function';
import { getCategories, getDimensionField, getNumberFields, getProportionFields } from '@common/chart';
import { BaseHighCharts, SeriesItem } from '@common/chart/highcharts';


function tooltipFormatter(that: BarStack) {
  return function() {
    const { series, dtType } = that; // sortFn不能解构，否则失效
    const result = [];
    const map = new Map();
    const params = this.points.sort(that.sortFn);
    const _baseXAxis = params.filter(item => item.series.xAxis.index === 0);
    const _compareXAxis = params.filter(item => item.series.xAxis.index === 1);
    
    if (isNotEmpty(_baseXAxis)) {
      _baseXAxis.forEach(item => {
        if (map.has(item.series.name)) {
          const arr = map.get(item.series.name) as any[];
          arr.push(item);
          arr.reverse();
        } else {
          map.set(item.series.name, [item]);
        }
      });
    } else if (isNotEmpty(_compareXAxis)) {
      _compareXAxis.forEach(item => {
        if (map.has(item.series.name)) {
          const arr = map.get(item.series.name) as any[];
          arr.push(item);
          arr.reverse();
        } else {
          map.set(item.series.name, [item]);
        }
      });
    }
    
    const merged = [...map.values()].flat(1);

    

    result.push('<table class="text-sm">');
    merged.forEach((params, index) => {
      const { series: { name: seriesName, yAxis: { index: yAxisIndex } }, y: value, x: categorie, color, point: { yw } } = params;
      const { isPercent, isProportion } = series.find(item => item.name === seriesName);
      const previousItem = merged[index-1];
      const x_str = getDateStr(categorie, dtType);
      const w = getWeekByDate(categorie, dtType);
      
      if (previousItem?.series?.name === seriesName) {
        const currentValue = value;
        const previousValue = previousItem.y; 
        const diffValue = toDecimals((currentValue - previousValue));
        const ratioValue = toDecimals((currentValue - previousValue) / previousValue);

        result.push(`
          <tr>
            <td class="flex items-center" style="color:${color}">
              ${createPoint(color)}
              (${x_str}${w ? '<span class="flex-1 min-w-0 w-1"></span>' : ''}${w || ''})
            </td>
            <td class="pr-2" style="color:${color}">${seriesName}: </td>
            <td class="text-right">
              ${
                isPercent || isProportion ? 
                (Number.isFinite(value) ? (toDecimals(value) + '%') : '-') :
                (Number.isFinite(value) ? Intl.NumberFormat().format(value) : '-')
              }
            </td>
            <td class="pl-1">
              ${
                (isPercent || isProportion) ? createValueElement(diffValue, '(较对比期 {n}pp)') :
                Number.isFinite(ratioValue) ? createValueElement(ratioValue, '(较对比期 {n}%)') :
                '(较对比期 --)'
              }
            </td>
          </tr>
        `);
      } else {
        result.push(`
          <tr>
            <td class="flex items-center" style="color:${color}">
              ${createPoint(color)}
              (${x_str}${w ? '<span class="flex-1 min-w-0 w-1"></span>' : ''}${w || ''})
            </td>
            <td class="pr-2" style="color:${color}">${seriesName}: </td>
            <td class="text-right">
              ${
                Number.isFinite(value) 
                  ? isPercent || isProportion
                    ? (toDecimals(value) + '%')
                    : (Intl.NumberFormat().format(value))
                  : '-'
              }
            </td>
            <td>
              ${
                yw
                  ? isPercent || isProportion
                    ? (Number.isFinite(yw.diff)  ? createValueElement(yw.diff * 100, '(周同比 {n}pp)') : '(周同比 --)')
                    : (Number.isFinite(yw.ratio) ? createValueElement(yw.ratio,      '(周同比 {n}%)')  : '(周同比 --)')
                  : ''
              }
            </td>
          </tr>
        `);
      }
      
    })
    result.push('</table>');

    return result.join('');
  }
}


class xAxisItem {
  categories: string[];
  opposite: boolean;
  tickInterval = 1;
  tickWidth = 1;
  tickColor = '#ccd6eb';
  lineColor = '#ccd6eb';
  gridLineColor = '#e6e6e6';
  crosshair = true;
  labels = {
    useHTML: true,
    formatter: function() {
      if (/\d{4}-\d{2}-\d{2}/.test(this.value)) {
        const day = new Date(this.value.replace(/-/g, '/')).getDay();
        if (day === 0 || day === 6) {
          return `<span class="text-primary font-semibold">${this.value}</span>`;
        }
      }
      
      return this.value;
    }
  }

  constructor({ categories, opposite }: Partial<xAxisItem>) {
    categories && (this.categories = categories);
    opposite && (this.opposite = opposite);
  }
}


export class BarStack extends BaseHighCharts {

  responsive = {
    rules: [
      {
        condition: { maxWidth: 1200 },
        chartOptions: {
          xAxis: [
            { tickInterval: 2 },
          ]
        }
      },
      {
        condition: { maxWidth: 800 },
        chartOptions: {
          xAxis: [
            { tickInterval: 4 },
          ]
        }
      },
      {
        condition: { maxWidth: 500 },
        chartOptions: {
          xAxis: [
            { tickInterval: 6 },
          ]
        }
      }
    ]
  }
  
  xAxis: XAxisOptions[] = [];
  yAxis: YAxisOptions[] = [
    {
      title: { text: '' },
      labels: { format: undefined, },
      gridLineWidth: 1,
      gridLineColor: '#e6e6e6',
    },
    {
      title: { text: '' },
      labels: { format: undefined, },
      gridLineWidth: 1,
      gridLineColor: '#e6e6e6',
      opposite: true
    }
  ];


  constructor(
    properties: QueryOutputVo,
    public seriesType: 'column' | 'line' = 'column',
    public dtType: DtType,
  ) {
    super();

    const { headers, data } = properties;
    const categories = getCategories(headers, data);
    const series = this.getSeries(headers, data, 0);
    
    this.setCategories([categories]);
    this.setSeries(series);
    this.legend.verticalAlign = 'top';
  }


  getSeries(
    headers: { [key: string]: QueryOutputHeaderVo },
    data: { [key: string]: string }[],
    xAxisIndex?: number
  ) {
    const [dimensionField] = getDimensionField(headers);
    const [proportionFields] = getProportionFields(headers);
    const [numberFields] = getNumberFields(headers);
    const source = ['当前', '对比'];

    if (!data) {
      throw `${source[xAxisIndex] ?? ''}日期无数据`;
    }
    
    const groupData = groupBy<{[key: string]: { [key: string]: string }}>(data, dimensionField);
    const list = [] as SeriesItem[];
    
    Object.keys(groupData).sort().forEach((key, n) => {
      const series = new SeriesItem();
      const { aliasName, dataUnit } = headers[numberFields];
      
      if (dataUnit === '%') {
        series.isPercent = true;
      }

      series.dataUnit = dataUnit;
      series.stack = `${xAxisIndex}`;
      series.stacking = 'normal';
      series.xAxis = xAxisIndex;
      series.name = `${aliasName}-${key}`;
      series.data = groupData[key].map((item) => {
        const numeric = Number(item[numberFields]);
        let value: number = null;
        
        if (item[numberFields] === null) {
          value = null;
        } else if (dataUnit === '%') {
          value = toDecimals(numeric, 1, 4);
        } else {
          value = numeric;
        }

        if (isNotUndefined(item[`${numberFields}:yw_DIFF`], item[`${numberFields}:yw_DIFF_RATIO`])) {
          return {
            y: value,
            yw: {
              diff: +item[`${numberFields}:yw_DIFF`],
              ratio: +item[`${numberFields}:yw_DIFF_RATIO`],
              // proportion_diff: +item[`${numberFields}:proportion:yw_DIFF`],
            }
          };
        }

        return {
          y: value
        };
      });

      list.push(series);
    })

    console.log('[proportionFields]', proportionFields);
    
    if (proportionFields) {
      Object.keys(groupData).sort().forEach((key, n) => {
        const series = new SeriesItem();
        const { aliasName, dataUnit } = headers[proportionFields];
        
        series.type = 'spline';
        series.isProportion = true;
        series.dataUnit = dataUnit;
        series.xAxis = xAxisIndex;
        series.yAxis = 1;
        series.linkedTo = `${aliasName}-${key}`;
        series.name = `${aliasName}-${key}-占比`;
        series.marker = {
          symbol: 'circle',
          radius: 2,
        };
        series.data = groupData[key].map((item) => {
          const value = Number(item[proportionFields]);

          if (item[proportionFields] === null) {
            return null;
          }
          
          return toDecimals(value, 1, 4);
        });
  
        list.push(series);
      })
    }

    if (this.seriesType === 'line') {
      return list.map(({ stack, stacking, ...item }) => {
        const result = {
          ...item,
          type: 'spline',
          marker: {
            symbol: 'circle',
            radius: 2,
          }
        };

        if (xAxisIndex > 0) {
          result.dashStyle = 'Dash';
        }

        return result;
      })
    }

    return list;
  }


  setCategories(values: string[][]): void {
    values.forEach((categories, index) => {
      this.xAxis.push(new xAxisItem({ categories, opposite: index > 0 }));
    })
  }


  override getOption() {
    const value = this;

    return {
      ...value,
      tooltip: {
        ...value.tooltip,
        formatter: tooltipFormatter(this)
      }
    };
  }
  
}
