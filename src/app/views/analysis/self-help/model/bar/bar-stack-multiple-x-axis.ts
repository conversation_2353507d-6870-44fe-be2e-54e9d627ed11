import { XAxisOptions, YAxisOptions } from 'highcharts';
import { QueryOutputHeaderVo, QueryOutputVo } from '@api/query-engine/model';
import { createPoint, createValueElement, fillEmpty, getMaxLength, groupBy, isNotEmpty, toDecimals, getDateStr, getWeekByDate } from '@common/function';
import { getCategories, getDimensionField, getNumberFields, getProportionFields } from '@common/chart';
import { BaseHighCharts, Pattern, SeriesItem } from '@common/chart/highcharts';
import { DtType } from '@common/service/query-engine';


function tooltipFormatter(that: BarStackMultipleXAxis) {
  return function() {
    const { series, dtType } = that; // sortFn不能解构，否则失效
    const result = [];
    const map = new Map();
    const params = this.points.sort(that.sortFn);
    const _baseXAxis = params.filter(item => item.series.xAxis.index === 0);
    const _compareXAxis = params.filter(item => item.series.xAxis.index === 1);
    

    if (isNotEmpty(_baseXAxis)) {
      _baseXAxis.forEach(item => {
        map.set(item.series.name, []);
      })
    } else if (isNotEmpty(_compareXAxis)) {
      _compareXAxis.forEach(item => {
        map.set(item.series.name, []);
      })
    }

    params.forEach(item => {
      if (map.has(item.series.name)) {
        let arr = map.get(item.series.name) as any[];
        
        arr.push(item);
        arr = arr.sort((a, b) => a.series.xAxis.index - b.series.xAxis.index);
      }
    });
    
    const merged = [...map.values()].flat(1);
    
    result.push('<table class="text-sm">');

    const getAxis = (index: number) => {
      return merged.find(item => item.series.xAxis.index === index);
    }
    
    merged.forEach((point, index) => {
      const { series: { name: seriesName, yAxis: {index: yAxisIndex }, xAxis: { index: xAxisIndex }}, y: value, color } = point;
      const { isPercent, isProportion } = series.find(item => item.name === seriesName);
      const previousItem = merged[index-1];
      const currentValue  = previousItem?.y; 
      const previousValue = value;
      const diffValue = toDecimals((currentValue - previousValue));
      const ratioValue = toDecimals((currentValue - previousValue) / previousValue);
      const isCompareSeries = previousItem?.series?.name === seriesName;
      const hasBasic = getAxis(0) !== null;
      const hasCompare = getAxis(1) !== null;
      const VALUE = 
        Number.isFinite(value) 
          ? isPercent || isProportion
            ? toDecimals(value) + "%"
            : Intl.NumberFormat().format(value)
          : '-';

      const DIFF_VALUE = 
        Number.isFinite(ratioValue) 
          ? isPercent || isProportion
            ? createValueElement(diffValue, "{n}pp")
            : createValueElement(ratioValue, "{n}%")
          : '--';

      if (index === 0) {
        const baseAxis = getAxis(0);
        const compareAxis = getAxis(1);
        const x0 = baseAxis?.x;
        const x1 = compareAxis?.x;
        const x0_str = getDateStr(x0, dtType);
        const x1_str = getDateStr(x1, dtType);
        const w0 = getWeekByDate(x0, dtType);
        const w1 = getWeekByDate(x1, dtType);

        result.push(`
          <thead>
            <tr class="border-b border-gray-200 py-2">
              <td>指标名称</td>
              ${hasBasic && x0 ? (`
                <td class="text-center px-1">当前期 <br /> 
                  (${x0_str}${w0})
                </td>
              `) : ''}
              ${hasCompare && x1 ? (`
                <td class="text-center px-1">对比期 <br /> 
                  (${x1_str}${w1})
                </td>
              `) : ''}
              ${hasBasic && x0 && hasCompare && x1 ? (`<td class="text-center px-3">较对比期</td>`) : ''}
            </tr>
          </thead>
        `);
      }

      if (!isCompareSeries) {
        result.push('<tr>');
        result.push(`<td class="pr-2" style="color:${color}">${createPoint(color)} ${seriesName}: </td>`);
        result.push(`<td class="${hasCompare ? 'text-right' : 'text-center'}">${VALUE}</td>`);
      }

      if (isCompareSeries) {
        result.push(`<td class="text-right">${VALUE}</td>`);
        result.push(`<td class="text-right">${DIFF_VALUE}</td>`);
        result.push('</tr>');
      }
    })
    
    result.push('</table>');

    return result.join('');
  }
}


class xAxisItem {
  categories: string[];
  opposite: boolean;
  tickInterval = 1;
  tickWidth = 1;
  tickColor = '#ccd6eb';
  lineColor = '#ccd6eb';
  gridLineColor = '#e6e6e6';
  crosshair = true;
  linkedTo: number;
  labels = {
    useHTML: true,
    formatter: function() {
      if (/\d{4}-\d{2}-\d{2}/.test(this.value)) {
        const day = new Date(this.value.replace(/-/g, '/')).getDay();
        if (day === 0 || day === 6) {
          return `<span class="text-primary font-semibold">${this.value}</span>`;
        }
      }
      
      return this.value;
    }
  }

  constructor({ categories, opposite }: Partial<xAxisItem>) {
    categories && (this.categories = categories);
    opposite && (this.opposite = opposite);
  }
}


export class BarStackMultipleXAxis extends BaseHighCharts {

  xAxis: XAxisOptions[] = [];
  yAxis: YAxisOptions[] = [
    {
      title: { text: '' },
      labels: { format: undefined, },
      gridLineWidth: 1,
      gridLineColor: '#e6e6e6',
    },
    {
      title: { text: '' },
      labels: { format: undefined, },
      gridLineWidth: 1,
      gridLineColor: '#e6e6e6',
      opposite: true
    }
  ];


  constructor(
    properties: QueryOutputVo,
    public seriesType: 'column' | 'line' = 'column',
    public dtType: DtType,
  ) {
    super();

    const { headers, data, compareData } = properties;
    const primary_xaxis = getCategories(headers, data);
    const secondary_xaxis = getCategories(headers, compareData);
    const max_xaxis_length = getMaxLength(primary_xaxis, secondary_xaxis);
    const primary_series = this.getSeries(headers, data, 0);
    const secondary_series = this.getSeries(headers, compareData, 1);

    this.setCategories([
      fillEmpty(max_xaxis_length)(primary_xaxis), 
      fillEmpty(max_xaxis_length)(secondary_xaxis)
    ]);
    
    primary_series.forEach((item, index) => {
      primary_series[index].data.length = max_xaxis_length;
    })

    secondary_series.forEach((item, index) => {
      secondary_series[index].data.length = max_xaxis_length;
    })

    this.setSeries([...primary_series, ...secondary_series]);
    // this.colors.length = this.series.length / 2;
    this.colors.length = Math.ceil(this.series.length / 2);
    this.legend.verticalAlign = 'top';
  }


  getSeries(
    headers: { [key: string]: QueryOutputHeaderVo },
    data: { [key: string]: string }[],
    xAxisIndex?: number
  ) {
    const [dimensionField] = getDimensionField(headers);
    const [proportionFields] = getProportionFields(headers);
    const [numberFields] = getNumberFields(headers);
    const source = ['当前', '对比'];

    if (!data) {
      throw `${source[xAxisIndex] ?? ''}日期无数据`;
    }
    
    const groupData = groupBy<{[key: string]: { [key: string]: string }}>(data, dimensionField);
    const list = [] as SeriesItem[];

    Object.keys(groupData).sort().forEach((key, n) => {
      const series = new SeriesItem();
      const { aliasName, dataUnit } = headers[numberFields];

      if (xAxisIndex > 0) {
        series.linkedTo = `${aliasName}-${key}`;
      }
      
      if (dataUnit === '%') {
        series.isPercent = true;
      }

      series.xAxis = xAxisIndex;
      series.dataUnit = dataUnit;
      series.name  = `${aliasName}-${key}`;
      series.data  = groupData[key].map((item) => {
        const value = Number(item[numberFields]);
        const res = { y: value } as any;

        if (item[numberFields] === null) {
          return null;
        }

        if (xAxisIndex === 1) {
          res.color = {
            pattern: new Pattern({ color: this.colors[n] })
          };
        } else {
          res.color = this.colors[n];
        }

        if (dataUnit === '%') {
          res.y = toDecimals(value, 1, 4);
        }

        return res;
      });

      series.type = 'column';
      series.stack = `${xAxisIndex}`;
      series.stacking = 'normal';

      list.push(series);
    })

    if (proportionFields) {
      Object.keys(groupData).sort().forEach((key) => {
        const series = new SeriesItem();
        const { aliasName, dataUnit } = headers[proportionFields];
        
        if (xAxisIndex > 0) {
          series.dashStyle = 'Dash';
          series.lineWidth = 1;
        }

        series.type = 'spline';
        series.isProportion = true;
        series.dataUnit = dataUnit;
        series.xAxis = xAxisIndex;
        series.yAxis = 1;
        series.linkedTo = `${aliasName}-${key}`;
        series.name = `${aliasName}-${key}-占比`;
        series.marker = {
          symbol: 'circle',
          radius: 2,
        };
        series.data = groupData[key].map((item) => {
          const value = Number(item[proportionFields]);

          if (item[proportionFields] === null) {
            return null;
          }
          
          return toDecimals(value, 1, 4);
        });
  
        list.push(series);
      })
    }
    
    if (this.seriesType === 'line') {
      return list.map(({ stack, stacking, data, ...item }) => {

        const result: any = {
          ...item,
          type: 'spline',
          marker: {
            symbol: 'circle',
            radius: 2,
          }
        };
        
        result.data = data.map((item) => {
          if (item === null) {
            return null;
          }
          /**
           * 解决：
           * 因从柱状图切换到条形图时
           * 因柱状图有纹理颜色导致
           * 曲线图线条颜色与图例颜色不一致的问题
           */
          return item?.y || item;
        })

        if (xAxisIndex > 0) {
          result.dashStyle = 'Dash';
        }

        return result;
      });
    }

    return list;
  }
  

  setCategories(values: string[][]): void {
    values.forEach((categories, index) => {
      const item = new xAxisItem({
        categories,
        opposite: index > 0,
      });

      if (index > 0) {
        item.linkedTo = 0;
      }

      this.xAxis.push(item);
    })
  }


  override getOption() {
    const value = this;
    
    return {
      ...value,
      tooltip: {
        ...value.tooltip,
        formatter: tooltipFormatter(this)
      },
      // plotOptions: {
      //   series: {
      //     events: {
      //       legendItemClick: handleLegendItemClick(this),
      //     }
      //   }
      // }
    };
  }
  
}
