import { XAxisOptions, YAxisOptions } from 'highcharts';
import { DtType } from '@common/service/query-engine';
import { BaseHighCharts, SeriesItem } from '@common/chart/highcharts';
import { createPoint, createValueElement, isNotUndefined, toDecimals, getDateStr, getWeekByDate } from '@common/function';
import { getCategories, getNumberFields } from '@common/chart';
import { QueryOutputHeaderVo, QueryOutputVo } from '@api/query-engine/model';


function tooltipFormatter(that: BasicLine) {
  return function(param: any) {
    const { series, dtType } = that; // sortFn不能解构，否则失效
    const result = [];
    const map = new Map();
    const params = this.points.sort(that.sortFn);

    params.forEach(item => {
      if (map.has(item.series.name)) {
        const arr = map.get(item.series.name) as any[];
        arr.push(item);
        arr.reverse();
      } else {
        map.set(item.series.name, [item]);
      }
    });
    
    const merged = [...map.values()].flat(1);

    result.push('<table class="text-sm">');
    
    merged.forEach((params) => {
      const { series: { name: seriesName, yAxis: { index: yAxisIndex } }, y: value, x: categorie, color, point: { yw } } = params;
      const { isPercent } = series.find(item => item.name === seriesName);
      const x_str = getDateStr(categorie, dtType);
      const w = getWeekByDate(categorie, dtType);
      
      result.push(`
        <tr>
          <td class="flex items-center" style="color:${color}">
            ${createPoint(color)}
            (${x_str}${w ? '<span class="flex-1 min-w-0 w-1"></span>' : ''}${w || ''})
          </td>
          <td class="pr-2" style="color:${color}">${seriesName}: </td>
          <td class="text-right">
            ${
              Number.isFinite(value) 
                ? isPercent 
                  ? (toDecimals(value) + '%')
                  : Intl.NumberFormat().format(value)
                : '-'
            }
            ${
              yw
                ? isPercent
                  ? (Number.isFinite(yw.diff)  ? createValueElement(yw.diff * 100, '(周同比 {n}pp)') : '(周同比 --)')
                  : (Number.isFinite(yw.ratio) ? createValueElement(yw.ratio, '(周同比 {n}%)')  : '(周同比 --)')
                : ''
            }
          </td>
        </tr>
      `);
      
    })
    result.push('</table>');

    return result.join('');
  }
}


class xAxisItem {
  categories: string[];
  opposite: boolean;
  tickInterval = 1;
  tickWidth = 1;
  tickColor = '#ccd6eb';
  lineColor = '#ccd6eb';
  gridLineColor = '#e6e6e6';
  crosshair = true;
  labels = {
    useHTML: true,
    formatter: function() {
      if (/\d{4}-\d{2}-\d{2}/.test(this.value)) {
        const day = new Date(this.value.replace(/-/g, '/')).getDay();
        if (day === 0 || day === 6) {
          return `<span class="text-primary font-semibold">${this.value}</span>`;
        }
      }
      
      return this.value;
    }
  }

  constructor({ categories, opposite }: Partial<xAxisItem>) {
    categories && (this.categories = categories);
    opposite && (this.opposite = opposite);
  }
}


export class BasicLine extends BaseHighCharts {

  responsive = {
    rules: [
      {
        condition: { maxWidth: 1200 },
        chartOptions: {
          xAxis: [
            { tickInterval: 2 },
          ]
        }
      },
      {
        condition: { maxWidth: 800 },
        chartOptions: {
          xAxis: [
            { tickInterval: 4 },
          ]
        }
      },
      {
        condition: { maxWidth: 500 },
        chartOptions: {
          xAxis: [
            { tickInterval: 6 },
          ]
        }
      }
    ]
  }

  xAxis: XAxisOptions[] = [];
  yAxis: YAxisOptions[] = [
    {
      title: { text: '' },
      labels: { format: undefined, },
      gridLineWidth: 1,
      gridLineColor: '#e6e6e6',
    },
    {
      title: { text: '' },
      labels: { format: undefined, },
      gridLineWidth: 1,
      gridLineColor: '#e6e6e6',
      opposite: true
    }
  ];
  
  plotOptions = {
    series: {
      turboThreshold: 999999999,
      marker: {
        radius: 3,
        symbol: 'circle'
      }
    }
  }

  constructor(
    properties: QueryOutputVo,
    public dtType: DtType,
  ) {
    super();

    const { headers, data } = properties;
    const categories = getCategories(headers, data);
    const series = this.getSeries(headers, data);

    this.setCategories([categories]);
    this.setSeries(series);
    this.legend.verticalAlign = 'top';
    this.chart.type = 'spline';
  }


  getSeries(
    headers: { [key: string]: QueryOutputHeaderVo },
    data: { [key: string]: string }[],
  ) {
    const numberFields = getNumberFields(headers);
    
    return numberFields.map((key, index) => {
      const series = new SeriesItem();
      const { aliasName, dataUnit } = headers[key];
      
      if (dataUnit === '%') {
        series.isPercent = true;
      }
      
      series.name = aliasName;
      series.dataUnit = dataUnit;
      series.data = data.map(item => {
        const numeric = Number(item[key]);
        let value: number = null;
        
        if (item[key] === null) {
          value = null;
        } else if (dataUnit === '%') {
          value = toDecimals(numeric, 1, 4);
        } else {
          value = numeric;
        }

        if (isNotUndefined(item[`${key}:yw_DIFF`], item[`${key}:yw_DIFF_RATIO`])) {
          return {
            y: value,
            yw: {
              diff: +item[`${key}:yw_DIFF`],
              ratio: +item[`${key}:yw_DIFF_RATIO`]
            }
          };
        }

        return {
          y: value
        }
      });

      return series;
    });
  }
  

  setCategories(values: string[][]): void {
    values.forEach((categories, index) => {
      this.xAxis.push(new xAxisItem({ categories, opposite: index > 0 }));
    })
  }

  
  override getOption() {
    const value = this;

    return {
      ...value,
      tooltip: {
        ...value.tooltip,
        formatter: tooltipFormatter(this)
      },
      plotOptions: {
        ...value.plotOptions,
        series: {
          ...value.plotOptions.series,
        }
      }
    };
  }
  
}
