import { XAxisOptions, YAxisOptions } from 'highcharts';
import { getCategories, getNumberFields } from '@common/chart';
import { BaseHighCharts, SeriesItem } from '@common/chart/highcharts';
import { createPoint, createValueElement, fillEmpty, getDateStr, getMaxLength, getWeekByDate, isEmpty, isNotEmpty, toDecimals } from '@common/function';
import { QueryOutputHeaderVo, QueryOutputVo } from '@api/query-engine/model';
import { DtType } from '@common/service/query-engine';


function tooltipFormatter(that: MultipleXAxis) {
  return function() {
    const { series, dtType } = that; // sortFn不能解构，否则失效
    const result = [];
    const map = new Map();
    const params = this.points.sort(that.sortFn);
    const _baseXAxis = params.filter(item => item.series.xAxis.index === 0);
    const _compareXAxis = params.filter(item => item.series.xAxis.index === 1);
    

    if (isNotEmpty(_baseXAxis)) {
      _baseXAxis.forEach(item => {
        map.set(item.series.name, []);
      })
    } else if (isNotEmpty(_compareXAxis)) {
      _compareXAxis.forEach(item => {
        map.set(item.series.name, []);
      })
    }

    params.forEach(item => {
      if (map.has(item.series.name)) {
        let arr = map.get(item.series.name) as any[];
        
        arr.push(item);
        arr = arr.sort((a, b) => a.series.xAxis.index - b.series.xAxis.index);
      }
    });
    
    const merged = [...map.values()].flat(1);
    
    result.push('<table class="text-sm">');

    const getAxis = (index: number) => {
      return merged.find(item => item.series.xAxis.index === index);
    }
    
    merged.forEach((point, index) => {
      const { series: { name: seriesName, yAxis: {index: yAxisIndex }, xAxis: { index: xAxisIndex }}, y: value, color } = point;
      const { isPercent, isProportion } = series.find(item => item.name === seriesName);
      const previousItem = merged[index-1];
      const currentValue  = previousItem?.y; 
      const previousValue = value;
      const diffValue = toDecimals((currentValue - previousValue));
      const ratioValue = toDecimals((currentValue - previousValue) / previousValue);
      const isCompareSeries = previousItem?.series?.name === seriesName;
      const hasBasic = getAxis(0) !== null;
      const hasCompare = getAxis(1) !== null;
      const VALUE = 
        Number.isFinite(value) 
          ? isPercent || isProportion
            ? toDecimals(value) + "%"
            : Intl.NumberFormat().format(value)
          : '-';

      const DIFF_VALUE = 
        Number.isFinite(ratioValue) 
          ? isPercent || isProportion
            ? createValueElement(diffValue, "{n}pp")
            : createValueElement(ratioValue, "{n}%")
          : '--';

      if (index === 0) {
        const baseAxis = getAxis(0);
        const compareAxis = getAxis(1);
        const x0 = baseAxis?.x;
        const x1 = compareAxis?.x;
        const x0_str = getDateStr(x0, dtType);
        const x1_str = getDateStr(x1, dtType);
        const w0 = getWeekByDate(x0, dtType);
        const w1 = getWeekByDate(x1, dtType);

        result.push(`
          <thead>
            <tr class="border-b border-gray-200 py-2">
              <td>指标名称</td>
              ${hasBasic && x0 ? (`
                <td class="text-center px-1">当前期 <br /> 
                  (${x0_str}${w0})
                </td>
              `) : ''}
              ${hasCompare && x1 ? (`
                <td class="text-center px-1">对比期 <br /> 
                  (${x1_str}${w1})
                </td>
              `) : ''}
              ${hasBasic && x0 && hasCompare && x1 ? (`<td class="text-center px-3">较对比期</td>`) : ''}
            </tr>
          </thead>
        `);
      }

      if (!isCompareSeries) {
        result.push('<tr>');
        result.push(`<td class="pr-2" style="color:${color}">${createPoint(color)} ${seriesName}: </td>`);
        result.push(`<td class="${hasCompare ? 'text-right' : 'text-center'}">${VALUE}</td>`);
      }

      if (isCompareSeries) {
        result.push(`<td class="text-right">${VALUE}</td>`);
        result.push(`<td class="text-right">${DIFF_VALUE}</td>`);
        result.push('</tr>');
      }
    })
    
    result.push('</table>');

    return result.join('');
  }
}

class xAxisItem {
  categories: string[];
  opposite: boolean;
  tickInterval = 1;
  tickWidth = 1;
  tickColor = '#ccd6eb';
  lineColor = '#ccd6eb';
  gridLineColor = '#e6e6e6';
  crosshair = true;
  linkedTo: number;
  labels = {
    useHTML: true,
    formatter: function() {
      if (/\d{4}-\d{2}-\d{2}/.test(this.value)) {
        const day = new Date(this.value.replace(/-/g, '/')).getDay();
        if (day === 0 || day === 6) {
          return `<span class="text-primary font-semibold">${this.value}</span>`;
        }
      }
      
      return this.value;
    }
  }

  constructor({ categories, opposite }: Partial<xAxisItem>) {
    categories && (this.categories = categories);
    opposite && (this.opposite = opposite);
  }
}


export class MultipleXAxis extends BaseHighCharts {
  
  xAxis: XAxisOptions[] = [];
  yAxis: YAxisOptions[] = [
    {
      title: { text: '' },
      labels: { format: undefined, },
      gridLineWidth: 1,
      gridLineColor: '#e6e6e6',
    },
    {
      title: { text: '' },
      labels: { format: undefined, },
      gridLineWidth: 1,
      gridLineColor: '#e6e6e6',
      opposite: true
    }
  ];

  plotOptions = {
    series: {
      turboThreshold: 999999999,
      marker: {
        radius: 2,
        symbol: 'circle'
      }
    }
  }

  /**
   * Constructs a new instance of the LineMultipleXAxis class.
   * 
   * @param properties - The properties used to initialize the LineMultipleXAxis instance.
   * @throws {string} Throws an error if the x-axis is empty.
   */
  constructor(
    properties: QueryOutputVo,
    public dtType: DtType,
  ) {
    super();

    const { headers, data, compareData } = properties;
    const primary_xaxis = getCategories(headers, data);
    const secondary_xaxis = getCategories(headers, compareData);
    const primary_series = this.getSeries(headers, data, 0);
    const secondary_series = this.getSeries(headers, compareData, 1);
    const max_xaxis_length = getMaxLength(primary_xaxis, secondary_xaxis);
    
    if (isEmpty(primary_xaxis)) {
      throw 'x轴不能为空';
    }

    this.setCategories([
      fillEmpty(max_xaxis_length)(primary_xaxis), 
      fillEmpty(max_xaxis_length)(secondary_xaxis)
    ]);
    
    primary_series.forEach((item, index) => {
      primary_series[index].data.length = max_xaxis_length;
    })

    secondary_series.forEach((item, index) => {
      secondary_series[index].data.length = max_xaxis_length;
    })

    this.setSeries([...primary_series, ...secondary_series]);
    // this.colors.length = this.series.length / 2;
    this.colors.length = Math.ceil(this.series.length / 2);
    this.legend.verticalAlign = 'top';
    this.chart.type = 'spline';
  }
  

  getSeries(
    headers: { [key: string]: QueryOutputHeaderVo },
    data: { [key: string]: string }[],
    index?: number
  ) {
    const numberFields = getNumberFields(headers);
    const source = ['当前', '对比'];

    if (!data) {
      throw `${source[index] ?? ''}日期无数据`;
    }

    return numberFields.map(key => {
      const { aliasName, dataUnit } = headers[key];
      const series = new SeriesItem();

      if (index > 0) {
        series.dashStyle = 'Dash';
        series.lineWidth = 1;
        series.linkedTo = `${key}`;
      }
      
      if (dataUnit === '%') {
        series.isPercent = true;
      }

      series.xAxis = index;
      series.name = aliasName;
      series.dataUnit = dataUnit;
      series.data = data.map(item => {
        const value = Number(item[key]);

        if (item[key] === null) {
          return null;
        }

        if (dataUnit === '%') {
          return toDecimals(value, 1, 4);
        }

        return value;
      });

      return series;
    });
  }


  /**
   * Sets the categories for the multiple x-axis.
   * 
   * @param values - The array of string arrays representing the categories.
   */
  setCategories(values: string[][]): void {
    values.forEach((categories, index) => {
      const item = new xAxisItem({
        categories,
        opposite: index > 0,
      });

      if (index > 0) {
        item.linkedTo = 0;
      }

      this.xAxis.push(item);
    })
  }


  override getOption() {
    const value = this;

    return {
      ...value,
      tooltip: {
        ...value.tooltip,
        formatter: tooltipFormatter(this)
      },
      plotOptions: {
        ...value.plotOptions,
        series: {
          ...value.plotOptions.series,
          // events: {
          //   legendItemClick: handleLegendItemClick(this),
          // }
        }
      }
    };
  }
  
}
