<nz-spin [nzSpinning]="checking()">
  <div class="flex border border-neutral-200 divide-x divide-neutral-200">
    <div class="relative flex-1 min-w-0 flex flex-col gap-y-2 min-h-80 justify-start px-2 py-5">
      @if (dimension$ | async) {
        <app-custom-dimension [list]="dimensions() | sortBy: sortFn" (save)="updateDimension()" />
        @for (dimension of dimensions() | sortBy: sortFn; track dimension) {
          @if (dimension.display === 1) {
            @if (dimension.extendName === 'city_name') {
              <app-city-filter [data]="dimension" />
            } @else if (dimension.extendName === 'miage_intal' || dimension.extendName === 'inter_miage_intal' || dimension.extendName === 'inner_miage_intal') {
              @if (dimension.extendName === 'miage_intal') {
                <app-mileage-filter [list]="dimensions()" />
              }
            } @else if (dimension.key === 'c_seclev_channel' || dimension.key === 'out_c_seclev_channel' || dimension.key === 'own_c_seclev_channel') {
              @if (dimension.key === 'c_seclev_channel') {
                <app-seclev-channel-filter [list]="dimensions()" (updateSecievChannel)="updateSecievChannel($event)" />
              }
            } @else {
              <app-filter-item [data]="dimension" class="flex items-center">
                @if (dimension.extendName === 'odt_type') {
                  <a class="inline-flex items-center text-xs px-1.5 gap-x-0.5 h-5 underline underline-offset-2" href="https://confluence.didapinche.com/pages/viewpage.action?pageId=71006780" target="_blank">
                    口径说明
                    <OpenIcon class="scale-90 translate-y-px" />
                  </a>
                }
              </app-filter-item>
            }
          }
        }
      } @else {
        <div class="absolute inset-0 flex items-center justify-center bg-white/70">
          <app-line-spin class="block scale-75" />
        </div>
      }
    </div>

    <div class="flex-1 min-w-0 flex flex-col min-h-80 px-2 py-5">
      <fieldset class="relative flex-1 min-h-0 flex flex-col gap-1.5">
        @if (metricsLoading()) {
          <div class="absolute inset-0 flex items-center justify-center bg-white/70">
            <app-line-spin class="block scale-75" />
          </div>
        } @else {
          <legend class="flex! items-center gap-1">
            <label class="inline-flex items-start justify-end min-w-25 font-bold leading-5 whitespace-nowrap text-sm">选择指标：</label>
            <app-custom-metric [list]="metrics()" (save)="updateMetrics()" />
          </legend>

          @for (metric of metrics() | groupBy: groupFn; track $index) {
            <app-metric-item [data]="metric" />
          }

          <div class="flex justify-end gap-x-1.5 px-5 mt-auto">
            <button nz-button nzType="default" nzSize="small" (click)="handleAdd()" nz-tooltip="{{ disabled() ? '对比项超过15个，会影响查询和分析效率，建议去掉不需要的已选项之后再添加。' : '' }}" [disabled]="disabled()">添加为对比项</button>
            <button nz-button nzType="default" nzSize="small" (click)="restore()">重置</button>
          </div>

          <div class="text-right text-xs px-5 text-neutral-400 mt-2">
            说明：对比分析不同于趋势和构成分析，需要先将
            <span class="text-primary">已选择的维度和指标添加为对比项</span>
            ，才能进行查询。
          </div>
        }
      </fieldset>
    </div>
  </div>
</nz-spin>
