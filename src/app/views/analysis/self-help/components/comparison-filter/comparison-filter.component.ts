import { AsyncPipe } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, computed, DestroyRef, inject, input, output, signal } from '@angular/core';
import { debounceTime, finalize, map, shareReplay, tap, combineLatest, startWith, Observable } from 'rxjs';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzMessageService } from 'ng-zorro-antd/message';
import { NzSpinModule } from 'ng-zorro-antd/spin';

import { Before } from '@common/decorator';
import { CaerusApiService } from '@api/caerus';
import { QueryEngineApiService } from '@api/query-engine';
import { MetricVo } from '@api/query-engine/model';
import { DimensionMenuVo, MetricsMenuVo } from '@api/caerus/model';
import { QueryEngineFormService } from '@common/service/query-engine';
import { IconOpenComponent } from '@shared/modules/icons';
import { LineSpinComponent } from '@shared/components/line-spin';
import { GroupByPipe } from '@shared/pipes/group-by';
import { SortByPipe } from '@shared/pipes/sort-by'

import { MetricItemComponent } from '../metric-item/metric-item.component';
import { FilterItemComponent } from '../filter-item/filter-item.component';
import { CityFilterComponent, MileageFilterComponent, SeclevChannelFilterComponent } from '../filters';
import { CustomDimensionComponent } from '../custom-dimension/custom-dimension.component';
import { CustomMetricComponent } from '../custom-metric/custom-metric.component';

@Component({
  selector: 'app-comparison-filter',
  templateUrl: './comparison-filter.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    AsyncPipe,
    NzToolTipModule,
    NzButtonModule,
    NzSpinModule,
    CustomDimensionComponent,
    CustomMetricComponent,
    CityFilterComponent,
    MileageFilterComponent,
    SeclevChannelFilterComponent,
    FilterItemComponent,
    MetricItemComponent,
    IconOpenComponent,
    LineSpinComponent,
    GroupByPipe,
    SortByPipe,
  ],
  providers: [QueryEngineFormService],
})
export class ComparisonFilterComponent implements AfterViewInit {
  readonly cdr = inject(ChangeDetectorRef);
  readonly apiService = inject(QueryEngineApiService);
  readonly caerusApiService = inject(CaerusApiService);
  readonly formService = inject(QueryEngineFormService);
  readonly parentFormService = inject(QueryEngineFormService, {
    skipSelf: true,
  });
  readonly message = inject(NzMessageService);
  readonly destroyRef = inject(DestroyRef);

  count = input<number>(0);
  add = output<MetricVo[]>();
  checking = signal(false);
  dimensions = signal<any>([]);
  allDimensions = signal<any>([]);
  metrics = signal<MetricsMenuVo[]>([]);
  metrics$ = toObservable(this.metrics);
  metricsLoading = signal(true);

  disabled = computed(() => {
    const metricsCount = this.formService.form$()?.metrics?.length || 0;

    return this.count() + metricsCount > 15;
  });

  dimension$: Observable<DimensionMenuVo[]>;

  c_thilev_channel_map = {
    app: 'app_c_thilev_channel',
    微信小程序: 'minipg_wx_c_thilev_channel',
    支付宝小程序: 'minipg_ali_c_thilev_channel',
    腾讯: 'tencent_c_thilev_channel',
    钉钉: 'dingtalk_c_thilev_channel',
    百度地图: 'baidu_c_thilev_channel',
    第三方同程: 'tongcheng_c_thilev_channel',
    抖音: 'dy_minipg_c_thilev_channel',
    美团: 'meituan_c_thilev_channel',
  };

  ngAfterViewInit(): void {
    this.updateDimension();
    this.updateMetrics();
    this._subscribeToFormChange();
  }

  updateSecievChannel(value) {
    const arr = this.allDimensions().filter(d => d.aliasName !== '三级业务渠道');
    let c_thilev_channel = [];
    if (!value) {
      c_thilev_channel = this.allDimensions().filter(d => d.key === 'c_thilev_channel');
    } else {
      c_thilev_channel = this.allDimensions().filter(d => d.key === this.c_thilev_channel_map[value.key]);
    }
    this.dimensions.set(arr.concat(c_thilev_channel));
  }

  private _subscribeToFormChange() {
    combineLatest([this.formService.form.valueChanges.pipe(startWith(null)), this.parentFormService.form.valueChanges.pipe(startWith(null))])
      .pipe(
        // skip(11),
        tap(() => {
          setTimeout(() => {
            this.checking.set(true);
          }, 0);
        }),
        debounceTime(100),
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe(([a, b]) => {
        this.fetchSelectList();
      });
  }

  private fetchSelectList() {
    const body = this.formService.getSelectedList();

    body.dimensionEnNameList.pop();
    body.dimensionEnNameList = body.dimensionEnNameList.concat(this.parentFormService.value().dtType);

    this.apiService
      .fetchSelectList(body, 'select-list')
      .pipe(finalize(() => this.checking.set(false)))
      .subscribe(res => {
        if (!res.data) {
          return console.error(res);
        }

        this.formService.support.set(res.data);
      });
  }

  updateDimension() {
    this.dimension$ = this.caerusApiService.fetchDimension('menu-dimension').pipe(
      map(res => res.data),
      shareReplay(1)
    );
    this.caerusApiService.fetchDimension('menu-dimension').subscribe(res => {
      this.allDimensions.set(res.data);
      const arr = res.data.filter(d => d.aliasName !== '三级业务渠道');
      const c_thilev_channel = res.data.filter(d => d.key === 'c_thilev_channel');
      this.dimensions.set(arr.concat(c_thilev_channel));
    });

    this.formService.dimensions.clear();
    this.formService.filterItems.clear();
    this.cdr.markForCheck();
  }

  updateMetrics() {
    this.metricsLoading.set(true);
    this.caerusApiService
      .fetchMenuMetricsDataV2('menu-metrics')
      .pipe(finalize(() => this.metricsLoading.set(false)))
      .subscribe(res => {
        this.metrics.set(res.data);
      });

    this.formService.metrics.clear();
    this.cdr.detectChanges();
  }

  sortFn = (values: DimensionMenuVo[]) => {
    if (values) {
      return values.sort((a,b) => a.displayOrder - b.displayOrder);
    }
    return values;
  }

  groupFn = (values: MetricsMenuVo[]) => {
    if (values) {
      const map = new Map();
      const res = [];

      values.forEach(item => {
        if (item.display === 1) {
          if (map.has(item.tagName)) {
            map.get(item.tagName).push(item);
          } else {
            map.set(item.tagName, [item]);
          }
        }
      });

      map.forEach((value, key) => {
        res.push({ groupName: key, subMetric: value });
      });

      return res;
    }
    return values;
  };

  public formValid() {
    if (this.formService.metrics.length < 1) {
      this.message.warning('请先选择指标');
      return false;
    }

    return true;
  }

  @Before(ctx => ctx.formValid())
  handleAdd() {
    const { metrics, filter } = this.formService.value();
    const result = metrics.map(item => ({ ...item, filter }));

    this.add.emit(result);
  }

  restore() {
    this.formService.filterItems.clear();
    this.formService.metrics.clear();
  }
}
