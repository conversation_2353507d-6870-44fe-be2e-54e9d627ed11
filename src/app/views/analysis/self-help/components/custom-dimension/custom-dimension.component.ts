import { FormsModule } from '@angular/forms';
import { ConnectionPositionPair, OverlayModule } from '@angular/cdk/overlay';
import { AfterViewInit, ChangeDetectionStrategy, Component, computed, DestroyRef, effect, inject, input, output, signal } from '@angular/core';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { toObservable } from '@angular/core/rxjs-interop';
import { filter, take } from 'rxjs';
import * as _ from 'lodash';

import { POSITION_MAP, trace } from '@common/const';
import { BuriedPointService } from '@common/service';
import { BaseOverlayDirective } from '@common/directive';
import { IconCloseComponent, IconSearchComponent } from '@shared/modules/icons';
import { DimensionMenuVo } from '@api/caerus/model';
import { CaerusApiService } from '@api/caerus';
import { SearchPipe } from '@shared/pipes/search';
import { isNotEmpty } from '@common/function';
import { ToolsComponent } from '../../views/tools/tools.component';


@Component({
  selector: 'app-custom-dimension',
  template: `
    <div class="flex items-center gap-1">
      <label class="inline-flex items-start justify-end min-w-25 font-bold leading-5 whitespace-nowrap">选择维度：</label>
      <button cdkOverlayOrigin nz-button nzGhost nzType="primary" nzSize="small" (click)="toggle($event)">自选维度</button>
    </div>

    <ng-template
      #overlay="cdkConnectedOverlay"
      cdkConnectedOverlay
      [cdkConnectedOverlayHasBackdrop]="false"
      [cdkConnectedOverlayOrigin]="cdkOverlayOrigin"
      [cdkConnectedOverlayPositions]="listOfPositions"
      [cdkConnectedOverlayScrollStrategy]="scrollStrategy"
      [cdkConnectedOverlayOpen]="visible()"
      [cdkConnectedOverlayOffsetX]="5"
      (positionChange)="onPositionChange($event)"
      (overlayOutsideClick)="handleOutside($event)"
      (detach)="close()"
    >
      <div class="flex flex-col gap-y-2 w-72 p-3 bg-white shadow-1 border border-neutral-200 rounded-sm">
        <div class="flex items-center gap-x-1">
          <button nz-button nzType="primary" nzSize="small" (click)="handleSave()">保存</button>
          <span class="flex-1 min-w-0">已选中{{count()}}项</span>
          <CloseIcon iconBtn (click)="close()" />
        </div>
        <nz-input-group nzSearch [nzPrefix]="suffixIconSearch">
          <ng-template #suffixIconSearch>
            <SearchIcon />
          </ng-template>
          <input type="text" nz-input [(ngModel)]="keyword" placeholder="搜索维度名称" />
        </nz-input-group>
        <div class="flex flex-col h-60 overflow-auto">
          <label
            nz-checkbox
            [(ngModel)]="allChecked"
            (ngModelChange)="updateAllChecked()"
            [nzIndeterminate]="indeterminate()"
          >
            全选
          </label>
          @for (item of (items() | search: keyword(): 'aliasName'); track $index) {
            <label class="ml-0!" nz-checkbox [(ngModel)]="item.checked" (ngModelChange)="handleStateChange(item.key, $event)">{{item.aliasName}}</label>
          }
        </div>
      </div>
    </ng-template>
  `,
  styles: `
    :host::ng-deep .ant-checkbox, .ant-checkbox-wrapper {
      font-size: 12px;
    }
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    FormsModule,
    OverlayModule,
    NzButtonModule,
    NzCheckboxModule,
    NzInputModule,
    IconSearchComponent,
    IconCloseComponent,
    SearchPipe,
  ],
})
export class CustomDimensionComponent extends BaseOverlayDirective implements AfterViewInit {

  readonly root = inject(ToolsComponent);
  readonly destroyRef = inject(DestroyRef);
  readonly buriedPointService = inject(BuriedPointService);
  readonly apiService = inject(CaerusApiService);
  
  list = input.required<DimensionMenuVo[]>();
  save = output<void>();

  data = signal<DimensionMenuVo[]>([]);
  data$ = toObservable(this.data);
  keyword = signal('');
  allChecked = signal(false);
  indeterminate = computed(() => {
    if (
      this.items().every(item => item.checked === true) ||
      this.items().every(item => item.checked === false)
    ) {
      return false;
    }
    return true;
  });

  count = computed(() => {
    const checkeds = this.items().filter(item => item.display === 1);
    return checkeds.length;
  })

  items = computed(() => {
    return this.data()
      .map(item => {
        return {
          ...item,
          checked: item.display === 1
        };
      })
      .sort((a, b) => {
        return a.displayOrder - b.displayOrder
      })
  })

  override listOfPositions: ConnectionPositionPair[] = [
    POSITION_MAP.rightTop
  ];


  constructor() {
    super();
    this.scrollStrategy = this.scrollStrategyOptions.reposition();

    effect(() => {
      if (this.list()) {
        const keys = [
          'inter_miage_intal', 'inner_miage_intal', 'out_c_seclev_channel', 'own_c_seclev_channel', 
          // 'yse_is_site_pkg_book', 'no_is_site_pkg_book'
        ];
        const list = _.cloneDeep(this.list()).filter(item => !(keys.includes(item.key)));
        this.data.set(list);
      }
    })
  }


  ngAfterViewInit(): void {
    this.data$.pipe(
      filter(isNotEmpty),
      take(1)
    ).subscribe((items: any[]) => {
      const state = items.every(item => item.display === 1);
      this.allChecked.set(state);
    })
  }


  handleStateChange(key: string, state: boolean) {    
    this.data.update(items => items.map(item => {
      if (item.key === key) {
        item.display = Number(state);
      }
      return item;
    }))
  }

  
  handleSave() {
    trace(`埋点上报：自选维度保存按钮点击`, {
      page_name: this.root.page_name,
      graph_tab: this.root.sourceType(),
      set_options: 1,
    });

    this.buriedPointService.addStat('dida_dpm_caerus_Selfhelp_set_option', {
      page_name: this.root.page_name,
      graph_tab: this.root.sourceType(),
      set_options: 1,
    });
    
    this.apiService.patchCustomDimension(this.data()).subscribe(res => {
      this.save.emit();
    })
  }

  
  updateAllChecked() {
    this.data.update(items => items.map(item => {
      item.display = Number(this.allChecked());
      return item;
    }))
  }

}
