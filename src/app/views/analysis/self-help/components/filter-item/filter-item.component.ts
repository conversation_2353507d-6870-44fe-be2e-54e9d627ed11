import { FormGroup, FormsModule } from '@angular/forms'
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
  computed,
  inject,
  input,
  signal,
} from '@angular/core'
import { takeUntilDestroyed } from '@angular/core/rxjs-interop'
import { NzToolTipModule } from 'ng-zorro-antd/tooltip'
import { debounceTime, filter, startWith, take } from 'rxjs'
import * as _ from 'lodash'

import { DimensionMenuVo, DimensionValueMenuVo } from '@api/caerus/model'
import { QueryEngineFormService, FilterItemForm, DimensionValueForm } from '@common/service/query-engine'
import { RadioModule } from '@shared/modules/headless'

@Component({
  selector: 'app-filter-item',
  template: `
    @if (data()) {
      <app-radio-group class="flex gap-1" [(ngModel)]="value" (ngModelChange)="valueChange($event)">
        <label
          nz-tooltip="{{ data()?.bizExpression }}"
          [nzTooltipMouseEnterDelay]="1"
          class="inline-flex items-start justify-end min-w-25 font-bold leading-5 whitespace-nowrap"
        >
          {{ data().aliasName }}：
        </label>
        <div class="flex gap-1 flex-1 flex-wrap items-start">
          <app-radio class="tag-radio" activeClass="active" [disabled]="disabled()" [value]="null">全部</app-radio>
          @for (item of data().valueList; track item) {
            <app-radio class="tag-radio" activeClass="active" [disabled]="disabled()" [value]="item">
              {{ item.value }}
            </app-radio>
          }
          <ng-content />
        </div>
      </app-radio-group>
    }
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [FormsModule, RadioModule, NzToolTipModule],
})
export class FilterItemComponent implements AfterViewInit {
  readonly destroyRef = inject(DestroyRef)
  readonly formService = inject(QueryEngineFormService)

  value = signal<DimensionValueMenuVo>(null)
  data = input<DimensionMenuVo>(null)
  form = new FormGroup(new FilterItemForm())

  disabled = computed(() => {
    const { dimensions } = this.formService.support() ?? {}

    if (dimensions) {
      const exist = dimensions.some(item => item.extendName === this.data().extendName)

      return !exist
    }

    return false
  })

  ngAfterViewInit(): void {
    this._subscribeToFormChange()
  }

  private _subscribeToFormChange() {
    this.formService.filterItems.valueChanges
      .pipe(
        startWith(this.formService.filterItems.value),
        debounceTime(100),
        filter(values => values.length > 0),
        take(1)
      )
      .subscribe(filterItems => {
        filterItems.forEach(filterItem => {
          const { extendName, value } = filterItem

          if (extendName === this.data().extendName) {
            const val = this.data().valueList.find(item => {
              return _.isEqual({ key: item.key, value: item.value }, value.at(0) || {})
            })
            const index = this.formService.filterItems.controls.findIndex(control => {
              return _.isEqual(control.value, filterItem)
            })
            this.formService.filterItems.removeAt(index)
            if (val) {
              this.value.set(val)
              this.valueChange()
            }
          }
        })
      })

    this.formService.filterItems.valueChanges.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(() => {
      if (!this._hasControl()) {
        this.value.set(null)
      }
    })
  }

  valueChange(value: DimensionValueMenuVo = this.value()) {
    if (value) {
      const { extendName } = this.data()

      if (this._hasControl() === false) {
        this.formService.filterItems.push(this.form)
      }

      this.form.get('extendName').patchValue(extendName)
      this._setValue()
    } else {
      this.formService.filterItems.removeAt(this._index())
    }
  }

  private _setValue() {
    const { key, value, extendName } = this.value()
    const parentControl = this.form.get('value').parent
    const valueControl = this.form.controls.value
    const control = new FormGroup(new DimensionValueForm({ key, value }))

    parentControl.get('extendName').patchValue(extendName)
    valueControl.clear()
    valueControl.push(control)
  }

  private _index() {
    return this.formService.filterItems.controls.findIndex(control => control === this.form)
  }

  private _hasControl() {
    return this.formService.filterItems.controls.some(control => control === this.form)
  }
}
