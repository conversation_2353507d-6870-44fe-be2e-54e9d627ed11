import { ChangeDetectionStrategy, Component, computed, inject, input } from '@angular/core';
import { MetricVo } from '@api/query-engine/model';
import { QueryEngineFormService } from '@common/service/query-engine';
import { IconCloseFillComponent } from '@shared/modules/icons';

@Component({
  selector: 'app-comparison-filter-item',
  template: `
    <div class="axis-item">
      {{label()}}
      <CloseFillIcon iconBtn class="sm" (click)="remove()" />
    </div>
  `,
  styleUrl: './comparison-filter-item.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    IconCloseFillComponent,
  ],
})
export class ComparisonFilterItemComponent {

  readonly formService = inject(QueryEngineFormService);

  index = input.required<number>();
  metric = input.required<Partial<MetricVo>>();
  id = computed(() => {
    const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'.split('');
    return letters[this.index()];
  })

  label = computed(() => {
    const { aliasName, filter } = this.metric();
    const values = filter.items.map(items => {
      return items.value.map(item => {
        return item.value;
      })
    })

    const labels = [aliasName, ...values].flat(1).join('-');
    return `${this.id()}:${labels}`;
  })


  remove() {
    this.formService.metrics.removeAt(this.index());
  }

}
