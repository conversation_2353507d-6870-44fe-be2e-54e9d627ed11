import { OverlayModule } from '@angular/cdk/overlay'
import { AfterViewInit, ChangeDetectionStrategy, Component, DestroyRef, inject } from '@angular/core'
import { ReactiveFormsModule } from '@angular/forms'
import { NzButtonModule } from 'ng-zorro-antd/button'
import { NzRadioModule } from 'ng-zorro-antd/radio'

import { MetricVo } from '@api/query-engine/model'
import { NewBaseOverlayDirective } from '@common/directive'
import { QueryEngineFormService } from '@common/service/query-engine'
import { IconSettingComponent } from '@shared/modules/icons'
import { ToolsComponent } from '../../views/tools/tools.component'
import { BuriedPointService } from '@common/service'

@Component({
  selector: 'app-axes-control',
  templateUrl: './axes-control.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [OverlayModule, ReactiveFormsModule, NzButtonModule, NzRadioModule, IconSettingComponent],
})
export class AxesControlComponent extends NewBaseOverlayDirective implements AfterViewInit {
  readonly destroyRef = inject(DestroyRef)
  readonly formService = inject(QueryEngineFormService)
  readonly parent = inject(ToolsComponent)
  readonly buriedPointService = inject(BuriedPointService)

  override scrollStrategy = this.scrollStrategyOptions.reposition()

  ngAfterViewInit(): void {
    if (this.parent.isCreate()) {
      this._autoSetYAxis()
    }
    this._subscribeToOptionChange()
  }

  private _subscribeToOptionChange() {
    this.parent.query$.subscribe(() => {
      this._autoSetYAxis()
    })
  }

  private _autoSetYAxis() {
    const metrics =
      this.parent.sourceType() === 'constitute'
        ? []
        : this.parent.sourceType() === 'trend'
          ? this.parent.commonFilterRef().metrics()
          : this.parent.comparisonFilterRef().metrics()

    this.formService.metrics.controls.forEach(control => {
      const { aliasName } = control.value
      const metric = metrics.find(item => item.aliasName === aliasName)
      const isAllPercent = metrics
        .filter(item => control.parent.value.some(m => m.aliasName === item.aliasName))
        .every(item => item.dataUnit === '%')

      const value = isAllPercent ? 0 : metric.dataUnit === '%' ? 1 : 0
      control.controls.yAxis.patchValue(value)
    })
  }

  changeRadio(Axes_set, indicator_name) {
    this.buriedPointService.addStat('dida_dpm_caerus_Selfhelp_Axes_set', {
      page_name: this.parent.page_name,
      indicator_name,
      Axes_set,
    })
  }

  hasMetric(item: MetricVo) {
    const { series } = this.parent.option()

    return series.some(({ name }) => item.aliasName === name)
  }
}
