<button cdkOverlayOrigin nz-button nzType="default" (click)="toggle($event)">
  <SettingIcon />
  <span class="ml-1">设置坐标</span>
</button>

<ng-template
  #overlay="cdkConnectedOverlay"
  cdkConnectedOverlay
  [cdkConnectedOverlayHasBackdrop]="false"
  [cdkConnectedOverlayOrigin]="cdkOverlayOrigin()"
  [cdkConnectedOverlayPositions]="listOfPositions"
  [cdkConnectedOverlayScrollStrategy]="scrollStrategy"
  [cdkConnectedOverlayOpen]="visible()"
  (positionChange)="onPositionChange($event)"
  (overlayOutsideClick)="handleOutside($event)"
  (detach)="close()"
>
  <div class="py-1">
    <div class="w-96 min-h-64 p-3 bg-white shadow-1 rounded-sm border border-neutral-200">
      <div [formGroup]="formService.form">
        <div formArrayName="metrics">
          <table class="w-full text-xs">
            <thead class="h-5">
              <tr>
                <th class="text-left">指标</th>
                <th width="150">坐标轴</th>
              </tr>
            </thead>
            <tbody>
              @for (item of formService.metrics.controls; track $index; let i = $index) {
                <tr [formGroupName]="i">
                  @if (hasMetric(item.value)) {
                    <td>{{ item.controls.aliasName.value }}</td>
                    <td align="center">
                      <nz-radio-group
                        formControlName="yAxis"
                        (ngModelChange)="changeRadio($event, item.controls.aliasName.value)"
                      >
                        <label nz-radio class="text-xs!" [nzValue]="0">主轴</label>
                        <label nz-radio class="text-xs!" [nzValue]="1">次轴</label>
                      </nz-radio-group>
                    </td>
                  }
                </tr>
              }
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>
</ng-template>
