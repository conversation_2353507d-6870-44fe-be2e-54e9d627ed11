import { FormsModule } from '@angular/forms';
import { ConnectionPositionPair, OverlayModule } from '@angular/cdk/overlay';
import { ChangeDetectionStrategy, Component, computed, effect, inject, input, output, signal } from '@angular/core';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NzTreeModule, NzTreeNodeOptions } from 'ng-zorro-antd/tree';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzTagModule } from 'ng-zorro-antd/tag';
import * as _ from 'lodash';

import { POSITION_MAP, trace } from '@common/const';
import { BuriedPointService } from '@common/service';
import { CaerusApiService } from '@api/caerus';
import { MetricsMenuVo } from '@api/caerus/model';
import { BaseOverlayDirective } from '@common/directive';
import { groupBy } from '@common/function';
import { IconCloseComponent, IconSearchComponent } from '@shared/modules/icons';
import { HighlightPipe } from '@shared/pipes/highlight';
import { ToolsComponent } from '../../views/tools/tools.component';


@Component({
  selector: 'app-custom-metric',
  template: `
    <div class="flex items-center gap-1">
      <button cdkOverlayOrigin nz-button nzGhost nzType="primary" nzSize="small" (click)="toggle($event)">自选指标</button>
      <button nz-button nzType="link" nzSize="small" (click)="handleRestore()">恢复初始设置</button>
    </div>

    <ng-template
      #overlay="cdkConnectedOverlay"
      cdkConnectedOverlay
      [cdkConnectedOverlayHasBackdrop]="false"
      [cdkConnectedOverlayOrigin]="cdkOverlayOrigin"
      [cdkConnectedOverlayPositions]="listOfPositions"
      [cdkConnectedOverlayScrollStrategy]="scrollStrategy"
      [cdkConnectedOverlayOpen]="visible()"
      [cdkConnectedOverlayOffsetX]="5"
      (positionChange)="onPositionChange($event)"
      (overlayOutsideClick)="handleOutside($event)"
      (detach)="close()"
    >
      <div class="flex flex-col gap-y-2 w-80 p-3 bg-white shadow-1 border border-neutral-200 rounded-sm">
        <div class="flex items-center gap-x-1">
          <button nz-button nzType="primary" nzSize="small" (click)="handleSave()">保存</button>
          <span>已选中{{count()}}项</span>
          <span class="flex-1 min-w-0"></span>
          <CloseIcon iconBtn (click)="close()" />
        </div>
        <nz-input-group nzSearch [nzPrefix]="suffixIconSearch">
          <ng-template #suffixIconSearch>
            <SearchIcon />
          </ng-template>
          <input type="text" [(ngModel)]="searchValue" nz-input placeholder="搜索指标名称" />
        </nz-input-group>
        <div class="flex flex-col gap-y-0.5 h-125 overflow-auto">
          <nz-tree
            nzCheckable
            nzHideUnMatched
            [nzData]="nodes()"
            [nzSearchValue]="searchValue()"
            [nzCheckedKeys]="defaultCheckedKeys()"
            [nzTreeTemplate]="nzTreeTemplate"
            (nzCheckboxChange)="nzCheck()"
          ></nz-tree>

          <ng-template #nzTreeTemplate let-node let-origin="origin">
            <span nz-tooltip="{{origin.data}}" [innerHTML]="node.title | highlight: searchValue()"></span>
          </ng-template>
        </div>
      </div>
    </ng-template>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    FormsModule,
    OverlayModule,
    NzToolTipModule,
    NzTreeModule,
    NzTagModule,
    NzButtonModule,
    NzCheckboxModule,
    NzInputModule,
    HighlightPipe,
    IconSearchComponent,
    IconCloseComponent,
  ],
})
export class CustomMetricComponent extends BaseOverlayDirective {

  readonly root = inject(ToolsComponent);
  readonly buriedPointService = inject(BuriedPointService);
  readonly apiService = inject(CaerusApiService);
  
  list = input.required<MetricsMenuVo[]>();
  save = output<void>();

  data = signal<MetricsMenuVo[]>([]);
  nodes = signal<NzTreeNodeOptions[]>([]);
  defaultCheckedKeys = signal<string[]>([]);
  searchValue = signal<string>(null);
  defaultMetrics = computed(() => {
    return this.data().map(item => {
      item.display = item.recommend;
      return item;
    })
  })


  count = computed(() => {
    const checkeds = this.data()
    .filter(item => item.display === 1);
    return checkeds.length;
  })

  override listOfPositions: ConnectionPositionPair[] = [
    POSITION_MAP.rightTop
  ];

  constructor() {
    super();
    this.scrollStrategy = this.scrollStrategyOptions.reposition();

    effect(() => {
      if (this.list()) {
        const list = _.cloneDeep(this.list()) as MetricsMenuVo[];
        const selectedKeys = list.filter(item => item.display === 1).map(item => item.extendName);

        this.data.set(list);
        this.nodes.set([]);
        this.defaultCheckedKeys.set(selectedKeys);
        this._transformToTreeNodes();
      }
    })
  }


  override open() {
    this.visible.set(true);

    setTimeout(() => {
      const dom = this.overlayConnect.overlayRef.hostElement?.parentElement?.classList;
      
      dom?.add('z-999!');
      dom?.remove('z-50!');
    })
  }


  private _transformToTreeNodes() {
    const group = groupBy<MetricsMenuVo>(this.list(), 'tagName');

    Object.keys(group).forEach(key => {
      const children = group[key].map(item => {
        return {
          title: item.aliasName,
          key: item.extendName,
          data: item.bizExpression,
          isLeaf: true,
        }
      })
      this.nodes.update(value => {
        value.push({
          title: key,
          key,
          children
        });
        return value;
      })
    })
  }


  handleStateChange(key: string, state: boolean) {    
    this.data.update(items => items.map(item => {
      if (item.extendName === key) {
        item.display = Number(state);
      }
      return item;
    }))
  }


  nzCheck(): void {
    const checkedNodes = this.nodes()
      .map(node => {
        if (node.checked) {
          return node.children;
        } else {
          return node.children.filter(item => item.checked)
        }
      })
      .flat(1)
      .map(item => item.key);

    this.data.update(values => {
      values = values.map(item => {
        item.display = Number(checkedNodes.includes(item.extendName));
        return item;
      })
      return values;
    })
  }

  
  handleSave() {
    trace(`埋点上报：自选指标保存按钮点击`, {
      page_name: this.root.page_name,
      graph_tab: this.root.sourceType(),
      set_options: 2,
    });

    this.buriedPointService.addStat('dida_dpm_caerus_Selfhelp_set_option', {
      page_name: this.root.page_name,
      graph_tab: this.root.sourceType(),
      set_options: 2,
    });

    this.apiService.patchCustomMetrics(this.data()).subscribe(() => {
      this.save.emit();
    })
  }


  async handleRestore() {
    trace(`埋点上报：恢复初始设置按钮点击`, {
      page_name: this.root.page_name,
      graph_tab: this.root.sourceType(),
      set_options: 3,
    });

    this.buriedPointService.addStat('dida_dpm_caerus_Selfhelp_set_option', {
      page_name: this.root.page_name,
      graph_tab: this.root.sourceType(),
      set_options: 3,
    });
    
    this.apiService.patchCustomMetrics(this.defaultMetrics()).subscribe(() => {
      this.save.emit();
    })
  }

}
