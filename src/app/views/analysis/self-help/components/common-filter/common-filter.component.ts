import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  DestroyRef,
  inject,
  signal,
} from '@angular/core'
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop'
import { debounceTime, finalize, take } from 'rxjs'
import { NzButtonModule } from 'ng-zorro-antd/button'
import { find } from 'lodash'

import { CaerusApiService } from '@api/caerus'
import { DimensionMenuVo, MetricsMenuVo } from '@api/caerus/model'
import { QueryEngineFormService } from '@common/service/query-engine'
import { LineSpinComponent } from '@shared/components/line-spin'
import { IconOpenComponent } from '@shared/modules/icons'
import { GroupByPipe } from '@shared/pipes/group-by'
import { SortByPipe } from '@shared/pipes/sort-by'

import { MetricItemComponent } from '../metric-item/metric-item.component'
import { FilterItemComponent } from '../filter-item/filter-item.component'
import { CityFilterComponent, MileageFilterComponent, SeclevChannelFilterComponent } from '../filters'
import { CustomDimensionComponent } from '../custom-dimension/custom-dimension.component'
import { CustomMetricComponent } from '../custom-metric/custom-metric.component'

@Component({
  selector: 'app-common-filter',
  templateUrl: './common-filter.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NzButtonModule,
    CustomDimensionComponent,
    CustomMetricComponent,
    CityFilterComponent,
    MileageFilterComponent,
    SeclevChannelFilterComponent,
    FilterItemComponent,
    MetricItemComponent,
    IconOpenComponent,
    LineSpinComponent,
    GroupByPipe,
    SortByPipe,
  ],
})
export class CommonFilterComponent implements AfterViewInit {
  readonly caerusApiService = inject(CaerusApiService)
  readonly formService = inject(QueryEngineFormService)
  readonly cdr = inject(ChangeDetectorRef)
  readonly destroyRef = inject(DestroyRef)

  dimensions = signal<DimensionMenuVo[]>([])
  allDimensions = signal<DimensionMenuVo[]>([])
  metrics = signal<MetricsMenuVo[]>([])
  metrics$ = toObservable(this.metrics)
  dimensionsLoading = signal(true)
  metricsLoading = signal(true)
  c_seclev_channel = signal(null)

  c_thilev_channel_map = {
    app: 'app_c_thilev_channel',
    微信小程序: 'minipg_wx_c_thilev_channel',
    支付宝小程序: 'minipg_ali_c_thilev_channel',
    腾讯: 'tencent_c_thilev_channel',
    钉钉: 'dingtalk_c_thilev_channel',
    百度地图: 'baidu_c_thilev_channel',
    第三方同程: 'tongcheng_c_thilev_channel',
    抖音: 'dy_minipg_c_thilev_channel',
    美团: 'meituan_c_thilev_channel',
  }

  cThilevChannel = [
    {
      key: 'minipg_ali_c_thilev_channel',
      extendName: 'c_thilev_channel',
      aliasName: '三级业务渠道',
      type: null,
      display: 1,
      displayOrder: 24,
      constitute: 0,
      bizExpression: '对应乘客的具体下单产品，对应流量域的具体活跃三级产品',
      valueList: [],
    },
  ]

  tBookChannelName = ['腾讯出行', '百度', '电召平台']

  ngAfterViewInit(): void {
    this._subscribeToFormChange()
    this.updateDimension()
    this.updateMetrics()
  }

  updateSecievChannel(value) {
    // console.log('updateSecievChannel', value)
    this.c_seclev_channel.set(value)
    const arr = this.allDimensions().filter(d => d.aliasName !== '三级业务渠道')
    let c_thilev_channel: any = this.cThilevChannel
    let key = 'c_thilev_channel'
    if (value) {
      key = this.c_thilev_channel_map[value.key]
    }
    let filterDimension = this.allDimensions().filter(d => {
      return d.key === key
    })
    if (filterDimension.length !== 0) {
      c_thilev_channel = filterDimension
    }
    this.dimensions.set(arr.concat(c_thilev_channel))
  }

  private processMediaType(mediaType: any, sourceType: any, naturalType: string) {
    if (sourceType && sourceType.value[0].value === naturalType) {
      return {
        ...mediaType,
        valueList: [],
      }
    }
    return mediaType
  }

  private _subscribeToFormChange() {
    this.formService.metrics.valueChanges.pipe(debounceTime(100), take(1)).subscribe(value => {
      this.metrics.update(items => {
        return items.map(item => {
          const exist = value.some(({ extendName }) => extendName === item.extendName)
          if (exist) {
            item.display = Number(exist)
          }
          return item
        })
      })
    })
    this.formService.filterItems.valueChanges
      .pipe(debounceTime(100), takeUntilDestroyed(this.destroyRef))
      .subscribe(value => {
        const findByExtendName = (extendName: string) => find(value, ['extendName', extendName])
        const findDimensionByKey = (key: string) => this.allDimensions().filter(d => d.key === key)
        // console.log('value==', value)
        const item = findByExtendName('c_firlev_channel')
        const _item = findByExtendName('t_book_channel_type')
        const channelKey = this.c_seclev_channel()
          ? this.c_thilev_channel_map[this.c_seclev_channel().key]
          : 'c_thilev_channel'
        let c_thilev_channel: any[] | null = null
        const _filterDimension = findDimensionByKey('t_book_channel_name')
        let t_book_channel_name = _filterDimension
        // console.log('item', item)
        if (_item && _item.value[0].value === '自有渠道') {
          t_book_channel_name = [
            {
              ..._filterDimension[0],
              valueList: _filterDimension[0].valueList.filter(v => {
                return !this.tBookChannelName.includes(v.key)
              }),
            },
          ]
        }
        if (_item && _item.value[0].value === '外输渠道') {
          t_book_channel_name = [
            {
              ..._filterDimension[0],
              valueList: _filterDimension[0].valueList.filter(v => {
                return this.tBookChannelName.includes(v.key)
              }),
            },
          ]
        }
        if ((item && item.value[0].value === '自有渠道') || !item) {
          const filterDimension = findDimensionByKey(channelKey)
          if (filterDimension.length !== 0) {
            c_thilev_channel = filterDimension
          } else {
            c_thilev_channel = Array.isArray(this.cThilevChannel)
              ? this.cThilevChannel
              : this.cThilevChannel
                ? [this.cThilevChannel]
                : []
          }
        } else {
          c_thilev_channel = Array.isArray(this.cThilevChannel)
            ? this.cThilevChannel
            : this.cThilevChannel
              ? [this.cThilevChannel]
              : []
        }
        const pass_promt_channel_source_type = findByExtendName('pass_promt_channel_source_type')
        const driver_promt_channel_source_type = findByExtendName('driver_promt_channel_source_type')

        const updatedBaseDimensions = this.allDimensions()
          .filter(d => d.aliasName !== '三级业务渠道')
          .filter(d => d.extendName !== 't_book_channel_name')
          .map(dimension => {
            if (dimension.extendName === 'pass_media_type') {
              return this.processMediaType(dimension, pass_promt_channel_source_type, '自然乘客')
            }
            if (dimension.extendName === 'driver_media_type') {
              return this.processMediaType(dimension, driver_promt_channel_source_type, '自然车主')
            }
            return dimension
          })
        const finalDimensions = [...updatedBaseDimensions, ...(c_thilev_channel || []), ...t_book_channel_name]

        // console.log('dimesions', finalDimensions)
        this.dimensions.set(finalDimensions)
      })

    this.formService.filterItems.valueChanges.pipe(debounceTime(100), take(1)).subscribe(value => {
      this.dimensions.update(items => {
        return items.map(item => {
          const exist = value.some(({ extendName }) => {
            return (
              extendName === item.extendName ||
              (['city_bio_region', 'is_top20_city', 'city_name', 'province_name'].includes(extendName) &&
                ['city_bio_region', 'is_top20_city', 'city_name', 'province_name'].includes(item.extendName))
            )
          })
          if (exist) {
            item.display = Number(exist)
          }
          return item
        })
      })
    })
  }

  updateDimension() {
    this.dimensionsLoading.set(true)
    this.caerusApiService
      .fetchDimension('menu-dimension')
      .pipe(finalize(() => this.dimensionsLoading.set(false)))
      .subscribe(res => {
        // console.log('allDimensions', res.data)
        this.allDimensions.set(res.data)
        const arr = res.data.filter(d => d.aliasName !== '三级业务渠道')
        const c_thilev_channel = res.data.filter(d => d.key === 'c_thilev_channel')
        this.dimensions.set(arr.concat(c_thilev_channel))
      })

    this.formService.dimensions.clear()
    this.formService.filterItems.clear()
    this.cdr.detectChanges()
  }

  updateMetrics() {
    this.metricsLoading.set(true)
    this.caerusApiService
      .fetchMenuMetricsDataV2('menu-metrics')
      .pipe(finalize(() => this.metricsLoading.set(false)))
      .subscribe(res => {
        this.metrics.set(res.data)
      })

    this.formService.metrics.clear()
    this.cdr.detectChanges()
  }

  sortFn = (values: DimensionMenuVo[]) => {
    if (values) {
      return values.sort((a, b) => a.displayOrder - b.displayOrder)
    }
    return values
  }

  groupFn = (values: MetricsMenuVo[]) => {
    if (values) {
      const map = new Map()
      const res = []

      values.forEach(item => {
        if (item.display === 1) {
          if (map.has(item.tagName)) {
            map.get(item.tagName).push(item)
          } else {
            map.set(item.tagName, [item])
          }
        }
      })

      map.forEach((value, key) => {
        res.push({ groupName: key, subMetric: value })
      })

      return res
    }
    return values
  }
}
