<div class="flex border border-neutral-200 divide-x divide-neutral-200">
  <div class="relative flex-1 min-w-0 min-h-80 justify-around px-2 py-5">
    <div class="flex-1 min-h-0 flex flex-col h-full gap-y-2 justify-start">
      @if (!dimensionsLoading()) {
        <app-custom-dimension [list]="dimensions() | sortBy: sortFn" (save)="updateDimension()" />
        <div class="flex-1 min-h-0 flex flex-col gap-y-2 justify-start">
          @for (dimension of dimensions() | sortBy: sortFn; track dimension) {
            @if (dimension.display === 1) {
              @if (dimension.key === 'city_name') {
                <app-city-filter [data]="dimension" />
              } @else if (
                dimension.key === 'miage_intal' ||
                dimension.key === 'inter_miage_intal' ||
                dimension.key === 'inner_miage_intal'
              ) {
                @if (dimension.key === 'miage_intal') {
                  <app-mileage-filter [list]="dimensions()" />
                }
              } @else if (
                dimension.key === 'c_seclev_channel' ||
                dimension.key === 'out_c_seclev_channel' ||
                dimension.key === 'own_c_seclev_channel'
              ) {
                @if (dimension.key === 'c_seclev_channel') {
                  <app-seclev-channel-filter
                    [list]="dimensions()"
                    (updateSecievChannel)="updateSecievChannel($event)"
                  />
                }
              } @else {
                <app-filter-item [data]="dimension" class="flex items-center">
                  @if (dimension.key === 'odt_type') {
                    <a
                      class="inline-flex items-center text-xs px-1.5 gap-x-0.5 h-5 underline underline-offset-2"
                      href="https://confluence.didapinche.com/pages/viewpage.action?pageId=71006780"
                      target="_blank"
                    >
                      口径说明
                      <OpenIcon class="scale-90 translate-y-px" />
                    </a>
                  }
                </app-filter-item>
              }
            }
          }
        </div>
      } @else {
        <div class="absolute inset-0 flex items-center justify-center bg-white/70">
          <app-line-spin class="block scale-75" />
        </div>
      }
    </div>
  </div>

  <div class="flex-1 min-w-0 flex flex-col min-h-80 px-2 py-5">
    <fieldset class="relative flex-1 min-h-0 flex flex-col gap-y-2">
      @if (!metricsLoading()) {
        <legend class="flex! items-center gap-1">
          <label class="inline-flex items-start justify-end min-w-25 font-bold leading-5 whitespace-nowrap text-sm">
            选择指标：
          </label>
          <app-custom-metric [list]="metrics()" (save)="updateMetrics()" />
        </legend>

        @for (metric of metrics() | groupBy: groupFn; track $index) {
          <app-metric-item [data]="metric" />
        }

        <div class="flex justify-end gap-x-1.5 px-5 mt-3">
          <ng-content />
        </div>
      } @else {
        <div class="absolute inset-0 flex items-center justify-center bg-white/70">
          <app-line-spin class="block scale-75" />
        </div>
      }
    </fieldset>
  </div>
</div>
