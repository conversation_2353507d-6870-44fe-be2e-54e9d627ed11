import { FormGroup, FormsModule } from '@angular/forms'
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  DestroyRef,
  inject,
  model,
} from '@angular/core'
import { rxResource, takeUntilDestroyed } from '@angular/core/rxjs-interop'
import { NzToolTipModule } from 'ng-zorro-antd/tooltip'
import { debounceTime, map, startWith } from 'rxjs'

import { Debounce } from '@common/decorator'
import { MetricsMenuVo } from '@api/caerus/model'
import { MetricVoForm, QueryEngineFormService } from '@common/service/query-engine'
import { CheckboxModule } from '@shared/modules/headless'
import { IconInfoComponent } from '@shared/modules/icons'
import { isNotEmpty } from '@common/function'
import { CaerusApiService } from '@api/caerus'


@Component({
  selector: 'app-metric-item',
  template: `
    @if (data()) {
      <div class="flex gap-1">
        <label class="inline-flex items-start justify-end min-w-25 font-bold leading-5 whitespace-nowrap">
          {{ data().groupName }}
          @if (metricsTagMapResource.value()?.has(data().groupName)) {
            <InfoIcon class="text-primary mx-0.5 translate-y-[3px] cursor-pointer" nz-tooltip="{{metricsTagMapResource.value().get(data().groupName)}}" />
          }
          ：
        </label>
        <div class="flex gap-1 flex-wrap items-start">
          @for (item of data().subMetric; track $index) {
            <app-checkbox
              nz-tooltip
              [nzTooltipTitle]="item.bizExpression"
              [nzTooltipMouseEnterDelay]="1"
              [disabled]="getState(item)"
              [(ngModel)]="item.checked"
              (ngModelChange)="onModelChange(item)"
            >
              {{ item.aliasName }}
            </app-checkbox>
          }
        </div>
      </div>
    }
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    FormsModule, CheckboxModule, NzToolTipModule,
    IconInfoComponent,
  ],
})
export class MetricItemComponent implements AfterViewInit {
  readonly destroyRef = inject(DestroyRef)
  readonly caerusApiService = inject(CaerusApiService);
  readonly formService = inject(QueryEngineFormService)
  readonly cdr = inject(ChangeDetectorRef)

  data = model<{
    /** 分组名称 */
    groupName: string

    /** 指标列表 */
    subMetric: Array<MetricsMenuVo & { checked: boolean }>
  }>(null)

  metricsTagMapResource = rxResource({
    loader: () => this.caerusApiService.fetchMenuMetricsTag().pipe(
      map(res => {
        const mp = new Map();

        Object.keys(res.data).forEach(key => {
          mp.set(key, res.data[key].describe);
        })

        return mp;
      })
    )
  })


  ngAfterViewInit(): void {
    this._subscribeToFormChange()
  }

  private _subscribeToFormChange() {
    this.formService.metrics.valueChanges
      .pipe(startWith(this.formService.metrics.value), debounceTime(100), takeUntilDestroyed(this.destroyRef))
      .subscribe(values => {
        this.data.update(value => {
          value.subMetric = value.subMetric.map(sub => {
            const metric = values.find(value => value.extendName === sub.extendName)

            return {
              ...sub,
              checked: Boolean(metric),
            }
          })
          return value
        })
        const checkedMetrics = this.data().subMetric.filter(item => item.checked)

        if (isNotEmpty(checkedMetrics)) {
          // console.log('[当前选择的指标]', checkedMetrics)
        }

        this.cdr.markForCheck() // 为了即时刷新chekbox选中状态
      })
  }

  getState(item: MetricsMenuVo) {
    const { metrics } = this.formService.support() ?? {}

    if (metrics) {
      const exist = metrics.some(metric => metric.extendName === item.extendName)

      return !exist
    }

    return false
  }

  private _add(metric: MetricsMenuVo) {
    const control = new FormGroup(new MetricVoForm(metric))

    /**
     * 如果选择了构成维度，则改为“单选模式”
     * 选择前清空全部指标
     */
    if (this.formService.dimensions.controls.length > 0) {
      this.formService.metrics.clear()
    }

    this.formService.metrics.push(control)
  }

  private _remove({ extendName }: MetricsMenuVo) {
    const index = this.formService.metrics.controls.findIndex(control => control.value.extendName === extendName)

    if (index >= 0) {
      this.formService.metrics.removeAt(index)
    }
  }

  @Debounce(100)
  onModelChange(metric: MetricsMenuVo & { checked: boolean }) {
    metric?.checked ? this._add(metric) : this._remove(metric)
  }
}
