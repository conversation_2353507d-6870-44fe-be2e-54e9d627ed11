import { FormArray, FormGroup, FormsModule } from '@angular/forms';
import { ChangeDetectionStrategy, Component, computed, DestroyRef, effect, inject, input, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { debounceTime } from 'rxjs';

import { DimensionValueMenuVo, DimensionMenuVo } from '@api/caerus/model';
import { QueryEngineFormService, FilterItemForm, DimensionValueForm } from '@common/service/query-engine';
import { RadioModule } from '@shared/modules/headless';


@Component({
  selector: 'app-site-pkg-filter',
  template: `
    @if (data()) {
      <app-radio-group class="flex gap-1" [(ngModel)]="value">
        <label nz-tooltip="{{data()?.bizExpression}}" [nzTooltipMouseEnterDelay]="1" class="inline-flex items-start justify-end min-w-25 font-bold leading-5 whitespace-nowrap">{{data()?.aliasName}}：</label>
        <div class="flex gap-1 flex-wrap items-start">
          <app-radio class="tag-radio" activeClass="active" [disabled]="disabled()" [value]="null">全部</app-radio>
          @for (item of data().valueList; track $index) {
            <app-radio class="tag-radio" activeClass="active" [disabled]="disabled()" [value]="item">{{item.value}}</app-radio>
          }
        </div>
      </app-radio-group>
    }
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    FormsModule,
    RadioModule,
    NzToolTipModule,
  ],
})
export class SitePkgFilterComponent {

  readonly destroyRef = inject(DestroyRef);
  readonly formService = inject(QueryEngineFormService);

  form = new FormGroup(new FilterItemForm());
  value = signal<DimensionValueMenuVo>(null);
  list = input<DimensionMenuVo[]>(null);
  type = signal<string>(null);
  data = computed<DimensionMenuVo>(() => {
    switch (this.type()) {
      case '站点拼车单':
        return this.list().find(item => item.key === 'yse_is_site_pkg_book');
      case '非站点拼车单':
        return this.list().find(item => item.key === 'no_is_site_pkg_book');
      default:
        return this.list().find(item => item.key === 'is_site_pkg_book');
    }
  });

  disabled = computed(() => {
    const { dimensions } = this.formService.support() ?? {};
      
    if (dimensions) {
      const exist = dimensions.some(item => item.extendName === this.data().extendName);

      return !exist;
    }
    
    return false;
  });

  constructor() {
    this.formService.filterItems.valueChanges.pipe(
      debounceTime(100)
    ).subscribe(values => {
      const items = values.find(item => item.extendName === 'is_site_pkg');

      this.type.set(items?.value[0]?.value ?? null);
    })

    /** 如果当前列表中不存在已选中项，则清空之前选中项 */
    effect(() => {
      const exist = this.data().valueList.some(item => item.key === this.value()?.key);
      
      if (!exist) {
        this.value.set(null);
      }
    });

    effect(() => {
      this.value.set(null);
    })
    
    effect(() => {
      if (this.value()) {
        const { extendName } = this.data();
        
        if (!this._hasControl()) {
          this.formService.filterItems.push(this.form);
        }

        this.form.get('extendName').patchValue(extendName);
        this._setValue();
      } else {
        this.formService.filterItems.removeAt(this._index());
      }
    })

    this.formService.filterItems.valueChanges.pipe(
      takeUntilDestroyed(this.destroyRef)
    ).subscribe(() => {
      if (!this._hasControl()) {
        this.value.set(null);
      }
    })
  }


  private _setValue() {
    const valueControl = this.form.get('value') as FormArray<FormGroup<DimensionValueForm>>;
    const control = new FormGroup(new DimensionValueForm());
    const { key, value } = this.value();

    control.patchValue({ key, value });
    valueControl.clear();
    valueControl.push(control);
  }


  private _index() {
    return this.formService.filterItems.controls.findIndex(control => control === this.form);
  }


  private _hasControl() {
    return this.formService.filterItems.controls.some(control => control === this.form);
  }

}
