import { FormGroup, FormsModule } from '@angular/forms'
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  DestroyRef,
  computed,
  effect,
  inject,
  input,
  output,
  signal,
} from '@angular/core'
import { takeUntilDestroyed } from '@angular/core/rxjs-interop'
import { NzToolTipModule } from 'ng-zorro-antd/tooltip'
import { debounceTime, filter, startWith, take } from 'rxjs'
import * as _ from 'lodash'

import { DimensionMenuVo, DimensionValueMenuVo } from '@api/caerus/model'
import { DimensionValueForm, FilterItemForm, QueryEngineFormService } from '@common/service/query-engine'
import { RadioModule } from '@shared/modules/headless'

@Component({
  selector: 'app-seclev-channel-filter',
  template: `
    @if (data()) {
      <app-radio-group class="flex gap-1" [(ngModel)]="value" (ngModelChange)="valueChange($event)">
        <label
          nz-tooltip="{{ data()?.bizExpression }}"
          [nzTooltipMouseEnterDelay]="1"
          class="inline-flex items-start justify-end min-w-25 font-bold leading-5 whitespace-nowrap"
        >
          {{ data().aliasName }}：
        </label>
        <div class="flex gap-1 flex-wrap items-start">
          <app-radio class="tag-radio" activeClass="active" [disabled]="disabled()" [value]="null">全部</app-radio>
          @for (item of data().valueList; track $index) {
            <app-radio class="tag-radio" activeClass="active" [disabled]="disabled()" [value]="item">
              {{ item.value }}
            </app-radio>
          }
        </div>
      </app-radio-group>
    }
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [FormsModule, RadioModule, NzToolTipModule],
})
export class SeclevChannelFilterComponent implements AfterViewInit {
  readonly destroyRef = inject(DestroyRef)
  readonly formService = inject(QueryEngineFormService)

  form = new FormGroup(new FilterItemForm())
  value = signal<DimensionValueMenuVo>(null)
  list = input<DimensionMenuVo[]>(null)

  updateSecievChannel = output<any>()

  type = signal<string>(null)
  data = computed<DimensionMenuVo>(() => {
    switch (this.type()) {
      case '自有渠道':
        return this._getChannel('own_c_seclev_channel')
      case '外输渠道':
        return this._getChannel('out_c_seclev_channel')
      default:
        return this._getChannel('c_seclev_channel')
    }
  })

  disabled = computed(() => {
    const { dimensions } = this.formService.support() ?? {}

    if (dimensions) {
      const exist = dimensions.some(item => item.extendName === this.data().extendName)

      return !exist
    }

    return false
  })

  constructor() {
    /** 如果当前列表中不存在已选中项，则清空之前选中项 */
    effect(() => {
      const exist = this.data().valueList.some(item => item.key === this.value()?.key)
      if (!exist) {
        this.value.set(null)
      } else {
        const item = _.find(this.data().valueList, ['key', this.value()?.key])
        this.value.set(item)
      }
    })

    effect(() => {
      this.value.set(null)
    })
  }

  ngAfterViewInit(): void {
    this._subscribeToFormChange()
  }

  private _subscribeToFormChange() {
    this.formService.filterItems.valueChanges
      .pipe(
        startWith(this.formService.filterItems.value),
        debounceTime(100),
        filter(values => values.length > 0),
        take(1)
      )
      .subscribe(values => {
        values.forEach(filterItem => {
          const { extendName, value } = filterItem

          if (extendName === this.data().extendName) {
            const { key } = value.at(0) || {}
            const val = this.data().valueList.find(item => item.key === key)
            const index = this.formService.filterItems.controls.findIndex(control =>
              _.isEqual(control.value, filterItem)
            )

            this.formService.filterItems.removeAt(index)
            this.value.set(val)
            this.valueChange()
          }
        })
      })

    this.formService.filterItems.valueChanges
      .pipe(debounceTime(100), takeUntilDestroyed(this.destroyRef))
      .subscribe(values => {
        const channel = values.find(item => item.extendName === 'c_firlev_channel')
        const type = channel?.value[0]?.value ?? null

        this.type.set(type)
      })

    this.formService.filterItems.valueChanges.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(() => {
      if (!this._hasControl()) {
        this.value.set(null)
      }
    })
  }

  private _getChannel(extendName: string) {
    return this.list().find(item => item.key === extendName)
  }

  valueChange(value: DimensionValueMenuVo = this.value()) {
    // console.log('valueChange')
    this.updateSecievChannel.emit(this.value())
    if (value) {
      const { extendName } = this.data()

      if (this._hasControl() === false) {
        this.formService.filterItems.push(this.form)
      }

      this.form.get('extendName').patchValue(extendName)
      this._setValue()
    } else {
      this.formService.filterItems.removeAt(this._index())
    }
  }

  private _setValue() {
    const { key, value } = this.value()
    const valueControl = this.form.controls.value
    const control = new FormGroup(new DimensionValueForm({ key, value }))

    valueControl.clear()
    valueControl.push(control)
  }

  private _index() {
    return this.formService.filterItems.controls.findIndex(control => control === this.form)
  }

  private _hasControl() {
    return this.formService.filterItems.controls.some(control => control === this.form)
  }
}
