import { FormGroup, FormsModule } from '@angular/forms';
import { AfterViewInit, ChangeDetectionStrategy, Component, DestroyRef, effect, inject, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { debounceTime, distinctUntilChanged, filter, map, startWith, take } from 'rxjs';

import { CaerusApiService } from '@api/caerus';
import { SkeletonComponent } from '@shared/components/skeleton';
import { DimensionVo, QueryEngineFormService } from '@common/service/query-engine';
import { ConstituteMenuVo, DimensionValueMenuVo } from '@api/caerus/model';
import { RadioModule } from '@shared/modules/headless';
import { isNotNull } from '@common/function';


@Component({
  selector: 'app-dimension-filter',
  template: `
    <app-radio-group class="flex gap-1" [(ngModel)]="value" (ngModelChange)="valueChange($event)">
      <label class="inline-flex items-start justify-end min-w-25 font-bold leading-5 whitespace-nowrap">构成维度：</label>
      <div class="flex gap-1 flex-wrap items-start">
        @for (item of dimensions(); track $index) {
          @if (
            item.extendName === 'miage_intal'       ||
            item.extendName === 'inter_miage_intal' ||
            item.extendName === 'inner_miage_intal'
          ) {
            @if (
              item.extendName === 'miage_intal'       && type() === null  ||
              item.extendName === 'inter_miage_intal' && type() === '城际' ||
              item.extendName === 'inner_miage_intal' && type() === '市内'
            ) {
              <app-radio class="tag-radio" activeClass="active" [disabled]="notIn(item)" [value]="item">{{item.aliasName}}</app-radio>
            }
          } @else {
            <app-radio class="tag-radio" activeClass="active" [disabled]="notIn(item)" [value]="item">{{item.aliasName}}</app-radio>
          }
        } @empty {
          <app-skeleton class="w-10 h-full scale-y-80 mr-1" />
          <app-skeleton class="w-10 h-full scale-y-80 mr-1" />
          <app-skeleton class="w-10 h-full scale-y-80 mr-1" />
          <app-skeleton class="w-10 h-full scale-y-80 mr-1" />
          <app-skeleton class="w-10 h-full scale-y-80 mr-1" />
          <app-skeleton class="w-10 h-full scale-y-80 mr-1" />
          <app-skeleton class="w-10 h-full scale-y-80 mr-1" />
          <app-skeleton class="w-10 h-full scale-y-80 mr-1" />
          <app-skeleton class="w-10 h-full scale-y-80 mr-1" />
          <app-skeleton class="w-10 h-full scale-y-80 mr-1" />
        }
      </div>
    </app-radio-group>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    FormsModule,
    RadioModule,
    SkeletonComponent,
  ],
})
export class DimensionFilterComponent implements AfterViewInit {

  readonly apiService = inject(CaerusApiService);
  readonly formService = inject(QueryEngineFormService);
  readonly destroyRef = inject(DestroyRef);

  /** 里程类型：null | '城际' | '市内' */
  type = signal<string>(null);
  value = signal<Partial<DimensionValueMenuVo>>(null);
  form = new FormGroup(new DimensionVo());
  dimensions = signal<ConstituteMenuVo[]>([]);

  constructor() {
    effect(() => {
      /**
       * 因里程会与`订单类型`产生联动，所以当`里程类型`改变时清空value
       */
      switch (this.type()) {
        default:
          this.value.set(null);
      }
    });
  }

  
  ngAfterViewInit(): void {
    this.fetchConstitute();
    this._subscribeToFormChange();
  }


  private _subscribeToFormChange() {
    this.formService.dimensions.valueChanges.pipe(
      debounceTime(100),
      filter(isNotNull),
      take(1)
    ).subscribe(async value => {
      const { extendName } = value[0];
      const dimension = this.dimensions().find(item => item.extendName === extendName);

      this.value.set(dimension);
    })

    /** 监听表单筛选项改变 */
    this.formService.filterItems.valueChanges.pipe(
      startWith(this.formService.filterItems.value),
      debounceTime(50),
      /** 查找筛选项是否包含`订单类型`维度 */
      map(values => values.find(item => item.extendName === 'c_ord_type')),
      map(mileage => mileage?.value[0]?.value ?? null),
      distinctUntilChanged(),
      takeUntilDestroyed(this.destroyRef)
    ).subscribe(value => {
      /** 设置`里程类型` */
      this.type.set(value ?? null);
    })
  }


  valueChange(value: Partial<DimensionValueMenuVo> = this.value()) {
    this.formService.dimensions.clear();

    if (value) {
      const { extendName } = value;

      if (this.formService.metrics.controls.length > 1) {
        const first = this.formService.metrics.controls[0];
        
        this.formService.metrics.clear();
        this.formService.metrics.push(first);
      }

      this.form.get('extendName').patchValue(extendName);
      this.formService.dimensions.push(this.form);
    }
  }


  fetchConstitute() {
    this.apiService.fetchConstitute().subscribe(res => {
      this.dimensions.set(res.data);
    })
  }


  notIn(item: ConstituteMenuVo) {
    const { dimensions } = this.formService.support() ?? {};

    if (dimensions) {
      const exist = dimensions.some(dimension => dimension.extendName === item?.extendName);
    
      return !exist;
    }
    
    return false;
  }

}
