import { FormGroup, FormsModule } from '@angular/forms';
import { AfterViewInit, ChangeDetectionStrategy, Component, DestroyRef, computed, inject, input, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { debounceTime, filter, startWith, take } from 'rxjs';
import * as _ from 'lodash';

import { DimensionMenuVo, DimensionValueMenuVo } from '@api/caerus/model';
import { QueryEngineFormService, FilterItemForm, DimensionValueForm } from '@common/service/query-engine';
import { MoreCitySelectComponent } from './more-city-select/more-city-select.component';
import { RadioModule } from '@shared/modules/headless';

@Component({
  selector: 'app-city-filter',
  template: `
    @if (data()) {
      <app-radio-group class="flex gap-1" [(ngModel)]="value" (ngModelChange)="valueChange($event)">
        <label nz-tooltip="{{ data()?.bizExpression }}" [nzTooltipMouseEnterDelay]="1" class="inline-flex items-start justify-end min-w-25 font-bold leading-5 whitespace-nowrap">{{ data().aliasName }}：</label>
        <div class="flex gap-1 flex-wrap items-start">
          <app-radio class="tag-radio" activeClass="active" [disabled]="disabled()" [value]="null">全部</app-radio>
          @for (item of defaultShowCitys(); track $index) {
            <app-radio class="tag-radio" activeClass="active" [disabled]="disabled(item)" [value]="item">{{ item.showValue }}</app-radio>
          }
          @if (!hasAreaDimension()) {
            <app-more-city-select [supports]="supports()" [options]="moreCitys()" [(area)]="value" />
          }
        </div>
      </app-radio-group>
    }
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    FormsModule,
    RadioModule,
    NzToolTipModule,
    MoreCitySelectComponent
  ],
})
export class CityFilterComponent implements AfterViewInit {
  readonly destroyRef = inject(DestroyRef);
  readonly formService = inject(QueryEngineFormService);

  value = signal<DimensionValueMenuVo>(null);
  data = input<DimensionMenuVo>(null);
  form = new FormGroup(new FilterItemForm());
  hasAreaDimension = signal(false);
  available_area = ['广东', '江浙沪', '川渝', '山东', '京津冀', '东北三省'];

  supports = computed(() => {
    const { dimensions } = this.formService.support() ?? {};

    if (dimensions) {
      return dimensions.map(item => item.extendName);
    }

    return [];
  });

  disabled(item?: DimensionValueMenuVo) {
    const { dimensions } = this.formService.support() ?? {};

    if (!item) {
      return false;
    }

    if (this.hasAreaDimension()) {
      const exist = this.available_area.includes(item?.value);

      return !exist;
    }

    if (dimensions) {
      const exist = dimensions.some(dimension => dimension.extendName === item?.extendName);

      return !exist;
    }
  }

  defaultShowCitys = computed(() => {
    if (this.data()) {
      return this.data().valueList.filter(item => item.defaultShow);
    }
    return [];
  });

  moreCitys = computed(() => {
    if (this.data()) {
      return this.data()
        .valueList.filter(item => item.defaultShow === 0)
        .sort((a, b) => a.showOrder - b.showOrder);
    }
    return [];
  });

  ngAfterViewInit(): void {
    this._subscribeToFormChange();
  }

  private _subscribeToFormChange() {
    this.formService.filterItems.valueChanges
      .pipe(
        startWith(this.formService.filterItems.value),
        debounceTime(100),
        filter(values => values.length > 0),
        take(1)
      )
      .subscribe(values => {
        values.forEach(filterItem => {
          const { extendName, value } = filterItem;

          if (['city_bio_region', 'is_top20_city', 'city_name', 'province_name'].includes(extendName)) {
            const { key } = value.at(0) || {};
            const val = this.data().valueList.find(item => item.key === key);
            const index = this.formService.filterItems.controls.findIndex(control => _.isEqual(control.value, filterItem));

            this.formService.filterItems.removeAt(index);
            this.value.set(val);
            this.valueChange();
          }
        });
      });

    this.formService.filterItems.valueChanges.pipe(debounceTime(100), takeUntilDestroyed(this.destroyRef)).subscribe(() => {
      if (!this._hasControl()) {
        this.value.set(null);
      }
    });

    this.formService.dimensions.valueChanges.pipe(debounceTime(100), takeUntilDestroyed(this.destroyRef)).subscribe(value => {
      const exist = value.some(item => item.extendName === 'city_bio_region');
      this.hasAreaDimension.set(exist);
    });
  }

  valueChange(value: DimensionValueMenuVo = this.value()) {
    if (value) {
      const { extendName } = this.data();

      if (this._hasControl() === false) {
        this.formService.filterItems.push(this.form);
      }

      this.form.get('extendName').patchValue(extendName);
      this._setValue();
    } else {
      this.formService.filterItems.removeAt(this._index());
    }
  }

  private _setValue() {
    const { key, value, extendName } = this.value();
    const parentControl = this.form.get('value').parent;
    const valueControl = this.form.controls.value;
    const control = new FormGroup(new DimensionValueForm({ key, value }));

    parentControl.get('extendName').patchValue(extendName);
    valueControl.clear();
    valueControl.push(control);
  }

  private _index() {
    return this.formService.filterItems.controls.findIndex(control => control === this.form);
  }

  private _hasControl() {
    return this.formService.filterItems.controls.some(control => control === this.form);
  }
}
