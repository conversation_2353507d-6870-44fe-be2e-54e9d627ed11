import { DatePipe } from '@angular/common'
import {
  AfterViewInit,
  booleanAttribute,
  ChangeDetectionStrategy,
  Component,
  computed,
  DestroyRef,
  effect,
  inject,
  input,
  signal,
} from '@angular/core'
import { ControlContainer, FormGroup, FormsModule } from '@angular/forms'
import { startWith, take } from 'rxjs'

import { CompareTime, Time } from '@common/class'
import { QueryDt } from '@common/service/query-engine'
import { RangePicker } from './range-picker'

@Component({
  selector: 'month-picker',
  template: `
    <range-picker
      [isCompare]="isCompare()"
      class="block min-w-56 h-8"
      [(ngModel)]="value"
      [rangeMap]="rangeMap()"
      mode="month"
    />
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [FormsModule, RangePicker],
  providers: [DatePipe],
})
export class MonthPicker implements AfterViewInit {
  readonly datePipe = inject(DatePipe)
  readonly parentContainer = inject(ControlContainer)
  readonly destroyRef = inject(DestroyRef)

  isCompare = input(false, { transform: booleanAttribute })
  rangeMap = computed(() => {
    return this.isCompare() ? this.compareRangeMap() : this.baseRangeMap()
  })

  baseRangeMap = () => {
    const result = new Map()

    // result.set('近3月', new Time('month-2m', 'month').toString())
    // result.set('近6月', new Time('month-5m', 'month').toString())
    // result.set('近12月', new Time('month-11m', 'month').toString())
    // result.set('近24月', new Time('month-23m', 'month').toString())
    // result.set('今年', new Time('month-y1', 'month').toString())
    // result.set('去年', new Time('month-1y1', 'month-1y0').toString())
    // result.set('近2年', new Time('month-2y1', 'month').toString())

    result.set('近3月', new Time('month-3m', 'month').toString())
    result.set('近6月', new Time('month-6m', 'month').toString())
    result.set('近12月', new Time('month-12m', 'month').toString())
    result.set('近24月', new Time('month-24m', 'month').toString())
    result.set('今年', new Time('month-y1', 'month').toString())
    result.set('去年', new Time('month-1y1', 'month-1y0').toString())
    result.set('近2年', new Time('month-2y1', 'month').toString())

    return result
  }

  compareRangeMap = computed(() => {
    const result = new Map()

    result.set('月环比', new CompareTime('month-1m', 'month-1m').toString())
    result.set('去年同期', new CompareTime('month-1y', 'month-1y').toString())

    return result
  })

  value = signal<string>(null)

  constructor() {
    effect(() => {
      if (this.value()) {
        const [startTime, endTime] = this.value().split(',')

        this.form.controls.startTime.patchValue(startTime)
        this.form.controls.endTime.patchValue(endTime)
      }
    })
  }

  ngAfterViewInit(): void {
    this.form.valueChanges
      .pipe(
        startWith(this.form.getRawValue())
        // take(1)
      )
      .subscribe(value => {
        if (value) {
          const { startTime, endTime } = value

          if (startTime && endTime) {
            this.value.set(`${startTime},${endTime}`)
          }
        }
      })
  }

  get form() {
    return this.parentContainer.control as FormGroup<QueryDt>
  }
}
