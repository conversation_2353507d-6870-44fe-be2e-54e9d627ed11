import { DatePipe } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { ChangeDetectionStrategy, Component, DestroyRef, effect, inject, signal } from '@angular/core';
import { addDays } from 'date-fns';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { skip, startWith } from 'rxjs';

import { CompareTime, Time } from '@common/class';
import { DtType, QueryEngineFormService } from '@common/service/query-engine';
import { DiffTime, isNotUndefined } from '@common/function';
import { IconDeleteComponent } from '@shared/modules/icons';
import { DatePicker } from './date-picker';
import { WeekPicker } from './week-picker';
import { MonthPicker } from './month-picker';


@Component({
  selector: 'app-date-filter',
  template: `
    <form [formGroup]="formService.form">
      <div class="flex items-center gap-x-1 px-2">
        <label class="inline-flex items-center justify-end min-w-16 font-bold leading-5 whitespace-nowrap">日期：</label>

        <nz-select formControlName="dtType" class="w-24">
          @for (item of unitOptions(); track $index) {
            <nz-option [nzLabel]="item.label" [nzValue]="item.value"></nz-option>
          }
        </nz-select>

        <ng-container formGroupName="dt">
          @switch (dtType()) {
            @case ('dt') { <date-picker /> }
            @case ('yw') { <week-picker /> }
            @case ('ym') { <month-picker /> }
          }
        </ng-container>

        @if (compareVisible()) {
          <label class="inline-flex items-center justify-end min-w-16 font-bold leading-5 whitespace-nowrap">对比期：</label>

          <ng-container formGroupName="compareDt">
            @switch (dtType()) {
              @case ('dt') { <date-picker isCompare /> }
              @case ('yw') { <week-picker isCompare /> }
              @case ('ym') { <month-picker isCompare /> }
            }
          </ng-container>

          <DeleteIcon iconBtn class="xl" (click)="removeDateCompare()" />
        }
        @else {
          <button nz-button nzType="default" (click)="addDateCompare()">添加日期对比</button>
        }
      </div>
    </form>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    ReactiveFormsModule,
    NzButtonModule,
    NzSelectModule,
    IconDeleteComponent,
    DatePicker,
    WeekPicker,
    MonthPicker,
  ],
  providers: [
    DatePipe,
  ],
})
export class DateFilterComponent {

  readonly destroyRef = inject(DestroyRef);
  readonly formService = inject(QueryEngineFormService);
  readonly datePipe = inject(DatePipe);

  compareVisible = signal(false);

  unitOptions = signal([
    { label: '按单日', value: 'dt' },
    { label: '按周',   value: 'yw' },
    { label: '按月',   value: 'ym' },
  ]);

  dtType = signal<DtType>(null);


  constructor() {
    effect(() => {
      const { compareDt } = this.formService.form$() || {};
      const visible = isNotUndefined(compareDt);

      this.compareVisible.set(visible);
    });
  }


  ngAfterViewInit(): void {
    const dt = this.formService.form.controls.dt;
    const dtType = this.formService.form.controls.dtType;

    dtType.valueChanges.pipe(
      startWith(dtType.value),
      takeUntilDestroyed(this.destroyRef)
    ).subscribe((type) => {
      this.dtType.set(type);
    });

    dtType.valueChanges.pipe(
      // skip(1),
      // distinctUntilChanged(),
      takeUntilDestroyed(this.destroyRef)
    ).subscribe((type) => {
      const compareDt = this.formService.form.controls.compareDt;
      // console.log('dtType change', type);

      switch (type) {
        case 'dt':
          dt.reset({startTime: 'now-14d', endTime: 'now-1d'});
          break;
        case 'yw':
          dt.reset({startTime: 'week-4w', endTime: 'week-0w'});
          break;
        case 'ym':
          dt.reset({startTime: 'month-y1', endTime: 'month'});
          break;
      }

      if (type) {
        // compareDt?.patchValue({ startTime: null, endTime: null });
        switch (type) {
          case 'dt':
            compareDt?.patchValue({startTime: 'now-1d', endTime: 'now-1d'});
            break;
          case 'yw':
            compareDt?.patchValue({startTime: 'week-1w', endTime: 'week-1w'});
            break;
          case 'ym':
            compareDt?.patchValue({startTime: 'month-1m', endTime: 'month-1m'});
            break;
        }
      }
    })
  }


  addDateCompare() {
    const { dt: {startTime, endTime}, dtType } = this.formService.form.getRawValue();

    this.formService.addDateCompare();

    if (startTime && endTime) {
      if (dtType === 'dt') {
        const isRelative = Time.isRelative(`${startTime},${endTime}`);
        let startDate: Date;
        let endDate  : Date;

        if (isRelative) {
          const [start, end] = Time.getDate('date', `${startTime},${endTime}`);

          startDate = new Date(start);
          endDate   = new Date(end);
        } else {
          startDate = new Date(startTime);
          endDate   = new Date(endTime);
        }

        const dayCount  = new DiffTime(startDate, endDate).count('days');
        const compareDtStartTime = this.datePipe.transform(addDays(startDate, -(dayCount) + -1), 'yyyy-MM-dd');
        const compareDtEndTime   = this.datePipe.transform(addDays(startDate, -1), 'yyyy-MM-dd');

        this.formService.form.get('compareDt.startTime').patchValue(compareDtStartTime);
        this.formService.form.get('compareDt.endTime').patchValue(compareDtEndTime);
      }

      // yw
      // ym
    }
  }

  removeDateCompare() {
    this.formService.removeDateCompare();
  }

}
