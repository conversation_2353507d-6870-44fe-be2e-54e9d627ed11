@reference "../../../../../../../styles.css";

.menu-wrapper-column-2 {
  @apply
    flex justify-between 
    content-start flex-wrap gap-y-2
    w-40 h-[269px] 
    p-2 overflow-auto;
}

.menu-wrapper-column-2 .menu-item {
  @apply
    flex items-center justify-center
    min-w-17 h-5 rounded 
    bg-slate-50 border border-slate-200 
    text-slate-500 
    font-bold text-xs/none text-center 
    hover:bg-primary hover:text-white 
    cursor-pointer;
}

.menu-wrapper-column-2.dt .menu-item:first-child {
  @apply
    min-w-full
}

.menu-wrapper-column-2 .menu-item span {
  @apply
    inline-block scale-85;
}