import { DecimalPipe, NgTemplateOutlet } from '@angular/common'
import { ChangeDetectionStrategy, Component, computed, inject, input } from '@angular/core'
import { NzTableModule } from 'ng-zorro-antd/table'
import { maxBy, minBy } from 'lodash'

import { LegendControlService } from '@common/service'
import { getDimensionField, getMetricField } from '@common/chart'
import { sortCategoriesFn3 } from '@common/function'
import { QueryEngineFormService } from '@common/service/query-engine'
import { QueryOutputVo } from '@api/query-engine/model'
import { computeDimensionValue, computeRatioValue, computeValue } from './helper'

@Component({
  selector: 'app-numeric-table',
  template: `
    <p class="text-xs py-3 px-8">
      @if (sourceType() !== 'constitute') {
        注：表格中给出的AVG、MAX、MIN值是基于上方图表中所有数据的数学公式处理，例如：“（顺）下单接单率”
        的均值，即所选时间段内的所有下单接单率数值求平均，
      } @else {
        注：表格中给出的AVG、MAX、MIN值是基于上方图表中所有数据的数学公式处理，例如：“（顺）下单接单率”
        的均值，即所选时间段内的所有下单接单率数值求平均；表格中每列的最大值、最小值做了高亮标注，最大值为红色，最小值为绿色。
      }
    </p>
    <nz-table #basicTable [nzData]="rows()" nzBordered nzSize="small" [nzShowPagination]="false" [nzPageSize]="100">
      @if (hasCompareData()) {
        <thead>
          <tr>
            <th rowspan="2">指标名称</th>
            <th colspan="3">时间段内均值(AVG)</th>
            <th colspan="3">时间段内最大值(MAX)</th>
            <th colspan="3">时间段内最小值(MIN)</th>
          </tr>
          <tr>
            @for (column of compareCol(); track column) {
              <th
                [nzSortOrder]="column.sortOrder"
                [nzSortFn]="column.sortFn"
                [nzSortDirections]="column.nzSortDirections"
                class="text-center!"
              >
                {{ column.name }}
              </th>
            }
          </tr>
        </thead>
        <tbody>
          @for (item of basicTable.data; track $index) {
            <tr>
              <td>{{ item.aliasName }}</td>
              <td
                align="right"
                [class.text-red-500]="item.avg && item.avg === maxValueMap().avg"
                [class.bg-red-50]="item.avg && item.avg === maxValueMap().avg"
                [class.text-green-500]="item.avg && item.avg === maxValueMap().minAvg"
                [class.bg-green-50]="item.avg && item.avg === maxValueMap().minAvg"
              >
                <ng-template
                  *ngTemplateOutlet="avgValueTemplate; context: { $implicit: item, key: 'avg' }"
                ></ng-template>
              </td>
              <td
                align="right"
                [class.text-red-500]="item.compareAvg && item.compareAvg === maxValueMap().compareAvg"
                [class.bg-red-50]="item.compareAvg && item.compareAvg === maxValueMap().compareAvg"
                [class.text-green-500]="item.compareAvg && item.compareAvg === maxValueMap().minCompareAvg"
                [class.bg-green-50]="item.compareAvg && item.compareAvg === maxValueMap().minCompareAvg"
              >
                <ng-template
                  *ngTemplateOutlet="avgValueTemplate; context: { $implicit: item, key: 'compareAvg' }"
                ></ng-template>
              </td>
              <td align="right">
                <ng-template
                  *ngTemplateOutlet="diffValueTemplate; context: { $implicit: item, key: 'diffAvg' }"
                ></ng-template>
              </td>
              <td
                align="right"
                [class.text-red-500]="item.max && item.max === maxValueMap().max"
                [class.bg-red-50]="item.max && item.max === maxValueMap().max"
                [class.text-green-500]="item.max && item.max === maxValueMap().minMax"
                [class.bg-green-50]="item.max && item.max === maxValueMap().minMax"
              >
                <ng-template *ngTemplateOutlet="valueTemplate; context: { $implicit: item, key: 'max' }"></ng-template>
              </td>
              <td
                align="right"
                [class.text-red-500]="item.compareMax && item.compareMax === maxValueMap().compareMax"
                [class.bg-red-50]="item.compareMax && item.compareMax === maxValueMap().compareMax"
                [class.text-green-500]="item.compareMax && item.compareMax === maxValueMap().minCompareMax"
                [class.bg-green-50]="item.compareMax && item.compareMax === maxValueMap().minCompareMax"
              >
                <ng-template
                  *ngTemplateOutlet="valueTemplate; context: { $implicit: item, key: 'compareMax' }"
                ></ng-template>
              </td>
              <td align="right">
                <ng-template
                  *ngTemplateOutlet="diffValueTemplate; context: { $implicit: item, key: 'diffMax' }"
                ></ng-template>
              </td>
              <td
                align="right"
                [class.text-red-500]="item.min && item.min === maxValueMap().min"
                [class.bg-red-50]="item.min && item.min === maxValueMap().min"
                [class.text-green-500]="item.min && item.min === maxValueMap().minMin"
                [class.bg-green-50]="item.min && item.min === maxValueMap().minMin"
              >
                <ng-template *ngTemplateOutlet="valueTemplate; context: { $implicit: item, key: 'min' }"></ng-template>
              </td>
              <td
                align="right"
                [class.text-red-500]="item.compareMin && item.compareMin === maxValueMap().compareMin"
                [class.bg-red-50]="item.compareMin && item.compareMin === maxValueMap().compareMin"
                [class.text-green-500]="item.compareMin && item.compareMin === maxValueMap().minCompareMin"
                [class.bg-green-50]="item.compareMin && item.compareMin === maxValueMap().minCompareMin"
              >
                <ng-template
                  *ngTemplateOutlet="valueTemplate; context: { $implicit: item, key: 'compareMin' }"
                ></ng-template>
              </td>
              <td align="right">
                <ng-template
                  *ngTemplateOutlet="diffValueTemplate; context: { $implicit: item, key: 'diffMin' }"
                ></ng-template>
              </td>
            </tr>
          }
        </tbody>
      } @else {
        <thead>
          <tr>
            @for (column of noCompareCol(); track column) {
              <th
                [nzSortOrder]="column.sortOrder"
                [nzSortFn]="column.sortFn"
                [nzSortDirections]="column.nzSortDirections"
              >
                <div [class.text-right]="!$first">{{ column.name }}</div>
              </th>
            }
          </tr>
        </thead>
        <tbody>
          @for (item of basicTable.data; track $index) {
            <tr>
              <td>{{ item.aliasName }}</td>
              <td
                align="right"
                [class.text-red-500]="item.avg && item.avg === maxValueMap().avg"
                [class.bg-red-50]="item.avg && item.avg === maxValueMap().avg"
                [class.text-green-500]="item.avg && item.avg === maxValueMap().minAvg"
                [class.bg-green-50]="item.avg && item.avg === maxValueMap().minAvg"
              >
                <ng-template
                  *ngTemplateOutlet="avgValueTemplate; context: { $implicit: item, key: 'avg' }"
                ></ng-template>
              </td>
              <td
                align="right"
                [class.text-red-500]="item.max && item.max === maxValueMap().max"
                [class.bg-red-50]="item.max && item.max === maxValueMap().max"
                [class.text-green-500]="item.max && item.max === maxValueMap().minMax"
                [class.bg-green-50]="item.max && item.max === maxValueMap().minMax"
              >
                <ng-template *ngTemplateOutlet="valueTemplate; context: { $implicit: item, key: 'max' }"></ng-template>
              </td>
              <td
                align="right"
                [class.text-red-500]="item.min && item.min === maxValueMap().min"
                [class.bg-red-50]="item.min && item.min === maxValueMap().min"
                [class.text-green-500]="item.min && item.min === maxValueMap().minMin"
                [class.bg-green-50]="item.min && item.min === maxValueMap().minMin"
              >
                <ng-template *ngTemplateOutlet="valueTemplate; context: { $implicit: item, key: 'min' }"></ng-template>
              </td>
            </tr>
          }
        </tbody>
      }
    </nz-table>

    <ng-template #avgValueTemplate let-item let-key="key">
      @let value = item[key];
      @if (item.dataUnit === '%') {
        {{ value === null ? '-' : (value | number: '1.0-2') + '%' }}
      } @else {
        {{ value === null ? '-' : (value | number: '1.0-3') }}
      }
    </ng-template>

    <ng-template #valueTemplate let-item let-key="key">
      @let value = item[key];
      @if (item.dataUnit === '%') {
        {{ value === null ? '-' : (value | number: '1.0-2') + '%' }}
      } @else if (item.dataUnit === '个') {
        {{ value === null ? '-' : (value | number: '1.0-3') }}
      } @else {
        {{ value | number: '1.0-4' }}
      }
    </ng-template>

    <ng-template #diffValueTemplate let-item let-key="key">
      @let isPercent = item.dataUnit === '%';
      @let ratioValue = item[key + 'Ratio'];
      @let value = item[key];

      <span
        class="whitespace-nowrap"
        [class.text-neutral-500]="value === 0"
        [class.text-green-500]="value < 0"
        [class.text-red-600]="value > 0"
      >
        @if (isPercent) {
          {{ value }}pp
        } @else {
          {{ value }} ({{ ratioValue === null ? '-' : ratioValue + '%' }})
        }
      </span>
    </ng-template>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [DecimalPipe, NgTemplateOutlet, NzTableModule],
})
export class NumericTableComponent {
  readonly formService = inject(QueryEngineFormService)
  readonly legendControlService = inject(LegendControlService)

  data = input<QueryOutputVo>(null)
  sourceType = input('trend')

  hasCompareData = computed(() => {
    const { compareData } = this.data()

    return !!compareData
  })

  noCompareCol = computed(() => {
    return [
      {
        name: '指标名称',
        nzSortDirections: [null],
        sortOrder: null,
      },
      {
        name: '时间段内均值(AVG)',
        nzSortDirections: ['ascend', 'descend', null],
        sortOrder: this.sourceType() === 'comparison' ? null : 'descend',
        sortFn: (a, b) => a.avg - b.avg,
      },
      {
        name: '时间段内最大值(MAX)',
        nzSortDirections: ['ascend', 'descend', null],
        sortOrder: null,
        sortFn: (a, b) => a.max - b.max,
      },
      {
        name: '时间段内最小值(MIN)',
        nzSortDirections: ['ascend', 'descend', null],
        sortOrder: null,
        sortFn: (a, b) => a.min - b.min,
      },
    ]
  })

  compareCol = computed(() => {
    return [
      {
        name: '当前期',
        nzSortDirections: ['ascend', 'descend', null],
        sortOrder: this.sourceType() === 'comparison' ? null : 'descend',
        sortFn: (a, b) => {
          const aValue = a.dataUnit === '%' ? a.avg / 100 : a.avg
          const bValue = b.dataUnit === '%' ? b.avg / 100 : b.avg
          return aValue - bValue
        },
      },
      {
        name: '对比期',
        nzSortDirections: ['ascend', 'descend', null],
        sortOrder: null,
        sortFn: (a, b) => {
          const aValue = a.dataUnit === '%' ? a.compareAvg / 100 : a.compareAvg
          const bValue = b.dataUnit === '%' ? b.compareAvg / 100 : b.compareAvg
          return aValue - bValue
        },
      },
      {
        name: '较对比期',
        nzSortDirections: ['ascend', 'descend', null],
        sortOrder: null,
        sortFn: (a, b) => {
          const aValue = a.dataUnit === '%' ? a.diffAvg / 100 : a.diffAvg
          const bValue = b.dataUnit === '%' ? b.diffAvg / 100 : b.diffAvg
          return aValue - bValue
        },
      },
      {
        name: '当前期',
        nzSortDirections: ['ascend', 'descend', null],
        sortOrder: null,
        sortFn: (a, b) => {
          const aValue = a.dataUnit === '%' ? a.max / 100 : a.max
          const bValue = b.dataUnit === '%' ? b.max / 100 : b.max
          return aValue - bValue
        },
      },
      {
        name: '对比期',
        nzSortDirections: ['ascend', 'descend', null],
        sortOrder: null,
        sortFn: (a, b) => {
          const aValue = a.dataUnit === '%' ? a.compareMax / 100 : a.compareMax
          const bValue = b.dataUnit === '%' ? b.compareMax / 100 : b.compareMax
          return aValue - bValue
        },
      },
      {
        name: '较对比期',
        nzSortDirections: ['ascend', 'descend', null],
        sortOrder: null,
        sortFn: (a, b) => {
          const aValue = a.dataUnit === '%' ? a.diffMax / 100 : a.diffMax
          const bValue = b.dataUnit === '%' ? b.diffMax / 100 : b.diffMax
          return aValue - bValue
        },
      },
      {
        name: '当前期',
        nzSortDirections: ['ascend', 'descend', null],
        sortOrder: null,
        sortFn: (a, b) => {
          const aValue = a.dataUnit === '%' ? a.min / 100 : a.min
          const bValue = b.dataUnit === '%' ? b.min / 100 : b.min
          return aValue - bValue
        },
      },
      {
        name: '对比期',
        nzSortDirections: ['ascend', 'descend', null],
        sortOrder: null,
        sortFn: (a, b) => {
          const aValue = a.dataUnit === '%' ? a.compareMin / 100 : a.compareMin
          const bValue = b.dataUnit === '%' ? b.compareMin / 100 : b.compareMin
          return aValue - bValue
        },
      },
      {
        name: '较对比期',
        nzSortDirections: ['ascend', 'descend', null],
        sortOrder: null,
        sortFn: (a, b) => {
          const aValue = a.dataUnit === '%' ? a.diffMin / 100 : a.diffMin
          const bValue = b.dataUnit === '%' ? b.diffMin / 100 : b.diffMin
          return aValue - bValue
        },
      },
    ]
  })

  rows = computed(() => {
    const { headers } = this.data()
    const visibleLegendNames = this.legendControlService.visibleLegendNames()
    const metricFields = getMetricField(headers)
    const [dimensionField] = getDimensionField(headers)
    const body = this.formService.value()
    let results: any[] = []

    metricFields.forEach(key => {
      let item = null
      const { dataUnit } = headers[key]

      if (dimensionField) {
        const items = computeDimensionValue(body, this.data(), key)

        results.push(...items)
      } else {
        if (dataUnit === '%') {
          item = computeRatioValue(body, this.data(), key)
        } else {
          item = computeValue(body, this.data(), key)
        }
      }

      item && results.push(item)
    })

    results = results.sort(sortCategoriesFn3('aliasName')).filter(item => {
      if (visibleLegendNames === null) {
        // 如果可见的图例名称为`null`，则列表全部可见
        return true
      }
      return visibleLegendNames.includes(item.aliasName) // 否则筛选图例相关列表数据
    })
    // console.log('results', results)
    return results
  })

  maxValueMap = computed(() => {
    if (this.sourceType() === 'constitute') {
      return {
        avg: maxBy(this.rows(), 'avg')?.avg,
        compareAvg: maxBy(this.rows(), 'compareAvg')?.compareAvg,
        max: maxBy(this.rows(), 'max')?.max,
        compareMax: maxBy(this.rows(), 'compareMax')?.compareMax,
        min: maxBy(this.rows(), 'min')?.min,
        compareMin: maxBy(this.rows(), 'compareMin')?.compareMin,

        minAvg: minBy(this.rows(), 'avg')?.avg,
        minCompareAvg: minBy(this.rows(), 'compareAvg')?.compareAvg,
        minMax: minBy(this.rows(), 'max')?.max,
        minCompareMax: minBy(this.rows(), 'compareMax')?.compareMax,
        minMin: minBy(this.rows(), 'min')?.min,
        minCompareMin: minBy(this.rows(), 'compareMin')?.compareMin,
      }
    }
    return {}
  })
}
