import { differenceInCalendarDays, differenceInCalendarMonths, differenceInCalendarISOWeeks } from 'date-fns';
import { QueryDt, QueryInputVo, QueryOutputVo } from '@api/query-engine/model';
import { getFirstDayOfISOWeek, groupBy, isNotNull, isNotUndefinedOrNotNull, toDecimals, toNumber } from '@common/function';
import { getDimensionField } from '@common/chart';
import { DtType } from '@common/service/query-engine';
import { Util } from '@common/class';


function computeDaysCount({ startTime, endTime }: QueryDt, dtType: DtType) {
  const start = startTime.split('-').map(toNumber);
  const end = endTime.split('-').map(toNumber);

  if (dtType === 'dt') {
    return (
      differenceInCalendarDays(
        new Date(endTime),
        new Date(startTime)
      ) + 1
    )
  } else if (dtType === 'ym') {
    return (
      differenceInCalendarMonths(
        new Date(end[0], end[1] - 1),
        new Date(start[0], start[1] - 1)
      ) + 1
    )
  } else if (dtType === 'yw') {
    return (
      differenceInCalendarISOWeeks(
        getFirstDayOfISOWeek(end[0], end[1]),
        getFirstDayOfISOWeek(start[0], start[1])
      ) + 1
    )
  }

  return 0;
}


function computeNumeric(dt: QueryDt, dtType: DtType, data: any[], key: string) {
  if (!dt) return [0, 0, 0, 0];
  const values = data?.map(item => item[key]).filter(isNotUndefinedOrNotNull).map(toNumber) || [];
  const sum = Util.sum(values);
  const daysCount = computeDaysCount(dt, dtType);  
  // const avg = sum / daysCount;
  const avg = sum / values.length;
  const max = Math.max(...values);
  const min = Math.min(...values);

  return [avg, max, min, sum];
}


export function computeRatioValue({ dt, compareDt, dtType }: QueryInputVo, inputData: QueryOutputVo, key: string) {
  const { headers, data, compareData } = inputData;
  const isProportion = key.endsWith(':proportion');
  const { aliasName, dataUnit } = headers[key];
  const [ avg, max, min ] = computeNumeric(dt, dtType, data, key);
  const [ compareAvg, compareMax, compareMin ] = computeNumeric(compareDt, dtType, compareData || [], key);

  return {
    dataUnit,
    aliasName: isProportion ? `${aliasName}占比` : aliasName,
    avg: toDecimals(avg),
    max: toDecimals(max),
    min: toDecimals(min),
    compareAvg: toDecimals(compareAvg),
    compareMax: toDecimals(compareMax),
    compareMin: toDecimals(compareMin),
    diffAvg: toDecimals(avg - compareAvg),
    diffMax: toDecimals(max - compareMax),
    diffMin: toDecimals(min - compareMin),
  };
}


export function computeValue({ dt, compareDt, dtType }: QueryInputVo, inputData: QueryOutputVo, key: string) {
  const { headers, data, compareData } = inputData;
  const { aliasName, dataUnit } = headers[key];
  const [ avg, max, min ] = computeNumeric(dt, dtType, data, key);
  const [ compareAvg, compareMax, compareMin ] = computeNumeric(compareDt, dtType, compareData || [], key);

  return {
    dataUnit,
    aliasName,
    avg: toDecimals(avg, 1, 3),
    max: toDecimals(max, 1, 3),
    min: toDecimals(min, 1, 3),
    compareAvg: toDecimals(compareAvg, 1),
    compareMax: toDecimals(compareMax, 1),
    compareMin: toDecimals(compareMin, 1),
    diffAvg: toDecimals(avg - compareAvg, 1),
    diffMax: toDecimals(max - compareMax, 1),
    diffMin: toDecimals(min - compareMin, 1),
    diffAvgRatio: toDecimals((avg - compareAvg) / compareAvg),
    diffMaxRatio: toDecimals((max - compareMax) / compareMax),
    diffMinRatio: toDecimals((min - compareMin) / compareMin),
  };
}


export function computeDimensionValue({ dt, compareDt, dtType }: QueryInputVo, inputData: QueryOutputVo, key: string) {
  const { headers, data, compareData } = inputData;
  const isProportion = key.endsWith(':proportion');
  const { aliasName, dataUnit } = headers[key];
  const [dimensionField] = getDimensionField(headers);
  const groupData = groupBy(data, dimensionField);
  const groupCompareData = groupBy(compareData, dimensionField);
  const items = [];

  Object.keys(groupData).forEach(k => {
    const [ avg, max, min ] = computeNumeric(dt, dtType, groupData[k], key);
    const [ compareAvg, compareMax, compareMin ] = computeNumeric(compareDt, dtType, groupCompareData[k], key);

    if (isProportion) {
      items.push({
        dataUnit,
        aliasName: `${aliasName}-${k}-占比`,
        avg: toDecimals(avg),
        max: toDecimals(max),
        min: toDecimals(min),
        compareAvg: toDecimals(compareAvg),
        compareMax: toDecimals(compareMax),
        compareMin: toDecimals(compareMin),
        diffAvg: toDecimals(avg - compareAvg),
        diffMax: toDecimals(max - compareMax),
        diffMin: toDecimals(min - compareMin),
      })
    } else {
      if (dataUnit === '%') {
        items.push({
          dataUnit,
          aliasName: `${aliasName}-${k}`,
          avg: toDecimals(avg),
          max: toDecimals(max),
          min: toDecimals(min),
          compareAvg: toDecimals(compareAvg),
          compareMax: toDecimals(compareMax),
          compareMin: toDecimals(compareMin),
          diffAvg: toDecimals(avg - compareAvg),
          diffMax: toDecimals(max - compareMax),
          diffMin: toDecimals(min - compareMin),
          diffAvgRatio: toDecimals((avg - compareAvg) / compareAvg),
          diffMaxRatio: toDecimals((max - compareMax) / compareMax),
          diffMinRatio: toDecimals((min - compareMin) / compareMin),
        })
      } else {
        items.push({
          dataUnit,
          aliasName: `${aliasName}-${k}`,
          avg: toDecimals(avg, 1, 3),
          max: toDecimals(max, 1, 3),
          min: toDecimals(min, 1, 3),
          compareAvg: toDecimals(compareAvg, 1),
          compareMax: toDecimals(compareMax, 1),
          compareMin: toDecimals(compareMin, 1),
          diffAvg: toDecimals(avg - compareAvg, 1),
          diffMax: toDecimals(max - compareMax, 1),
          diffMin: toDecimals(min - compareMin, 1),
          diffAvgRatio: toDecimals((avg - compareAvg) / compareAvg),
          diffMaxRatio: toDecimals((max - compareMax) / compareMax),
          diffMinRatio: toDecimals((min - compareMin) / compareMin),
        })
      }
      
    }
  })

  return items;
}