<app-navigation />

<nz-layout>
  <nz-sider
    nzCollapsible
    [(nzCollapsed)]="isCollapsed"
    [nzTrigger]="null"
    nzTheme="light"
    [nzCollapsedWidth]="50"
    [nzWidth]="140"
  >
    <ul nz-menu nzTheme="light" nzMode="inline" class="h-full">
      <div
        (click)="toggle()"
        class="py-2 text-center cursor-pointer hover:bg-neutral-50 transition-colors border-r border-[#f0f0f0]"
      >
        <span
          nz-icon
          [nzType]="isCollapsed() ? 'menu-unfold' : 'menu-fold'"
        ></span>
      </div>
      <li
        [nz-tooltip]="isCollapsed() ? '自助查询' : ''"
        nzTooltipPlacement="right"
        nz-menu-item
        nzMatchRouter
        nzMatchRouterExact
        routerLink="./"
      >
        <span nz-icon nzType="tool" nzTheme="outline"></span>
        <span>自助查询</span>
      </li>
      <li
        [nz-tooltip]="isCollapsed() ? '我的看板' : ''"
        nzTooltipPlacement="right"
        nz-menu-item
        nzMatchRouter
        nzMatchRouterExact
        routerLink="./dashboard"
      >
        <span nz-icon nzType="dashboard" nzTheme="outline"></span>
        <span>我的看板</span>
      </li>
    </ul>
  </nz-sider>
  <nz-layout #layout cdkScrollable class="h-[calc(100vh_-_64px)] overflow-auto">
    <router-outlet />
  </nz-layout>
</nz-layout>
