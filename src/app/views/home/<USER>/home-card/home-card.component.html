<app-radio-group
  [class]="display() === 1 ? `grid grid-cols-${cols()} h-full w-full gap-x-4 gap-y-6` : 'flex items-center gap-3 w-full flex-1 h-full'"
  [(ngModel)]="index"
  radioGroupOrientation="vertical"
  (ngModelChange)="changeCard()"
>
  @for (item of list(); track $index) {
    <div class="relative w-full h-full flex flex-col gap-1">
      <ng-template #metricsTitleTemplate>
        {{ item?.showName }}
        <span class="text-xs opacity-30 px-1">({{ item?.aliasName }})</span>
      </ng-template>

      <app-radio
        nz-popover
        class="card-radio card-theme flex! items-center h-full {{ themeArr()[$index] }} {{ flex() ? 'flex-1' : '' }}"
        activeClass="active"
        [nzPopoverPlacement]="'top'"
        [nzPopoverMouseEnterDelay]="0.5"
        [nzPopoverTitle]="metricsTitleTemplate"
        [nzPopoverContent]="contentTemplate"
        [value]="$index + 1"
      >
        <ng-template #contentTemplate>
          <div [innerHTML]="item?.bizExpression"></div>
        </ng-template>
        <div class="flex flex-col justify-around gap-1 px-4 py-2 w-full h-full">
          <div class="flex items-center justify-center gap-x-1 font-bold text-base text-inherit">
            {{ item.showName }}
          </div>
          @if (!item.twobizExpression) {
            <div class="flex items-center justify-center gap-x-1 font-bold text-base text-inherit py-1">
              @if (item.extendNames[0].data) {
                {{ item.extendNames[0].data | number: '1.0-2' }}
              } @else {
                -
              }
              {{ dataUnit() === 'pp' || item.extendNames[0].dataUnit === 'pp' ? '%' : '' }}
            </div>
            <div class="flex items-center justify-center gap-3 pb-1.5">
              @for (c of compareObj(); track c) {
                <div>
                  {{ c.label }}:
                  <span [class]="`${renderNumText(item.extendNames[0][c.value]).color} whitespace-nowrap`">
                    {{ renderNumText(item.extendNames[0][c.value]).text
                    }}{{ item.extendNames[0].dataUnit === 'pp' ? 'pp' : dataUnit() }}
                  </span>
                </div>
              }
            </div>
          } @else {
            <div class="flex items-center justify-center gap-6">
              <div class="flex flex-col items-center">
                <div class="text-neutral-400 text-[10px]">{{ item.twobizExpressionName[0] }}</div>
                <div class="flex items-center justify-center gap-x-1 font-bold text-base text-inherit py-1">
                  {{ item.extendNames[0].data | number: '1.0-2' }}
                </div>
                <div class="text-[10px] pb-1.5">
                  @for (c of compareObj(); track c) {
                    <div>
                      {{ c.label }}:
                      <span [class]="renderNumText(item.extendNames[0][c.value]).color">
                        {{ renderNumText(item.extendNames[0][c.value]).text
                        }}{{ item.extendNames[0].dataUnit === 'pp' ? 'pp' : dataUnit() }}
                      </span>
                    </div>
                  }
                </div>
              </div>
              <div>/</div>
              <div class="flex flex-col items-center">
                <div class="text-neutral-400 text-[10px]">{{ item.twobizExpressionName[1] }}</div>
                @if (item.extendNames.length === 4) {
                  <div class="flex items-center justify-center gap-x-1 font-bold text-base text-inherit py-1">
                    {{ item.extendNames[3].data | number: '1.0-2' }}
                  </div>
                  <div class="text-[10px] pb-1.5">
                    @for (c of compareObj(); track c) {
                      <div>
                        {{ c.label }}:
                        <span [class]="renderNumText(item.extendNames[3][c.value]).color">
                          {{ renderNumText(item.extendNames[3][c.value]).text
                          }}{{ item.extendNames[0].dataUnit === 'pp' ? 'pp' : dataUnit() }}
                        </span>
                      </div>
                    }
                  </div>
                } @else {
                  <div class="flex items-center justify-center gap-x-1 font-bold text-base text-inherit py-1">
                    {{ item.extendNames[1].data | number: '1.0-2' }}
                  </div>
                  <div class="text-[10px] pb-1.5">
                    @for (c of compareObj(); track c) {
                      <div>
                        {{ c.label }}:
                        <span [class]="renderNumText(item.extendNames[1][c.value]).color">
                          {{ renderNumText(item.extendNames[1][c.value]).text
                          }}{{ item.extendNames[0].dataUnit === 'pp' ? 'pp' : dataUnit() }}
                        </span>
                      </div>
                    }
                  </div>
                }
              </div>
            </div>
          }

          @if (!item.extendNames[2] && needShowProgress()) {
            <div class="text-neutral-400 text-[10px]">
              <div class="flex justify-between">
                <div>{{ isCost() ? '核销进度' : '完成度' }}</div>
                <div>-(-)</div>
              </div>
              <div style="margin: -4px 0">
                <nz-progress [nzPercent]="0" [nzShowInfo]="false"></nz-progress>
              </div>
              <div class="flex justify-end">暂无目标</div>
            </div>
          } @else {
            <div
              class="text-neutral-400 text-[10px]"
              [class]="!item.extendNames[2] && !hidden() ? 'opacity-0' : hidden() ? 'hidden' : ''"
            >
              <div class="flex justify-between">
                <div>{{ isCost() ? '核销进度' : '完成度' }}</div>
                <div>
                  @if (dataUnit() === 'pp') {
                    {{ item.extendNames[2]?.data | number: '1.0-2' }}%
                  } @else {
                    {{ item.extendNames[2]?.data * 100 | number: '1.0-2' }}%
                  }

                  @if (item.diff < 0) {
                    <span class="text-green-500">
                      (较{{ isTime() ? '时间进度慢' : '目标差' }}{{ item.diff | number: '1.0-2'
                      }}{{ isTime() || dataUnit() === 'pp' ? 'pp' : '' }})
                    </span>
                  } @else {
                    <span class="text-red-500">
                      (较{{ isTime() ? '时间进度快' : '目标多' }}{{ item.diff | number: '1.0-2'
                      }}{{ isTime() || dataUnit() === 'pp' ? 'pp' : '' }})
                    </span>
                  }
                </div>
              </div>
              <div style="margin: -4px 0">
                @if (dataUnit() === 'pp') {
                  <nz-progress [nzPercent]="item.extendNames[2]?.data || 0" [nzShowInfo]="false"></nz-progress>
                } @else {
                  <nz-progress [nzPercent]="item.extendNames[2]?.data * 100 || 0" [nzShowInfo]="false"></nz-progress>
                }
              </div>
              <div class="flex justify-end">
                {{ subTitle() }}：{{ (item.extendNames[1]?.data | number: '1.0-2') || '-'
                }}{{ dataUnit() === 'pp' ? '%' : '' }}
              </div>
            </div>
          }
        </div>
      </app-radio>
      <span class="absolute -bottom-4.5 text-neutral-400 text-[10px]" [innerHTML]="tooltip()[$index]"></span>
    </div>
  }
</app-radio-group>
