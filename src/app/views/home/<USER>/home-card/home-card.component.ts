import { DecimalPipe } from '@angular/common'
import {
  booleanAttribute,
  ChangeDetectionStrategy,
  Component,
  computed,
  effect,
  input,
  OnChanges,
  output,
  signal,
  SimpleChanges,
} from '@angular/core'
import { FormsModule } from '@angular/forms'
import { RadioModule } from '@shared/modules/headless'
import { NzPopoverModule } from 'ng-zorro-antd/popover'
import { NzProgressModule } from 'ng-zorro-antd/progress'

@Component({
  selector: 'app-home-card',
  imports: [FormsModule, RadioModule, NzPopoverModule, NzProgressModule, DecimalPipe],
  templateUrl: './home-card.component.html',
  styles: ``,
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: {
    class: 'w-full h-full',
  },
})
export class HomeCardComponent implements OnChanges {
  from = input<string>()
  list = input([])
  themeArr = input(['orange', 'teal', 'blue'])
  display = input(1)
  cols = input(1)
  compareType = input('month')
  updateIndex = output<number>()
  tooltip = input([])
  flex = input(false)
  hidden = input(false)
  showMonthSubTitle = input(false, { transform: booleanAttribute })
  needShowProgress = input(false)
  isTime = input(false)
  dataUnit = input('%')
  isCost = input(false)

  subTitle = computed(() => {
    if (this.compareType() === 'year') {
      if (this.showMonthSubTitle()) {
        return '当月目标'
      }
      if (this.isCost()) {
        return '本月预算'
      }
      return 'MTD目标'
    }
    return '当日目标'
  })

  compareObj = computed(() => {
    if (this.compareType() === 'month') {
      return [
        {
          label: '日环比',
          value: 'dt',
        },
        {
          label: '周同比',
          value: 'yw',
        },
      ]
    }
    if (this.compareType() === 'week') {
      return [
        {
          label: '周环比',
          value: 'yw',
        },
        {
          label: '年同比',
          value: 'year',
        },
      ]
    }
    return [
      {
        label: '月环比',
        value: 'ym',
      },
      {
        label: '年同比',
        value: 'year',
      },
    ]
  })

  index = signal(1)
  inputIndex = input(1)

  ngOnChanges(): void {
    this.index.set(this.inputIndex())
  }

  changeCard() {
    this.updateIndex.emit(this.index())
  }

  renderNumText(_num) {
    if (!_num) {
      return {
        text: '-',
        color: 'text-black',
      }
    }
    const num = Number(_num)
    if (num > 0) {
      return {
        text: `+${Intl.NumberFormat().format(+num.toFixed(2))}`,
        color: 'text-red-500',
      }
    }
    if (num < 0) {
      return {
        text: Intl.NumberFormat().format(+num.toFixed(2)),
        color: 'text-green-500',
      }
    }
    return {
      text: Intl.NumberFormat().format(+num.toFixed(2)),
      color: 'text-black',
    }
  }
}
