<span class="flex items-center gap-x-1.5 font-black text-base pt-3">
  <MapArrowRightIcon />
  交易效率监控

  @switch (viewType()) {
    @case ('passenger') {
      <span class="text-xs text-neutral-400 font-normal">注：乘客视角是自有渠道数据</span>
    }
    @case ('driver') {
      <span class="text-xs text-neutral-400 font-normal">注：车主视角是全部渠道数据</span>
    }
  }
  <span></span>
</span>
<div class="mt-[-30px]">
  <div class="flex justify-center">
    <app-radio-group [(ngModel)]="viewType" class="flex items-start px-3 gap-[30px]" (ngModelChange)="changeViewType()">
      <app-radio class="line-radio text-xs whitespace-nowrap" activeClass="active" value="passenger">
        乘客视角
      </app-radio>
      <app-radio class="line-radio text-xs whitespace-nowrap" activeClass="active" value="driver">车主视角</app-radio>
      <app-radio class="line-radio text-xs whitespace-nowrap" activeClass="active" value="order">订单视角</app-radio>
    </app-radio-group>
  </div>
</div>
<div class="flex flex-1 flex-wrap items-center gap-x-5 gap-y-1 pt-3 pb-1">
  <div class="flex items-center">
    <label class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap">统计口径：</label>
    <app-radio-group class="relative flex gap-1" [(ngModel)]="type" (ngModelChange)="dealGroupDimension()">
      <app-radio class="tag-radio-new" activeClass="active" value="day">按日看</app-radio>
      <app-radio class="tag-radio-new" activeClass="active" value="week">按周看</app-radio>
      <app-radio class="tag-radio-new" activeClass="active" value="month">按月看</app-radio>
      <app-radio-thumb class="rounded-xs bg-primary" />
    </app-radio-group>
  </div>
  @if (viewType() === 'passenger') {
    <div class="flex items-center">
      <label class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap">
        乘客四类：
      </label>
      <app-radio-group
        class="relative flex gap-1"
        [(ngModel)]="c_pass_fourca_user_type"
        (ngModelChange)="getCardData()"
      >
        <app-radio class="tag-radio-new" activeClass="active" [value]="null">全部</app-radio>
        @for (item of config()?.c_pass_fourca_user_type; track item) {
          <app-radio class="tag-radio-new" activeClass="active" [value]="item">{{ item.showValue }}</app-radio>
        }
        <app-radio-thumb class="rounded-xs bg-primary" />
      </app-radio-group>
    </div>
  } @else if (viewType() === 'driver') {
    <div class="flex items-center">
      <label class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap">
        车主四类：
      </label>
      <app-radio-group
        class="relative flex gap-1"
        [(ngModel)]="cdriver_fourca_user_type"
        (ngModelChange)="getCardData()"
      >
        <app-radio class="tag-radio-new" activeClass="active" [value]="null">全部</app-radio>
        @for (item of config()?.cdriver_fourca_user_type; track item) {
          <app-radio class="tag-radio-new" activeClass="active" [value]="item">{{ item.showValue }}</app-radio>
        }
        <app-radio-thumb class="rounded-xs bg-primary" />
      </app-radio-group>
    </div>
    <div class="flex items-center">
      <label class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap">ABC车主：</label>
      <app-radio-group class="relative flex gap-1" [(ngModel)]="cdriver_abc_type" (ngModelChange)="getCardData()">
        <app-radio class="tag-radio-new" activeClass="active" [value]="null">全部</app-radio>
        @for (item of config()?.cdriver_abc_type; track item) {
          <app-radio class="tag-radio-new" activeClass="active" [value]="item">{{ item.showValue }}</app-radio>
        }
        <app-radio-thumb class="rounded-xs bg-primary" />
      </app-radio-group>
    </div>
  } @else {
    <div class="flex items-center">
      <label class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap">
        业务渠道：
      </label>
      <app-radio-group class="relative flex gap-1" [(ngModel)]="c_firlev_channel" (ngModelChange)="getCardData()">
        <app-radio class="tag-radio-new" activeClass="active" [value]="null">全部</app-radio>
        @for (item of config()?.c_firlev_channel; track item) {
          <app-radio class="tag-radio-new" activeClass="active" [value]="item">{{ item.showValue }}</app-radio>
        }
        <app-radio-thumb class="rounded-xs bg-primary" />
      </app-radio-group>
    </div>
    <div class="flex items-center">
      <label class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap">
        订单类型：
      </label>
      <app-radio-group class="relative flex gap-1" [(ngModel)]="c_ord_type" (ngModelChange)="getCardData()">
        <app-radio class="tag-radio-new" activeClass="active" [value]="null">全部</app-radio>
        @for (item of config()?.c_ord_type; track item) {
          <app-radio class="tag-radio-new" activeClass="active" [value]="item">{{ item.showValue }}</app-radio>
        }
        <app-radio-thumb class="rounded-xs bg-primary" />
      </app-radio-group>
    </div>
  }
  <div class="flex items-center">
    <label class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap">区域：</label>
    <app-area-select [(ngModel)]="area" (ngModelChange)="getCardData()" />
  </div>
</div>
<span class="text-xs text-neutral-400 font-normal pb-3">
  卡片中展示的是
  @switch (type()) {
    @case ('day') {
      <span class="text-xs text-neutral-400 font-normal">昨日</span>
    }
    @case ('week') {
      <span class="text-xs text-neutral-400 font-normal">本周</span>
    }
    @case ('month') {
      <span class="text-xs text-neutral-400 font-normal">本月</span>
    }
  }
  数值。
</span>
<div class="flex flex-col gap-5 h-135">
  <div class="flex flex-wrap w-full gap-y-5 px-1 py-3.5 customized-scrollbar overflow-x-auto">
    @if (cardLoading()) {
      <div class="flex items-center justify-center w-full">
        <app-line-spin />
      </div>
    } @else {
      <app-home-card
        class="min-w-360!"
        (updateIndex)="changeCard($event)"
        [list]="list()"
        [display]="2"
        [inputIndex]="index()"
        dataUnit="pp"
        [showMonthSubTitle]="true"
        [themeArr]="['orange', 'orange', 'orange', 'orange', 'orange', 'orange']"
        [compareType]="type() === 'day' ? 'month' : type() === 'week' ? 'week' : 'year'"
        [needShowProgress]="type() === 'month' && (viewType() === 'passenger' || viewType() === 'driver')"
      ></app-home-card>
    }
  </div>
  <div class="flex-1 min-w-0 flex flex-col h-full shadow-md rounded-sm border border-neutral-100 relative">
    <div class="flex-1 min-h-0 relative flex items-center justify-center text-xs">
      @if (loading()) {
        <app-line-spin />
      } @else {
        @if (option()) {
          <app-graph class="absolute inset-2" [options]="option()" />
        } @else if (errorMessage()) {
          <span class="text-slate-600 text-xs">{{ errorMessage() }}</span>
        }
      }
    </div>
  </div>
</div>
