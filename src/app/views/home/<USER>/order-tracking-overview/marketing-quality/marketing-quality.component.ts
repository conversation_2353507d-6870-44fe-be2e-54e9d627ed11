import { AfterViewInit, ChangeDetectionStrategy, Component, computed, DestroyRef, inject, signal } from '@angular/core'
import { FormsModule } from '@angular/forms'
import { RadioComponent, RadioGroupComponent, RadioModule } from '@shared/modules/headless/radio'
import { IconMapArrowRightComponent } from '@shared/modules/icons'
import { AreaSelectComponent } from '../../../../../shared/components/area-select/area-select.component'
import { LineSpinComponent } from '@shared/components/line-spin'
import { HomeCardComponent } from '../../home-card/home-card.component'
import { GraphComponent } from '@shared/components/graph'
import { QueryOutputVo } from '@api/query-engine/model'
import { AreaBasic } from '../lib'
import { BuriedPointService, LegendControlService, LegendItemClickHandler } from '@common/service'
import { CaerusApiService } from '@api/caerus'
import { QueryEngineFormService } from '@common/service/query-engine'
import { QueryEngineApiService } from '@api/query-engine'
import { groupBy, findIndex } from 'lodash'
import { SwitchMap } from '@common/decorator'
import { takeUntilDestroyed } from '@angular/core/rxjs-interop'
import { combineLatest, finalize } from 'rxjs'
import { DatePipe } from '@angular/common'
import { HomeService } from '@views/home/<USER>'
import { getISOWeek, subDays, format } from 'date-fns'
import { PAGE_NAME } from '@common/directive'

@Component({
  selector: 'app-marketing-quality',
  host: {
    class: 'block px-4',
  },
  imports: [
    IconMapArrowRightComponent,
    RadioGroupComponent,
    RadioComponent,
    FormsModule,
    RadioModule,
    AreaSelectComponent,
    LineSpinComponent,
    HomeCardComponent,
    GraphComponent,
  ],
  templateUrl: './marketing-quality.component.html',
  styles: ``,
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [LegendControlService, QueryEngineFormService, DatePipe],
})
export class MarketingQualityComponent implements AfterViewInit {
  page_name = inject(PAGE_NAME)
  readonly buriedPointService = inject(BuriedPointService)
  readonly legendControlService = inject(LegendControlService)
  readonly apiService = inject(CaerusApiService)
  readonly formService = inject(QueryEngineFormService)
  readonly queryService = inject(QueryEngineApiService)
  readonly datePipe = inject(DatePipe)
  readonly destroyRef = inject(DestroyRef)
  readonly service = inject(HomeService)

  readonly radioMap = {
    day: ':dt_DIFF',
    week: ':yw_DIFF',
    month: ':ym_DIFF',
    year: ':year_DIFF',
  }

  viewType = signal('passenger')
  type = signal('week')
  passType = signal(null)
  area = signal(null)
  cardLoading = signal(false)
  loading = signal(false)
  errorMessage = signal(null)
  option = signal(null)
  allMetrics = signal(null)
  currentMetrics = signal([])
  dt = signal(null)
  list = signal([])
  allConfig = signal(null)
  c_pass_fourca_user_type = signal(null)
  cdriver_fourca_user_type = signal(null)
  cdriver_abc_type = signal(null)
  c_firlev_channel = signal(null)
  c_ord_type = signal(null)
  index = signal(1)

  filters = computed(() => {
    return [
      this.c_pass_fourca_user_type(),
      this.cdriver_fourca_user_type(),
      this.cdriver_abc_type(),
      this.c_firlev_channel(),
      this.c_ord_type(),
      this.area(),
    ].filter(f => f)
  })

  dtType = computed(() => {
    if (this.type() === 'day') {
      return 'dt'
    }
    if (this.type() === 'week') {
      return 'yw'
    }
    if (this.type() === 'month') {
      return 'ym'
    }
  })

  dimensions = computed(() => {
    if (!this.allMetrics()) {
      return null
    }
    return this.allMetrics()[`transaction_efficiency_${this.viewType()}_${this.type()}`]
  })

  config: any = computed(() => {
    if (!this.allConfig()) {
      return null
    }
    let obj = {}
    this.allConfig()[this.viewType()].forEach(c => {
      obj[c.extendName] = c.values
    })
    return obj
  })

  chartMetrics = computed(() => {
    if (!this.allMetrics()) {
      return []
    }
    const groupDimension = groupBy(this.dimensions().subMetric, 'tagName')
    const arr = Object.keys(groupDimension).map((dimension, index) => {
      return {
        showName: dimension,
        aliasName: groupDimension[dimension][0].aliasName,
        bizExpression: groupDimension[dimension][0].bizExpression,
        value: groupDimension[dimension].map(d => {
          return {
            aliasName: d.aliasName,
            extendName: d.extendName,
            recommend: d.recommend,
            displayOrder: d.displayOrder,
          }
        }),
        checked: index === this.index() - 1,
      }
    })
    return arr
  })

  ngAfterViewInit(): void {
    this.fetchMetrics()
    this.fetchDimension()
    this.subscribeToMonthChange()
  }

  changeViewType() {
    this.type.set('day')
    this.index.set(1)
    this.c_pass_fourca_user_type.set(null)
    this.cdriver_fourca_user_type.set(null)
    this.cdriver_abc_type.set(null)
    this.c_firlev_channel.set(null)
    this.c_ord_type.set(null)
    this.area.set(null)
    this.dealGroupDimension()
  }

  subscribeToMonthChange() {
    combineLatest([this.service.month$, this.service.updateTimeResource$])
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(([value, updateTime]) => {
        if (!updateTime) {
          return
        }
        this.dt.set({
          startTime: updateTime,
          endTime: updateTime,
        })
        this.getCardData()
      })
  }

  dealGroupDimension() {
    if (!this.dimensions()) {
      return
    }
    this.loading.set(true)
    const groupDimension = groupBy(
      this.dimensions().subMetric.filter(d => d.recommend === 1),
      'tagName'
    )
    const arr = Object.values(groupDimension).map((item: any, index) => {
      const extendNames = item.map(sub => ({
        extendName: sub.extendName,
        recommend: sub.recommend,
        displayOrder: sub.displayOrder,
      }))

      return {
        aliasName: item[0].aliasName,
        bizExpression: item[0].bizExpression,
        showName: Object.keys(groupDimension)[index],
        extendNames,
        twobizExpression: false,
      }
    })
    this.currentMetrics.set(arr)
    this.getCardData()
  }

  fetchMetrics() {
    this.apiService.fetchMetricsConfig('transaction_efficiency').subscribe(res => {
      if (res.data) {
        console.log('transaction_efficiency', res.data)
        this.allMetrics.set(res.data)
        this.dealGroupDimension()
      } else {
        this.currentMetrics.set([])
      }
    })
  }

  fetchDimension() {
    this.apiService.fetchDimensionConfig('transaction_efficiency_dim').subscribe(res => {
      console.log('transaction_efficiency_dim', res.data)
      if (res.data) {
        this.allConfig.set(res.data)
      } else {
        this.allConfig.set(null)
      }
    })
  }

  changeCard(index) {
    if (this.list().length !== 0) {
      this.buriedPointService.addStat('dida_dpm_caerus_home_tradecard_click', {
        page_name: this.page_name,
        indicator_name: this.list()[index - 1]?.extendNames[0].extendName,
      })
    }

    this.index.set(index)
    this.getChartData()
  }

  getStartWeekNative(endIsoWeek) {
    // 1. 解析结束周
    const [endYear, endWeek] = endIsoWeek.split('-').map(Number)

    // 2. 计算结束周的周四（ISO年锚点）
    const jan4 = new Date(endYear, 0, 4)
    const jan4Day = jan4.getDay()
    const firstThursday = new Date(jan4)
    firstThursday.setDate(jan4.getDate() + ((11 - jan4Day) % 7)) // 定位首周周四

    // 3. 计算结束周周四
    const endThursday = new Date(firstThursday)
    endThursday.setDate(firstThursday.getDate() + (endWeek - 1) * 7)

    // 4. 回溯51周
    const startThursday: any = new Date(endThursday)
    startThursday.setDate(endThursday.getDate() - 51 * 7)

    // 5. 计算开始周的ISO年/周
    const startYear = startThursday.getFullYear()
    const yearStart = new Date(startYear, 0, 4)
    const yearStartDay = yearStart.getDay()
    const yearFirstThursday: any = new Date(yearStart)
    yearFirstThursday.setDate(yearStart.getDate() + ((11 - yearStartDay) % 7))

    // 6. 计算周数
    const weekDiff = Math.floor((startThursday - yearFirstThursday) / (7 * 86400000))
    const week = (weekDiff + 1).toString().padStart(2, '0')

    return `${startYear}-${week}`
  }

  @SwitchMap()
  getChartData() {
    if (this.chartMetrics().length === 0) {
      return
    }
    this.loading.set(true)
    const body = this.formService.value()
    const [year, month] = this.dt().startTime.split('-')

    let time = this.dt().startTime
    let endTime, startTime
    if (this.type() === 'day') {
      startTime = format(subDays(new Date(time), 89), 'yyyy-MM-dd')
      endTime = time
    } else if (this.type() === 'week') {
      const week = getISOWeek(new Date(time))
      startTime = this.getStartWeekNative(`${year}-${week}`)
      endTime = `${year}-${week}`
    } else {
      startTime = `${year - 1}-${month}`
      endTime = `${year}-${month}`
    }

    body.dt = {
      startTime,
      endTime,
    }

    body.dataFillConfig = {
      open: 0,
      fillRangeType: 1,
      fillValue: 1,
    }
    body.dimensions = [
      {
        id: null,
        extendName: this.dtType(),
        predefineCompareType:
          this.type() === 'day' ? ['dt', 'yw'] : this.type() === 'week' ? ['yw', 'year'] : ['ym', 'year'],
      },
    ]

    body.dtType = this.dtType()

    const items = this.filters().map(f => {
      return {
        conditionType: 2,
        condition: '>=',
        extendName: f.extendName,
        value: [
          {
            key: f.key,
            value: f.value,
          },
        ],
        valueType: null,
      }
    })

    body.filter = {
      items,
      type: null,
    }

    const checkedMetrics = this.chartMetrics().filter(c => c.checked)
    const _metrics = checkedMetrics.reduce((acc, c) => acc.concat(c.value), [])

    // console.log(_metrics);

    body.metrics = _metrics.filter(item => item.displayOrder === 1).map(m => m.extendName)

    this.legendControlService.reset()
    return this.queryService
      .search(body, 'marketing-chart')
      .pipe(finalize(() => this.loading.set(false)))
      .subscribe(res => {
        if (res.data) {
          this._setChartData(res.data)
        } else {
          this.option.set(null)
          this.errorMessage.set(res.message)
        }
      })
  }

  @SwitchMap()
  getCardData() {
    if (this.currentMetrics().length === 0) {
      return
    }

    const body = this.formService.value()

    let time
    if (this.type() === 'day') {
      time = this.dt().startTime
    } else if (this.type() === 'week') {
      const week = getISOWeek(new Date(this.dt().startTime))
      time = `${this.dt().startTime.split('-')[0]}-${week}`
    } else {
      time = `${this.dt().startTime.split('-')[0]}-${this.dt().startTime.split('-')[1]}`
    }

    body.dt = {
      startTime: time,
      endTime: time,
    }

    body.dtType = this.dtType()

    body.dimensions = [
      {
        id: null,
        extendName: this.dtType(),
        predefineCompareType:
          this.type() === 'day' ? ['dt', 'yw'] : this.type() === 'week' ? ['yw', 'year'] : ['ym', 'year'],
      },
    ]

    const items = this.filters().map(f => {
      return {
        conditionType: 2,
        condition: '>=',
        extendName: f.extendName,
        value: [
          {
            key: f.key,
            value: f.value,
          },
        ],
        valueType: null,
      }
    })

    body.filter = {
      items,
      type: null,
    }

    body.metrics = this.currentMetrics()
      .reduce((arr, m) => arr.concat(m.extendNames.map(ex => ex.extendName)), [])
      .map(extendName => ({ extendName }))

    body.dataFillConfig = {
      open: 1,
      fillRangeType: 2,
      fillValue: 1,
    }
    this.cardLoading.set(true)

    return this.queryService
      .search(body, 'marketing-card')
      .pipe(finalize(() => this.cardLoading.set(false)))
      .subscribe(res => {
        if (res.data && res.data.data.length !== 0) {
          const _data = res.data.data[0]

          this.currentMetrics().forEach(m => {
            m.extendNames.forEach(ex => {
              ex.dataUnit = 'pp'
              ex.data = _data[ex.extendName] ? Number(_data[ex.extendName]) * 100 : null
              if (ex.displayOrder === 1) {
                ex.dt = (Number(_data[`${ex.extendName}${this.radioMap.day}`]) * 100).toFixed(2)
                ex.yw = (Number(_data[`${ex.extendName}${this.radioMap.week}`]) * 100).toFixed(2)
                ex.ym = (Number(_data[`${ex.extendName}${this.radioMap.month}`]) * 100).toFixed(2)
                ex.year = (Number(_data[`${ex.extendName}${this.radioMap.year}`]) * 100).toFixed(2)
              }
            })
            if (m.extendNames[1]) {
              m.diff = m.extendNames[0].data - m.extendNames[1].data
            }
          })
          const d = this.currentMetrics()[this.index() || 1 - 1].extendNames[0].data
          if (!d) {
            const _index = findIndex(this.currentMetrics(), m => m.extendNames[0].data !== null)
            this.index.set(_index ? _index + 1 : 1)
          }
        } else {
          this.currentMetrics.set([])
          this.index.set(1)
        }
        this.list.set(this.currentMetrics())
      })
  }

  private _setChartData(data: QueryOutputVo) {
    try {
      const chart = new AreaBasic({ ...data })
      const legendItemClick = LegendItemClickHandler(this.legendControlService)
      chart.plotOptions.series.events = { legendItemClick }
      this.option.set(chart?.getOption() || null)
    } catch (e) {
      this.option.set(null)
      this.errorMessage.set(e)
      console.error(e)
    }
  }
}
