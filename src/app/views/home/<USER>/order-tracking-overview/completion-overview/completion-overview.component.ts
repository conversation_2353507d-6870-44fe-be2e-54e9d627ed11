import { AfterViewInit, ChangeDetectionStrategy, Component, computed, DestroyRef, inject, signal } from '@angular/core'
import { FormsModule } from '@angular/forms'
import { GraphComponent } from '@shared/components/graph'
import { LineSpinComponent } from '@shared/components/line-spin'
import { RadioModule } from '@shared/modules/headless'
import { IconMapArrowRightComponent } from '@shared/modules/icons'
import { NzPopoverModule } from 'ng-zorro-antd/popover'
import { NzProgressModule } from 'ng-zorro-antd/progress'
import { HomeCardComponent } from '../../home-card/home-card.component'
import { CaerusApiService } from '@api/caerus'
import { groupBy, find } from 'lodash'
import { QueryEngineFormService } from '@common/service/query-engine'
import { QueryEngineApiService } from '@api/query-engine'
import { finalize, combineLatest } from 'rxjs'
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox'
import { QueryOutputVo } from '@api/query-engine/model'
import { ProgressTrend } from '../lib'
import { BuriedPointService, LegendControlService, LegendItemClickHandler } from '@common/service'
import { takeUntilDestroyed } from '@angular/core/rxjs-interop'
import { HomeService } from '@views/home/<USER>'
import { getMonthFirstAndLastDay, getMonthFirstAndNowDay } from '@common/class'
import { DatePipe } from '@angular/common'
import { SwitchMap } from '@common/decorator'
import { toNumber } from '@common/function'
import { PAGE_NAME } from '@common/directive'

@Component({
  selector: 'app-completion-overview',
  host: {
    class: 'block px-4',
  },
  imports: [
    IconMapArrowRightComponent,
    RadioModule,
    FormsModule,
    LineSpinComponent,
    GraphComponent,
    NzPopoverModule,
    NzProgressModule,
    HomeCardComponent,
    NzCheckboxModule,
  ],
  providers: [LegendControlService, QueryEngineFormService, DatePipe],
  templateUrl: './completion-overview.component.html',
  styles: ``,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CompletionOverviewComponent implements AfterViewInit {
  page_name = inject(PAGE_NAME)
  readonly buriedPointService = inject(BuriedPointService)
  readonly apiService = inject(CaerusApiService)
  readonly formService = inject(QueryEngineFormService)
  readonly queryService = inject(QueryEngineApiService)
  readonly legendControlService = inject(LegendControlService)
  readonly destroyRef = inject(DestroyRef)
  readonly service = inject(HomeService)
  readonly datePipe = inject(DatePipe)
  readonly radioMap = {
    day: ':dt_DIFF_RATIO',
    week: ':yw_DIFF_RATIO',
    month: ':ym_DIFF_RATIO',
    year: ':year_DIFF_RATIO',
  }

  type = signal('day')
  dimensions = signal('1')
  loading = signal(false)
  cardLoading = signal(false)
  option = signal(null)
  errorMessage = signal(null)
  index = signal(1)
  list = signal([])
  currentMetrics = signal<any>([])
  allMetrics = signal<any>(null)
  dt = signal(null)
  viewType = signal('passenger')
  bizType = signal('1')
  checkValue = signal([1])

  tooltip = computed(() => {
    if (this.dimensions() === '3') {
      return [
        `说明: 目标及目标完成率按<span class="text-blue-500">业务口径</span>数据计算`,
        `说明: 目标及目标完成率按<span class="text-blue-500">业务口径</span>数据计算`,

        `说明: 目标及目标完成率按<span class="text-blue-500">业务口径</span>数据计算`,
      ]
    }
    return [`说明: 目标及目标完成率按<span class="text-blue-500">业务口径</span>数据计算`]
  })

  dealGroupDimension() {
    let d
    if (this.dimensions() !== '2') {
      d = this.allMetrics()[`order_progress_${this.type()}_tab${this.dimensions()}`]
    } else {
      d = this.allMetrics()[`order_progress_month_tab2_${this.viewType()}`]
    }

    const dimensions = d

    const groupDimension = groupBy(
      dimensions.subMetric.filter(d => d.recommend === 1),
      'tagName'
    )

    const arr = Object.values(groupDimension).map((item: any, index) => {
      const extendNames = item.map(sub => ({
        extendName: sub.extendName,
        recommend: sub.recommend,
        displayOrder: sub.displayOrder,
      }))
      const order4 = find(item, ['displayOrder', 4])
      return {
        aliasName: item[0].aliasName,
        bizExpression: item[0].bizExpression,
        showName: Object.keys(groupDimension)[index],
        extendNames,
        twobizExpression: order4 && order4.recommend === 1,
        twobizExpressionName: ['业务口径', '财务口径'],
      }
    })
    this.currentMetrics.set(arr)
    this.getCardData()
  }

  extraMetrics = computed(() => {
    const checked = this.chartMetrics().filter(c => c.checked)
    return checked[0].value.filter(v => {
      if (this.bizType() === '1') {
        return v.displayOrder === 1 || v.displayOrder === 7
      } else {
        return v.displayOrder === 1 || v.displayOrder === 8
      }
    })
  })

  chartMetrics = computed(() => {
    if (!this.allMetrics()) {
      return []
    }
    let d
    if (this.dimensions() !== '2') {
      d = this.allMetrics()[`order_progress_${this.type()}_tab${this.dimensions()}`]
    } else {
      d = this.allMetrics()[`order_progress_month_tab2_${this.viewType()}`]
    }

    const dimensions = d
    const groupDimension = groupBy(dimensions.subMetric, 'tagName')
    console.log('groupDimension', groupDimension)
    const arr = Object.keys(groupDimension).map((dimension, index) => {
      return {
        showName: dimension,
        aliasName: groupDimension[dimension][0].aliasName,
        bizExpression: groupDimension[dimension][0].bizExpression,
        value: groupDimension[dimension].map(d => {
          return d
        }),
        checked: index === this.index() - 1,
      }
    })
    console.log('arr====', arr)
    return arr
  })

  ngAfterViewInit(): void {
    this.fetchMetrics()
    this.subscribeToMonthChange()
  }

  changeBizType() {
    this.checkValue.set([1])
    this.getChartData()
  }

  subscribeToMonthChange() {
    combineLatest([this.service.month$, this.service.updateTimeResource$])
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(([value, updateTime]) => {
        const month = this.datePipe.transform(value, 'yyyy-MM-dd')
        const [startTime] = getMonthFirstAndNowDay(month)
        this.dt.set({
          startTime,
          endTime: updateTime,
        })
        this.getCardData()
      })
  }

  @SwitchMap()
  getChartData() {
    if (this.chartMetrics().length === 0 || !this.dt().endTime) {
      return
    }
    this.loading.set(true)
    const body = this.formService.value()
    const [, end] = getMonthFirstAndLastDay(this.dt().endTime)
    body.dt = {
      startTime: this.dt().startTime,
      endTime: end,
    }
    body.dataFillConfig = {
      open: 0,
      fillRangeType: 1,
      fillValue: 1,
    }
    body.dimensions = [
      {
        id: null,
        extendName: 'dt',
      },
    ]

    const checkedMetrics = this.chartMetrics().filter(c => c.checked)

    const values = checkedMetrics.reduce((acc, c) => acc.concat(c.value), [])
    if (this.dimensions() !== '1' && this.dimensions() !== '2') {
      let _m = []
      this.checkValue().forEach(c => {
        if (c === 1) {
          if (this.bizType() === '1') {
            _m.push(...values.filter(v => v.displayOrder !== 4 && v.displayOrder !== 7 && v.displayOrder !== 8))
          } else {
            _m.push(...values.filter(v => v.displayOrder === 4))
          }
        } else {
          _m.push(...values.filter(v => v.displayOrder === c))
        }
      })
      body.metrics = _m
    } else {
      if (this.bizType() === '1') {
        if (this.dimensions() !== '2') {
          body.metrics = values.filter(v => v.displayOrder !== 4 && v.displayOrder !== 5 && v.displayOrder !== 6)
        } else {
          body.metrics = values.filter(v => v.displayOrder !== 4)
        }
      } else {
        body.metrics = values.filter(v => v.displayOrder === 4 || v.displayOrder === 5 || v.displayOrder === 6)
      }
    }

    this.legendControlService.reset()
    return this.queryService
      .search(body, 'order-tracking-overview-chart')
      .pipe(finalize(() => this.loading.set(false)))
      .subscribe(res => {
        if (res.data) {
          if (this.bizType() === '1') {
            if (this.dimensions() === '2') {
              const obj = {}
              checkedMetrics.forEach(c => {
                const order6 = find(c.value, ['displayOrder', 6])
                if (order6) {
                  const order3 = find(c.value, ['displayOrder', 3])
                  obj[order3.extendName] = order6.extendName
                }
              })
              const result = {}
              Object.keys(obj).forEach(o => {
                result[`${res.data.headers[o].aliasName}`] = []
              })
              res.data.data.forEach(d => {
                Object.keys(obj).forEach(o => {
                  result[`${res.data.headers[o].aliasName}`].push(
                    `${((toNumber(d[o]) - toNumber(d[obj[o]])) * 100).toFixed(2)}`
                  )
                })
              })

              this._setChartData(res.data, result)
            } else {
              const order1 = find(body.metrics, ['displayOrder', 1])
              if (order1) {
                const obj = {}
                checkedMetrics.forEach(c => {
                  const order2 = find(c.value, ['displayOrder', 2])
                  if (order2) {
                    const order1 = find(c.value, ['displayOrder', 1])
                    obj[order1.extendName] = order2.extendName
                  }
                })
                const result = {}
                Object.keys(obj).forEach(o => {
                  result[`${res.data.headers[o].aliasName}`] = []
                })
                res.data.data.forEach(d => {
                  Object.keys(obj).forEach(o => {
                    result[`${res.data.headers[o].aliasName}`].push(`${toNumber(d[o]) - toNumber(d[obj[o]])}`)
                  })
                })

                this._setChartData(res.data, result)
              } else {
                this._setChartData(res.data, null)
              }
            }
          } else {
            this._setChartData(res.data, null)
          }
        } else {
          this.option.set(null)
          this.errorMessage.set(res.message)
        }
      })
  }

  changeItem() {
    this.getChartData()
  }

  fetchMetrics() {
    this.apiService.fetchMetricsConfig('cockpit_own_channel_process_v2').subscribe(res => {
      if (res.data) {
        console.log('cockpit_top_metrics_v2', res.data)
        this.allMetrics.set(res.data)
        this.dealGroupDimension()
      } else {
        this.currentMetrics.set([])
      }
    })
  }

  @SwitchMap()
  private getCardData() {
    if (this.currentMetrics().length === 0) {
      return
    }
    const body = this.formService.value()
    const endTime = this.dt().endTime
    body.dt = {
      startTime: endTime,
      endTime,
    }

    body.dimensions = [
      {
        id: null,
        extendName: 'dt',
        predefineCompareType: this.type() === 'day' ? ['yw', 'dt'] : ['ym', 'year'],
      },
    ]

    body.metrics = this.currentMetrics()
      .reduce((arr, m) => arr.concat(m.extendNames.map(ex => ex.extendName)), [])
      .map(extendName => ({ extendName }))

    body.dataFillConfig = {
      open: 1,
      fillRangeType: 2,
      fillValue: 1,
    }
    this.cardLoading.set(true)
    this.loading.set(true)

    return this.queryService
      .search(body, 'order-tracking-overview')
      .pipe(finalize(() => this.cardLoading.set(false)))
      .subscribe(res => {
        if (res.data && res.data.data.length !== 0) {
          const _data = res.data.data[0]
          console.log(' this.currentMetrics()', this.currentMetrics())
          this.currentMetrics().forEach(m => {
            m.extendNames.forEach(ex => {
              ex.data = _data[ex.extendName]
              if (ex.displayOrder === 1 || ex.displayOrder === 4) {
                if (this.type() === 'day') {
                  ex.dt = _data[`${ex.extendName}${this.radioMap.day}`]
                  ex.yw = _data[`${ex.extendName}${this.radioMap.week}`]
                } else {
                  ex.ym = _data[`${ex.extendName}${this.radioMap.month}`]
                  ex.year = _data[`${ex.extendName}${this.radioMap.year}`]
                }
              }
            })
            if (this.dimensions() !== '2') {
              m.diff = m.extendNames[0].data - m.extendNames[1].data
            } else {
              m.diff = (m.extendNames[2].data - m.extendNames[3].data) * 100
            }
          })
        }
        console.log('lis==', this.currentMetrics())
        this.list.set(this.currentMetrics())
      })
  }

  changeCard(index) {
    this.index.set(index)
    this.checkValue.set([1])
    this.bizType.set('1')
    this.getChartData()
  }

  changeType() {
    if (!this.allMetrics()) {
      return
    }

    this.bizType.set('1')
    this.dealGroupDimension()
  }

  readonly dimensionMap = {
    '1': '按下、接、完看订单目标完成度',
    '2': '按生命周期看完单UV目标完成度',
    '3': '按业务渠道看自有完单进度',
    '4': '按拼车类型看自有完单进度',
    '5': '按端内端外额外增量看自有完单进度',
  }

  changeDimension() {
    if (!this.allMetrics()) {
      return
    }
    this.buriedPointService.addStat('dida_dpm_caerus_home_Operationaltab_click', {
      page_name: this.page_name,
      tab_name: this.dimensionMap[this.dimensions()],
    })

    if (this.dimensions() === '2') {
      this.type.set('month')
    } else {
      this.type.set('day')
    }

    this.bizType.set('1')
    this.dealGroupDimension()
  }

  private _setChartData(data: QueryOutputVo, diffData) {
    try {
      const chart = new ProgressTrend({ ...data, diffData })
      const legendItemClick = LegendItemClickHandler(this.legendControlService)
      chart.plotOptions.series.events = { legendItemClick }
      this.option.set(chart?.getOption() || null)
    } catch (e) {
      this.option.set(null)
      this.errorMessage.set(e)
      console.error(e)
    }
  }
}
