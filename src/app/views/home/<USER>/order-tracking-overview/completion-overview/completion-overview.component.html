<span class="flex items-center gap-x-1.5 font-black text-base pt-2">
  <MapArrowRightIcon />
  交易规模目标完成度监控
</span>

<div class="flex flex-col gap-1">
  <div class="flex flex-1 flex-wrap items-center gap-x-5 gap-y-1 pt-3 pb-1">
    <div class="flex items-center">
      <label class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap">
        统计口径：
      </label>
      <app-radio-group class="relative flex gap-1" [(ngModel)]="type" (ngModelChange)="changeType()">
        <app-radio class="tag-radio-new" activeClass="active" value="day" [disabled]="dimensions() === '2'">
          昨日
        </app-radio>
        <app-radio class="tag-radio-new" activeClass="active" value="month">当月累计</app-radio>
        <app-radio-thumb class="rounded-xs bg-primary" />
      </app-radio-group>
    </div>
    <div class="flex items-center">
      <label class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap">
        运营视角：
      </label>
      <app-radio-group class="relative flex gap-1" [(ngModel)]="dimensions" (ngModelChange)="changeDimension()">
        <app-radio class="tag-radio-new" activeClass="active" value="1">按下、接、完看订单目标完成度</app-radio>
        <app-radio class="tag-radio-new" activeClass="active" value="2">按生命周期看完单UV目标完成度</app-radio>
        <app-radio class="tag-radio-new" activeClass="active" value="3">按业务渠道看自有完单进度</app-radio>
        <app-radio class="tag-radio-new" activeClass="active" value="4">按拼车类型看自有完单进度</app-radio>
        <app-radio class="tag-radio-new" activeClass="active" value="5">按端内端外额外增量看自有完单进度</app-radio>
        <app-radio-thumb class="rounded-xs bg-primary" />
      </app-radio-group>
    </div>
  </div>
  <div class="flex flex-1 flex-wrap items-center gap-x-5 pb-3">
    @if (dimensions() === '2') {
      <div class="flex items-center">
        <label class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap">
          用户视角：
        </label>
        <app-radio-group class="relative flex gap-1" [(ngModel)]="viewType" (ngModelChange)="dealGroupDimension()">
          <app-radio class="tag-radio-new" activeClass="active" value="passenger">乘客视角</app-radio>
          <app-radio class="tag-radio-new" activeClass="active" value="driver">车主视角</app-radio>
          <app-radio-thumb class="rounded-xs bg-primary" />
        </app-radio-group>
      </div>
      <div>
        @if (viewType() === 'passenger') {
          <span class="text-neutral-400 text-[12px]">
            说明：乘客视角下，看的是
            <span class="text-blue-400">自有</span>
            的
            <span class="text-blue-400">完单乘客</span>
            ,以及目标达成情况。
          </span>
        } @else {
          <span class="text-neutral-400 text-[12px]">
            说明：车主视角下，看的是
            <span class="text-blue-400">全部</span>
            的
            <span class="text-blue-400">完单车主</span>
            ,以及目标达成情况。
          </span>
        }
      </div>
    }
  </div>

  <div class="flex flex-auto gap-5 h-135" [class]="dimensions() === '2' ? 'flex-col gap-2' : ''">
    @if (dimensions() === '2') {
      <div>
        <div class="flex flex-wrap w-full gap-y-5 px-1 py-3.5 customized-scrollbar overflow-x-auto">
          @if (cardLoading()) {
            <div class="flex items-center justify-center w-full">
              <app-line-spin />
            </div>
          } @else {
            <app-home-card
              class="min-w-360!"
              showMonthSubTitle
              [list]="list()"
              [display]="2"
              (updateIndex)="changeCard($event)"
              [themeArr]="['orange', 'orange', 'orange', 'orange', 'orange']"
              [compareType]="type() === 'day' ? 'month' : 'year'"
              [isTime]="true"
            ></app-home-card>
          }
        </div>
      </div>
    } @else {
      <div class="flex flex-wrap min-w-75 max-w-75 h-full gap-y-5">
        @if (cardLoading()) {
          <div class="flex items-center justify-center w-75">
            <app-line-spin />
          </div>
        } @else {
          <app-home-card
            [flex]="true"
            [list]="list()"
            (updateIndex)="changeCard($event)"
            [tooltip]="dimensions() !== '5' ? tooltip() : []"
            [compareType]="type() === 'day' ? 'month' : 'year'"
          ></app-home-card>
        }
      </div>
    }

    <div class="flex-1 min-w-0 flex flex-col h-full shadow-md rounded-sm border border-neutral-100 relative">
      <div class="flex gap-x-5 items-center pt-2 px-4">
        @if (dimensions() !== '1' && dimensions() !== '2') {
          <div class="flex items-center">
            <label class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap">
              指标选择：
            </label>
            <div>
              <nz-checkbox-group [(ngModel)]="checkValue" (ngModelChange)="getChartData()">
                @for (item of extraMetrics(); track $index) {
                  <ng-template #metricsTitleTemplate>
                    {{ item?.showName }}
                    <span class="text-xs opacity-30 px-1">({{ item?.aliasName }})</span>
                  </ng-template>

                  <label
                    nz-popover
                    nz-checkbox
                    class="ml-0! text-xs!"
                    [nzPopoverMouseEnterDelay]="0.5"
                    [nzPopoverTitle]="metricsTitleTemplate"
                    [nzPopoverContent]="contentTemplate"
                    [nzValue]="item.displayOrder"
                  >
                    <ng-template #contentTemplate>
                      <div [innerHTML]="item?.bizExpression"></div>
                    </ng-template>
                    {{ item.showName }}
                  </label>
                }
              </nz-checkbox-group>
            </div>
          </div>
        }
        @if (dimensions() !== '5' && dimensions() !== '2') {
          <div class="flex items-center">
            <label class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap">
              口径选择：
            </label>
            <app-radio-group class="relative flex gap-1" [(ngModel)]="bizType" (ngModelChange)="changeBizType()">
              <app-radio class="tag-radio-new" activeClass="active" value="1">业务口径</app-radio>
              <app-radio
                class="tag-radio-new"
                activeClass="active"
                value="2"
                [disabled]="!currentMetrics()[index() - 1]?.twobizExpression"
              >
                财务口径
              </app-radio>
              <app-radio-thumb class="rounded-xs bg-primary" />
            </app-radio-group>
          </div>
        }
      </div>
      <div class="flex-1 min-h-0 relative flex items-center justify-center text-xs">
        @if (loading()) {
          <app-line-spin />
        } @else {
          @if (option()) {
            <app-graph class="absolute inset-2" [options]="option()" showAnnotations showAvgPlotLines />
          } @else if (errorMessage()) {
            <span class="text-slate-600 text-xs">{{ errorMessage() }}</span>
          }
        }
      </div>
    </div>
  </div>
</div>
