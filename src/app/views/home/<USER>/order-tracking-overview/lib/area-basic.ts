import { XAxisOptions, YAxisOptions } from 'highcharts'
import { BaseHighCharts, SeriesItem } from '@common/chart/highcharts'
import { createPoint, createValueElement, isNotUndefined, toDecimals } from '@common/function'
import { QueryOutputHeaderVo, QueryOutputVo } from '@api/query-engine/model'
import { getCategories, getNumberFields, getPercentFields, isEveryElementPercent } from '@common/chart'
import { use } from 'echarts'
import { sortBy } from 'lodash'
import { format } from 'date-fns'

function tooltipFormatter(that) {
  return function (param: any) {
    const result = []
    const map = new Map()
    const params = this.points.sort(that.sortFn)

    params.forEach(item => {
      if (map.has(item.series.name)) {
        const arr = map.get(item.series.name) as any[]
        arr.push(item)
        arr.reverse()
      } else {
        map.set(item.series.name, [item])
      }
    })

    const merged = [...map.values()].flat(1)
    const arr = sortBy(merged, [
      function (m) {
        return m.series.name
      },
    ])
    result.push('<table class="text-sm">')

    arr.forEach((params, index) => {
      const {
        series: {
          name: seriesName,
          yAxis: { index: yAxisIndex },
        },
        y: value,
        x: categorie,
        color,
        point: { yw, dt, ym, dtType, year },
      } = params
      const { isPercent } = that.series.find(item => item.name === seriesName) ?? { isPercent: false }
      // const isDateStr = /\d{4}-\d{2}-\d{2}/.test(categorie)
      // const day = new Date(categorie.replace(/-/g, '/')).getDay()
      // const weeks = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
      // const week = isDateStr && weeks[day]

      if (index === 0) {
        result.push(`
          <tr>
            <td rowspan="2" class="px-4 text-center border-r border-b border-gray-200">指标</td>
            <td colspan="${dtType === 'dt' ? 2 : 3}" class="px-4 pb-1 text-center border-b border-gray-200">当前${dtType === 'dt' ? '日' : dtType === 'yw' ? '周' : '月'}: ${categorie}</td>
          </tr>
          <tr>
            <td class="px-4 text-center py-1 border-r border-b border-gray-200">数值</td>
            ${dtType === 'dt' ? `<td class="px-4 text-center py-1 border-r border-b border-gray-200">周同比</td>` : ''}
            ${dtType === 'yw' ? `<td class="px-4 text-center py-1 border-r border-b border-gray-200">周环比</td><td class="px-4 text-center py-1 border-r border-b border-gray-200">年同比</td>` : ''}
            ${dtType === 'ym' ? `<td class="px-4 text-center py-1 border-r border-b border-gray-200">月环比</td><td class="px-4 text-center py-1 border-r border-b border-gray-200">年同比</td>` : ''}
          </tr>
        `)
      }
      result.push(`
        <tr>
          <td class="px-4 text-center py-1 border-r border-gray-200 font-bold">
            ${seriesName
              .split('_userDefExtendName')
              [seriesName.split('_userDefExtendName').length - 1].replace('<', '&lt;')}:
          </td>

          <td class="text-cneter px-4 py-1 border-r border-gray-200">
            ${
              yAxisIndex === 1 || isPercent
                ? Number.isFinite(value)
                  ? value + '%'
                  : '-'
                : Number.isFinite(value)
                  ? Intl.NumberFormat().format(value)
                  : '-'
            }
          </td>
          <td class="text-cneter px-4 py-1 border-r border-gray-200">
            ${
              dtType === 'dt' ? (Number.isFinite(dt.diff) ? `${createValueElement(dt.diff * 100, '{n}pp')}` : '--') : ''
            }
            ${
              dtType === 'yw' ? (Number.isFinite(yw.diff) ? `${createValueElement(yw.diff * 100, '{n}pp')}` : '--') : ''
            }
            ${
              dtType === 'ym' ? (Number.isFinite(ym.diff) ? `${createValueElement(ym.diff * 100, '{n}pp')}` : '--') : ''
            }
           </td>
           ${
             dtType !== 'dt'
               ? `
               <td class="text-cneter px-4 py-1 border-r border-gray-200">
                ${Number.isFinite(year.diff) ? `${createValueElement(year.diff * 100, '{n}pp')}` : '--'}
               </td>
            `
               : ''
           }

        </tr>
      `)
    })
    result.push('</table>')

    return result.join('')
  }
}

class xAxisItem {
  categories: string[]
  opposite: boolean
  tickInterval = 1
  tickWidth = 1
  tickColor = '#ccd6eb'
  lineColor = '#ccd6eb'
  gridLineColor = '#e6e6e6'
  crosshair = true
  labels = {
    useHTML: true,
    formatter: function () {
      if (/\d{4}-\d{2}-\d{2}/.test(this.value)) {
        const day = new Date(this.value.replace(/-/g, '/')).getDay()
        if (day === 0 || day === 6) {
          return `<span class="text-primary font-semibold">${this.value}</span>`
        }
      }

      return this.value
    },
  }

  constructor({ categories, opposite }: Partial<xAxisItem>) {
    categories && (this.categories = categories)
    opposite && (this.opposite = opposite)
  }
}

export class AreaBasic extends BaseHighCharts {
  responsive = {
    rules: [
      {
        condition: { maxWidth: 1200 },
        chartOptions: {
          xAxis: [{ tickInterval: 2 }],
        },
      },
      {
        condition: { maxWidth: 800 },
        chartOptions: {
          xAxis: [{ tickInterval: 4 }],
        },
      },
      {
        condition: { maxWidth: 500 },
        chartOptions: {
          xAxis: [{ tickInterval: 6 }],
        },
      },
    ],
  }

  xAxis: XAxisOptions[] = []
  yAxis: YAxisOptions[] = [
    {
      title: { text: '' },
      labels: { format: undefined },
      gridLineWidth: 1,
      gridLineColor: '#e6e6e6',
    },
  ]
  colors = [
    'rgba(80,135,236,0.7)',
    'rgba(238,117,47,0.7)',
    'rgba(104,187,196,0.7)',
    'rgba(88,165,92,0.7)',
    'rgba(242,189,66,0.7)',
    'rgba(238,117,47,0.7)',
    'rgba(176,102,93,0.7)',
    'rgba(228,196,119,0.7)',
    'rgba(163,194,251,0.7)',
    'rgba(160,208,213,0.7)',
    'rgba(152,179,226,0.7)',
    'rgba(222,134,143,0.7)',
    'rgba(244,206,152,0.7)',
    'rgba(180,200,217,0.7)',
    'rgba(147,210,243,0.7)',
    'rgba(64,149,229,0.7)',
    'rgba(127,131,247,0.7)',
    '#E99D42',
    '#CBA43F',
    '#BFBF3D',
    '#81B337',
    '#347CAF',
    '#377F7F',
    '#FCCA00',
    '#B886F8',
    '#A16222',
    '#5087EC',
    '#68BBC4',
    '#58A55C',
    '#F2BD42',
    '#EE752F',
    '#B0665D',
    '#E4C477',
    '#A3C2FB',
    '#A0D0D5',
    '#98B3E2',
    '#DE868F',
    '#F4CE98',
    '#B4C8D9',
    '#93D2F3',
    '#4095E5',
    '#7F83F7',
  ]

  plotOptions: any = {
    series: {
      turboThreshold: 999999999,
      marker: {
        radius: 3,
        symbol: 'circle',
      },
    },
    line: {
      marker: {
        enabled: false,
        symbol: 'circle',
        radius: 2,
        states: {
          hover: {
            enabled: true,
          },
        },
      },
    },
  }

  constructor(properties: QueryOutputVo) {
    super()

    const { headers, data } = properties
    let dtType = 'dt'
    if (headers['yw']) {
      dtType = 'yw'
    }
    if (headers['ym']) {
      dtType = 'ym'
    }
    const categories = getCategories(headers, data)
    const series = this.getSeries(headers, data, dtType)
    const [percentFields] = getPercentFields(headers)
    const isEveryFieldPercent = isEveryElementPercent(headers)
    const _series = sortBy(series, [
      function (m) {
        return m.name
      },
    ])
    if (isEveryFieldPercent) {
      this.yAxis = [
        {
          title: { text: '' },
          labels: { format: '{text}%' },
          gridLineWidth: 1,
          gridLineColor: '#e6e6e6',
        },
      ]
    } else if (percentFields) {
      this.yAxis.push({
        title: { text: '' },
        labels: { format: '{text}%' },
        gridLineWidth: 1,
        gridLineColor: '#e6e6e6',
        opposite: true,
      })
    }

    this.setCategories([categories], dtType)
    this.setSeries(_series)
    this.legend.verticalAlign = 'top'
    this.chart.type = 'line'
  }

  getSeries(headers: { [key: string]: QueryOutputHeaderVo }, data: { [key: string]: string }[], dtType) {
    const numberFields = getNumberFields(headers)
    const isEveryFieldPercent = isEveryElementPercent(headers)

    return numberFields.map(key => {
      const series = new SeriesItem()
      const { aliasName, dataUnit, extendName } = headers[key]
      // series.type = 'area'
      if (dataUnit === '%') {
        series.isPercent = true
      }

      if (dataUnit === '%' && !isEveryFieldPercent) {
        series.yAxis = 1
      }

      if (extendName.includes('dida_benchmark_book_ord_amt')) {
        series.dashStyle = 'ShortDash'
        series.marker = {
          enabled: false,
        }
      }

      series.name = aliasName

      series.dataUnit = dataUnit
      // series.fillOpacity = 0.2
      series.data = data.map(item => {
        const numeric = Number(item[key])
        let value: number = null

        if (item[key] === null) {
          value = null
        } else if (dataUnit === '%') {
          value = toDecimals(numeric)
        } else {
          value = numeric
        }

        return {
          y: value,
          yw: {
            diff: +item[`${key}:yw_DIFF`],
            ratio: +item[`${key}:yw_DIFF_RATIO`],
          },
          dt: {
            diff: +item[`${key}:dt_DIFF`],
            ratio: +item[`${key}:dt_DIFF_RATIO`],
          },
          ym: {
            diff: +item[`${key}:ym_DIFF`],
            ratio: +item[`${key}:ym_DIFF_RATIO`],
          },
          year: {
            diff: +item[`${key}:year_DIFF`],
            ratio: +item[`${key}:year_DIFF_RATIO`],
          },
          dtType,
        }
      })

      return series
    })
  }

  getISOWeekDate = (year, week) => {
    const janFirst = new Date(year, 0, 1)
    const janFirstDay = janFirst.getDay() || 7
    const daysToFirstMonday = janFirstDay === 1 ? 0 : 8 - janFirstDay
    const start = new Date(year, 0, 1 + daysToFirstMonday + (week - 1) * 7)
    const end = new Date(start)
    start.setDate(start.getDate() - 6)
    return { start, end }
  }

  setCategories(values: string[][], dtType): void {
    values.forEach((categories, index) => {
      let _categories = categories
      if (dtType === 'yw') {
        _categories = categories.map(c => {
          const [year, week] = c.split('-')
          const { start, end } = this.getISOWeekDate(year, week)
          const s = format(start, 'MM.dd')
          const e = format(end, 'MM.dd')
          return `${year}年第${week}周(${s}-${e})`
        })
      }
      this.xAxis.push(new xAxisItem({ categories: _categories, opposite: index > 0 }))
    })
  }

  override getOption() {
    const value = this

    return {
      ...value,
      tooltip: {
        ...value.tooltip,
        formatter: tooltipFormatter(this),
      },
      plotOptions: {
        ...value.plotOptions,
        series: {
          ...value.plotOptions.series,
          // events: {
          //   legendItemClick: handleLegendItemClick(this),
          // }
        },
      },
    }
  }
}
