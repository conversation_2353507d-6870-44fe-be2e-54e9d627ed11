import { ChangeDetectionStrategy, Component } from '@angular/core'
import { HeadingComponent } from '@shared/components/heading'
import { IconMoreSquareComponent } from '@shared/modules/icons'
import { CompletionOverviewComponent } from './completion-overview/completion-overview.component'
import { MarketingQualityComponent } from './marketing-quality/marketing-quality.component'

@Component({
  selector: 'app-order-tracking-overview',
  templateUrl: './order-tracking-overview.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: {
    class: 'block px-5',
  },
  imports: [HeadingComponent, IconMoreSquareComponent, CompletionOverviewComponent, MarketingQualityComponent],
})
export class OrderTrackingOverviewComponent {}
