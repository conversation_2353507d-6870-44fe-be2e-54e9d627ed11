<span class="flex items-center gap-x-1.5 font-black text-base">
  <MapArrowRightIcon />
  端外推广质量分析
</span>

<div class="flex gap-x-5 w-full mt-3">
  <div class="flex-1 flex flex-col gap-2.5">
    <nz-spin [nzSpinning]="metricsDataResource.isLoading() || metricsTrendResource.isLoading()">
      <app-indicator-card-group class="group relative p-3.5 py-6 rounded-2xl card-theme orange">
        <ExpandIcon class="absolute! right-3 top-3 expand-btn" iconBtn nz-tooltip="查看" (click)="openMetricsDetail(passMetrics())" />

        @for (item of passMetrics(); track item) {
          <app-indicator-card>
            <app-indicator-content>
              <app-indicator-value-group vertical>
                <ng-template #metricsTitleTemplate>
                  {{item?.showName}} <span class="text-xs opacity-30 px-1">({{item?.aliasName}})</span>
                </ng-template>
                <app-indicator-title
                  class="scale-70"
                  nzPopoverPlacement="topLeft"
                  [nzPopoverTitle]="metricsTitleTemplate"
                  [nzPopoverContent]="contentTemplate"
                >
                  <ng-template #contentTemplate>
                    <div [innerHTML]="item?.bizExpression"></div>
                  </ng-template>
                  {{item.showName}}
                  <span class="text-xs scale-75 origin-bottom opacity-50">
                    {{avgTypeMap.get(item.tagName) || item.tagName}}
                  </span>
                </app-indicator-title>
                <app-indicator-value-group class="justify-center" alignItems="items-end" gap="gap-x-1">
                  <app-indicator-value [value]="(valueMap.get(item.extendName)?.value) | increment | async" />
                  <!-- <app-indicator-value-unit>元</app-indicator-value-unit> -->
                </app-indicator-value-group>
              </app-indicator-value-group>
              <app-indicator-compare-group vertical>
                <app-indicator-compare iconVisible label="月环比" [value]="valueMap.get(item.extendName)?.ym_DIFF_RATIO" />
                <app-indicator-compare iconVisible label="年同比" [value]="valueMap.get(item.extendName)?.year_DIFF_RATIO" />
              </app-indicator-compare-group>
              <app-indicator-graph class="block h-20 bg-neutral-200/0 rounded-lg">
                <app-graph-mini-area [value]="valueMap.get(item.extendName)?.trendVo" />
              </app-indicator-graph>
            </app-indicator-content>
          </app-indicator-card>
        }
      </app-indicator-card-group>
    </nz-spin>
  </div>

  <div class="flex-1 flex flex-col gap-2.5">
    <nz-spin [nzSpinning]="metricsDataResource.isLoading() || metricsTrendResource.isLoading()">
      <app-indicator-card-group class="group relative p-3.5 py-6 rounded-2xl card-theme blue">
        <ExpandIcon class="absolute! right-3 top-3 expand-btn" iconBtn nz-tooltip="查看" (click)="openMetricsDetail(driverMetrics())" />

        @for (item of driverMetrics(); track item) {
          <app-indicator-card>
            <app-indicator-content>
              <app-indicator-value-group vertical>
                <ng-template #metricsTitleTemplate>
                  {{item?.showName}} <span class="text-xs opacity-30 px-1">({{item?.aliasName}})</span>
                </ng-template>
                <app-indicator-title
                  class="scale-70"
                  nzPopoverPlacement="topLeft"
                  [nzPopoverTitle]="metricsTitleTemplate"
                  [nzPopoverContent]="contentTemplate"
                >
                  <ng-template #contentTemplate>
                    <div [innerHTML]="item?.bizExpression"></div>
                  </ng-template>
                  {{item.showName}}
                  <span class="text-xs scale-75 origin-bottom opacity-50">
                    {{avgTypeMap.get(item.tagName) || item.tagName}}
                  </span>
                </app-indicator-title>
                <app-indicator-value-group class="justify-center" alignItems="items-end" gap="gap-x-1">
                  <app-indicator-value [value]="(valueMap.get(item.extendName)?.value) | increment | async" />
                  <!-- @if (item.dataUnit === '元') {
                    <app-indicator-value-unit>元</app-indicator-value-unit>
                  } -->
                </app-indicator-value-group>
              </app-indicator-value-group>
              <app-indicator-compare-group vertical>
                <app-indicator-compare iconVisible label="月环比" [value]="valueMap.get(item.extendName)?.ym_DIFF_RATIO" />
                <app-indicator-compare iconVisible label="年同比" [value]="valueMap.get(item.extendName)?.year_DIFF_RATIO" />
              </app-indicator-compare-group>
              <app-indicator-graph class="block h-20 bg-neutral-200/0 rounded-lg">
                <app-graph-mini-area [value]="valueMap.get(item.extendName)?.trendVo" />
              </app-indicator-graph>
            </app-indicator-content>
          </app-indicator-card>
        }
      </app-indicator-card-group>
    </nz-spin>
  </div>
</div>