
<div [@opacityAnimation]="visible()" class="modal-backdrop"></div>

<div class="modal">
  <div class="absolute inset-0 z-0" (click)="close()"></div>
  <div [@modalOutToInAnimation]="visibleState()" (@modalOutToInAnimation.done)="animationDone($event)" class="relative z-10 m-auto">
    <div cdkDragBoundary=".modal" cdkDrag class="modal-content max-w-unset! w-256">
      <header cdkDragHandle class="modal-header">
        <h3 class="modal-title">{{data?.metrics?.at(0)?.showName}}</h3>

        <div class="flex items-center gap-x-5 text-xs pl-6">
          <div>
            <nz-select class="w-28 mr-2!" [(ngModel)]="dateType" (ngModelChange)="onDateTypeChange($event)">
              @for (item of unitOptions(); track $index) {
                <nz-option [nzLabel]="item.label" [nzValue]="item.value" [nzDisabled]="item.disabled"></nz-option>
              }
            </nz-select>

            <nz-date-picker [nzShowToday]="false" [nzMode]="dateMode()" [nzFormat]="dateFormat()" [nzDisabledDate]="dateMode() === 'month' ? disabledMonth : disabledDate" [(ngModel)]="date" />
          </div>
        </div>

        <CloseIcon iconBtn class="xl ml-auto" (click)="close()" />
      </header>
      
      <div class="modal-body">
        <app-radio-group class="relative flex items-start gap-x-0.5 pt-2" [(ngModel)]="metric">
          @for (metric of this.data.metrics; track metric) {
            <app-radio class="line-radio" [value]="metric">{{metric.aliasName}}</app-radio>
          }
          <app-radio-thumb class="border-primary border-b-2" />
        </app-radio-group>

        <div class="relative h-132">
          @if (metricsDataResource.error() || metricsTrendResource.error()) {
            <div class="flex items-center justify-center h-full">
              <span class="text-slate-600 text-xs">{{ metricsDataResource.error() || metricsTrendResource.error() }}</span>
            </div>
          }
          
          <nz-spin [nzSpinning]="metricsDataResource.isLoading()">
            @if (!metricsDataResource.error() && metric()) {
              <div class="flex items-center justify-center gap-x-1 w-full">
                @for (item of [metric()]; track $index) {
                  @let extendName = item.extendName;
                  @let dataUnit = data.metrics?.at(0)?.dataUnit;
                  <app-indicator-content class="max-w-1/4 pt-7 pb-5">
                    <app-indicator-header class="justify-center">
                      <app-indicator-value class="py-3" [value]="(valueMap.get(extendName)?.value) | increment | async" [suffix]="(dataUnit === '无单位' || dataUnit === '个') ? '' : dataUnit" />
                      <app-indicator-subtitle class="-translate-y-1/2">
                        {{avgTypeMap.get(item.tagName) || item.tagName}}
                      </app-indicator-subtitle>
                    </app-indicator-header>
                    <app-indicator-compare-group gap="gap-1">
                      @switch (dateType()) {
                        @case ('dt') {
                          <app-indicator-compare iconVisible label="日环比" [value]="valueMap.get(extendName)?.dt_DIFF_RATIO" />
                          <app-indicator-compare iconVisible label="周同比" [value]="valueMap.get(extendName)?.yw_DIFF_RATIO" />
                        }
                        @case ('yw') {
                          <app-indicator-compare iconVisible label="周环比" [value]="valueMap.get(extendName)?.yw_DIFF_RATIO" />
                          <app-indicator-compare iconVisible label="年同比" [value]="valueMap.get(extendName)?.year_DIFF_RATIO" />
                        }
                        @case ('ym') {
                          <app-indicator-compare iconVisible label="月环比" [value]="valueMap.get(extendName)?.ym_DIFF_RATIO" />
                          <app-indicator-compare iconVisible label="年同比" [value]="valueMap.get(extendName)?.year_DIFF_RATIO" />
                        }
                      }
                    </app-indicator-compare-group>
                  </app-indicator-content>
                }
              </div>
            }
          </nz-spin>

          <nz-spin [nzSpinning]="metricsTrendResource.isLoading()">
            @if (!metricsTrendResource.error()) {
              <div class="relative w-full h-100 bg-neutral-100/0 rounded-lg">
                <!-- [isPercent]="metrics()[index()]?.percent"  -->
                @if (metricsTrendResource.value()) {
                  <app-metrics-chart [value]="$any(metricsTrendResource.value())" [trendTilte]="trendTilte()" />
                }
              </div>
            }
          </nz-spin>
        </div>
      </div>
    </div>
  </div>
</div>