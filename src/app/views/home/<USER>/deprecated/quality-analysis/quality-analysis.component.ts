import { AsyncPipe, DatePipe } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, computed, effect, inject, ViewContainerRef } from '@angular/core';
import { rxResource } from '@angular/core/rxjs-interop';
import { NzSpinComponent } from 'ng-zorro-antd/spin';
import { NzTooltipDirective } from 'ng-zorro-antd/tooltip';
import { subYears, format } from 'date-fns';
import { map, tap, of } from 'rxjs';

import { ModalService } from '@core/dialog';
import { MetricsMenuVo } from '@api/caerus/model';
import { CaerusApiService } from '@api/caerus';
import { QueryEngineApiService } from '@api/query-engine';
import { groupBy, isEmpty, toNumber } from '@common/function';
import { QueryInputVo } from '@api/query-engine/model';
import { IconExpandComponent, IconMapArrowRightComponent } from '@shared/modules/icons';
import { IndicatorModule } from '@shared/components/indicator';
import { GraphMiniAreaComponent } from '@shared/components/graph';
import { IncrementPipe } from '@shared/pipes/increment';
import { HomeService } from '@views/home';

import { MetricsDetailComponent } from './metrics-detail';


@Component({
  selector: 'app-quality-analysis',
  templateUrl: './quality-analysis.component.html',
  host: {
    'id': 'offplt-promt'
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    AsyncPipe,
    NzSpinComponent,
    NzTooltipDirective,
    IndicatorModule,
    GraphMiniAreaComponent,
    IconMapArrowRightComponent,
    IconExpandComponent,
    IncrementPipe,
  ],
  providers: [
    DatePipe,
  ],
})
export class QualityAnalysisComponent {

  readonly datePipe = inject(DatePipe);
  readonly cdr = inject(ChangeDetectorRef);
  readonly viewContainerRef = inject(ViewContainerRef);
  readonly caerusApiService = inject(CaerusApiService);
  readonly apiService = inject(QueryEngineApiService);
  readonly modalService = inject(ModalService);
  readonly service = inject(HomeService);

  #metrics = computed(() => {
    return this.metricsConfigResource.value()?.map(({extendName}) => ({ extendName }));
  });

  passMetrics = computed(() => {
    return this.metricsConfigResource.value()?.filter(item => item.tagOrder === 1);
  });

  driverMetrics = computed(() => {
    return this.metricsConfigResource.value()?.filter(item => item.tagOrder === 2);
  });
  
  metricsConfigResource = rxResource({
    loader: () => this.caerusApiService.fetchMetricsConfig('cockpit_outplt_mkt_business').pipe(
      map(res => res.data['quality']?.subMetric),
      tap(res => {
        console.log('[端外推广-result]', res);
      })
    )
  });

  valueMap = new Map();
  avgTypeMap = new Map([
    ['month_avg', '月日均'],
    ['cur_month', '当月']
  ]);

  metricsDataBody = computed(() => {
    const month = this.datePipe.transform(this.service.month(), 'yyyy-MM');
    const dt = { startTime: month, endTime: month };
    const dimensions = [{ id: null, extendName: 'ym', predefineCompareType: ['ym', 'year'] }];

    return {
      dt,
      dtType: 'ym',
      metrics: this.#metrics(),
      dimensions,
      filter: { items: [], type: null },
      scene: 4,
      dataFillConfig: { open: 1, fillRangeType: 1, fillValue: 1 },
    } as QueryInputVo;
  })

  metricsTrendBody = computed(() => {
    const startTime = format(subYears(this.service.month(), 2).setMonth(0), 'yyyy-MM');
    const endTime = format(this.service.month(), 'yyyy-MM');
    const dt = { startTime, endTime }; // 近2年
    const dimensions = [{ id: null, extendName: 'ym' }];

    return {
      dt,
      dtType: 'ym',
      metrics: this.#metrics(),
      dimensions,
      filter: { items: [], type: null },
      scene: 4,
      dataFillConfig: { open: 1, fillRangeType: 1, fillValue: 1 },
    } as QueryInputVo;
  })
  
  metricsDataResource = rxResource({
    request: () => this.metricsDataBody(),
    loader: ({ request }) => isEmpty(request.metrics) 
      ? of(null)
      : this.apiService.search(request, 'internal-marketing-quality-analysis-metrics-overview').pipe(
        tap(res => {
          if (res.status !== '00000') {
            throw res.message;
          }
        }),
        map(res => res.data?.data?.at(0)),
        tap(value => {
          Object.keys(value).forEach(key => {
            if (key !== 'ym' && key.indexOf(':') === -1) {
              const target = this.valueMap.get(key);

              this.valueMap.set(key, { ...target, value: parseFloat(value[key]) || null });
            }
            else if (key.indexOf(':') > -1) {
              const [k, v] = key.split(':');
              const target = this.valueMap.get(k);

              this.valueMap.set(k, {  ...target, [v]: parseFloat(value[key]) || null });
            }
          })

          this.cdr.markForCheck();
        })
      )
  })

  metricsTrendResource = rxResource({
    request: () => this.metricsTrendBody(),
    loader: ({ request }) => isEmpty(request.metrics) 
      ? of(null) 
      : this.apiService.search(request, 'internal-marketing-quality-analysis-metrics-trend').pipe(
        tap(res => {
          if (res.status !== '00000') {
            throw res.message;
          }
        }),
        map(res => res.data.data),
      )
  })

  constructor() {
    effect(() => {
      if (
        this.metricsDataResource.value() &&
        this.metricsTrendResource.value()
      ) {
        const values = this.metricsTrendResource.value();

        this.valueMap.forEach((value) => {
          value.trendVo = [];
        })
        
        values.forEach(item => {
          const [year, month] = item['ym'].split('-').map(toNumber);
          Object.keys(item).filter(key => key !== 'ym').forEach(key => {
            const target = this.valueMap.get(key);
            const trendVo = target.trendVo || [];
            const value = parseFloat(item[key]) || null;

            trendVo.push({ year, month, value })
            this.valueMap.set(key, {  ...target, trendVo });
          });
        })

        this.cdr.markForCheck();
      }
    })
  }

  openMetricsDetail(metrics: MetricsMenuVo[]) {
    console.clear();
    this.modalService.open(MetricsDetailComponent, { 
      viewContainerRef: this.viewContainerRef,
      data: {
        metrics,
      }
    });
  }

}
