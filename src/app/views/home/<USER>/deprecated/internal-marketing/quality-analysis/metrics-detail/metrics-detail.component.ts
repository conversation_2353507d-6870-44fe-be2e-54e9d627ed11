import { AsyncPipe, DatePipe } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, computed, inject, signal } from '@angular/core';
import { differenceInCalendarDays, addDays, differenceInCalendarMonths, addMonths, subYears, format } from 'date-fns';
import { FormsModule } from '@angular/forms';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { AnimationEvent } from '@angular/animations';
import { rxResource } from '@angular/core/rxjs-interop';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { map, of, tap } from 'rxjs';


import { Animations } from '@common/animation';
import { _pad, isEmpty, toDecimals, toNumber } from '@common/function';
import { QueryEngineApiService } from '@api/query-engine';
import { MetricVo, QueryInputVo } from '@api/query-engine/model';
import { ModalVisibleState } from '@core/modal';
import { ModalRef, MODAL_DATA } from '@core/dialog';
import { IconCloseComponent } from '@shared/modules/icons';
import { IndicatorModule } from '@shared/components/indicator';
import { IncrementPipe } from '@shared/pipes/increment';
import { MetricsChartComponent } from '@views/cockpit/components/metrics-detail';
import { HomeService } from '@views/home';


@Component({
  selector: 'app-metrics-detail',
  templateUrl: './metrics-detail.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: {
    'class': 'flex w-screen h-screen'
  },
  animations: [
    ...Animations.OpacityAnimation,
    ...Animations.ModalOutToInAnimation,
  ],
  imports: [
    AsyncPipe,
    FormsModule,
    DragDropModule,
    NzSelectModule,
    NzCheckboxModule,
    NzSpinModule,
    NzDatePickerModule,
    MetricsChartComponent,
    IndicatorModule,
    IconCloseComponent,
    IncrementPipe,
  ],
  providers: [
    DatePipe,
  ],
})
export class MetricsDetailComponent implements AfterViewInit {

  readonly datePipe = inject(DatePipe);
  readonly cdr = inject(ChangeDetectorRef);
  readonly modalRef = inject(ModalRef);
  readonly service = inject(HomeService);
  readonly apiService = inject(QueryEngineApiService);
  

  readonly data = inject<{
    metrics: MetricVo[],
  }>(MODAL_DATA);

  visible = signal(false);
  visibleState = signal<ModalVisibleState>('invisible');

  today = signal(new Date());
  date = signal(this.service.month());
  dateType = signal<'dt' | 'yw' | 'ym'>('ym');
  supports = signal(['day', 'week', 'month']);
  trendTilte = computed(() => [...new Set(this.titles())]);
  titles = signal([]);

  dateMode = computed(() => {
    switch(this.dateType()) {
      case 'dt':
        return null;
      case 'yw':
        return 'week';
      case 'ym':
        return 'month';
    }
  })

  dateFormat = computed(() => {
    switch(this.dateType()) {
      case 'dt':
        return 'yyyy/MM/dd';
      case 'yw':
        return 'YYYY第ww周';
      case 'ym':
        return 'yyyy/MM';
    }
  })

  format = computed(() => {
    switch(this.dateType()) {
      case 'dt':
        return 'yyyy-MM-dd';
      case 'yw':
        return 'YYYY-ww';
      case 'ym':
        return 'yyyy-MM';
    }
  })

  unitOptions = computed(() => {
    const supports = this.supports();

    return [
      { label: '日', value: 'dt', disabled: !supports.includes('day') },
      { label: '周', value: 'yw', disabled: !supports.includes('week') },
      { label: '月', value: 'ym', disabled: !supports.includes('month') },
    ];
  });

  #filters = computed(() => {
    return {
      items: [],
      type: null,
    }
  })

  valueMap = new Map();
  avgTypeMap = new Map([
    ['month_avg', '月日均'],
    ['cur_month', '当月']
  ]);

  metricsDataBody = computed(() => {
    let metrics = this.data.metrics;

    const month = this.datePipe.transform(this.date(), this.format());
    const dt = { startTime: month, endTime: month };
    const dimensions = [{
      id: null, 
      extendName: this.dateType(), 
      predefineCompareType: [
        this.dateType(), 
        this.dateType() === 'dt'
          ? 'yw'
          : 'year'
      ]
    }];
    const body: QueryInputVo = {
      dt,
      dtType: this.dateType(),
      dimensions,
      filter: this.#filters(),
      scene: 4,
      dataFillConfig: { open: 1, fillRangeType: 1, fillValue: 1 },
    };

    body.metrics = metrics;

    return body;
  })

  metricsTrendBody = computed(() => {
    let metrics = this.data.metrics;

    const startTime = new Date(subYears(this.date(), 2).setMonth(0)).setDate(1);
    const endTime = format(this.date(), this.format());
    const dt = { startTime: format(startTime, this.format()), endTime }; // 近2年
    const dimensions = [{ id: null, extendName: this.dateType() }];
    const body: QueryInputVo = {
      dt,
      dtType: this.dateType(),
      dimensions,
      filter: this.#filters(),
      scene: 4,
      dataFillConfig: { open: 1, fillRangeType: 1, fillValue: 1 },
    };

    body.metrics = metrics;

    return body;
  })

  metricsDataResource = rxResource({
    request: () => this.metricsDataBody(),
    loader: ({ request }) => isEmpty(request.metrics) 
      ? of(null)
      : this.apiService.search(request, 'metrics-overview').pipe(
        tap(res => {
          if (res.status !== '00000') {
            throw res.message;
          }
        }),
        map(res => res.data?.data?.at(0)),
        tap(value => {
          const dtType = this.dateType();
          const isPercent = this.data.metrics.at(0).dataUnit === '%';

          Object.keys(value).forEach(key => {
            if (key !== dtType && key.indexOf(':') === -1) {
              let val: string | number = parseFloat(value[key]) || null;
              const target = this.valueMap.get(key);

              if (key === 'A3') {
                val = val === null ? null : toDecimals(val) + '%';
              }

              this.valueMap.set(key, { ...target, value: (
                val === null 
                  ? null 
                  : isPercent && typeof val === 'number'
                    ? toDecimals(val)
                    : val
              ) });
            }
            else if (key.indexOf(':') > -1) {
              const [k, v] = key.split(':');
              const target = this.valueMap.get(k);
              const val = parseFloat(value[key]) || null;
              
              this.valueMap.set(k, {  ...target, [v]: (
                val === null 
                  ? null 
                  : isPercent
                    ? toDecimals(val)
                    : val
              ) });
            }
          })

          this.cdr.markForCheck();
        })
      )
  })

  metricsTrendResource = rxResource({
    request: () => this.metricsTrendBody(),
    loader: ({ request }) => isEmpty(request.metrics) 
      ? of(null) 
      : this.apiService.search(request, 'metrics-trend').pipe(
        tap(res => {
          this.titles.set([]);
          if (res.status !== '00000') {
            throw res.message;
          }
        }),
        map(res => res.data.data),
        map(values => {
          const dtType = this.dateType();
          const isPercent = this.data.metrics.at(0).dataUnit === '%';

          return values.map(item => {
            const [year, month, day] = item[dtType].split('-').map(toNumber);
            return Object.keys(item).filter(key => key !== dtType).map(key => {
              const value = parseFloat(item[key]) || null;
              const val = (value === null 
                ? null 
                : isPercent
                  ? toDecimals(value)
                  : value
              )
  
              this.updateTitles({ year, month, day });
              return { year, month, day, value: val };
            });
          }).flat(1);
        })
      )
  })


  ngAfterViewInit(): void {
    // console.log(this.data);
    const { tagName } = this.data.metrics.at(0);

    this.visible.set(true);
    this.visibleState.set('visible');

    if (tagName === 'cur_month') {
      this.supports.set(['month']);
    }
  }
  
  
  disabledDate = (current: Date): boolean => {
    // Can not select days before today and today
    return differenceInCalendarDays(current, this.today()) > -1;
  }


  disabledMonth = (current: Date): boolean => {
    // Can not select days before today and today
    const startDateForMonth = new Date().setDate(1);
    const dateRight = addMonths(startDateForMonth, 1);
    
    return differenceInCalendarMonths(current, dateRight) > -1;
  }


  animationDone(event: AnimationEvent): void {
    if (event.fromState === 'visible' && event.toState === 'leave') {
      this.modalRef.close();
    }
  }


  close() {
    this.visible.set(false);
    this.visibleState.set('leave');
  }

  
  updateTitles({ year, month, day }) {
    const dtType = this.dateType();
    if (dtType === 'dt') {
      this.titles.update(titles => titles.concat(`${_pad(month)}-${_pad(day)}`));

    } else if (dtType === 'yw') {
      this.titles.update(titles => titles.concat(`第${month}周`));

    } else {
      this.titles.update(titles => titles.concat(month + '月'));
    }
  }
  

  onDateTypeChange(value: 'dt' | 'yw' | 'ym') {
    const now = new Date().setHours(0, 0, 0, 0);

    switch (value) {
      case 'ym':
        this.date.set(this.service.month());
        break;
      case 'yw':
      case 'dt':
        this.date.set(addDays(now, -1));
        break;
      default:
        this.date.set(null);
        break;
    }
  }

}
