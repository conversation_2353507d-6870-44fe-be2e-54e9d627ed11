import { AsyncPipe, DatePipe } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, computed, effect, inject, signal, ViewContainerRef } from '@angular/core';
import { subYears, format } from 'date-fns';
import { FormsModule } from '@angular/forms';
import { rxResource } from '@angular/core/rxjs-interop';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzSpinComponent } from 'ng-zorro-antd/spin';
import { map, of, tap } from 'rxjs';

import { ModalService } from '@core/dialog';
import { QueryEngineApiService } from '@api/query-engine';
import { MetricsMenuVo } from '@api/caerus/model';
import { QueryInputVo } from '@api/query-engine/model';
import { CaerusApiService } from '@api/caerus';
import { isEmpty, toDecimals, toNumber } from '@common/function';
import { RadioModule } from '@shared/modules/headless';
import { IconExpandComponent, IconMapArrowRightComponent } from '@shared/modules/icons';
import { GraphMiniAreaComponent } from '@shared/components/graph';
import { IndicatorModule } from '@shared/components/indicator';
import { IncrementPipe } from '@shared/pipes/increment';
import { HomeService } from '@views/home';

import { MetricsDetailComponent } from './metrics-detail';


@Component({
  selector: 'app-quality-analysis',
  templateUrl: './quality-analysis.component.html',
  host: {
    id: 'internal-marketing'
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    AsyncPipe,
    FormsModule,
    NzToolTipModule,
    NzSpinComponent,
    IndicatorModule,
    RadioModule,
    GraphMiniAreaComponent,
    IconMapArrowRightComponent,
    IconExpandComponent,
    IncrementPipe,
  ],
  providers: [
    DatePipe,
  ],
})
export class QualityAnalysisComponent {

  readonly datePipe = inject(DatePipe);
  readonly cdr = inject(ChangeDetectorRef);
  readonly viewContainerRef = inject(ViewContainerRef);
  readonly caerusApiService = inject(CaerusApiService);
  readonly apiService = inject(QueryEngineApiService);
  readonly modalService = inject(ModalService);
  readonly service = inject(HomeService);

  type = signal<'quality_pass' | 'quality_driver'>('quality_pass');

  #metrics = computed(() => {
    return this.metricsConfigResource.value()?.map(({extendName, dataUnit}) => ({
      extendName, 
      dataUnit
    }));
  });
  
  metricsConfigResource = rxResource({
    request: () => this.type(),
    loader: ({ request }) => this.caerusApiService.fetchMetricsConfig('cockpit_onplt_mkt_business').pipe(
      map(res => res.data[request]?.subMetric),
    )
  });

  valueMap = new Map();
  avgTypeMap = new Map([
    ['month_avg', '月日均'],
    ['cur_month', '当月']
  ]);

  metricsDataBody = computed(() => {
    const month = this.datePipe.transform(this.service.month(), 'yyyy-MM');
    const dt = { startTime: month, endTime: month };
    const dimensions = [{ id: null, extendName: 'ym', predefineCompareType: ['ym', 'year'] }];

    return {
      dt,
      dtType: 'ym',
      metrics: this.#metrics(),
      dimensions,
      filter: { items: [], type: null },
      scene: 4,
      dataFillConfig: { open: 1, fillRangeType: 1, fillValue: 1 },
    } as QueryInputVo;
  })

  metricsTrendBody = computed(() => {
    const startTime = format(new Date(subYears(this.service.month(), 2).setMonth(0)).setDate(1), 'yyyy-MM');
    const endTime = format(this.service.month(), 'yyyy-MM');
    const dt = { startTime, endTime }; // 近2年
    const dimensions = [{ id: null, extendName: 'ym' }];

    return {
      dt,
      dtType: 'ym',
      metrics: this.#metrics(),
      dimensions,
      filter: { items: [], type: null },
      scene: 4,
      dataFillConfig: { open: 1, fillRangeType: 1, fillValue: 1 },
    } as QueryInputVo;
  })
  
  metricsDataResource = rxResource({
    request: () => this.metricsDataBody(),
    loader: ({ request }) => isEmpty(request.metrics) 
      ? of(null)
      : this.apiService.search(request, 'internal-marketing-quality-analysis-metrics-overview').pipe(
        tap(res => {
          if (res.status !== '00000') {
            throw res.message;
          }
        }),
        map(res => res.data?.data?.at(0)),
        tap(value => {
          Object.keys(value).forEach(key => {
            const { dataUnit } = request.metrics.find(item => item.extendName === key) || {};
            const isPercent = dataUnit === '%';
            const target = this.valueMap.get(key);

            let val = parseFloat(value[key]) || null;

            val = (val === null 
              ? null 
              : isPercent
                ? toDecimals(val)
                : val
            )

            if (key !== 'ym' && key.indexOf(':') === -1) {
              this.valueMap.set(key, { ...target, value: val });
            }
            else if (key.indexOf(':') > -1) {
              const [k, v] = key.split(':');
              const target = this.valueMap.get(k);

              this.valueMap.set(k, {  ...target, [v]: val });
            }
          })

          this.cdr.markForCheck();
        })
      )
  })

  metricsTrendResource = rxResource({
    request: () => this.metricsTrendBody(),
    loader: ({ request }) => isEmpty(request.metrics) 
      ? of(null) 
      : this.apiService.search(request, 'internal-marketing-quality-analysis-metrics-trend').pipe(
        tap(res => {
          if (res.status !== '00000') {
            throw res.message;
          }
        }),
        map(res => res.data.data),
      )
  })

  constructor() {
    effect(() => {
      if (
        this.metricsDataResource.value() &&
        this.metricsTrendResource.value()
      ) {
        const values = this.metricsTrendResource.value();
        
        this.valueMap.forEach((value) => {
          value.trendVo = [];
        })
        
        values.forEach(item => {
          const [year, month] = item['ym'].split('-').map(toNumber);
          Object.keys(item).filter(key => key !== 'ym').forEach(key => {
            const { dataUnit } = this.#metrics().find(item => item.extendName === key) || {};
            const target = this.valueMap.get(key);
            const trendVo = target.trendVo || [];
            const value = parseFloat(item[key]) || null;
            const isPercent = dataUnit === '%';
            const val = (value === null 
              ? null 
              : isPercent
                ? toDecimals(value)
                : value
            )

            trendVo.push({ year, month, value: val })
            this.valueMap.set(key, {  ...target, trendVo });
          });
        })

        this.cdr.markForCheck();
      }
    })
  }

  openMetricsDetail(data: MetricsMenuVo) {
    console.clear();
    this.modalService.open(MetricsDetailComponent, { 
      viewContainerRef: this.viewContainerRef,
      data: {
        metrics: [data],
      }
    });
  }

}
