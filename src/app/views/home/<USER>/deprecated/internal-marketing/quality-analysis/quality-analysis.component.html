<span class="relative flex items-center gap-x-1.5 font-black text-base">
  <MapArrowRightIcon />
  端内营销质量分析

  <app-radio-group [(ngModel)]="type" class="absolute left-1/2 -translate-x-1/2 w-80 flex items-start gap-x-0.5 p-0.5 bg-[#ebebec] rounded-lg">
    <app-radio class="dida-radio-new" activeClass="active" value="quality_pass">乘客视角</app-radio>
    <app-radio class="dida-radio-new" activeClass="active" value="quality_driver">车主视角</app-radio>
    <app-radio-thumb [offsetX]="2" class="bg-linear-to-r from-[#465867] to-[#1E2C3A] rounded-md" />
  </app-radio-group>
</span>

<div class="w-full mt-5">
  @if (metricsConfigResource.value()) {
    @if (metricsDataResource.error()) {
      <div class="flex items-center justify-center h-85 text-xs">
        {{metricsDataResource.error()}}
      </div>
    }
    @else {
      <nz-spin [nzSpinning]="metricsDataResource.isLoading()">
        <app-indicator-card-group gap="gap-x-4" class="w-full min-h-64.5 px-3 py-3 overflow-x-auto">
          @let themeArr = type() === 'quality_pass' ? ['orange', 'orange', 'orange', 'orange', 'orange'] : ['blue', 'blue', 'blue', 'blue', 'blue'];
          @for (item of metricsConfigResource.value(); track $index) {
            <app-indicator-card class="snap-start scroll-mx-4 min-w-64! flex-1 p-3.5 rounded-2xl translate-y-0.5 card-theme {{themeArr[$index]}}">
              <app-indicator-header>
                <ng-template #metricsTitleTemplate>
                  {{item?.showName}} <span class="text-xs opacity-30 px-1">({{item?.aliasName}})</span>
                </ng-template>
                <app-indicator-title
                  nzPopoverPlacement="topLeft"
                  [nzPopoverTitle]="metricsTitleTemplate"
                  [nzPopoverContent]="contentTemplate"
                >
                  <ng-template #contentTemplate>
                    <div [innerHTML]="item?.bizExpression"></div>
                  </ng-template>
                  {{item.showName}}
                </app-indicator-title>
                <app-indicator-subtitle>
                  {{avgTypeMap.get(item.tagName) || item.tagName}}
                </app-indicator-subtitle>
                <span class="flex-1 min-w-0"></span>
                <ExpandIcon class="expand-btn" iconBtn nz-tooltip="查看" (click)="openMetricsDetail(item)" />
              </app-indicator-header>
              <app-indicator-content>
                @let isPercent = item.dataUnit === '%';
                <app-indicator-value class="py-3" [suffix]="isPercent ? '%' : ''" [value]="(valueMap.get(item.extendName)?.value) | increment | async" />
                <app-indicator-compare-group gap="gap-1">
                  <app-indicator-compare [iconVisible]="false" label="月环比" [symbol]="isPercent ? 'pp' : '%'" [value]="valueMap.get(item.extendName)?.ym_DIFF_RATIO" />
                  <app-indicator-compare [iconVisible]="false" label="年同比" [symbol]="isPercent ? 'pp' : '%'" [value]="valueMap.get(item.extendName)?.year_DIFF_RATIO" />
                </app-indicator-compare-group>
                <app-indicator-graph class="relative block h-32 bg-neutral-200/0 rounded-lg font-medium">
                  <app-graph-mini-area [value]="valueMap.get(item.extendName)?.trendVo" [isPercent]="isPercent" />
                </app-indicator-graph>
              </app-indicator-content>
            </app-indicator-card>
          }
        </app-indicator-card-group>
      </nz-spin>
    }
  }
</div>