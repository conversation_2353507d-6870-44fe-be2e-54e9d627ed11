import { ChangeDetectionStrategy, Component } from '@angular/core';
import { HeadingComponent } from '@shared/components/heading';
import { IconMarketingComponent } from '@shared/modules/icons';
import { OrderTrackingComponent } from './order-tracking/order-tracking.component';
import { QualityAnalysisComponent } from './quality-analysis/quality-analysis.component';

@Component({
  selector: 'app-internal-marketing',
  template: `
    <app-heading title="端内营销业务情况">
      <MarketingIcon ngProjectAs="[icon]" class="text-2xl" />
    </app-heading>

    <div class="p-5 space-y-6">
      <app-internal-marketing-order-tracking class="block" />
      <app-quality-analysis class="block" />
    </div>
  `,
  host: {
    'class': 'block px-5'
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    HeadingComponent,
    IconMarketingComponent,
    OrderTrackingComponent,
    QualityAnalysisComponent,
  ],
})
export class InternalMarketingComponent {

}
