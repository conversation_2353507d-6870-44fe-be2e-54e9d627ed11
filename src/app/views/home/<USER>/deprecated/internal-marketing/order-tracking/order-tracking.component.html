<span class="flex items-center gap-x-1.5 font-black text-base">
  <MapArrowRightIcon />
  端内营销订单进度
</span>

<div class="flex flex-col gap-1">
  <div class="flex flex-1 flex-wrap items-center gap-5 py-3 px-2">
    <div class="flex items-center">
      <label class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap">
        统计口径：
      </label>
      <app-radio-group class="relative flex gap-1" [(ngModel)]="type" (ngModelChange)="changeType()">
        <app-radio class="tag-radio-new" activeClass="active" value="day">昨日</app-radio>
        <app-radio class="tag-radio-new" activeClass="active" value="month">当月累计</app-radio>
        <app-radio-thumb class="rounded-xs bg-primary" />
      </app-radio-group>
    </div>
  </div>
  <div class="flex flex-wrap w-full gap-y-5 h-40">
    @if (cardLoading()) {
      <div class="flex items-center justify-center w-full">
        <app-line-spin />
      </div>
    } @else {
      <app-home-card
        [list]="list()"
        [display]="2"
        compareType="year"
        (updateIndex)="changeCard($event)"
        [themeArr]="['orange', 'orange', 'blue', 'blue']"
        [compareType]="type() === 'day' ? 'month' : 'year'"
      ></app-home-card>
    }
  </div>
  <div class="flex flex-col h-96 shadow-md rounded-sm border border-neutral-100 p-4 mt-2">
    <div class="flex gap-x-5 items-center">
      <div class="flex items-center">
        <label class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap">
          选择指标：
        </label>
        <div>
          @for (item of chartMetrics(); track $index) {
            <ng-template #metricsTitleTemplate>
              {{ item?.showName }}
              <span class="text-xs opacity-30 px-1">({{ item?.aliasName }})</span>
            </ng-template>

            <label
              nz-popover
              nz-checkbox
              class="ml-0! text-xs!"
              [nzPopoverMouseEnterDelay]="0.5"
              [nzPopoverTitle]="metricsTitleTemplate"
              [nzPopoverContent]="contentTemplate"
              [(ngModel)]="item.checked"
              (ngModelChange)="changeItem()"
            >
              <ng-template #contentTemplate>
                <div [innerHTML]="item?.bizExpression"></div>
              </ng-template>
              {{ item.showName }}
            </label>
          }
        </div>
      </div>
    </div>
    <div class="flex-1 min-h-0 relative flex items-center justify-center text-xs">
      @if (loading()) {
        <app-line-spin />
      } @else {
        @if (option()) {
          <app-graph class="absolute inset-2" [options]="option()" showAnnotations showAvgPlotLines />
        } @else if (errorMessage()) {
          <span class="text-slate-600 text-xs">{{ errorMessage() }}</span>
        }
      }
    </div>
  </div>
</div>
