import { XAxisOptions, YAxisOptions } from 'highcharts'
import { BaseHighCharts, SeriesItem } from '@common/chart/highcharts'
import { sortBy } from 'lodash'
import { _pad, getWeekByDate } from '@common/function'

function tooltipFormatter(that) {
  return function (param: any) {
    const result = []
    const map = new Map()
    const params = this.points.sort(that.sortFn)

    params.forEach(item => {
      if (map.has(item.series.name)) {
        const arr = map.get(item.series.name) as any[]
        arr.push(item)
        arr.reverse()
      } else {
        map.set(item.series.name, [item])
      }
    })

    const merged = [...map.values()].flat(1)
    const arr = sortBy(merged, [
      function (m) {
        return m.series.name
      },
    ]).reverse()

    result.push('<table class="text-sm">')
    arr.forEach((params, index) => {
      const {
        series: { name: seriesName },
        x: categorie,
        point: { type },
      } = params
      if (index === 0) {
        result.push(`
          <tr class="py-2">
          <td rowspan="2" class="px-3 border-b border-r border-gray-200">指标</td>
        `)
      }

      const year = seriesName.split('-')[0]
      const month = (<string>categorie).replace(/\D/g, '')
      let dateLabel = ''

      if (type === 'yw') {
        // console.clear();
        // console.log(month);
        dateLabel = `(${getWeekByDate(`${year}-${_pad(+month)}`, type)})`.replace(/\s/g, '') // 例：(04-29~05-05)
      }

      result.push(`
        <td colspan="2" class="px-3 pb-1 text-center border-b border-r border-gray-200">
          ${index === 0 ? `当前${type === 'ym' ? '月' : '周'}：` : ''}
          ${year}年${categorie} ${dateLabel}
        </td>
      `)
    })
    result.push('</tr>')
    result.push('<tr>')
    arr.forEach((params, index) => {
      const {
        series: { name: seriesName },
        point: { type },
      } = params
      result.push(
        `<td class="px-3 py-1 border-b border-r border-gray-200">数值</td>
        ${
          index === 0
            ? `<td class="px-3 py-1 border-b border-gray-200 border-r">${type === 'ym' ? '月环比' : '周环比'}</td>`
            : `<td class="px-3 py-1 border-b border-gray-200 border-r">${type === 'ym' ? `当月同比${seriesName.split('-')[0]}年` : `当周同比${seriesName.split('-')[0]}年`}</td>`
        }`
      )
    })
    result.push('</tr>')
    result.push('<tr>')
    arr.forEach((params, index) => {
      const {
        series: {
          name: seriesName,
          yAxis: { index: yAxisIndex },
        },
        y: value,
        point: { diff, dataUnit },
      } = params
      const { isPercent } = that.series.find(item => item.name === seriesName) ?? { isPercent: false }

      if (index === 0) {
        result.push(
          `<td class="px-3 border-b border-r border-gray-200 py-1 font-bold">${seriesName.split('-')[1]}</td>`
        )
      }

      result.push(`
          <td class="text-right px-3 border-b border-r border-gray-200 py-1">
            ${
              yAxisIndex === 1 || isPercent
                ? Number.isFinite(value)
                  ? value + '%'
                  : '-'
                : Number.isFinite(value)
                  ? Intl.NumberFormat().format(value)
                  : '-'
            }
            ${dataUnit === '%' ? '%' : ''}
          </td>
          ${
            dataUnit !== '%'
              ? `<td class="text-right px-3 border-b border-r border-gray-200 py-1">
            ${
              diff
                ? `<span>
              <span style="${diff.diff > 0 ? 'color:red' : diff.diff < 0 ? 'color:green' : ''}">${diff.diff > 0 ? `+${diff.diff}` : diff.diff || '-'}</span>
              <span style="${diff.diff_ratio > 0 ? 'color:red' : diff.diff_ratio < 0 ? 'color:green' : ''}">(${diff.diff_ratio > 0 ? `+${diff.diff_ratio}` : diff.diff_ratio || '-'}%)</span>
              </span>`
                : '-(-)'
            }
          </td>`
              : `<td class="text-right px-3 border-b border-r border-gray-200 py-1">
            ${
              diff
                ? `<span>
              <span style="${diff.diff > 0 ? 'color:red' : diff.diff < 0 ? 'color:green' : ''}">${diff.diff > 0 ? `+${diff.diff}` : diff.diff || '-'}pp</span>
              </span>`
                : '-'
            }
          </td>`
          }

      `)
    })
    result.push('</tr>')
    result.push('</table>')

    return result.join('')
  }
}

class xAxisItem {
  categories: string[]
  opposite: boolean
  tickInterval = 1
  tickWidth = 1
  tickColor = '#ccd6eb'
  lineColor = '#ccd6eb'
  gridLineColor = '#e6e6e6'
  crosshair = true
  labels = {
    useHTML: true,
    formatter: function () {
      if (/\d{4}-\d{2}-\d{2}/.test(this.value)) {
        const day = new Date(this.value.replace(/-/g, '/')).getDay()
        if (day === 0 || day === 6) {
          return `<span class="text-primary font-semibold">${this.value}</span>`
        }
      }

      return this.value
    },
  }

  constructor({ categories, opposite }: Partial<xAxisItem>) {
    categories && (this.categories = categories)
    opposite && (this.opposite = opposite)
  }
}

export class AreaBasic extends BaseHighCharts {
  responsive = {
    rules: [
      {
        condition: { maxWidth: 1200 },
        chartOptions: {
          xAxis: [{ tickInterval: 2 }],
        },
      },
      {
        condition: { maxWidth: 800 },
        chartOptions: {
          xAxis: [{ tickInterval: 4 }],
        },
      },
      {
        condition: { maxWidth: 500 },
        chartOptions: {
          xAxis: [{ tickInterval: 6 }],
        },
      },
    ],
  }

  xAxis: XAxisOptions[] = []
  yAxis: YAxisOptions[] = [
    {
      title: { text: '' },
      labels: { format: undefined },
      gridLineWidth: 1,
      gridLineColor: '#e6e6e6',
    },
  ]

  colors = [
    'rgba(80,135,236,0.7)',
    'rgba(238,117,47,0.7)',
    'rgba(104,187,196,0.7)',
    'rgba(88,165,92,0.7)',
    'rgba(242,189,66,0.7)',
    'rgba(238,117,47,0.7)',
    'rgba(176,102,93,0.7)',
    'rgba(228,196,119,0.7)',
    'rgba(163,194,251,0.7)',
    'rgba(160,208,213,0.7)',
    'rgba(152,179,226,0.7)',
    'rgba(222,134,143,0.7)',
    'rgba(244,206,152,0.7)',
    'rgba(180,200,217,0.7)',
    'rgba(147,210,243,0.7)',
    'rgba(64,149,229,0.7)',
    'rgba(127,131,247,0.7)',
    '#E99D42',
    '#CBA43F',
    '#BFBF3D',
    '#81B337',
    '#347CAF',
    '#377F7F',
    '#FCCA00',
    '#B886F8',
    '#A16222',
    '#5087EC',
    '#68BBC4',
    '#58A55C',
    '#F2BD42',
    '#EE752F',
    '#B0665D',
    '#E4C477',
    '#A3C2FB',
    '#A0D0D5',
    '#98B3E2',
    '#DE868F',
    '#F4CE98',
    '#B4C8D9',
    '#93D2F3',
    '#4095E5',
    '#7F83F7',
  ]

  plotOptions: any = {
    series: {
      turboThreshold: 999999999,
      marker: {
        radius: 3,
        symbol: 'circle',
      },
    },
    line: {
      marker: {
        enabled: false,
        symbol: 'circle',
        radius: 2,
        states: {
          hover: {
            enabled: true,
          },
        },
      },
    },
  }

  constructor(properties: any) {
    super()

    const { xData, data, name } = properties
    console.log('xdata', properties)
    // const categories = getCategories(headers, data)
    const series = this.getSeries(data, name)
    // const [percentFields] = getPercentFields(headers)
    // const isEveryFieldPercent = isEveryElementPercent(headers)
    // const _series = sortBy(series, [
    //   function (m) {
    //     return m.name
    //   },
    // ])
    // if (isEveryFieldPercent) {
    //   this.yAxis = [
    //     {
    //       title: { text: '' },
    //       labels: { format: '{text}%' },
    //       gridLineWidth: 1,
    //       gridLineColor: '#e6e6e6',
    //     },
    //   ]
    // } else if (percentFields) {
    //   this.yAxis.push({
    //     title: { text: '' },
    //     labels: { format: '{text}%' },
    //     gridLineWidth: 1,
    //     gridLineColor: '#e6e6e6',
    //     opposite: true,
    //   })
    // }
    console.log('series', series)
    this.setCategories([xData])
    this.setSeries(series)
    this.legend.verticalAlign = 'top'
    this.chart.type = 'line'// 产品要求改为折线图
  }

  getSeries(data, name) {
    // const numberFields = getNumberFields(headers)
    // const isEveryFieldPercent = isEveryElementPercent(headers)

    return Object.keys(data).map(key => {
      const series = new SeriesItem()
      // const { aliasName, dataUnit, extendName } = headers[key]
      // series.type = 'area' // 产品要求改为折线图
      // if (dataUnit === '%') {
      //   series.isPercent = true
      // }

      // if (dataUnit === '%' && !isEveryFieldPercent) {
      //   series.yAxis = 1
      // }

      // if (extendName.includes('dida_benchmark_book_ord_amt')) {
      //   series.dashStyle = 'ShortDash'
      //   series.marker = {
      //     enabled: false,
      //   }
      // }

      series.name = `${key}-${name}`
      // series.fillOpacity = 0.2 // 产品要求改为折线图

      // series.dataUnit = dataUnit
      series.data = data[key]
      // series.data = data.map(item => {
      //   const numeric = Number(item[key])
      //   let value: number = null

      //   if (item[key] === null) {
      //     value = null
      //   } else if (dataUnit === '%') {
      //     value = toDecimals(numeric)
      //   } else {
      //     value = numeric
      //   }

      //   if (isNotUndefined(item[`${key}:yw_DIFF`], item[`${key}:yw_DIFF_RATIO`])) {
      //     return {
      //       y: value,
      //       yw: {
      //         diff: +item[`${key}:yw_DIFF`],
      //         ratio: +item[`${key}:yw_DIFF_RATIO`],
      //       },
      //       dt: {
      //         diff: +item[`${key}:dt_DIFF`],
      //         ratio: +item[`${key}:dt_DIFF_RATIO`],
      //       },
      //       ym: {
      //         diff: +item[`${key}:ym_DIFF`],
      //         ratio: +item[`${key}:ym_DIFF_RATIO`],
      //       },
      //     }
      //   }

      //   return {
      //     y: value,
      //   }
      // })

      return series
    })
  }

  setCategories(values: string[][]): void {
    values.forEach((categories, index) => {
      this.xAxis.push(new xAxisItem({ categories, opposite: index > 0 }))
    })
  }

  override getOption() {
    const value = this

    return {
      ...value,
      tooltip: {
        ...value.tooltip,
        formatter: tooltipFormatter(this),
      },
      plotOptions: {
        ...value.plotOptions,
        series: {
          ...value.plotOptions.series,
          // events: {
          //   legendItemClick: handleLegendItemClick(this),
          // }
        },
      },
    }
  }
}
