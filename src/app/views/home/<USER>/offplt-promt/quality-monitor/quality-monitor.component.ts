import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  computed,
  DestroyRef,
  effect,
  inject,
  signal,
} from '@angular/core'
import { FormsModule } from '@angular/forms'
import { LineSpinComponent } from '@shared/components/line-spin'
import { RadioModule } from '@shared/modules/headless'
import { IconMapArrowRightComponent } from '@shared/modules/icons'
import { HomeCardComponent } from '../../home-card/home-card.component'
import { GraphComponent } from '@shared/components/graph'
import { AreaBasic } from '../lib'
import { BuriedPointService, LegendControlService, LegendItemClickHandler } from '@common/service'
import { uniq, groupBy, find } from 'lodash'
import { CaerusApiService } from '@api/caerus'
import { SwitchMap } from '@common/decorator'
import { QueryEngineFormService } from '@common/service/query-engine'
import { combineLatest, finalize } from 'rxjs'
import { QueryEngineApiService } from '@api/query-engine'
import { takeUntilDestroyed } from '@angular/core/rxjs-interop'
import { DatePipe } from '@angular/common'
import { HomeService } from '@views/home/<USER>'
import { getISOWeek } from 'date-fns'
import { PAGE_NAME } from '@common/directive'

@Component({
  selector: 'app-quality-monitor',
  imports: [FormsModule, IconMapArrowRightComponent, RadioModule, LineSpinComponent, HomeCardComponent, GraphComponent],
  templateUrl: './quality-monitor.component.html',
  styles: ``,
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [LegendControlService, QueryEngineFormService, DatePipe],
})
export class QualityMonitorComponent implements AfterViewInit {
  page_name = inject(PAGE_NAME)
  readonly buriedPointService = inject(BuriedPointService)
  readonly legendControlService = inject(LegendControlService)
  readonly apiService = inject(CaerusApiService)
  readonly formService = inject(QueryEngineFormService)
  readonly queryService = inject(QueryEngineApiService)
  readonly datePipe = inject(DatePipe)
  readonly destroyRef = inject(DestroyRef)
  readonly service = inject(HomeService)

  readonly radioMap = {
    day: ':dt_DIFF_RATIO',
    week: ':yw_DIFF_RATIO',
    month: ':ym_DIFF_RATIO',
    year: ':year_DIFF_RATIO',
  }

  readonly radioMapDiff = {
    day: ':dt_DIFF',
    week: ':yw_DIFF',
    month: ':ym_DIFF',
    year: ':year_DIFF',
  }

  type = signal('week')
  cardLoading = signal(false)
  loading = signal(false)
  option = signal(null)
  errorMessage = signal(null)
  dtType = signal('ym')
  currentMetrics = signal([])
  metric = signal(null)
  allMetrics = signal(null)
  dt = signal(null)
  list = signal([])
  index = signal(1)

  dimensions = computed(() => {
    if (!this.allMetrics()) {
      return null
    }
    return this.allMetrics()[`quality_passenger_${this.type()}`]
  })

  chartMetrics = computed(() => {
    if (!this.allMetrics()) {
      return []
    }
    const groupDimension = groupBy(this.dimensions().subMetric, 'tagName')
    const arr = Object.keys(groupDimension).map((dimension, index) => {
      return {
        showName: dimension,
        aliasName: groupDimension[dimension][0].aliasName,
        bizExpression: groupDimension[dimension][0].bizExpression,
        value: groupDimension[dimension].map(d => {
          return {
            aliasName: d.aliasName,
            extendName: d.extendName,
            recommend: d.recommend,
            displayOrder: d.displayOrder,
          }
        }),
        checked: index === this.index() - 1,
      }
    })
    return arr
  })

  ngAfterViewInit(): void {
    this.subscribeToMonthChange()
    this.fetchMetrics()
  }

  subscribeToMonthChange() {
    combineLatest([this.service.month$, this.service.updateTimeResource$])
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(([value, updateTime]) => {
        if (!updateTime) {
          return
        }
        this.dt.set({
          startTime: updateTime,
          endTime: updateTime,
        })
        this.getCardData()
        // this.getChartData()
      })
  }

  changeType() {
    this.dealGroupDimension()
  }

  dealGroupDimension() {
    if (!this.dimensions()) {
      return
    }
    this.loading.set(true)
    const groupDimension = groupBy(
      this.dimensions().subMetric.filter(d => d.recommend === 1),
      'tagName'
    )
    const arr = Object.values(groupDimension).map((item: any, index) => {
      const extendNames = item.map(sub => ({
        extendName: sub.extendName,
        recommend: sub.recommend,
        displayOrder: sub.displayOrder,
      }))

      return {
        aliasName: item[0].aliasName,
        bizExpression: item[0].bizExpression,
        showName: Object.keys(groupDimension)[index],
        extendNames,
        twobizExpression: false,
      }
    })
    this.currentMetrics.set(arr)
    this.getCardData()
  }

  fetchMetrics() {
    this.apiService.fetchMetricsConfig('cockpit_outplt_process_quality').subscribe(res => {
      if (res.data) {
        console.log('cockpit_outplt_process_quality', res.data)
        this.allMetrics.set(res.data)
        this.dealGroupDimension()
      } else {
        this.currentMetrics.set([])
      }
    })
  }

  changeCard(index) {
    if (this.list().length !== 0) {
      this.buriedPointService.addStat('dida_dpm_caerus_home_promotioncard_click', {
        page_name: this.page_name,
        indicator_name: this.list()[index - 1].extendNames[0].extendName,
      })
    }
    this.index.set(index)
    this.getChartData()
  }

  getStartWeekNative(endIsoWeek) {
    // 1. 解析结束周
    const [endYear, endWeek] = endIsoWeek.split('-').map(Number)

    // 2. 计算结束周的周四（ISO年锚点）
    const jan4 = new Date(endYear, 0, 4)
    const jan4Day = jan4.getDay()
    const firstThursday = new Date(jan4)
    firstThursday.setDate(jan4.getDate() + ((11 - jan4Day) % 7)) // 定位首周周四

    // 3. 计算结束周周四
    const endThursday = new Date(firstThursday)
    endThursday.setDate(firstThursday.getDate() + (endWeek - 1) * 7)

    // 4. 回溯51周
    const startThursday: any = new Date(endThursday)
    startThursday.setDate(endThursday.getDate() - 51 * 7)

    // 5. 计算开始周的ISO年/周
    const startYear = startThursday.getFullYear()
    const yearStart = new Date(startYear, 0, 4)
    const yearStartDay = yearStart.getDay()
    const yearFirstThursday: any = new Date(yearStart)
    yearFirstThursday.setDate(yearStart.getDate() + ((11 - yearStartDay) % 7))

    // 6. 计算周数
    const weekDiff = Math.floor((startThursday - yearFirstThursday) / (7 * 86400000))
    const week = (weekDiff + 1).toString().padStart(2, '0')

    return `${startYear}-${week}`
  }

  @SwitchMap()
  getChartData() {
    if (this.chartMetrics().length === 0) {
      return
    }
    this.loading.set(true)
    const body = this.formService.value()
    const [year, month] = this.dt().startTime.split('-')

    let time = this.dt().startTime
    let endTime, startTime
    if (this.type() === 'week') {
      const week = getISOWeek(new Date(time))
      startTime = this.getStartWeekNative(`${year}-${week}`)
      endTime = `${year}-${week}`
    } else {
      startTime = `${year - 1}-${month}`
      endTime = `${year}-${month}`
    }

    body.dt = {
      startTime,
      endTime,
    }

    body.dataFillConfig = {
      open: 0,
      fillRangeType: 1,
      fillValue: 1,
    }
    body.dimensions = [
      {
        id: null,
        extendName: this.type() === 'week' ? 'yw' : 'ym',
        predefineCompareType: [this.type() === 'week' ? 'yw' : 'ym', 'year'],
      },
    ]

    body.dtType = this.type() === 'week' ? 'yw' : 'ym'

    const checkedMetrics = this.chartMetrics().filter(c => c.checked)
    const _metrics = checkedMetrics.reduce((acc, c) => acc.concat(c.value), [])

    body.metrics = _metrics.map(m => m.extendName)

    this.metric.set(_metrics[0].extendName)

    this.legendControlService.reset()
    return this.queryService
      .search(body, 'quality-chart')
      .pipe(finalize(() => this.loading.set(false)))
      .subscribe(res => {
        if (res.data) {
          this._setChartData(res.data)
        } else {
          this.option.set(null)
          this.errorMessage.set(res.message)
        }
      })
  }

  @SwitchMap()
  private getCardData() {
    if (this.currentMetrics().length === 0) {
      return
    }
    const body = this.formService.value()

    let time
    if (this.type() === 'week') {
      const week = getISOWeek(new Date(this.dt().startTime))
      time = `${this.dt().startTime.split('-')[0]}-${week}`
    } else {
      time = `${this.dt().startTime.split('-')[0]}-${this.dt().startTime.split('-')[1]}`
    }

    body.dt = {
      startTime: time,
      endTime: time,
    }

    body.dtType = this.type() === 'week' ? 'yw' : 'ym'

    body.dimensions = [
      {
        id: null,
        extendName: this.type() === 'week' ? 'yw' : 'ym',
        predefineCompareType: this.type() === 'week' ? ['yw', 'year'] : ['ym', 'year'],
      },
    ]

    body.metrics = this.currentMetrics()
      .reduce((arr, m) => arr.concat(m.extendNames.map(ex => ex.extendName)), [])
      .map(extendName => ({ extendName }))

    body.dataFillConfig = {
      open: 1,
      fillRangeType: 2,
      fillValue: 1,
    }
    this.cardLoading.set(true)

    return this.queryService
      .search(body, 'quality-card')
      .pipe(finalize(() => this.cardLoading.set(false)))
      .subscribe(res => {
        if (res.data && res.data.data.length !== 0) {
          const _data = res.data.data[0]
          this.currentMetrics().forEach(m => {
            m.extendNames.forEach(ex => {
              const dataUnit = res.data.headers[ex.extendName].dataUnit === '%' ? 'pp' : '%'
              ex.dataUnit = dataUnit
              if (dataUnit === 'pp') {
                ex.data = _data[ex.extendName] ? Number(_data[ex.extendName]) * 100 : 0

                if (ex.displayOrder === 1) {
                  ex.yw = _data[`${ex.extendName}${this.radioMapDiff.week}`]
                    ? Number(_data[`${ex.extendName}${this.radioMapDiff.week}`]) * 100
                    : 0
                  ex.ym = _data[`${ex.extendName}${this.radioMapDiff.month}`]
                    ? Number(_data[`${ex.extendName}${this.radioMapDiff.month}`]) * 100
                    : 0
                  ex.year = _data[`${ex.extendName}${this.radioMapDiff.year}`]
                    ? Number(_data[`${ex.extendName}${this.radioMapDiff.year}`]) * 100
                    : 0
                }
              } else {
                ex.data = _data[ex.extendName] || 0
                if (ex.displayOrder === 1) {
                  ex.yw = _data[`${ex.extendName}${this.radioMap.week}`]
                  ex.ym = _data[`${ex.extendName}${this.radioMap.month}`]
                  ex.year = _data[`${ex.extendName}${this.radioMap.year}`]
                }
              }
            })
          })
        } else {
          this.currentMetrics.set([])
        }
        this.list.set(this.currentMetrics())
      })
  }

  dealData(_data) {
    const datamap = {}
    const xData = []
    const monthArr = []
    const yearArr = []
    const name = _data.headers[this.metric()].aliasName
    const dataUnit = _data.headers[this.metric()].dataUnit
    const currentYear = this.dt().startTime.split('-')[0]
    let t = 'ym'
    if (this.type() === 'week') {
      t = 'yw'
    }
    _data.data.forEach(d => {
      const [year, month] = d[t].split('-')
      const monthNum = parseInt(month, 10)
      monthArr.push(month)
      yearArr.push(year)
      xData.push(t === 'ym' ? `${monthNum}月` : `第${monthNum}周`)
    })
    uniq(yearArr).forEach(y => {
      datamap[y] = []
    })
    uniq(monthArr).forEach(m => {
      uniq(yearArr).forEach(y => {
        const _d = find(_data.data, [t, `${y}-${m}`])
        if (!_d) {
          datamap[y].push({
            y: null,
            diff: null,
            type: t,
            dataUnit,
          })
        } else {
          const isCurrentYear = y === currentYear
          let diff
          const value = dataUnit === '%' ? Number(_d[this.metric()]) * 100 : Number(_d[this.metric()])
          if (isCurrentYear) {
            if (dataUnit === '%') {
              diff = {
                diff: (Number(_d[`${this.metric()}:${t}_DIFF`]) * 100).toFixed(2),
                diff_ratio: Number(_d[`${this.metric()}:${t}_DIFF_RATIO`]).toFixed(2),
              }
            } else {
              diff = {
                diff: Number(_d[`${this.metric()}:${t}_DIFF`]).toFixed(2),
                diff_ratio: Number(_d[`${this.metric()}:${t}_DIFF_RATIO`]).toFixed(2),
              }
            }
          } else {
            const currentYearData = find(_data.data, [t, `${currentYear}-${m}`])
            if (currentYearData) {
              diff = {
                diff:
                  dataUnit === '%'
                    ? (Number(currentYearData[this.metric()]) * 100 - value).toFixed(2)
                    : (Number(currentYearData[this.metric()]) - value).toFixed(2),
                diff_ratio:
                  value === 0 ? 0 : (((Number(currentYearData[this.metric()]) - value) / value) * 100).toFixed(2),
              }
            }
          }
          datamap[y].push({
            y: value,
            type: t,
            diff,
            dataUnit,
          })
        }
      })
    })

    const xAxis = uniq(xData)
    return {
      xData: xAxis,
      data: datamap,
      name,
    }
  }

  private _setChartData(_data) {
    try {
      // const obj = this.dealData(_data)
      const chart = new AreaBasic(_data)
      const legendItemClick = LegendItemClickHandler(this.legendControlService)
      chart.plotOptions.series.events = { legendItemClick }
      this.option.set(chart?.getOption() || null)
    } catch (e) {
      this.option.set(null)
      this.errorMessage.set(e)
      console.error(e)
    }
  }
}
