<span class="flex items-center gap-x-1.5 font-black text-base">
  <MapArrowRightIcon />
  端外推广质量监控
</span>
<div class="flex flex-1 flex-wrap items-center gap-5 py-3 px-2">
  <div class="flex items-center">
    <label class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap">统计口径：</label>
    <app-radio-group class="relative flex gap-1" [(ngModel)]="type" (ngModelChange)="changeType()">
      <app-radio class="tag-radio-new" activeClass="active" value="week">按周看</app-radio>
      <app-radio class="tag-radio-new" activeClass="active" value="month">按月看</app-radio>
      <app-radio-thumb class="rounded-xs bg-primary" />
    </app-radio-group>
  </div>
  <div class="text-xs text-neutral-400 font-normal">
    卡片中展示的是
    @switch (type()) {
      @case ('week') {
        <span class="text-xs text-neutral-400 font-normal">本周</span>
      }
      @case ('month') {
        <span class="text-xs text-neutral-400 font-normal">本月</span>
      }
    }
    数值。
  </div>
</div>

<div class="flex flex-col gap-5 h-135">
  <div class="h-45">
    <div class="flex flex-wrap w-full gap-y-5 h-50 p-1 pb-7 customized-scrollbar overflow-x-auto">
      @if (cardLoading()) {
        <div class="flex items-center justify-center w-full">
          <app-line-spin />
        </div>
      } @else {
        <app-home-card
          class="min-w-360!"
          [themeArr]="['orange', 'orange', 'orange', 'blue', 'blue', 'blue']"
          [list]="list()"
          [display]="2"
          (updateIndex)="changeCard($event)"
          [compareType]="type() === 'week' ? 'week' : 'year'"
        ></app-home-card>
      }
    </div>
  </div>
  <div class="flex-1 min-w-0 flex flex-col h-full shadow-md rounded-sm border border-neutral-100 relative">
    <div class="flex-1 min-h-0 relative flex items-center justify-center text-xs">
      @if (loading()) {
        <app-line-spin />
      } @else {
        @if (option()) {
          <app-graph class="absolute inset-2" [options]="option()" />
        } @else if (errorMessage()) {
          <span class="text-slate-600 text-xs">{{ errorMessage() }}</span>
        }
      }
    </div>
  </div>
</div>
