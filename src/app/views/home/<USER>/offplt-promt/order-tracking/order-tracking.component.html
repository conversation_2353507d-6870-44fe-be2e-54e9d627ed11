<span class="flex items-center gap-x-1.5 font-black text-base">
  <MapArrowRightIcon />
  端外推广规模完成度
</span>
<div class="mt-[-30px]">
  <div class="flex justify-center">
    <app-radio-group [(ngModel)]="viewType" (ngModelChange)="changeViewType()" class="flex items-start px-3 gap-[30px]">
      <app-radio class="line-radio text-xs whitespace-nowrap" activeClass="active" value="passenger">
        乘客视角
      </app-radio>
      <app-radio class="line-radio text-xs whitespace-nowrap" activeClass="active" value="driver">车主视角</app-radio>
    </app-radio-group>
  </div>
</div>
<div class="flex flex-col gap-1">
  <div class="flex flex-1 flex-wrap items-center gap-5 py-3 px-2">
    <div class="flex items-center">
      <label class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap">
        统计口径：
      </label>
      <app-radio-group class="relative flex gap-1" [(ngModel)]="type" (ngModelChange)="changeType()">
        <app-radio class="tag-radio-new" activeClass="active" value="day">昨日</app-radio>
        <app-radio class="tag-radio-new" activeClass="active" value="month">当月累计</app-radio>
        <app-radio-thumb class="rounded-xs bg-primary" />
      </app-radio-group>
    </div>
  </div>
  <div class="h-50">
    <div class="flex w-full gap-y-5 h-55 p-1 pb-7 customized-scrollbar overflow-x-auto">
      @if (cardLoading()) {
        <div class="flex items-center justify-center w-full">
          <app-line-spin />
        </div>
      } @else {
        <app-home-card
          class="min-w-360!"
          [list]="list()"
          [display]="2"
          [themeArr]="['teal', 'teal', 'orange', 'orange', 'orange']"
          (updateIndex)="changeCard($event)"
          [compareType]="type() === 'day' ? 'month' : 'year'"
          [needShowProgress]="true"
          [inputIndex]="index()"
        ></app-home-card>
      }
    </div>
  </div>
  <div class="flex flex-col h-96 shadow-md rounded-sm border border-neutral-100 p-4 mt-6">
    <div class="flex gap-x-5 items-center">
      @if (index() === 1) {
        <div class="flex items-center">
          <label class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap">
            产品渠道：
          </label>
          <app-radio-group class="relative flex gap-1" [(ngModel)]="bizType" (ngModelChange)="getChartData()">
            <app-radio class="tag-radio-new" activeClass="active" value="1">出行APP</app-radio>
            <app-radio class="tag-radio-new" activeClass="active" value="2">车主APP</app-radio>
            <app-radio-thumb class="rounded-xs bg-primary" />
          </app-radio-group>
        </div>
      }
      <div class="flex items-center">
        <label class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap">
          来源类型：
        </label>
        <nz-checkbox-group [(ngModel)]="checkValue" (ngModelChange)="buriedPoint('来源类型'); getChartData()">
          <label
            nz-checkbox
            class="text-xs! ml-0!"
            [nzValue]="null"
            [nzDisabled]="checkValue().length === 2 && !showCheckValue().includes(null)"
            [nz-tooltip]="checkValue().length === 2 && '最多可支持两个选项同时对比查看'"
          >
            全部
          </label>

          @for (item of config(); track item) {
            <label
              nz-checkbox
              class="text-xs! ml-0!"
              [nzValue]="item"
              [nzDisabled]="checkValue().length === 2 && !showCheckValue().includes(item.showValue)"
              [nz-tooltip]="checkValue().length === 2 && '最多可支持两个选项同时对比查看'"
            >
              {{ item.showValue }}
            </label>
          }
        </nz-checkbox-group>
      </div>
      <!-- <div class="flex items-center">
        <label class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap">
          选择指标：
        </label>
        <div>
          @for (item of chartMetrics(); track $index) {
            <ng-template #metricsTitleTemplate>
              {{ item?.showName }}
              <span class="text-xs opacity-30 px-1">({{ item?.aliasName }})</span>
            </ng-template>

            <label
              nz-popover
              nz-checkbox
              class="ml-0! text-xs!"
              [nzPopoverMouseEnterDelay]="0.5"
              [nzPopoverTitle]="metricsTitleTemplate"
              [nzPopoverContent]="item?.bizExpression"
              [(ngModel)]="item.checked"
              (ngModelChange)="changeItem()"
            >
              {{ item.showName }}
            </label>
          }
        </div>
      </div> -->
      <div>
        <label class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap">
          查看用户来源-推广占比：
        </label>
        <nz-switch
          nzSize="small"
          [(ngModel)]="accountfor"
          (ngModelChange)="buriedPoint('推广占比'); getChartData()"
        ></nz-switch>
      </div>
    </div>
    <div class="flex-1 min-h-0 relative flex items-center justify-center text-xs">
      @if (loading()) {
        <app-line-spin />
      } @else {
        @if (option()) {
          <app-graph class="absolute inset-2" [options]="option()" showAnnotations showAvgPlotLines />
        } @else if (errorMessage()) {
          <span class="text-slate-600 text-xs">{{ errorMessage() }}</span>
        }
      }
    </div>
  </div>
</div>
