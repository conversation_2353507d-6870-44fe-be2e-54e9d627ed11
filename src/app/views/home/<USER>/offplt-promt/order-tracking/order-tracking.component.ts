import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  computed,
  DestroyRef,
  effect,
  inject,
  signal,
} from '@angular/core'
import { FormsModule } from '@angular/forms'
import { RadioModule } from '@shared/modules/headless'
import { IconMapArrowRightComponent } from '@shared/modules/icons'
import { HomeCardComponent } from '../../home-card/home-card.component'
import { LineSpinComponent } from '@shared/components/line-spin'
import { GraphComponent } from '@shared/components/graph'
import { NzPopoverModule } from 'ng-zorro-antd/popover'
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox'
import { groupBy, find } from 'lodash'
import { CaerusApiService } from '@api/caerus'
import { QueryEngineFormService } from '@common/service/query-engine'
import { QueryEngineApiService } from '@api/query-engine'
import { BuriedPointService, LegendControlService, LegendItemClickHandler } from '@common/service'
import { combineLatest, finalize } from 'rxjs'
import { QueryOutputVo } from '@api/query-engine/model'
import { ProgressTrend } from '../lib'
import { NzSwitchModule } from 'ng-zorro-antd/switch'
import { getMonthFirstAndLastDay, getMonthFirstAndNowDay } from '@common/class'
import { DatePipe } from '@angular/common'
import { HomeService } from '@views/home/<USER>'
import { takeUntilDestroyed } from '@angular/core/rxjs-interop'
import { SwitchMap } from '@common/decorator'
import { parseISO, endOfMonth, format } from 'date-fns'
import { NzToolTipModule } from 'ng-zorro-antd/tooltip'
import { flatten } from 'lodash'
import { PAGE_NAME } from '@common/directive'

@Component({
  selector: 'app-offplt-promt-order-tracking',
  templateUrl: './order-tracking.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    IconMapArrowRightComponent,
    FormsModule,
    RadioModule,
    HomeCardComponent,
    LineSpinComponent,
    GraphComponent,
    NzPopoverModule,
    NzCheckboxModule,
    NzSwitchModule,
    NzToolTipModule,
  ],
  providers: [LegendControlService, QueryEngineFormService, DatePipe],
})
export class OrderTrackingComponent implements AfterViewInit {
  page_name = inject(PAGE_NAME)
  readonly buriedPointService = inject(BuriedPointService)
  readonly apiService = inject(CaerusApiService)
  readonly formService = inject(QueryEngineFormService)
  readonly queryService = inject(QueryEngineApiService)
  readonly legendControlService = inject(LegendControlService)
  readonly radioMap = {
    day: ':dt_DIFF_RATIO',
    week: ':yw_DIFF_RATIO',
    month: ':ym_DIFF_RATIO',
    year: ':year_DIFF_RATIO',
  }
  readonly datePipe = inject(DatePipe)
  readonly destroyRef = inject(DestroyRef)
  readonly service = inject(HomeService)

  tooltip = []

  type = signal('day')
  cardLoading = signal(false)
  loading = signal(false)
  option = signal(null)
  errorMessage = signal(null)
  list = signal([])
  index = signal(2)
  currentMetrics = signal([])
  allMetrics = signal(null)
  accountfor = signal(false)
  dt = signal(null)
  viewType = signal('passenger')
  bizType = signal('1')
  checkValue = signal([null])
  allConfig = signal(null)

  dimensions = computed(() => {
    if (!this.allMetrics()) {
      return null
    }
    return this.allMetrics()[`scale_${this.viewType()}_${this.type()}`]
  })

  config = computed(() => {
    if (!this.allConfig()) {
      return
    }
    return this.allConfig()[this.viewType()][0]?.values
  })

  showCheckValue = computed(() => {
    return this.checkValue().map(c => {
      if (!c) {
        return c
      } else {
        return c.showValue
      }
    })
  })

  chartMetrics = computed(() => {
    if (!this.allMetrics()) {
      return []
    }
    const groupDimension = groupBy(this.dimensions().subMetric, 'tagName')
    const arr = Object.keys(groupDimension).map((dimension, index) => {
      return {
        showName: dimension,
        aliasName: groupDimension[dimension][0].aliasName,
        bizExpression: groupDimension[dimension][0].bizExpression,
        value: groupDimension[dimension].map(d => {
          return {
            aliasName: d.aliasName,
            extendName: d.extendName,
            recommend: d.recommend,
            displayOrder: d.displayOrder,
          }
        }),
        checked: index === this.index() - 1,
      }
    })
    return arr
  })

  // constructor() {
  //   effect(() => {
  //     console.clear();
  //     console.log(this.list());
  //   })
  // }

  ngAfterViewInit(): void {
    this.fetchMetrics()
    this.fetchDimension()
    this.subscribeToMonthChange()
  }

  subscribeToMonthChange() {
    combineLatest([this.service.month$, this.service.updateTimeResource$])
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(([value, updateTime]) => {
        if (!updateTime) {
          return
        }

        const month = this.datePipe.transform(value, 'yyyy-MM-dd')
        const [startTime] = getMonthFirstAndNowDay(month)
        this.dt.set({
          startTime,
          endTime: updateTime,
        })
        this.getCardData()
        // this.getChartData()
      })
  }

  dealGroupDimension() {
    if (!this.dimensions()) {
      return
    }
    this.loading.set(true)
    const groupDimension = groupBy(
      this.dimensions().subMetric.filter(d => d.recommend === 1),
      'tagName'
    )
    const arr = Object.values(groupDimension).map((item: any, index) => {
      const extendNames = item.map(sub => ({
        extendName: sub.extendName,
        recommend: sub.recommend,
        displayOrder: sub.displayOrder,
      }))
      const order4 = find(item, ['displayOrder', 4])
      return {
        aliasName: item[0].aliasName,
        bizExpression: item[0].bizExpression,
        showName: Object.keys(groupDimension)[index],
        extendNames,
        twobizExpression: order4 && order4.recommend === 1,
        twobizExpressionName: ['出行APP', '车主APP'],
      }
    })
    this.currentMetrics.set(arr)
    this.getCardData()
  }

  changeViewType() {
    this.index.set(2)
    this.checkValue.set([null])
    this.dealGroupDimension()
  }

  fetchDimension() {
    this.apiService.fetchDimensionConfig('cockpit_outplt_process_dim').subscribe(res => {
      if (res.data) {
        this.allConfig.set(res.data)
      } else {
        this.allConfig.set(null)
      }
    })
  }

  fetchMetrics() {
    this.apiService.fetchMetricsConfig('cockpit_outplt_process_scale').subscribe(res => {
      if (res.data) {
        this.allMetrics.set(res.data)
        this.dealGroupDimension()
      } else {
        this.currentMetrics.set([])
      }
    })
  }

  changeType() {
    if (!this.allMetrics()) {
      return
    }
    this.index.set(2)
    this.dealGroupDimension()
  }

  buriedPoint(name) {
    this.buriedPointService.addStat('dida_dpm_caerus_home_promotion_graph_click', {
      page_name: this.page_name,
      component_name: name,
    })
  }

  changeCard(index) {
    if (this.list().length !== 0) {
      this.buriedPointService.addStat('dida_dpm_caerus_home_promotioncard_click', {
        page_name: this.page_name,
        indicator_name: this.list()[index - 1].extendNames[0].extendName,
      })
    }
    this.index.set(index)
    this.bizType.set('1')
    this.getChartData()
  }

  changeItem() {
    this.getChartData()
  }

  @SwitchMap()
  getChartData() {
    if (this.chartMetrics().length === 0 || !this.dt().endTime) {
      return
    }
    this.loading.set(true)
    const body = this.formService.value()
    const [, end] = getMonthFirstAndLastDay(this.dt().endTime)
    body.dt = {
      startTime: this.dt().startTime,
      endTime: end,
    }
    body.dataFillConfig = {
      open: 0,
      fillRangeType: 1,
      fillValue: 1,
    }
    body.dimensions = [
      {
        id: null,
        extendName: 'dt',
      },
    ]

    const checkedMetrics = this.chartMetrics().filter(c => c.checked)
    const _metrics = checkedMetrics.reduce((acc, c) => acc.concat(c.value), [])

    let metrics
    let extraMetric = []
    if (this.bizType() === '1') {
      metrics = _metrics.filter(m => m.displayOrder !== 4 && m.displayOrder !== 5 && m.displayOrder !== 6)
    } else {
      metrics = _metrics.filter(m => m.displayOrder === 4)
    }

    if (this.accountfor()) {
      extraMetric = _metrics
        .filter(m => {
          if (this.bizType() === '1') {
            return m.displayOrder === 5
          } else {
            return m.displayOrder === 6
          }
        })
        .map(m => {
          return {
            extendName: m.extendName,
            filter: {
              items: [],
              type: null,
            },
            userDefAliasName: `${m.aliasName}`,
            userDefExtendName: `${m.extendName}`,
          }
        })
    }

    const arr = this.checkValue().map((c, i) => {
      return metrics.map((metric, index) => {
        if (!c) {
          return {
            extendName: metric.extendName,
            filter: {
              items: [],
              type: null,
            },
            userDefAliasName: `${metric.aliasName}_全部`,
            userDefExtendName: `${metric.extendName}_${i}_${index}`,
          }
        } else {
          return {
            extendName: metric.extendName,
            filter: {
              items: [
                {
                  conditionType: 2,
                  condition: '=',
                  extendName: c.extendName,
                  value: [
                    {
                      key: c.key,
                      value: c.value,
                    },
                  ],
                  valueType: null,
                },
              ],
              type: null,
            },
            userDefAliasName: `${metric.aliasName}_${c.showValue}`,
            userDefExtendName: `${metric.extendName}_${c.extendName}_${c.key}`,
          }
        }
      })
    })

    body.metrics = [...flatten(arr), ...extraMetric]

    this.legendControlService.reset()
    return this.queryService
      .search(body, 'output-tracking-chart')
      .pipe(finalize(() => this.loading.set(false)))
      .subscribe(res => {
        if (res.data) {
          // if (this.bizType() === '1') {
          //   const obj = {}
          //   checkedMetrics.forEach(c => {
          //     const order2 = find(c.value, ['displayOrder', 2])
          //     if (order2) {
          //       const order1 = find(c.value, ['displayOrder', 1])
          //       obj[order1.extendName] = order2.extendName
          //     }
          //   })
          //   const result = {}
          //   Object.keys(obj).forEach(o => {
          //     result[`${res.data.headers[o].aliasName}`] = []
          //   })
          //   res.data.data.forEach(d => {
          //     Object.keys(obj).forEach(o => {
          //       result[`${res.data.headers[o].aliasName}`].push(`${toNumber(d[o]) - toNumber(d[obj[o]])}`)
          //     })
          //   })
          //   this._setChartData(res.data, result)
          // } else {
          //   this._setChartData(res.data, null)
          // }

          this._setChartData(res.data, null)
        } else {
          this.option.set(null)
          this.errorMessage.set(res.message)
        }
      })
  }

  @SwitchMap()
  private getCardData() {
    if (this.currentMetrics().length === 0) {
      return
    }
    const body = this.formService.value()
    const endTime = this.dt().endTime
    body.dt = {
      startTime: endTime,
      endTime,
    }

    body.dimensions = [
      {
        id: null,
        extendName: 'dt',
        predefineCompareType: this.type() === 'day' ? ['yw', 'dt'] : ['ym', 'year'],
      },
    ]

    body.metrics = this.currentMetrics()
      .reduce((arr, m) => arr.concat(m.extendNames.map(ex => ex.extendName)), [])
      .map(extendName => ({ extendName }))

    body.dataFillConfig = {
      open: 1,
      fillRangeType: 2,
      fillValue: 1,
    }
    this.cardLoading.set(true)

    return this.queryService
      .search(body, 'output-tracking-card')
      .pipe(finalize(() => this.cardLoading.set(false)))
      .subscribe(res => {
        if (res.data.data.length !== 0) {
          const _data = res.data.data[0]
          this.currentMetrics().forEach(m => {
            m.extendNames.forEach(ex => {
              ex.data = _data[ex.extendName] || 0
              if (ex.displayOrder === 1 || ex.displayOrder === 4) {
                // if (this.type() === 'day') {
                ex.dt = _data[`${ex.extendName}${this.radioMap.day}`]
                ex.yw = _data[`${ex.extendName}${this.radioMap.week}`]
                // debugger
                // } else {
                ex.ym = _data[`${ex.extendName}${this.radioMap.month}`]
                ex.year = _data[`${ex.extendName}${this.radioMap.year}`]
                // }
              }
            })
            m.diff = m.extendNames[1] ? m.extendNames[0]?.data - m.extendNames[1]?.data : 0
          })
        }
        this.list.set(this.currentMetrics())
      })
  }

  private _setChartData(data: QueryOutputVo, diffData) {
    try {
      const chart = new ProgressTrend({ ...data, diffData })
      const legendItemClick = LegendItemClickHandler(this.legendControlService)
      chart.plotOptions.series.events = { legendItemClick }
      this.option.set(chart?.getOption() || null)
    } catch (e) {
      this.option.set(null)
      this.errorMessage.set(e)
      console.error(e)
    }
  }
}
