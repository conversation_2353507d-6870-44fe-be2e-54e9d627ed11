import { Router } from '@angular/router'
import { ChangeDetectionStrategy, Component, inject } from '@angular/core'
import { HeadingComponent } from '@shared/components/heading'
import { IconPromotionComponent } from '@shared/modules/icons'
import { OrderTrackingComponent } from './order-tracking/order-tracking.component'
import { QualityMonitorComponent } from './quality-monitor/quality-monitor.component'

@Component({
  selector: 'app-offplt-promt',
  template: `
    <div class="flex items-center justify-between">
      <app-heading title="端外推广进度监控" description="注：激活、注册及乘客相关指标是自有渠道数据；车主相关指标是全部渠道数据。">
        <PromotionIcon ngProjectAs="[icon]" class="text-2xl" />
      </app-heading>
      <span class="text-center pt-3 cursor-pointer text-blue-500" style="font-size: 15px" (click)="toOffPlt()">
        查看更多端外推广内容
      </span>
    </div>

    <div class="p-5 space-y-6">
      <app-offplt-promt-order-tracking class="block" />
      <app-quality-monitor class="block" />
    </div>

  `,
  host: {
    class: 'block px-5',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [HeadingComponent, IconPromotionComponent, OrderTrackingComponent, QualityMonitorComponent],
})
export class OffpltPromtComponent {
  readonly router = inject(Router)

  toOffPlt() {
    this.router.navigateByUrl('monitor/business/offplt')
  }
}
