import { XAxisOptions, YAxisOptions } from 'highcharts'
import { getCategories, getNumberFields, getPercentFields, isEveryElementPercent } from '@common/chart'
import { BaseHighCharts, SeriesItem } from '@common/chart/highcharts'
import { createPoint, createValueElement, isEmpty, toDecimals } from '@common/function'
import { QueryOutputHeaderVo, QueryOutputVo } from '@api/query-engine/model'
import { flattenDeep, sortBy, dropRight, uniq } from 'lodash'

function tooltipFormatter(that: ProgressTrend, diffData: any) {
  return function () {
    const result = []
    const map = new Map()
    const params = this.points.sort(that.sortFn)
    params.forEach(item => {
      if (map.has(item.series.name)) {
        const arr = map.get(item.series.name) as any[]
        arr.push(item)
        arr.reverse()
      } else {
        map.set(item.series.name, [item])
      }
    })

    const merged = [...map.values()].flat(1)

    const sort1 = [null, '_推广', '_自然']
    // const sort2 = [null, '占比']
    const arr1 = sort1.map(s => {
      return merged.filter(m => {
        if (s) {
          return m.point.name.includes(s)
        } else {
          return !m.point.name.includes('_推广') && !m.point.name.includes('_自然')
        }
      })
    })
    const ratio1 = flattenDeep(arr1).filter(a => {
      return a.point.name.includes('占比')
    })
    const ratio2 = flattenDeep(arr1).filter(a => {
      return !a.point.name.includes('占比')
    })
    // const arr2 = arr1.map(a => {
    //   return sort2.map(s => {
    //     return a.filter(m => {
    //       if (!s) {
    //         return !m.point.name.includes('占比')
    //       } else {
    //         return m.point.name.includes(s)
    //       }
    //     })
    //   })
    // })
    const tooltip = sortBy(ratio2, [
      function (m) {
        return m.point.name
      },
    ])
    const arrr = [...tooltip, ...ratio1]

    result.push('<table class="text-sm">')
    arrr.forEach((point, index) => {
      const {
        series: {
          yAxis: { index: yAxisIndex },
        },
        y: value,
        color,
        point: { name: seriesName },
      } = point
      const { isPercent } = that.series.find(item => item.data[0]?.name === seriesName)
      const categorie = point.x
      const previousItem = merged[index - 1]
      const isDateStr = /\d{4}-\d{2}-\d{2}/.test(categorie)
      const day = new Date(categorie.replace(/-/g, '/')).getDay()
      const weeks = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
      const week = isDateStr && weeks[day]
      const isCompletion = /完成度$/.test(seriesName)
      let text = ''
      if (diffData) {
        if (Object.keys(diffData).includes(seriesName)) {
          const data = diffData[seriesName][point.point.index]
          if (Number(data) > 0) {
            text = `<span class="text-red-500 text-[12px]">（较目标值多${data}）</span>`
          } else {
            text = `<span class="text-green-500 text-[12px]">（较目标值差${data}）</span>`
          }
        }
      }

      if (previousItem?.series.name === seriesName) {
        const currentValue = value
        const previousValue = previousItem.y
        const ratioValue = toDecimals((currentValue - previousValue) / previousValue)
        const diffValue = currentValue - previousValue
        // style="color:${color}"
        result.push(`
          <tr>
            <td class="flex items-center">
              ${createPoint(color)}
              (
                ${categorie}
                ${week ? '<span class="flex-1 min-w-0 w-1"></span>' : ''}
                ${week || ''}
              )
            </td>
            <td class="pr-2" >${seriesName}: </td>
            <td class="text-right">
              ${
                yAxisIndex === 1 || isPercent
                  ? Number.isFinite(value)
                    ? value + '%'
                    : '-'
                  : Number.isFinite(value)
                    ? Intl.NumberFormat().format(value)
                    : '-'
              }
            </td>
          </tr>
        `)
      } else {
        let opacity = 1
        if (seriesName.includes('完成度')) {
          opacity = 0.5
        }

        result.push(`
          <tr>
            <td class="flex items-center"">
              <span style="opacity: ${opacity}">${createPoint(color)}</span>
              (${categorie}${week ? '<span class="flex-1 min-w-0 w-1"></span>' : ''}${week || ''})
            </td>
            <td class="pr-2">${seriesName}: </td>
            <td class="text-right ${isCompletion && value < 100 ? 'text-red-500' : ''}">
              ${
                yAxisIndex === 1 || isPercent
                  ? Number.isFinite(value)
                    ? value + '%'
                    : '-'
                  : Number.isFinite(value)
                    ? Intl.NumberFormat().format(value)
                    : '-'
              }
            </td>
            <td>${text}</td>
          </tr>
        `)
      }
    })
    result.push('</table>')

    return result.join('')
  }
}

class xAxisItem {
  categories: string[]
  opposite: boolean
  tickInterval = 1
  tickWidth = 1
  tickColor = '#ccd6eb'
  lineColor = '#ccd6eb'
  gridLineColor = '#e6e6e6'
  crosshair = true
  linkedTo: number
  labels = {
    useHTML: true,
    formatter: function () {
      if (/\d{4}-\d{2}-\d{2}/.test(this.value)) {
        const day = new Date(this.value.replace(/-/g, '/')).getDay()
        if (day === 0 || day === 6) {
          return `<span class="text-primary font-semibold">${this.value}</span>`
        }
      }

      return this.value
    },
  }

  constructor({ categories, opposite }: Partial<xAxisItem>) {
    categories && (this.categories = categories)
    opposite && (this.opposite = opposite)
  }
}

export class ProgressTrend extends BaseHighCharts {
  xAxis: XAxisOptions = {}
  yAxis: YAxisOptions[] = [
    {
      title: { text: '' },
      labels: { format: undefined },
      gridLineWidth: 1,
      gridLineColor: '#e6e6e6',
    },
  ]
  diffData: any

  colors = [
    'rgba(80,135,236)',
    'rgba(80,135,236)',
    'rgba(80,135,236)',
    'rgba(80,135,236)',
    'rgba(238,117,47)',
    'rgba(238,117,47)',
    'rgba(238,117,47)',
    'rgba(88,165,92,0.7)',
    'rgba(242,189,66)',
    'rgba(80,135,236)',
    'rgba(238,117,47)',
    'rgba(104,187,196)',
    'rgba(88,165,92,0.7)',
    'rgba(242,189,66,0.7)',
    'rgba(238,117,47,0.7)',
    'rgba(176,102,93,0.7)',
    'rgba(228,196,119,0.7)',
    'rgba(163,194,251,0.7)',
    'rgba(160,208,213,0.7)',
    'rgba(152,179,226,0.7)',
    'rgba(222,134,143,0.7)',
    'rgba(244,206,152,0.7)',
    'rgba(180,200,217,0.7)',
    'rgba(147,210,243,0.7)',
    'rgba(64,149,229,0.7)',
    'rgba(127,131,247,0.7)',
    '#E99D42',
    '#CBA43F',
    '#BFBF3D',
    '#81B337',
    '#347CAF',
    '#377F7F',
    '#FCCA00',
    '#B886F8',
    '#A16222',
    '#5087EC',
    '#68BBC4',
    '#58A55C',
    '#F2BD42',
    '#EE752F',
    '#B0665D',
    '#E4C477',
    '#A3C2FB',
    '#A0D0D5',
    '#98B3E2',
    '#DE868F',
    '#F4CE98',
    '#B4C8D9',
    '#93D2F3',
    '#4095E5',
    '#7F83F7',
  ]

  plotOptions = {
    series: {
      turboThreshold: 999999999,
      marker: {
        radius: 2,
        symbol: 'circle',
      },
    } as any,
    column: {
      grouping: false,
      shadow: false,
      borderWidth: 0,
    } as any,
  }

  customSort(array) {
    // 定义排序优先级
    const prefixOrder = ['全部', '推广', '自然']
    const suffixOrder = ['目标', '实际值', '完成率']

    // 创建自定义排序映射
    const suffixMap = {
      目标值: 0,
      实际值: 1,
      完成度: 2,
    }

    // 排序逻辑
    return array.sort((a, b) => {
      // 使用正则表达式拆分前缀和后缀
      const [aPrefix, aSuffix] = a.name.match(/(.+)_(.+)/).slice(1)
      const [bPrefix, bSuffix] = b.name.match(/(.+)_(.+)/).slice(1)

      // 前缀优先级比较
      if (aPrefix !== bPrefix) {
        return prefixOrder.indexOf(aPrefix) - prefixOrder.indexOf(bPrefix)
      }

      // 后缀优先级比较
      return suffixMap[aSuffix] - suffixMap[bSuffix]
    })
  }

  seriesSortFn = (a: SeriesItem, b: SeriesItem) => {
    if (a.name.includes('目标值') && !b.name.includes('目标值')) {
      return 1 // a排在前面
    }
    if (!a.name.includes('目标值') && b.name.includes('目标值')) {
      return -1 // b排在前面
    }

    if (a.name.includes('完成度') && !b.name.includes('完成度')) {
      return 1 // a排在后面
    }
    if (!a.name.includes('完成度') && b.name.includes('完成度')) {
      return -1 // b排在后面
    }
    return 0 // 如果没有关键字的区别，维持原有顺序
  }

  /**
   * Constructs a new instance of the LineMultipleXAxis class.
   *
   * @param properties - The properties used to initialize the LineMultipleXAxis instance.
   * @throws {string} Throws an error if the x-axis is empty.
   */
  constructor(properties: any, debug?: boolean) {
    super()

    const { headers, data, diffData } = properties

    const xaxis = getCategories(headers, data)

    const _series = this.getSeries(headers, data, 0).sort(this.seriesSortFn)
    // this.getSeries(headers, data, 0).forEach(a => {
    //   if (a.name.includes('目标值')) {
    //     _series.push(a)
    //   } else if (!a.name.includes('目标值') && !a.name.includes('完成度')) {
    //     _series.push(a)
    //   } else {
    //     _series.push(a)
    //   }
    // })

    if (diffData) {
      this.diffData = diffData
    }

    let series = _series
    console.log('series', _series)

    const sort1 = ['全部', '推广', '自然']

    const names = uniq(
      _series
        .filter(s => {
          return !s.name.includes('占比')
        })
        .map(s => {
          const isContained = sort1.some(keyword => s.name.includes(keyword))
          if (!isContained) {
            return undefined
          }
          const arr = s.name.split('_')
          if (arr.length === 1) {
            return s.name.split('_')[1]
          }
          return s.name.split('_')[s.name.split('_').length - 1]
        })
    )
    console.log('names', names)

    if (_series.length > 4) {
      const extra = _series.filter(m => {
        return !m.name.includes('_')
      })

      const filter = _series.filter(m => {
        return m.name.includes('_')
      })

      const arr1 = sort1.map(s => {
        return filter.filter(m => {
          return m.name.includes(s)
        })
      })

      series = [...flattenDeep(arr1), ...extra]
    }

    const colItems = []
    const linkedTo = [1, 2, 4, 5]
    console.log('_series', _series)
    series.forEach((item, index) => {
      if (item.type === 'column') {
        colItems.push({ ...item, originalIndex: index })
      }
      if (names.length > 1 && _series.length < 4) {
        if (item.type === 'line') {
          item.color = this.colors[7]
          item.opacity = 0.5
        }
      } else {
        // item.color = this.colors[index]
        if (item.type === 'line') {
          item.color = this.colors[index]
          item.opacity = 0.5
        }
      }
      if (item.name.includes('占比')) {
        item.color = this.colors[7]
      }

      if (item.name.includes('目标') && item.type === 'column') {
        if (series.length <= 3) {
          item.pointPadding = 0.1 // series小于3个时，加粗目标column
        } else {
          item.pointPadding = 0.25
        }
      } else if (item.type === 'column') {
        if (series.length <= 3) {
          // series小于3个时，加粗完成度column
          item.pointPadding = 0.3
        } else {
          item.pointPadding = 0.4
        }
      }

      if (_series.length > 4) {
        if (linkedTo.includes(index)) {
          item.linkedTo = ':previous'
        }
        if (index === 1 || index === 2) {
          item.name = series[0].name
        }
        if (index === 4 || index === 5) {
          item.name = series[3].name
        }
      }
    })

    // 没有目标值的情况
    if (names.length > 1 && _series.length < 4) {
      colItems.forEach((item, colIndex) => {
        if (colIndex === 0) {
          series[item.originalIndex].pointPlacement = -0.15
          series[item.originalIndex].color = this.colors[0]
        } else {
          series[item.originalIndex].pointPlacement = 0.15
          series[item.originalIndex].color = this.colors[4]
        }
      })
    } else {
      const _c = ['rgba(80,135,236,0.2)', 'rgba(80,135,236)']
      const _c2 = ['rgba(238,117,47,0.2)', 'rgba(238,117,47)']
      colItems.forEach((item, colIndex) => {
        if (colIndex === 0 || colIndex === 1) {
          series[item.originalIndex].pointPlacement = -0.15
          if (item.name.includes('目标值')) {
            series[item.originalIndex].color = _c[0]
          } else {
            series[item.originalIndex].color = _c[1]
          }
        } else {
          series[item.originalIndex].pointPlacement = 0.15
          // series[item.originalIndex].color = this.colors[4]
          if (item.name.includes('目标值')) {
            series[item.originalIndex].color = _c2[0]
          } else {
            series[item.originalIndex].color = _c2[1]
          }
        }
      })
    }

    const [percentFields] = getPercentFields(headers)
    const isEveryFieldPercent = isEveryElementPercent(headers)

    if (debug) {
      debugger
    }

    if (isEmpty(xaxis)) {
      throw 'x轴不能为空'
    }

    if (isEveryFieldPercent) {
      this.yAxis = [
        {
          title: { text: '' },
          labels: { format: '{text}%' },
          gridLineWidth: 1,
          gridLineColor: '#e6e6e6',
        },
      ]
    } else if (percentFields) {
      this.yAxis.push({
        title: { text: '' },
        labels: { format: '{text}%' },
        gridLineWidth: 1,
        gridLineColor: '#e6e6e6',
        opposite: true,
      })
    }

    this.setCategories([xaxis])
    this.setSeries(series)

    this.legend.verticalAlign = 'top'
    this.responsive = null
  }

  getSeries(headers: { [key: string]: QueryOutputHeaderVo }, data: { [key: string]: string }[], index?: number) {
    const numberFields = getNumberFields(headers)
    const isEveryFieldPercent = isEveryElementPercent(headers)
    const source = ['当前', '对比']

    if (!data) {
      throw `${source[index] ?? ''}日期无数据`
    }

    return numberFields.map((key, i) => {
      const { aliasName, dataUnit } = headers[key]
      const series = new SeriesItem()

      if (index > 0) {
        series.dashStyle = 'Dash'
        series.lineWidth = 1
        series.linkedTo = `${key}`
      }

      if (dataUnit === '%') {
        series.isPercent = true
        // series.type = 'column'
        series.type = 'line'
        if (aliasName.endsWith('目标')) {
          series.lineWidth = 1
          series.dashStyle = 'ShortDash'
        }
      } else {
        series.type = 'column'
        series.zIndex = 2
      }

      if (dataUnit === '%' && !isEveryFieldPercent) {
        series.yAxis = 1
      }

      series.xAxis = index
      series.name = aliasName

      series.data = data.map(item => {
        const value = Number(item[key])

        if (item[key] === null) {
          return {
            y: null,
            name: aliasName,
          }
        }

        if (dataUnit === '%') {
          return {
            y: toDecimals(value),
            name: aliasName,
          }
        }

        return {
          y: value,
          name: aliasName,
        }
      })
      return series
    })
  }

  /**
   * Sets the categories for the multiple x-axis.
   *
   * @param values - The array of string arrays representing the categories.
   */
  setCategories(values: string[][]): void {
    values.forEach((categories, index) => {
      const item = new xAxisItem({
        categories,
        opposite: index > 0,
      })

      if (index > 0) {
        item.linkedTo = 0
      }

      this.xAxis = item
    })
  }

  override getOption() {
    const value = this

    // console.clear()
    // console.log(value);

    return {
      ...value,
      tooltip: {
        ...value.tooltip,
        formatter: tooltipFormatter(this, this.diffData),
      },
      plotOptions: {
        ...value.plotOptions,
        series: {
          ...value.plotOptions.series,
        },
      },
    }
  }
}
