<span class="flex items-center gap-x-1.5 font-black text-base">
  <MapArrowRightIcon />
  端内营销费用
  <span class="text-neutral-400 text-[12px] pl-4">
    说明：此处优惠券核销要求业务完单；且按实际核销时间统计，对应乘客侧的支付时间和车主侧的给车主打款
  </span>
</span>

<div class="flex flex-col gap-1">
  <div class="flex flex-1 flex-wrap items-center gap-5 py-3 px-2">
    <div class="flex items-center">
      <label class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap">
        统计口径：
      </label>
      <app-radio-group class="relative flex gap-1" [(ngModel)]="type" (ngModelChange)="changeType()">
        <!-- <app-radio class="tag-radio-new" activeClass="active" value="day">昨日</app-radio> -->
        <app-radio class="tag-radio-new" activeClass="active" value="month">当月累计</app-radio>
        <app-radio-thumb class="rounded-xs bg-primary" />
      </app-radio-group>
    </div>
  </div>
  <div class="flex flex-auto gap-4 h-135">
    <div class="flex flex-wrap min-w-75 max-w-75 h-full gap-y-4">
      @if (cardLoading()) {
        <div class="flex items-center justify-center w-full">
          <app-line-spin />
        </div>
      } @else {
        <app-home-card
          [list]="list()"
          compareType="year"
          (updateIndex)="changeCard($event)"
          [themeArr]="['blue', 'orange', 'teal']"
          [compareType]="'year'"
          [isTime]="true"
          [isCost]="true"
        ></app-home-card>
      }
    </div>
    <div class="flex-1 min-w-0 flex flex-col h-full shadow-md rounded-sm border border-neutral-100 relative">
      <div class="flex-1 min-h-0 relative flex items-center justify-center text-xs">
        @if (loading()) {
          <app-line-spin />
        } @else {
          @if (option()) {
            <app-graph class="absolute inset-2" [options]="option()" showAnnotations showAvgPlotLines />
          } @else if (errorMessage()) {
            <span class="text-slate-600 text-xs">{{ errorMessage() }}</span>
          }
        }
      </div>
    </div>
  </div>
</div>
