import { ChangeDetectionStrategy, Component } from '@angular/core'
import { FormsModule } from '@angular/forms'
import { HeadingComponent } from '@shared/components/heading'
import { IconMarketingComponent } from '@shared/modules/icons'
import { InternalCostComponent } from './internal-cost/internal-cost.component'
import { OffpltCostComponent } from './offplt-cost/offplt-cost.component'

@Component({
  selector: 'app-cost-off-progress',
  imports: [HeadingComponent, FormsModule, IconMarketingComponent, InternalCostComponent, OffpltCostComponent],
  templateUrl: './cost-off-progress.component.html',
  host: {
    'class': 'block px-5'
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class CostOffProgressComponent {}
