import { AfterViewInit, ChangeDetectionStrategy, Component, computed, DestroyRef, inject, signal } from '@angular/core'
import { FormsModule } from '@angular/forms'
import { RadioModule } from '@shared/modules/headless'
import { IconMapArrowRightComponent } from '@shared/modules/icons'
import { HomeCardComponent } from '../../home-card/home-card.component'
import { LineSpinComponent } from '@shared/components/line-spin'
import { GraphComponent } from '@shared/components/graph'
import { NzPopoverModule } from 'ng-zorro-antd/popover'
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox'
import { LegendControlService, LegendItemClickHandler } from '@common/service'
import { QueryEngineFormService } from '@common/service/query-engine'
import { CaerusApiService } from '@api/caerus'
import { QueryEngineApiService } from '@api/query-engine'
import { groupBy } from 'lodash'
import { combineLatest, finalize } from 'rxjs'
import { QueryOutputVo } from '@api/query-engine/model'
import { ProgressTrend } from '../../order-tracking-overview/lib'
import { DatePipe } from '@angular/common'
import { HomeService } from '@views/home/<USER>'
import { getMonthFirstAndLastDay, getMonthFirstAndNowDay } from '@common/class'
import { takeUntilDestroyed } from '@angular/core/rxjs-interop'
import { SwitchMap } from '@common/decorator'

@Component({
  selector: 'app-offplt-cost',
  imports: [
    IconMapArrowRightComponent,
    FormsModule,
    RadioModule,
    HomeCardComponent,
    LineSpinComponent,
    GraphComponent,
    NzPopoverModule,
    NzCheckboxModule,
  ],
  providers: [LegendControlService, QueryEngineFormService, DatePipe],
  templateUrl: './offplt-cost.component.html',
  styles: ``,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class OffpltCostComponent implements AfterViewInit {
  readonly apiService = inject(CaerusApiService)
  readonly formService = inject(QueryEngineFormService)
  readonly queryService = inject(QueryEngineApiService)
  readonly legendControlService = inject(LegendControlService)
  readonly datePipe = inject(DatePipe)
  readonly destroyRef = inject(DestroyRef)
  readonly service = inject(HomeService)

  readonly radioMap = {
    day: ':dt_DIFF_RATIO',
    week: ':yw_DIFF_RATIO',
    month: ':ym_DIFF_RATIO',
    year: ':year_DIFF_RATIO',
  }

  type = signal('month')
  allMetrics = signal(null)
  currentMetrics = signal([])
  list = signal([])
  cardLoading = signal(false)
  index = signal(1)
  loading = signal(false)
  option = signal(null)
  errorMessage = signal(null)
  dt = signal(null)

  chartMetrics = computed(() => {
    if (!this.allMetrics()) {
      return []
    }
    const dimensions = this.allMetrics()[`fee_caver_promt_caver_${this.type()}_tab1`]
    const groupDimension = groupBy(dimensions.subMetric, 'tagName')
    const arr = Object.keys(groupDimension).map((dimension, index) => {
      return {
        showName: dimension,
        aliasName: groupDimension[dimension][0].aliasName,
        bizExpression: groupDimension[dimension][0].bizExpression,
        value: groupDimension[dimension].map(d => {
          return {
            extendName: d.extendName,
          }
        }),
        checked: index === this.index() - 1,
      }
    })
    return arr
  })

  ngAfterViewInit(): void {
    this.fetchMetrics()
    this.subscribeToMonthChange()
  }

  subscribeToMonthChange() {
    combineLatest([this.service.month$, this.service.updateTimeResource$])
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(([value, updateTime]) => {
        if (!updateTime) {
          return
        }
        const month = this.datePipe.transform(value, 'yyyy-MM-dd')
        const [startTime] = getMonthFirstAndNowDay(month)
        this.dt.set({
          startTime,
          endTime: updateTime,
        })
        this.getCardData()
        // this.getChartData()
      })
  }

  dealGroupDimension() {
    this.loading.set(true)
    const dimensions = this.allMetrics()[`fee_caver_promt_caver_${this.type()}_tab1`]
    const groupDimension = groupBy(
      dimensions.subMetric.filter(d => d.recommend === 1),
      'tagName'
    )
    const arr = Object.values(groupDimension).map((item: any, index) => {
      const extendNames = item.map(sub => ({
        extendName: sub.extendName,
        recommend: sub.recommend,
        displayOrder: sub.displayOrder,
      }))
      return {
        aliasName: item[0].aliasName,
        bizExpression: item[0].bizExpression,
        showName: Object.keys(groupDimension)[index],
        extendNames,
      }
    })
    this.currentMetrics.set(arr)
    this.getCardData()
  }

  fetchMetrics() {
    this.apiService.fetchMetricsConfig('cockpit_finance_fee_caver_process_promt_caver_v2').subscribe(res => {
      console.log('internal-cost', res.data)
      if (res.data) {
        this.allMetrics.set(res.data)
        this.dealGroupDimension()
      } else {
        this.currentMetrics.set([])
      }
    })
  }

  changeType() {
    if (!this.allMetrics()) {
      return
    }
    this.dealGroupDimension()
  }

  changeCard(index) {
    this.index.set(index)
    this.getChartData()
  }

  changeItem() {
    this.getChartData()
  }

  @SwitchMap()
  getChartData() {
    if (this.chartMetrics().length === 0 || !this.dt().endTime) {
      return
    }
    this.loading.set(true)
    const body = this.formService.value()
    const [, end] = getMonthFirstAndLastDay(this.dt().endTime)
    body.dt = {
      startTime: this.dt().startTime,
      endTime: end,
    }
    body.dataFillConfig = {
      open: 0,
      fillRangeType: 1,
      fillValue: 1,
    }
    body.dimensions = [
      {
        id: null,
        extendName: 'dt',
      },
    ]

    body.metrics = this.chartMetrics()
      .filter(c => c.checked)
      .reduce((acc, c) => acc.concat(c.value), [])

    this.legendControlService.reset()
    return this.queryService
      .search(body, 'offplt-cost-chart')
      .pipe(finalize(() => this.loading.set(false)))
      .subscribe(res => {
        if (res.data) {
          this._setChartData(res.data)
        } else {
          this.option.set(null)
          this.errorMessage.set(res.message)
        }
      })
  }

  @SwitchMap()
  private getCardData() {
    if (this.currentMetrics().length === 0) {
      return
    }
    const body = this.formService.value()
    const endTime = this.dt().endTime
    // const endTime = '2025-05-08'
    body.dt = {
      startTime: endTime,
      endTime,
    }

    body.dimensions = [
      {
        id: null,
        extendName: 'dt',
        predefineCompareType: this.type() === 'day' ? ['yw', 'dt'] : ['ym', 'year'],
      },
    ]

    body.metrics = this.currentMetrics()
      .reduce((arr, m) => arr.concat(m.extendNames.map(ex => ex.extendName)), [])
      .map(extendName => ({ extendName }))

    body.dataFillConfig = {
      open: 1,
      fillRangeType: 2,
      fillValue: 1,
    }
    this.cardLoading.set(true)

    return this.queryService
      .search(body, 'offplt-cost-card')
      .pipe(finalize(() => this.cardLoading.set(false)))
      .subscribe(res => {
        console.log('offplt-cost-card', res.data)
        if (res.data.data.length !== 0) {
          const _data = res.data.data[0]
          this.currentMetrics().forEach(m => {
            m.extendNames.forEach(ex => {
              ex.data = _data[ex.extendName] || 0
              if (ex.displayOrder === 1) {
                if (this.type() === 'day') {
                  ex.dt = _data[`${ex.extendName}${this.radioMap.day}`]
                  ex.yw = _data[`${ex.extendName}${this.radioMap.week}`]
                } else {
                  ex.ym = _data[`${ex.extendName}${this.radioMap.month}`]
                  ex.year = _data[`${ex.extendName}${this.radioMap.year}`]
                }
              }
            })
            m.diff = (m.extendNames[2].data - m.extendNames[3].data) * 100
          })
        }
        this.list.set(this.currentMetrics())
      })
  }

  private _setChartData(data: QueryOutputVo) {
    try {
      const chart = new ProgressTrend(data)
      const legendItemClick = LegendItemClickHandler(this.legendControlService)
      chart.plotOptions.series.events = { legendItemClick }
      this.option.set(chart?.getOption() || null)
    } catch (e) {
      this.option.set(null)
      this.errorMessage.set(e)
      console.error(e)
    }
  }
}
