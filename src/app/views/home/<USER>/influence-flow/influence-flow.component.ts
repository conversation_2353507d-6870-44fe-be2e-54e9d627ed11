import { ChangeDetectionStrategy, Component } from '@angular/core';

import { IconFlowComponent } from '@shared/modules/icons';
import { HeadingComponent } from '@shared/components/heading';
import { InfluenceFlowItemComponent } from './influence-flow-item';

@Component({
  selector: 'app-influence-flow',
  templateUrl: './influence-flow.component.html',
  host: {
    'class': 'block px-5'
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    HeadingComponent,
    IconFlowComponent,
    InfluenceFlowItemComponent,
  ],
})
export class InfluenceFlowComponent {

}
