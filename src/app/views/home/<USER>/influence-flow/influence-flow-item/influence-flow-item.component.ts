import { AsyncPipe, NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, computed, effect, inject, input, signal } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { rxResource } from '@angular/core/rxjs-interop';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzPopoverModule } from 'ng-zorro-antd/popover';
import { NzSpinComponent } from 'ng-zorro-antd/spin';
import { format, subMonths, subYears } from 'date-fns';
import { map, of, tap } from 'rxjs';

import { CaerusApiService } from '@api/caerus';
import { QueryEngineApiService } from '@api/query-engine';
import { QueryInputVo } from '@api/query-engine/model';
import { isEmpty, toDecimals, toNumber } from '@common/function';
import { CombinationSeries, GraphCombinationComponent } from '@shared/components/graph';
import { IndicatorModule } from '@shared/components/indicator';
import { TrendDateOptions } from '@views/cockpit/models';
import { IncrementPipe } from '@shared/pipes/increment';
import { HomeService } from '@views/home';


@Component({
  selector: 'app-influence-flow-item',
  templateUrl: './influence-flow-item.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    AsyncPipe,
    FormsModule,
    NgTemplateOutlet,
    NzPopoverModule,
    NzSelectModule,
    NzSpinComponent,
    IndicatorModule,
    GraphCombinationComponent,
    IncrementPipe,
  ],
})
export class InfluenceFlowItemComponent {

  readonly cdr = inject(ChangeDetectorRef);
  readonly apiService = inject(QueryEngineApiService);
  readonly caerusApiService = inject(CaerusApiService);
  readonly service = inject(HomeService);

  type = input.required<'in' | 'out'>();

  date = computed(() => this.service.month());
  trendDate = signal(1);
  trendDateOptions = TrendDateOptions;
  series = signal<CombinationSeries[]>([]);
  categories = computed(() => [...new Set(this.titles())]);
  titles = signal([]);

  
  label = computed(() => this.type() === 'in' ? '流入' : '流出');

  #metrics = computed(() => {
    return this.metricsConfigResource.value()?.map(({ extendName, dataUnit}) => ({
      extendName, 
      dataUnit
    }));
  });

  flowInMetrics = computed(() => {
    return this.metricsConfigResource.value()?.filter(item => item.tagOrder === 1);
  });

  flowOutMetrics = computed(() => {
    return this.metricsConfigResource.value()?.filter(item => item.tagOrder === 2);
  });

  metricsConfigResource = rxResource({
    loader: () => this.caerusApiService.fetchMetricsConfig('cockpit_out_co_trans_rate_metrics').pipe(
      map(res => res.data['tengxun']?.subMetric),
    )
  });

  #filters = computed(() => {
    return {
      items: [],
      type: null,
    }
  })

  valueMap = new Map();

  metricsDataBody = computed(() => {
    const month = format(this.date(), 'yyyy-MM');
    const dt = { startTime: month, endTime: month };
    const dimensions = [{ id: null, extendName: 'ym', predefineCompareType: ['ym', 'year'] }];
    const body: QueryInputVo = {
      dt,
      dtType: 'ym',
      metrics: this.#metrics(),
      dimensions,
      filter: this.#filters(),
      scene: 4,
      dataFillConfig: { open: 1, fillRangeType: 1, fillValue: 1 },
    };


    return body;
  })

  metricsTrendBody = computed(() => {
    const month = this.service.month();
    const amount = this.trendDate();
    const startTime = amount === 0 ? '2023-08' : format(subYears(month, amount), 'yyyy-MM');
    const endTime = format(this.service.month(), 'yyyy-MM');
    const dt = { startTime, endTime }; 
    const dimensions = [{ id: null, extendName: 'ym' }];

    return {
      dt,
      dtType: 'ym',
      metrics: this.#metrics(),
      dimensions,
      filter: { items: [], type: null },
      scene: 4,
      dataFillConfig: { open: 1, fillRangeType: 1, fillValue: 1 },
    } as QueryInputVo;
  })

  metricsDataResource = rxResource({
    request: () => this.metricsDataBody(),
    loader: ({ request }) => isEmpty(request.metrics) 
      ? of(null)
      : this.apiService.search(request, 'tencent-influence-flow-metrics-overview').pipe(
        tap(res => {
          if (res.status !== '00000') {
            throw res.message;
          }
        }),
        map(res => res.data?.data?.at(0)),
        tap(value => {
          Object.keys(value).forEach(key => {
            const { dataUnit } = request.metrics.find(item => item.extendName === key) || {};
            const isPercent = dataUnit === '%';
            let val = Number.isFinite(parseFloat(value[key])) ? parseFloat(value[key]) : null;

            val = (val === null 
              ? null 
              : isPercent
                ? toDecimals(val)
                : val
            )

            if (key !== 'ym' && key.indexOf(':') === -1) {
              const target = this.valueMap.get(key);

              this.valueMap.set(key, { ...target, value: val });
            }
            else if (key.indexOf(':') > -1) {
              const [k, v] = key.split(':');
              const target = this.valueMap.get(k);

              this.valueMap.set(k, {  ...target, [v]: val });
            }
          })

          this.cdr.markForCheck();
        })
      )
  })

  metricsTrendResource = rxResource({
    request: () => this.metricsTrendBody(),
    loader: ({ request }) => isEmpty(request.metrics) 
      ? of(null) 
      : this.apiService.search(request, 'tencent-influence-flow-trend').pipe(
        tap(res => {
          if (res.status !== '00000') {
            throw res.message;
          }
        }),
        map(res => res.data.data),
      )
  })
  
  constructor() {
    effect(() => {
      if (
        this.metricsDataResource.value() &&
        this.metricsTrendResource.value()
      ) {
        const values = this.metricsTrendResource.value();

        this.series.set([]);
        this.titles.set([]);
        this.valueMap.forEach((value) => {
          value.trendVo = [];
        })
        
        values.forEach(item => {
          const [year, month] = item['ym'].split('-').map(toNumber);

          Object.keys(item)
            .filter(key => key !== 'ym')
            .forEach((key, index) => {
              const { dataUnit } = this.#metrics().find(item => item.extendName === key) || {};
              const target = this.valueMap.get(key);
              const trendVo = target.trendVo || [];
              const value = Number.isFinite(parseFloat(item[key])) ? parseFloat(item[key]) : null;
              const isPercent = dataUnit === '%';
              const val = (value === null 
                ? null 
                : isPercent
                  ? toDecimals(value)
                  : value
              )

              trendVo.push(val);
              this.titles.update(titles => titles.concat(`${year}年${month}月`));
              this.valueMap.set(key, {  ...target, trendVo });
            });
        })

        const metrics = this.type() === 'in' 
          ? this.flowInMetrics() 
          : this.flowOutMetrics();

        metrics
          .filter(item => item.displayOrder !== 2)
          .forEach(item => {
            const { trendVo } = this.valueMap.get(item.extendName);
            const series = item.displayOrder === 1
              ? new CombinationSeries(item.showName || '', 'column', 0, 2, trendVo || [])
              : new CombinationSeries(item.showName || '', 'line',   1, 3, trendVo || []);

            this.series.update(items => items.concat(series));
          })
        
        this.cdr.markForCheck();
      }
    })
  }
  
}
