<ng-template #iconTemplate let-index>
  @switch (index) {
    @case (0) { <CooperationIcon [style]="2" class="text-orange-500" /> }
    @case (1) { <UserFillIcon class="text-teal-700" /> }
    @case (2) { <TaxiIcon [style]="3" class="text-teal-700" /> }
    @case (3) { <CollectIcon [style]="2" class="text-blue-700" /> }
    @case (4) { <CardIcon [style]="2" class="text-blue-700" /> }
    @case (5) { <CardIcon [style]="2" class="text-blue-700" /> }
  }
</ng-template>

<header class="relative flex items-center px-3">
  <span class="flex items-center gap-x-1.5 font-black text-base">
    <MapArrowRightIcon />
    核心结果指标
  </span>
</header>

@if (metricsConfigResource.value()) {
  @if (metricsDataResource.error() && !metricsDataResource.isLoading()) {
    <div class="flex items-center justify-center h-85 text-xs">
      {{metricsDataResource.error() | replaceBy: '重试'}}
      <a (click)="metricsDataResource.reload()">重试</a>
    </div>
  }
  @else {
    <nz-spin [nzSpinning]="metricsDataResource.isLoading()">
      <app-indicator-card-group gap="gap-x-4" class="items-stretch w-full px-3 pt-3 pb-3 max-2xl:py-1.5 customized-scrollbar overflow-x-auto">

        @for (items of metricsList(); track $index) {
          <app-indicator-tabs-card
            #tabCardRef
            class="group min-w-64! max-w-72"
            [metrics]="items"
            [valueMap]="metricsValueMap()"
            [trendMap]="metricsTrendMap()"
          >
            @if (tabCardRef.metric() && tabCardRef.valueMap()) {
              <app-indicator-header>
                <app-indicator-avatar class="text-2xl leading-0">
                  
                </app-indicator-avatar>
                <ng-template #metricsTitleTemplate>
                  {{tabCardRef.metric().showName}} <span class="text-xs opacity-30 px-1">({{tabCardRef.metric().aliasName}})</span>
                </ng-template>
                <app-indicator-title
                  nzPopoverPlacement="topLeft"
                  [nzPopoverTitle]="metricsTitleTemplate"
                  [nzPopoverContent]="contentTemplate"
                >
                  <ng-template #contentTemplate>
                    <div [innerHTML]="tabCardRef.metric().bizExpression"></div>
                  </ng-template>
                  {{tabCardRef.metric().card}}
                </app-indicator-title>
                <span class="flex-1 min-w-0"></span>
                <ExpandIcon class="expand-btn relative z-50" iconBtn nz-tooltip="查看" (click)="openMetricsDetail(tabCardRef.metric())" />
              </app-indicator-header>
              
              <app-indicator-content class="gap-y-1!">
                @if (tabCardRef.hasTabs()) {
                  <app-radio-group [(ngModel)]="tabCardRef.tabIndex" class="inline-flex justify-center mt-2 max-2xl:-mt-5 max-2xl:translate-x-2 -translate-y-0.5">
                    @for (tab of tabCardRef.tabs(); track $index) {
                      <app-radio [value]="$index" class="tab-radio" activeClass="active">{{tab.tagName}}</app-radio>
                    }
                  </app-radio-group>
                }
                
                <app-indicator-value class="mt-auto mx-auto text-2xl!" [nullValue]="'-'" [value]="tabCardRef.value()?.value | increment | async">
                  <span class="absolute inline-flex items-start top-0 right-0 translate-x-full">
                    <app-indicator-subtitle>{{tabCardRef.metric().bizTag}}</app-indicator-subtitle>
                  </span>
                </app-indicator-value>

                <app-indicator-compare-group gap="gap-1" class="scale-80">
                  <app-indicator-compare label="月环比" [value]="tabCardRef.value()?.ym_DIFF_RATIO" />
                  <app-indicator-compare label="年同比" [value]="tabCardRef.value()?.year_DIFF_RATIO" />
                </app-indicator-compare-group>

                @let diffState = (
                  tabCardRef.metric().compareDiff === 'target'
                    ? tabCardRef.target()?.value - tabCardRef.value()?.value > 0
                    : tabCardRef.timeDiffRate() > 0
                );

                @let diffLabel = (
                  tabCardRef.metric().compareDiff === 'target' 
                    ? (
                      diffState 
                        ? '<span class="text-green-500">(较目标少{n})</span>' 
                        : '<span class="text-red-600">(较目标多{n})</span>'
                    )
                    : (
                      diffState 
                        ? '<span class="text-red-600">(较时间进度快'+tabCardRef.timeDiffRate()+'pp)</span>' 
                        : '<span class="text-green-500">(较时间进度慢'+tabCardRef.timeDiffRate()+'pp)</span>'
                    )
                );

                @let fractionDigits = tabCardRef.metric().compareDiff === 'target' ? 1 : 2;

                <app-indicator-progress
                  [nullValuePlaceholder]="''"
                  [label]="tabCardRef.rateMetric()?.showName || ''"
                  [targetLabel]="tabCardRef.targetMetric()?.showName || '暂无目标'"
                  [diffLabel]="diffLabel"
                  [value]="tabCardRef.value()?.value"
                  [total]="tabCardRef.target()?.value"
                  [fractionDigits]="fractionDigits"
                />
                <app-indicator-graph class="relative block content-center text-center h-28 max-2xl:h-20 bg-neutral-200/0 rounded-lg font-medium">
                  @if (metricsTrendResource.isLoading()) {
                    <app-line-spin class="inline-block scale-80" />
                  } @else {
                    <app-graph-mini-area [value]="tabCardRef.trendValue()?.trendVo" />
                  }
                </app-indicator-graph>
              </app-indicator-content>
            }
          </app-indicator-tabs-card>
        }
      </app-indicator-card-group>
    </nz-spin>
  }
}