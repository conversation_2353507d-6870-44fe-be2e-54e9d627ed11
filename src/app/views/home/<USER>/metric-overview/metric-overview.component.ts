import { FormsModule } from '@angular/forms';
import { ChangeDetectionStrategy, Component, computed, effect, inject, linkedSignal, signal } from '@angular/core';
import { rxResource } from '@angular/core/rxjs-interop';
import { map } from 'rxjs';
import * as _ from 'lodash';


import { trace } from '@common/const';
import { BuriedPointService } from '@common/service';
import { CaerusApiService } from '@api/caerus';
import { FilterItemVo } from '@api/caerus/model';
import { toFilterItem } from '@common/function';
import { HeadingComponent } from '@shared/components/heading';
import { IconMapArrowRightComponent, IconMoreSquareComponent } from '@shared/modules/icons';
import { AreaSelectComponent } from '@shared/components/area-select';
import { DimensionFilterComponent } from '@shared/components/filters';
import { RadioModule } from '@shared/modules/headless';
import { HomeComponent } from '@views/home';
import { CoreComponent } from './core/core.component';
import { PassengerComponent } from './passenger/passenger.component';
import { DriverComponent } from './driver/driver.component';


@Component({
  selector: 'app-metric-overview',
  template: `
    <app-heading title="大盘核心指标总览">
      <MoreSquareIcon ngProjectAs="[icon]" class="text-2xl" />
    </app-heading>

    <div>
      <div class="filter-container-grid my-3 max-2xl:my-1.5 max-2xl:py-2 max-2xl:px-3">
        <app-dimension-filter label="业务渠道" [(ngModel)]="c_firlev_channel" [options]="firlevChannelOptions()" class="row-start-1" />
        <app-dimension-filter label="产品渠道" [(ngModel)]="c_seclev_channel" [options]="seclevChannelOptions()" class="row-start-1 col-span-12" />
        <app-dimension-filter label="订单类型" [(ngModel)]="c_ord_type"       [options]="orderTypeOptions()"     class="row-start-2 col-start-1" />
        <div class="flex items-center">
          <label class="inline-flex items-center font-bold leading-0 whitespace-nowrap text-xs">区域：</label>
          <app-area-select [(ngModel)]="area" />
        </div>
      </div>
    </div>

    <div class="grid grid-cols-1 gap-4 max-2xl:gap-y-2">
      <app-core />
      
      <div>
        <header class="relative flex items-center gap-x-3 px-3">
          <span class="flex items-center gap-x-1.5 font-black text-base">
            <MapArrowRightIcon />
            关键过程指标
          </span>
          <span class="text-xs text-neutral-400 font-normal">
            注：乘客侧指标为自有渠道，车主侧为全部渠道
          </span>
        </header>

        <div class="grid grid-cols-2 gap-4">
          <app-passenger class="max-xl:col-span-2" />
          <app-driver class="max-xl:col-span-2" />
        </div>
      </div>
    </div>
  `,
  host: {
    'class': 'block px-5'
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    FormsModule,
    HeadingComponent,
    RadioModule,
    AreaSelectComponent,
    DimensionFilterComponent,
    CoreComponent,
    PassengerComponent,
    DriverComponent,
    IconMapArrowRightComponent,
    IconMoreSquareComponent,
  ]
})
export class MetricOverviewComponent {

  readonly caerusApiService = inject(CaerusApiService);
  readonly buriedPointService = inject(BuriedPointService);
  readonly root = inject(HomeComponent);

  c_ord_type = signal<FilterItemVo>(null);
  c_firlev_channel = signal<FilterItemVo>(null);
  c_seclev_channel = linkedSignal<any, any>({
    source: () => this.seclevChannelOptions(),
    computation: (source, previous) => {
      const selected = source.find(item => _.isEqual(item.value, previous?.value));

      return (
        selected?.value || null
      );
    }
  });
  area = signal(null);

  dimensionConfigResource = rxResource({
    loader: () => this.caerusApiService.fetchDimensionConfig('cockpit_dim_v2').pipe(
      map(res => res.data.top_filter)
    )
  });

  orderTypeOptions = computed(() => {
    return this.dimensionConfigResource.value()
      ?.find(item => item.keyName === 'c_ord_type')
      ?.values
      ?.map(toFilterItem) || [];
  })

  firlevChannelOptions = computed(() => {
    return this.dimensionConfigResource.value()
      ?.find(item => item.keyName === 'c_firlev_channel')
      ?.values
      ?.map(toFilterItem) || [];
  })
  
  seclevChannelOptions = computed(() => {
    return this.dimensionConfigResource.value()
      ?.find(item => item.keyName === 'c_thilev_channel')
      ?.values
      ?.filter(() => {
        const firlev_channel = this.c_firlev_channel()?.value?.at(0)?.value;

        if (firlev_channel === '外输渠道') {
          return false;
        }
        return true;
      })
      ?.map(toFilterItem) || [];
  })

  gmv_visible = computed(() => {
    const state = (
      this.c_ord_type() !== null ||
      this.c_firlev_channel()?.value?.at(0)?.value === '外输渠道'
    );
    
    return state;
  })

  constructor() {
    effect(() => {
      trace(`埋点上报：模块筛选项点击`, {
        page_name: this.root.page_name,
        c_firlev_channel: this.c_firlev_channel()?.value?.at(0)?.value || '全部',
        c_seclev_channel: this.c_seclev_channel()?.value?.at(0)?.value || '全部',
        c_ord_type: this.c_ord_type()?.value?.at(0)?.value || '全部',
        area: this.area()?.value || '全部',
      });

      this.buriedPointService.addStat('dida_dpm_caerus_home_vip_fliter_click', {
        page_name: this.root.page_name,
        c_firlev_channel: this.c_firlev_channel()?.value?.at(0)?.value || '全部',
        c_seclev_channel: this.c_seclev_channel()?.value?.at(0)?.value || '全部',
        c_ord_type: this.c_ord_type()?.value?.at(0)?.value || '全部',
        area: this.area()?.value || '全部',
      });
    })
  }

}
