import { AsyncPipe, DatePipe } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, computed, DestroyRef, effect, ElementRef, inject, linkedSignal, signal, viewChild } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { AnimationEvent } from '@angular/animations';
import { rxResource, takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NzSelectModule } from 'ng-zorro-antd/select';
import { NzPopoverModule } from 'ng-zorro-antd/popover';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { filter, map, of, tap } from 'rxjs';
import { addDays, format } from 'date-fns'
import * as _ from 'lodash';

import { Animations } from '@common/animation';
import { _pad, groupBy, isEmpty, isNotNull, isNotUndefinedOrNotNull, isNull, isUndefinedOrNull, toFilterItem } from '@common/function';
import { QueryEngineApiService } from '@api/query-engine';
import { CaerusApiService } from '@api/caerus';
import { MetricVo, QueryInputVo } from '@api/query-engine/model';
import { ModalVisibleState } from '@core/modal';
import { ModalRef, MODAL_DATA } from '@core/dialog';
import { FilterItemVo, MetricsMenuVo } from '@api/caerus/model';
import { IconChevronLeftComponent, IconChevronRightComponent, IconCloseComponent, IconSuccessFillComponent } from '@shared/modules/icons';
import { DateFilterComponent, DimensionFilterComponent } from '@shared/components/filters';
import { IndicatorModule } from '@shared/components/indicator';
import { IncrementPipe } from '@shared/pipes/increment';
import { ValueFormatter } from '@shared/components/value-formatter';
import { SortByPipe } from '@shared/pipes/sort-by';
import { AreaSelectComponent } from '@shared/components/area-select';
import { RadioModule } from '@shared/modules/headless';
import { MetricsGroupDirective } from '@views/home/<USER>';
import { HomeComponent, HomeService } from '@views/home';
import { HighlightPipe } from '@shared/pipes/highlight';
import { MetricTrendComponent } from './metric-trend.component';
import { isDev, trace } from '@common/const';
import { BuriedPointService, UserService } from '@common/service';
import { Sleep } from '@common/decorator';


@Component({
  selector: 'app-metrics-detail',
  templateUrl: './metrics-detail.component.html',
  styleUrls: ['./metrics-detail.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: {
    'class': 'flex w-screen h-screen'
  },
  animations: [
    ...Animations.OpacityAnimation,
    ...Animations.ModalOutToInAnimation,
  ],
  imports: [
    AsyncPipe,
    FormsModule,
    DragDropModule,
    NzSelectModule,
    NzCheckboxModule,
    NzSpinModule,
    NzDatePickerModule,
    NzPopoverModule,
    IndicatorModule,
    RadioModule,
    DateFilterComponent,
    DimensionFilterComponent,
    AreaSelectComponent,
    MetricsGroupDirective,
    // ValueFormatter,
    MetricTrendComponent,
    IconCloseComponent,
    IconChevronLeftComponent,
    IconChevronRightComponent,
    IconSuccessFillComponent,
    SortByPipe,
    IncrementPipe,
    HighlightPipe,
  ],
  providers: [
    DatePipe,
  ],
})
export class MetricsDetailComponent implements AfterViewInit {

  readonly datePipe = inject(DatePipe);
  readonly cdr = inject(ChangeDetectorRef);
  readonly modalRef = inject(ModalRef);
  readonly service = inject(HomeService);
  readonly apiService = inject(QueryEngineApiService);
  readonly buriedPointService = inject(BuriedPointService);
  readonly caerusApiService = inject(CaerusApiService);
  readonly root = inject(HomeComponent);
  readonly destroyRef = inject(DestroyRef);
  readonly userService = inject(UserService);
  readonly hostElement = inject<ElementRef<HTMLElement>>(ElementRef);
  readonly data = inject<{
    metrics: MetricVo[],
    filters: Partial<{
      area: string;
      c_firlev_channel: FilterItemVo;
      c_seclev_channel: FilterItemVo;
      c_ord_type: FilterItemVo;
    }>
  }>(MODAL_DATA);

  carouselContainer = viewChild('carouselContainer', { read: ElementRef });
  dateFilterRef = viewChild(DateFilterComponent);

  valueMap = new Map();
  metricsValueMap = signal(null);
  hasPermission = signal(isDev() ? true : false);

  visible = signal(false);
  visibleState = signal<ModalVisibleState>('invisible');
  colorMap = new Map([
    ['订单与收支', 'orange'],
    ['乘客规模', 'blue'],
    ['车主规模', 'lime'],
  ])

  yesterday = signal(addDays(new Date(), -1))
  
  monthStr = computed(() => {
    const isMonth = this.dateFilterRef().dateType() === 'ym';

    if (isMonth) {
      return this.dateFilterRef().endTime() as string;
    }

    return null;
  })
  updateTimeResource = rxResource({
    request: () => this.monthStr(),
    loader: ({ request }) => !request ? of(null) : this.apiService.fetchUpdateTimeModel2(request).pipe(
      map(res => res.data?.dt || format(this.yesterday(), 'yyyy-MM-dd'))
    ),
  })

  date = signal<Date | string>(this.service.month());
  
  area = linkedSignal<any, any>({
    source: () => {},
    computation: () => {
      return this.data.filters.area || null;
    }
  });

  c_ord_type = linkedSignal<Array<{ key: string; value: FilterItemVo }>, FilterItemVo>({
    source: () => this.orderTypeOptions(),
    computation: (source) => {
      const { value } = this.data.filters.c_ord_type?.value?.at(0) || {};

      if (value && source.length > 0 ) {
        const option = source.find(item => item.key === value);
        return option.value;
      }

      return null;
    }
  });

  c_firlev_channel = linkedSignal<Array<{ key: string; value: FilterItemVo }>, FilterItemVo>({
    source: () => this.firlevChannelOptions(),
    computation: (source) => {
      const { value } = this.data.filters.c_firlev_channel?.value?.at(0) || {};

      if (value && source.length > 0 ) {
        const option = source.find(item => item.key === value);
        return option.value;
      }

      return null;
    }
  });

  c_seclev_channel = linkedSignal<Array<{ key: string; value: FilterItemVo }>, FilterItemVo>({
    source: () => this.seclevChannelOptions(),
    computation: (source) => {
      const { value } = this.data.filters.c_seclev_channel?.value?.at(0) || {};

      if (value && source.length > 0 ) {
        const option = source.find(item => item.key === value);
        return option.value;
      }

      return null;
    }
  });

  c_pass_fourca_user_type = signal<FilterItemVo>(null);
  cdriver_fourca_user_type = signal<FilterItemVo>(null);
  cdriver_abc_type = signal<FilterItemVo>(null);

  metric = linkedSignal({
    source: () => this.data.metrics,
    computation: (source) => {
      if (source) {
        if (this.metricsConfigResource.value()) {
          return this.metricsConfigResource.value().find(item => {
            return _.isEqual(item, source.at(0))
          })
        }
      }
      return null;
    }
  });

  #metrics = computed(() => {    
    return this.metricsConfigResource.value()
      ?.filter(item => !item?.dtQuery)
      ?.map(({ extendName, showName, aliasName, classTag }) => ({ 
        extendName, showName, 
        userDefExtendName: 'COMPARE_'+extendName, 
        userDefAliasName: aliasName,
        classTag 
      }));
  });

  #special_metrics = computed(() => {    
    return this.metricsConfigResource.value()
      ?.filter(item => item?.dtQuery === true)
      ?.map(({ extendName, showName, aliasName }) => ({ 
        extendName, showName, 
        userDefExtendName: 'COMPARE_'+extendName, 
        userDefAliasName: aliasName,
      }));
  });

  metricsList = computed<any>(() => {
    if (this.metricsConfigResource.value()) {
      const res = this.metricsConfigResource.value().filter(item => item.display === 1);
      const groupObj = groupBy(res, 'tagName');
      const groupMap = new Map();
      const arr = Object.keys(groupObj);
      
      arr.forEach(key => {
        const item = groupObj[key];
        const { tagName } = item.at(0);

        if (groupMap.has(tagName)) {
          groupMap.set(tagName, groupMap.get(tagName).concat([item]));
        } else {
          groupMap.set(tagName, [item]);
        }
      })

      const items = [...groupMap.values()].map((c: any[]) => {
        return c.map((m) => {
          return m.filter(item => {
            const exclude = [];

            if (item?.dtQuery === true && this.dateFilterRef().dateType() !== 'ym') {
              return false;
            }
            
            if (item?.auth === true) {
              return this.hasPermission()
            }

            if (this.c_pass_fourca_user_type() !== null) {
              exclude.push('车主规模');
            }

            if (this.cdriver_fourca_user_type() !== null || this.cdriver_abc_type() !== null) {
              exclude.push('乘客规模');
            }

            if (exclude.length > 0) {
              this.restoreMetric();
              return !exclude.includes(item.classTag);
            }

            return true;
          })
        })
      })
      .filter(item => {
        return item.at(0).length > 0;
      });

      // console.log('[items]', items);

      return items;
    }
    return [];
  })

  @Sleep(200)
  restoreMetric() {
    this.metric.set(this.metricsList().at(0).at(0).at(0));
    // console.log('[???]', this.metric());
  }

  metricsConfigResource = rxResource({
    loader: () => this.caerusApiService.fetchMetricsConfigV2('cockpit_top_metrics_v2').pipe(
      map(res => Object.keys(res.data).map(key => res.data[key].subMetric).flat(1)),
    )
  });

  metricsDataResource = rxResource({
    request: () => this.metricsDataBody(),
    loader: ({ request }) => (isNull(request) || isEmpty(request.metrics))
      ? of(null)
      : this.caerusApiService.getCoreResultAll(request, 'key-process-all').pipe(
        tap(res => {
          if (res.status !== '00000') {
            this.metricsValueMap.set(null);
            throw res.message;
          }
        }),
        map(res => res.data?.data?.at(0)),
        // tap(value => {
        //   this.valueMap.clear();
          
        //   Object.keys(value).forEach(key => {
        //     if (key !== this.dateFilterRef().dateType() && key.indexOf(':') === -1) {
        //       const target = this.valueMap.get(key);
        //       this.valueMap.set(key, { ...target, value: parseFloat(value[key]) || null });
        //     }
        //     else if (key.indexOf(':') > -1) {
        //       const [k, v] = key.split(':');
        //       const target = this.valueMap.get(k);

        //       this.valueMap.set(k, {  ...target, [v]: parseFloat(value[key]) || null });
        //     }
        //   })

        //   this.metricsValueMap.set(null);
        //   setTimeout(() => {
        //     this.metricsValueMap.set(this.valueMap);
        //     this.cdr.markForCheck();
        //   }, 0)
        // })
      )
  })

  specialMetricsDataResource = rxResource({
    request: () => this.specialMetricsDataBody(),
    loader: ({ request }) => (isNull(request) || isEmpty(request?.metrics))
      ? of(null)
      : this.caerusApiService.getKeyProcessDt(request, 'special-key-process-all').pipe(
        tap(res => {
          if (res.status !== '00000') {
            this.metricsValueMap.set(null);
            throw res.message;
          }
        }),
        map(res => res.data?.data?.at(0))
      )
  })

  metricsTrendResource = rxResource({
    request: () => this.metricsTrendBody(),
    loader: ({ request }) => (isNull(request) || isEmpty(request.metrics))
      ? of(null) 
      : this.caerusApiService.getCoreResultAllPop(request, 'metrics-trend').pipe(
        tap(res => {
          if (res.status !== '00000') {
            throw res.message;
          }
        }),
        map(res => res.data),
      )
  })

  metricsDataBody = computed(() => {
    /** 指标卡请求参数中日期需等于结束日期 */
    const startTime = this.dateFilterRef().endTime();
    const endTime = this.dateFilterRef().endTime();
    const dt = { startTime, endTime };
    const isDay = this.dateFilterRef().dateType() === 'dt';
    const isWeek = this.dateFilterRef().dateType() === 'yw';
    const predefineCompareType = [
      isDay ? ['dt', 'yw'] : 
      isWeek ? ['yw', 'year']:
      ['ym', 'year']
    ].flat(1);
    const dimensions = [{ id: null, extendName: this.dateFilterRef().dateType(), predefineCompareType }];
    const metrics = this.#metrics()?.map(item => {
      let items = [
        this.c_seclev_channel()
      ];
      
      return {
        ...item,
        filter: {
          items: items.filter(isNotNull),
          type: null
        },
      };
    });

    const body = {
      dt,
      dtType: this.dateFilterRef().dateType(),
      metrics,
      dimensions,
      filter: this.#filters(),
      scene: 4,
      dataFillConfig: { open: 1, fillRangeType: 1, fillValue: 1 },
      queryType: 'compare',
      accelerateFilter: true,
    } as QueryInputVo;

    if (isNull(startTime, endTime)) {
      return null;
    }

    return body;
  })

  specialMetricsDataBody = computed(() => {
    /** 指标卡请求参数中日期需等于结束日期 */
    // const month = this.datePipe.transform(this.service.updateTime(), 'yyyy-MM-dd');
    const month = this.updateTimeResource.value();
    const dt = { startTime: month, endTime: month };
    const dimensions = [{ id: null, extendName: 'dt', predefineCompareType: ['ym', 'year'] }];
    const metrics = this.#special_metrics()?.map(item => {
      let items = [
        this.c_seclev_channel()
      ];
      
      return {
        ...item,
        filter: {
          items: items.filter(isNotNull),
          type: null
        },
      };
    });

    const body = {
      dt,
      dtType: 'dt',
      metrics,
      dimensions,
      filter: this.#filters(),
      scene: 4,
      dataFillConfig: { open: 1, fillRangeType: 1, fillValue: 1 },
      queryType: 'compare',
      accelerateFilter: true,
    } as QueryInputVo;

    if (!month) {
      return null;
    }

    return body;
  })

  metricsTrendBody = computed(() => {
    const startTime = this.dateFilterRef().startTime();
    const endTime = this.dateFilterRef().endTime();
    const dt = { startTime, endTime }; // 近2年
    const isMonth = this.dateFilterRef().dateType() === 'ym';
    const predefineCompareType = [
      isMonth ? 'ym' : 'yw'
    ]
    const dimensions = [{ id: null, extendName: this.dateFilterRef().dateType(), predefineCompareType }];

    if (isNull(startTime, endTime)) {
      return null;
    }

    const metrics = [this.metric()]
      .map(({ extendName, showName, aliasName, classTag }) => ({ 
        extendName, showName, 
        userDefExtendName: 'COMPARE_'+extendName, 
        userDefAliasName: aliasName,
        classTag 
      }))
      .map(item => {
        let items = [ this.c_seclev_channel() ];
        
        return {
          ...item,
          filter: {
            items: items.filter(isNotNull),
            type: null
          },
        };
      });

    
    return {
      dt,
      dtType: this.dateFilterRef().dateType(),
      metrics,
      dimensions,
      filter: this.#filters(),
      scene: 4,
      dataFillConfig: { open: 1, fillRangeType: 1, fillValue: 1 },
      queryType: 'compare',
      accelerateFilter: true,
    } as QueryInputVo;
  })

  dimensionConfigResource = rxResource({
    loader: () => this.caerusApiService.fetchDimensionConfig('cockpit_dim_v2').pipe(
      map(res => res.data?.pop_filter),
      // tap(res => console.log(res))
    )
  })

  metricsCategoriesOptions = computed(() => {
    if (this.metricsConfigResource.value()) {
      const items = groupBy(this.metricsConfigResource.value(), 'classTag');

      return Object.keys(items).map(key => {
        return items[key].at(0);
      }) as MetricsMenuVo[]
    }

    return [];
  })

  metricsCategories = linkedSignal({
    source: () => [this.metricsCategoriesOptions(), this.metric()] as [MetricsMenuVo[], MetricsMenuVo],
    computation: (source) => {
      const [items, metric] = source;
      const index = items.findIndex(item => item.classTag === metric?.classTag);
      
      if (index === -1) {
        return null;
      }

      return index;
    }
  });


  orderTypeOptions = computed(() => {
    return this.dimensionConfigResource.value()
      ?.find(item => item.keyName === 'c_ord_type')
      ?.values
      ?.map(toFilterItem) || [];
  })

  firlevChannelOptions = computed(() => {
    return this.dimensionConfigResource.value()
      ?.find(item => item.keyName === 'c_firlev_channel')
      ?.values
      ?.map(toFilterItem) || [];
  })
  
  seclevChannelOptions = computed(() => {
    return this.dimensionConfigResource.value()
      ?.find(item => item.keyName === 'c_thilev_channel')
      ?.values
      ?.filter(item => {
        const firlev_channel = this.c_firlev_channel()?.value?.at(0)?.value;

        if (firlev_channel === '外输渠道') {
          return false;
        }
        return true;
      })
      ?.map(toFilterItem) || [];
  })

  passFourcaUserTypeOptions = computed(() => {
    return this.dimensionConfigResource.value()
      ?.find(item => item.keyName === 'c_pass_fourca_user_type')
      ?.values
      ?.map(toFilterItem) || [];
  })

  driverFourcaUserTypeOptions = computed(() => {
    return this.dimensionConfigResource.value()
      ?.find(item => item.keyName === 'cdriver_fourca_user_type')
      ?.values
      ?.map(toFilterItem) || [];
  })

  driverAbcTypeOptions = computed(() => {
    return this.dimensionConfigResource.value()
      ?.find(item => item.keyName === 'cdriver_abc_type')
      ?.values
      ?.map(toFilterItem) || [];
  })

  #filters = computed(() => {
    const items = [
      this.c_ord_type(),
      this.c_firlev_channel(),
      this._globalAreaFilter(),
      this.c_pass_fourca_user_type(),
      this.cdriver_fourca_user_type(),
      this.cdriver_abc_type(),
    ].filter(isNotNull);

    return { 
      items, 
      type: null
    }
  })

  _globalAreaFilter = computed(() => {
    if (this.area()) {
      const { key, value, extendName } = this.area();
      
      return {
        conditionType: 2,
        condition: "=",
        id: null,
        extendName,
        value: [{ key, value }],
        valueType: null
      };
    }
    return null;
  })

  constructor() {
    effect(() => {
      // console.log('[订单类型]', this.c_ord_type());
      // console.log('[一级渠道]', this.c_firlev_channel());
      // console.log('[二级渠道]', this.c_seclev_channel());
      // console.log('[日期]', this.date());
      // console.log('[区域]', this._globalAreaFilter());
      // console.log('[指标]', this.metric());
      // console.log('[指标列表]', this.metricsList());
      
      if (
        this.metricsDataResource.value() ||
        this.specialMetricsDataResource.value()
      ) {
        const value = {
          ...this.metricsDataResource.value() || {},
          ...this.specialMetricsDataResource.value() || {},
        };

        this.valueMap.clear();
        Object.keys(value).forEach(key => {
            if (key !== this.dateFilterRef().dateType() && key.indexOf(':') === -1) {
              const target = this.valueMap.get(key);
              this.valueMap.set(key, { ...target, value: parseFloat(value[key]) || null });
            }
            else if (key.indexOf(':') > -1) {
              const [k, v] = key.split(':');
              const target = this.valueMap.get(k);

              this.valueMap.set(k, {  ...target, [v]: parseFloat(value[key]) || null });
            }
          })

          this.metricsValueMap.set(null);
          setTimeout(() => {
            this.metricsValueMap.set(this.valueMap);
            this.cdr.markForCheck();
          }, 0)
      }
    })


    effect(() => {
      if (this.metric()) {
        const { extendName: indicator_name } = this.metric();

        trace(`埋点上报：大盘核心指标总览弹窗中的卡片点击`, {
          page_name: this.root.page_name,
          indicator_name
        });

        this.buriedPointService.addStat('dida_dpm_caerus_home_pop_indicator_click', {
          page_name: this.root.page_name,
          indicator_name
        });
      }
    })

    effect(() => {
      trace(`埋点上报：大盘核心指标总览弹窗中的筛选项点击`, {
        page_name: this.root.page_name,
        c_firlev_channel: this.c_firlev_channel()?.value?.at(0)?.value || '全部',
        c_seclev_channel: this.c_seclev_channel()?.value?.at(0)?.value || '全部',
        c_ord_type: this.c_ord_type()?.value?.at(0)?.value || '全部',
        c_pass_fourca_user_type: this.c_pass_fourca_user_type()?.value?.at(0)?.value || '全部',
        cdriver_fourca_user_type: this.cdriver_fourca_user_type()?.value?.at(0)?.value || '全部',
        cdriver_abc_type: this.cdriver_abc_type()?.value?.at(0)?.value || '全部',
        area: this.area()?.value || '全部',
      });

      this.buriedPointService.addStat('dida_dpm_caerus_home_pop_fliter_click', {
        page_name: this.root.page_name,
        c_firlev_channel: this.c_firlev_channel()?.value?.at(0)?.value || '全部',
        c_seclev_channel: this.c_seclev_channel()?.value?.at(0)?.value || '全部',
        c_ord_type: this.c_ord_type()?.value?.at(0)?.value || '全部',
        c_pass_fourca_user_type: this.c_pass_fourca_user_type()?.value?.at(0)?.value || '全部',
        cdriver_fourca_user_type: this.cdriver_fourca_user_type()?.value?.at(0)?.value || '全部',
        cdriver_abc_type: this.cdriver_abc_type()?.value?.at(0)?.value || '全部',
        area: this.area()?.value || '全部',
      });
    })
  }


  ngAfterViewInit(): void {
    console.log(this.data);
    this.visible.set(true);
    this.visibleState.set('visible');
    this._subscribeToRolesChange();
  }


  private _subscribeToRolesChange() {
    this.userService.role$.pipe(filter(isNotNull), takeUntilDestroyed(this.destroyRef)).subscribe(() => {
      const hasPermission = this.userService.hasRole('Caerus财务盯盘查看权限');

      this.hasPermission.set(hasPermission);
    });
  }


  animationDone(event: AnimationEvent): void {
    if (event.fromState === 'visible' && event.toState === 'leave') {
      this.modalRef.close();
    }
  }


  close() {
    this.visible.set(false);
    this.visibleState.set('leave');
  }

  
  sortFn = (values: any[]) => {
    if (values && values.length > 0) {
      const sorted = values.sort((a: any[][], b: any[][]) => {
        return a.at(0).at(0).tagOutOrder - b.at(0).at(0).tagOutOrder
      });
      return sorted;
    }
    return values;
  }
  
  
  scrollTo(index: number) {
    if (
      (this.cdriver_fourca_user_type() || this.cdriver_abc_type()) && index === 1 ||
      (this.c_pass_fourca_user_type()) && index === 2
    ) {
      return;
    }
    const target = this.metricsCategoriesOptions()[index];
    const metric = this.metricsConfigResource.value().find(item => {
      return _.isEqual(item, target);
    })

    this.metric.set(metric);
  }
  

  prev() {
    this.carouselContainer().nativeElement.scrollBy({ left: -256, behavior: 'smooth' });
  }


  next() {
    this.carouselContainer().nativeElement.scrollBy({ left: 256, behavior: 'smooth' });
  }

}
