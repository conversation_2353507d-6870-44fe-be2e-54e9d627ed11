
<div [@opacityAnimation]="visible()" class="modal-backdrop"></div>

<div class="modal max-2xl:p-5!">
  <div class="absolute inset-0 z-0" (click)="close()"></div>
  <div [@modalOutToInAnimation]="visibleState()" (@modalOutToInAnimation.done)="animationDone($event)" class="relative z-10 flex m-auto w-full min-h-full">
    <div cdkDragBoundary=".modal" cdkDrag class="modal-content m-auto max-w-10/12! max-2xl:max-w-full! w-full h-full max-2xl:max-h-[calc(100vh_-_40px)] max-2xl:overflow-auto!">
      <header cdkDragHandle class="modal-header">
        <h3 class="modal-title mb-0!">大盘核心指标下钻分析</h3>
        <div class="flex items-center ml-3">
          <!-- <label class="inline-flex items-center font-bold leading-0 whitespace-nowrap">时间：</label> -->
          <app-date-filter [(ngModel)]="date" [defaultMonth]="this.service.month()" />
        </div>
        <CloseIcon iconBtn class="xl ml-auto" (click)="close()" />
      </header>
      
      <div class="modal-body flex-1 min-h-0 flex flex-col gap-y-2.5">
        <div class="filter-container max-2xl:gap-1.5! max-2xl:px-3! max-2xl:py-2!">
          <div class="flex items-center">
            <label class="inline-flex items-center font-bold leading-0 whitespace-nowrap">区域：</label>
            <app-area-select [(ngModel)]="area" />
          </div>
          <app-dimension-filter label="订单类型" [(ngModel)]="c_ord_type" [options]="orderTypeOptions()" />
          <app-dimension-filter label="业务渠道" [(ngModel)]="c_firlev_channel" [options]="firlevChannelOptions()" />
          <app-dimension-filter label="产品渠道" [(ngModel)]="c_seclev_channel" [options]="seclevChannelOptions()" />
          <app-dimension-filter label="乘客四类" [(ngModel)]="c_pass_fourca_user_type" [options]="passFourcaUserTypeOptions()" />
          <app-dimension-filter label="车主四类" [(ngModel)]="cdriver_fourca_user_type" [options]="driverFourcaUserTypeOptions()" />
          <app-dimension-filter label="ABC车主" [(ngModel)]="cdriver_abc_type" [options]="driverAbcTypeOptions()" />
        </div>
        
        <div class="flex flex-col gap-y-1 px-3 py-2 max-2xl:pt-2 max-2xl:pb-0 bg-blue-50 rounded">
          <header class="flex gap-x-2">
            <label class="inline-flex items-center font-bold leading-0 whitespace-nowrap">指标类别：</label>
            <app-radio-group disabledAccessibility [(ngModel)]="metricsCategories" class="relative flex gap-1">
              <app-radio class="tag-radio" activeClass="active" [value]="0" (mouseup)="scrollTo(0)">订单与收支</app-radio>
              <app-radio class="tag-radio" activeClass="active" [value]="1" (mouseup)="scrollTo(1)" [disabled]="this.cdriver_fourca_user_type() || this.cdriver_abc_type()" >乘客规模</app-radio>
              <app-radio class="tag-radio" activeClass="active" [value]="2" (mouseup)="scrollTo(2)" [disabled]="this.c_pass_fourca_user_type()" >车主规模</app-radio>
            </app-radio-group>
          </header>

          <nz-spin [nzSpinning]="this.metricsDataResource.isLoading()">
            <main class="flex items-stretch gap-x-1 h-36 overflow-hidden">
              <button class="button-prev" (click)="prev()">
                <ChevronLeftIcon height="1.3em" />
              </button>

              <div class="flex-1 min-w-0 h-34 overflow-hidden">
                  <!-- @if (metricsDataResource.error()) {
                    <div class="flex items-center justify-center h-full">
                      <span class="text-slate-600 text-xs">{{ metricsDataResource.error() }}</span>
                    </div>
                  }
                  @else { -->
                    <app-radio-group #carouselContainer jumpToFocus class="carousel customized-scrollbar" [(ngModel)]="metric">
                      @for (items of metricsList() | sortBy: sortFn; track $index) {
                        <app-radio
                          appMetricsGroup
                          [fractionDigits]="2"
                          #metricsGroup="metricsGroup"
                          [metrics]="items"
                          [valueMap]="metricsValueMap()"
                          nz-popover
                          nzPopoverPlacement="topLeft"
                          [nzPopoverMouseEnterDelay]="0.5"
                          [nzPopoverTitle]="metricsTitleTemplate"
                          [nzPopoverContent]="contentTemplate"
                          [value]="metricsGroup.metric()"
                          [class]="`radio min-h-[126px] ${colorMap.get(metricsGroup.metric()?.classTag)}`"
                          activeClass="active"
                        >
                          <ng-template #contentTemplate>
                            <div [innerHTML]="metricsGroup.metric().bizExpression"></div>
                          </ng-template>
                          <ng-template #metricsTitleTemplate>{{metricsGroup.metric()?.showName}} 
                            <span class="text-xs opacity-30 px-1">({{metricsGroup.metric()?.aliasName}})</span>
                          </ng-template> 

                          <div class="flex flex-col gap-y-1.5 h-full justify-around">
                            <div class="truncate text-sm text-center">
                              @let keyword = '本月预测';
                              @let bgColor = 'transparent';
                              @let fontColor = 'red';
                              <span class="inline-block scale-95" [innerHTML]="metricsGroup.metric()?.showName | highlight: keyword : bgColor : fontColor"></span>
                              <app-indicator-subtitle>{{metricsGroup.metric().bizTag}}</app-indicator-subtitle>
                            </div>

                            <app-indicator-value
                              class="mx-auto font-medium"
                              fontColor="text-neutral-600"
                              fontSize="text-xl"
                              [value]="metricsGroup.value()?.value | increment | async"
                              [nullValue]="'-'"
                            />
                            
                            <app-indicator-compare-group gap="gap-1" class="scale-80">
                              @switch (this.dateFilterRef().dateType()) {
                                @case ('dt') {
                                  <app-indicator-compare label="日环比" [value]="metricsGroup.value()?.dt_DIFF_RATIO" />
                                  <app-indicator-compare label="周同比" [value]="metricsGroup.value()?.yw_DIFF_RATIO" />
                                }
                                @case ('yw') {
                                  <app-indicator-compare label="周环比" [value]="metricsGroup.value()?.yw_DIFF_RATIO" />
                                  <app-indicator-compare label="年同比" [value]="metricsGroup.value()?.year_DIFF_RATIO" />
                                }
                                @case ('ym') {
                                  <app-indicator-compare label="月环比" [value]="metricsGroup.value()?.ym_DIFF_RATIO" />
                                  <app-indicator-compare label="年同比" [value]="metricsGroup.value()?.year_DIFF_RATIO" />
                                }
                              }
                            </app-indicator-compare-group>

                            <div class="text-center">
                              @if (metricsGroup.targetLabelFormat() === '暂无目标') {
                                <span class="inline-block scale-80">{{metricsGroup.targetLabelFormat()}}</span>
                              }
                              @else {
                                @let timeSymbol = metricsGroup.timeDiffRate() === null ? '' : metricsGroup.timeDiffRate() >= 0 ? '+' : '';
                                @let timeColor = metricsGroup.timeDiffRate() === null ? '' : metricsGroup.timeDiffRate() >= 0 ? 'text-red-600' : 'text-green-500';

                                @let symbol = metricsGroup.diffValue() === null ? '' : metricsGroup.diffValue() >= 0 ? '+' : '';
                                @let color = metricsGroup.diffValue() === null ? '' : metricsGroup.diffValue() >= 0 ? 'text-red-600' : 'text-green-500';

                                @let diffLabel = (
                                  metricsGroup.metric().compareDiff === 'target' 
                                    ? '较目标<span class="'+color+'">'+symbol+metricsGroup.diffLabelFormat()+'</span>'
                                    : '较时间进度<span class="'+timeColor+'">'+timeSymbol+metricsGroup.timeDiffRate()+'pp</span>'
                                );

                                @let targetColor = (
                                  metricsGroup.percent() === null 
                                    ? '' 
                                    : metricsGroup.percent() >= 100 ? 'text-red-600' : 'text-green-500'
                                );
                                {{metricsGroup.targetMetric().showName}}：{{metricsGroup.targetLabelFormat()}}
                                (
                                  {{metricsGroup.rateMetric().showName}}：
                                  <span class="{{targetColor}}">{{metricsGroup.parentFormat()}}</span>；
                                  <span [innerHTML]="diffLabel"></span>
                                )
                              }
                            </div>

                            <SuccessFillIcon *radioChecked class="absolute top-3 right-3 text-blue-600 text-xl" />
                          </div>
                        </app-radio>
                      }
                    </app-radio-group>
                  <!-- } -->
              </div>

              <button class="button-next" (click)="next()">
                <ChevronRightIcon height="1.3em" />
              </button>
            </main>
          </nz-spin>
        </div>

        <nz-spin [nzSpinning]="this.metricsTrendResource.isLoading()">
          <div class="bg-neutral-50/0 h-100 max-2xl:h-75 rounded">
            <app-metric-trend [dtType]="this.dateFilterRef().dateType()" [value]="this.metricsTrendResource.value()" />
          </div>
        </nz-spin>
      </div>
    </div>
  </div>
</div>