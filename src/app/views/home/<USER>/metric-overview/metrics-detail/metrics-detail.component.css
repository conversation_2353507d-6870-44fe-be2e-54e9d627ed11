@reference "../../../../../../styles.css";

.button-prev,
.button-next {
  @apply
    h-34 px-1.5 rounded cursor-pointer
    transition-colors
    bg-blue-700/10
    translate-y-0.5
    hover:bg-blue-700/15
    disabled:opacity-50
    disabled:cursor-not-allowed;
}


.carousel {
  @apply 
    flex flex-nowrap gap-x-3 p-1 pb-5 
    overflow-x-auto overflow-y-hidden;
}

.radio {
  @apply
    relative block min-w-64 
    text-xs cursor-pointer rounded-lg px-3.5 py-3
    transition-all !outline-none
    bg-gradient-to-b
    shadow-md/50
    ;

  &.orange {
    @apply 
      from-orange-100/60 to-white
      shadow-orange-200;
  }
  &.blue {
    @apply 
      from-blue-100/60 to-white
      shadow-blue-200;
  }
  &.lime {
    @apply 
      from-lime-100/60 to-white
      shadow-blue-200;
  }
}

.radio.active {
  @apply
    outline-none
    !ring-2 ring-blue-600
    bg-blue-50/50;
}
