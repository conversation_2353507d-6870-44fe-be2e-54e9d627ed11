import { DecimalPipe } from '@angular/common';
import { Directive, computed, effect, inject, input, signal } from '@angular/core';
import { MetricsMenuVo } from '@api/caerus/model';
import { isEveryFinite } from '@common/function';
import { HomeService } from '@views/home';

@Directive({
  selector: '[appMetricsGroup]',
  exportAs: 'metricsGroup',
  providers: [DecimalPipe]
})
export class MetricsGroupDirective {

  homeService = inject(HomeService);
  decimalPipe = inject(DecimalPipe);
  
  metrics = input<MetricsMenuVo[][]>();
  valueMap = input<Map<string, {
    trendVo: Array<{ year: number, month: number, value: number }>;
    value: number;
    dt_DIFF: number;
    dt_DIFF_RATIO: number;
    dt_COMPARE_VALUE: number;
    yw_DIFF: number;
    yw_DIFF_RATIO: number;
    yw_COMPARE_VALUE: number;
    ym_DIFF: number;
    ym_DIFF_RATIO: number;
    ym_COMPARE_VALUE: number;
    year_DIFF: number;
    year_DIFF_RATIO: number;
    year_COMPARE_VALUE: number;
  }>>();

  constructor() {
    effect(() => {
      // this.valueMap() && console.log(this.valueMap());
    })
  }

  tabIndex = signal(0);
  hasTabs = computed(() => {
    if (this.metrics()) {
      return this.metrics().length > 1;
    }
    return false;
  })

  tabs = computed(() => {
    if (this.metrics()) {
      return this.metrics().map(item => item.at(0));
    }
    return [];
  })

  metric = computed(() => {
    if (this.metrics()) {
      return this.metrics().at(this.tabIndex()).at(0);
    }
  });

  value = computed(() => {
    if (this.metrics() && this.valueMap()) {
      const { extendName } = this.metrics().at(this.tabIndex()).find(item => item.metricsType === 'value') || {};
      return this.valueMap().get(`COMPARE_${extendName}`);
    }
  });

  valueMetric = computed(() => {
    if (this.metrics()) {
      return this.metrics().at(this.tabIndex()).find(item => item.metricsType === 'value') || {} as any;
    }
  });

  target = computed(() => {
    if (this.metrics() && this.valueMap()) {
      const { extendName } = this.metrics().at(this.tabIndex()).find(item => item.metricsType === 'target') || {};
      return this.valueMap().get(`COMPARE_${extendName}`);
    }
  });

  targetMetric = computed(() => {
    if (this.metrics()) {
      return this.metrics().at(this.tabIndex()).find(item => item.metricsType === 'target') || {} as any;
    }
  });

  rate = computed(() => {
    if (this.metrics() && this.valueMap()) {
      const { extendName } = this.metrics().at(this.tabIndex()).find(item => item.metricsType === 'rate') || {};
      return this.valueMap().get(`COMPARE_${extendName}`);
    }
  });

  rateMetric = computed(() => {
    if (this.metrics()) {
      return this.metrics().at(this.tabIndex()).find(item => item.metricsType === 'rate') || {} as any;
    }
  });

  timeDiffRate = computed<number>(() => {
    if (
      this.rate() &&
      this.homeService.timeProgressValue()
    ) {
      const rate = this.rate().value * 100;
      const diff = rate - this.homeService.timeProgressValue();

      return Number(diff.toFixed(2));
    }
  })

  fractionDigits = input(0);

  diffValue = computed(() => {
    const total = this.target()?.value;
    const value = this.value()?.value;
    if (isEveryFinite(total, value)) {
      return value - total;
    }
    return null
  });

  diffLabelFormat = computed(() => {
    if (this.diffValue()) {
      return this.decimalPipe.transform(this.diffValue(), '1.0-0');
    }

    return '--';
  })

  targetLabelFormat = computed(() => {
    const total = this.target()?.value ?? '暂无目标';

    if (total) {
      const totalStr = typeof total === 'number' ? this.decimalPipe.transform(total) : total;
      return `${totalStr}`;
    }
    return '';
  })

  percent = computed(() => {
    let _val = 0;
    const total = this.target()?.value;
    const value = this.value()?.value;

    if (value === null || total === null || total === 0) {
      return 0;
    }

    if (this.fractionDigits() === 0) {
      _val = Math.ceil((value / total) * 100) || 0;
    } else {
      _val = parseFloat(((value / total) * 100).toFixed(this.fractionDigits())) || 0;
    }

    return Number.isFinite(_val) ? _val : null;
  });
  
  parentFormat = computed(() => {
    if (!isEveryFinite(this.value()?.value, this.target()?.value)) {
      return '--';
    }
    return `${this.percent()}%`;
  })

  

}
