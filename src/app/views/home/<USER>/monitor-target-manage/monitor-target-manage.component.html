<div class="w-full px-6">
  <div class="flex justify-center">
    <app-radio-group [(ngModel)]="currentType" (ngModelChange)="changeType()" class="flex items-start px-3">
      <app-radio class="line-radio text-xs whitespace-nowrap" activeClass="active" value="detail">
        每日目标详情
      </app-radio>
      <app-radio class="line-radio text-xs whitespace-nowrap" activeClass="active" value="upload">
        每日目标上传
      </app-radio>
      <app-radio class="line-radio text-xs whitespace-nowrap" activeClass="active" value="budget">
        月度目标管理
      </app-radio>
    </app-radio-group>
  </div>
  @if (currentType() === 'detail') {
    <app-monitor-detail />
  } @else if (currentType() === 'upload') {
    <div class="flex flex-col gap-3">
      <div class="flex gap-3 justify-between">
        <div class="flex items-center gap-3">
          <button nz-button nzType="primary" (click)="downloadTemplate('1')" nzSize="small">业务目标模板下载</button>
          <button nz-button nzType="primary" (click)="downloadTemplate('2')" nzSize="small">财务目标模板下载</button>
        </div>

        @if (canUpload()) {
          <button nz-button nz-dropdown [nzDropdownMenu]="menu" nzPlacement="bottomCenter">目标上传</button>
          <nz-dropdown-menu #menu="nzDropdownMenu">
            <div class="flex flex-col bg-white">
              <label
                class="cursor-pointer h-[38px] flex items-center justify-center px-[15px] text-center"
                style="border: solid 1px #d9d9d9"
              >
                <input
                  #fileRef
                  type="file"
                  class="sr-only"
                  (change)="upload($event, '1')"
                  accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel"
                />
                业务目标上传
              </label>
              <label
                class="cursor-pointer h-[38px] flex items-center justify-center px-[15px] text-center"
                style="
                  border-bottom: solid 1px #d9d9d9;
                  border-left: solid 1px #d9d9d9;
                  border-right: solid 1px #d9d9d9;
                "
              >
                <input
                  #fileRef
                  type="file"
                  class="sr-only"
                  (change)="upload($event, '2')"
                  accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel"
                />
                财务目标上传
              </label>
            </div>
          </nz-dropdown-menu>
        }
      </div>
      <nz-table
        #basicTable
        [nzData]="targetData()"
        [nzFrontPagination]="false"
        [nzScroll]="{ y: '400px' }"
        [nzLoading]="historyLoading()"
      >
        <thead>
          <tr>
            <th>月份</th>
            <th>操作时间</th>
            <th>操作人</th>
            <th>操作类型</th>
            <th>文件类型</th>
            <th>详情</th>
          </tr>
        </thead>
        <tbody>
          @for (data of basicTable.data; track data) {
            <tr>
              <td>{{ data.ym }}</td>
              <td>{{ data.operateTime | date: 'yyyy-MM-dd HH:mm:ss' }}</td>
              <td>{{ data.operator }}</td>
              <td>目标上传</td>
              <td>{{ data.fileType === '1' ? '业务目标模板' : '财务目标模板' }}</td>
              <td>
                <a (click)="downloadHistoryFile(data.id, data.fileName)">{{ data.fileName }}</a>
              </td>
            </tr>
          }
        </tbody>
      </nz-table>
      <div class="flex justify-end">
        <nz-pagination
          [nzPageIndex]="current()"
          [nzTotal]="total()"
          (nzPageIndexChange)="pageChange($event)"
        ></nz-pagination>
      </div>
    </div>
  } @else {
    <app-budge-target-manage />
  }
</div>
