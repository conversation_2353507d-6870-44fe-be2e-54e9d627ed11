import { ChangeDetectionStrategy, Component, signal, inject, ElementRef, viewChild, computed } from '@angular/core'
import { FormsModule } from '@angular/forms'
import { NzRadioModule } from 'ng-zorro-antd/radio'
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker'
import { NzTableModule } from 'ng-zorro-antd/table'
import { NzButtonModule } from 'ng-zorro-antd/button'
import { CaerusApiService } from '@api/caerus'
import { NzUploadModule } from 'ng-zorro-antd/upload'
import { DatePipe } from '@angular/common'
import { finalize } from 'rxjs'
import { NzMessageService } from 'ng-zorro-antd/message'
import { NzPaginationModule } from 'ng-zorro-antd/pagination'
import { UserInfoService } from '@api/user-info.service'
import { isDev } from '@common/const'
import { find } from 'lodash'
import { NzModalModule } from 'ng-zorro-antd/modal'
import { FormBuilder, ReactiveFormsModule } from '@angular/forms'
import { NzFormModule } from 'ng-zorro-antd/form'
import { NzInputNumberModule } from 'ng-zorro-antd/input-number'
import { RadioModule } from '@shared/modules/headless'
import { MonitorDetailComponent } from './monitor-detail/monitor-detail.component'
import { BudgeTargetManageComponent } from './budge-target-manage/budge-target-manage.component'
import { NzDropDownModule } from 'ng-zorro-antd/dropdown'

@Component({
  selector: 'app-monitor-target-manage',
  imports: [
    NzRadioModule,
    FormsModule,
    NzDatePickerModule,
    NzTableModule,
    NzButtonModule,
    NzUploadModule,
    DatePipe,
    NzPaginationModule,
    NzModalModule,
    ReactiveFormsModule,
    NzFormModule,
    NzInputNumberModule,
    RadioModule,
    MonitorDetailComponent,
    BudgeTargetManageComponent,
    NzDropDownModule,
  ],
  templateUrl: './monitor-target-manage.component.html',
  styles: ``,
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [DatePipe],
})
export class MonitorTargetManageComponent {
  constructor(
    private message: NzMessageService,
    private fb: FormBuilder
  ) {}
  readonly apiService = inject(CaerusApiService)
  readonly userInfo = inject(UserInfoService)
  readonly datePipe = inject(DatePipe)

  fileRef = viewChild<ElementRef<HTMLInputElement>>('fileRef')
  currentType = signal('detail')
  date = signal(new Date().setDate(new Date().getDate() - 1))
  targetData = signal([])
  detailData = signal<any>([])
  loading = signal(false)
  current = signal(1)
  total = signal(0)
  historyLoading = signal(false)
  budgetData = signal([])
  budgetLoading = signal(false)
  isVisible = signal(false)
  saveLoading = signal(false)
  budgetType = signal(null)
  currentbudgetData = signal<any>(null)

  canUpload = computed(() => {
    if (isDev()) {
      return true
    }
    const menu = find(this.userInfo.config(), ['url', '/monitor/business'])
    console.log('menu', menu)
    if (menu) {
      const button = find(menu.button, ['url', '/targetupload'])
      if (button) {
        return true
      }
      return false
    }
    return false
  })

  getMonitorUploadHistory() {
    this.historyLoading.set(true)
    this.apiService
      .getMonitorUploadHistory(this.current(), 10)
      .pipe(finalize(() => this.historyLoading.set(false)))
      .subscribe((res: any) => {
        console.log('getMonitorUploadHistory', res.data)
        if (res.data && res.data.records) {
          this.targetData.set(res.data.records)
          this.total.set(res.data.total)
        } else {
          this.targetData.set([])
        }
      })
  }

  changeType() {
    if (this.currentType() === 'upload') {
      this.getMonitorUploadHistory()
    }
  }

  downloadTemplate(type) {
    const name = type === '1' ? '业务目标上传模板' : '财务目标上传模板'
    this.apiService.getTemplateDownload(name, type).subscribe()
  }

  downloadHistoryFile(_id, name) {
    this.apiService.getHistoryFileDownload(_id, name.split('.')[0]).subscribe()
  }

  upload(event, type) {
    const file = (event.target as HTMLInputElement).files[0]
    const formData = new FormData()
    formData.append('file', file)
    // formData.append('fileType', type)
    const id = this.message.loading('上传中', {
      nzDuration: 0,
    }).messageId

    if (type === '1') {
      this.apiService.uploadTargetFileType1(formData).subscribe(res => {
        console.log('upload', res)
        this.message.remove(id)
        if (res.body.status === '00000') {
          this.message.success('上传成功')
          this.getMonitorUploadHistory()
        } else {
          this.message.error(`上传失败, ${res.body.message}`)
        }
        this.fileRef().nativeElement.value = ''
      })
    } else {
      this.apiService.uploadTargetFileType2(formData).subscribe(res => {
        console.log('upload', res)
        this.message.remove(id)
        if (res.body.status === '00000') {
          this.message.success('上传成功')
          this.getMonitorUploadHistory()
        } else {
          this.message.error(`上传失败, ${res.body.message}`)
        }
        this.fileRef().nativeElement.value = ''
      })
    }
  }

  pageChange(page) {
    this.current.set(page)
    this.getMonitorUploadHistory()
  }
}
