<div class="flex flex-col gap-3">
  <div class="flex items-center justify-between">
    <div class="flex items-center">
      <label class="inline-flex items-center text-xs font-bold leading-5 whitespace-nowrap">目标类型：</label>
      <app-radio-group class="relative flex gap-1" [(ngModel)]="type" (ngModelChange)="changeType()">
        <app-radio class="tag-radio-new" activeClass="active" value="cost">财务预算</app-radio>
        <app-radio class="tag-radio-new" activeClass="active" value="business">业务目标</app-radio>
        <app-radio-thumb class="rounded-xs bg-primary" />
      </app-radio-group>
    </div>
    @if (canUpload()) {
      <div>
        @if (type() === 'cost') {
          <button nz-button nzType="primary" nzSize="small" (click)="showModal()">预算填报</button>
        } @else {
          <button nz-button nzType="primary" nzSize="small" (click)="showBusinessModal()">目标设置</button>
        }
      </div>
    }
  </div>

  @if (type() === 'cost') {
    <nz-table
      #basicTable
      [nzData]="budgetData()"
      [nzFrontPagination]="false"
      [nzScroll]="{ y: '400px' }"
      [nzLoading]="budgetLoading()"
    >
      <thead>
        <tr>
          <th>月份</th>
          <th>端内预算</th>
          <th>端内乘客预算</th>
          <th>端内车主预算</th>
          <th>端外预算</th>
          <th>端外乘客预算</th>
          <th>端外车主预算</th>
          @if (canUpload()) {
            <th>操作</th>
          }
        </tr>
      </thead>
      <tbody>
        @for (data of basicTable.data; track data) {
          <tr>
            <td>{{ data.ym }}</td>
            <td class="font-bold bg-yellow-100">
              {{ data.ctmOnpltTargetCaverFee }}
            </td>
            <td>{{ data.ctmOnpltPassTargetCaverFee }}</td>
            <td>{{ data.ctmOnpltDriverTargetCaverFee }}</td>
            <td class="font-bold bg-yellow-100">
              {{ data.ctmOffpltTargetPromtFee }}
            </td>
            <td>{{ data.ctmOffpltPassTargetPromtFee }}</td>
            <td>{{ data.ctmOffpltDriverTargetPromtFee }}</td>
            @if (canUpload()) {
              <td>
                <a (click)="edit(data)">编辑</a>
              </td>
            }
          </tr>
        }
      </tbody>
    </nz-table>
  } @else {
    <nz-table
      #basicTable
      [nzData]="budgetData()"
      [nzFrontPagination]="false"
      [nzScroll]="{ y: '400px', x: '1800px' }"
      [nzLoading]="budgetLoading()"
    >
      <thead>
        <tr>
          <th>月份</th>
          <th>乘客MAU</th>
          <th>新_完单乘客数</th>
          <th>上月完单本月留存_完单乘客数</th>
          <th>流失召回_完单乘客数</th>
          <th>其他_完单乘客数</th>
          <th>乘客累计人均完单频次</th>
          <th>乘客上月完单留存率</th>
          <th>车主MAU</th>
          <th>新_完单车主数</th>
          <th>上月完单本月留存_完单车主数</th>
          <th>流失召回_完单车主数</th>
          <th>其他_完单车主数</th>
          <th>车主累计人均完单频次</th>
          <th>车主上月完单留存率</th>
          @if (canUpload()) {
            <th>操作</th>
          }
        </tr>
      </thead>
      <tbody>
        @for (data of basicTable.data; track data) {
          <tr>
            <td>{{ data.ym }}</td>
            <td>{{ data.cpassMTargetActiveUv }}</td>
            <td>{{ data.ctmTargetOwnNewaddFinishPassUnt }}</td>
            <td>{{ data.ctmTargetOwnLtmFinishPassUnt }}</td>
            <td>{{ data.ctmTargetOwnRecallFinishPassUnt }}</td>
            <td>{{ data.ctmTargetOwnOtherFinishPassUnt }}</td>
            <td>{{ data.ctargetAmPassPavgFinishOrdCnt }}</td>
            <td>{{ data.cownLtmTargetFretenPassRate }}</td>
            <td>{{ data.cdriverMTargetActiveUv }}</td>
            <td>{{ data.ctmTargetFordDriverUv }}</td>
            <td>{{ data.ctmTargetLtmFretenDriverUnt }}</td>
            <td>{{ data.ctmTargetRecallDriverUv }}</td>
            <td>{{ data.ctmTargetOtherFinishDriverUnt }}</td>
            <td>{{ data.ctargetAmDriverPavgFinishOrdCnt }}</td>
            <td>{{ data.cltmTargetFretenDriverRate }}</td>
            @if (canUpload()) {
              <td>
                <a (click)="editBusiness(data)">编辑</a>
              </td>
            }
          </tr>
        }
      </tbody>
    </nz-table>
  }

  <nz-modal
    [(nzVisible)]="isVisible"
    nzTitle="预算填报"
    (nzOnCancel)="handleCancel()"
    (nzOnOk)="handleOk()"
    [nzWidth]="800"
    [nzOkLoading]="saveLoading()"
  >
    <ng-container *nzModalContent>
      <form nz-form [formGroup]="validateForm">
        <nz-form-item>
          <nz-form-label [nzSm]="8" [nzXs]="24" nzRequired>月份</nz-form-label>
          <nz-form-control [nzSm]="16" [nzXs]="24" nzErrorTip="请选择月份!">
            <nz-date-picker [nzDisabled]="budgetType() === 'edit'" nzMode="month" formControlName="ym"></nz-date-picker>
          </nz-form-control>
        </nz-form-item>
        <nz-form-item>
          <nz-form-label nzRequired [nzSm]="8" [nzXs]="24">端内预算</nz-form-label>
          <nz-form-control nzErrorTip="请输入预算!" [nzSm]="16" [nzXs]="24">
            <nz-input-number
              nzPlaceHolder="请输入整数,单位元"
              style="width: 200px"
              nzMin="0"
              formControlName="ctmOnpltTargetCaverFee"
              [nzPrecision]="0"
              [nzFormatter]="formatterNum"
              [nzParser]="parserNum"
            />
          </nz-form-control>
        </nz-form-item>
        <nz-form-item>
          <nz-form-label nzRequired [nzSm]="8" [nzXs]="24">端内乘客预算</nz-form-label>
          <nz-form-control nzErrorTip="请输入预算!" [nzSm]="16" [nzXs]="24">
            <nz-input-number
              nzPlaceHolder="请输入整数,单位元"
              style="width: 200px"
              nzMin="0"
              formControlName="ctmOnpltPassTargetCaverFee"
              [nzPrecision]="0"
              [nzFormatter]="formatterNum"
              [nzParser]="parserNum"
            />
          </nz-form-control>
        </nz-form-item>
        <nz-form-item>
          <nz-form-label nzRequired [nzSm]="8" [nzXs]="24">端内车主预算</nz-form-label>
          <nz-form-control nzErrorTip="请输入预算!" [nzSm]="16" [nzXs]="24">
            <nz-input-number
              nzPlaceHolder="请输入整数,单位元"
              style="width: 200px"
              nzMin="0"
              formControlName="ctmOnpltDriverTargetCaverFee"
              [nzPrecision]="0"
              [nzFormatter]="formatterNum"
              [nzParser]="parserNum"
            />
          </nz-form-control>
        </nz-form-item>

        <nz-form-item>
          <nz-form-label nzRequired [nzSm]="8" [nzXs]="24">端外预算</nz-form-label>
          <nz-form-control nzErrorTip="请输入预算!" [nzSm]="16" [nzXs]="24">
            <nz-input-number
              nzPlaceHolder="请输入整数,单位元"
              style="width: 200px"
              nzMin="0"
              formControlName="ctmOffpltTargetPromtFee"
              [nzPrecision]="0"
              [nzFormatter]="formatterNum"
              [nzParser]="parserNum"
            />
          </nz-form-control>
        </nz-form-item>
        <nz-form-item>
          <nz-form-label nzRequired [nzSm]="8" [nzXs]="24">端外乘客预算</nz-form-label>
          <nz-form-control nzErrorTip="请输入预算!" [nzSm]="16" [nzXs]="24">
            <nz-input-number
              nzPlaceHolder="请输入整数,单位元"
              style="width: 200px"
              nzMin="0"
              formControlName="ctmOffpltPassTargetPromtFee"
              [nzPrecision]="0"
              [nzFormatter]="formatterNum"
              [nzParser]="parserNum"
            />
          </nz-form-control>
        </nz-form-item>
        <nz-form-item>
          <nz-form-label nzRequired [nzSm]="8" [nzXs]="24">端外车主预算</nz-form-label>
          <nz-form-control nzErrorTip="请输入预算!" [nzSm]="16" [nzXs]="24">
            <nz-input-number
              nzPlaceHolder="请输入整数,单位元"
              style="width: 200px"
              nzMin="0"
              formControlName="ctmOffpltDriverTargetPromtFee"
              [nzPrecision]="0"
              [nzFormatter]="formatterNum"
              [nzParser]="parserNum"
            />
          </nz-form-control>
        </nz-form-item>
      </form>
    </ng-container>
  </nz-modal>

  <nz-modal
    [(nzVisible)]="businessVisible"
    nzTitle="目标设置"
    (nzOnCancel)="businessCancel()"
    (nzOnOk)="businessOk()"
    [nzWidth]="800"
    [nzOkLoading]="saveLoading()"
  >
    <ng-container *nzModalContent>
      <form nz-form [formGroup]="validateBusinessForm">
        <nz-form-item>
          <nz-form-label [nzSm]="8" [nzXs]="24" nzRequired>月份</nz-form-label>
          <nz-form-control [nzSm]="16" [nzXs]="24" nzErrorTip="请选择月份!">
            <nz-date-picker [nzDisabled]="budgetType() === 'edit'" nzMode="month" formControlName="ym"></nz-date-picker>
          </nz-form-control>
        </nz-form-item>

        <!-- 乘客 -->

        <nz-form-item>
          <nz-form-label nzRequired [nzSm]="8" [nzXs]="24">乘客MAU</nz-form-label>
          <nz-form-control nzErrorTip="请输入乘客MAU!" [nzSm]="16" [nzXs]="24">
            <nz-input-number
              nzPlaceHolder="请输入整数"
              style="width: 200px"
              nzMin="0"
              formControlName="cpassMTargetActiveUv"
              [nzPrecision]="0"
            />
          </nz-form-control>
        </nz-form-item>
        <nz-form-item>
          <nz-form-label nzRequired [nzSm]="8" [nzXs]="24">新_完单乘客数</nz-form-label>
          <nz-form-control nzErrorTip="请输入新_完单乘客数!" [nzSm]="16" [nzXs]="24">
            <nz-input-number
              nzPlaceHolder="请输入整数"
              style="width: 200px"
              nzMin="0"
              formControlName="ctmTargetOwnNewaddFinishPassUnt"
              [nzPrecision]="0"
            />
          </nz-form-control>
        </nz-form-item>
        <nz-form-item>
          <nz-form-label nzRequired [nzSm]="8" [nzXs]="24">上月完单本月留存_完单乘客数</nz-form-label>
          <nz-form-control nzErrorTip="请输入上月完单本月留存_完单乘客数!" [nzSm]="16" [nzXs]="24">
            <nz-input-number
              nzPlaceHolder="请输入整数"
              style="width: 200px"
              nzMin="0"
              formControlName="ctmTargetOwnLtmFinishPassUnt"
              [nzPrecision]="0"
            />
          </nz-form-control>
        </nz-form-item>

        <nz-form-item>
          <nz-form-label nzRequired [nzSm]="8" [nzXs]="24">流失召回_完单乘客数</nz-form-label>
          <nz-form-control nzErrorTip="请输入流失召回_完单乘客数!" [nzSm]="16" [nzXs]="24">
            <nz-input-number
              nzPlaceHolder="请输入整数"
              style="width: 200px"
              nzMin="0"
              formControlName="ctmTargetOwnRecallFinishPassUnt"
              [nzPrecision]="0"
            />
          </nz-form-control>
        </nz-form-item>

        <nz-form-item>
          <nz-form-label nzRequired [nzSm]="8" [nzXs]="24">其他_完单乘客数</nz-form-label>
          <nz-form-control nzErrorTip="请输入其他_完单乘客数!" [nzSm]="16" [nzXs]="24">
            <nz-input-number
              nzPlaceHolder="请输入整数"
              style="width: 200px"
              nzMin="0"
              formControlName="ctmTargetOwnOtherFinishPassUnt"
              [nzPrecision]="0"
            />
          </nz-form-control>
        </nz-form-item>

        <nz-form-item>
          <nz-form-label nzRequired [nzSm]="8" [nzXs]="24">乘客累计人均完单频次</nz-form-label>
          <nz-form-control nzErrorTip="请输入乘客累计人均完单频次!" [nzSm]="16" [nzXs]="24">
            <nz-input-number
              nzPlaceHolder="请输入数字，两位小数"
              style="width: 200px"
              nzMin="0"
              formControlName="ctargetAmPassPavgFinishOrdCnt"
              [nzPrecision]="2"
            />
          </nz-form-control>
        </nz-form-item>

        <nz-form-item>
          <nz-form-label nzRequired [nzSm]="8" [nzXs]="24">乘客上月完单留存率</nz-form-label>
          <nz-form-control nzErrorTip="请输入乘客上月完单留存率!" [nzSm]="16" [nzXs]="24">
            <nz-input-number
              nzPlaceHolder="请输入数字，四位小数"
              style="width: 200px"
              nzMin="0"
              formControlName="cownLtmTargetFretenPassRate"
              [nzPrecision]="4"
            />
          </nz-form-control>
        </nz-form-item>

        <!-- 车主 -->
        <nz-form-item>
          <nz-form-label nzRequired [nzSm]="8" [nzXs]="24">车主MAU</nz-form-label>
          <nz-form-control nzErrorTip="请输入车主MAU!" [nzSm]="16" [nzXs]="24">
            <nz-input-number
              nzPlaceHolder="请输入整数"
              style="width: 200px"
              nzMin="0"
              formControlName="cdriverMTargetActiveUv"
              [nzPrecision]="0"
            />
          </nz-form-control>
        </nz-form-item>
        <nz-form-item>
          <nz-form-label nzRequired [nzSm]="8" [nzXs]="24">新_完单车主数</nz-form-label>
          <nz-form-control nzErrorTip="请输入新_完单车主数!" [nzSm]="16" [nzXs]="24">
            <nz-input-number
              nzPlaceHolder="请输入整数"
              style="width: 200px"
              nzMin="0"
              formControlName="ctmTargetFordDriverUv"
              [nzPrecision]="0"
            />
          </nz-form-control>
        </nz-form-item>
        <nz-form-item>
          <nz-form-label nzRequired [nzSm]="8" [nzXs]="24">上月完单本月留存_完单车主数</nz-form-label>
          <nz-form-control nzErrorTip="请输入上月完单本月留存_完单车主数!" [nzSm]="16" [nzXs]="24">
            <nz-input-number
              nzPlaceHolder="请输入整数"
              style="width: 200px"
              nzMin="0"
              formControlName="ctmTargetLtmFretenDriverUnt"
              [nzPrecision]="0"
            />
          </nz-form-control>
        </nz-form-item>

        <nz-form-item>
          <nz-form-label nzRequired [nzSm]="8" [nzXs]="24">流失召回_完单车主数</nz-form-label>
          <nz-form-control nzErrorTip="请输入流失召回_完单车主数!" [nzSm]="16" [nzXs]="24">
            <nz-input-number
              nzPlaceHolder="请输入整数"
              style="width: 200px"
              nzMin="0"
              formControlName="ctmTargetRecallDriverUv"
              [nzPrecision]="0"
            />
          </nz-form-control>
        </nz-form-item>

        <nz-form-item>
          <nz-form-label nzRequired [nzSm]="8" [nzXs]="24">其他_完单车主数</nz-form-label>
          <nz-form-control nzErrorTip="请输入其他_完单车主数!" [nzSm]="16" [nzXs]="24">
            <nz-input-number
              nzPlaceHolder="请输入整数"
              style="width: 200px"
              nzMin="0"
              formControlName="ctmTargetOtherFinishDriverUnt"
              [nzPrecision]="0"
            />
          </nz-form-control>
        </nz-form-item>

        <nz-form-item>
          <nz-form-label nzRequired [nzSm]="8" [nzXs]="24">车主累计人均完单频次</nz-form-label>
          <nz-form-control nzErrorTip="请输入车主累计人均完单频次!" [nzSm]="16" [nzXs]="24">
            <nz-input-number
              nzPlaceHolder="请输入数字，两位小数"
              style="width: 200px"
              nzMin="0"
              formControlName="ctargetAmDriverPavgFinishOrdCnt"
              [nzPrecision]="2"
            />
          </nz-form-control>
        </nz-form-item>

        <nz-form-item>
          <nz-form-label nzRequired [nzSm]="8" [nzXs]="24">车主上月完单留存率</nz-form-label>
          <nz-form-control nzErrorTip="请输入车主上月完单留存率!" [nzSm]="16" [nzXs]="24">
            <nz-input-number
              nzPlaceHolder="请输入数字，四位小数"
              style="width: 200px"
              nzMin="0"
              formControlName="cltmTargetFretenDriverRate"
              [nzPrecision]="4"
            />
          </nz-form-control>
        </nz-form-item>
      </form>
    </ng-container>
  </nz-modal>
</div>
