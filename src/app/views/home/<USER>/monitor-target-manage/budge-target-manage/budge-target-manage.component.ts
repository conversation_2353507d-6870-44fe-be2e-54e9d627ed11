import { ChangeDetectionStrategy, Component, signal, inject, computed, AfterViewInit } from '@angular/core'
import { FormsModule } from '@angular/forms'
import { NzRadioModule } from 'ng-zorro-antd/radio'
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker'
import { NzTableModule } from 'ng-zorro-antd/table'
import { NzButtonModule } from 'ng-zorro-antd/button'
import { CaerusApiService } from '@api/caerus'
import { NzUploadModule } from 'ng-zorro-antd/upload'
import { DatePipe } from '@angular/common'
import { finalize } from 'rxjs'
import { NzMessageService } from 'ng-zorro-antd/message'
import { NzPaginationModule } from 'ng-zorro-antd/pagination'
import { UserInfoService } from '@api/user-info.service'
import { isDev } from '@common/const'
import { find } from 'lodash'
import { NzModalModule } from 'ng-zorro-antd/modal'
import { Subject } from 'rxjs'
import { FormBuilder, ReactiveFormsModule, Validators } from '@angular/forms'
import { NzFormModule } from 'ng-zorro-antd/form'
import { NzInputNumberModule } from 'ng-zorro-antd/input-number'
import { RadioModule } from '@shared/modules/headless'

@Component({
  selector: 'app-budge-target-manage',
  imports: [
    NzRadioModule,
    FormsModule,
    NzDatePickerModule,
    NzTableModule,
    NzButtonModule,
    NzUploadModule,
    NzPaginationModule,
    NzModalModule,
    ReactiveFormsModule,
    NzFormModule,
    NzInputNumberModule,
    RadioModule,
  ],
  templateUrl: './budge-target-manage.component.html',
  styles: ``,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class BudgeTargetManageComponent implements AfterViewInit {
  constructor(
    private message: NzMessageService,
    private fb: FormBuilder
  ) {}
  readonly apiService = inject(CaerusApiService)
  readonly userInfo = inject(UserInfoService)
  readonly datePipe = inject(DatePipe)
  private destroy$ = new Subject<void>()

  budgetData = signal([])
  budgetLoading = signal(false)
  isVisible = signal(false)
  businessVisible = signal(false)
  saveLoading = signal(false)
  budgetType = signal(null)
  currentbudgetData = signal<any>(null)
  type = signal('cost')

  canUpload = computed(() => {
    if (isDev()) {
      return true
    }
    const menu = find(this.userInfo.config(), ['url', '/monitor/business'])
    console.log('menu', menu)
    if (menu) {
      const button = find(menu.button, ['url', '/targetupload'])
      if (button) {
        return true
      }
      return false
    }
    return false
  })

  ngAfterViewInit(): void {
    this.getBudgetData()
  }

  getBudgetData() {
    this.budgetLoading.set(true)
    this.apiService
      .getBudgetList()
      .pipe(finalize(() => this.budgetLoading.set(false)))
      .subscribe(res => {
        console.log('budget', res)
        if (res.data) {
          this.budgetData.set(res.data)
        } else {
          this.budgetData.set([])
        }
      })
  }

  getBusinessData() {
    this.budgetLoading.set(true)
    this.apiService
      .getBusinessBudgetList()
      .pipe(finalize(() => this.budgetLoading.set(false)))
      .subscribe(res => {
        console.log('getBusinessBudgetList', res)
        if (res.data) {
          this.budgetData.set(res.data)
        } else {
          this.budgetData.set([])
        }
      })
  }

  changeType() {
    if (this.type() === 'cost') {
      this.getBudgetData()
    } else {
      this.getBusinessData()
    }
  }

  showModal() {
    this.budgetType.set('add')
    this.isVisible.set(true)
  }

  showBusinessModal() {
    this.budgetType.set('add')
    this.businessVisible.set(true)
  }

  handleOk() {
    if (this.validateForm.valid) {
      this.saveLoading.set(true)
      const data = {
        ...this.validateForm.value,
        ym: this.datePipe.transform(this.validateForm.value.ym, 'yyyy-MM'),
      }
      if (this.budgetType() === 'add') {
        this.apiService
          .addBudget(data)
          .pipe(finalize(() => this.saveLoading.set(false)))
          .subscribe(res => {
            console.log('add', res)
            if (res.status !== '00000') {
              this.message.error(res.message)
              return
            }
            this.message.success('添加成功')
            this.handleCancel()
            this.getBudgetData()
          })
      } else {
        this.apiService
          .updateBudget({
            ...data,
            id: this.currentbudgetData().id,
          })
          .pipe(finalize(() => this.saveLoading.set(false)))
          .subscribe(res => {
            console.log('add', res)
            if (res.status !== '00000') {
              this.message.error(res.message)
              return
            }
            this.message.success('编辑成功')
            this.handleCancel()
            this.getBudgetData()
          })
      }
    } else {
      Object.values(this.validateForm.controls).forEach(control => {
        if (control.invalid) {
          control.markAsDirty()
          control.updateValueAndValidity({ onlySelf: true })
        }
      })
    }
  }

  businessOk() {
    if (this.validateBusinessForm.valid) {
      this.saveLoading.set(true)
      const data = {
        ...this.validateBusinessForm.value,
        ym: this.datePipe.transform(this.validateBusinessForm.value.ym, 'yyyy-MM'),
      }
      if (this.budgetType() === 'add') {
        this.apiService
          .addBusinessBudget(data)
          .pipe(finalize(() => this.saveLoading.set(false)))
          .subscribe(res => {
            console.log('add', res)
            if (res.status !== '00000') {
              this.message.error(res.message)
              return
            }
            this.message.success('添加成功')
            this.businessCancel()
            this.getBusinessData()
          })
      } else {
        this.apiService
          .updateBusinessBudget({
            ...data,
            id: this.currentbudgetData().id,
          })
          .pipe(finalize(() => this.saveLoading.set(false)))
          .subscribe(res => {
            console.log('update', res)
            if (res.status !== '00000') {
              this.message.error(res.message)
              return
            }
            this.message.success('编辑成功')
            this.businessCancel()
            this.getBusinessData()
          })
      }
    } else {
      Object.values(this.validateBusinessForm.controls).forEach(control => {
        if (control.invalid) {
          control.markAsDirty()
          control.updateValueAndValidity({ onlySelf: true })
        }
      })
    }
  }

  handleCancel() {
    this.isVisible.set(false)
    this.validateForm.reset()
    Object.values(this.validateForm.controls).forEach(control => {
      control.clearValidators()
    })
  }

  businessCancel() {
    this.businessVisible.set(false)
    this.validateBusinessForm.reset()
    Object.values(this.validateBusinessForm.controls).forEach(control => {
      control.clearValidators()
    })
  }

  validateForm = this.fb.group({
    ym: this.fb.control<Date | null>(null, [Validators.required]),
    ctmOnpltTargetCaverFee: this.fb.control(null, [Validators.required]),
    ctmOnpltPassTargetCaverFee: this.fb.control(null, [Validators.required]),
    ctmOnpltDriverTargetCaverFee: this.fb.control(null, [Validators.required]),
    ctmOffpltTargetPromtFee: this.fb.control(null, [Validators.required]),
    ctmOffpltPassTargetPromtFee: this.fb.control(null, [Validators.required]),
    ctmOffpltDriverTargetPromtFee: this.fb.control(null, [Validators.required]),
  })

  validateBusinessForm = this.fb.group({
    ym: this.fb.control<Date | null>(null, [Validators.required]),
    ctargetAmPassPavgFinishOrdCnt: this.fb.control(null, [Validators.required]),
    ctargetAmDriverPavgFinishOrdCnt: this.fb.control(null, [Validators.required]),
    cpassMTargetActiveUv: this.fb.control(null, [Validators.required]),
    ctmTargetOwnNewaddFinishPassUnt: this.fb.control(null, [Validators.required]),
    ctmTargetOwnLtmFinishPassUnt: this.fb.control(null, [Validators.required]),
    ctmTargetOwnOtherFinishPassUnt: this.fb.control(null, [Validators.required]),
    ctmTargetOwnRecallFinishPassUnt: this.fb.control(null, [Validators.required]),
    cownLtmTargetFretenPassRate: this.fb.control(null, [Validators.required]),
    cdriverMTargetActiveUv: this.fb.control(null, [Validators.required]),
    ctmTargetFordDriverUv: this.fb.control(null, [Validators.required]),
    ctmTargetLtmFretenDriverUnt: this.fb.control(null, [Validators.required]),
    ctmTargetOtherFinishDriverUnt: this.fb.control(null, [Validators.required]),
    ctmTargetRecallDriverUv: this.fb.control(null, [Validators.required]),
    cltmTargetFretenDriverRate: this.fb.control(null, [Validators.required]),
  })

  ngOnDestroy(): void {
    this.destroy$.next()
    this.destroy$.complete()
  }

  edit(_data) {
    this.currentbudgetData.set(_data)
    this.budgetType.set('edit')
    this.isVisible.set(true)
    this.validateForm.controls.ym.setValue(_data.ym)
    this.validateForm.controls.ctmOnpltTargetCaverFee.setValue(_data.ctmOnpltTargetCaverFee)
    this.validateForm.controls.ctmOnpltPassTargetCaverFee.setValue(_data.ctmOnpltPassTargetCaverFee)
    this.validateForm.controls.ctmOnpltDriverTargetCaverFee.setValue(_data.ctmOnpltDriverTargetCaverFee)
    this.validateForm.controls.ctmOffpltTargetPromtFee.setValue(_data.ctmOffpltTargetPromtFee)
    this.validateForm.controls.ctmOffpltPassTargetPromtFee.setValue(_data.ctmOffpltPassTargetPromtFee)
    this.validateForm.controls.ctmOffpltDriverTargetPromtFee.setValue(_data.ctmOffpltDriverTargetPromtFee)
  }

  editBusiness(_data) {
    this.currentbudgetData.set(_data)
    this.budgetType.set('edit')
    this.businessVisible.set(true)
    this.validateBusinessForm.controls.ym.setValue(_data.ym)
    this.validateBusinessForm.controls.ctargetAmDriverPavgFinishOrdCnt.setValue(_data.ctargetAmDriverPavgFinishOrdCnt)
    this.validateBusinessForm.controls.ctargetAmPassPavgFinishOrdCnt.setValue(_data.ctargetAmPassPavgFinishOrdCnt)

    this.validateBusinessForm.controls.cpassMTargetActiveUv.setValue(_data.cpassMTargetActiveUv)
    this.validateBusinessForm.controls.ctmTargetOwnNewaddFinishPassUnt.setValue(_data.ctmTargetOwnNewaddFinishPassUnt)
    this.validateBusinessForm.controls.ctmTargetOwnLtmFinishPassUnt.setValue(_data.ctmTargetOwnLtmFinishPassUnt)
    this.validateBusinessForm.controls.ctmTargetOwnOtherFinishPassUnt.setValue(_data.ctmTargetOwnOtherFinishPassUnt)
    this.validateBusinessForm.controls.ctmTargetOwnRecallFinishPassUnt.setValue(_data.ctmTargetOwnRecallFinishPassUnt)
    this.validateBusinessForm.controls.cownLtmTargetFretenPassRate.setValue(_data.cownLtmTargetFretenPassRate)
    this.validateBusinessForm.controls.cdriverMTargetActiveUv.setValue(_data.cdriverMTargetActiveUv)
    this.validateBusinessForm.controls.ctmTargetFordDriverUv.setValue(_data.ctmTargetFordDriverUv)
    this.validateBusinessForm.controls.ctmTargetLtmFretenDriverUnt.setValue(_data.ctmTargetLtmFretenDriverUnt)
    this.validateBusinessForm.controls.ctmTargetOtherFinishDriverUnt.setValue(_data.ctmTargetOtherFinishDriverUnt)
    this.validateBusinessForm.controls.ctmTargetRecallDriverUv.setValue(_data.ctmTargetRecallDriverUv)
    this.validateBusinessForm.controls.cltmTargetFretenDriverRate.setValue(_data.cltmTargetFretenDriverRate)
  }

  formatterNum(value) {
    return `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
  }

  parserNum(value) {
    const ppp = value.replace(/\$\s?|(,*)/g, '')
    return Number(ppp)
  }
}
