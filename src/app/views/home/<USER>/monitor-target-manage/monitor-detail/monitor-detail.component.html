<div class="flex flex-col gap-2">
  <div class="flex items-center gap-3">
    <div>
      <span class="pr-2 font-bold text-[14px]">时间:</span>
      <nz-date-picker
        nzMode="month"
        [(ngModel)]="date"
        [nzAllowClear]="false"
        (ngModelChange)="changeType()"
      ></nz-date-picker>
    </div>
    <div class="flex items-center">
      <label class="inline-flex items-center text-xs font-bold leading-5 whitespace-nowrap">目标类型：</label>
      <app-radio-group class="relative flex gap-1" [(ngModel)]="type" (ngModelChange)="changeType()">
        <app-radio class="tag-radio-new" activeClass="active" value="business">业务目标</app-radio>
        <app-radio class="tag-radio-new" activeClass="active" value="cost">财务目标</app-radio>
        <app-radio-thumb class="rounded-xs bg-primary" />
      </app-radio-group>
    </div>
  </div>
  @if (type() === 'business') {
    <nz-table
      #fixedTable
      [nzData]="detailData()"
      [nzFrontPagination]="false"
      [nzScroll]="{ y: '400px', x: '4600px' }"
      [nzLoading]="loading()"
    >
      <thead>
        <tr>
          <th nzWidth="130px" nzLeft>日期</th>
          <th>大盘自有下单量</th>
          <th>APP下单量</th>
          <th>微信小程序下单量</th>
          <th>支付宝小程序下单量</th>
          <th>站点拼车下单量</th>
          <th>非站点拼车下单量</th>
          <th>大盘自有接单量</th>
          <th>APP接单量</th>
          <th>微信小程序接单量</th>
          <th>支付宝小程序接单量</th>
          <th>站点拼车接单量</th>
          <th>非站点拼车接单量</th>
          <th>大盘自有完单量</th>
          <th>端内额外完单量</th>
          <th>端内乘客额外完单量</th>
          <th>端内车主额外完单量</th>
          <th>端外推广完单量</th>
          <th>端外乘客推广完单量</th>
          <th>端外车主推广完单量</th>
          <th>端外推广完单乘客数</th>
          <th>端外推广完单车主数</th>
          <th>自有-自然完单量（预估值）</th>
          <th>APP完单量</th>
          <th>微信小程序完单量</th>
          <th>支付宝小程序完单量</th>
          <th>站点拼车完单量</th>
          <th>非站点拼车完单量</th>
          <th>新注册用户-自然</th>
          <th>新注册用户-推广</th>
          <th>首单乘客数-自然</th>
          <th>首单乘客数-推广</th>
          <th>首单乘客完单量-自然</th>
          <th>首单乘客完单量-推广</th>
          <th>召回乘客数-自然</th>
          <th>召回乘客数-推广</th>
          <th>召回乘客完单量-自然</th>
          <th>召回乘客完单量-推广</th>
          <th>新认证车主数-自然</th>
          <th>新认证车主数-推广</th>
          <th>首单车主数-自然</th>
          <th>首单车主数-推广</th>
          <th>首单车主完单量-自然</th>
          <th>首单车主完单量-推广</th>
          <th>召回车主数-自然</th>
          <th>召回车主数-推广</th>
          <th>召回车主完单量-自然</th>
          <th>召回车主完单量-推广</th>
          <th>大盘外输完单量</th>
        </tr>
      </thead>
      <tbody>
        @for (data of fixedTable.data; track data) {
          @if ($index === 0) {
            <tr class="sticky top-0 z-999 bg-white">
              <td nzLeft>{{ data.dt }}</td>
              <td class="font-bold bg-yellow-100">
                {{ data.cownTargetBookOrdCnt }}
              </td>
              <td>{{ data.capTargetBookOrdCnt }}</td>
              <td>{{ data.cwxminipgTargetBookOrdCnt }}</td>
              <td>{{ data.caliminipgTargetBookOrdCnt }}</td>
              <td>{{ data.csitepkgTargetBookOrdCnt }}</td>
              <td>{{ data.cnonsitepkgTargetBookOrdCnt }}</td>
              <td class="font-bold bg-yellow-100">
                {{ data.cownTargetReplyOrdCnt }}
              </td>
              <td>{{ data.capTargetReplyOrdCnt }}</td>
              <td>{{ data.cwxminipgTargetReplyOrdCnt }}</td>
              <td>{{ data.caliminipgTargetReplyOrdCnt }}</td>
              <td>{{ data.csitepkgTargetReplyOrdCnt }}</td>
              <td>{{ data.cnonsitepkgTargetReplyOrdCnt }}</td>
              <td class="font-bold bg-yellow-100">
                {{ data.cownTargetFinishOrdCnt }}
              </td>
              <td>{{ data.conpltExtraTargetFinishOrdCnt }}</td>
              <td>{{ data.cpassOnpltExtraFinishFinishOrdCnt }}</td>
              <td>{{ data.cdriverOnpltExtraTargetFinishOrdCnt }}</td>
              <td>{{ data.coffpltPromtTargetFinishOrdCnt }}</td>
              <td>{{ data.cpassOffpltPromtTargetFinishOrdCnt }}</td>
              <td>{{ data.cdriverOffpltPromtTargetFinishOrdCnt }}</td>
              <td>{{ data.ctargetOffpltNewcFinishPassUnt }}</td>
              <td>{{ data.ctargetOffpltNewcFinishDriverUnt }}</td>
              <td>{{ data.cownNaturalTargetFinishOrdCnt }}</td>
              <td>{{ data.capTargetFinishOrdCnt }}</td>
              <td>{{ data.cwxminipgTargetFinishOrdCnt }}</td>
              <td>{{ data.caliminipgTargetFinishOrdCnt }}</td>
              <td>{{ data.csitepkgTargetFinishOrdCnt }}</td>
              <td>{{ data.cnonsitepkgTargetFinishOrdCnt }}</td>
              <td>{{ data.ctdTargetNewRegUvOwn }}</td>
              <td>{{ data.ctdTargetNewRegUvPromt }}</td>
              <td>{{ data.ctdTargetFordPassUvOwn }}</td>
              <td>{{ data.ctdTargetFordPassUvPromt }}</td>
              <td>{{ data.ctdTargetFordPassFinishOrdCntOwn }}</td>
              <td>{{ data.ctdTargetFordPassFinishOrdCntPromt }}</td>
              <td>{{ data.ctdTargetRecallPassUvOwn }}</td>
              <td>{{ data.ctdTargetRecallPassUvPromt }}</td>
              <td>{{ data.ctdTargetRecallPassFinishOrdCntOwn }}</td>
              <td>{{ data.ctdTargetRecallPassFinishOrdCntPromt }}</td>
              <td>{{ data.ctdTargetNewCeredUvOwn }}</td>
              <td>{{ data.ctdTargetNewCeredUvPromt }}</td>
              <td>{{ data.ctdTargetFordDriverUvOwn }}</td>
              <td>{{ data.ctdTargetFordDriverUvPromt }}</td>
              <td>{{ data.ctdTargetFordDriverFinishOrdCntOwn }}</td>
              <td>{{ data.ctdTargetFordDriverFinishOrdCntPromt }}</td>
              <td>{{ data.ctdTargetRecallDriverUvOwn }}</td>
              <td>{{ data.ctdTargetRecallDriverUvPromt }}</td>
              <td>{{ data.ctdTargetRecallDriverFinishOrdCntOwn }}</td>
              <td>{{ data.ctdTargetRecallDriverFinishOrdCntPromt }}</td>
              <td>{{ data.coutTargetFinishOrdCnt }}</td>
            </tr>
          } @else {
            <tr>
              <td nzLeft>{{ data.dt }}</td>
              <td class="font-bold bg-yellow-100">
                {{ data.cownTargetBookOrdCnt }}
              </td>
              <td>{{ data.capTargetBookOrdCnt }}</td>
              <td>{{ data.cwxminipgTargetBookOrdCnt }}</td>
              <td>{{ data.caliminipgTargetBookOrdCnt }}</td>
              <td>{{ data.csitepkgTargetBookOrdCnt }}</td>
              <td>{{ data.cnonsitepkgTargetBookOrdCnt }}</td>
              <td class="font-bold bg-yellow-100">
                {{ data.cownTargetReplyOrdCnt }}
              </td>
              <td>{{ data.capTargetReplyOrdCnt }}</td>
              <td>{{ data.cwxminipgTargetReplyOrdCnt }}</td>
              <td>{{ data.caliminipgTargetReplyOrdCnt }}</td>
              <td>{{ data.csitepkgTargetReplyOrdCnt }}</td>
              <td>{{ data.cnonsitepkgTargetReplyOrdCnt }}</td>
              <td class="font-bold bg-yellow-100">
                {{ data.cownTargetFinishOrdCnt }}
              </td>
              <td>{{ data.conpltExtraTargetFinishOrdCnt }}</td>
              <td>{{ data.cpassOnpltExtraFinishFinishOrdCnt }}</td>
              <td>{{ data.cdriverOnpltExtraTargetFinishOrdCnt }}</td>
              <td>{{ data.coffpltPromtTargetFinishOrdCnt }}</td>
              <td>{{ data.cpassOffpltPromtTargetFinishOrdCnt }}</td>
              <td>{{ data.cdriverOffpltPromtTargetFinishOrdCnt }}</td>
              <td>{{ data.ctargetOffpltNewcFinishPassUnt }}</td>
              <td>{{ data.ctargetOffpltNewcFinishDriverUnt }}</td>
              <td>{{ data.cownNaturalTargetFinishOrdCnt }}</td>
              <td>{{ data.capTargetFinishOrdCnt }}</td>
              <td>{{ data.cwxminipgTargetFinishOrdCnt }}</td>
              <td>{{ data.caliminipgTargetFinishOrdCnt }}</td>
              <td>{{ data.csitepkgTargetFinishOrdCnt }}</td>
              <td>{{ data.cnonsitepkgTargetFinishOrdCnt }}</td>
              <td>{{ data.ctdTargetNewRegUvOwn }}</td>
              <td>{{ data.ctdTargetNewRegUvPromt }}</td>
              <td>{{ data.ctdTargetFordPassUvOwn }}</td>
              <td>{{ data.ctdTargetFordPassUvPromt }}</td>
              <td>{{ data.ctdTargetFordPassFinishOrdCntOwn }}</td>
              <td>{{ data.ctdTargetFordPassFinishOrdCntPromt }}</td>
              <td>{{ data.ctdTargetRecallPassUvOwn }}</td>
              <td>{{ data.ctdTargetRecallPassUvPromt }}</td>
              <td>{{ data.ctdTargetRecallPassFinishOrdCntOwn }}</td>
              <td>{{ data.ctdTargetRecallPassFinishOrdCntPromt }}</td>
              <td>{{ data.ctdTargetNewCeredUvOwn }}</td>
              <td>{{ data.ctdTargetNewCeredUvPromt }}</td>
              <td>{{ data.ctdTargetFordDriverUvOwn }}</td>
              <td>{{ data.ctdTargetFordDriverUvPromt }}</td>
              <td>{{ data.ctdTargetFordDriverFinishOrdCntOwn }}</td>
              <td>{{ data.ctdTargetFordDriverFinishOrdCntPromt }}</td>
              <td>{{ data.ctdTargetRecallDriverUvOwn }}</td>
              <td>{{ data.ctdTargetRecallDriverUvPromt }}</td>
              <td>{{ data.ctdTargetRecallDriverFinishOrdCntOwn }}</td>
              <td>{{ data.ctdTargetRecallDriverFinishOrdCntPromt }}</td>
              <td>{{ data.coutTargetFinishOrdCnt }}</td>
            </tr>
          }
        }
      </tbody>
    </nz-table>
  } @else {
    <nz-table
      #fixedTable
      [nzData]="detailData()"
      [nzFrontPagination]="false"
      [nzScroll]="{ y: '400px', x: '1600px' }"
      [nzLoading]="loading()"
    >
      <thead>
        <tr>
          <th nzWidth="130px" nzLeft>日期</th>
          <th>GMV</th>
          <th>市内自有GMV</th>
          <th>市内外输GMV</th>
          <th>城际自有GMV</th>
          <th>城际外输GMV</th>
          <th>税后完单佣金</th>
          <th>市内自有税后完单佣金</th>
          <th>市内外输税后完单佣金</th>
          <th>城际外输税后完单佣金</th>
          <th>城际自有税后完单佣金</th>
          <th>市内自有完单量</th>
          <th>城际自有完单量</th>
          <th>市内外输完单量</th>
          <th>城际外输完单量</th>
        </tr>
      </thead>
      <tbody>
        @for (data of fixedTable.data; track data) {
          @if ($index === 0) {
            <tr class="sticky top-0 z-999 bg-white">
              <td nzLeft>{{ data.dt }}</td>
              <td class="font-bold bg-yellow-100">
                {{ data.ctdFinishPayTargetAmt }}
              </td>
              <td>{{ data.ctdInnerOwnFinishPayTargetAmt }}</td>
              <td>{{ data.ctdInnerOutFinishPayTargetAmt }}</td>
              <td>{{ data.ctdInterOwnFinishPayTargetAmt }}</td>
              <td>{{ data.ctdInterOutFinishPayTargetAmt }}</td>
              <td class="font-bold bg-yellow-100">
                {{ data.ctdFdayAtaxCommiFinishTargetAmt }}
              </td>
              <td>{{ data.ctdInnerOwnFdayAtaxCommiFinishTargetAmt }}</td>
              <td>{{ data.ctdInnerOutFdayAtaxCommiFinishTargetAmt }}</td>
              <td>{{ data.ctdInterOutFdayAtaxCommiFinishTargetAmt }}</td>
              <td>{{ data.ctdInterOwnFdayAtaxCommiFinishTargetAmt }}</td>
              <td>{{ data.ownInnerCAmFdayTargetFinishOrdCnt }}</td>
              <td>{{ data.ownInterCAmFdayTargetFinishOrdCnt }}</td>
              <td>{{ data.outInnerCAmFdayTargetFinishOrdCnt }}</td>
              <td>{{ data.outInterCAmFdayTargetFinishOrdCnt }}</td>
            </tr>
          } @else {
            <tr>
              <td nzLeft>{{ data.dt }}</td>
              <td class="font-bold bg-yellow-100">
                {{ data.ctdFinishPayTargetAmt }}
              </td>
              <td>{{ data.ctdInnerOwnFinishPayTargetAmt }}</td>
              <td>{{ data.ctdInnerOutFinishPayTargetAmt }}</td>
              <td>{{ data.ctdInterOwnFinishPayTargetAmt }}</td>
              <td>{{ data.ctdInterOutFinishPayTargetAmt }}</td>
              <td class="font-bold bg-yellow-100">
                {{ data.ctdFdayAtaxCommiFinishTargetAmt }}
              </td>
              <td>{{ data.ctdInnerOwnFdayAtaxCommiFinishTargetAmt }}</td>
              <td>{{ data.ctdInnerOutFdayAtaxCommiFinishTargetAmt }}</td>
              <td>{{ data.ctdInterOutFdayAtaxCommiFinishTargetAmt }}</td>
              <td>{{ data.ctdInterOwnFdayAtaxCommiFinishTargetAmt }}</td>
              <td>{{ data.ownInnerCAmFdayTargetFinishOrdCnt }}</td>
              <td>{{ data.ownInterCAmFdayTargetFinishOrdCnt }}</td>
              <td>{{ data.outInnerCAmFdayTargetFinishOrdCnt }}</td>
              <td>{{ data.outInterCAmFdayTargetFinishOrdCnt }}</td>
            </tr>
          }
        }
      </tbody>
    </nz-table>
  }
</div>
