import { DatePipe } from '@angular/common'
import { AfterViewInit, ChangeDetectionStrategy, Component, inject, signal } from '@angular/core'
import { FormsModule, ReactiveFormsModule } from '@angular/forms'
import { CaerusApiService } from '@api/caerus'
import { finalize } from 'rxjs'
import { isNumber } from '@common/function'
import { NzRadioModule } from 'ng-zorro-antd/radio'
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker'
import { NzTableModule } from 'ng-zorro-antd/table'
import { NzButtonModule } from 'ng-zorro-antd/button'
import { NzUploadModule } from 'ng-zorro-antd/upload'
import { NzPaginationModule } from 'ng-zorro-antd/pagination'
import { NzModalModule } from 'ng-zorro-antd/modal'
import { NzFormModule } from 'ng-zorro-antd/form'
import { NzInputNumberModule } from 'ng-zorro-antd/input-number'
import { RadioModule } from '@shared/modules/headless'

@Component({
  selector: 'app-monitor-detail',
  imports: [
    NzRadioModule,
    FormsModule,
    NzDatePickerModule,
    NzTableModule,
    NzButtonModule,
    NzUploadModule,
    NzPaginationModule,
    NzModalModule,
    ReactiveFormsModule,
    NzFormModule,
    NzInputNumberModule,
    RadioModule,
  ],
  templateUrl: './monitor-detail.component.html',
  styles: ``,
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [DatePipe],
})
export class MonitorDetailComponent implements AfterViewInit {
  readonly apiService = inject(CaerusApiService)
  readonly datePipe = inject(DatePipe)

  date = signal(new Date().setDate(new Date().getDate() - 1))
  detailData = signal<any>([])
  loading = signal(false)
  type = signal('business')

  reduceTotal(_data, key) {
    let sum = 0
    _data.forEach(el => {
      if (isNumber(el[key])) {
        sum += el[key]
      }
    })
    return sum
  }

  getMonitorManageDetail() {
    this.loading.set(true)
    const month = this.datePipe.transform(this.date(), 'yyyy-MM')
    this.apiService
      .getMonitorManageDetail(month)
      .pipe(finalize(() => this.loading.set(false)))
      .subscribe((res: any) => {
        console.log('getMonitorManageDetail', res)
        if (res.data) {
          let _obj = {}
          if (res.data.length !== 0) {
            const arr = Object.keys(res.data[0])
            const keys = ['dt', 'id', 'operator', 'updateTime', 'createTime']
            arr.forEach(item => {
              if (!keys.includes(item)) {
                _obj[item] = this.reduceTotal(res.data, item)
              }
            })
          }
          const obj = {
            dt: '累计',
            ..._obj,
          }

          res.data.unshift(obj)
          this.detailData.set(res.data)
          console.log('_obj', this.detailData())
        } else {
          this.detailData.set([])
        }
      })
  }

  getCostDetail() {
    this.loading.set(true)
    const month = this.datePipe.transform(this.date(), 'yyyy-MM')
    this.apiService
      .getFinanceManageList(month)
      .pipe(finalize(() => this.loading.set(false)))
      .subscribe((res: any) => {
        console.log('getCostDetail', res)
        if (res.data) {
          let _obj = {}
          if (res.data.length !== 0) {
            const arr = Object.keys(res.data[0])
            const keys = ['dt', 'id', 'operator', 'updateTime', 'createTime']
            arr.forEach(item => {
              if (!keys.includes(item)) {
                _obj[item] = this.reduceTotal(res.data, item)
              }
            })
          }
          const obj = {
            dt: '累计',
            ..._obj,
          }

          res.data.unshift(obj)
          this.detailData.set(res.data)
          console.log('_obj', this.detailData())
        } else {
          this.detailData.set([])
        }
      })
  }

  ngAfterViewInit(): void {
    this.getMonitorManageDetail()
  }

  changeType() {
    if (this.type() === 'business') {
      this.getMonitorManageDetail()
    } else {
      this.getCostDetail()
    }
  }
}
