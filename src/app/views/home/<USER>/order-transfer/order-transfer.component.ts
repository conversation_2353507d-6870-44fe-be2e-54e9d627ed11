import { DatePipe } from '@angular/common'
import { AfterViewInit, ChangeDetectionStrategy, Component, computed, DestroyRef, inject, signal } from '@angular/core'
import { FormsModule } from '@angular/forms'
import { CaerusApiService } from '@api/caerus'
import { QueryEngineApiService } from '@api/query-engine'
import { QueryOutputVo } from '@api/query-engine/model'
import { LegendControlService, LegendItemClickHandler } from '@common/service'
import { QueryEngineFormService } from '@common/service/query-engine'
import { AreaSelectComponent } from '@shared/components/area-select'
import { ChartFunnelCompareComponent, ChartFunnelComponent } from '@shared/components/chart'
import { DateCompareComponent } from '@shared/components/date-compare'
import { GraphComponent } from '@shared/components/graph'
import { HeadingComponent } from '@shared/components/heading'
import { LineSpinComponent } from '@shared/components/line-spin'
import { RadioModule } from '@shared/modules/headless'
import { IconAlbumComponent } from '@shared/modules/icons'
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox'
import { combineLatest, finalize } from 'rxjs'
import { MultipleXAxis } from '../order-tracking-overview/lib'
import { ProgressTrend } from '../conversion-analytics/lib'
import { HomeService } from '../../home.service'
import { takeUntilDestroyed } from '@angular/core/rxjs-interop'
import { SwitchMap } from '@common/decorator'
import { getMonthFirstAndNowDay } from '@common/class'
import { NzToolTipModule } from 'ng-zorro-antd/tooltip'
import { NzRadioModule } from 'ng-zorro-antd/radio'
import { CityPickerComponent } from '@shared/components/filters'

@Component({
  selector: 'app-order-transfer',
  imports: [
    NzToolTipModule,
    HeadingComponent,
    FormsModule,
    AreaSelectComponent,
    // CityPickerComponent,
    DateCompareComponent,
    RadioModule,
    NzCheckboxModule,
    NzRadioModule,
    LineSpinComponent,
    GraphComponent,
    ChartFunnelCompareComponent,
    ChartFunnelComponent,
    IconAlbumComponent,
  ],
  templateUrl: './order-transfer.component.html',
  host: {
    class: 'block px-5',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [DatePipe, LegendControlService, QueryEngineFormService],
})
export class OrderTransferComponent implements AfterViewInit {
  readonly apiService = inject(CaerusApiService)
  readonly formService = inject(QueryEngineFormService)
  readonly queryService = inject(QueryEngineApiService)
  readonly datePipe = inject(DatePipe)
  readonly legendControlService = inject(LegendControlService)
  readonly service = inject(HomeService)
  readonly destroyRef = inject(DestroyRef)

  area = signal(null)
  config = signal(null)
  top_filter = signal(null)
  checkValue = signal([])
  checkValue2 = signal(null)
  metrics = signal([])
  allMetrics = signal([])
  selectMetrics = signal([])
  funnel_loading = signal(false)
  loading = signal(false)
  errorMessage = signal(null)
  errorMessageFunnel = signal(null)
  option = signal(null)
  type = signal('c_book_firlev_channel_name')
  funnelSeries = signal([])
  hasCompareDate = signal(false)

  showCheckValue = computed(() => {
    if (this.hasCompareDate()) {
      return ['当前期', '对比期']
    }
    // if ([this.area()].length === 2) {
    //   return this.area().map(c => {
    //     if (!c) {
    //       return '全国'
    //     }
    //     return c.value
    //   })
    // }
    return this.checkValue().map(c => {
      return c ? c.showValue : '全部'
    })
  })

  showCheckMetric = computed(() => {
    return this.selectMetrics().map(c => {
      return c.showName
    })
  })

  ngAfterViewInit(): void {
    this.subscribeToMonthChange()
    this.subscribeDateChange()
    this.fetchMetrics()
    this.fetchConfig()
  }

  subscribeToMonthChange() {
    combineLatest([this.service.month$, this.service.updateTimeResource$])
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(([value, updateTime]) => {
        if (!updateTime) {
          return
        }
        const month = this.datePipe.transform(value, 'yyyy-MM-dd')
        const [startTime] = getMonthFirstAndNowDay(month)
        this.formService.dt.patchValue({
          startTime: startTime,
          endTime: updateTime,
        })
        this.getFunnlData()
      })
  }

  subscribeDateChange() {
    this.formService.form.valueChanges.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(value => {
      if (value.compareDt && value.compareDt.startTime) {
        const _check = this.checkValue()[0]
        // const _area = this.area()[0] ? this.area()[0] : null
        // this.area.set([_area])
        this.checkValue.set([_check])
        this.checkValue2.set(_check)
        this.hasCompareDate.set(true)
      } else {
        this.hasCompareDate.set(false)
      }
      this.getFunnlData()
    })
  }

  fetchMetrics() {
    this.apiService.fetchMetricsConfig('cockpit_out_co_trans_rate_metrics').subscribe(res => {
      if (res.data) {
        const subMetric = res.data['tc_c_out_user_in_out_analysis_tab1'].subMetric
        this.metrics.set(subMetric.filter(s => s.recommend === 1))
        this.allMetrics.set(subMetric)
        this.selectMetrics.set(this.allMetrics().filter((_s, i) => i < 3))
        console.log('transfer-Metrics', this.allMetrics())
        this.getFunnlData()
      } else {
        this.metrics.set([])
      }
    })
  }

  fetchConfig() {
    this.apiService.fetchDimensionConfig('cockpit_out_co_trans_rate_dim').subscribe(res => {
      if (res.data) {
        const obj = {}
        res.data.top_filter.forEach(filter => {
          obj[filter.keyName] = filter.values
        })
        this.top_filter.set(obj)
        console.log('transfer-config', obj)
        this.config.set(obj[this.type()])
        this.checkValue.set([this.config()[0], this.config()[1]])
        this.getFunnlData()
      } else {
        this.config.set(null)
      }
    })
  }

  changeType() {
    if (!this.top_filter()) {
      return
    }
    this.config.set(this.top_filter()[this.type()])
    if (this.type() === 'c_book_firlev_channel_name') {
      if (this.hasCompareDate()) {
        this.checkValue.set([this.config()[0]])
      } else {
        this.checkValue.set([this.config()[0], this.config()[1]])
      }
    } else {
      if (this.hasCompareDate()) {
        this.checkValue.set([null])
      } else {
        this.checkValue.set([null, this.config()[0]])
      }
    }
    this.getFunnlData()
  }

  // changeArea() {
  //   if (this.area().length === 2) {
  //     const _check = this.checkValue()[0]
  //     this.checkValue.set([_check])
  //     this.checkValue2.set(_check)
  //   }
  //   this.getFunnlData()
  //   this.getChartData()
  // }

  getQuery(type) {
    const body = this.formService.value()
    if (type === 'funnl') {
      body.dimensions = [{ id: null, extendName: 'dt' }]
      body.outAggDimensions = []
    } else {
      body.dimensions = [
        {
          id: null,
          extendName: 'dt',
        },
      ]
    }

    body.filter = {
      items: this.area()
        ? [
            {
              conditionType: 2,
              condition: '=',
              id: null,
              extendName: this.area().extendName,
              value: [
                {
                  key: this.area().key,
                  value: this.area().value,
                },
              ],
              valueType: null,
            },
          ]
        : [],
      type: null,
    }
    const items = this.checkValue().map(c => {
      if (!c) {
        return null
      }
      return {
        conditionType: 2,
        condition: '=',
        id: null,
        extendName: c.extendName,
        value: [
          {
            key: c.key,
            value: c.value,
          },
        ],
        valueType: null,
      }
    })

    // const area = this.area().map(a => {
    //   if (!a) {
    //     return null
    //   }
    //   return {
    //     conditionType: 2,
    //     condition: '>=',
    //     extendName: a.extendName,
    //     value: [
    //       {
    //         key: a.key,
    //         value: a.value,
    //       },
    //     ],
    //     valueType: null,
    //   }
    // })

    const temp = []
    const _metrics = type === 'funnl' ? this.metrics() : this.selectMetrics()
    if ([this.area()].length !== 2) {
      items.forEach((i, index) => {
        _metrics.forEach((m, _index) => {
          temp.push({
            extendName: m.extendName,
            type: 3,
            userDefAliasName: i ? `${m.showName}_${i.value[0].value}` : `${m.showName}_全部`,
            userDefExtendName: i ? `${m.extendName}_${i.extendName}_${index}_${_index}` : `${m.extendName}`,
            filter: {
              items: i ? [i] : [],
              type: null,
            },
          })
        })
      })
    } else {
      // area.forEach((i, index) => {
      //   _metrics.forEach((m, _index) => {
      //     temp.push({
      //       extendName: m.extendName,
      //       type: 3,
      //       userDefAliasName: i ? `${m.showName}_${i.value[0].value}` : `${m.showName}_全国`,
      //       userDefExtendName: i ? `${m.extendName}_${i.extendName}_${index}_${_index}` : `${m.extendName}`,
      //       filter: {
      //         items: i ? [i, ...items.filter(a => a)] : [...items.filter(a => a)],
      //         type: null,
      //       },
      //     })
      //   })
      // })
    }

    body.metrics = temp
    body.useLocalQuery = true

    return body
  }

  @SwitchMap()
  getFunnlData() {
    if (this.checkValue().length === 0 || this.metrics().length === 0) {
      return
    }
    if (this.formService.hasDateCompare() && !this.formService.value().compareDt.startTime) {
      return
    }
    const query = this.getQuery('funnl')
    this.getChartData()
    this.funnel_loading.set(true)
    return this.queryService
      .search(query, 'transfer-funnl')
      .pipe(finalize(() => this.funnel_loading.set(false)))
      .subscribe(res => {
        console.log('transfer-funnl', res.data)
        if (!res.data) {
          this.errorMessageFunnel.set(res.message)
          return
        }
        if (res.data && res.data.data.length !== 0) {
          const data = res.data.data[0]
          const FunnelData = []
          if ([this.area()].length !== 2) {
            this.checkValue().forEach((c, index) => {
              FunnelData.push(
                this.metrics().map((l, _index) => {
                  return {
                    name: l.showName,
                    leftLabel: l.showName,
                    rightLabel: l.tagName,
                    value: !c
                      ? Number(data[l.extendName])
                      : Number(data[`${l.extendName}_${c.extendName}_${index}_${_index}`]),
                  }
                })
              )
            })
          } else {
            // this.area().forEach((c, index) => {
            //   FunnelData.push(
            //     this.metrics().map((l, _index) => {
            //       return {
            //         name: l.showName,
            //         leftLabel: l.showName,
            //         rightLabel: l.tagName,
            //         value: !c
            //           ? Number(data[l.extendName])
            //           : Number(data[`${l.extendName}_${c.extendName}_${index}_${_index}`]),
            //       }
            //     })
            //   )
            // })
          }

          if (this.hasCompareDate()) {
            const compareData = res.data.compareData[0]
            this.checkValue().forEach((c, index) => {
              FunnelData.push(
                this.metrics().map((l, _index) => {
                  return {
                    name: l.showName,
                    leftLabel: l.showName,
                    rightLabel: l.tagName,
                    value: !c
                      ? Number(compareData[l.extendName])
                      : Number(compareData[`${l.extendName}_${c.extendName}_${index}_${_index}`]),
                  }
                })
              )
            })
          }
          console.log('arr', FunnelData)
          if (FunnelData.length === 1) {
            this.funnelSeries.set(FunnelData[0])
          } else {
            this.funnelSeries.set(FunnelData)
          }
        } else {
          this.funnelSeries.set([])
        }
      })
  }

  @SwitchMap()
  getChartData() {
    this.loading.set(true)
    const query = this.getQuery('chart')
    this.legendControlService.reset()
    return this.queryService
      .search(query, 'transfer-chart')
      .pipe(finalize(() => this.loading.set(false)))
      .subscribe(res => {
        if (res.data) {
          this._setChartData(res.data)
        } else {
          this.option.set(null)
          this.errorMessage.set(res.message)
        }
      })
  }

  private _setChartData(data: QueryOutputVo) {
    try {
      let chart
      if (this.formService.value().compareDt?.startTime) {
        chart = new MultipleXAxis(data)
      } else {
        chart = new ProgressTrend(data)
      }
      const legendItemClick = LegendItemClickHandler(this.legendControlService)
      chart.plotOptions.series.events = { legendItemClick }

      this.option.set(chart?.getOption() || null)
    } catch (e) {
      this.option.set(null)
      this.errorMessage.set(e)
      console.error(e)
    }
  }

  clickChart(params) {
    if (params.data) {
      console.log('params', params)
      if (params.dataIndex === 0) {
        this.selectMetrics.set(this.allMetrics().filter((_m, i) => i < 3))
      }
      if (params.dataIndex === 1) {
        this.selectMetrics.set(this.allMetrics().filter((_m, i) => i > 1))
      }
      if (params.dataIndex === 2) {
        this.selectMetrics.set(this.allMetrics().filter((_m, i) => i > 3))
      }
      this.getChartData()
    }
  }

  handleCheckValueChange(value: any[]) {
    // console.log('[$event]', value);
    // console.log('[checkValue]', this.checkValue());
    if (this.hasCompareDate()) {
      this.checkValue.set([value.pop()])
    } else {
      this.checkValue.set(value)
    }
  }

  handleCheckValueChange2(value: any) {
    if (this.hasCompareDate()) {
      this.checkValue.set([value])
    } else {
      this.checkValue.set([value])
    }
  }
}
