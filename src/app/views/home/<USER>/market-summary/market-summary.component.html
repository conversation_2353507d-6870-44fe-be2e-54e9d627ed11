<div class="px-5">
  <div id="anchor-resume" class="bg-slate-50 rounded-sm text-slate-500 text-xs leading-5 border border-slate-200 shadow-sm shadow-slate-200">
    <header class="flex items-center gap-x-1.5 px-5 py-3 max-2xl:px-3 max-2xl:py-1.5 border-b border-slate-200" [class.border-transparent!]="collapsed()">
      <CaliberIcon />
      <span class="text-sm font-bold whitespace-nowrap">盯盘摘要：</span>

      <div class="h-5 overflow-hidden">
        <div class="transition-all duration-300 ease-out" [style.transform]="collapsed() ? 'translateY(-50%)' : 'translateY(0)'" >
          <p class="font-normal whitespace-nowrap">
            说明：除特别标注外，摘要信息中的指标均为业务口径；摘要中，
            <span class="text-blue-500">蓝色表示指标实际数值，</span>
            <span class="text-red-500">红色表示进度健康，</span>
            <span class="text-green-500">绿色表示进度预警。</span>
          </p>
          <p class="whitespace-nowrap" [innerHTML]="headResume() | safe: 'html'"></p>
        </div>
      </div>

      <a class="inline-flex items-center gap-x-1 select-none ml-auto whitespace-nowrap" (click)="toggle()">
        <FastArrowRightIcon [style]="2" class="-rotate-90" [class.rotate-90]="collapsed()" />
        {{ collapsed() ? '展开' : '收起'}}
      </a>
    </header>

    <div class="h-74 overflow-y-auto transition-all" [class.h-0!]="collapsed()">
      <div class="px-6 py-3">
        @if (resumeResource.isLoading()) {
          <div class="flex items-center justify-center h-70">
            <app-line-spin class="scale-80" />
          </div>
        }
        @else if (resumeResource.error()) {
          <div class="flex items-center justify-center h-42 pb-5">
            <span class="text-slate-600 text-xs">{{ resumeResource.error() }}</span>
          </div>
        }
        @else {
          <table class="w-full text-sm/relaxed">
            <tbody>
              @for (item of resume(); track item.title) {
                <tr>
                  <td class="pr-3 text-right align-text-top text-slate-600 font-medium whitespace-nowrap">
                    {{item.title}}
                  </td>
                  <td class="pl-3 text-slate-500">
                    <hr class="my-3 -ml-3 border-slate-400/30" />
                    @for (child of item.data; track $index) {
                      <p [innerHTML]="child | safe: 'html'"></p>
                    }
                  </td>
                </tr>
              }
            </tbody>
          </table>
          <div class="h-10"></div>
        }
      </div>
    </div>
  </div>
</div>
