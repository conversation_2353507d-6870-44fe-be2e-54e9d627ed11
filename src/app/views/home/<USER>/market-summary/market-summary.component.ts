import { AfterViewInit, ChangeDetectionStrategy, Component, computed, DestroyRef, inject, signal } from '@angular/core';
import { rxResource, takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { filter, map, of } from 'rxjs';

import { isDev, trace } from '@common/const';
import { CaerusApiService } from '@api/caerus';
import { hasUndefined, isNotNull } from '@common/function';
import { BuriedPointService, UserService } from '@common/service';
import { IconCaliberComponent, IconFastArrowRightComponent } from '@shared/modules/icons';
import { LineSpinComponent } from '@shared/components/line-spin';
import { HomeService, HomeComponent } from '@views/home';
import { SafePipe } from '@shared/pipes/safe';


@Component({
  selector: 'app-market-summary',
  templateUrl: './market-summary.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    LineSpinComponent,
    IconCaliberComponent,
    IconFastArrowRightComponent,
    SafePipe,
  ],
})
export class MarketSummaryComponent implements AfterViewInit {

  readonly destroyRef = inject(DestroyRef);
  readonly homeService = inject(HomeService);
  readonly caerusApiService = inject(CaerusApiService);
  readonly buriedPointService = inject(BuriedPointService);
  readonly userService = inject(UserService);
  readonly root = inject(HomeComponent);
  
  auth = signal(isDev() ? true : false);
  collapsed = signal(true);
  loading = signal(false);

  resumeBody = computed(() => {
    return {
      auth: this.auth(),
      dt: this.homeService.updateTime()
    }
  })

  resumeResource = rxResource({
    request: () => this.resumeBody(),
    loader: ({ request }) => 
      hasUndefined<boolean | string>(request.auth, request.dt)
        ? of(null) 
        : this.caerusApiService.fetchBusinessMonitorResumeV2(request.auth, request.dt).pipe(
            map(res => {
              if (res.status === '00000') {
                return res.data
              }
              throw res.message
            })
          )
  })

  headResume = computed(() => {
    return this.resumeResource.value()?.filter(item => item.title === 'head')?.at(0)?.data;
  })

  resume = computed(() => {
    return this.resumeResource.value()?.filter(item => item.title !== 'head');
  })

  toggle() {
    this.collapsed.update(state => !state);
    trace(`埋点上报：盯盘摘要展开/收起按钮点击`, {
      page_name: this.root.page_name,
      option_type: Number(this.collapsed()),
    });

    this.buriedPointService.addStat('dida_dpm_caerus_home_expand_click', {
      page_name: this.root.page_name,
      option_type: Number(this.collapsed()),
    });
  }

  ngAfterViewInit(): void {
    this.userService.role$.pipe(
      filter(isNotNull),
      takeUntilDestroyed(this.destroyRef),
    ).subscribe(() => {
      const hasPermission = this.userService.hasRole('Caerus财务盯盘查看权限');
      this.auth.set(hasPermission);
    })
  }
  
}
