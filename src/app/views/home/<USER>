import { DatePipe } from '@angular/common'
import { FormsModule } from '@angular/forms'
import { AfterViewInit, ChangeDetectionStrategy, Component, DestroyRef, effect, inject, signal } from '@angular/core'
import {BreakpointObserver, Breakpoints} from '@angular/cdk/layout';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop'
import { ActivatedRoute, Router } from '@angular/router'
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker'
import { NzModalModule, NzModalService } from 'ng-zorro-antd/modal'
import { NzAnchorModule } from 'ng-zorro-antd/anchor'
import { filter } from 'rxjs'

import { isDev, trace } from '@common/const'
import { isNotNull } from '@common/function'
import { BuriedPointService, UserService } from '@common/service'
import { PAGE_NAME, PageEnterLeaveDirective } from '@common/directive'
import { SkeletonComponent } from '@shared/components/skeleton'
import { NavigationComponent } from '@shared/components/navigation'
import { BackTopComponent } from '@shared/components/back-top'
import { SelfhelpBtnComponent } from '@shared/components/selfhelp-btn'
import { ProgressLoaderComponent } from '@shared/components/progress-loader'
import { RibbonModule } from '@shared/modules/ribbon'
import { ShowFullTextDirective } from '@shared/directives/show-full-text'
import { IconOkrComponent } from '@shared/modules/icons'
import { RadioModule } from '@shared/modules/headless'

import {
  MarketSummaryComponent,
  MetricOverviewComponent,
  OrderTrackingOverviewComponent,
  OffpltPromtComponent,
  ConversionAnalyticsComponent,
  InfluenceFlowComponent,
} from './components'
import { CostOffProgressComponent } from './components/cost-off-progress/cost-off-progress.component'
import { IncomeProgressMonitoringComponent } from './components/income-progress-monitoring/income-progress-monitoring.component'
import { MonitorTargetManageComponent } from './components/monitor-target-manage/monitor-target-manage.component'
import { OrderTransferComponent } from './components/order-transfer/order-transfer.component'
import { HomeService } from './home.service'

@Component({
  selector: 'app-home',
  templateUrl: './home.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: {
    class: 'block relative',
  },
  hostDirectives: [PageEnterLeaveDirective],
  imports: [
    DatePipe,
    FormsModule,
    RadioModule,
    NzModalModule,
    NzAnchorModule,
    NzDatePickerModule,
    RibbonModule,
    NavigationComponent,
    SkeletonComponent,
    MarketSummaryComponent,
    MetricOverviewComponent,
    OrderTrackingOverviewComponent,
    OffpltPromtComponent,
    ConversionAnalyticsComponent,
    SelfhelpBtnComponent,
    OrderTransferComponent,
    InfluenceFlowComponent,
    IncomeProgressMonitoringComponent,
    CostOffProgressComponent,
    ProgressLoaderComponent,
    IconOkrComponent,
    BackTopComponent,
    ShowFullTextDirective,
  ],
  providers: [HomeService, { provide: PAGE_NAME, useValue: 'home' }],
})
export class HomeComponent implements AfterViewInit {
  readonly router = inject(Router)
  readonly route = inject(ActivatedRoute)
  readonly service = inject(HomeService)
  readonly destroyRef = inject(DestroyRef)
  readonly userService = inject(UserService)
  readonly modal = inject(NzModalService)
  readonly buriedPointService = inject(BuriedPointService);
  readonly page_name = inject(PAGE_NAME);
  readonly breakpointObserver = inject(BreakpointObserver);

  type = signal<'own' | 'offplt' | 'finance'>('own')
  typeMap = new Map([
    ['own', '自有'],
    ['offplt', '外输'],
    ['finance', '财务'],
  ]);
  hasPermission = signal(isDev() ? true : false)

  constructor() {
    this.route.queryParamMap.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(paramMap => {
      if (paramMap.get('deadline') !== null) {
        this.service.userUpdateTime.set(paramMap.get('deadline'))
      }
    })

    effect(() => {
      const tab_name = this.typeMap.get(this.type());

      trace(`埋点上报：tab导航切换`, {
        page_name: this.page_name,
        tab_name,
      });

      this.buriedPointService.addStat('dida_dpm_caerus_home_tab_click', {
        page_name: this.page_name,
        tab_name,
      });
    })
  }

  datePickerSize = signal(null);

  ngAfterViewInit(): void {
    this._subscribeToRolesChange()
    this._subscribeToBreakpointChange();
  }

  private _subscribeToBreakpointChange() {
    this.breakpointObserver.observe([Breakpoints.Medium]).pipe(
      takeUntilDestroyed(this.destroyRef)
    ).subscribe(result => {
      if (result.matches) {
        this.datePickerSize.set('small');
      } else {
        this.datePickerSize.set(null);
      }
    });
  }

  private _subscribeToRolesChange() {
    this.userService.role$.pipe(filter(isNotNull), takeUntilDestroyed(this.destroyRef)).subscribe(() => {
      const hasPermission = this.userService.hasRole('Caerus财务盯盘查看权限')

      this.hasPermission.set(hasPermission)
    })
  }

  showModal() {
    this.modal.create({
      nzTitle: '目标管理',
      nzContent: MonitorTargetManageComponent,
      nzWidth: '80%',
      nzFooter: null,
    })
  }

  hasChangeLog(version: string) {
    const changelog = window.localStorage.getItem('caerus.home.changelog')

    if (changelog && changelog.includes(version)) {
      return false
    }

    return true
  }

  markRead(version: string) {
    const changelog = window.localStorage.getItem('caerus.home.changelog')

    if (!changelog) {
      window.localStorage.setItem('caerus.home.changelog', version)
    } else {
      const arr = changelog.split(',')
      arr.push(version)
      window.localStorage.setItem('caerus.home.changelog', arr.join(','))
    }
  }

  openBackDoor() {
    this.router.navigate(['./'], { relativeTo: this.route, queryParams: { deadline: this.service.updateTime() } })
  }
}
