<app-navigation />

<app-ribbon class="block" (onClose)="markRead('1.9.2')">
  @if (hasChangeLog('1.9.2')) {
    <app-ribbon-item>
      <div showFullText style="max-width: calc(100% - 100px)">
        <span>
          说明：首页主要用于替代【
          <span class="text-blue-400">管理驾驶舱</span>
          】以及【
          <span class="text-blue-400">业务盯盘-盯盘总览</span>
          】两个页面为用户提供数据内容；为方便用户适应，新旧页面会共存一段时间，原有两个页面预计8月初下线。
        </span>
      </div>
    </app-ribbon-item>
  }
</app-ribbon>

<div class="sticky top-0 py-2 bg-white fixed-header z-999">
  <div class="flex justify-center">
    <div class="max-w-(--breakpoint-2xl) flex-1 min-w-0 flex items-center gap-x-8 text-xs px-5">
      <div class="flex items-center gap-x-2">
        <span class="inline-flex items-center font-bold whitespace-nowrap">时间：</span>

        <nz-date-picker
          nzMode="month"
          [nzFormat]="'yyyy/MM'"
          [nzDisabledDate]="service.disabledMonth"
          [(ngModel)]="service.month"
          [nzSize]="datePickerSize()"
        />

        @if (service.updateTimeResource.isLoading()) {
          <app-skeleton class="w-35 h-4" />
        } @else {
          <span class="text-xs whitespace-nowrap">
            日期截至:
            <strong class="font-normal!" (dblclick)="openBackDoor()">
              {{ service.updateTime() | date: 'yyyy-MM-dd' }}
            </strong>
          </span>
          <span class="text-xs ml-3 whitespace-nowrap">本月时间进度: {{ service.timeProgress() }}</span>
        }
      </div>
    </div>
    <div class="w-17.5"></div>
  </div>
</div>

<div class="flex justify-center">
  <div class="max-w-(--breakpoint-2xl) flex-1 min-w-0 flex flex-col gap-y-5 max-2xl:gap-y-2">
    @defer (on viewport) {
      <app-market-summary id="anchor-market-summary" />
    } @placeholder {
      <div id="anchor-market-summary" class="relative w-full h-95">
        <app-progress-loader />
      </div>
    }

    @defer (on viewport) {
      <app-metric-overview id="anchor-metric-overview" />
    } @placeholder {
      <div id="anchor-metric-overview" class="relative w-full h-99">
        <app-progress-loader />
      </div>
    }

    <header class="bg-white/70 sticky top-12 z-30 flex items-center gap-x-1 px-5 py-7 backdrop-blur-xs">
      <app-radio-group
        [(ngModel)]="type"
        class="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 w-105 flex items-start gap-x-0.5 p-0.5 bg-[#ebebec] rounded-lg"
      >
        <app-radio class="dida-radio" activeClass="dida-radio-active" value="own">自有渠道运营进度监控</app-radio>
        <app-radio class="dida-radio" activeClass="dida-radio-active" value="offplt">外输渠道合作情况</app-radio>
        @if (hasPermission()) {
          <app-radio class="dida-radio" activeClass="dida-radio-active" value="finance">财务收支进度</app-radio>
        }
      </app-radio-group>
    </header>

    @if (type() === 'own') {
      @defer (on viewport) {
        <app-order-tracking-overview id="anchor-order-tracking-overview" />
      } @placeholder {
        <div id="anchor-order-tracking-overview" class="relative w-full h-159">
          <app-progress-loader />
        </div>
      }

      @defer (on viewport) {
        <app-offplt-promt id="anchor-offplt-promt" />
      } @placeholder {
        <div id="anchor-offplt-promt" class="relative w-full h-262">
          <app-progress-loader />
        </div>
      }

      @defer (on viewport) {
        <app-conversion-analytics id="anchor-conversion-analytics" />
      } @placeholder {
        <div id="anchor-conversion-analytics" class="relative w-full h-150">
          <app-progress-loader />
        </div>
      }
    } @else if (type() === 'offplt') {
      @defer (on viewport) {
        <app-order-transfer id="anchor-order-transfer" />
      } @placeholder {
        <div id="anchor-order-transfer" class="relative w-full h-144">
          <app-progress-loader />
        </div>
      }

      @defer (on viewport) {
        <app-influence-flow id="anchor-influence-flow" />
      } @placeholder {
        <div id="anchor-influence-flow" class="relative w-full h-169">
          <app-progress-loader />
        </div>
      }
    } @else {
      @defer (on viewport) {
        <app-income-progress-monitoring id="anchor-income-progress-monitoring" />
      } @placeholder {
        <div id="anchor-income-progress-monitoring" class="relative w-full h-129">
          <app-progress-loader />
        </div>
      }

      @defer (on viewport) {
        <app-cost-off-progress id="anchor-cost-off-progress" />
      } @placeholder {
        <div id="anchor-cost-off-progress" class="relative w-full h-307">
          <app-progress-loader />
        </div>
      }
    }
  </div>

  <div class="tools-bar">
    <app-selfhelp-btn />
    <div class="btn pointer-events-auto" (click)="showModal()">
      <OkrIcon iconBtn class="xxl text-3xl text-[#FFA400]" />
      <span class="label">目标管理</span>
    </div>

    <div class="flex-1 flex flex-col justify-center">
      <nz-anchor [nzBounds]="200" [nzTargetOffset]="50">
        <nz-link nzTitle="摘要" nzHref="#anchor-market-summary"></nz-link>

        <nz-link [nzTitle]="manageTitle" nzHref="#anchor-metric-overview">
          <ng-template #manageTitle>
            管理
            <br />
            指标
          </ng-template>
        </nz-link>

        @if (type() === 'own') {
          <nz-link [nzTitle]="operationalTitle" nzHref="#anchor-order-tracking-overview">
            <ng-template #operationalTitle>
              运营
              <br />
              指标
            </ng-template>
          </nz-link>

          <nz-link [nzTitle]="offpltTitle" nzHref="#anchor-offplt-promt">
            <ng-template #offpltTitle>
              端外
              <br />
              推广
            </ng-template>
          </nz-link>

          <nz-link nzTitle="转化" nzHref="#anchor-conversion-analytics"></nz-link>
        } @else if (type() === 'offplt') {
          <nz-link nzTitle="转化" nzHref="#anchor-order-transfer"></nz-link>
          <nz-link [nzTitle]="orderTrackingTitle" nzHref="#anchor-influence-flow">
            <ng-template #orderTrackingTitle>
              流入
              <br />
              流出
            </ng-template>
          </nz-link>
        } @else {
          <nz-link nzTitle="收入" nzHref="#anchor-income-progress-monitoring"></nz-link>
          <nz-link nzTitle="费用" nzHref="#anchor-cost-off-progress"></nz-link>
        }
      </nz-anchor>
    </div>

    <app-back-top />
  </div>
</div>
