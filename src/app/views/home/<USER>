import { computed, inject, Injectable, linkedSignal, signal } from '@angular/core'
import { rxResource, toObservable } from '@angular/core/rxjs-interop'
import { QueryEngineApiService } from '@api/query-engine'
import { getMonthProgress } from '@common/class'
import { toNumber } from '@common/function'
import { differenceInCalendarMonths, addMonths, addDays, format } from 'date-fns'
import { map } from 'rxjs'

@Injectable()
export class HomeService {
  readonly apiService = inject(QueryEngineApiService)

  today = signal(new Date())
  yesterday = signal(addDays(new Date(), -1))
  month = linkedSignal({
    source: () => this.today(),
    computation: (source) => {
      if (source.getDate() === 1) {
        return this.yesterday()
      }
      return this.today();
    }
  });
  month$ = toObservable(this.month)
  monthStr = computed(() => format(this.month(), 'yyyy-MM'))

  userUpdateTime = signal<string>(null);
  updateTime = linkedSignal({
    source: () => this.userUpdateTime(),
    computation: (source) => {
      if (source) {
        return source;
      }
      return this.updateTimeResource.value();
    }
  });
  updateTimeResource = rxResource({
    request: () => this.monthStr(),
    loader: ({ request }) => this.apiService.fetchUpdateTimeModel2(request).pipe(
      map(res => res.data?.dt || format(this.yesterday(), 'yyyy-MM-dd'))
    ),
  })

  updateTimeResource$ = toObservable(this.updateTime)

  timeProgressValue = computed(() => {
    if (this.updateTimeResource.value()) {
      const deadline = this.updateTimeResource.value();
      const [year, month] = deadline.split('-').map(toNumber);
      const today = this.today();
      const y = today.getFullYear();
      const m = today.getMonth() + 1;
      const isThisMonth = year === y && month === m;
      const progress = getMonthProgress(deadline);

      if (isThisMonth) {
        return progress;
      }
      return 100;
    }

    return null;
  })

  timeProgress = computed(() => {
    if (this.updateTimeResource.value()) {
      return `${this.timeProgressValue()}%`;
    }
    return null;
  })

  disabledMonth = (current: Date): boolean => {
    const startDateForMonth = new Date().setDate(1)
    const dateRight = addMonths(startDateForMonth, 1)

    return differenceInCalendarMonths(current, dateRight) > -1
  }
}
