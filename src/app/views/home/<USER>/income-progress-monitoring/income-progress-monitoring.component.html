<app-heading title="收入进度监控">
  <TransferIcon ngProjectAs="[icon]" class="text-2xl" />
</app-heading>

<div class="flex items-center flex-1 flex-wrap gap-x-4 gap-y-1.5 py-3">
  <div class="flex items-center">
    <label class="inline-flex items-center text-xs font-bold leading-5 whitespace-nowrap">统计口径：</label>
    <app-radio-group
      class="relative flex gap-1"
      [(ngModel)]="type"
      (ngModelChange)="buriedPoint('统计口径', type()); changeType()"
    >
      <app-radio class="tag-radio-new" activeClass="active" value="day">昨日</app-radio>
      <app-radio class="tag-radio-new" activeClass="active" value="month">当月累计</app-radio>
      <app-radio-thumb class="rounded-xs bg-primary" />
    </app-radio-group>
  </div>
  <div class="flex items-center">
    <label class="inline-flex items-center text-xs font-bold leading-5 whitespace-nowrap">订单类型：</label>
    <app-radio-group
      class="relative flex gap-1"
      [(ngModel)]="c_ord_type"
      (ngModelChange)="buriedPoint('订单类型', c_ord_type()); reRender()"
    >
      <app-radio class="tag-radio-new" activeClass="active" [value]="null">全部</app-radio>
      @for (item of config()?.c_ord_type; track item) {
        <app-radio class="tag-radio-new" activeClass="active" [value]="item">{{ item.showValue }}</app-radio>
      }
      <app-radio-thumb class="rounded-xs bg-primary" />
    </app-radio-group>
  </div>
  <div class="flex items-center">
    <label class="inline-flex items-center text-xs font-bold leading-5 whitespace-nowrap">一级业务渠道：</label>
    <app-radio-group
      class="relative flex gap-1"
      [(ngModel)]="c_firlev_channel"
      (ngModelChange)="buriedPoint('一级业务渠道', c_firlev_channel()); reRender()"
    >
      <app-radio class="tag-radio-new" activeClass="active" [value]="null">全部</app-radio>
      @for (item of config()?.c_firlev_channel; track item) {
        <app-radio class="tag-radio-new" activeClass="active" [value]="item">{{ item.showValue }}</app-radio>
      }
      <app-radio-thumb class="rounded-xs bg-primary" />
    </app-radio-group>
  </div>
  <div class="flex items-center">
    <label class="inline-flex items-center text-xs font-bold leading-5 whitespace-nowrap">二级业务渠道：</label>
    <app-radio-group
      class="relative flex gap-1"
      [(ngModel)]="c_seclev_channel"
      (ngModelChange)="buriedPoint('二级业务渠道', c_seclev_channel()); reRender()"
    >
      <app-radio class="tag-radio-new" activeClass="active" [value]="null">全部</app-radio>
      @for (item of seclevChannelOptions(); track $index) {
        <app-radio class="tag-radio-new" activeClass="active" [value]="item">{{ item.key }}</app-radio>
      }
      <app-radio-thumb class="rounded-xs bg-primary" />
    </app-radio-group>
  </div>
  <div class="flex items-center">
    <label class="inline-flex items-center text-xs font-bold leading-5 whitespace-nowrap">是否站点拼车：</label>
    <app-radio-group
      class="relative flex gap-1"
      [(ngModel)]="is_site_pkg"
      (ngModelChange)="buriedPoint('是否站点拼车', is_site_pkg()); reRender()"
    >
      <app-radio class="tag-radio-new" activeClass="active" [value]="null">全部</app-radio>
      @for (item of config()?.is_site_pkg; track item) {
        <app-radio class="tag-radio-new" activeClass="active" [value]="item">{{ item.showValue }}</app-radio>
      }
      <app-radio-thumb class="rounded-xs bg-primary" />
    </app-radio-group>
  </div>
</div>

<div class="flex flex-col gap-y-4 h-135">
  <div class="flex flex-wrap h-48">
    @if (cardLoading()) {
      <div class="flex items-center justify-center w-full">
        <app-line-spin />
      </div>
    } @else {
      <app-home-card
        [list]="list()"
        [display]="2"
        (updateIndex)="changeCard($event)"
        [tooltip]="tooltip"
        [themeArr]="['orange', 'orange', 'blue', 'blue']"
        [compareType]="type() === 'day' ? 'month' : 'year'"
      ></app-home-card>
    }
  </div>
  <div class="flex-1 min-w-0 mt-4 flex flex-col h-full shadow-md rounded-sm border border-neutral-100 relative">
    <!-- <div class="flex gap-x-5 items-center pt-2 px-4">
      <div class="flex items-center">
        <label class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap">
          选择指标：
        </label>
        <div>
          @for (item of chartMetrics(); track $index) {
            <ng-template #metricsTitleTemplate>
              {{ item?.showName }}
              <span class="text-xs opacity-30 px-1">({{ item?.aliasName }})</span>
            </ng-template>

            <label
              nz-popover
              nz-checkbox
              class="ml-0! text-xs!"
              [nzPopoverMouseEnterDelay]="0.5"
              [nzPopoverTitle]="metricsTitleTemplate"
              [nzPopoverContent]="item?.bizExpression"
              [(ngModel)]="item.checked"
              (ngModelChange)="changeItem()"
            >
              {{ item.showName }}
            </label>
          }
        </div>
      </div>
    </div> -->
    <div class="flex items-center px-4 pt-2">
      <label class="inline-flex items-center text-xs font-bold leading-5 whitespace-nowrap">口径选择：</label>
      <app-radio-group class="relative flex gap-1" [(ngModel)]="bizType" (ngModelChange)="getChartData()">
        <app-radio class="tag-radio-new" activeClass="active" value="1">财务口径</app-radio>
        <app-radio class="tag-radio-new" activeClass="active" value="2">业务口径</app-radio>
        <app-radio-thumb class="rounded-xs bg-primary" />
      </app-radio-group>
    </div>
    <div class="flex-1 min-h-0 relative flex items-center justify-center text-xs">
      @if (loading()) {
        <app-line-spin />
      } @else {
        @if (option()) {
          <app-graph class="absolute inset-2" [options]="option()" showAnnotations showAvgPlotLines />
        } @else if (errorMessage()) {
          <span class="text-slate-600 text-xs">{{ errorMessage() }}</span>
        }
      }
    </div>
  </div>
</div>
