import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>son<PERSON>ip<PERSON> } from '@angular/common'
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  computed,
  DestroyRef,
  effect,
  inject,
  signal,
} from '@angular/core'
import { takeUntilDestroyed } from '@angular/core/rxjs-interop'
import { FormsModule } from '@angular/forms'
import { CaerusApiService } from '@api/caerus'
import { QueryEngineApiService } from '@api/query-engine'
import { QueryOutputVo } from '@api/query-engine/model'
import { SwitchMap } from '@common/decorator'
import { BuriedPointService, LegendControlService, LegendItemClickHandler } from '@common/service'
import { QueryEngineFormService } from '@common/service/query-engine'
import { AreaSelectComponent } from '@shared/components/area-select'
import { ChartFunnelCompareComponent, ChartFunnelComponent } from '@shared/components/chart'
import { DateCompareComponent } from '@shared/components/date-compare'
import { GraphComponent } from '@shared/components/graph'
import { HeadingComponent } from '@shared/components/heading'
import { LineSpinComponent } from '@shared/components/line-spin'
import { RadioModule } from '@shared/modules/headless'
import { IconTransferComponent } from '@shared/modules/icons'
import { HomeService } from '@views/home/<USER>'
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox'
import { combineLatest, finalize } from 'rxjs'
import { MultipleXAxis } from '../order-tracking-overview/lib'
import { ProgressTrend } from './lib'
import { getMonthFirstAndNowDay } from '@common/class'
import { NzToolTipModule } from 'ng-zorro-antd/tooltip'
import { NzRadioModule } from 'ng-zorro-antd/radio'
import { CityPickerComponent } from '@shared/components/filters'
import { PAGE_NAME } from '@common/directive'

@Component({
  selector: 'app-conversion-analytics',
  templateUrl: './conversion-analytics.component.html',
  host: {
    class: 'block px-5',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [DatePipe, LegendControlService, QueryEngineFormService],
  imports: [
    NzToolTipModule,
    NzCheckboxModule,
    NzRadioModule,
    HeadingComponent,
    IconTransferComponent,
    RadioModule,
    FormsModule,
    DateCompareComponent,
    AreaSelectComponent,
    // CityPickerComponent,
    LineSpinComponent,
    GraphComponent,
    ChartFunnelCompareComponent,
    ChartFunnelComponent,
  ],
})
export class ConversionAnalyticsComponent implements AfterViewInit {
  page_name = inject(PAGE_NAME)
  readonly buriedPointService = inject(BuriedPointService)
  readonly apiService = inject(CaerusApiService)
  readonly formService = inject(QueryEngineFormService)
  readonly queryService = inject(QueryEngineApiService)
  readonly datePipe = inject(DatePipe)
  readonly legendControlService = inject(LegendControlService)
  readonly service = inject(HomeService)
  readonly destroyRef = inject(DestroyRef)

  viewType = signal('pass')
  area = signal(null)
  config = signal(null)
  top_filter = signal(null)
  allConfig = signal(null)
  checkValue = signal([])
  checkValue2 = signal(null)
  metrics = signal([])
  allMetrics = signal(null)
  selectMetrics = signal([])
  funnel_loading = signal(false)
  loading = signal(false)
  errorMessage = signal(null)
  errorMessageFunnel = signal(null)
  option = signal(null)
  type = signal('c_thilev_channel')
  funnelSeries = signal([])
  hasCompareDate = signal(false)

  showCheckValue = computed(() => {
    if (this.hasCompareDate()) {
      return ['当前期', '对比期']
    }
    // if (this.area().length === 2) {
    //   return this.area().map(c => {
    //     if (!c) {
    //       return '全国'
    //     }
    //     return c.value
    //   })
    // }
    return this.checkValue().map(c => {
      return c ? c.showValue : '全部'
    })
  })

  showCheckMetric = computed(() => {
    return this.selectMetrics().map(c => {
      return c.showName
    })
  })

  ngAfterViewInit(): void {
    this.subscribeToMonthChange()
    this.subscribeDateChange()
    this.fetchMetrics()
    this.fetchConfig()
  }

  subscribeToMonthChange() {
    combineLatest([this.service.month$, this.service.updateTimeResource$])
      .pipe(takeUntilDestroyed(this.destroyRef))
      .subscribe(([value, updateTime]) => {
        if (!updateTime) {
          return
        }
        // console.log('value', value, updateTime)
        const month = this.datePipe.transform(value, 'yyyy-MM-dd')
        const [startTime] = getMonthFirstAndNowDay(month)
        this.formService.dt.patchValue({
          startTime: startTime,
          endTime: updateTime,
        })
        this.getFunnlData()
      })
  }

  subscribeDateChange() {
    this.formService.form.valueChanges.pipe(takeUntilDestroyed(this.destroyRef)).subscribe(value => {
      if (value.compareDt && value.compareDt.startTime) {
        const _check = this.checkValue()[0]
        this.checkValue.set([_check])
        this.checkValue2.set(_check)
        // const _area = this.area()[0] ? this.area()[0] : null
        // this.area.set([_area])
        this.hasCompareDate.set(true)
      } else {
        this.hasCompareDate.set(false)
      }
      this.getFunnlData()
    })
  }

  fetchMetrics() {
    this.apiService.fetchMetricsConfig('cockpit_own_user_transfer_metrics').subscribe(res => {
      if (res.data) {
        const subMetric = res.data[this.viewType()].subMetric
        this.metrics.set(subMetric)
        this.allMetrics.set(res.data)
        const filterMetrics = this.metrics().filter((_s, i) => i < 2)
        filterMetrics.push({
          showName: filterMetrics[filterMetrics.length - 1].tagName,
          hidden: true,
        })
        this.selectMetrics.set(filterMetrics)
        this.getFunnlData()
      } else {
        this.metrics.set([])
      }
    })
  }

  fetchConfig() {
    this.apiService.fetchDimensionConfig('cockpit_dim').subscribe(res => {
      if (res.data) {
        // console.log('转化效率config', res.data)
        const obj = {}
        res.data['own_pass_transfer'].forEach(filter => {
          obj[filter.keyName] = filter.values
        })
        this.top_filter.set(obj)
        // console.log('transfer-config', obj)
        this.config.set(obj[this.type()])
        this.allConfig.set(res.data)
        this.checkValue.set([null, this.config()[0]])
        this.getFunnlData()
      } else {
        this.config.set(null)
      }
    })
  }

  changeArea() {
    if ([this.area()].length === 2) {
      const _check = this.checkValue()[0]
      this.checkValue.set([_check])
      this.checkValue2.set(_check)
    }
    this.getFunnlData()
    this.getChartData()
  }

  changeViewType() {
    if (!this.allConfig() || !this.allMetrics()) {
      return
    }
    let key
    if (this.viewType() === 'pass') {
      key = 'own_pass_transfer'
      this.type.set('c_thilev_channel')
    } else {
      key = 'own_driver_transfer'
      this.type.set('c_reply_propp_name')
    }

    const obj = {}
    this.allConfig()[key].forEach(filter => {
      obj[filter.keyName] = filter.values
    })
    this.top_filter.set(obj)
    this.config.set(obj[this.type()])
    if (this.hasCompareDate()) {
      this.checkValue.set([null])
    } else {
      this.checkValue.set([null, this.config()[0]])
    }

    const subMetric = this.allMetrics()[this.viewType()].subMetric
    this.metrics.set(subMetric)
    const filterMetrics = this.metrics().filter((_s, i) => i < 2)
    filterMetrics.push({
      showName: filterMetrics[filterMetrics.length - 1].tagName,
      hidden: true,
    })
    this.selectMetrics.set(filterMetrics)
    this.getFunnlData()
  }
  changeType() {
    if (!this.top_filter()) {
      return
    }
    this.config.set(this.top_filter()[this.type()])
    if (this.hasCompareDate()) {
      this.checkValue.set([null])
    } else {
      this.checkValue.set([null, this.config()[0]])
    }

    this.getFunnlData()
  }

  getQuery(type) {
    const body = this.formService.value()
    if (body.compareDt && !body.compareDt.endTime) {
      return
    }
    if (type === 'funnl') {
      body.dimensions = [{ id: null, extendName: 'dt' }]
      body.outAggDimensions = []
    } else {
      body.dimensions = [
        {
          id: null,
          extendName: 'dt',
          predefineCompareType: ['yw'],
        },
      ]
    }

    const items = this.checkValue().map(c => {
      if (!c) {
        return null
      }
      return {
        conditionType: 2,
        condition: '=',
        id: null,
        extendName: c.extendName,
        value: [
          {
            key: c.key,
            value: c.value,
          },
        ],
        valueType: null,
      }
    })
    const temp = []
    const _metrics = type === 'funnl' ? this.metrics() : this.selectMetrics().filter(s => !s.hidden)

    console.log('_metrics', _metrics)
    const area = [this.area()].map(a => {
      if (!a) {
        return null
      }
      return {
        conditionType: 2,
        condition: '=',
        extendName: a.extendName,
        value: [
          {
            key: a.key,
            value: a.value,
          },
        ],
        valueType: null,
      }
    })

    // 当区域只有一个的时候
    if (true) {
      if (type === 'chart' && _metrics.length === 2) {
        const arr = []
        items.forEach((i, index) => {
          arr.push({
            userDefAliasName: `${_metrics[_metrics.length - 1].tagName}_${i ? i.value[0].value : '全部'}`,
            userDefExtendName: `${_metrics[_metrics.length - 1].extendName}_rate_${i ? i.extendName : 'all'}_${index}`,
            expression: i
              ? `${_metrics[1].extendName}_${i.extendName}_${index}_1/${_metrics[0].extendName}_${i.extendName}_${index}_0`
              : `${_metrics[1].extendName}_${index}_1/${_metrics[0].extendName}_${index}_0`,
          })
        })
        body.userDefExpressions = arr
      }
      items.forEach((i, index) => {
        _metrics.forEach((m, _index) => {
          temp.push({
            extendName: m.extendName,
            type: 3,
            userDefAliasName: i ? `${m.showName}_${i.value[0].value}` : `${m.showName}_全部`,
            userDefExtendName: i
              ? `${m.extendName}_${i.extendName}_${index}_${_index}`
              : `${m.extendName}_${index}_${_index}`,
            filter: {
              items: i
                ? [
                    i,
                    ...area.filter(a => a),
                    {
                      conditionType: 2,
                      condition: '>=',
                      extendName: 'td',
                      value: [
                        {
                          key: m.displayOrder,
                          value: m.displayOrder,
                        },
                      ],
                      valueType: null,
                    },
                  ]
                : [
                    ...area.filter(a => a),
                    {
                      conditionType: 2,
                      condition: '>=',
                      extendName: 'td',
                      value: [
                        {
                          key: m.displayOrder,
                          value: m.displayOrder,
                        },
                      ],
                      valueType: null,
                    },
                  ],
              type: null,
            },
          })
        })
      })
    } else {
      if (type === 'chart') {
        const arr = []
        this.area().forEach((i, index) => {
          arr.push({
            userDefAliasName: `${_metrics[_metrics.length - 1].tagName}_${i ? i.value : '全国'}`,
            userDefExtendName: `${_metrics[_metrics.length - 1].extendName}_rate_${i ? i.extendName : 'all'}_${index}`,
            expression: i
              ? `${_metrics[1].extendName}_${i.extendName}_${index}_1/${_metrics[0].extendName}_${i.extendName}_${index}_0`
              : `${_metrics[1].extendName}_${index}_1/${_metrics[0].extendName}_${index}_0`,
          })
        })
        body.userDefExpressions = arr
        console.log('userDefExpressions', arr)
      }
      area.forEach((i, index) => {
        _metrics.forEach((m, _index) => {
          temp.push({
            extendName: m.extendName,
            type: 3,
            userDefAliasName: i ? `${m.showName}_${i.value[0].value}` : `${m.showName}_全国`,
            userDefExtendName: i
              ? `${m.extendName}_${i.extendName}_${index}_${_index}`
              : `${m.extendName}_${index}_${_index}`,
            filter: {
              items: i
                ? [
                    i,
                    ...items.filter(i => i),
                    {
                      conditionType: 2,
                      condition: '>=',
                      extendName: 'td',
                      value: [
                        {
                          key: _index + 1,
                          value: _index + 1,
                        },
                      ],
                      valueType: null,
                    },
                  ]
                : [
                    ...items.filter(i => i),
                    {
                      conditionType: 2,
                      condition: '>=',
                      extendName: 'td',
                      value: [
                        {
                          key: _index + 1,
                          value: _index + 1,
                        },
                      ],
                      valueType: null,
                    },
                  ],
              type: null,
            },
          })
        })
      })
    }
    body.metrics = temp
    body.useLocalQuery = true

    return body
  }

  @SwitchMap()
  getFunnlData() {
    if (this.checkValue().length === 0 || this.metrics().length === 0) {
      return
    }
    if (this.formService.hasDateCompare() && !this.formService.value().compareDt.startTime) {
      return
    }
    const query = this.getQuery('funnl')
    this.getChartData()
    this.funnel_loading.set(true)
    this.errorMessageFunnel.set(null)
    return this.queryService
      .search(query, 'conversion-funnl')
      .pipe(finalize(() => this.funnel_loading.set(false)))
      .subscribe(res => {
        if (!res.data) {
          this.errorMessageFunnel.set(res.message)
          return
        }
        if (res.data && res.data.data.length !== 0) {
          const data = res.data.data[0]
          const FunnelData = []
          if (true) {
            this.checkValue().forEach((c, index) => {
              FunnelData.push(
                this.metrics().map((l, _index) => {
                  return {
                    name: l.showName,
                    leftLabel: l.showName,
                    rightLabel: l.tagName,
                    value: !c
                      ? Number(data[`${l.extendName}_${index}_${_index}`])
                      : Number(data[`${l.extendName}_${c.extendName}_${index}_${_index}`]),
                  }
                })
              )
            })
          } else {
            this.area().forEach((c, index) => {
              FunnelData.push(
                this.metrics().map((l, _index) => {
                  return {
                    name: l.showName,
                    leftLabel: l.showName,
                    rightLabel: l.tagName,
                    value: !c
                      ? Number(data[`${l.extendName}_${index}_${_index}`])
                      : Number(data[`${l.extendName}_${c.extendName}_${index}_${_index}`]),
                  }
                })
              )
            })
          }

          if (this.hasCompareDate()) {
            const compareData = res.data.compareData[0]
            this.checkValue().forEach((c, index) => {
              FunnelData.push(
                this.metrics().map((l, _index) => {
                  return {
                    name: l.showName,
                    leftLabel: l.showName,
                    rightLabel: l.tagName,
                    value: !c
                      ? Number(compareData[`${l.extendName}_${index}_${_index}`])
                      : Number(compareData[`${l.extendName}_${c.extendName}_${index}_${_index}`]),
                  }
                })
              )
            })
          }
          console.log('arr', FunnelData)
          if (FunnelData.length === 1) {
            this.funnelSeries.set(FunnelData[0])
          } else {
            this.funnelSeries.set(FunnelData)
          }
        } else {
          this.funnelSeries.set([])
        }
      })
  }

  @SwitchMap()
  getChartData() {
    this.loading.set(true)
    const query = this.getQuery('chart')
    this.legendControlService.reset()
    return this.queryService
      .search(query, 'conversion-chart')
      .pipe(finalize(() => this.loading.set(false)))
      .subscribe(res => {
        if (res.data) {
          this._setChartData(res.data)
        } else {
          this.option.set(null)
          this.errorMessage.set(res.message)
        }
      })
  }

  private _setChartData(data: QueryOutputVo) {
    try {
      let chart
      if (this.formService.value().compareDt?.startTime) {
        chart = new MultipleXAxis(data)
      } else {
        chart = new ProgressTrend(data)
      }
      const legendItemClick = LegendItemClickHandler(this.legendControlService)
      chart.plotOptions.series.events = { legendItemClick }

      this.option.set(chart?.getOption() || null)
    } catch (e) {
      this.option.set(null)
      this.errorMessage.set(e)
      console.error(e)
    }
  }

  clickChart(params) {
    if (params.data) {
      console.log('params', params)
      if (params.dataIndex === this.metrics().length - 1) {
        this.selectMetrics.set([this.metrics()[this.metrics().length - 1]])
      } else {
        const filterMetrics = [this.metrics()[params.dataIndex], this.metrics()[params.dataIndex + 1]]
        filterMetrics.push({
          showName: filterMetrics[filterMetrics.length - 1].tagName,
          hidden: true,
        })
        this.selectMetrics.set(filterMetrics)
      }
      this.getChartData()
    }
  }

  handleCheckValueChange(value: any[]) {
    // console.log('[$event]', value);
    // console.log('[checkValue]', this.checkValue());
    if (this.hasCompareDate()) {
      this.checkValue.set([value.pop()])
    } else {
      this.checkValue.set(value)
    }
    let name
    if (this.type() === 'c_thilev_channel' || this.type() === 'c_reply_propp_name') {
      name = '产品渠道'
    } else if (this.type() === 'cdriver_abc_type') {
      name = 'ABC车主'
    } else {
      name = '乘客四类'
    }
    const _value = value
      .map(v => {
        if (v) {
          return v.showValue
        } else {
          return '全部'
        }
      })
      .join(',')
    this.buriedPoint(name, _value)
  }

  buriedPoint(name, value) {
    this.buriedPointService.addStat('dida_dpm_caerus_home_user_fliter_click', {
      page_name: this.page_name,
      fliter_name: name,
      fliter_options: value,
    })
  }

  handleCheckValueChange2(value: any) {
    let name
    if (this.type() === 'c_thilev_channel' || this.type() === 'c_reply_propp_name') {
      name = '产品渠道'
    } else if (this.type() === 'cdriver_abc_type') {
      name = 'ABC车主'
    } else {
      name = '乘客四类'
    }
    this.buriedPoint(name, value ? value.showValue : '全部')
    this.checkValue.set([value])
  }
}
