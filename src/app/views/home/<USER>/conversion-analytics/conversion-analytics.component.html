<div class="relative">
  <app-heading title="用户转化效率分析">
    <TransferIcon ngProjectAs="[icon]" class="text-2xl" />
    <ng-container ngProjectAs="[tab]">
      <app-radio-group
        [(ngModel)]="viewType"
        (ngModelChange)="changeViewType()"
        class="flex items-start px-3 gap-[30px]"
      >
        <app-radio class="line-radio text-xs whitespace-nowrap" activeClass="active" value="pass">乘客视角</app-radio>
        <app-radio class="line-radio text-xs whitespace-nowrap" activeClass="active" value="driver">车主视角</app-radio>
      </app-radio-group>
    </ng-container>
  </app-heading>
</div>

<span class="text-neutral-400 text-[12px] pl-4">
  注：此处漏斗涉及指标均为日均口径；乘客视角是自有渠道数据；车主视角是全部渠道数据。
</span>

<div class="flex flex-col gap-3 my-3">
  <div class="flex items-center flex-1 flex-wrap gap-2">
    <app-date-compare compareBtn deleteBtn [showDtType]="false" />
    <div class="flex items-center">
      <label class="inline-flex items-center text-xs font-bold leading-5 whitespace-nowrap">区域：</label>
      <app-area-select
        [(ngModel)]="area"
        (ngModelChange)="buriedPoint('区域', !area() ? '全部' : area().key); getFunnlData()"
      />
      <!-- <app-city-picker
        [(ngModel)]="area"
        (ngModelChange)="changeArea()"
        [maxMultipleCount]="hasCompareDate() || checkValue().length === 2 ? 1 : 2"
      /> -->
    </div>
  </div>
  <div class="flex items-center flex-1 flex-wrap gap-x-4 gap-y-1.5 px-6">
    <div class="flex items-center">
      <label class="inline-flex items-center text-xs font-bold leading-5 whitespace-nowrap">维度选择：</label>
      <app-radio-group
        class="relative flex gap-1"
        [(ngModel)]="type"
        (ngModelChange)="buriedPoint('维度选择', type()); changeType()"
      >
        @if (viewType() === 'pass') {
          <app-radio class="tag-radio-new" activeClass="active" value="c_thilev_channel">按产品渠道</app-radio>
          <app-radio class="tag-radio-new" activeClass="active" value="c_pass_fourca_user_type">按乘客四类</app-radio>
        } @else {
          <app-radio class="tag-radio-new" activeClass="active" value="c_reply_propp_name">按产品渠道</app-radio>
          <app-radio class="tag-radio-new" activeClass="active" value="cdriver_fourca_user_type">按车主四类</app-radio>
          <app-radio class="tag-radio-new" activeClass="active" value="cdriver_abc_type">按ABC车主</app-radio>
        }

        <app-radio-thumb class="rounded-xs bg-primary" />
      </app-radio-group>
    </div>
    <div class="flex">
      <label class="inline-flex text-xs font-bold leading-5 whitespace-nowrap">
        @if (type() === 'c_thilev_channel' || type() === 'c_reply_propp_name') {
          产品渠道：
        } @else if (type() === 'cdriver_abc_type') {
          ABC车主：
        } @else {
          {{ viewType() === 'driver' ? '车主' : '乘客' }}四类：
        }
      </label>
      @if (hasCompareDate()) {
        <nz-radio-group [(ngModel)]="checkValue2" (ngModelChange)="handleCheckValueChange2($event); getFunnlData()">
          <label class="text-xs!" nz-radio [nzValue]="null">全部</label>
          @for (item of config(); track item) {
            <label class="text-xs!" nz-radio [nzValue]="item">{{ item.showValue }}</label>
          }
        </nz-radio-group>
      } @else {
        <nz-checkbox-group [ngModel]="checkValue()" (ngModelChange)="handleCheckValueChange($event); getFunnlData()">
          <label
            nz-checkbox
            class="text-xs! ml-0!"
            [nzValue]="null"
            [nzDisabled]="hasCompareDate() ? false : checkValue().length === 2 && !showCheckValue().includes('全部')"
            [nz-tooltip]="
              checkValue().length === 2 && !showCheckValue().includes('全部') && '最多可支持两个选项同时对比查看'
            "
          >
            全部
          </label>

          @for (item of config(); track item) {
            <label
              nz-checkbox
              class="text-xs! ml-0!"
              [nzValue]="item"
              [nzDisabled]="
                hasCompareDate() ? false : checkValue().length === 2 && !showCheckValue().includes(item.showValue)
              "
              [nz-tooltip]="
                checkValue().length === 2 &&
                !showCheckValue().includes(item.showValue) &&
                '最多可支持两个选项同时对比查看'
              "
            >
              {{ item.showValue }}
            </label>
          }
        </nz-checkbox-group>
      }
    </div>
  </div>
</div>
<div class="grid grid-cols-12 gap-x-5 mb-5">
  <div class="col-span-4">
    <div class="flex flex-col h-105 shadow-md rounded-sm border border-neutral-100 relative">
      <div class="w-full">
        @if (funnel_loading()) {
          <div class="pt-[100px]">
            <app-line-spin />
          </div>
        } @else if (errorMessageFunnel()) {
          <span class="text-slate-600 text-xs">{{ errorMessageFunnel() }}</span>
        } @else if (checkValue().length === 1 && !hasCompareDate()) {
          <app-chart-funnel [value]="funnelSeries()" (click)="clickChart($event)" />
        } @else {
          <div class="absolute top-[15px] w-full">
            <div class="w-[55%] mx-auto">
              <div class="flex justify-between w-full">
                @for (c of showCheckValue(); track c) {
                  <div class="whitespace-nowrap">{{ c }}</div>
                }
              </div>
            </div>
          </div>
          <app-chart-funnel-compare
            [themes]="['#73c0de', '#b6a2de', '#91cc75', '#f6bd16', '#fc8452', '#e01f54']"
            [value]="funnelSeries()"
            (click)="clickChart($event)"
          />
        }
      </div>
    </div>
  </div>
  <div class="col-span-8">
    <div class="flex flex-col h-105 shadow-md rounded-sm border border-neutral-100 py-2">
      <div class="flex items-center gap-6">
        <div class="flex items-center gap-1 px-4 py-1 font-bold text-[12px]">
          已选指标：【
          {{ showCheckMetric().join('，') }}
          】
        </div>
        <div class="flex items-center gap-1 px-4 py-1 font-bold text-[12px]">
          已选维度：【
          {{ showCheckValue().join('，') }}
          】
        </div>
      </div>

      <div class="text-xs text-neutral-400 px-4">
        说明：点击左侧漏斗的彩色条，可自动选择彩色条对应的指标和下一级指标，以及两级指标的转化率。点击图例默认单选，长按shift键点击可多选。
      </div>
      <div class="flex-1 min-h-0 relative flex items-center justify-center text-xs">
        @if (loading()) {
          <app-line-spin />
        } @else {
          @if (option()) {
            <app-graph class="absolute inset-2" [options]="option()" showAnnotations showAvgPlotLines />
          } @else if (errorMessage()) {
            <span class="text-slate-600 text-xs">{{ errorMessage() }}</span>
          }
        }
      </div>
    </div>
  </div>
</div>
