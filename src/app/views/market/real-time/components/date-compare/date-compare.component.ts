import { DatePipe } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { AfterViewInit, ChangeDetectionStrategy, Component, computed, effect, inject, signal } from '@angular/core';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { differenceInCalendarDays } from 'date-fns';
import { addDays } from 'date-fns';

import { IconDeleteComponent } from '@shared/modules/icons';
import { QueryEngineForm, QueryEngineFormService } from '@common/service/query-engine';
import { RealTimeService } from '../../real-time.service';

@Component({
  selector: 'app-date-compare',
  template: `
    <div class="flex items-center">
      <div>当前日期：</div>
      <ng-container>
        <nz-date-picker [(ngModel)]="baseDate" [nzShowToday]="false" [nzDisabledDate]="disabledStartDate" />
      </ng-container>
      @if (compareDateVisibled()) {
        <div class="w-4"></div>
        <div>对比日期：</div>
        <nz-date-picker [(ngModel)]="compareDate" [nzShowToday]="false" [nzDisabledDate]="disabledDate" />
        <DeleteIcon iconBtn class="xl ml-1" (click)="removeCompareDate()" />
      }
      @else {
        <button class="ml-2!" nz-button nzType="default" (click)="addCompareDate()">指定对比日期</button>
      }
    </div>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    FormsModule,
    NzDatePickerModule,
    NzButtonModule,
    IconDeleteComponent,
  ],
  providers: [
    DatePipe,
  ],
})
export class DateCompareComponent implements AfterViewInit {

  readonly service = inject(RealTimeService);
  readonly formService = inject(QueryEngineFormService);
  readonly datePipe = inject(DatePipe);
  
  today = signal(new Date());
  baseDate = signal(new Date());
  compareDate = signal(null);
  compareDateVisibled = signal(false);

  isNotToday = computed(() => {
    const todayStr = this.datePipe.transform(this.today(), 'yyyy-MM-dd');
    const baseDateStr = this.datePipe.transform(this.baseDate(), 'yyyy-MM-dd');
    
    return todayStr !== baseDateStr;
  })


  constructor() {
    effect(() => {
      if (this.baseDate()) {
        const value = this.datePipe.transform(this.baseDate(), 'yyyy-MM-dd');

        this.updateDate('dt', value);
      }

      if (this.compareDate()) {
        const value = this.datePipe.transform(this.compareDate(), 'yyyy-MM-dd');

        this.updateDate('compareDt', value);
      }
    })
  }


  ngAfterViewInit(): void {
  }


  disabledDate = (current: Date): boolean => {
    // Can not select days before today and today
    return differenceInCalendarDays(current, this.today()) > -1;
  }


  disabledStartDate = (current: Date): boolean => {
    // Can not select days before today and today
    return differenceInCalendarDays(current, this.today()) > 0;
  }


  updateDate(target: keyof QueryEngineForm, value: string) {
    // if (target === 'dt') {
    //   if (this.isNotToday()) {
    //     console.log('起始日期不等于当日，定时器停止。');
    //     this.service.stop();
    //   } else {
    //     console.log('起始日期等于当日，定时器启动。');
    //     this.service.restart();
    //   }
    // }
    this.formService[target].patchValue({
      startTime: value,
      endTime: value
    })
  }


  addCompareDate() {
    this.formService.addDateCompare();
    this.compareDateVisibled.set(true);
    this.compareDate.set(addDays(this.baseDate(), -1));
  }


  removeCompareDate() {
    this.formService.removeDateCompare();
    this.compareDateVisibled.set(false);
    this.compareDate.set(null);
  }

}
