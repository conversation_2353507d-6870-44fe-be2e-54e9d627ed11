import { AfterViewInit, ChangeDetectionStrategy, Component, DestroyRef, inject, signal } from '@angular/core';
import { skip, switchMap, startWith, interval, map, takeUntil, Subject } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

import { SwitchMap } from '@common/decorator';
import { QueryEngineFormService } from '@common/service/query-engine';
import { RealTimeService } from '../../real-time.service';

@Component({
  selector: 'app-progress-bar',
  template: '',
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: {
    'class': 'pointer-events-none ease-linear bg-black/10',
    '[style.width.%]': 'width()',
    '[class.transition-all]': 'width() > 0'
  },
  imports: [],
})
export class ProgressBarComponent implements AfterViewInit {

  readonly service = inject(RealTimeService);
  readonly formService = inject(QueryEngineFormService);
  readonly destroyRef = inject(DestroyRef);

  clearInterval$ = new Subject<void>();
  width = signal(0);
  
  ngAfterViewInit(): void {
    this.service.stop$.subscribe(() => {
      this.width.set(0);
      this.clearInterval$.next();
    })
    
    this.service.start$.subscribe(() => {
      this.updateWidth();
    })

    this.service.source$.pipe(
      skip(1),
      takeUntilDestroyed(this.destroyRef)
    ).subscribe(() => {
      this.width.set(0);
    })

    this.service.source$.pipe(
      switchMap(() => this.service.interval$.pipe(startWith(0))),
      takeUntilDestroyed(this.destroyRef)
    ).subscribe(() => {
      this.updateWidth();
    })
  }


  @SwitchMap()
  updateWidth() {
    return interval(100).pipe(
      map(i => i / 10 / 60 / this.service.minute * 100),
      takeUntil(this.clearInterval$),
      takeUntilDestroyed(this.destroyRef)
    ).subscribe((value) => {
      const width = Math.min(value, 100);

      this.width.set(width);
    })
  }

}
