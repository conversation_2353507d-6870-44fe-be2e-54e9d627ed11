import { FormsModule } from '@angular/forms';
import { DecimalPipe, NgTemplateOutlet } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, Component, computed, DestroyRef, effect, inject, signal } from '@angular/core';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { combineLatest, debounceTime, finalize, forkJoin, startWith, switchMap } from 'rxjs';
import { NzPopoverModule } from 'ng-zorro-antd/popover';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NzTableModule, NzTableSortFn } from 'ng-zorro-antd/table';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzInputModule } from 'ng-zorro-antd/input';
import { format } from 'date-fns';

import { CaerusApiService } from '@api/caerus';
import { isUndefinedOrNull } from '@common/function';
import { SwitchMap } from '@common/decorator';
import { BuriedPointService } from '@common/service';
import { PAGE_NAME } from '@common/directive';
import { QueryEngineApiService } from '@api/query-engine';
import { QueryEngineFormService } from '@common/service/query-engine';
import { SplitPipe } from '@shared/pipes/split';
import { RadioModule } from '@shared/modules/headless';
import { IconSearchComponent } from '@shared/modules/icons';
import { SearchPipe } from '@shared/pipes/search';
import { RealTimeComponent } from '../real-time.component';
import { RealTimeService } from '../real-time.service';
import { ValueFormatter } from '@shared/components/value-formatter';
import { trace } from '@common/const';

export class FilterItemValue {
  constructor(
    /** 编码【数字或者英文字符】 */
    public key: string,
    /** 码值【中文名称可用来展示】 */
    public value: string,
  ) {}
}

class FilterItem {
  /** 运算类型 逻辑运算 1,条件运算 2 */
  conditionType = 2;
  /** 运算标识
   * - 条件运算公式 [>,<,=,>=,<=,!=,not null,in]
   * - 逻辑运算公式 [and,or] */
  condition = '=';
  /** 指标id/维度id */
  id = null;
  /** 维度英文名 */
  extendName: string;
  value: FilterItemValue[];
  constructor(props?: Partial<FilterItem>) {
    if (props) {
      Object.assign(this, { ...props });
    }
  }
}


class DimensionItem {
  id = null;
  extendName: string;
  predefineCompareType: string[];
  constructor(props?: Partial<DimensionItem>) {
    if (props) {
      Object.assign(this, { ...props });
    }
  }
}


interface ColumnItem {
  showName: string;
  extendName: string;
  sortFn: NzTableSortFn<any> | null;
  diffSortFn: NzTableSortFn<any> | null;
}

@Component({
  selector: 'app-real-time-area',
  templateUrl: './real-time-area.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    // DecimalPipe,
    NgTemplateOutlet,
    FormsModule,
    NzInputModule,
    NzButtonModule,
    NzDropDownModule,
    NzCheckboxModule,
    NzPopoverModule,
    NzTableModule,
    RadioModule,
    ValueFormatter,
    IconSearchComponent,
    SplitPipe,
  ],
  providers: [
    SearchPipe
  ]
})
export class RealTimeAreaComponent implements AfterViewInit {

  readonly service = inject(RealTimeService);
  readonly apiService = inject(CaerusApiService);
  readonly searchPipe = inject(SearchPipe);
  readonly queryEngineApiService = inject(QueryEngineApiService);
  readonly formService = inject(QueryEngineFormService);
  readonly buriedPointService = inject(BuriedPointService);
  readonly page_name = inject(PAGE_NAME);
  readonly destroyRef = inject(DestroyRef);
  readonly parent = inject(RealTimeComponent);

  firstRowData = signal(null);
  listOfData = signal([]);
  listOfDisplayData = signal([]);
  errorMessage = signal(null);
  querying = signal(false);
  searchValue = signal<string>(null);
  visible = signal<boolean>(null);

  order_type       = signal<{ key: string, value: string, extendName: string }>(null);
  c_firlev_channel = signal<{ key: string, value: string, extendName: string }>(null);
  c_seclev_channel = signal<{ key: string, value: string, extendName: string, relValue: string }>(null);
  is_site_pkg_book = signal<{ key: string, value: string, extendName: string, }>(null);
  dimension = signal<string>(null);
  predefineCompareType = signal<string>('dt');

  order_type$ = toObservable(this.order_type);
  c_firlev_channel$ = toObservable(this.c_firlev_channel);
  c_seclev_channel$ = toObservable(this.c_seclev_channel);
  is_site_pkg_book$ = toObservable(this.is_site_pkg_book);
  dimension$ = toObservable(this.dimension);
  predefineCompareType$ = toObservable(this.predefineCompareType);

  listOfColumns: ColumnItem[] = [
  ];
  
  isToday = computed(() => {
    const today = format(new Date(), 'yyyy-MM-dd');
    if (this.formService.form$()) {
      const { startTime } = this.formService.form$().dt;

      return startTime === today;
    }
    return false;
  })

  conditionKey = computed(() => {
    return this.isToday() ? 'condition_name' : 'union_condition_name';
  })

  // 大区 city_bio_region > 省份 province_name >城市 city_name
  regionDisabled = computed(() => {
    const { extendName } = this.parent.areaForm$() || {};

    if (
      extendName === 'city_bio_region'||
      isUndefinedOrNull(extendName)
    ) {
      return false;
    }

    return true;
  });


  provinceDisabled = computed(() => {
    const { extendName } = this.parent.areaForm$() || {};
    
    if (
      extendName === 'city_bio_region' || 
      extendName === 'province_name' || 
      isUndefinedOrNull(extendName)
    ) {
      return false;
    }

    return true;
  });

  constructor() {
    effect(() => {
      const { extendName } = this.parent.areaForm$() || {};
      
      if (extendName) {
        if (extendName === 'is_top20_city') {
          this.dimension.set('city_name');
        }
        else {
          this.dimension.set(extendName);
        }
      }
    });

    effect(() => {
      if (this.service.config()) {
        const { dimensions, metrics }  = this.service.config().realTimeArea;

        this.dimension.set(dimensions[0].extendName);
        metrics.forEach(({ showName, extendName, }, index) => {
          this.listOfColumns.push({
            showName, 
            extendName,
            sortFn: (a, b) => (+a[extendName]) - (+b[extendName]),
            diffSortFn: (pre, next, sortOrder) => {
              let a = pre[`${extendName}:${this.predefineCompareType()}_DIFF`];
              let b = next[`${extendName}:${this.predefineCompareType()}_DIFF`];
              
              if (sortOrder === 'descend') {
                if (a === null && b === null) return 0;
                if (a === null) return -1;  // a 是 null，b 排在前面
                if (b === null) return 1; // b 是 null，a 排在前面
            
                return parseFloat(a) - parseFloat(b)
              }
              else {
                if (a === null && b === null) return 0;
                if (a === null) return 1;  // a 是 null，b 排在前面
                if (b === null) return -1; // b 是 null，a 排在前面
                
                return parseFloat(a) - parseFloat(b)
              }
            }
          })
        })
      }
    })

    effect(() => {
      trace(`埋点上报：实时大盘-局部筛选项点击时上报`);
      this.buriedPointService.addStat('dida_dpm_caerus_L_fliter_Realtime', {
        page_name: this.page_name,
        fliter_position: 2,
        fliter_options: {
          predefineCompareType: this.predefineCompareType(),
          dimension: this.dimension(),
          order_type: this.order_type()?.value || '全部',
          c_firlev_channel: this.c_firlev_channel()?.value || '全部',
        }
      })
    })

    effect(() => {
      const c_firlev_channel = this.c_firlev_channel();
      const c_seclev_channel = this.c_seclev_channel();

      if (
        c_firlev_channel === null ||
        c_firlev_channel !== null && c_seclev_channel !== null && c_seclev_channel.relValue !== c_firlev_channel.value
      ) {
        this.c_seclev_channel.set(null);
      }

      if (c_firlev_channel?.value === '外输渠道') {
        this.is_site_pkg_book.set(null);
      }
    })
  }

  ngAfterViewInit(): void {
    combineLatest([
      this.order_type$,
      this.c_firlev_channel$,
      this.c_seclev_channel$,
      this.is_site_pkg_book$,
      this.dimension$,
      this.service.deadline$,
      this.service.config$,
      this.formService.form.valueChanges.pipe(startWith(this.formService.form.value)),
      this.predefineCompareType$,
      this.service.source$.pipe(
        switchMap(() => this.service.interval$.pipe(startWith(0))),
      )
    ]).pipe(
      debounceTime(300),
      takeUntilDestroyed(this.destroyRef)
    ).subscribe(([a, b, c, d, e, f, g]) => {
      // trace('[input]', [a, b, c, d, e]);
      if ([a, b, c, d, e, f, g].some(item => item !== null)) {
        this.query();
        this.service.restore();
      }
    })
  }


  private countryBody() {
    const value = this.formService.value();

    value.filter.items = [];
    value.dimensions = [];

    if (this.service.deadline()) {
      const time = this.service.deadline();

      value.filter.items = value.filter.items.concat([
        new FilterItem({
          extendName: this.service.config().realTimeArea.minuteExtendName,
          value: [ new FilterItemValue(time, time) ]
        })],
        new FilterItem({
          extendName: 'one_h_time_slice',
          condition: '<=',
          value: [ new FilterItemValue('24:00:00', '24:00:00') ]
        })
      );
    }

    if (this.order_type()) {
      value.filter.items = value.filter.items.concat(new FilterItem({
        extendName: this.order_type().extendName,
        value: [ this.order_type() ]
      }));
    }
    
    if (this.c_firlev_channel()) {
      value.filter.items = value.filter.items.concat(new FilterItem({
        extendName: this.c_firlev_channel().extendName,
        value: [ this.c_firlev_channel() ]
      }));
    }

    if (this.c_seclev_channel()) {
      value.filter.items = value.filter.items.concat(new FilterItem({
        extendName: this.c_seclev_channel().extendName,
        value: [ this.c_seclev_channel() ]
      }));
    }

    if (this.is_site_pkg_book()) {
      if (this.is_site_pkg_book().extendName === 'is_site_pkg_book') {
        value.filter.items = value.filter.items.concat(new FilterItem({
          extendName: 'is_site_pkg',
          value: [{ value: '站点拼车单', key: '1'} ]
        }));
      }
      
      value.filter.items = value.filter.items.concat(new FilterItem({
        extendName: this.is_site_pkg_book().extendName,
        value: [ this.is_site_pkg_book() ]
      }));
    }
    
    if (!this.formService.hasDateCompare()) {
      value.dimensions = value.dimensions.concat(new DimensionItem({
        extendName: 'dt',
        predefineCompareType: [this.predefineCompareType()]
      }))
    }

    const nonsupport = value.filter.items.some(item => {
      return item.value.some(sub => {
        return (
          sub.value      === '外输渠道' ||
          sub?.relValue  === '外输渠道' ||
          sub.extendName === 'c_ord_type' ||
          sub.extendName === 'is_site_pkg' ||
          sub.extendName === 'is_site_pkg_book'
        )
      })
    })

    value.metrics = this.service.config().realTimeArea.metrics.filter(metric => {
      if (nonsupport && /_uv$/.test(metric.extendName)) {
        return false;
      }
      return true;
    }); // 查询累计数据需要全部指标，如果筛选条件中包含DAU不支持的筛选项，则排出_uv相关指标

    trace('[实时区域-全国]', value.filter.items);

    return value;
  }


  private _value() {
    const value = this.formService.value();

    value.dimensions = [];

    if (this.service.deadline()) {
      const time = this.service.deadline();

      value.filter.items = value.filter.items.concat([
        new FilterItem({
          extendName: this.service.config().realTimeArea.minuteExtendName,
          value: [ new FilterItemValue(time, time) ]
        }),
        new FilterItem({
          extendName: 'one_h_time_slice',
          condition: '<=',
          value: [ new FilterItemValue('24:00:00', '24:00:00') ]
        })
      ]);
    }

    if (this.order_type()) {
      value.filter.items = value.filter.items.concat(new FilterItem({
        extendName: this.order_type().extendName,
        value: [ this.order_type() ]
      }));
    }
    
    if (this.c_firlev_channel()) {
      value.filter.items = value.filter.items.concat(new FilterItem({
        extendName: this.c_firlev_channel().extendName,
        value: [ this.c_firlev_channel() ]
      }));
    }

    if (this.c_seclev_channel()) {
      value.filter.items = value.filter.items.concat(new FilterItem({
        extendName: this.c_seclev_channel().extendName,
        value: [ this.c_seclev_channel() ]
      }));
    }

    if (this.is_site_pkg_book()) {
      if (this.is_site_pkg_book().extendName === 'is_site_pkg_book') {
        value.filter.items = value.filter.items.concat(new FilterItem({
          extendName: 'is_site_pkg',
          value: [{ value: '站点拼车单', key: '1'} ]
        }));
      }
      
      value.filter.items = value.filter.items.concat(new FilterItem({
        extendName: this.is_site_pkg_book().extendName,
        value: [ this.is_site_pkg_book() ]
      }));
    }

    if (this.dimension()) {
      const extendName = this.dimension();
      value.dimensions = value.dimensions.concat(new DimensionItem({
        extendName
      }));
      
      if (!['city_bio_region', 'province_name'].includes(extendName)) {
        value.dimensions = value.dimensions.concat(new DimensionItem({
          extendName: this.conditionKey()
        }))
      }
    }
    
    if (!this.formService.hasDateCompare()) {
      value.dimensions = value.dimensions.concat(new DimensionItem({
        extendName: 'dt',
        predefineCompareType: [this.predefineCompareType()]
      }))
    }


    const nonsupport = value.filter.items.some(item => {
      return item.value.some(sub => {
        return (
          sub.value      === '外输渠道' ||
          sub?.relValue  === '外输渠道' ||
          sub.extendName === 'c_ord_type' ||
          sub.extendName === 'is_site_pkg' ||
          sub.extendName === 'is_site_pkg_book'
        )
      })
    })

    value.metrics = this.service.config().realTimeArea.metrics.filter(metric => {
      if (nonsupport && /_uv$/.test(metric.extendName)) {
        return false;
      }
      return true;
    }); // 查询累计数据需要全部指标，如果筛选条件中包含DAU不支持的筛选项，则排出_uv相关指标

    return value;
  }


  reset(): void {
    this.searchValue.set(null);
    this.search();
  }


  search(): void {
    const key = this.dimension();
    const list = this.searchPipe.transform(this.listOfData(), this.searchValue(), key);

    this.visible.set(false);
    this.listOfDisplayData.set(list);
  }


  drill(value: string) {
    trace(`埋点上报：实时大盘-城市/省份/区域点击时上报 -> ${value || '全国'}`);
    this.parent.area.set(value);
    this.buriedPointService.addStat('dida_dpm_caerus_R_fliter_Realtime', {
      page_name: this.page_name,
      fliter_options: value || '全国'
    })
  }


  @SwitchMap()
  query() {
    const body = this._value();

    body.dataFillConfig = {
      open: 0,
      fillRangeType: 1,
      fillValue: 1,
    };

    this.querying.set(true);
    this.errorMessage.set(null);
    this.listOfData.set([]);
    this.listOfDisplayData.set([]);
    this.firstRowData.set([]);

    return forkJoin([
      this.queryEngineApiService.search(this.countryBody(), 'real-time-area-country'),
      this.queryEngineApiService.search(body, 'real-time-area')
    ]).pipe(
      finalize(() => this.querying.set(false))
    ).subscribe(([countryRes, res]) => {
      if (res.status !== '00000') {
        this.errorMessage.set(res.message);
      }

      if (countryRes.data && res.data) {
        const data = res.data.data;
        this.listOfData.set(data);
        this.listOfDisplayData.set(data as any);
        this.firstRowData.set(countryRes.data.data[0]);
      }
    })
  }
  
}
