<div class="px-5 mt-5">
  <header class="relative flex items-center gap-x-2 px-5 h-12 border-l-4 border-emerald-500 bg-linear-to-r from-neutral-300/40 via-white via-20%">
    <div class="flex items-center gap-x-2">
      <span class="text-lg">实时区域</span>
    </div>
  </header>

  <div class="flex items-center flex-wrap gap-5 p-5">
    @if (!formService.hasDateCompare()) {
      <div class="flex items-center">
        <label class="inline-flex items-center font-bold leading-0 whitespace-nowrap">对比日期：</label>
        <app-radio-group class="relative flex gap-1" [(ngModel)]="predefineCompareType">
          <app-radio class="tag-radio-new" activeClass="active" value="dt">1日前</app-radio>
          <app-radio class="tag-radio-new" activeClass="active" value="yw">7日前</app-radio>
          <app-radio-thumb class="rounded-xs bg-primary" />
        </app-radio-group>
      </div>
    }

    <div class="flex items-center">
      <label class="inline-flex items-center font-bold leading-0 whitespace-nowrap">区域粒度：</label>
      <app-radio-group class="relative flex gap-1" [(ngModel)]="dimension">
        <app-radio class="tag-radio" activeClass="active" [disabled]="regionDisabled()"   value="city_bio_region">按区域</app-radio>
        <app-radio class="tag-radio" activeClass="active" [disabled]="provinceDisabled()" value="province_name">按省份</app-radio>
        <app-radio class="tag-radio" activeClass="active"                                 value="city_name">按城市</app-radio>
      </app-radio-group>
    </div>
    
    <div class="flex items-center">
      <label class="inline-flex items-center font-bold leading-0 whitespace-nowrap">订单类型：</label>
      <app-radio-group class="relative flex gap-1" [(ngModel)]="order_type">
        <app-radio class="tag-radio-new" activeClass="active" [value]="null">全部</app-radio>
        @for(item of service.config()?.realTimeArea?.filter['c_ord_type']?.values; track item) {
          <app-radio class="tag-radio-new" activeClass="active" [value]="item">{{item.value}}</app-radio>
        }
        <app-radio-thumb class="rounded-xs bg-primary" />
      </app-radio-group>
    </div>

    <div class="flex items-center">
      <label class="inline-flex items-center font-bold leading-0 whitespace-nowrap">一级业务渠道：</label>
      <app-radio-group class="relative flex gap-1" [(ngModel)]="c_firlev_channel">
        <app-radio class="tag-radio-new" activeClass="active" [value]="null">全部</app-radio>
        @for(item of service.config()?.realTimeArea?.filter['c_firlev_channel']?.values; track item) {
          <app-radio class="tag-radio-new" activeClass="active" [value]="item">{{item.value}}</app-radio>
        }
        <app-radio-thumb class="rounded-xs bg-primary" />
      </app-radio-group>
    </div>

    <div class="flex items-center">
      <label class="inline-flex items-center font-bold leading-0 whitespace-nowrap">二级业务渠道：</label>
      <app-radio-group class="relative flex gap-1" [(ngModel)]="c_seclev_channel">
        <app-radio class="tag-radio" activeClass="active" [value]="null">全部</app-radio>
        @if (c_firlev_channel()?.value !== null) {
          @for(item of service.config()?.realTimeArea?.filter['c_seclev_channel']?.values; track item) {
            @if (c_firlev_channel()?.value === null || c_firlev_channel()?.value === item.relValue) {
              <app-radio class="tag-radio" activeClass="active" [value]="item">{{item.value}}</app-radio>
            }
          }
        }
      </app-radio-group>
    </div>

    <div class="flex items-center">
      <label class="inline-flex items-center font-bold leading-0 whitespace-nowrap">是否站点拼车：</label>
      <app-radio-group class="relative flex gap-1" [(ngModel)]="is_site_pkg_book">
        <app-radio class="tag-radio" activeClass="active" [value]="null">全部</app-radio>
        @if (c_firlev_channel()?.value !== '外输渠道') {
          @for(item of service.config()?.realTimeArea?.filter['is_site_pkg_book']?.values; track item) {
            @switch (item.value) {
              @case ('纯站点拼车单')   { <ng-container *ngTemplateOutlet="siteRadioTemplate; context:{ $implicit: item }"></ng-container> }
              @case ('站点拼车组合单') { <ng-container *ngTemplateOutlet="siteRadioTemplate; context:{ $implicit: item }"></ng-container> }
              @default { <app-radio class="tag-radio" activeClass="active" [value]="item">{{item.value}}</app-radio> }
            }
          }
        }
        <ng-template #siteRadioTemplate let-item>
          @let site_value = is_site_pkg_book()?.value;
  
          @if (site_value === '站点拼车单' || site_value === '纯站点拼车单' || site_value === '站点拼车组合单') {
            <app-radio class="tag-radio" activeClass="active" [value]="item">{{item.value}}</app-radio>
          }
        </ng-template>
      </app-radio-group>
    </div>
  </div>

  @if (errorMessage()) {
    <div class="flex items-center justify-center h-96 text-xs">{{errorMessage()}}</div>
  }
  @else {
    <nz-table #groupingTable [nzData]="listOfDisplayData()" [nzLoading]="querying()" nzBordered nzSize="middle" [nzScroll]="{ x: 'auto' }" [nzPageSize]="20" nzShowSizeChanger [nzShowTotal]="totalTemplate">
      <thead>
        <tr>
          <th nzLeft rowspan="2" nzWidth="120px">
            @switch (dimension()) {
              @case ('city_bio_region') { 区域 }
              @case ('province_name')   { 省份 }
              @case ('city_name')       { 城市 }
            }

            <nz-filter-trigger [(nzVisible)]="visible" [nzActive]="searchValue()?.length > 0" [nzDropdownMenu]="menu">
              <SearchIcon />
            </nz-filter-trigger>
    
            <nz-dropdown-menu #menu="nzDropdownMenu">
              <div class="ant-table-filter-dropdown">
                <div class="search-box">
                  <input type="text" nz-input autofocus placeholder="请输入" [(ngModel)]="searchValue" (keydown.Enter)="search()" />
                  <button nz-button nzSize="small" type="submit" nzType="primary" (click)="search()" class="search-button">搜索</button>
                  <button nz-button nzSize="small" type="reset" (click)="reset()">重置</button>
                </div>
              </div>
            </nz-dropdown-menu>
          </th>
          @if (dimension() === 'city_name') {
            <th rowspan="2" class="text-nowrap">实时天气</th>
          }
          @for (item of service.config()?.realTimeArea?.metrics; track $index) {
            <th
              colspan="2"
              nz-popover
              nzPopoverPlacement="top"
              class="text-nowrap"
              [nzPopoverMouseEnterDelay]="0.5"
              [nzPopoverTitle]="item?.aliasName"
              [nzPopoverContent]="item?.bizExpression"
            >{{item.showName}}</th>
          }
        </tr>
        <tr>
          @for (column of listOfColumns; track $index) {
            <th class="text-nowrap" [nzSortFn]="column.sortFn" [nzSortOrder]="$first ? 'descend' : null" nzWidth="140px">数值</th>
            <th class="text-nowrap" [nzSortFn]="column.diffSortFn" nzWidth="170px">涨跌幅</th>
          }
        </tr>
      </thead>
      <tbody>

        @if (firstRowData()) {
          <tr>
            <td nzLeft class="text-center">
              <a (click)="drill('全国')">全国</a>
            </td>
            @if (dimension() === 'city_name') {
              <td>-</td>
            }
            @for (metric of service.config()?.realTimeArea?.metrics; track $index) {
              @if (formService.hasDateCompare()) {
                @let key   = metric.extendName;
                @let diff  = key+'_DIFF';
                @let ratio = key+'_DIFF_RATIO';

                <td align="right">
                  <value-formatter [value]="firstRowData()[key]" />
                </td>
                <td align="right">
                  <value-formatter useColor [value]="firstRowData()[diff]" />
                  <value-formatter useColor showBracket useMultiplier="false" suffix="%" [value]="firstRowData()[ratio]" />
                </td>
              }
              @else {
                @let key = metric.extendName;
                @let diff  = key + ':'+predefineCompareType()+'_DIFF';
                @let ratio = key + ':'+predefineCompareType()+'_DIFF_RATIO';

                <td align="right" nzWidth="140px">
                  <value-formatter [value]="firstRowData()[key]" />
                </td>
                <td align="right">
                  <value-formatter useColor [value]="firstRowData()[diff]" />
                  <value-formatter useColor showBracket useMultiplier="false" suffix="%" [value]="firstRowData()[ratio]" />
                </td>
              }
            }
          </tr>
        }

        @for (item of groupingTable.data; track $index) {
          <tr>
            <td nzLeft class="text-center">
              <a (click)="drill(item[dimension()])">{{item[dimension()]}}</a>
            </td>
            @if (dimension() === 'city_name') {
              <td>
                <div class="inline-flex">
                  @for (icon of (item[conditionKey()] | split: ','); track $index) {
                    <img [title]="icon" src="assets/images/weather/{{icon || 'w44'}}.png" class="size-6 object-cover">
                  }
                </div>
              </td>
            }
            @for (metric of service.config()?.realTimeArea?.metrics; track $index) {
              @if (formService.hasDateCompare()) {
                @let key = metric.extendName;
                @let diff = key+'_DIFF';
                @let ratio = key+'_DIFF_RATIO';

                <td align="right">
                  <value-formatter [value]="item[key]" />
                </td>
                <td align="right">
                  <value-formatter useColor [value]="item[diff]" />
                  <value-formatter useColor showBracket useMultiplier="false" suffix="%" [value]="item[ratio]" />
                </td>
              }
              @else {
                @let key   = metric.extendName;
                @let diff  = key + ':'+predefineCompareType()+'_DIFF';
                @let ratio = key + ':'+predefineCompareType()+'_DIFF_RATIO';

                <td align="right">
                  <value-formatter [value]="item[key]" />
                </td>
                <td align="right">
                  <value-formatter useColor [value]="item[diff]" />
                  <value-formatter useColor showBracket useMultiplier="false" suffix="%" [value]="item[ratio]" />
                </td>
              }
            }
          </tr>
        }
      </tbody>
    </nz-table>

    <ng-template #totalTemplate let-total> 共 {{ total }} 条记录 </ng-template>
  }


  <div class="h-40"></div>
</div>
