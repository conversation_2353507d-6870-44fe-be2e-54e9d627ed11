import { DatePipe } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, Component, computed, DestroyRef, inject, signal } from '@angular/core';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { FormGroup, FormsModule } from '@angular/forms';
import { NzAnchorModule } from 'ng-zorro-antd/anchor';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { combineLatest, debounceTime, startWith, switchMap } from 'rxjs';
import { format } from 'date-fns';

import { trace } from '@common/const';
import { BuriedPointService } from '@common/service';
import { SwitchMap } from '@common/decorator';
import { PAGE_NAME, PageEnterLeaveDirective } from '@common/directive';
import { DimensionValueForm, FilterItemForm, QueryEngineFormService } from '@common/service/query-engine';
import { DimensionValueMenuVo } from '@api/caerus/model';
import { CaerusApiService } from '@api/caerus';
import { SkeletonComponent } from '@shared/components/skeleton';
import { AreaSelectComponent } from '@shared/components/area-select';
import { NavigationComponent } from '@shared/components/navigation';
import { SelfhelpBtnComponent } from '@shared/components/selfhelp-btn';
import { BackTopComponent } from '@shared/components/back-top';
import { DateCompareComponent, ProgressBarComponent } from './components';
import { MarketOverviewComponent } from './market-overview/market-overview.component';
import { RealTimeAreaComponent } from './real-time-area/real-time-area.component';
import { RealTimeService } from './real-time.service';


@Component({
  selector: 'app-real-time',
  templateUrl: './real-time.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: {
    'class': 'block'
  },
  hostDirectives: [
    PageEnterLeaveDirective,
  ],
  imports: [
    FormsModule,
    NzButtonModule,
    NzAnchorModule,
    NavigationComponent,
    AreaSelectComponent,
    DateCompareComponent,
    MarketOverviewComponent,
    RealTimeAreaComponent,
    SelfhelpBtnComponent,
    BackTopComponent,
    SkeletonComponent,
    ProgressBarComponent,
  ],
  providers: [
    { provide: PAGE_NAME, useValue: 'market-real-time' },
    QueryEngineFormService,
    RealTimeService,
    DatePipe,
  ],
})
export class RealTimeComponent implements AfterViewInit {
  
  readonly service = inject(RealTimeService);
  readonly apiService = inject(CaerusApiService);
  readonly formService = inject(QueryEngineFormService);
  readonly buriedPointService = inject(BuriedPointService);
  readonly page_name = inject(PAGE_NAME);
  readonly destroyRef = inject(DestroyRef);
  readonly datePipe = inject(DatePipe);

  querying = signal(false);
  area = signal(null);
  areaForm = new FormGroup(new FilterItemForm({}, new FormGroup(new DimensionValueForm())));
  areaForm$ = toSignal(this.areaForm.valueChanges);
  deadline = signal<string>(null);

  isToday = computed(() => {
    const today = format(new Date(), 'yyyy-MM-dd');
    if (this.formService.form$()) {
      const { startTime } = this.formService.form$().dt;

      return startTime === today;
    }
    return false;
  })


  ngAfterViewInit(): void {
    this.fetchConfig();
    this._subscribeToStartTimeChange();
    this._subscribeToFormChange();
  }


  private _subscribeToStartTimeChange() {
    const startTime = this.formService.form.controls.dt.controls.startTime;
    combineLatest([
      startTime.valueChanges.pipe(startWith(startTime.value)),
      this.service.source$.pipe(
        switchMap(() => this.service.interval$.pipe(startWith(0))),
      )
    ])
    .pipe(
      takeUntilDestroyed(this.destroyRef)
    ).subscribe(() => {
      this.updateDeadline();
    })
  }


  private _subscribeToFormChange() {
    this.formService.form.valueChanges.pipe(
      debounceTime(300),
      takeUntilDestroyed(this.destroyRef)
    ).subscribe((value) => {
      trace(`埋点上报：实时大盘-公共筛选项栏点击`, {
        dt: value.dt.startTime,
        compareDt: value.compareDt?.startTime,
        area: value.filter.items[0]?.value[0]?.value || '全国'
      });
      
      this.buriedPointService.addStat('dida_dpm_caerus_L_fliter_Realtime', {
        page_name: this.page_name,
        fliter_position: 2,
        fliter_options: {
          dt: value.dt.startTime,
          compareDt: value.compareDt?.startTime,
          area: value.filter.items[0]?.value[0]?.value || '全国'
        }
      })
    })
  }


  private fetchConfig() {
    this.apiService.fetchConfig('real_time_overview').subscribe(res => {
      if (res.data) {
        this.service.config.set(res.data);
      }
    });
  }


  @SwitchMap()
  updateDeadline(date: string = this.formService.form.controls.dt.controls.startTime.value) {
    return this.apiService.fetchRealTimeDeadLine({ date }).subscribe(res => {
      if (res.data) {
        const time = this.isToday() ? res.data : '00:00:00'
        this.deadline.set(time);
        this.service.deadline.set(time);
      }
    })
  }


  onAreaChange(area: DimensionValueMenuVo) {
    const { filterItems } = this.formService;

    if (area) {
      const { extendName, key, value } = area;

      if (!filterItems.controls.includes(this.areaForm)) {
        filterItems.push(this.areaForm);
      }
      
      this.areaForm.patchValue({ extendName, value: [{key, value}] });
    } else {
      this.areaForm.patchValue({
        extendName: null
      })
      // this.areaForm.controls.extendName.patchValue(null);
      // this.areaForm.controls.value.clear();
      filterItems.clear();
    }
  }


  async reset() {
    this.service.query();
    this.area.set('全国');
  }

}
