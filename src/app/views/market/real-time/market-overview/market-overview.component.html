<div class="px-5 mt-5">
  <header class="relative flex items-center gap-x-2 px-5 h-12 border-l-4 border-emerald-500 bg-linear-to-r from-neutral-300/40 via-white via-20%">
    <div class="flex items-center gap-x-2">
      <span class="text-lg">实时大盘概览</span>
    </div>
  </header>

  <div class="flex items-center gap-5 flex-wrap p-5 pb-0">
    <div class="flex items-center">
      <label class="inline-flex items-center font-bold leading-0 whitespace-nowrap">订单类型：</label>
      <app-radio-group class="relative flex gap-1" [(ngModel)]="order_type">
        <app-radio class="tag-radio-new" activeClass="active" [value]="null">全部</app-radio>
        @for(item of service.config()?.overview?.filter['c_ord_type']?.values; track item) {
          <app-radio class="tag-radio-new" activeClass="active" [value]="item">{{item.value}}</app-radio>
        }
        <app-radio-thumb class="rounded-xs bg-primary" />
      </app-radio-group>
    </div>

    <div class="flex items-center">
      <label class="inline-flex items-center font-bold leading-0 whitespace-nowrap">一级业务渠道：</label>
      <app-radio-group class="relative flex gap-1" [(ngModel)]="c_firlev_channel">
        <app-radio class="tag-radio-new" activeClass="active" [value]="null">全部</app-radio>
          @for(item of service.config()?.overview?.filter['c_firlev_channel']?.values; track item) {
            <app-radio class="tag-radio-new" activeClass="active" [value]="item">{{item.value}}</app-radio>
          }
        <app-radio-thumb class="rounded-xs bg-primary" />
      </app-radio-group>
    </div>

    <div class="flex items-center">
      <label class="inline-flex items-center font-bold leading-0 whitespace-nowrap">二级业务渠道：</label>
      <app-radio-group class="relative flex gap-1" [(ngModel)]="c_seclev_channel">
        <app-radio class="tag-radio" activeClass="active" [value]="null">全部</app-radio>
        @if (c_firlev_channel()?.value !== null) {
          @for(item of service.config()?.overview?.filter['c_seclev_channel']?.values; track item) {
            @if (c_firlev_channel()?.value === null || c_firlev_channel()?.value === item.relValue) {
              <app-radio class="tag-radio" activeClass="active" [value]="item">{{item.value}}</app-radio>
            }
          }
        }
      </app-radio-group>
    </div>

    <div class="flex items-center">
      <label class="inline-flex items-center font-bold leading-0 whitespace-nowrap">是否站点拼车：</label>
      <app-radio-group class="relative flex gap-1" [(ngModel)]="is_site_pkg_book">
        <app-radio class="tag-radio" activeClass="active" [value]="null">全部</app-radio>
        @if (c_firlev_channel()?.value !== '外输渠道') {
          @for(item of service.config()?.overview?.filter['is_site_pkg_book']?.values; track item) {
            @switch (item.value) {
              @case ('纯站点拼车单')   { <ng-container *ngTemplateOutlet="siteRadioTemplate; context:{ $implicit: item }"></ng-container> }
              @case ('站点拼车组合单') { <ng-container *ngTemplateOutlet="siteRadioTemplate; context:{ $implicit: item }"></ng-container> }
              @default { <app-radio class="tag-radio" activeClass="active" [value]="item">{{item.value}}</app-radio> }
            }
          }
        }
        <!-- <app-radio-thumb class="rounded-xs bg-primary" /> -->

        <ng-template #siteRadioTemplate let-item>
          @let site_value = is_site_pkg_book()?.value;
  
          @if (site_value === '站点拼车单' || site_value === '纯站点拼车单' || site_value === '站点拼车组合单') {
            <app-radio class="tag-radio" activeClass="active" [value]="item">{{item.value}}</app-radio>
          }
        </ng-template>
      </app-radio-group>
    </div>
  </div>

  <div class="px-5 text-xs text-neutral-400 my-3">
    说明：当日累计指标与指定日期数值进行比较时，比较的是指定日期相同截止时间的数据
  </div>

  
  <!-- 曝光埋点 appIntersection (visible)="onVisible(1)" -->
  <div>
    @if (metricErrorMessage()) {
      <div class="flex items-center justify-center m-auto h-[140px] text-xs">{{metricErrorMessage()}}</div>
    }
    @else {
      <app-radio-group class="flex gap-x-3" [(ngModel)]="metric" (ngModelChange)="onMetricChange($event); onMetricsChange()" >
        @for (item of service.config()?.overview?.accumulateData?.metrics; track item) {
          @let nonsupport = (
            (
              order_type()       !== null || 
              is_site_pkg_book() !== null ||
              c_firlev_channel()?.value    === '外输渠道' || 
              c_seclev_channel()?.relValue === '外输渠道'
            ) && 
            (item.key === 'c_active_pass_uv' || item.key === 'c_active_driver_uv')
          );
          <app-radio
            class="radio flex-1 min-w-0 min-h-[112px] overflow-hidden"
            activeClass="active"
            [value]="item"
            [disabled]="nonsupport"
            nz-popover
            nzPopoverPlacement="topLeft"
            [nzPopoverMouseEnterDelay]="0.5"
            [nzPopoverTitle]="metricsTitleTemplate"
            [nzPopoverContent]="item?.bizExpression"
            (mouseup)="handlerIndicatorCardClick(item)"
          >
            <div class="flex flex-col gap-y-1.5">
              <ng-template #metricsTitleTemplate>
                {{item?.showName}} <span class="text-xs opacity-30 px-1">({{item?.aliasName}})</span>
              </ng-template>
              <div class="truncate text-sm">
                <span class="inline-block scale-95 origin-left">{{item.showName}}</span>
              </div>
              <div class="font-semibold text-2xl leading-none">
                {{(item?.value === null || item?.value === undefined) || nonsupport ? '-' : (+item?.value | increment | async | number: '1.0-0')}}
              </div>
              <div class="flex flex-col gap-y-0.5 whitespace-nowrap">
                @if (formService.hasDateCompare()) {
                  <div>
                    <span class="text-sm">较对比日: </span>
                    <value-formatter useColor [value]="item?.diff?.value" />
                    <value-formatter showBracket useColor useMultiplier="false" suffix="%" [value]="item?.diff?.ratio" />
                  </div>
                }
                @else {
                  <div>
                    <span class="text-sm">较1日前: </span>
                    <value-formatter useColor [value]="item?.dt?.DIFF" />
                    <value-formatter showBracket useColor useMultiplier="false" suffix="%" [value]="item?.dt?.DIFF_RATIO" />
                  </div>

                  <div>
                    <span class="text-sm">较7日前: </span>
                    <value-formatter useColor [value]="item?.yw?.DIFF" />
                    <value-formatter showBracket useColor useMultiplier="false" suffix="%" [value]="item?.yw?.DIFF_RATIO" />
                  </div>
                }
              </div>
              <SuccessFillIcon *radioChecked class="absolute right-3 text-blue-600 text-xl" />
            </div>
          </app-radio>
        }
      </app-radio-group>
    }
  </div>

  <div class="flex items-center gap-x-5 p-5 pb-0">
    <div class="flex items-center">
      <label class="inline-flex items-center font-bold leading-0 whitespace-nowrap">数据统计粒度：</label>
      <app-radio-group class="relative flex gap-1" [(ngModel)]="dimension">
        @for(item of service.config()?.overview?.realTimeData?.dimensions; track $index) {
          <app-radio class="tag-radio-new" activeClass="active" [value]="item">{{item.showName}}</app-radio>
        }
        <app-radio-thumb class="rounded-xs bg-primary" />
      </app-radio-group>
    </div>

    <div class="flex items-center">
      <label class="inline-flex items-center font-bold leading-0 whitespace-nowrap">选择指标对比：</label>
      <div>
        @for(item of service.config()?.overview?.realTimeData?.metrics; track $index) {
          @let nonsupport = (
            (
              order_type()       !== null || 
              is_site_pkg_book() !== null ||
              c_firlev_channel()?.value    === '外输渠道' || 
              c_seclev_channel()?.relValue === '外输渠道'
            ) && 
            (item.key === 'c_active_pass_uv' || item.key === 'c_active_driver_uv')
          );
          <ng-template #metricsTitleTemplate>
            {{item?.showName}} <span class="text-xs opacity-30 px-1">({{item?.aliasName}})</span>
          </ng-template>
          <label
            nz-popover
            nz-checkbox
            class="ml-0! text-xs!"
            [nzDisabled]="nonsupport"
            [nzPopoverMouseEnterDelay]="0.5"
            [nzPopoverTitle]="metricsTitleTemplate"
            [nzPopoverContent]="item?.bizExpression"
            [(ngModel)]="item.checked"
            (ngModelChange)="onMetricsChange()"
          >{{item.showName}}</label>
        }
      </div>
    </div>
  </div>

  <div class="px-5 text-xs text-neutral-400 my-3">
    说明：点击图例默认单选，长按shift键点击可多选。
  </div>

  <div #metricSelect class="flex flex-col pb-5 gap-y-2.5">
    <div class="relative flex items-center justify-center h-100">
      <div class="absolute inset-y-0 inset-x-4 flex">
        @if (errorMessage()) {
          <div class="flex items-center justify-center m-auto h-96 text-xs">{{errorMessage()}}</div>
        } @else {
          @if (loading()) {
            <app-line-spin class="m-auto" />
          }
          @else {
            @if (option()) {
              <app-graph [options]="option()" showAnnotations showAvgPlotLines noHandleColors />
            }
          }
        }
      </div>
    </div>
    <!-- <pre>{{metric() | json}}</pre> -->
  </div>
</div>