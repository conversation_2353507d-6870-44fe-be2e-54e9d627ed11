import { <PERSON><PERSON><PERSON><PERSON><PERSON>, DecimalPipe, <PERSON>sonPip<PERSON>, NgTemplateOutlet } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, computed, DestroyRef, effect, ElementRef, inject, signal, viewChild } from '@angular/core';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { combineLatest, debounceTime, finalize, forkJoin, map, Observable, of, startWith, switchMap } from 'rxjs';
import { NzPopoverModule } from 'ng-zorro-antd/popover';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { addDays, format } from 'date-fns';

import { trace } from '@common/const';
import { SwitchMap } from '@common/decorator';
import { QueryEngineFormService } from '@common/service/query-engine';
import { CaerusApiService } from '@api/caerus';
import { RadioModule } from '@shared/modules/headless';
import { IconSuccessFillComponent } from '@shared/modules/icons';
import { IncrementPipe } from '@shared/pipes/increment';
import { QueryEngineApiService } from '@api/query-engine';
import { BaseHighCharts } from '@common/chart/highcharts';
import { GraphComponent } from '@shared/components/graph';
import { CityWeatherVo } from '@api/caerus/model/city-weather.model';
import { LineSpinComponent } from '@shared/components/line-spin';
import { BuriedPointService, LegendControlService, LegendItemClickHandler } from '@common/service';
import { ValueFormatter } from '@shared/components/value-formatter';
import { FillDataOfTrend } from '@common/class';
import { PAGE_NAME } from '@common/directive';
import { RealTimeService } from '../real-time.service';
import { RealTimeComponent } from '../real-time.component';
import { RealTimeChart } from '../real-time-chart';


interface MetricsConfig {
  aliasName: string;
  extendName: string;
  bizExpression: string;
  key: string;
}


export class FilterItemValue {
  constructor(
    /** 编码【数字或者英文字符】 */
    public key: string,
    /** 码值【中文名称可用来展示】 */
    public value: string,
  ) {}
}

class FilterItem {
  /** 运算类型 逻辑运算 1,条件运算 2 */
  conditionType = 2;
  /** 运算标识;条件运算公式 [>,<,=,>=,<=,!=,not null,in],逻辑运算公式 [and,or] */
  condition = '=';
  /** 指标id/维度id */
  id = null;
  /** 维度英文名 */
  extendName: string;
  value: FilterItemValue[];
  valueType = null;
  subFilter: FilterItem[];

  constructor(props?: Partial<FilterItem>) {
    if (props) {
      Object.assign(this, { ...props });
    }
  }
}

class MetricItem {
  id = null;
  type = 3;
  extendName: string
  customType = null;
  proportionDimension = [];
  constructor(props?: Partial<MetricItem>) {
    if (props) {
      Object.assign(this, { ...props });
    }
  }
}

class DimensionItem {
  id = null;
  extendName: string;
  predefineCompareType: string[];
  constructor(props?: Partial<DimensionItem>) {
    if (props) {
      Object.assign(this, { ...props });
    }
  }
}


@Component({
  selector: 'app-market-overview',
  templateUrl: './market-overview.component.html',
  styleUrl: './market-overview.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    // JsonPipe,
    AsyncPipe,
    DecimalPipe,
    FormsModule,
    NgTemplateOutlet,
    NzCheckboxModule,
    NzPopoverModule,
    RadioModule,
    GraphComponent,
    LineSpinComponent,
    ValueFormatter,
    IconSuccessFillComponent,
    IncrementPipe,
  ],
  providers: [
    LegendControlService,
  ]
})
export class MarketOverviewComponent implements AfterViewInit {

  readonly cdr = inject(ChangeDetectorRef);
  readonly service = inject(RealTimeService);
  readonly apiService = inject(CaerusApiService);
  readonly queryEngineApiService = inject(QueryEngineApiService);
  readonly formService = inject(QueryEngineFormService);
  readonly buriedPointService = inject(BuriedPointService);
  readonly page_name = inject(PAGE_NAME);
  readonly legendControlService = inject(LegendControlService);
  readonly destroyRef = inject(DestroyRef);
  readonly root = inject(RealTimeComponent);
  
  errorMessage = signal(null);
  metricErrorMessage = signal(null);
  anchor = viewChild<ElementRef>('metricSelect');
  metric = signal(null);
  metrics = signal<MetricItem[]>(null);
  dimension = signal<{ extendName: string }>(null);
  order_type       = signal<{ key: string, value: string, extendName: string }>(null);
  c_firlev_channel = signal<{ key: string, value: string, extendName: string }>(null);
  c_seclev_channel = signal<{ key: string, value: string, extendName: string, relValue: string }>(null);
  is_site_pkg_book = signal<{ key: string, value: string, extendName: string, }>(null);
  option = signal<BaseHighCharts>(null);
  weathers = signal<any[]>([]);
  loading = signal(false);

  order_type$ = toObservable(this.order_type);
  c_firlev_channel$ = toObservable(this.c_firlev_channel);
  c_seclev_channel$ = toObservable(this.c_seclev_channel);
  is_site_pkg_book$ = toObservable(this.is_site_pkg_book);
  dimension$ = toObservable(this.dimension);
  metrics$ = toObservable(this.metrics);

  isToday = computed(() => {
    const today = format(new Date(), 'yyyy-MM-dd');
    if (this.formService.form$()) {
      const { startTime } = this.formService.form$().dt;

      return startTime === today;
    }
    return false;
  })

  constructor() {
    effect(() => {
      if (this.service.config()) {
        const { metrics } = this.service.config().overview.accumulateData;
        const { dimensions }  = this.service.config().overview.realTimeData;

        this.metric.set(metrics[0]);
        this.dimension.set(dimensions[1]);
      }
    })

    effect(() => {
      const metric = this.metric();
      const order_type = this.order_type();
      const is_site_pkg_book = this.is_site_pkg_book();
      const c_firlev_channel = this.c_firlev_channel();
      const c_seclev_channel = this.c_seclev_channel();

      if (
        metric && 
        this.service.config()
      ) {
        if (
          (
            order_type !== null || 
            is_site_pkg_book !== null ||
            c_firlev_channel?.value === '外输渠道' ||
            c_seclev_channel?.relValue === '外输渠道'
          ) &&
          ['c_active_pass_uv', 'c_active_driver_uv'].includes(metric.key)
        ) {
          const { metrics } = this.service.config().overview.accumulateData;
          this.metric.set(metrics[0]);
        }

        if (
          order_type !== null || 
          is_site_pkg_book !== null ||
          c_firlev_channel?.value === '外输渠道' ||
          c_seclev_channel?.relValue === '外输渠道'
        ) {
          const keys = ['realtime_pot_c_active_pass_uv', 'realtime_pot_c_active_driver_uv'];

          this.service.updateMetricsCheckedState(keys, false);
          this.onMetricsChange();
        }
      }
    })

    effect(() => {
      trace(`埋点上报：实时大盘-局部筛选项被点击时上报`, {
        order_type: this.order_type()?.value || '全部',
        c_firlev_channel: this.c_firlev_channel()?.value || '全部',
      });
      
      this.buriedPointService.addStat('dida_dpm_caerus_L_fliter_Realtime', {
        page_name: this.page_name,
        fliter_position: 1,
        fliter_options: {
          order_type: this.order_type()?.value || '全部',
          c_firlev_channel: this.c_firlev_channel()?.value || '全部',
        }
      })
    })

    effect(() => {
      trace(`埋点上报：实时大盘-图表选项点击时上报`, {
        metrics: this.metrics(),
        dimension: this.dimension(),
      });

      this.buriedPointService.addStat('dida_dpm_caerus_g_fliter_Realtime', {
        page_name: this.page_name,
        fliter_options: {
          metrics: this.metrics(),
          dimension: this.dimension(),
        }
      })
    })

    effect(() => {
      const c_firlev_channel = this.c_firlev_channel();
      const c_seclev_channel = this.c_seclev_channel();

      if (
        c_firlev_channel === null ||
        c_firlev_channel !== null && c_seclev_channel !== null && c_seclev_channel.relValue !== c_firlev_channel.value
      ) {
        this.c_seclev_channel.set(null);
      }

      if (c_firlev_channel?.value === '外输渠道') {
        this.is_site_pkg_book.set(null);
      }
    })
  }
  

  ngAfterViewInit(): void {
    combineLatest([
      this.service.deadline$,
      this.formService.form.valueChanges.pipe(startWith(this.formService.form.value)),
      this.service.config$,
      this.order_type$,
      this.c_firlev_channel$,
      this.c_seclev_channel$,
      this.is_site_pkg_book$,
      this.service.source$.pipe(
        switchMap(() => this.service.interval$.pipe(startWith(0))),
      )
    ]).pipe(
      debounceTime(300),
      takeUntilDestroyed(this.destroyRef)
    ).subscribe(([a, b, c]) => {
      if ([a, b, c].every(item => item !== null)) {
        // trace('[input]', [a, b]);
        this.query();
        this.service.restore();
      }
    })


    combineLatest([
      this.dimension$,
      this.metrics$,
      this.service.deadline$,
      this.service.config$,
      this.formService.form.valueChanges.pipe(startWith(this.formService.form.value)),
      this.order_type$,
      this.c_firlev_channel$,
      this.c_seclev_channel$,
      this.is_site_pkg_book$,
      this.service.source$.pipe(
        switchMap(() => this.service.interval$.pipe(startWith(0))),
      )
    ]).pipe(
      debounceTime(300),
      takeUntilDestroyed(this.destroyRef)
    ).subscribe(([a, b, c, d, e]) => {
      if ([a, b, c, d, e].every(item => item !== null)) {
        // trace('[input]', [a, b, c, d, e]);
        this.queryChartData();
        this.service.restore();
      }
    })
  }


  /**
   * 实时大盘指标卡点击埋点
   * @param item 
   */
  handlerIndicatorCardClick(item: MetricsConfig | any) {
    trace(`埋点上报: 实时大盘指标卡点击 -> ${item}`);
    this.buriedPointService.addStat('dida_dpm_caerus_card_click_Realtime', {
      page_name: this.page_name,
      indicator_name: item.aliasName
    })
  }


  onMetricChange({ key }: any) {
    this.service.config.update(config => {
      config.overview.realTimeData.metrics = config.overview.realTimeData.metrics.map(item => {
        item.checked = item.key === key;
        return item;
      })
      return config;
    })
  }


  onMetricsChange() {
    const metrics = this.service.config()?.overview?.realTimeData.metrics
      .filter(item => item.checked)
      .map(item => new MetricItem({
        extendName: item.extendName
      }));

    this.metrics.set(metrics);
  }


  private _value() {
    const value = this.formService.value();
    
    value.dimensions = [];

    if (this.order_type()) {
      value.filter.items = value.filter.items.concat(new FilterItem({
        extendName: this.order_type().extendName,
        value: [ this.order_type() ]
      }));
    }
    
    if (this.c_firlev_channel()) {
      value.filter.items = value.filter.items.concat(new FilterItem({
        extendName: this.c_firlev_channel().extendName,
        value: [ this.c_firlev_channel() ]
      }));
    }

    if (this.c_seclev_channel()) {
      value.filter.items = value.filter.items.concat(new FilterItem({
        extendName: this.c_seclev_channel().extendName,
        value: [ this.c_seclev_channel() ]
      }));
    }

    if (this.is_site_pkg_book()) {
      if (this.is_site_pkg_book().extendName === 'is_site_pkg_book') {
        value.filter.items = value.filter.items.concat(new FilterItem({
          extendName: 'is_site_pkg',
          value: [{ value: '站点拼车单', key: '1'} ]
        }));
      }
      
      value.filter.items = value.filter.items.concat(new FilterItem({
        extendName: this.is_site_pkg_book().extendName,
        value: [ this.is_site_pkg_book() ]
      }));
    }

    return value;
  }


  fetchRealTimeCityWeather(): Observable<CityWeatherVo[]> {
    const { dt: { startTime }, compareDt, filter: { items } } = this.formService.value();
    const dtList = [startTime];
    const beforeDate = format(addDays(new Date(startTime.replace(/-/g, '/')), -1), 'yyyy-MM-dd');
    const beforeWeekDate = format(addDays(new Date(startTime.replace(/-/g, '/')), -7), 'yyyy-MM-dd');
    const cityName = items[0]?.value[0]?.value;

    if (compareDt) {
      dtList.push(compareDt.startTime);
    } else {
      dtList.push(beforeDate);
      dtList.push(beforeWeekDate);
    }

    if (cityName) {
      return this.apiService.fetchRealTimeCityWeather({ cityName, dtList }).pipe(
        map(res => res.data)
      );
    }

    return of([]);
  }


  /**
   * 查询累计数据
   */
  @SwitchMap()
  query() {
    const body = this._value();
    const nonsupport = body.filter.items.some(item => {
      return item.value.some(sub => {
        return (
          sub.value      === '外输渠道' ||
          sub?.relValue  === '外输渠道' ||
          sub.extendName === 'c_ord_type' ||
          sub.extendName === 'is_site_pkg' ||
          sub.extendName === 'is_site_pkg_book'
        )
      })
    })

    body.dimensions = [];
    body.metrics = this.service.config().overview.accumulateData.metrics.filter(metric => {
      if (nonsupport && /_uv$/.test(metric.extendName)) {
        return false;
      }
      return true;
    }); // 查询累计数据需要全部指标，如果筛选条件中包含DAU不支持的筛选项，则排出_uv相关指标

    /**
     * 培文说：累计指标卡如果选择了对比日期不需要传predefineCompareType
     */
    if (!this.formService.hasDateCompare()) {
      body.dimensions = body.dimensions.concat([
        new DimensionItem({
          extendName: 'dt',
          predefineCompareType: ['dt', 'yw']
        }),
      ]);
    }

    if (this.service.deadline()) {
      const time = this.service.deadline();

      body.filter.items = body.filter.items.concat([
        new FilterItem({
          extendName: this.service.config().overview.accumulateData.minuteExtendName,
          value: [ new FilterItemValue(time, time) ]
        }),
        new FilterItem({
          extendName: 'one_h_time_slice',
          condition: '<=',
          value: [ new FilterItemValue('24:00:00', '24:00:00') ]
        })
      ]);
    }

    this.metricErrorMessage.set(null);

    return this.queryEngineApiService.search(body, 'real-time-overview').subscribe(res => {
      if (res.status !== '00000') {
        this.metricErrorMessage.set(res.message);
      }
      if (res.data) {        
        this.service.updateMetricsValue(res.data.data[0] || {});
        this.cdr.markForCheck();
      }
    })
  }


  /**
   * 查询图表数据
   */
  @SwitchMap()
  queryChartData() {
    const body = this._value();
    const time = this.isToday() ? this.service.deadline() : '24:00:00';
    const { extendName: time_interval } = this.dimension();
    const legendItemClick = LegendItemClickHandler(this.legendControlService);
    const date = body.dt.startTime.replace(/-/g, '/');
    const compareDate  = body.compareDt?.startTime?.replace(/-/g, '/');
    const todayStr     = format(addDays(new Date(date), -0), 'yyyy-MM-dd');
    const yesterdayStr = format(addDays(new Date(date), -1), 'yyyy-MM-dd');
    const lastweekStr  = format(addDays(new Date(date), -7), 'yyyy-MM-dd');
    
    body.metrics = body.metrics.concat(this.metrics()); // 查询图表数据需要已选中的指标
    body.dimensions = body.dimensions.concat([
      new DimensionItem({ extendName: 'dt' }),
      new DimensionItem({ extendName: time_interval })
    ]);

    body.dataFillConfig = {
      open: 1,
      fillRangeType: 2,
      fillValue: 0,
    };

    if (body.compareDt) {
      const compareDateStr  = format(new Date(compareDate), 'yyyy-MM-dd');

      body.filter.items = body.filter.items.concat(new FilterItem({
        conditionType: 1,
        condition: 'OR',
        subFilter: [
          new FilterItem({
            conditionType: 1,
            condition: 'AND',
            subFilter: [
              new FilterItem({
                conditionType: 2,
                condition: '=',
                extendName: 'dt',
                value: [ new FilterItemValue(todayStr, todayStr) ],
                valueType: null,
              }),
              new FilterItem({
                conditionType: 2,
                condition: '<=',
                extendName: this.dimension().extendName,
                value: [ new FilterItemValue(time, time) ],
                valueType: null,
              }),
            ]
          }),
          new FilterItem({
            conditionType: 2,
            condition: '=',
            extendName: 'dt',
            value: [ new FilterItemValue(compareDateStr, compareDateStr) ],
            valueType: null,
          }),
        ]
      }));
    } else {
      const subFilter = [
        new FilterItem({
          conditionType: 1,
          condition: 'AND',
          subFilter: [
            new FilterItem({
              conditionType: 2,
              condition: '=',
              extendName: 'dt',
              value: [ new FilterItemValue(todayStr, todayStr) ],
              valueType: null,
            }),
            new FilterItem({
              conditionType: 2,
              condition: '<=',
              extendName: this.dimension().extendName,
              value: [ new FilterItemValue(time, time) ],
              valueType: null,
            }),
          ]
        })
      ];

      /**
       * 产品要求：
       * 如果指标选中数量大于1，则不对比1日前、7日前
       */
      if (body.metrics.length < 2) {
        subFilter.push(new FilterItem({
          conditionType: 2,
          condition: '=',
          extendName: 'dt',
          value: [ new FilterItemValue(yesterdayStr, yesterdayStr) ],
          valueType: null,
        }));

        subFilter.push(new FilterItem({
          conditionType: 2,
          condition: '=',
          extendName: 'dt',
          value: [ new FilterItemValue(lastweekStr, lastweekStr) ],
          valueType: null,
        }))
      }
      
      body.dt.startTime = lastweekStr;
      body.filter.items = body.filter.items.concat(new FilterItem({
        conditionType: 1,
        condition: 'OR',
        subFilter
      }));
    }
    
    this.option.set(null);
    this.errorMessage.set(null);
    this.loading.set(true);
    this.legendControlService.reset();

    return forkJoin([
      this.fetchRealTimeCityWeather(),
      this.queryEngineApiService.search(body, 'real-time-chart'),
    ]).pipe(finalize(() => {
      this.loading.set(false);
    })).subscribe(([weathers, res]) => {
      if (res.status !== '00000') {
        this.errorMessage.set(res.message);
      }
      if (res.data) {
        const { headers, data, } = res.data;
        const newData = new FillDataOfTrend(data, headers, body.dt).fill(time_interval);
        const chart = new RealTimeChart({ ...res.data, data: newData }, date, time, compareDate, weathers);

        chart.plotOptions.series.events = { legendItemClick };
        this.option.set(chart?.getOption() || null);
        this.weathers.set(weathers);
      }
    })
  }
  
}
