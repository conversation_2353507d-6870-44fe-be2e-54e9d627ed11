<app-navigation />

<div class="sticky top-0 z-40 py-2 mb-1 bg-white shadow-lg">
  <div class="flex items-center justify-center px-5">
    <div class="max-w-(--breakpoint-2xl) flex-1 min-w-0 flex items-center gap-x-2 text-xs">
      <app-date-compare />

      <div class="flex items-center ml-8">
        <label class="inline-flex items-center font-bold leading-0 whitespace-nowrap">区域：</label>
        <app-area-select [(ngModel)]="area" (ngModelChange)="onAreaChange($event)" />
      </div>

      <button nz-button nzType="default" (click)="reset()">重置</button>
    </div>
    
    @if (deadline()) {
      <div class="flex text-xs max-xl:flex-col max-xl:items-end pl-1.5">
        <span>当前日期数据截止至</span>
        <span>{{isToday() ? deadline() : '23:59:59'}}	</span>
      </div>
    } @else {
      <app-skeleton class="w-56 h-4" />
    }
  </div>

  <app-progress-bar class="absolute inset-x-0 top-0 h-px z-10 scale-y-50 origin-top" />
</div>


<div class="flex justify-center">
  <div class="max-w-(--breakpoint-2xl) flex-1 min-w-0">
    <app-market-overview id="anchor-overview" />
    <app-real-time-area id="anchor-area" />
  </div>

  <div class="tools-bar pointer-events-none">
    <app-selfhelp-btn />

    <div class="my-auto flex flex-col justify-center pointer-events-auto">
      <nz-anchor [nzBounds]="200" [nzTargetOffset]="50">
        <nz-link nzTitle="总览" nzHref="#anchor-overview"></nz-link>
        <nz-link nzTitle="区域" nzHref="#anchor-area"></nz-link>
      </nz-anchor>
    </div>

    <app-back-top />
  </div>
</div>