import { addDays, format } from 'date-fns';
import { AxisTypeValue, XAxisOptions } from 'highcharts';
import Highcharts from 'highcharts';
import { _pad, createPoint, groupBy, toDecimals, toNumber } from '@common/function';
import { BaseHighCharts, SeriesItem } from '@common/chart/highcharts';
import { QueryOutputHeaderVo, QueryOutputVo } from '@api/query-engine/model';
import { CityWeatherVo } from '@api/caerus/model/city-weather.model';
import { getDimensionField, getNumberFields } from '@common/chart';

function tooltipFormatter() {
  return function(param) {
    const result = [];

    const map = new Map();
    const params = this.points.sort();
    
    params.forEach(item => {
      if (map.has(item.series.name)) {
        const arr = map.get(item.series.name) as any[];
        arr.push(item);
        arr.reverse();
      } else {
        map.set(item.series.name, [item]);
      }
    });
    
    const merged = [...map.values()].flat(1);
    const hDate = new Date(merged[0].x);
    const hh = hDate.getUTCHours();
    const mm = hDate.getUTCMinutes();
    const ss = hDate.getUTCSeconds();
    const hourStr = _pad(hh);
    const minuteStr = _pad(mm);
    const secondStr = _pad(ss);

    if (hourStr === '00' && minuteStr === '00' && secondStr === '00') {
      result.push(`
        <span>23:59:59</span>
        <table class="text-sm">
      `);
    } else {
      result.push(`
        <span>${hourStr}:${minuteStr}:${secondStr}</span>
        <table class="text-sm">
      `);
    }

    merged.forEach((params) => {
      const { series: { name: seriesName, userOptions: { weatherMap } }, y: value, x, color } = params;
      const [categorie] = seriesName.match(/\d{4}-\d{2}-\d{2}/g);
      const day = new Date(categorie.replace(/-/g, '/')).getDay();
      const weeks = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
      const week = weeks[day];
      
      const date = new Date(x);
      const hours = date.getUTCHours();
      const realDate = seriesName.match(/\d{4}-\d{2}-\d{2}/g)[0];
      const weather = (<Map<string, string>>weatherMap).get(`${realDate} ${hours}`);
      const iconUrl = `assets/images/weather/${weather || 'w44'}.png`;
      const weatherIcon = weather ? `, <span class="inline-block size-4 px-1 box-content align-middle bg-no-repeat bg-center" style="background-image:url(${iconUrl}); background-size: auto 100%;"></span>` : '';
      const [preName] = seriesName.match(/^.*\(\d{4}-\d{2}-\d{2}\)/g);
      const [metricName] = seriesName.match(/(?<=\))\s*(.*)/g);

      result.push(`
        <tr>
          <td>
            <div class="flex items-center justify-center">
              ${createPoint(color)}
            </div>
          </td>
          <td class="text-right">
            ${preName.replace(/(\d{4}-\d{2}-\d{2})/, `$1, ${week}${weatherIcon}`)}
          </td>
          <td class="pr-2 text-right">
            ${metricName}: 
          </td>
          <td class="text-right">
            ${Number.isFinite(value) ? Intl.NumberFormat().format(value) : '-'}
          </td>
          <td class="pl-1"></td>
        </tr>
      `);
    })
    result.push('</table>');

    return result.join('');
  }
}


const sortByTimes = (key) => {
  return (a, b) => {
    // 将时间字符串转换为秒数，HH:MM:SS -> 总秒数
    const toSeconds = (timeStr: string) => {
      const [hours, minutes, seconds] = timeStr.split(':').map(Number);
      
      return hours * 3600 + minutes * 60 + seconds;
    };
    
    return toSeconds(a[key]) - toSeconds(b[key]); // 根据秒数进行比较排序
  }
};


export class PlotBand {
  borderWidth = 1;
  // borderColor = 'rgba(56, 189, 248, 0.2)';
  color = 'rgba(56, 189, 248, 0.1)';
  from: number;
  to: number;
  zIndex: number;
  label = {};

  constructor(props?: Partial<PlotBand>) {
    if (props) {
      Object.assign(this, { ...props });
    }
  }
}

class xAxisItem {
  categories: string[];
  opposite: boolean;
  tickInterval = 60 * 60 * 1000; // 间隔1小时
  tickWidth = 1;
  tickColor = '#ccd6eb';
  lineColor = '#ccd6eb';
  lineWidth = 1;
  gridLineColor = '#e6e6e6';
  crosshair = true;
  type: AxisTypeValue = 'datetime';
  plotBands: PlotBand[];
  labels: {
    useHTML?: boolean;
    formatter: (params: any) => string;
  }
  
  constructor(props?: Partial<xAxisItem>) {
    if (props) {
      Object.assign(this, { ...props });
    }
  }
}


function isToday(inputDate: string) {
  const today = format(new Date(), 'yyyy/MM/dd');
  return inputDate === today;
} 


export class RealTimeChart extends BaseHighCharts {

  #weatherMap = new Map<string, string>();
  override colors = ['#5087EC', '#EE752F', '#81B337', '#7F83F7', '#226FC2', '#A411D1', '#69AFE8', '#377F7F', '#FCCA00', '#B886F8', '#A16222', '#A0D0D5'];
  xAxis: XAxisOptions[] = [
    new xAxisItem({
      labels: {
        useHTML: true,
        formatter: function(params) {
          const hours = Highcharts.dateFormat('%H', this.value as any);
          const minutes = Highcharts.dateFormat('%M', this.value as any);
          const isTomorrow = params.isLast && hours === '00' && minutes === '00';

          return `
            <div class="inline-flex flex-col text-center">
              ${isTomorrow ? `23:59` : `${hours}:${minutes}`}
            </div>
          `;  
        }
      }
    }),
    new xAxisItem({
      opposite: true,
      tickWidth: 0,
      labels: {
        useHTML: true,
        formatter: function() {
          const { weatherMap } = this.chart.series[0].userOptions;
          const key = Highcharts.dateFormat('%Y-%m-%d %k', this.value as any);
          const weatherIcon = weatherMap?.get(key);

          return weatherIcon ? `
            <div class="inline-flex flex-col text-center">
              <div title="${weatherIcon}" class="w-7 h-7 bg-cover bg-no-repeat" style="background-image:url(assets/images/weather/${weatherIcon}.png)"></div>
            </div>
          ` : '';  
        }
      }
    })
  ];

  plotOptions = {
    series: {
      turboThreshold: 999999999,
      lineWidth: 1,
      marker: {
        radius: 0,
        symbol: 'circle'
      },
      events: {}
    }
  }

  override responsive = {} as any;

  constructor(
    properties: QueryOutputVo, 
    dateStr: string, 
    timeStr: string, 
    compareDateStr: string, 
    weathers: CityWeatherVo[]
  ) {
    super();

    const { headers, data, compareData } = properties;

    if (weathers.length > 0) {
      weathers.forEach(({ predictDate, predictHour, conditionName }) => {
        this.#weatherMap.set(`${predictDate} ${predictHour}`, conditionName);
      });
    }

    const titleMap = new Map([
      [`${format(addDays(new Date(dateStr), -0), 'yyyy-MM-dd')}`, '当日'],
      [`${format(addDays(new Date(dateStr), -1), 'yyyy-MM-dd')}`, '1日前'],
      [`${format(addDays(new Date(dateStr), -7), 'yyyy-MM-dd')}`, '7日前'],
    ]);

    const compareTitleMap = new Map([
      [`${format(addDays(new Date(dateStr), -0), 'yyyy-MM-dd')}`, '当日'],
      [`${format(addDays(new Date(compareDateStr || dateStr), -0), 'yyyy-MM-dd')}`, '对比日'],
    ]);

    const series = this.getSeries(
      headers, [...data, ...(compareData || [])], 
      dateStr, compareDateStr ? compareTitleMap : titleMap
    );

    this.xAxis.forEach(item => {
      const [year, month, day] = dateStr.split('/').map(toNumber);
      const maxDateStr = format(addDays(new Date(dateStr), 1), 'yyyy/MM/dd');
      const [maxYear, maxMonth, maxDay] = maxDateStr.split('/').map(toNumber);
      /**
       * 设置x轴起始点-结束点时间为
       * 当日的00:00-当日第二天的00:00
       */
      item.min = Date.UTC(year, month - 1, day, 0, 0, 0);
      item.max = Date.UTC(maxYear, maxMonth - 1, maxDay, 0, 0, 0);
    })

    /**
     * 如果`dateStr`为今天，则从`dateStr`和`timeStr`中解析出年月日时分秒
     * 为x轴设置当前时间范围区域
     */
    if (isToday(dateStr)) {
      const [year, month, day] = dateStr.split('/').map(toNumber);
      const [hours, minutes] = timeStr.split(':').map(toNumber);

      this.xAxis.at(0).plotBands = [
        new PlotBand({
          from: Date.UTC(year, month - 1, day, 0),
          to:   Date.UTC(year, month - 1, day, hours, minutes),
        })
      ];
    }
    
    this.setSeries(series);
    this.tooltip.xDateFormat = '%Y-%m-%d %H:%M';
    this.legend.verticalAlign = 'top';
    this.chart.type = 'line';
  }


  /**
   * 
   * @param headers 
   * @param data 
   * @param {string} date 当前日期，用于日期计算
   * @returns 
   */
  getSeries(
    headers: { [key: string]: QueryOutputHeaderVo },
    data: { [key: string]: string }[],
    dateStr: string,
    titleMap: Map<string, string>
  ) {
    const [dimensionField] = getDimensionField(headers);
    const numberFields = getNumberFields(headers);
    const groupData = groupBy(data, 'dt');
    const list = [] as SeriesItem[];

    if (!dimensionField) {
      throw new Error('dimensionField is undefined')
    }

    numberFields.map(field => {
      const { aliasName, dataUnit } = headers[field];
      
      Object.keys(groupData)
        .sort((a, b) => b.localeCompare(a))
        .forEach((key, n) => {
          const series = new SeriesItem();
          const title = titleMap.get(key);

          if (n > 0) {
            series.dashStyle = 'Dash';
            series.lineWidth = 1;
          }

          series.name = `${title}(${key})${aliasName}`;
          series.xAxis = n > 1 ? 1 : n;
          series.weatherMap = this.#weatherMap;

          series.data = groupData[key]
            .sort(sortByTimes(dimensionField))
            .map((item: any) => {
              const numeric = Number(item[field]);
              const [year, month, date] = dateStr.split('/').map(toNumber);
              const [hours, minutes, seconds = 0] = item[dimensionField].split(':').map(toNumber);
              let value: number = null;
          
              if (item[field] === null) {
                value = null;
              } else if (dataUnit === '%') {
                value = toDecimals(numeric);
              } else {
                value = numeric;
              }

              // 每一个series都必须等于同一天，否则数据无法堆叠展示
              return {
                x: Date.UTC(year, month - 1, date, hours, minutes, seconds, 0),
                y: value,
              }
            })

          list.push(series);
        })
    })

    return list;
  }


  override getOption() {
    const value = this;

    return {
      ...value,
      tooltip: {
        ...value.tooltip,
        formatter: tooltipFormatter()
      },
      plotOptions: {
        ...value.plotOptions,
        series: {
          ...value.plotOptions.series,
        }
      }
    };
  }

}