<div class="px-4 space-y-10">
  <div class="flex flex-col justify-center gap-3 p-6 rounded-lg bg-white">
    <h2 class="text-3xl">Table</h2>
    <div>

      <table class="text-xs bg-neutral-300 border-separate! border-spacing-px">
        <colgroup>
          <col span="7" class="bg-white">
        </colgroup>
        <thead>
          <tr>
            <th class="py-1 px-5" rowspan="2">指标</th>
            <th class="py-1 px-5" colspan="2">当前月:2025.06</th>
            <th class="py-1 px-5" colspan="2">当前月:2024.06</th>
            <th class="py-1 px-5" colspan="2">当前月:2023.06</th>
          </tr>
          <tr>
            <th class="py-1 px-5">数值</th>
            <th class="py-1 px-5">月环比</th>
            <th class="py-1 px-5">数值</th>
            <th class="py-1 px-5">同比2024</th>
            <th class="py-1 px-5">数值</th>
            <th class="py-1 px-5">同比2023</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td class="py-1 px-5">车主访问接单率</td>
            <td class="py-1 px-5">278,213</td>
            <td class="py-1 px-5"><span class="text-red-400">+100(+1.2%)</span></td>
            <td class="py-1 px-5">248,213</td>
            <td class="py-1 px-5"><span class="text-red-400">+100(+1.2%)</span></td>
            <td class="py-1 px-5">268,213</td>
            <td class="py-1 px-5"><span class="text-red-400">+100(+1.2%)</span></td>
          </tr>
        </tbody>
      </table>

    </div>
  </div>
  <div class="flex flex-col justify-center gap-3 p-6 rounded-lg bg-white">
    <h2 class="text-3xl">ICONS ({{iconCount()}})</h2>
    <dl class="flex flex-col gap-y-10">
      @for (items of icons(); track $index) {
        <dt class="capitalize">{{items.type}}</dt>
        <dd>
          <ul class="grid grid-flow-row gap-x-3 gap-y-10 grid-cols-2 sm:grid-cols-4 md:grid-cols-6 lg:grid-cols-8 xl:grid-cols-12">
            @for (item of items.children; track $index) {
              <li class="flex flex-col items-center gap-y-2">
                <app-icon-btn iconBtn class="xxl text-2xl">
                  @if (item?.component) {
                    <ng-container *ngComponentOutlet="item?.component"></ng-container>
                  }
                </app-icon-btn>
                
                <span class="capitalize text-xs truncate">
                  {{item.name}}
                </span>
              </li>
            }
          </ul>
        </dd>
      }
    </dl>
  </div>
</div>