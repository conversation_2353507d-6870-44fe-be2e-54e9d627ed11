import { NgComponentOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, signal } from '@angular/core';
import { IconBtnComponent } from '@shared/modules/headless/button';
import icons from '@shared/modules/icons/icons';

@Component({
  selector: 'app-example',
  templateUrl: './example.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgComponentOutlet,
    IconBtnComponent,
  ],
})
export class ExampleComponent {

  icons = signal(icons);
  iconCount = computed(() => {
    return this.icons().map(item => item.children).flat(1).length
  });

}
