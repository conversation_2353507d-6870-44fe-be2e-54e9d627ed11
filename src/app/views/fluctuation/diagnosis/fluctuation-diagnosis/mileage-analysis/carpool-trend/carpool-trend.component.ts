import { FormsModule } from '@angular/forms';
import { AfterViewInit, ChangeDetectionStrategy, Component, computed, DestroyRef, inject, signal } from '@angular/core';
import { NzSwitchModule } from 'ng-zorro-antd/switch';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { combineLatest, debounceTime, finalize } from 'rxjs';

import { groupBy, isNotNull } from '@common/function';
import { BuriedPointService } from '@common/service';
import { SwitchMap } from '@common/decorator';
import { QueryEngineFormService } from '@common/service/query-engine';
import { IntersectionDirective, PAGE_NAME } from '@common/directive';
import { CaerusApiService } from '@api/caerus';
import { QueryOutputVo } from '@api/query-engine/model';
import { GraphComponent } from '@shared/components/graph';
import { MetricsData } from '@api/caerus/model';
import { DiagnosisComponent } from '@views/fluctuation/diagnosis';
import { LineSpinComponent } from '@shared/components/line-spin';
import { LineMultiXAxis, processDataGroup, sortBy, } from '../../shared/lib';
import { FlowService } from '../../shared/components/flow';


@Component({
  selector: 'app-carpool-trend',
  template: `
    <div class="flex flex-col gap-y-2" appIntersection (visible)="onVisible()">
      <header class="flex justify-center gap-x-1 text-base">
        <strong>[{{title()}}]</strong>
        <span class="inline-block text-neutral-500 scale-90">{{showName()}}</span>
        <span class="inline-block text-neutral-500 scale-90">{{suffix()}}</span>
        @if (targetVisible()) {
          <label class="flex items-center gap-x-1">
            <nz-switch [(ngModel)]="showTotal" nzSize="small" class="leading-none"></nz-switch>
            <span>对比大盘</span>
          </label>
        }
      </header>
      <div class="relative flex items-center justify-center h-100 text-xs">
        @if (loading()) {
          <app-line-spin />
        }
        @else {
          @if (option()) {
            <app-graph class="w-full" [options]="option()" />
          }
          @else if (errorMessage()) {
            <span>{{errorMessage()}}</span>
          }
        }
      </div>
    </div>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    FormsModule,
    NzSwitchModule,
    GraphComponent,
    LineSpinComponent,
    IntersectionDirective,
  ],
})
export class CarpoolTrendComponent implements AfterViewInit {
  
  readonly page_name   = inject(PAGE_NAME);
  readonly destroyRef  = inject(DestroyRef);
  readonly formService = inject(QueryEngineFormService);
  readonly apiService  = inject(CaerusApiService);
  readonly buriedPointService = inject(BuriedPointService);
  readonly flowService = inject(FlowService);
  readonly root = inject(DiagnosisComponent);

  showName = signal('');
  title = signal('合拼类型趋势');
  suffix = signal('');

  option = signal(null);
  errorMessage = signal<string>(null);
  loading = signal(false);

  showTotal = signal(false);
  showTotal$ = toObservable(this.showTotal);

  targetVisible = computed(() => {
    if (this.root.areaForm$()) {
      return this.root.areaForm$().extendName !== null;
    }

    return false;
  });
  
  ngAfterViewInit(): void {
    combineLatest([
      this.formService.form.valueChanges,
      this.flowService.param$,
      this.flowService.metrics$,
      this.flowService.tab$,
      this.flowService.filter$,
      this.showTotal$,
    ])
    .pipe(
      debounceTime(100),
      takeUntilDestroyed(this.destroyRef)
    ).subscribe(([body, param, metrics]) => {
      if (isNotNull(body, param, metrics)) {
        this.suffix.set('');
        this.showName.set(metrics.showName);
        this._fetchMetrics(metrics);
      }
    })
  }


  @SwitchMap()
  private _fetchMetrics({ extendName }: MetricsData) {    
    const body: any = this.formService.value();
    const showTotal = Number(this.showTotal());
    const totalExtendName = this.root.areaForm$()?.extendName;
    const tabFilter = this.flowService.tab();
    const filter = this.flowService.filter();
    
    if (tabFilter) {
      this.suffix.set(`(${tabFilter?.value[0]?.value})`);      
      body.filter.items = body.filter.items.concat(tabFilter);
    }

    if (filter) {
      body.filter.items = body.filter.items.concat(filter);
    }

    body.metrics = [{ extendName }];

    if (showTotal) {
      body['showTotal'] = showTotal;
      body['totalExtendName'] = totalExtendName
    }

    // console.log(`%c[订单时效-合拼趋势图请求参数]`, 'color: red', body);
    this.loading.set(true);
    return this.apiService.fetchPkgTypeValueData({ ...body }).pipe(
      finalize(() => this.loading.set(false))
    ).subscribe(res => {
      if (res.data?.data) {
        console.log(res.data);
        const data = res.data;
        const property = 'pkg_type';
        const dataGroup = groupBy(res.data.data, property);
        const compareDataGroup = groupBy(res.data.compareData, property);
        const { dt, compareDt, dtType } = this.formService.form.getRawValue();

        data.data = processDataGroup(dataGroup, data.headers, dt, dtType, property, key => key);
        data.compareData = processDataGroup(compareDataGroup, data.headers, compareDt, dtType, property, key => key);
        
        this._setChartData(data);
        this.errorMessage.set(null);
      }
      else {
        console.error('[错误]', res.error);
        this.errorMessage.set(res.error);
      }
    })
  }


  private _setChartData(data: QueryOutputVo) {
    let chart: LineMultiXAxis = null;

    try {
      const orderList = ['整体', '优享', '拼车', '独享', '优享-拼上', '拼车-拼上', '优享-拼上占比', '拼车-拼上占比'];
      chart = new LineMultiXAxis(data, false, sortBy(orderList, null));
      chart.yAxis.push({
        title: { text: '' },
        labels: { format: '{text}%', },
        gridLineWidth: 1,
        gridLineColor: '#e6e6e6',
        opposite: true
      });
      chart.legend.enabled = true;
      // chart.colors.length = chart.series.length / 2;
      chart.colors = chart.colors.slice(0, chart.series.length / 2);
      chart.sortFn = sortBy(orderList, 'series.name');
      
      this.option.set(chart?.getOption() || null);
    }
    catch (e: any) {
      console.log(e);
      this.errorMessage.set(e);
    }
  }

  
  onVisible() {
    console.log('波动诊断图表曝光埋点上报:', this.title());
    this.buriedPointService.addStat('dida_dpm_caerus_diagnose_graph_exposure', {
      page_name: this.page_name,
      graph_name: this.title()
    });
  }

}
