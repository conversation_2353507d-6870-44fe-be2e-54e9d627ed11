import { FormsModule } from '@angular/forms';
import { AfterViewInit, ChangeDetectionStrategy, Component, computed, DestroyRef, effect, inject, signal } from '@angular/core';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { combineLatest, debounceTime, filter, of, tap } from 'rxjs';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { NzSwitchModule } from 'ng-zorro-antd/switch';

import { groupBy } from '@common/function';
import { SwitchMap } from '@common/decorator';
import { BuriedPointService } from '@common/service';
import { QueryEngineFormService } from '@common/service/query-engine';
import { IntersectionDirective, PAGE_NAME } from '@common/directive';
import { FilterItemVo, MetricsItem } from '@api/caerus/model';
import { QueryInputVo, QueryOutputHeaderVo, QueryOutputVo } from '@api/query-engine/model';
import { QueryEngineApiService } from '@api/query-engine';
import { DiagnosisComponent } from '@views/fluctuation/diagnosis';
import { LineSpinComponent } from '@shared/components/line-spin';
import { ChartComponent } from '@shared/components/chart';
import { LineMultipleXAxis, mergeResult, processDataGroup, sortBy } from '../../shared/lib';
import { FlowService } from '../../shared/components/flow';


@Component({
  selector: 'app-rlist-trend',
  template: `
    <div class="flex flex-col gap-y-2 h-full" appIntersection (visible)="onVisible()">
      <header class="text-center text-sm space-x-1">
        <strong>[{{title()}}]</strong>
        <span class="inline-block text-neutral-500 scale-90">{{showName()}}</span>
        <span class="inline-block text-neutral-500 scale-90">{{suffix()}}</span>
      </header>
      <div class="flex items-center justify-center gap-x-5">
        <div class="grid grid-flow-col grid-rows-1">
          @for (item of legends(); track $index) {
            <label nz-checkbox class="ml-0! text-xs!" [(ngModel)]="item.checked" (ngModelChange)="onLegendChange(item.key, $event)">{{item.key}}</label>
          }
        </div>
        @if (targetVisible()) {
          <label class="flex items-center gap-x-1">
            <nz-switch [(ngModel)]="compareTotal" nzSize="small" class="leading-none"></nz-switch>
            <span>对比大盘</span>
          </label>
        }
      </div>
      <div class="relative flex items-center justify-center h-100 text-xs">
        @if (loading()) {
          <app-line-spin />
        }
        @else {
          @if (option()) {
            <app-charts class="w-full" [options]="option()" />
          }
          @else if (errorMessage()) {
            <span>{{errorMessage()}}</span>
          }
          @else {
            <span></span>
          }
        }
      </div>
    </div>
  `,
  host: {
    class: 'relative pt-2.5 rounded-xl',
    '[class.bg-neutral-100]': '!option() && errorMessage()',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    FormsModule,
    NzSwitchModule,
    NzCheckboxModule,
    ChartComponent,
    LineSpinComponent,
    IntersectionDirective,
  ],
})
export class RlistTrendComponent implements AfterViewInit {
  
  readonly page_name = inject(PAGE_NAME);
  readonly destroyRef  = inject(DestroyRef);
  readonly formService = inject(QueryEngineFormService);
  readonly apiService  = inject(QueryEngineApiService);
  readonly buriedPointService = inject(BuriedPointService);
  readonly flowService = inject(FlowService);
  readonly root = inject(DiagnosisComponent);

  /** 大盘-整体结果 */
  totalOwnResult = signal(null);
  totalOwnResult$ = toObservable(this.totalOwnResult);
  /** 大盘-其他结果 */
  totalOutResult = signal(null);
  totalOutResult$ = toObservable(this.totalOutResult);
  /** 城市-整体结果 */
  cityOwnResult = signal(null);
  cityOwnResult$ = toObservable(this.cityOwnResult);
  /** 城市-其他结果 */
  cityOutResult = signal(null);
  cityOutResult$ = toObservable(this.cityOutResult);

  /** 整体结果 */
  ownResult = signal(null);
  ownResult$ = toObservable(this.ownResult);
  /** 其他结果 */
  outResult = signal(null);
  outResult$ = toObservable(this.outResult);

  /** 是否对比大盘 */
  compareTotal = signal(false);
  compareTotal$ = toObservable(this.compareTotal);
  dimensionName = signal('is_rlist');

  legends = signal([
    { checked: true, key: '整体', filter: null },
    { checked: false, key: '预约单', filter: {"key":"1","value":"预约单"}},
    { checked: false, key: '即时单', filter: {"key":"0","value":"即时单"}},
  ]);

  legends$ = toObservable(this.legends);

  showName = signal('');
  title = signal('预约/即时趋势');
  suffix = signal('');

  loading = signal(false);
  errorMessage = signal<string>(null);
  option = signal(null);

  targetVisible = computed(() => {
    if (this.root.areaForm$()) {
      return this.root.areaForm$().extendName !== null;
    }

    return false;
  });


  constructor() {
    effect(() => {
      if (this.targetVisible() === false)  {
        this.compareTotal.set(false);
      }
    });
  }


  ngAfterViewInit(): void {
    this._subscribeToLegendAndCompareChange();
    this._subscribeToFlowChange();

    this._subscribeToTotalResultChange();
    this._subscribeToResultChange();
  }


  private _subscribeToTotalResultChange() {
    combineLatest([
      this.compareTotal$,
      this.totalOwnResult$,
      this.totalOutResult$,
      this.cityOwnResult$,
      this.cityOutResult$,
    ]).pipe(
      filter(([isCompareTotal]) => isCompareTotal),
      debounceTime(100),
      takeUntilDestroyed(this.destroyRef),
    ).subscribe(([, totalOwnResult, totalOutResult, cityOwnResult, cityOutResult]) => {
      let data: QueryOutputVo;

      const { showType } = this.flowService.metrics() || {};
      const isPercent = showType === 'rate';

      data = mergeResult(data, totalOwnResult);
      data = mergeResult(data, totalOutResult);
      data = mergeResult(data, cityOwnResult);
      data = mergeResult(data, cityOutResult);

      if (data)  {
        // console.log('[before render]', data);
        const chart = new LineMultipleXAxis(data, false, isPercent);
        // chart.sortFn = sortBy(['整体', '自有渠道', '外输渠道'], 'seriesName');
  
        this.option.set(chart.getOption());
      } else {
        this.option.set(null);
      }

      this.loading.set(false);
    });
  }


  private _subscribeToResultChange() {
    combineLatest([
      this.compareTotal$,
      this.ownResult$,
      this.outResult$,
    ]).pipe(
      filter(([isCompareTotal]) => !isCompareTotal),
      debounceTime(100),
      takeUntilDestroyed(this.destroyRef),
    ).subscribe(([, ownResult, outResult]) => {
      let data: QueryOutputVo;

      const { showType } = this.flowService.metrics() || {};
      const isPercent = showType === 'rate';

      data = mergeResult(data, ownResult);
      data = mergeResult(data, outResult);

      if (data)  {
        const chart = new LineMultipleXAxis(data, false, isPercent);
        chart.sortFn = sortBy(['整体'], 'seriesName');
  
        this.option.set(chart.getOption());
      } else {
        this.option.set(null);
      }

      this.loading.set(false);
    });
  }


  private _subscribeToLegendAndCompareChange() {
    combineLatest([
      this.legends$,
      this.compareTotal$,
    ]).pipe(
      debounceTime(100),
      takeUntilDestroyed(this.destroyRef)
    ).subscribe(([legends, compareTotal]) => {
      // console.clear();
      const selected = legends.filter(item => item.checked);
      const hasTotal = selected.find(item => item.key === '整体');
      const hasOther = selected.filter(item => item.key !== '整体');
      
      this._clearResult();
      this.loading.set(true);

      /** 对比大盘 */
      if (compareTotal) {
        if (hasTotal) {
          this._fetchTotalOwnResult();
          this._fetchCityOwnResult();
        }

        if (hasOther.length > 0) {
          this._fetchTotalOutResult();
          this._fetchCityOutResult();
        }
      }
      else {
        if (hasTotal) {
          this._fetchOwnResult();
        }

        if (hasOther.length > 0) {
          this._fetchOutResult();
        }
      }
    });
  }


  private _subscribeToFlowChange() {
    combineLatest([
      this.formService.form.valueChanges,
      this.flowService.param$,
      this.flowService.metrics$,
      this.flowService.tab$,
      this.flowService.filter$,
    ])
    .pipe(
      debounceTime(100),
      takeUntilDestroyed(this.destroyRef)
    ).subscribe(([, , metrics]) => {
      this.suffix.set('');
      this.showName.set(metrics?.showName || '');
      this.loading.set(true);

      this.legends.update(items => items.map(item => {
        item.checked = item.key === '整体';
        return item;
      }));
    })
  }
  
  
  private _fetchMetrics(title: string, isTotal: boolean, dimension?: MetricsItem[], filters?: FilterItemVo[]) {    
    const body: QueryInputVo = this.formService.form.getRawValue();
    const { showName, extendName } = this.flowService.metrics() || {};
    const tabFilter = this.flowService.tab();
    const filter = this.flowService.filter();

    if (isTotal) {
      body.filter.items = [];
    }

    if (!showName || !extendName) {
      return of(null);
    } else {
      body.metrics = [{ extendName }];
    }

    if (dimension) {
      body.dimensions = dimension.concat({
        extendName: body.dtType
      });
    }

    if (tabFilter) {
      this.suffix.set(`(${tabFilter?.value[0]?.value})`);      
      body.filter.items = body.filter.items.concat(tabFilter);
    }

    if (filter) {
      body.filter.items = body.filter.items.concat(filter);
    }

    if (filters) {
      body.filter.items = body.filter.items.concat(filters);
    }

    // console.log(`[${title}-body]`, body);
    return this.apiService.search(body).pipe(
      tap(res => {
        // console.log(`[${title}-res]`, res.data);
        if (res.status !== '00000') {
          this.errorMessage.set(res.error);
          this.loading.set(false);
        }
      })
    );
  }


  /** 清空全部结果 */
  private _clearResult() {
    this.totalOwnResult.set(null);
    this.totalOutResult.set(null);
    this.cityOwnResult.set(null);
    this.cityOutResult.set(null);
    this.ownResult.set(null);
    this.outResult.set(null);
  }
  

  /** 获取大盘-整体结果 */
  @SwitchMap()
  private _fetchTotalOwnResult() {
    return this._fetchMetrics('大盘-整体', true, [], []).subscribe(res => {
      if (res?.data) {
        const data = res.data;
        const property = this.dimensionName();
        const dataGroup = groupBy(res.data.data, property);
        const compareDataGroup = groupBy(res.data.compareData, property);
        const { dt, compareDt, dtType } = this.formService.form.getRawValue();

        data.data = processDataGroup(dataGroup, data.headers, dt, dtType, property, () => `大盘-整体`);
        data.compareData = processDataGroup(compareDataGroup, data.headers, compareDt, dtType, property, () => `大盘-整体`);
        data.headers[property] = new QueryOutputHeaderVo();
        this.totalOwnResult.set(data);
      }
    });
  }


  /** 获取城市-整体结果 */
  @SwitchMap()
  private _fetchCityOwnResult() {
    const city = this.root.areaForm$().value[0].value;
    const body = this.formService.form.getRawValue();
    const filters = body.filter.items;

    return this._fetchMetrics(`${city}-整体`,false, [], filters).subscribe(res => {
      if (res?.data) {
        const data = res.data;
        const property = this.dimensionName();
        const dataGroup = groupBy(res.data.data, property);
        const compareDataGroup = groupBy(res.data.compareData, property);
        const { dt, compareDt, dtType } = this.formService.form.getRawValue();

        data.data = processDataGroup(dataGroup, data.headers, dt, dtType, property, () => `${city}-整体`);
        data.compareData = processDataGroup(compareDataGroup, data.headers, compareDt, dtType, property, () => `${city}-整体`);
        data.headers[property] = new QueryOutputHeaderVo();
        this.cityOwnResult.set(data);
      }
    })
  }


  /** 获取大盘-其他结果 */
  @SwitchMap()
  private _fetchTotalOutResult() {
    const selected = this.legends().filter(item => item.checked);
    const hasOther = selected.filter(item => item.key !== '整体');
    const property = this.dimensionName();

    const dimension = [{ extendName: 'is_rlist' }];
    const filterItem = new FilterItemVo(property, hasOther.map(item => item.filter), 'in');
    const filters = [].concat([filterItem]);

    return this._fetchMetrics(`大盘-其他`, true, dimension, filters).subscribe(res => {
      if (res?.data) {
        const data = res.data;
        const dataGroup = groupBy(res.data.data, property);
        const compareDataGroup = groupBy(res.data.compareData, property);
        const { dt, compareDt, dtType } = this.formService.form.getRawValue();

        data.data = processDataGroup(dataGroup, data.headers, dt, dtType, property, (key) => `大盘-${key}`);
        data.compareData = processDataGroup(compareDataGroup, data.headers, compareDt, dtType, property, (key) => `大盘-${key}`);
        this.totalOutResult.set(data);
      }
    })
  }


  /** 获取城市-其他结果 */
  @SwitchMap()
  private _fetchCityOutResult() {
    const city = this.root.areaForm$().value[0].value;
    const selected = this.legends().filter(item => item.checked);
    const hasOther = selected.filter(item => item.key !== '整体');
    const property = this.dimensionName();

    const body = this.formService.form.getRawValue();
    const dimension = [{ extendName: 'is_rlist' }];
    const filterItem = new FilterItemVo(property, hasOther.map(item => item.filter), 'in');
    const filters = body.filter.items.concat([filterItem]);

    return this._fetchMetrics(`${city}-其他`, false, dimension, filters).subscribe(res => {
      if (res?.data) {
        const data = res.data;
        const dataGroup = groupBy(res.data.data, property);
        const compareDataGroup = groupBy(res.data.compareData, property);
        const { dt, compareDt, dtType } = this.formService.form.getRawValue();

        data.data = processDataGroup(dataGroup, data.headers, dt, dtType, property, (key) => `${city}-${key}`);
        data.compareData = processDataGroup(compareDataGroup, data.headers, compareDt, dtType, property, (key) => `${city}-${key}`);
        this.cityOutResult.set(data);
      }
    })
  }


  /** 获取整体结果 */
  @SwitchMap()
  private _fetchOwnResult() {
    return this._fetchMetrics('整体', false, []).subscribe(res => {
      if (res?.data) {
        const data = res.data;
        const property = this.dimensionName();
        const dataGroup = groupBy(res.data.data, property);
        const compareDataGroup = groupBy(res.data.compareData, property);
        const { dt, compareDt, dtType } = this.formService.form.getRawValue();

        data.data = processDataGroup(dataGroup, data.headers, dt, dtType, property, () => '整体');
        data.compareData = processDataGroup(compareDataGroup, data.headers, compareDt, dtType, property, () => '整体');
        data.headers[property] = new QueryOutputHeaderVo();
        this.ownResult.set(data);
      }
    })
  }


  /** 获取其他结果 */
  @SwitchMap()
  private _fetchOutResult() {
    const selected = this.legends().filter(item => item.checked);
    const hasOther = selected.filter(item => item.key !== '整体');
    const property = this.dimensionName();

    const body = this.formService.form.getRawValue();
    const dimension = [{ extendName: 'is_rlist' }];
    const filterItem = new FilterItemVo(this.dimensionName(), hasOther.map(item => item.filter), 'in');
    const filters = body.filter.items.concat([filterItem]);

    return this._fetchMetrics('其他', false, dimension, filters).subscribe(res => {
      if (res?.data) {
        const data = res.data;
        const dataGroup = groupBy(res.data.data, property);
        const compareDataGroup = groupBy(res.data.compareData, property);
        const { dt, compareDt, dtType } = this.formService.form.getRawValue();

        data.data = processDataGroup(dataGroup, data.headers, dt, dtType, property, key => key);
        data.compareData = processDataGroup(compareDataGroup, data.headers, compareDt, dtType, property, key => key);
        this.outResult.set(res.data);
      }
    });
  }


  onLegendChange(key: string, state: boolean) {
    this.legends.update(items => items.map(item => {
      if (item.key === key) {
        item.checked = state;
      }
      return item;
    }))
  }

  
  onVisible() {
    console.log('波动诊断图表曝光埋点上报:', this.title());
    this.buriedPointService.addStat('dida_dpm_caerus_diagnose_graph_exposure', {
      page_name: this.page_name,
      graph_name: this.title()
    });
  }

}
