import { AfterViewInit, ChangeDetectionStrategy, Component, computed, DestroyRef, inject, signal, viewChild } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { combineLatest, debounceTime, finalize } from 'rxjs';

import { groupBy, isNotNull } from '@common/function';
import { IntersectionDirective, PAGE_NAME } from '@common/directive';
import { BuriedPointService } from '@common/service';
import { QueryEngineFormService } from '@common/service/query-engine';
import { QueryEngineApiService } from '@api/query-engine';
import { MetricsData, MetricsItem } from '@api/caerus/model';
import { QueryInputVo, QueryOutputVo } from '@api/query-engine/model';
import { GraphComponent } from '@shared/components/graph';
import { Bar, getPlotLinesLabel, SeriesItem } from '../../shared/lib';
import { FlowService } from '../../shared/components/flow';
import { LineSpinComponent } from '@shared/components/line-spin';
import { SwitchMap } from '@common/decorator';

@Component({
  selector: 'app-rlist-fluctuate',
  template: `
    <div class="flex flex-col h-full" appIntersection (visible)="onVisible()">
      <header class="text-center text-sm space-x-1">
        <strong>[{{title()}}]</strong>
        <span class="inline-block text-neutral-500 scale-90">{{showName()}}</span>
        <span class="inline-block text-neutral-500 scale-90">{{suffix()}}</span>
      </header>
      <div class="flex-1 min-h-0 relative flex items-center justify-center text-xs">
        @if (loading()) {
          <app-line-spin />
        }
        @else {
          @if (option()) {
            <app-graph [options]="option()" />
          }
          @else if (errorMessage()) {
            <span>{{errorMessage()}}</span>
          }
          @else {
            <span></span>
          }
        }
      </div>
    </div>
  `,
  host: {
    class: 'relative pt-2.5',
    '[class.rounded-xl]': 'errorMessage()',
    '[class.bg-neutral-100]': 'errorMessage()',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    GraphComponent,
    LineSpinComponent,
    IntersectionDirective,
  ],
})
export class RlistFluctuateComponent implements AfterViewInit {

  readonly page_name = inject(PAGE_NAME);
  readonly destroyRef = inject(DestroyRef);
  readonly formService = inject(QueryEngineFormService);
  readonly apiService = inject(QueryEngineApiService);
  readonly buriedPointService = inject(BuriedPointService);
  readonly flowService = inject(FlowService);
  
  highChartsRef = viewChild(GraphComponent);

  showName = signal('');
  title = signal('预约/即时波动');
  suffix = signal('');

  ownResult = signal<QueryOutputVo>(null);
  outResult = signal<QueryOutputVo>(null);

  metricsName = signal<string>(null);
  dimensionName = signal<string>('is_rlist');
  
  loading = signal(false);
  errorMessage = signal<string>(null);
  option = computed(() => {
    if (this.ownResult() && this.outResult()) {
      // console.log('[own]', this.ownResult());
      // console.log('[out]', this.outResult());
      
      const chart = new Bar();
      const own = this.ownResult();
      const out = this.outResult();
      const obj = groupBy<{[key:string]: string}>(out.data, this.dimensionName());
      const keys = Object.keys(obj);
      const own_diff_value = +own.data[0][`${this.metricsName()}_DIFF`];
      const own_diff_ratio = +own.data[0][`${this.metricsName()}_DIFF_RATIO`];
      const { showType } = this.flowService.metrics();
      const { labelText, labelColor } = getPlotLinesLabel(own_diff_value, own_diff_ratio, showType);

      const values = keys.map(key => {
        const item = obj[key][0];
        const name = item[this.dimensionName()];
        const diff  = +item[`${this.metricsName()}_DIFF`];
        const ratio = +item[`${this.metricsName()}_DIFF_RATIO`];
        const result = new SeriesItem();
        
        let rate = 1;
        let flucuate_ratio = NaN;

        if (showType === 'rate') {
          rate = 100;
        }
        
        if (own.compareData) {
          const own_value = +own.compareData[0][this.metricsName()];
          flucuate_ratio = +(diff / own_value * 100).toFixed(2);
        }

        result.name = name;
        result.y = parseFloat((diff * rate).toFixed(2));
        result.diff = parseFloat((diff * rate).toFixed(2));
        result.flucuate_ratio = flucuate_ratio;
        result.ratio = ratio;
        result.unit = showType;
        return result;
      });
      
      chart.setCategories(keys);
      chart.setSeries('较对比期涨跌', values);
      chart.subtitle.text = labelText;
      chart.subtitle.style.color = labelColor;

      return chart.getOption();
    }

    return null;
  });
  
  ngAfterViewInit() {
    combineLatest([
      this.formService.form.valueChanges,
      this.flowService.param$,
      this.flowService.metrics$,
      this.flowService.tab$,
      this.flowService.filter$,
    ])
    .pipe(
      debounceTime(100),
      takeUntilDestroyed(this.destroyRef)
    ).subscribe(([body, param, metrics]) => {
      if (isNotNull(body, param, metrics)) {
        this.suffix.set('');
        this.showName.set(metrics.showName);
        this.metricsName.set(metrics.extendName);
        this.loading.set(true);
        this.fetchOwnResult();
        this.fetchOutResult();
      }
    })
  }


  @SwitchMap()
  fetchOwnResult() {
    const metrics = this.flowService.metrics();

    this.ownResult.set(null);
    return this._fetchMetrics(metrics, [], (data) => {
      this.ownResult.set(data);
    });
  }


  @SwitchMap()
  fetchOutResult() {
    const metrics = this.flowService.metrics();
    const dimension = [{ extendName: 'is_rlist'}];

    this.outResult.set(null);
    return this._fetchMetrics(metrics, dimension, (data) => {
      this.outResult.set(data);
    });
  }


  private _fetchMetrics(
    { extendName }: MetricsData, 
    dimension: MetricsItem[],
    callback: (result: QueryOutputVo) => void
  ) {    
    const body: QueryInputVo = this.formService.form.getRawValue();
    const tabFilter = this.flowService.tab();
    const filter = this.flowService.filter();

    if (dimension) {
      body.dimensions = dimension;
    }

    if (tabFilter) {
      this.suffix.set(`(${tabFilter?.value[0]?.value})`);      
      body.filter.items = body.filter.items.concat(tabFilter);
    }

    if (filter) {
      body.filter.items = body.filter.items.concat(filter);
    }

    body.metrics = [{ extendName }];

    return this.apiService.search(body).pipe(
      finalize(() => this.loading.set(false))
    ).subscribe(res => {
      if (res.data?.data) {
        callback(res.data);
        this.errorMessage.set(null);
      }
      else {
        console.error('[错误]', res);
        this.errorMessage.set(res.error || res.message);
      }
    })
  }

  
  onVisible() {
    console.log('波动诊断图表曝光埋点上报:', this.title());
    this.buriedPointService.addStat('dida_dpm_caerus_diagnose_graph_exposure', {
      page_name: this.page_name,
      graph_name: this.title()
    });
  }

}
