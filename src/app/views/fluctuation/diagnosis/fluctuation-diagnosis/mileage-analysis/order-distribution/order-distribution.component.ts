import { AfterViewInit, ChangeDetectionStrategy, Component, DestroyRef, inject, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { combineLatest, debounceTime } from 'rxjs';

import { SwitchMap } from '@common/decorator';
import { BuriedPointService } from '@common/service';
import { IntersectionDirective, PAGE_NAME } from '@common/directive';
import { QueryEngineApiService } from '@api/query-engine';
import { QueryInputVo, QueryOutputVo } from '@api/query-engine/model';
import { MetricsData, MetricsItem } from '@api/caerus/model';
import { QueryEngineFormService } from '@common/service/query-engine';
import { isNotNull, sleep } from '@common/function';
import { GraphComponent } from '@shared/components/graph';
import { FlowService } from '../../shared/components/flow';
import { BarStacked } from '../../shared/lib/bar-stack';
import { Bar, generateSeriesItem } from '../../shared/lib';
import { sortBy } from '../../shared/lib/helpers';


@Component({
  selector: 'app-order-distribution',
  template: `
    <header class="relative flex items-center p-5">
      <span class="font-black text-base">
        <strong class="text-primary">{{flowService.metrics()?.showName}}</strong>-细分定位
      </span>
    </header>
    <div class="flex flex-col h-full" appIntersection (visible)="onVisible()">
      <header class="text-center text-sm space-x-1">
        <strong>[{{title()}}]</strong>
        <span class="inline-block text-neutral-500 scale-90">{{showName()}}</span>
        <span class="inline-block text-neutral-500 scale-90">{{suffix()}}</span>
      </header>
      <div class="flex-1 min-h-0 relative">
        @if (option()) {
          <app-graph [options]="option()" />
        }
      </div>
    </div>
  `,
  host: {
    class: 'relative'
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    GraphComponent,
    IntersectionDirective,
  ],
})
export class OrderDistributionComponent implements AfterViewInit {

  readonly page_name = inject(PAGE_NAME);
  readonly destroyRef = inject(DestroyRef);
  readonly formService = inject(QueryEngineFormService);
  readonly apiService = inject(QueryEngineApiService);
  readonly buriedPointService = inject(BuriedPointService);
  readonly flowService = inject(FlowService);
  
  option = signal(null);
  title = signal('市内/城际分布');
  showName = signal('');
  suffix = signal('');

  metricsName = signal<string>(null);
  dimensionName = signal<string>(null);
  
  
  ngAfterViewInit() {
    combineLatest([
      this.formService.form.valueChanges,
      this.flowService.param$,
      this.flowService.metrics$,
      this.flowService.tab$,
      this.flowService.filter$,
    ])
    .pipe(
      debounceTime(100),
      takeUntilDestroyed(this.destroyRef)
    ).subscribe(([body, param, metrics]) => {
      if (isNotNull(body, metrics, param)) {
        this.suffix.set('');
        this.showName.set(metrics.parentShowName);
        this.metricsName.set(metrics.extendName);
        this.dimensionName.set(param.dimension[0].extendName);
        this.fetchMetrics(metrics, param.dimension);
      }
    })
  }

  
  @SwitchMap()
  fetchMetrics({ extendName }: MetricsData, dimension?: MetricsItem[]) {    
    const body: QueryInputVo = this.formService.form.getRawValue();
    const tabFilter = this.flowService.tab();
    // const filter = this.flowService.filter();

    if (dimension) {
      body.dimensions = dimension;
    }

    if (tabFilter) {
      this.suffix.set(`(${tabFilter?.value[0]?.value})`);      
      body.filter.items = body.filter.items.concat(tabFilter);
    }

    // if (filter) {
    //   body.filter.items = body.filter.items.concat(filter);
    // }

    body.metrics = [{ extendName }];
    // console.log('%c[里程分布图请求参数]', 'color: #f00', body);
    
    return this.apiService.search(body).subscribe(res => {
      if (res.data?.data) {
        this._setChartData(res.data);
      }
      else {
        console.error(res.error);
      }
    })
  }


  /**
   * Sets the chart data for the channel fluctuate component.
   * 
   * @param data - The data for the chart.
   * @param compareData - The comparison data for the chart.
   * @param totalValue - The total value for the chart.
   */
  private async _setChartData({ headers, data, compareData }: QueryOutputVo) {
    const { showType, extendName } = this.flowService.metrics();
    const filter = this.flowService.filter();
    const selected = filter[0]?.value[0]?.value;
    const types = ['市内', '城际'];

    this.option.set(null);
    await sleep(100);

    if (showType === 'num') {
      const chart = new BarStacked();
      const series = chart
        .getSeries(headers, data.concat(compareData))
        .sort(sortBy(types));

      chart.plotOptions.bar.allowPointSelect = true;
      chart.plotOptions.bar.cursor = 'pointer';
      chart.plotOptions.bar.states.hover.brightness = 0.05;

      if (selected) {
        const index = types.indexOf(selected);

        chart.colors = chart.colors.map((color, i) => {
          if (index === i) {
            return color;
          } else {
            return '#ddd';
          }
        })
      }

      chart.setCategories(['当前期', '对比期']);
      chart.setSeries(series);

      this.option.set(chart.getOption());
      
      chart.click.subscribe(({ index, selected: state }) => {
        const lines = this.flowService.structure();
        const { extendName, parentShowName } = this.flowService.metrics();        
        const metrics = lines[index]['metricsDataList'].find(item => item.extendName === extendName);
        
        if (state) {
          this.flowService.setMetrics(metrics);
          this.flowService.setFilter(metrics);
        }

        console.log('埋点上报：点击 -> 波动诊断图形点击');
        this.buriedPointService.addStat('dida_dpm_caerus_diagnose_graph_click', {
          page_name: this.page_name,
          indicator_name: parentShowName,
          dimension_name: types[index],
        });
      });
      
    } else {
      const chart = new Bar();
      let rate = 1;

      chart.colors = ['#5087EC', '#68BBC4'];
      
      if (showType === 'rate') {
        rate = 100;
        chart.tooltip.valueSuffix = '%';
        chart.yAxis.labels.format = `{value}%`;
      }

      if (selected) {
        const index = types.indexOf(selected);

        chart.colors = chart.colors.map((color, i) => {
          if (index === i) {
            return '#ddd';
          } else {
            return color;
          }
        })
      }

      const _series = [
        generateSeriesItem('对比期', compareData, this.dimensionName(), extendName, rate),
        generateSeriesItem('当前期', data,        this.dimensionName(), extendName, rate),
      ];
      
      const keys = data.map(item => item[this.dimensionName()]);

      chart.series = _series;
      chart.plotOptions.bar.allowPointSelect = true;
      chart.plotOptions.bar.colorByPoint = true;
      chart.plotOptions.bar.cursor = 'pointer';
      chart.plotOptions.bar.dataLabels.enabled = true;
      chart.plotOptions.bar.dataLabels.format = '{point.title}';
      chart.tooltip.shared = true;
      chart.legend.enabled = false;
      chart.setCategories(keys);

      this.option.set(chart.getRawOption());

      chart.click.subscribe(({ name, selected: state }) => {
        const lines = this.flowService.structure();
        const { extendName } = this.flowService.metrics();        
        const index = types.indexOf(name);
        const metrics = lines[index]['metricsDataList'].find(item => item.extendName === extendName);
        
        if (state) {
          this.flowService.setMetrics(metrics);
          this.flowService.setFilter(metrics);
        }
      });
    }
  }

  
  onVisible() {
    console.log('波动诊断图表曝光埋点上报:', this.title());
    this.buriedPointService.addStat('dida_dpm_caerus_diagnose_graph_exposure', {
      page_name: this.page_name,
      graph_name: this.title()
    });
  }

}
