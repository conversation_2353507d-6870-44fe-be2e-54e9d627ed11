import { EventEmitter } from '@angular/core';
import { createPoint } from '@common/function';
import { SeriesItem as HighChartSeriesItem} from '@common/chart/highcharts';

interface Series {
  name: string;
  data: number[] | SeriesItem[];
}

export class SeriesItem extends HighChartSeriesItem {
  title: string;
  y: number;
  diff: number
  ratio: number;
  flucuate_ratio: number;
  /** 指标类型 rate 比率，frequency 频次，num 数值型  */
  unit: 'rate' | 'frequency' | 'num';
}


export function generateSeriesItem(
  name: string, 
  data: any[], 
  dimensionName: string, 
  extendName: string, 
  rate: number,
  sortFn = (a, b) => 0
) {
  return {
    name,
    data: (
      data.map(item => {
        const series = new SeriesItem();
        series.title = name;
        series.name = item[dimensionName];
        series.y = parseFloat((Number(item[extendName]) * rate).toFixed(2));
        series.diff = Number(item[extendName]+'_DIFF');
        return series;
      })
      .sort(sortFn)
    )
  }
}


export class Bar {
  chart = { type: 'bar', spacing: [0, 10, 15, 10] };
  colors = ['#2478f2', '#2478f2','#2478f2', '#2478f2', '#2478f2', '#2478f2', '#2478f2', '#2478f2', '#2478f2', '#2478f2', '#2478f2', '#2478f2', '#2478f2'];
  credits = { enabled: false };

  title = {
    text: '',
  };

  subtitle = {
    text: '',
    style: {
      color: '#666'
    }
  };

  legend = {
    enabled: undefined,
  };

  xAxis = {
    categories: [] as string[],
    crosshair: true,
    lineColor: "#ccd6eb",
  };

  yAxis = {
    min: undefined,
    max: null,
    title: {
      text: ''
    },
    plotLines: [],
    labels: {
      format: undefined,
    }
  };

  series: Series[] = [];
  click = new EventEmitter<any>();

  plotOptions = {
    bar: {
      allowPointSelect: false,
      colorByPoint: true,
      cursor: null,
      states: {
        select: {
          enabled: false
        },
        hover: {
          brightness: 0.1
        }
      },
      dataLabels: {
        enabled: false,
        color: '#fff',
        style: {
          textOutline: 'none',
          fontSize: '12px'
        },
        format: '{point.name}',
        inside: true,
      }
    }
  };

  tooltip = {
    shared: false,
    valueSuffix: '',
  };

  setCategories(categories: string[]) {
    this.xAxis.categories = categories;
  }

  setSeries(name: string, data: number[] | SeriesItem[]) {
    this.series = [{ name, data }];
  }

  setPlotLines(value: number, labelText: string, labelColor = '#6c6c6c', align: any = 'center') {
    this.yAxis.plotLines = [{
      color: '#bbbbbb',
      dashStyle: 'Dash',
      width: 2,
      value,
      label: {
        align,
        text: labelText,
        rotation: 0,
        x: -1,
        y: -5,
        style: {
          color: labelColor,
          backgroundColor: '#fff'
          // padding: '2px 3px'
        }
      },
      zIndex: 5
    }]
  }

  getOption() {
    
    return {
      ...this,
      tooltip: {
        useHTML: true,
        padding: 1,
        formatter: tooltipFormatterFn
      }
    };
  }

  getRawOption() {
    const value = this;
    
    return {
      ...value,
      plotOptions: {
        ...value.plotOptions,
        bar: {
          ...value.plotOptions.bar,
          events: {
            click: (event) => {
              
              setTimeout(() => {
                const { selected, name } = event.point;

                this.click.emit({ name, selected });
              })
            }
          }
        }
      }
    };
  }

}


export function tooltipFormatterFn(params: any) {
  const { name, color, diff, ratio, flucuate_ratio, unit } = this.point as SeriesItem;
  const labelColor = diff >= 0 ? '#fb7185' : '#34d399';
  const prefix = diff >= 0 ? '+' : '';
  const visible = unit === 'num';
  const suffix = unit === 'rate' ? 'pp' : '';

  return `
    <div class="text-xs p-1.5">
      <div>${name}</div>
      <div class="flex items-center gap-x-1">
        ${createPoint(color)}
        较对比期 <span style="color: ${labelColor}">${prefix}${diff}${suffix}</span>
        <span class="${visible ? '' : 'hidden'}">
          (<span style="color: ${labelColor}">${prefix}${ratio}%</span>)
        </span>
      </div>

      <div class="flex items-center gap-x-1 ${visible ? '' : 'hidden'}">
        ${createPoint(color)}
        波动占比 <span style="color: ${labelColor}">${prefix}${flucuate_ratio}%</span>
      </div>
    </div>
  `
}
