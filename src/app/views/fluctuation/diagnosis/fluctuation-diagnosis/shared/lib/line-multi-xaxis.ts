import { YAxisOptions } from 'highcharts';
import { QueryOutputHeaderVo, QueryOutputVo } from '@api/query-engine/model';
import { createPoint, fillEmpty, getDateStr, getMaxLength, getWeekByDate, groupBy, handleLegendItemClick, toDecimals } from '@common/function';
import { SeriesItem as HighChartSeriesItem} from '@common/chart/highcharts';
import { createValueElement, getCategories, getDimensionField, getMetricField, getNumberFields } from './helpers';
import { DtType } from '@common/service/query-engine';


class xAxisItem {
  categories: string[];
  opposite: boolean;
  tickInterval = 1;
  tickWidth = 1;
  tickColor = '#ccd6eb';
  lineColor = '#ccd6eb';
  gridLineColor = '#e6e6e6';
  crosshair = true;
  linkedTo: number;
  alignTicks = true;

  constructor({ categories, opposite }: Partial<xAxisItem>) {
    categories && (this.categories = categories);
    opposite && (this.opposite = opposite);
  }
}


class SeriesItem extends HighChartSeriesItem {
  type = 'spline';

  constructor(properties?: Partial<SeriesItem>) {
    super();
    if (properties) {
      const { name, data } = properties;

      name && (this.name = name);
      data && (this.data = data);
    }
  }
}


function tooltipFormatter({ sortFn, dtType }: LineMultiXAxis) {
  return function() {
    const result = [];
    const map = new Map();
    const params = this.points.sort(sortFn);
    
    params.forEach(item => {
      if (map.has(item.series.name)) {
        const arr = map.get(item.series.name) as any[];
        arr.push(item);
        arr.reverse();
      } else {
        map.set(item.series.name, [item]);
      }
    });
    
    const merged = [...map.values()].flat(1);

    result.push('<table class="text-sm">');
    merged.forEach((point, index) => {
      const { series: { name: seriesName }, y: value, color } = point;
      const categorie = point.x;
      const previousItem = merged[index-1];
      const x_str = getDateStr(categorie, dtType);
      const w = getWeekByDate(categorie, dtType);


      if (previousItem?.series.name === seriesName) {
        const currentValue  = value;
        const previousValue = previousItem.y; 
        const ratioValue = toDecimals((currentValue - previousValue) / previousValue);
        const diffValue = currentValue - previousValue;

        result.push(`
          <tr>
            <td class="flex items-center">
              ${createPoint(color)}
              (${x_str}${w ? '<span class="flex-1 min-w-0 w-1"></span>' : ''}${w || ''})
            </td>
            <td class="pr-2">${seriesName}: </td>
            <td class="text-right">
              ${
                seriesName.endsWith('占比') ? 
                (Number.isFinite(value) ? (value + '%') : '-') :
                (Number.isFinite(value) ? Intl.NumberFormat().format(value) : '-')
              }
            </td>
            <td>
              ${
                (<string>seriesName).endsWith('占比') ? createValueElement(diffValue, '(较对比期 {n}pp)') :
                Number.isFinite(ratioValue) ? createValueElement(ratioValue, '(较对比期 {n}%)') :
                '(较对比期 --)'
              }
            </td>
          </tr>
        `);
      } else {
        result.push(`
          <tr>
            <td class="flex items-center">
              ${createPoint(color)}
              (${x_str}${w ? '<span class="flex-1 min-w-0 w-1"></span>' : ''}${w || ''})
            </td>
            <td class="pr-2">${seriesName}: </td>
            <td class="text-right">
              ${
                seriesName.endsWith('占比') ? 
                (Number.isFinite(value) ? (value + '%') : '-') :
                (Number.isFinite(value) ? Intl.NumberFormat().format(value) : '-')
              }
            </td>
            <td></td>
          </tr>
        `);
      }
      
    })
    result.push('</table>');

    return result.join('');
  }
}


abstract class BaseLine {

  sortFn = (a, b) => 0;
  sortSeriesFn = (a, b) => 0;
  dtType: DtType;

  tooltip = {
    shared: true,
    useHTML: true,
  };
  colors = ['#5087EC', '#68BBC4', '#58A55C', '#F2BD42', '#EE752F', '#B0665D', '#E4C477', '#A3C2FB', '#A0D0D5', '#98B3E2', '#DE868F', '#F4CE98', '#B4C8D9', '#93D2F3', '#4095E5', '#7F83F7', '#E99D42', '#CBA43F', '#BFBF3D', '#81B337', '#347CAF', '#377F7F', '#FCCA00', '#B886F8', '#A16222',];
  legend = { enabled: false, verticalAlign: 'top' };
  xAxis: xAxisItem[] = [];
  yAxis: YAxisOptions[] = [{ title: { text: '' }, opposite: false }];
  series: SeriesItem[] = [];
  chart = { type: 'spline' };
  title = { text: '' };
  credits = { enabled: false };
  plotOptions = {
    series: {
      marker: {
        radius: 2,
        symbol: 'circle'
      }
    }
  }
  
  setSeries(series: SeriesItem[]) {
    this.series = series;
  }
  

  getSeries(
    headers: { [key: string]: QueryOutputHeaderVo },
    data: { [key: string]: string }[],
    xAxisIndex: number,
    withMetricName = false
  ) {
    const [dimensionField] = getDimensionField(headers);
    const source = ['当前', '对比'];

    if (!data) {
      throw `${source[xAxisIndex] ?? ''}日期无数据`;
    }

    if (dimensionField) {
      const groupData = groupBy<{[key: string]: { [key: string]: string }}>(data, dimensionField);
      const list = [] as SeriesItem[];
      
      Object.keys(groupData).sort(this.sortSeriesFn).forEach((key) => {
        const metricFields = getMetricField(headers);
        
        metricFields.forEach((metricField) => {
          const series = new SeriesItem();
          
          if (withMetricName) {
            series.name = `${key ? `${key}-` : ''}${headers[metricField].aliasName}`;
          } else {
            series.name = `${key}`;
          }
  
          const isRatio = series.name.indexOf('占比') > -1;

          series.data = groupData[key].map(item => {
            const value = Number(item[metricField]);

            if (item[metricField] === null) {
              return null;
            }

            if (isRatio) {
              return toDecimals(value);
            }
            
            return value;
          });

          series.yAxis = isRatio ? 1 : 0;
          series.xAxis = xAxisIndex;
  
          if (xAxisIndex > 0) {
            series.dashStyle = 'Dash';
            series.linkedTo = `${key}`;
            series.lineWidth = 1;
          }
  
          list.push(series);
        });
      })

      return list;
    }
    else {
      const numberFields = getNumberFields(headers);

      return numberFields.map((key) => {
        const { aliasName } = headers[key];
        const series = new SeriesItem();
        
        series.name = aliasName;
        series.xAxis = xAxisIndex;
        series.data = data.map(item => {
          return item[key] === null ? null : Number(item[key])
        });
  
        if (xAxisIndex > 0) {
          series.dashStyle = 'Dash';
          series.lineWidth = 1;
        }
  
        return series;
      });
    }
  }

  setCategories(values: string[][]): void {
    values.forEach((categories, index) => {
      const item = new xAxisItem({
        categories,
        opposite: index > 0,
      });

      if (index > 0) {
        item.linkedTo = 0;
      }

      this.xAxis.push(item);
    })
  }

  getOption() {
    const value = this;

    return {
      ...value,
      tooltip: {
        ...value.tooltip,
        formatter: tooltipFormatter(this)
      },
      plotOptions: {
        ...value.plotOptions,
        series: {
          ...value.plotOptions.series,
          events: {
            legendItemClick: handleLegendItemClick(this),
          }
        }
      }
    };
  }

}


export class LineMultiXAxis extends BaseLine {

  xAxis: xAxisItem[] = [];

  constructor(
    properties: QueryOutputVo, 
    withMetricName = false,
    sortSeriesFn = (a, b) => 0
  ) {
    super();

    this.sortSeriesFn = sortSeriesFn;
    const { headers, data, compareData } = properties;
    const primary_xaxis = getCategories(headers, data);
    const secondary_xaxis = getCategories(headers, compareData);
    const max_xaxis_length = getMaxLength(primary_xaxis, secondary_xaxis);
    const primary_series = this.getSeries(headers, data, 0, withMetricName);
    const secondary_series = this.getSeries(headers, compareData, 1, withMetricName);

    this.setCategories([
      fillEmpty(max_xaxis_length)(primary_xaxis), 
      fillEmpty(max_xaxis_length)(secondary_xaxis)
    ]);
    
    primary_series.forEach((item, index) => {
      primary_series[index].data.length = max_xaxis_length;
    })

    secondary_series.forEach((item, index) => {
      secondary_series[index].data.length = max_xaxis_length;
    })
    
    this.setSeries([...primary_series, ...secondary_series]);
  }
  
}
