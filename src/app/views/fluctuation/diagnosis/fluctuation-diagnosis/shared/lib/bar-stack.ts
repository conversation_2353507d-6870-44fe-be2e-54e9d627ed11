import { EventEmitter } from '@angular/core';
import { QueryOutputHeaderVo } from '@api/query-engine/model';
import { SeriesItem as HighChartSeriesItem } from '@common/chart/highcharts';
import { getDimensionField, getNumberFields } from './helpers';
import { groupBy } from '@common/function';


class Pattern {
  color: string;
  height = 5;
  path = "M 0 0 L 5 5 M 4.5 -0.5 L 5.5 0.5 M -0.5 4.5 L 0.5 5.5";
  patternTransform = "scale(0.84 0.84)";
  width = 5;
  constructor({ color }: Partial<Pattern> = {}) {
    this.color = color;
  }
}


export class SeriesItem extends HighChartSeriesItem {
  stacking = 'percent' as const;
}


class BaseBarStacked {
  chart = { type: 'bar' };
  colors = ['#5087EC', '#68BBC4', '#58A55C', '#F2BD42', '#EE752F', '#B0665D', '#E4C477', '#A3C2FB', '#A0D0D5', '#98B3E2', '#DE868F', '#F4CE98', '#B4C8D9', '#93D2F3', '#4095E5', '#7F83F7', '#E99D42', '#CBA43F', '#BFBF3D', '#81B337', '#347CAF', '#377F7F', '#FCCA00', '#B886F8', '#A16222',];
  credits = { enabled: false };
  title = { text: '' };

  xAxis = {
    categories: [],
    lineColor: "transparent",
  };

  yAxis = {
    allowDecimals: false,
    min: 0,
    gridLineColor: 'transparent',
    title: {
      text: ''
    }
  };

  plotOptions = {
    bar: {
      stacking: 'normal',
      allowPointSelect: false,
      cursor: null,
      dataLabels: {
        enabled: true,
        format: '{point.percentage:,.2f}%'
      },
      states: {
        select: {
          enabled: false
        },
        hover: {
          brightness: 0.1
        }
      }
    }
  };

  series: SeriesItem[] = [];
  click = new EventEmitter<any>();

  setCategories(value: string[]): void {
    this.xAxis.categories = value;
  }


  setSeries(series: SeriesItem[]) {
    this.series = series;
  }


  getSeries(
    headers: { [key: string]: QueryOutputHeaderVo },
    data: { [key: string]: string }[],
  ) {
    const [dimensionField] = getDimensionField(headers);
    const groupData = groupBy<{[key: string]: { [key: string]: string }}>(data, dimensionField);
    const list = [] as SeriesItem[];
    
    Object.keys(groupData).sort().forEach((key, n) => {
      const series = new SeriesItem();
      const [numberFields] = getNumberFields(headers);

      series.name  = `${key}`;
      series.data  = groupData[key]
        .map(item => item[numberFields] === null ? null : Number(item[numberFields]))
        .map((item, index) => {
          const value = item ? parseFloat(item.toFixed(2)) : item;
          // 因目前无法解决选中后的颜色问题，所以先将纹理注释
          // const res = { y: value } as any;

          // if (index === 1) {
          //   res.color = {
          //     pattern: new Pattern({ color: this.colors[n] })
          //   };
          // } else {
          //   res.color = this.colors[n];
          // }
          
          return value;
        });
  
      list.push(series);
    })
  
    return list;
  }
  

  getOption() {
    const value = this;

    return {
      ...value,
      plotOptions: {
        ...value.plotOptions,
        bar: {
          ...value.plotOptions.bar,
          events: {
            click: (event) => {
              setTimeout(() => {
                const { selected, series: { index } } = event.point;

                this.click.emit({ index, selected });
              })
            }
          }
        }
      }
    };
  }

}


export class BarStacked extends BaseBarStacked {
  
}