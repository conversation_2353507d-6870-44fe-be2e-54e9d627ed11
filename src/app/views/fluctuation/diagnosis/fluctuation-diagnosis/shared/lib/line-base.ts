import { QueryOutputHeaderVo, QueryOutputVo } from '@api/query-engine/model';
import { createPoint, groupBy, isUndefinedOrNull, toDecimals } from '@common/function';
import { createValueElement, getCategories, getDimensionField, getMetricField, getNumberFields } from './helpers';

export class xAxisItem {
  type = 'category';
  data: string[];
  axisTick = {
    alignWithLabel: true
  };

  constructor({ data }: Partial<xAxisItem>) {
    data && (this.data = data);
  }
}


interface LineStyle {
  type: 'solid' | 'dashed' | 'dotted' | number[],
  width: number;
}

class SeriesItem {
  name: string;
  type = 'line';
  xAxisIndex: number;
  smooth = true;
  data: number[] | any[];
  symbolSize = 3;
  lineStyle: LineStyle = {
    type: 'solid',
    width: 2,
  };

  constructor(properties?: Partial<SeriesItem>) {
    if (properties) {
      const { name, data } = properties;

      name && (this.name = name);
      data && (this.data = data);
    }
  }
}

export abstract class BasicLine {

  title = {
    text: '',
    textAlign: 'center',
    left: 'middle',
    textStyle: {
      fontWeight: 'normal',
      fontSize: 14,
    }
  };
  // color = ['#5470C6', '#EE6666'];
  tooltip: any = {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    },
    hideDelay: 0,
    formatter: (params: any[]) => {   
      const result = [] as string[];
      const map = new Map();

      params.forEach(item => {
        if (map.has(item.seriesName)) {
          const arr = map.get(item.seriesName) as any[];
          arr.push(item);
          arr.reverse();
        } else {
          map.set(item.seriesName, [item]);
        }
      });

      const merged = [...map.values()].flat(1);

      result.push('<table>');
      merged.forEach((item, index) => {
        const { seriesIndex, dataIndex, seriesName, value, color } = item;
        const { xAxisIndex } = this.series[seriesIndex];
        const categorie = this.xAxis[xAxisIndex].data[dataIndex];
        const previousItem = merged[index-1];
        const isDateStr = /\d{4}-\d{2}-\d{2}/.test(categorie);
        const day = new Date(categorie.replace(/-/g, '/')).getDay();
        const weeks = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
        const week = isDateStr && weeks[day];
        
        if (
          this.xAxis.length > 1 &&
          previousItem?.seriesName === seriesName
        ) {
          const currentValue  = value;
          const previousValue = merged[index-1].value; 
          const ratioValue = toDecimals((currentValue - previousValue) / previousValue);
          
          result.push(`
            <tr>
              <td class="flex items-center">
                ${createPoint(color)}
                (${categorie}${week ? '<span class="flex-1 min-w-0 w-1"></span>' : ''}${week || ''})
              </td>
              <td class="pr-2">${seriesName}: </td>
              <td class="text-right">
                ${isUndefinedOrNull(value) ? '-' : Intl.NumberFormat().format(value)}
              </td>
              <td>
                ${Number.isFinite(ratioValue) ? createValueElement(ratioValue, '(较对比期 {n}%)') : '(较对比期 --)'}
              </td>
            </tr>
          `);
        } else {
          result.push(`
            <tr>
              <td class="flex items-center">
                ${createPoint(color)}
                (${categorie}${week ? '<span class="flex-1 min-w-0 w-1"></span>' : ''}${week || ''})
              </td>
              <td class="pr-2">${seriesName}: </td>
              <td class="text-right">
                ${isUndefinedOrNull(value) ? '-' : Intl.NumberFormat().format(value)}
              </td>
              <td></td>
            </tr>
          `);
        }
      })
      result.push('</table>');
      
      return result.join('');
    }
  };
  legend = {
    show: true
  };
  xAxis: xAxisItem[] = [];
  yAxis = [{ type: 'value' }];
  series: SeriesItem[] = [];
  dataZoom = [
    { type: 'slider', start: 0, end: 100 },
    { start: 0, end: 100 }
  ];

  grid = {
    left: '10%',
    top: 60,
    right: '10%',
  }
  
  abstract setCategories(categories: string[] | string[][] | any): void;


  setSeries(series: SeriesItem[]) {
    this.series = series;
  }
  

  getSeries(
    headers: { [key: string]: QueryOutputHeaderVo },
    data: { [key: string]: string }[],
  ) {
    const [dimensionField] = getDimensionField(headers);

    if (dimensionField) {
      const groupData = groupBy<{[key: string]: { [key: string]: string }}>(data, dimensionField);
      const list = [] as SeriesItem[];
      
      Object.keys(groupData).forEach((key) => {
        const metricFields = getMetricField(headers);
        
        metricFields.forEach(metricField => {
          const series = new SeriesItem();

          series.name = `${key ? `${key}-` : ''}${headers[metricField].aliasName}`;
          series.data  = groupData[key].map(item => item[metricField] === null ? null : Number(item[metricField]));
  
          list.push(series);
        });
      })

      return list;
    }
    else {
      const numberFields = getNumberFields(headers);
      return numberFields.map(key => {
        const { aliasName } = headers[key];
        const series = new SeriesItem();
        
        series.name = aliasName;
        series.data = data.map(item => {
          return item[key] === null ? null : Number(item[key])
        });

        return series;
      });
    }
  }


  getOption() {
    // return JSON.parse(JSON.stringify(this));
    return this;
  }

}

export class LineBase extends BasicLine {

  xAxis = [{
    type: 'category',
    data: [] as string[],
    axisTick: {
      alignWithLabel: true
    }
  }];


  constructor(properties: QueryOutputVo) {
    super();

    const { headers, data } = properties;
    const categories = getCategories(headers, data);
    const series = this.getSeries(headers, data);
    
    this.tooltip = {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
    };
    
    this.setCategories(categories);
    this.setSeries(series);
  }


  setCategories(categories: string[]): void {
    this.xAxis[0].data = categories;
  }
  
}
