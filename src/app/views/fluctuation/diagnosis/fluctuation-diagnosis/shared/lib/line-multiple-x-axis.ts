import { QueryOutputHeaderVo, QueryOutputVo } from '@api/query-engine/model';
import { createPoint, fillEmpty, getMaxLength, groupBy, toDecimals } from '@common/function';
import { createValueElement, getCategories, getDimensionField, getMetricField, getNumberFields } from './helpers';
import { trace } from '@common/const';


class xAxisItem {
  type = 'category';
  data: string[];
  axisTick = {
    alignWithLabel: true
  };

  constructor({ data }: Partial<xAxisItem>) {
    data && (this.data = data);
  }
}


interface LineStyle {
  type: 'solid' | 'dashed' | 'dotted' | number[],
  width: number;
}


class SeriesItem {
  name: string;
  type = 'line';
  xAxisIndex: number;
  yAxisIndex = 0;
  smooth = true;
  data: number[] | any[];
  symbolSize = 3;
  lineStyle: LineStyle = {
    type: 'solid',
    width: 2,
  };

  isPercent: boolean;

  constructor(properties?: Partial<SeriesItem>) {
    if (properties) {
      const { name, data } = properties;

      name && (this.name = name);
      data && (this.data = data);
    }
  }
}

function computeNiceMinY(minData, maxData, tickCount = 5, bufferRatio = 0.1) {
    const range = maxData - minData;
    const bufferedMin = minData - range * bufferRatio;

    const rawInterval = (maxData - bufferedMin) / tickCount;
    const magnitude = Math.pow(10, Math.floor(Math.log10(rawInterval)));
    const normalized = rawInterval / magnitude;

    let niceInterval;
    if (normalized < 1.5) {
      niceInterval = 1 * magnitude;
    } else if (normalized < 3) {
      niceInterval = 2 * magnitude;
    } else if (normalized < 7) {
      niceInterval = 5 * magnitude;
    } else {
      niceInterval = 10 * magnitude;
    }

    return Math.floor(bufferedMin / niceInterval) * niceInterval;
  }

abstract class BaseLine {

  sortFn = (a, b) => 0;
  tooltip = {
    trigger: 'axis',
    axisPointer: {
      type: 'shadow'
    },
    formatter: (params) => {            
      params.sort(this.sortFn)
      const result = [] as string[];
      const map = new Map();

      params.forEach(item => {
        if (map.has(item.seriesName)) {
          const arr = map.get(item.seriesName) as any[];
          arr.push(item);
          arr.reverse();
        } else {
          map.set(item.seriesName, [item]);
        }
      });

      const merged = [...map.values()].flat(1);

      result.push('<table>');
      merged.forEach((item, index) => {
        const { seriesIndex, dataIndex, seriesName, value, color } = item;
        const { xAxisIndex, isPercent } = this.series[seriesIndex];
        const categorie = this.xAxis[xAxisIndex].data[dataIndex];
        const previousItem = merged[index-1];
        const isDateStr = /\d{4}-\d{2}-\d{2}/.test(categorie);
        const day = new Date(categorie.replace(/-/g, '/')).getDay();
        const weeks = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
        const week = isDateStr && weeks[day];
        
        if (
          this.xAxis.length > 1 &&
          previousItem?.seriesName === seriesName
        ) {
          const currentValue  = value;
          const previousValue = merged[index-1].value; 
          const ratioValue = toDecimals((currentValue - previousValue) / previousValue);
          const diffValue = currentValue - previousValue;

          result.push(`
            <tr>
              <td class="flex items-center">
                ${createPoint(color)}
                (${categorie}${week ? '<span class="flex-1 min-w-0 w-1"></span>' : ''}${week || ''})
              </td>
              <td class="pr-2">${seriesName}: </td>
              <td class="text-right">
                ${
                  (seriesName.endsWith('占比') || isPercent) ? 
                  (Number.isFinite(value) ? (value + '%') : '-') :
                  (Number.isFinite(value) ? Intl.NumberFormat().format(value) : '-')
                }
              </td>
              <td>
                ${
                  ((<string>seriesName).endsWith('占比') || isPercent) ? createValueElement(diffValue, '(较对比期 {n}pp)') :
                  Number.isFinite(ratioValue) ? createValueElement(ratioValue, '(较对比期 {n}%)') :
                  '(较对比期 --)'
                }
              </td>
            </tr>
          `);
        } else {
          result.push(`
            <tr>
              <td class="flex items-center">
                ${createPoint(color)}
                (${categorie}${week ? '<span class="flex-1 min-w-0 w-1"></span>' : ''}${week || ''})
              </td>
              <td class="pr-2">${seriesName}: </td>
              <td class="text-right">
                ${
                  (seriesName.endsWith('占比') || isPercent) ? 
                  (Number.isFinite(value) ? (value + '%') : '-') :
                  (Number.isFinite(value) ? Intl.NumberFormat().format(value) : '-')
                }
              </td>
              <td></td>
            </tr>
          `);
        }
      })
      result.push('</table>');
      
      return result.join('');
    }
  };
  legend: {
    show: boolean;
    data?: string[]
  } = { show: false } ;
  color = ['#5087EC', '#68BBC4', '#58A55C', '#F2BD42', '#EE752F', '#B0665D', '#E4C477', '#A3C2FB', '#A0D0D5', '#98B3E2', '#DE868F', '#F4CE98', '#B4C8D9', '#93D2F3', '#4095E5', '#7F83F7', '#E99D42', '#CBA43F', '#BFBF3D', '#81B337', '#347CAF', '#377F7F', '#FCCA00', '#B886F8', '#A16222'];
  xAxis: xAxisItem[] = [];
  yAxis = [{
    type: 'value',
    splitNumber: 5,
    min: function({min, max}) {
      // trace({ min, max });
      return Math.max(0, computeNiceMinY(min, max, 5));
    },
    boundaryGap: [0, '10%']  // 给轴添加一个额外的空间，避免数据和轴线重叠
  }] as any[];
  series: SeriesItem[] = [];
  dataZoom = [
    { type: 'slider', start: 0, end: 100 },
    { start: 0, end: 100 }
  ];

  grid = {
    left: '10%',
    top: 30,
    right: '10%',
  }
  

  setSeries(series: SeriesItem[]) {
    this.series = series;
  }
  

  getSeries(
    headers: { [key: string]: QueryOutputHeaderVo },
    data: { [key: string]: string }[],
    xAxisIndex: number,
    withMetricName = false,
    isPercent = false,
  ) {
    const [dimensionField] = getDimensionField(headers);
    const source = ['当前', '对比'];

    if (!data) {
      throw `${source[xAxisIndex] ?? ''}日期无数据`;
    }

    if (dimensionField) {
      const groupData = groupBy<{[key: string]: { [key: string]: string }}>(data, dimensionField);
      const list = [] as SeriesItem[];
      
      Object.keys(groupData).forEach((key) => {
        const metricFields = getMetricField(headers);
        
        metricFields.forEach(metricField => {
          const series = new SeriesItem();

          if (withMetricName) {
            series.name = `${key ? `${key}-` : ''}${headers[metricField].aliasName}`;
          } else {
            series.name = `${key}`;
          }
  
          const isRatio = series.name.indexOf('占比') > -1;

          if (isRatio) {
            series.yAxisIndex = 1;
          }
          
          series.isPercent = isPercent;
          series.xAxisIndex = xAxisIndex;
          series.data  = groupData[key].map(item => {
            const value = Number(item[metricField]);

            if (item[metricField] === null) {
              return null;
            }

            if (isRatio || isPercent) {
              return toDecimals(value);
            }

            return value;
          });

  
          if (xAxisIndex > 0) {
            series.lineStyle.type = 'dashed';
            series.lineStyle.width = 1;
          }
  
          list.push(series);
        });
      })

      return list;
    }
    else {
      const numberFields = getNumberFields(headers);

      return numberFields.map(key => {
        const { aliasName } = headers[key];
        const series = new SeriesItem();
        
        series.name = aliasName;
        series.data = data.map(item => {
          return item[key] === null ? null : Number(item[key])
        });
        series.xAxisIndex = xAxisIndex;
  
        if (xAxisIndex > 0) {
          series.lineStyle.type = 'dashed';
          series.lineStyle.width = 1;
        }
  
        return series;
      });
    }
  }


  getOption() {
    return this;
  }

}


export class LineMultipleXAxis extends BaseLine {

  xAxis: xAxisItem[] = [];


  constructor(properties: QueryOutputVo, withMetricName = false, private _isPercent = false) {
    super();

    const { headers, data, compareData } = properties;
    const categories = getCategories(headers, data);
    const secondaryCategories = getCategories(headers, compareData);
    const maxLength = getMaxLength(categories, secondaryCategories);
    const series = this.getSeries(headers, data, 0, withMetricName, _isPercent);
    const secondarySeries = this.getSeries(headers, compareData, 1, withMetricName, _isPercent);

    this.setCategories([
      fillEmpty(maxLength)(categories), 
      fillEmpty(maxLength)(secondaryCategories)
    ]);
    this.setSeries([...series, ...secondarySeries]);

    if (this._isPercent) {
      this.yAxis.at(0).axisLabel = {
        formatter: '{value}%'
      };
    }
  }


  setCategories(categories: string[][]): void {
    categories.forEach(data => {
      this.xAxis.push(new xAxisItem({ data }));
    })
  }
  
}
