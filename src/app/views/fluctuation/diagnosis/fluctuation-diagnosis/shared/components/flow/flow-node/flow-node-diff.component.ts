import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { ValueFormatter } from '@shared/components/value-formatter';

@Component({
  selector: 'app-flow-node-diff',
  template: `
    较对比期: 
    @if (type() === 'num') {
      <value-formatter useColor useMultiplier="false" [value]="value()" />
    }
    @if (ratio() !== undefined && ratio() !== null) {
      @switch (type()) {
        @case ('num')       { <value-formatter useColor [value]="ratio()" showBracket useMultiplier="false" suffix="%" /> }
        @case ('rate')      { <value-formatter useColor [value]="value()" suffix="pp" /> }
        @case ('frequency') { <value-formatter useColor [value]="value()" useMultiplier="false" /> }
      }
    }
    @else {
      <span>-</span>
    }
  `,
  host: {
    class: 'px-2 whitespace-nowrap cursor-pointer'
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    ValueFormatter,
  ],
})
export class FlowNodeDiffComponent {

  type  = input<'rate' | 'frequency' | 'num'>();
  value = input<number | any>();
  ratio = input<number | string>();

}
