import { ChangeDetectionStrategy, Component, model } from '@angular/core';

@Component({
  selector: 'app-flow-panel',
  template: `
    @if (visible()) {
      <ng-content />
    }
    <ng-content select="app-flow-node-expander" />
  `,
  styleUrl: './flow-panel.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [],
})
export class FlowPanelComponent {

  visible = model(true);

}
