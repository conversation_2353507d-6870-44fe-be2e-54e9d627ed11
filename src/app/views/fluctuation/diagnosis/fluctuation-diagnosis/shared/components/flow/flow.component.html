<nz-spin [nzSpinning]="loading1() || loading2() || loading3()">
  <div class="flex flex-col gap-y-4">
    <app-flow-panels>
      @for (item of metricsDataList(); track $index) {
        <app-flow-node
          class="text-sm! min-h-16 border-2! {{'border-'+service.colors[$index]}}"
          [data]="item"
          (click)="flowService.setMetrics(item); flowService.setFilter(item); flowService.scrollToAnchor()"
        >
          <app-flow-node-title>{{item.showName}}</app-flow-node-title>
          <app-flow-node-value [type]="item.showType" [value]="firstValueList() ? firstValueList()[item.extendName] : null" />
          <app-flow-node-diff class="text-xs text-black"
            [type]="item.showType"
            [value]="firstValueList() ? firstValueList()[item.extendName+'_DIFF'] : null"
            [ratio]="firstValueList() ? firstValueList()[item.extendName+'_DIFF_RATIO'] : null"
          />
        </app-flow-node>
        <app-flow-node-symbol class="last:hidden" [color]="'text-'+service.colors[$index]" [icon]="item.icon" />
      }
    </app-flow-panels>

    <app-flow-group>
      <app-flow-group-expander />

      @for (line of lineData(); track line) {
        <ng-template *ngTemplateOutlet="panelsItemTemplate; context: { $implicit: line, map: secondValueMap }"></ng-template>

        @if (line.levelData) {
          <app-flow-group>
            <app-flow-group-expander />
            
            @for (item of line.levelData.lineData; track $index) {
              <ng-template *ngTemplateOutlet="panelsItemTemplate; context: { $implicit: item, map: thirdValueMap }"></ng-template>
            }
          </app-flow-group>
        }
      }
    </app-flow-group>
  </div>
</nz-spin>


<ng-template #nodeItemTemplate let-item let-parent="parent" let-index="index" let-map="map">
  <app-flow-node
    [data]="item"
    [ngClass]="'border-'+service.colors[index]"
    (click)="flowService.setMetrics(item); flowService.setFilter(item); flowService.scrollToAnchor()"
  >
    @let key = item.dimensionFilters[0].value[0].value;
    <app-flow-node-title>{{item.showName}}</app-flow-node-title>
    <app-flow-node-value
      [type]="item.showType"
      [value]="map.get(key)?.[item.extendName]"
    />
    <app-flow-node-diff
     [type]="item.showType"
     [value]="map.get(key)?.[item.extendName+'_DIFF']"
     [ratio]="map.get(key)?.[item.extendName+'_DIFF_RATIO']"
    />
  </app-flow-node>
</ng-template>


<ng-template #panelsItemTemplate let-line let-map="map">
  <app-flow-panels>
    <ng-template *ngTemplateOutlet="nodeItemTemplate; context: { $implicit: line.metricsDataList[0], parent: line, index: 0, map: map }"></ng-template>

    <app-flow-panel>
      <app-flow-node-expander />
      <app-flow-node-symbol class="last:hidden" [color]="'text-'+service.colors[0]" [icon]="line.metricsDataList[0].icon" />

      @for (item of line.metricsDataList; track $index; let last = $last) {
        @if ($index > 0) {
          <ng-template *ngTemplateOutlet="nodeItemTemplate; context: { $implicit: item, parent: line, index: $index, map: map }"></ng-template>
          @if (!last) {
            <app-flow-node-symbol class="last:hidden" [color]="'text-'+service.colors[$index]" [icon]="item.icon" />
          }
        }
      }
    </app-flow-panel>
  </app-flow-panels>
</ng-template>

<div class="hidden text-orange-300 bg-orange-300 border-orange-300"></div>
<div class="hidden text-rose-300 bg-rose-300 border-rose-300"></div>
<div class="hidden text-orange-200 bg-orange-200 border-orange-200"></div>
<div class="hidden text-teal-500/50 bg-teal-500/50 border-teal-500/50"></div>
<div class="hidden text-sky-300 bg-sky-300 border-sky-300"></div>
<div class="hidden text-teal-700/70 bg-teal-700/70 border-teal-700/70"></div>