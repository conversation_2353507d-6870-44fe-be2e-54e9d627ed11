import { ChangeDetectionStrategy, Component, computed, HostListener, inject, input } from '@angular/core';
import { NzPopoverModule } from 'ng-zorro-antd/popover';
import * as _ from 'lodash';

import { MetricsData } from '@api/caerus/model';
import { FluctuationDiagnosisService } from '@views/fluctuation/diagnosis/fluctuation-diagnosis';
import { FlowService } from '../flow.service';
import { FlowComponent } from '../flow.component';
import { Sleep } from '@common/decorator';
import { BuriedPointService } from '@common/service';
import { PAGE_NAME } from '@common/directive';

@Component({
  selector: 'app-flow-node',
  template: `
    <ng-template #metricsTitleTemplate>{{data().showName}} <span class="text-xs opacity-30 px-1">({{data().aliasName}})</span></ng-template>

    <div nz-popover nzPopoverPlacement="topLeft" [nzPopoverMouseEnterDelay]="0.5" [nzPopoverTitle]="metricsTitleTemplate" [nzPopoverContent]="data().bizExpression" >
      <ng-content />
    </div>
  `,
  styleUrl: './flow-node.component.css',
  host: {
    '[class.active]': 'isActive()'
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NzPopoverModule,
  ],
})
export class FlowNodeComponent {

  readonly page_name = inject(PAGE_NAME);
  readonly flowService = inject(FlowService);
  readonly service = inject(FluctuationDiagnosisService);
  readonly buriedPointService = inject(BuriedPointService);
  readonly parent = inject(FlowComponent);

  data = input.required<MetricsData>();

  isActive = computed(() => {
    return _.isEqual(this.flowService.metrics(), this.data());
  })


  @HostListener('click')
  @Sleep(100)
  handlerClick() {
    const key = this.parent.token();
    const { tabName, tabSubName } = this.service.config()[key];
    const { showName } = this.flowService.metrics();

    console.log('埋点上报：点击 -> 波动诊断指标卡');
    this.buriedPointService.addStat('dida_dpm_caerus_diagnose_indicator_card_click', {
      page_name: this.page_name,
      indicator_name: showName,
      tab_name: tabName,
      sub_tab_name: tabSubName,
    });
  }

}
