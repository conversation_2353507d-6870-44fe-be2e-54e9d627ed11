import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, NgTemplateOutlet } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, computed, DestroyRef, inject, input, signal } from '@angular/core';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { combineLatest, debounceTime, filter, Subscription } from 'rxjs';
import { NzSpinModule } from 'ng-zorro-antd/spin';

import { QueryEngineApiService } from '@api/query-engine';
import { QueryEngineFormService } from '@common/service/query-engine';
import { DimensionVo, MetricVo, QueryInputVo, QueryOutputVo } from '@api/query-engine/model';
import { TabPanelComponent } from '@shared/modules/headless/tabs';
import { groupBy, isNotNull } from '@common/function';

import { FlowGroupComponent } from './flow-group/flow-group.component';
import { FlowPanelComponent } from './flow-panel/flow-panel.component';
import { FlowGroupExpanderComponent } from './flow-group-expander/flow-group-expander.component';
import { FlowNodeExpanderComponent } from './flow-node-expander/flow-node-expander.component';
import { FlowNodeComponent } from './flow-node/flow-node.component';
import { FlowNodeTitleComponent } from './flow-node/flow-node-title.component';
import { FlowNodeValueComponent } from './flow-node/flow-node-value.component';
import { FlowNodeSymbolComponent } from './flow-node-symbol/flow-node-symbol.component';
import { FlowNodeDiffComponent } from './flow-node/flow-node-diff.component';
import { FlowPanelsComponent } from './flow-panels/flow-panels.component';
import { FluctuationDiagnosisService } from '../../../fluctuation-diagnosis.service';
import { FlowService } from './flow.service';


@Component({
  selector: 'app-flow',
  templateUrl: './flow.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: {
    class: ''
  },
  imports: [
    // JsonPipe,
    NgClass,
    NgTemplateOutlet,
    NzSpinModule,
    FlowGroupComponent,
    FlowGroupExpanderComponent,
    FlowPanelsComponent,
    FlowPanelComponent,
    FlowNodeComponent,
    FlowNodeTitleComponent,
    FlowNodeValueComponent,
    FlowNodeExpanderComponent,
    FlowNodeSymbolComponent,
    FlowNodeDiffComponent,
  ]
})
export class FlowComponent implements AfterViewInit {

  readonly destroyRef = inject(DestroyRef);
  readonly cdr = inject(ChangeDetectorRef);
  readonly apiService = inject(QueryEngineApiService);
  readonly service = inject(FluctuationDiagnosisService);
  readonly formService = inject(QueryEngineFormService);
  readonly flowService = inject(FlowService);
  readonly tabPanel = inject(TabPanelComponent);

  token = input.required<string>();
  levelData = computed(() => {
    if (this.service.config()) {
      const { levelData } = this.service.config()[this.token()];

      return levelData;
    }
    return null;
  })

  metricsDataList = computed(() => {
    if (this.levelData()) {
      return this.levelData().lineData[0]?.metricsDataList;
    }
    return null;
  })

  param = computed(() => {
    if (this.levelData()) {
      return this.levelData().lineData[0].levelData.param;
    }
    return null;
  })

  rootLineData = computed(() => {
    if (this.levelData()) {
      return this.levelData().lineData[0];
    }
    return null;
  })

  lineData = computed(() => {
    if (this.levelData()) {
      return this.levelData().lineData[0].levelData.lineData;
    }
    return null;
  })

  token$ = toObservable(this.token);
  levelData$ = toObservable(this.levelData);
  metricsDataList$ = toObservable(this.metricsDataList);
  loading1 = signal(false);
  loading2 = signal(false);
  loading3 = signal(false);
  errorMessage = signal<string>(null);
  firstValueList = signal<{ [key: string]: any }>(null);
  secondValueMap = new Map<string, {[key: string]: string}>();
  thirdValueMap = new Map<string,{[key: string]: string}>();


  ngAfterViewInit(): void {
    const source1$ = this.formService.form.valueChanges;
    const source2$ = this.levelData$;
    const source3$ = this.token$;
    let fetch1: Subscription;
    let fetch2: Subscription;
    let fetch3: Subscription;

    
    combineLatest([source1$, source2$, this.tabPanel.isActive$]).pipe(
      filter(([, , isActive]) => isActive),
      debounceTime(100),
      takeUntilDestroyed(this.destroyRef)
    ).subscribe(([a, b]) => {
      if (isNotNull(a, b)) {
        const { metrics, filter } = this.levelData().param;
        
        this.loading1.set(true);
        fetch1 && fetch1.unsubscribe();
        fetch1 = this.fetchMetrics(metrics, [], filter, (res) => {
          if (res?.data) {
            this.firstValueList.set(res.data[0]);
          }
          this.loading1.set(false);
        });
      }
    })

    
    combineLatest([source1$, source2$, source3$, this.tabPanel.isActive$]).pipe(
      filter(([, , , isActive]) => isActive),
      debounceTime(100),
      takeUntilDestroyed(this.destroyRef)
    ).subscribe(([body, levelData]: any) => {
      if (isNotNull(body, levelData)) {
        // 第二级节点数据获取
        const { metrics, dimension, filter } = levelData.lineData[0].levelData.param;

        this.loading2.set(true);
        this.flowService.setStructure(levelData.lineData[0].levelData.lineData);
        fetch2 && fetch2.unsubscribe();
        fetch2 = this.fetchMetrics(metrics, dimension, filter, (res) => {
          const { extendName } = dimension[0];

          if (res?.data) {
            this._setValueMap(res.data, extendName, this.secondValueMap);
          }
          this.loading2.set(false);
        })

        // 第三级节点数据获取
        if (levelData.lineData[0].levelData.lineData[0].levelData) {
          const { metrics, dimension, filter } = levelData.lineData[0].levelData.lineData[0].levelData.param;

          this.loading3.set(true);
          fetch3 && fetch3.unsubscribe();
          fetch3 = this.fetchMetrics(metrics, dimension, filter, (res) => {
            const { extendName } = dimension[0];
            
            this._setValueMap(res.data, extendName, this.thirdValueMap);
            // console.log('%c[第三级节点数据]', 'color:red', this.thirdValueMap);
            this.loading3.set(false);
          })
        }
      }
    })
  }


  private _setValueMap(data: {[key: string]: string}[], property: string, map: Map<string,{[key: string]: string}>) {
    const obj = groupBy<{[key: string]: string }>(data, property);

    Object.keys(obj).forEach(key => {
      map.set(key, obj[key][0]);
    })

    this.cdr.markForCheck();
  }

  
  fetchMetrics(
    metrics: MetricVo[], 
    dimensions: DimensionVo[], 
    filters: any[], 
    callback: (data: QueryOutputVo) => void
  ) {    
    const body: QueryInputVo = this.formService.form.getRawValue();

    body.metrics = metrics;
    body.dimensions = dimensions;

    if (filters) {
      body.filter.items = body.filter.items.concat(filters);
      // body.filter = { type: null, items: [
      //   ...body.filter.items,
      //   ...filters
      // ]};
    }
    
    // console.log('%c[获取节点数值]', 'color:red', body);
    return this.apiService.search(body).subscribe(res => {      
      if (res.error || !res?.data) {
        console.error(res.error || res.message);
      }
      callback(res?.data);
    })
  }

}
