import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { IconArrowRightComponent, IconCloseComponent, IconFastArrowRightComponent } from '@shared/modules/icons';

@Component({
  selector: 'app-flow-node-symbol',
  template: `
    @switch (icon()) {
      @case ('eq')   { <span class="text-base">=</span> }
      @case ('mult') { <CloseIcon class="text-xs" /> }
      @case ('>')    { <ArrowRightIcon class="text-2xl {{color() || ''}}" /> }
      @case ('>>')   { <FastArrowRightIcon class="{{color()}}" /> }
      @default       { <span>{{ icon() }}</span> }
    }
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    IconArrowRightComponent,
    IconFastArrowRightComponent,
    IconCloseComponent,
  ],
})
export class FlowNodeSymbolComponent {
  icon = input.required<string>();
  color = input<string>();
}
