import { toObservable } from '@angular/core/rxjs-interop';
import { ElementRef, EventEmitter, Injectable, signal } from '@angular/core';
import { DimensionFilter, FilterItemVo, LineData, MetricsData, Param } from '@api/caerus/model';

@Injectable()
export class FlowService {

  click = new EventEmitter<any>();

  anchor = signal<ElementRef>(null);
  
  param = signal<Param>(null);
  param$ = toObservable(this.param);

  structure = signal<LineData[]>(null);

  dimensions = signal<MetricsData>(null);
  metrics = signal<MetricsData>(null);
  metrics$ = toObservable(this.metrics);

  /** metrics -> dimensionFilter */
  filter = signal<DimensionFilter[]>([]);
  /** metrics -> dimensionFilter -> change event */
  filter$ = toObservable(this.filter);
  tab = signal<FilterItemVo>(null);
  tab$ = toObservable(this.tab);


  setParam(value: Param) {
    this.param.set(value);
  }


  setAnchor(value: ElementRef) {
    this.anchor.set(value);
  }
  

  setStructure(value: LineData[]) {
    this.structure.set(value);
  }


  /** 似乎用不上 */
  setDimensions(value: MetricsData) {
    console.log('setDimensions', value);
    // this.dimensions.set(value);
  }

  
  setMetrics(value: MetricsData) {
    this.metrics.set({...value});
  }


  setFilter(value: MetricsData) {
    this.filter.set(value.dimensionFilters || []);
  }

  
  setTab(value: FilterItemVo) {
    this.tab.set(value);
  }
  
  
  // handleNodeClick(parent: LineData, data: MetricsData, param: Param) {
    // if (parent.levelData) {
    //   this.setMetricsData(data);
    //   // console.log(`'''parent [param]'''`, param);
    //   this.setParam(param);
    //   this.setSelect(null);
    // } else {
      // if (data.dimensionFilters) {
      //   const { value } = data.dimensionFilters[0].value[0];
      //   const { extendName } = data.dimensionFilters[0];

      //   this.setParam({ dimension: [{ extendName }] });
      //   this.setSelect(value);
      // } else {
      //   this.setSelect(null);
      // }

      // console.log(`'''child [param]'''`, param);
      // this.setMetricsData(data); 
    // }
    
    // this._scrollToAnchor();
  // }


  scrollToAnchor() {
    try {
      const { top } = this.anchor().nativeElement.getBoundingClientRect()
      
      window.scrollTo({ top: window.scrollY + top - 64, behavior: 'smooth' });
    } catch (error) {
      console.error('anchor is undefined');
    }
  }

}
