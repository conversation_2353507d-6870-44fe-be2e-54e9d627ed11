import { DecimalPipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, input } from '@angular/core';

@Component({
  selector: 'app-flow-node-value',
  template: `
    @if (value() === undefined || value() === null) {
      <span>-</span>
    }
    @else {
      @switch (type()) {
        @case('num') { <span>{{value() | number: '1.0-2'}}</span> }
        @case('rate') { <span>{{(value() * 100) | number:'1.2-2'}}%</span> }
        @default     { <span>{{value() | number: '1.0-2'}}</span> }
      }
    }
  `,
  host: {
    class: 'leading-none cursor-pointer text-shadow-sm'
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    DecimalPipe,
  ],
})
export class FlowNodeValueComponent {

  type = input<'rate' | 'frequency' | 'num'>();
  value = input<number>();

}
