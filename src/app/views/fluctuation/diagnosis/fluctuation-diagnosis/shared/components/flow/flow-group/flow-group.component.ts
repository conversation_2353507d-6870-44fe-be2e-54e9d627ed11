import { ChangeDetectionStrategy, Component, model } from '@angular/core';

@Component({
  selector: 'app-flow-group',
  template: `
    <ng-content select="app-flow-group-expander" />
    @if (visible()) {
      <ng-content />
    }
  `,
  host: {
    class: 'pl-8'
  },
  styleUrl: './flow-group.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [],
})
export class FlowGroupComponent {

  visible = model(true);
  
}
