import { ChangeDetectionStrategy, Component, HostListener, inject } from '@angular/core';
import { IconMinusCircleComponent, IconPlusCircleComponent } from '@shared/modules/icons';
import { FlowPanelComponent } from '../flow-panel/flow-panel.component';
import { FlowComponent } from '../flow.component';
import { PAGE_NAME } from '@common/directive';
import { BuriedPointService } from '@common/service';
import { FluctuationDiagnosisService } from '@views/fluctuation/diagnosis/fluctuation-diagnosis/fluctuation-diagnosis.service';
import { FlowService } from '../flow.service';
import { Sleep } from '@common/decorator';

@Component({
  selector: 'app-flow-node-expander',
  template: `
    @if (parent.visible()) {
      <MinusCircleIcon />
    } @else {
      <PlusCircleIcon />
    }
  `,
  host: {
    class: 'cursor-pointer hover:bg-black/5 rounded-sm duration-300 transition-colors'
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    IconPlusCircleComponent,
    IconMinusCircleComponent,
  ],
})
export class FlowNodeExpanderComponent {

  readonly page_name = inject(PAGE_NAME);
  readonly flowService = inject(FlowService);
  readonly service = inject(FluctuationDiagnosisService);
  readonly buriedPointService = inject(BuriedPointService);
  readonly parent = inject(FlowPanelComponent);
  readonly root = inject(FlowComponent);

  @HostListener('click')
  @Sleep(100)
  click() {
    const key = this.root.token();
    const { tabName, tabSubName } = this.service.config()[key];
    const { showName } = this.flowService.metrics();
    const button_type = this.parent.visible() ? 1 : 2;
    
    console.log('埋点上报：点击 -> 波动诊断指标卡展开/收起按钮');
    this.buriedPointService.addStat('dida_dpm_caerus_diagnose_indicator_add_click', {
      page_name: this.page_name,
      indicator_name: showName,
      tab_name: tabName,
      sub_tab_name: tabSubName,
      button_type,
    });

    this.parent.visible.update(state => !state);
  }

}
