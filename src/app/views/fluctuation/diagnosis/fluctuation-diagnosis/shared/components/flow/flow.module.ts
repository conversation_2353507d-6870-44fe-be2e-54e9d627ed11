import { NgModule } from '@angular/core';

import { FlowComponent } from './flow.component';
import { FlowGroupComponent } from './flow-group/flow-group.component';
import { FlowPanelComponent } from './flow-panel/flow-panel.component';
import { FlowGroupExpanderComponent } from './flow-group-expander/flow-group-expander.component';
import { FlowNodeExpanderComponent } from './flow-node-expander/flow-node-expander.component';
import { FlowNodeComponent } from './flow-node/flow-node.component';
import { FlowNodeTitleComponent } from './flow-node/flow-node-title.component';
import { FlowNodeValueComponent } from './flow-node/flow-node-value.component';
import { FlowNodeSymbolComponent } from './flow-node-symbol/flow-node-symbol.component';
import { FlowNodeDiffComponent } from './flow-node/flow-node-diff.component';
import { FlowPanelsComponent } from './flow-panels/flow-panels.component';



@NgModule({
  imports: [
    FlowComponent,
    FlowGroupComponent,
    FlowPanelComponent,
    FlowNodeComponent,
    FlowNodeTitleComponent,
    FlowNodeValueComponent,
    FlowNodeSymbolComponent,
    FlowNodeDiffComponent,
    FlowPanelsComponent,
    FlowNodeExpanderComponent,
    FlowGroupExpanderComponent,
  ],
  exports: [
    FlowComponent,
    FlowGroupComponent,
    FlowPanelComponent,
    FlowNodeComponent,
    FlowNodeTitleComponent,
    FlowNodeValueComponent,
    FlowNodeSymbolComponent,
    FlowNodeDiffComponent,
    FlowPanelsComponent,
    FlowNodeExpanderComponent,
    FlowGroupExpanderComponent,
  ]
})
export class FlowModule { }
