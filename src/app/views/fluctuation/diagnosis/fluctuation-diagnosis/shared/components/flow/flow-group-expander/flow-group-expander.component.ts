import { ChangeDetectionStrategy, Component, HostListener, inject } from '@angular/core';
import { IconMinusCircleComponent, IconPlusCircleComponent } from '@shared/modules/icons';
import { FlowGroupComponent } from '../flow-group/flow-group.component';

@Component({
  selector: 'app-flow-group-expander',
  template: `
    @if (parent.visible()) {
      <MinusCircleIcon />
    } @else {
      <PlusCircleIcon />
    }
  `,
  host: {
    class: 'cursor-pointer hover:bg-black/5 rounded-sm duration-300 transition-colors'
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    IconPlusCircleComponent,
    IconMinusCircleComponent,
  ],
})
export class FlowGroupExpanderComponent {

  parent = inject(FlowGroupComponent);

  @HostListener('click')
  click() {
    this.parent.visible.update(state => !state);
  }

}
