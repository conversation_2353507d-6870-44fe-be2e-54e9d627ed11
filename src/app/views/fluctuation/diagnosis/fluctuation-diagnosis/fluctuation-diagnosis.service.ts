import { Injectable, signal } from '@angular/core';
import { FluctuationAnalysisConfigVo } from '@api/caerus/model';

export const CONFIG_TOKEN = Symbol('config_token');

@Injectable()
export class FluctuationDiagnosisService {

  config = signal<{[key: string]: FluctuationAnalysisConfigVo}>(null);
  readonly colors = ['orange-300', 'rose-300', 'orange-200', 'teal-500/50', 'sky-300', 'teal-700/70'];

}
