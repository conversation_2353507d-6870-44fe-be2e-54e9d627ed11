import { XAxisOptions, YAxisOptions } from 'highcharts'
import { getCategories, getNumberFields, getPercentFields, isEveryElementPercent } from '@common/chart'
import { BaseHighCharts, SeriesItem } from '@common/chart/highcharts'
import {
  createPoint,
  createValueElement,
  fillEmpty,
  getMaxLength,
  handleLegendItemClick,
  isEmpty,
  toDecimals,
} from '@common/function'
import { QueryOutputHeaderVo, QueryOutputVo } from '@api/query-engine/model'
import { sortBy } from 'lodash'

function removeUppercaseLetters(arr: any[]): any[] {
  return arr.map(item => {
    if (item.series && item.series.name) {
      item.series.name = item.series.name.replace(/[A-Z]/g, '')
    }
    return item
  })
}

function removeUppercaseLettersSeries(arr: any[]): any[] {
  return arr.map(item => {
    if (item.name) {
      item.name = item.name.replace(/[A-Z]/g, '')
    }
    return item
  })
}

function tooltipFormatter(that: MultipleXAxis) {
  return function () {
    const result = []
    const map = new Map()
    const params = this.points.sort(that.sortFn)

    params
      .filter(item => item.series.xAxis.index === 0)
      .forEach(item => {
        map.set(item.series.name, [])
      })

    params.forEach(item => {
      if (map.has(item.series.name)) {
        let arr = map.get(item.series.name) as any[]

        arr.push(item)
        arr = arr.sort((a, b) => a.series.xAxis.index - b.series.xAxis.index)
      }
    })

    const merged = [...map.values()].flat(1)
    console.log('[merged]', merged)
    console.log(
      '[names]',
      merged.map(item => item.series.name)
    )
    console.log(
      '[xAxisIndex]',
      merged.map(item => item.series.xAxis.index)
    )
    console.log(
      '[values]',
      merged.map(item => item.y)
    )

    result.push('<table class="text-sm">')

    const getWeekByDate = (input: string) => {
      const isDateStr = /\d{4}-\d{2}-\d{2}/.test(input)
      const weeks = ['周日', '周一', '周二', '周三', '周四', '周五', '周六']
      const day = new Date(input.replace(/-/g, '/')).getDay()

      return isDateStr && weeks[day]
    }

    const getAxis = (index: number) => {
      return merged.find(item => item.series.xAxis.index === index)
    }

    const arr = sortBy(merged, [
      function (m) {
        return m.series.name
      },
    ])
    arr.forEach((point, index) => {
      const {
        series: {
          name: seriesName,
          yAxis: { index: yAxisIndex },
          xAxis: { index: xAxisIndex },
        },
        y: value,
        color,
      } = point
      const { isPercent } = that.series.find(item => item.name === seriesName)
      const previousItem = merged[index - 1]
      const currentValue = previousItem?.y
      const previousValue = value
      const ratioValue = toDecimals((currentValue - previousValue) / previousValue)
      const diffValue = currentValue - previousValue
      const isCompareSeries = previousItem?.series?.name === seriesName
      const hasCompare = getAxis(1) !== null && getAxis(1) !== undefined
      const VALUE =
        yAxisIndex === 1 || isPercent
          ? Number.isFinite(value)
            ? value + '%'
            : '-'
          : Number.isFinite(value)
            ? Intl.NumberFormat().format(value)
            : '-'

      const DIFF_VALUE =
        yAxisIndex === 1 || isPercent
          ? createValueElement(diffValue, '{n}pp')
          : Number.isFinite(ratioValue)
            ? createValueElement(ratioValue, '{n}%')
            : '--'

      if (index === 0) {
        const baseAxis = getAxis(0)
        const compareAxis = getAxis(1)
        const x0 = baseAxis.x
        const x1 = compareAxis?.x
        const w0 = getWeekByDate(x0)
        const w1 = getWeekByDate(x1)

        result.push(`
          <thead>
            <tr class="border-b border-gray-200 py-2">
              <td>指标名称</td>
              <td class="text-center px-1 xAxis-index-${merged[0].series.xAxis.index}">当前期 <br /> (${x0}${
                w0 ? '<span class="flex-1 min-w-0 w-1"></span>' : ''
              }${w0 || ''})</td>
              ${
                hasCompare
                  ? `<td class="text-center px-1 xAxis-index-${merged[1].series.xAxis.index}">对比期 <br /> (${x1}${
                      w1 ? '<span class="flex-1 min-w-0 w-1"></span>' : ''
                    }${w1 || ''})</td>`
                  : ''
              }
              ${hasCompare ? `<td class="text-center px-3">较对比期</td>` : ''}
            </tr>
          </thead>
        `)
      }

      if (!isCompareSeries) {
        result.push('<tr>')
        result.push(
          `<td class="pr-2" style="color:${color}">${createPoint(color)}   ${seriesName
            .split('_userDefExtendName')
            [seriesName.split('_userDefExtendName').length - 1].replace('<', '&lt;')}:: </td>`
        )
        result.push(`<td class="${hasCompare ? 'text-right' : 'text-center'}">${VALUE}</td>`)
      }

      if (isCompareSeries) {
        result.push(`<td class="text-right">${VALUE}</td>`)
        result.push(`<td class="text-right">${DIFF_VALUE}</td>`)
        result.push('</tr>')
      }
    })

    result.push('</table>')

    return result.join('')
  }
}

class xAxisItem {
  categories: string[]
  opposite: boolean
  tickInterval = 1
  tickWidth = 1
  tickColor = '#ccd6eb'
  lineColor = '#ccd6eb'
  gridLineColor = '#e6e6e6'
  crosshair = true
  linkedTo: number
  labels = {
    useHTML: true,
    formatter: function () {
      if (/\d{4}-\d{2}-\d{2}/.test(this.value)) {
        const day = new Date(this.value.replace(/-/g, '/')).getDay()
        if (day === 0 || day === 6) {
          return `<span class="text-primary font-semibold">${this.value}</span>`
        }
      }

      return this.value
    },
  }

  constructor({ categories, opposite }: Partial<xAxisItem>) {
    categories && (this.categories = categories)
    opposite && (this.opposite = opposite)
  }
}

export class MultipleXAxis extends BaseHighCharts {
  xAxis: XAxisOptions[] = []
  yAxis: YAxisOptions[] = [
    {
      title: { text: '' },
      labels: { format: undefined },
      gridLineWidth: 1,
      gridLineColor: '#e6e6e6',
    },
  ]

  plotOptions = {
    series: {
      turboThreshold: 999999999,
      marker: {
        radius: 2,
        symbol: 'circle',
      },
    },
  }

  /**
   * Constructs a new instance of the LineMultipleXAxis class.
   *
   * @param properties - The properties used to initialize the LineMultipleXAxis instance.
   * @throws {string} Throws an error if the x-axis is empty.
   */
  constructor(properties: QueryOutputVo) {
    super()

    const { headers, data, compareData } = properties
    const primary_xaxis = getCategories(headers, data)
    const secondary_xaxis = getCategories(headers, compareData)
    const primary_series = this.getSeries(headers, data, 0)
    const secondary_series = this.getSeries(headers, compareData, 1)
    const max_xaxis_length = getMaxLength(primary_xaxis, secondary_xaxis)
    const [percentFields] = getPercentFields(headers)
    const isEveryFieldPercent = isEveryElementPercent(headers)

    if (isEmpty(primary_xaxis)) {
      throw 'x轴不能为空'
    }

    if (isEveryFieldPercent) {
      this.yAxis = [
        {
          title: { text: '' },
          labels: { format: '{text}%' },
          gridLineWidth: 1,
          gridLineColor: '#e6e6e6',
        },
      ]
    } else if (percentFields) {
      this.yAxis.push({
        title: { text: '' },
        labels: { format: '{text}%' },
        gridLineWidth: 1,
        gridLineColor: '#e6e6e6',
        opposite: true,
      })
    }

    this.setCategories([fillEmpty(max_xaxis_length)(primary_xaxis), fillEmpty(max_xaxis_length)(secondary_xaxis)])

    primary_series.forEach((item, index) => {
      primary_series[index].data.length = max_xaxis_length
    })

    secondary_series.forEach((item, index) => {
      secondary_series[index].data.length = max_xaxis_length
    })

    const arr = [...primary_series, ...secondary_series]
    const series = sortBy(arr, [
      function (a) {
        return a.name
      },
    ])
    this.setSeries(series)
    // this.colors.length = this.series.length / 2;
    this.colors.length = Math.ceil(this.series.length / 2)
    this.legend.verticalAlign = 'top'
    this.chart.type = 'spline'
  }

  getSeries(headers: { [key: string]: QueryOutputHeaderVo }, data: { [key: string]: string }[], index?: number) {
    const numberFields = getNumberFields(headers)
    const isEveryFieldPercent = isEveryElementPercent(headers)
    const source = ['当前', '对比']

    if (!data) {
      throw `${source[index] ?? ''}日期无数据`
    }

    return numberFields.map(key => {
      const { aliasName, dataUnit, extendName } = headers[key]
      const series = new SeriesItem()

      if (index > 0) {
        series.dashStyle = 'Dash'
        series.lineWidth = 1
        series.linkedTo = `${key}`
      }

      if (dataUnit === '%') {
        series.isPercent = true
      }

      if (dataUnit === '%' && !isEveryFieldPercent) {
        series.yAxis = 1
      }

      series.xAxis = index

      series.name = aliasName
      series.data = data.map(item => {
        const value = Number(item[key])

        if (item[key] === null) {
          return null
        }

        if (dataUnit === '%') {
          return toDecimals(value)
        }

        return value
      })

      return series
    })
  }

  /**
   * Sets the categories for the multiple x-axis.
   *
   * @param values - The array of string arrays representing the categories.
   */
  setCategories(values: string[][]): void {
    values.forEach((categories, index) => {
      const item = new xAxisItem({
        categories,
        opposite: index > 0,
      })

      if (index > 0) {
        item.linkedTo = 0
      }

      this.xAxis.push(item)
    })
  }

  override getOption() {
    const value = this

    return {
      ...value,
      tooltip: {
        ...value.tooltip,
        formatter: tooltipFormatter(this),
      },
      plotOptions: {
        ...value.plotOptions,
        series: {
          ...value.plotOptions.series,
          // events: {
          //   legendItemClick: handleLegendItemClick(this),
          // }
        },
      },
    }
  }
}
