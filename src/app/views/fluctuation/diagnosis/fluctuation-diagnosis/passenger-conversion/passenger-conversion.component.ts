import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  effect,
  ElementRef,
  inject,
  signal,
  viewChild,
} from '@angular/core'
import { filter, take } from 'rxjs'

import { isNotNull } from '@common/function'
import { BuriedPointService } from '@common/service'
import { IntersectionDirective, PAGE_NAME } from '@common/directive'
import { TabPanelComponent } from '@shared/modules/headless/tabs'

import { FlowModule, FlowComponent, FlowService } from '../shared/components/flow'
import { PassengerDistributionComponent } from './passenger-distribution/passenger-distribution.component'
import { PassengerFluctuateComponent } from './passenger-fluctuate/passenger-fluctuate.component'
import { PassengerTrendComponent } from './passenger-trend/passenger-trend.component'
import { FluctuationDiagnosisService } from '../fluctuation-diagnosis.service'

@Component({
  selector: 'app-passenger-conversion',
  template: `
    <div class="pb-3">
      <div class="flex items-center justify-center w-full h-7.5 px-5 -translate-y-full">
        <span class="font-black text-base">业务链路定位</span>
        <div class="flex-1"></div>
        <span class="font-black invisible">业务链路定位</span>
      </div>
      <div class="text-xs text-neutral-400 mt-[-20px] px-5">
        说明：业务转化链路部分的卡片皆可点击，点击后可联动下方的趋势图切换指标。
      </div>
    </div>

    <app-flow [token]="source()" appIntersection (visible)="onVisible(5)" />

    <div #anchor id="anchor-fluctuation-reason" class="mt-10">
      <header class="relative flex items-center p-5">
        <span class="font-black text-base">
          <strong class="text-primary">{{ flowService.metrics()?.showName }}</strong>
          -细分定位
        </span>
      </header>
      <div class="grid grid-cols-12 gap-10">
        <!-- <app-passenger-distribution class="col-span-6 min-h-116" [class.hidden]="flowService.metrics()?.showType !== 'num'" /> -->
        <!-- <app-passenger-fluctuate    class="col-span-6 min-h-116" [class.hidden]="flowService.metrics()?.showType !== 'num'" /> -->
        <app-passenger-trend class="col-span-12 min-h-116" />
      </div>
    </div>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    FlowModule,
    // PassengerDistributionComponent,
    // PassengerFluctuateComponent,
    PassengerTrendComponent,
    IntersectionDirective,
  ],
  providers: [FlowService],
})
export class PassengerConversionComponent implements AfterViewInit {
  readonly page_name = inject(PAGE_NAME)
  readonly flowService = inject(FlowService)
  readonly service = inject(FluctuationDiagnosisService)
  readonly buriedPointService = inject(BuriedPointService)
  readonly tabPanel = inject(TabPanelComponent)

  flow = viewChild(FlowComponent)
  anchor = viewChild('anchor', { read: ElementRef })
  source = signal('fluctuation_c_pass_fourca_user_type')

  constructor() {
    effect(() => {
      if (this.tabPanel.isActive() && this.service.config()) {
        const filter = this.service.config()[this.source()].levelData.param.filter[0]

        this.flowService.setTab(filter)
        this._subscribeToMetricsDataListChange()
      }
    })
  }

  ngAfterViewInit(): void {
    this.tabPanel.isActive$
      .pipe(
        filter(isActive => isActive),
        take(1)
      )
      .subscribe(() => {
        this.flowService.setAnchor(this.anchor())
        this._subscribeToMetricsDataListChange()
      })
  }

  private _subscribeToMetricsDataListChange() {
    this.flow()
      .metricsDataList$.pipe(filter(isNotNull), take(1))
      .subscribe(data => {
        this.flowService.setMetrics(data[0])
        this.flowService.setParam(this.flow().param())
      })
  }

  onVisible(graph_position: number) {
    console.log('曝光埋点上报:页面内的图表（分区）', graph_position)
    this.buriedPointService.addStat('dida_dpm_caerus_indicator_graph_exposure', {
      page_name: this.page_name,
      graph_position,
    })
  }
}
