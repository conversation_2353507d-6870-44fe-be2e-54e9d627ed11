import { AfterViewInit, ChangeDetectionStrategy, Component, inject } from '@angular/core'

import { CaerusApiService } from '@api/caerus'
import { BuriedPointService } from '@common/service'
import { PAGE_NAME } from '@common/directive'
import { HeadingComponent } from '@shared/components/heading'
import { TabsModule } from '@shared/modules/headless'

import { SceneAnalysisComponent } from './scene-analysis/scene-analysis.component'
import { MileageAnalysisComponent } from './mileage-analysis/mileage-analysis.component'
import { PassengerConversionComponent } from './passenger-conversion/passenger-conversion.component'
import { DriverConversionComponent } from './driver-conversion/driver-conversion.component'
import { ChannelAnalysisComponent } from './channel-analysis/channel-analysis.component'
import { FluctuationDiagnosisService } from './fluctuation-diagnosis.service'

@Component({
  selector: 'app-fluctuation-diagnosis',
  template: `
    <div class="px-5 mt-5">
      <app-heading [title]="'波动定位'" />
      <app-tab-group class="relative block px-2 -translate-y-10 z-10">
        <app-tab-list
          class="relative left-1/2  -translate-x-1/2 inline-flex items-center gap-x-0.5 mx-auto p-0.5 bg-[#ebebec]"
        >
          <app-tab
            class="dida-radio-arrow flex-none px-5 w-auto"
            activeClass="active"
            (mouseup)="onTabClick('自有/外输分析')"
          >
            自有/外输分析
          </app-tab>
          <app-tab
            class="dida-radio-arrow flex-none px-5 w-auto"
            activeClass="active"
            (mouseup)="onTabClick('自有乘客转化链路')"
          >
            自有乘客转化链路
          </app-tab>
          <app-tab
            class="dida-radio-arrow flex-none px-5 w-auto"
            activeClass="active"
            (mouseup)="onTabClick('车主转化链路')"
          >
            车主转化链路
          </app-tab>

          <app-tab
            class="dida-radio-arrow flex-none px-5 w-auto"
            activeClass="active"
            (mouseup)="onTabClick('城际/市内、里程分析')"
          >
            城际/市内、里程分析
          </app-tab>
          <app-tab
            class="dida-radio-arrow flex-none px-5 w-auto"
            activeClass="active"
            (mouseup)="onTabClick('场景分析')"
          >
            场景分析
          </app-tab>
          <app-tab-ink-bar class="h-7 bottom-unset bg-linear-to-r from-[#465867] to-[#1E2C3A] rounded-md" />
        </app-tab-list>
        <app-tab-panels class="block py-10">
          <app-tab-panel>
            <app-channel-analysis />
          </app-tab-panel>
          <app-tab-panel>
            <app-passenger-conversion />
          </app-tab-panel>
          <app-tab-panel>
            <app-driver-conversion />
          </app-tab-panel>

          <app-tab-panel>
            <app-mileage-analysis />
          </app-tab-panel>
          <app-tab-panel>
            <app-scene-analysis />
          </app-tab-panel>
        </app-tab-panels>
      </app-tab-group>
    </div>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    TabsModule,
    HeadingComponent,
    ChannelAnalysisComponent,
    MileageAnalysisComponent,
    SceneAnalysisComponent,
    PassengerConversionComponent,
    DriverConversionComponent,
  ],
  providers: [FluctuationDiagnosisService],
})
export class FluctuationDiagnosisComponent implements AfterViewInit {
  readonly page_name = inject(PAGE_NAME)
  readonly buriedPointService = inject(BuriedPointService)
  readonly service = inject(FluctuationDiagnosisService)
  readonly apiService = inject(CaerusApiService)

  ngAfterViewInit(): void {
    this.apiService.fetchFluctuationAnalysisConfigAll().subscribe(res => {
      if (res.data) {
        this.service.config.set(res.data)
      }
    })
  }

  onTabClick(tab_name: string) {
    console.log('埋点上报：点击 -> tab按钮', tab_name, 5)
    this.buriedPointService.addStat('dida_dpm_caerus_fluctuation_diagnose_tab_click', {
      page_name: this.page_name,
      tab_position: 5,
      tab_name,
    })
  }
}
