import { AfterViewInit, ChangeDetectionStrategy, Component, computed, DestroyRef, inject, signal, viewChild } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { combineLatest, debounceTime, finalize } from 'rxjs';

import { SwitchMap } from '@common/decorator';
import { BuriedPointService } from '@common/service';
import { QueryEngineApiService } from '@api/query-engine';
import { QueryInputVo, QueryOutputVo } from '@api/query-engine/model';
import { IntersectionDirective, PAGE_NAME } from '@common/directive';
import { MetricsData, MetricsItem } from '@api/caerus/model';
import { QueryEngineFormService } from '@common/service/query-engine';
import { isNotNull, sleep } from '@common/function';
import { LineSpinComponent } from '@shared/components/line-spin';
import { GraphComponent } from '@shared/components/graph';
import { FlowService } from '../../shared/components/flow';
import { BarStacked } from '../../shared/lib/bar-stack';
import { Bar, generateSeriesItem } from '../../shared/lib';
import { sortBy } from '../../shared/lib/helpers';


@Component({
  selector: 'app-driver-distribution',
  template: `
    <div class="flex flex-col h-full" appIntersection (visible)="onVisible()">
      <header class="text-center text-sm space-x-1">
        <strong>[{{title()}}]</strong>
        <span class="inline-block text-neutral-500 scale-90">{{showName()}}</span>
      </header>
      <div class="flex-1 min-h-0 relative flex items-center justify-center text-xs">
        @if (loading()) {
          <app-line-spin />
        }
        @else {
          @if (option()) {
            <app-graph [options]="option()" />
          }
          @else if (errorMessage()) {
            <span>{{errorMessage()}}</span>
          }
          @else {
            <span></span>
          }
        }
      </div>
    </div>
  `,
  host: {
    class: 'relative pt-2.5 rounded-xl',
    '[class.bg-neutral-100]': 'errorMessage()',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    GraphComponent,
    LineSpinComponent,
    IntersectionDirective,
  ],
})
export class DriverDistributionComponent implements AfterViewInit {

  readonly page_name = inject(PAGE_NAME);
  readonly destroyRef = inject(DestroyRef);
  readonly formService = inject(QueryEngineFormService);
  readonly apiService = inject(QueryEngineApiService);
  readonly buriedPointService = inject(BuriedPointService);
  readonly flowService = inject(FlowService);
  
  highChartsRef = viewChild(GraphComponent);

  showName = signal('');
  title = computed(() => {
    const name = this._dimensionNameMap.get(this.dimensionName()) || '';
    return `${name}分布`;
  });
  option = signal(null);
  errorMessage = signal<string>(null);
  loading = signal(false);

  _dimensionNameMap = new Map([
    ['cdriver_abc_type', 'ABC车主'],
    ['cdriver_fourca_user_type', '车主四类用户'],
  ]);


  metricsName = signal<string>(null);
  dimensionName = signal<string>(null);
  
  ngAfterViewInit() {
    combineLatest([
      this.formService.form.valueChanges,
      this.flowService.param$,
      this.flowService.metrics$,
      this.flowService.tab$,
      this.flowService.filter$,
    ])
    .pipe(
      debounceTime(100),
      takeUntilDestroyed(this.destroyRef)
    ).subscribe(([body, param, metrics]) => {
      if (isNotNull(body, param, metrics)) {
        this.showName.set(metrics.parentShowName);
        this.metricsName.set(metrics.extendName);
        this.dimensionName.set(param.dimension[0].extendName);
        this.fetchMetrics(metrics, param.dimension);
      }
    })
  }

  
  @SwitchMap()
  fetchMetrics({ extendName }: MetricsData, dimension?: MetricsItem[]) {
    const body: QueryInputVo = this.formService.form.getRawValue();
    const tabFilter = this.flowService.tab();

    if (dimension) {
      body.dimensions = dimension;
    }

    if (tabFilter) {
      body.filter.items = body.filter.items.concat(tabFilter);
    }

    body.metrics = [{ extendName }];

    this.loading.set(true);
    return this.apiService.search(body).pipe(
      finalize(() => this.loading.set(false))
    ).subscribe(res => {
      // console.log('[fetchMetrics]', res.data);
      if (res.data?.data) {
        this._setChartData(res.data);
        this.errorMessage.set(null);
      }
      else {
        console.error('[错误]', res.error);
        this.errorMessage.set(res.error);
        this.option.set(null);
      }
    })
  }


  /**
   * Sets the chart data for the channel fluctuate component.
   * 
   * @param data - The data for the chart.
   * @param compareData - The comparison data for the chart.
   * @param totalValue - The total value for the chart.
   */
  private async _setChartData({ headers, data, compareData }: QueryOutputVo) {
    const { showType, extendName } = this.flowService.metrics();
    const filter = this.flowService.filter();
    const selected = filter[0]?.value[0]?.value;

    this.option.set(null);
    await sleep(100);

    if (showType === 'num') {
      const chart = new BarStacked();
      chart.colors = ['#5087EC', '#68BBC4', '#58A55C', '#F2BD42', '#EE752F'];
      chart.setCategories(['当前期', '对比期']);

      const types = (
        this.dimensionName() === 'cdriver_abc_type'
        ? ['A车主', 'B车主', 'C车主', '未覆盖车主']
        : ['新增车主','活跃车主','沉默车主','流失车主']
      );

      const series = chart
        .getSeries(headers, data.concat(compareData))
        .sort(sortBy(types.reverse()));
        
      chart.setSeries(series);

      if (selected) {
        const index = types.indexOf(selected);

        chart.colors = chart.colors.map((color, i) => {
          if (index === i) {
            return color;
          } else {
            return '#eee';
          }
        })
      }

      chart.plotOptions.bar.allowPointSelect = true;
      chart.plotOptions.bar.cursor = 'pointer';
      chart.plotOptions.bar.states.hover.brightness = 0.05;

      this.option.set(chart.getOption());

      chart.click.subscribe(({ index, selected: state }) => {
        const reverseList = Array.from({ length: 4 }).map((item, i) => i).reverse();
        const n = reverseList[index];
        const lines = this.flowService.structure();
        const { extendName } = this.flowService.metrics();
        const metrics = lines[n]['metricsDataList'].find(item => item.extendName === extendName);
        
        if (state) {
          this.flowService.setMetrics(metrics);
          this.flowService.setFilter(metrics);
        }
      });
    } else {
      const chart = new Bar();
      let rate = 1;

      const types = (
        this.dimensionName() === 'cdriver_abc_type'
        ? ['A车主', 'B车主', 'C车主', '未覆盖车主']
        : ['新增车主','活跃车主','沉默车主','流失车主']
      );

      // planA
      chart.colors = ['#5087EC', '#68BBC4', '#58A55C', '#F2BD42', '#EE752F'];

      // chart.colors = chart.colors = ['#5087EC', '#68BBC4'];
      // chart.plotOptions.bar.colorByPoint = false;

      if (showType === 'rate') {
        rate = 100;
        chart.tooltip.valueSuffix = '%';
        chart.yAxis.labels.format = `{value}%`;
      }

      if (selected) {
        const index = types.indexOf(selected);

        chart.colors = chart.colors.map((color, i) => {
          if (index === i) {
            return color;
          } else {
            return '#ddd';
          }
        })
      }

      const _series = [
        generateSeriesItem('对比期', compareData, this.dimensionName(), extendName, rate, sortBy(types, 'cdriver_abc_type')),
        generateSeriesItem('当前期', data,        this.dimensionName(), extendName, rate, sortBy(types, 'cdriver_abc_type')),
      ];
      
      const keys = data
        .map(item => item[this.dimensionName()])
        .sort(sortBy(types, null))

      chart.series = _series;
      chart.plotOptions.bar.allowPointSelect = true;
      chart.plotOptions.bar.colorByPoint = true;
      chart.plotOptions.bar.cursor = 'pointer';
      chart.plotOptions.bar.dataLabels.enabled = true;
      chart.plotOptions.bar.dataLabels.format = '{point.title}';
      chart.tooltip.shared = true;
      chart.setCategories(keys);

      this.option.set(chart.getRawOption());

      chart.click.subscribe(({ name, selected: state }) => {
        const lines = this.flowService.structure();
        const { extendName } = this.flowService.metrics();        
        const index = types.indexOf(name);
        const metrics = lines[index]['metricsDataList'].find(item => item.extendName === extendName);
        
        if (state) {
          this.flowService.setMetrics(metrics);
          this.flowService.setFilter(metrics);
        }
      });
    }
  }

  
  onVisible() {
    console.log('波动诊断图表曝光埋点上报:', this.title());
    this.buriedPointService.addStat('dida_dpm_caerus_diagnose_graph_exposure', {
      page_name: this.page_name,
      graph_name: this.title()
    });
  }

}
