import { AfterViewInit, ChangeDetectionStrategy, Component, computed, DestroyRef, inject, signal, viewChild } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { combineLatest, debounceTime, finalize } from 'rxjs';

import { SwitchMap } from '@common/decorator';
import { BuriedPointService } from '@common/service';
import { QueryEngineApiService } from '@api/query-engine';
import { QueryEngineFormService } from '@common/service/query-engine';
import { IntersectionDirective, PAGE_NAME } from '@common/directive';
import { groupBy, isNotNull } from '@common/function';
import { MetricsData, MetricsItem } from '@api/caerus/model';
import { QueryInputVo, QueryOutputVo } from '@api/query-engine/model';
import { LineSpinComponent } from '@shared/components/line-spin';
import { GraphComponent } from '@shared/components/graph';
import { Bar, getPlotLinesLabel, SeriesItem } from '../../shared/lib';
import { FlowService } from '../../shared/components/flow';


@Component({
  selector: 'app-driver-fluctuate',
  template: `
    <div class="flex flex-col h-full" appIntersection (visible)="onVisible()">
      <header class="text-center text-sm space-x-1">
        <strong>[{{title()}}]</strong>
        <span class="inline-block text-neutral-500 scale-90">{{showName()}}</span>
      </header>
      <div class="flex-1 min-h-0 relative flex items-center justify-center text-xs">
        @if (loading()) {
          <app-line-spin />
        }
        @else {
          @if (option()) {
            <app-graph [options]="option()" />
          }
          @else if (errorMessage()) {
            <span>{{errorMessage()}}</span>
          }
          @else {
            <span></span>
          }
        }
      </div>
    </div>
  `,
  host: {
    class: 'relative pt-2.5 rounded-xl',
    '[class.bg-neutral-100]': 'errorMessage()',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    GraphComponent,
    LineSpinComponent,
    IntersectionDirective,
  ],
})
export class DriverFluctuateComponent implements AfterViewInit {

  readonly page_name = inject(PAGE_NAME);
  readonly destroyRef = inject(DestroyRef);
  readonly formService = inject(QueryEngineFormService);
  readonly apiService = inject(QueryEngineApiService);
  readonly buriedPointService = inject(BuriedPointService);
  readonly flowService = inject(FlowService);
  
  highChartsRef = viewChild(GraphComponent);

  showName = signal('');
  title = computed(() => {
    const name = this._dimensionNameMap.get(this.dimensionName()) || '';
    return `${name}波动`;
  });
  
  ownResult = signal<QueryOutputVo>(null);
  outResult = signal<QueryOutputVo>(null);

  _dimensionNameMap = new Map([
    ['cdriver_abc_type', 'ABC车主'],
    ['cdriver_fourca_user_type', '车主四类用户'],
  ]);

  metricsName = signal<string>(null);
  dimensionName = signal<string>(null);

  loading = signal(false);
  errorMessage = signal<string>(null);
  option = computed(() => {
    if (this.ownResult() && this.outResult()) {
      // console.log('[own]', this.ownResult());
      // console.log('[out]', this.outResult());
      
      const chart = new Bar();
      const own = this.ownResult();
      const out = this.outResult();
      const obj = groupBy<{[key:string]: string}>(out.data, this.dimensionName());
      const order = ['A车主', 'B车主', 'C车主', '未覆盖车主','新增车主','活跃车主','沉默车主','流失车主'];
      const keys = Object.keys(obj).sort((a, b) => {
        return order.indexOf(a) - order.indexOf(b);
      });
      
      const own_diff_value = +own.data[0][`${this.metricsName()}_DIFF`];
      const own_diff_ratio = +own.data[0][`${this.metricsName()}_DIFF_RATIO`];
      const { showType } = this.flowService.metrics();
      const { labelText, labelColor } = getPlotLinesLabel(own_diff_value, own_diff_ratio, showType);
      const filter = this.flowService.filter();
      const selected = filter[0]?.value[0]?.value;

      const values = keys.map(key => {
        const item = obj[key][0];
        const name = item[this.dimensionName()];
        const diff  = +item[`${this.metricsName()}_DIFF`];
        const ratio = +item[`${this.metricsName()}_DIFF_RATIO`];
        const result = new SeriesItem();
        
        let rate = 1;
        let flucuate_ratio = NaN;

        if (showType === 'rate') {
          rate = 100;
        }
        
        if (own.compareData) {
          const own_value = +own.compareData[0][this.metricsName()];
          flucuate_ratio = +(diff / own_value * 100).toFixed(2);
        }

        result.name = name;
        result.y = parseFloat((diff * rate).toFixed(2));
        result.diff = parseFloat((diff * rate).toFixed(2));
        result.flucuate_ratio = flucuate_ratio;
        result.ratio = ratio;
        result.unit = showType;
        return result;
      });

      if (filter && selected) {
        const index = keys.indexOf(selected);

        chart.colors = chart.colors.map((color, i) => {
          if (index === i) {
            return color;
          } else {
            return '#ddd';
          }
        })
      }
      
      chart.setCategories(keys);
      chart.setSeries('较对比期涨跌', values);
      chart.subtitle.text = labelText;
      chart.subtitle.style.color = labelColor;

      return chart.getOption();
    }

    return null;
  });

  
  ngAfterViewInit() {
    combineLatest([
      this.formService.form.valueChanges,
      this.flowService.param$,
      this.flowService.metrics$,
      this.flowService.tab$,
      this.flowService.filter$,
    ])
    .pipe(
      debounceTime(100),
      takeUntilDestroyed(this.destroyRef)
    ).subscribe(([body, param, metrics]) => {
      if (isNotNull(body, metrics, param)) {
        this.showName.set(metrics.parentShowName);
        this.metricsName.set(metrics.extendName);
        this.dimensionName.set(param.dimension[0].extendName);
        this.loading.set(true);
        this.fetchOwnResult();
        this.fetchOutResult();
      }
    })
  }


  @SwitchMap()
  fetchOwnResult() {
    const metricsData = this.flowService.metrics();

    this.ownResult.set(null);
    return this._fetchMetrics(metricsData, [], (data) => {
      this.ownResult.set(data);
    });
  }


  @SwitchMap()
  fetchOutResult() {
    const metricsData = this.flowService.metrics();
    const dimension = this.flowService.param()?.dimension || [];

    this.outResult.set(null);
    return this._fetchMetrics(metricsData, dimension, (data) => {
      this.outResult.set(data);
    });
  }


  _fetchMetrics(
    { extendName }: MetricsData,
    dimension: MetricsItem[],
    callback: (result: QueryOutputVo) => void
  ) {    
    const body: QueryInputVo = this.formService.form.getRawValue();
    const tabFilter = this.flowService.tab();

    if (dimension) {
      body.dimensions = dimension;
    }

    if (tabFilter) {
      body.filter.items = body.filter.items.concat(tabFilter);
    }

    body.metrics = [{ extendName }];

    // console.log(`%c[车主转化链路-{${dimension.length === 1 ? '其他' : '整体'}}波动图表请求参数]`, 'color: red', body);
    return this.apiService.search(body).pipe(
      finalize(() => this.loading.set(false))
    ).subscribe(res => {
      // console.log('[fetchMetrics]', res.data);
      if (res.data?.data) {
        callback(res.data);
        this.errorMessage.set(null);
      }
      else {
        console.error('[错误]', res.error);
        this.errorMessage.set(res.error);
      }
    })
  }

  
  onVisible() {
    console.log('波动诊断图表曝光埋点上报:', this.title());
    this.buriedPointService.addStat('dida_dpm_caerus_diagnose_graph_exposure', {
      page_name: this.page_name,
      graph_name: this.title()
    });
  }

}
