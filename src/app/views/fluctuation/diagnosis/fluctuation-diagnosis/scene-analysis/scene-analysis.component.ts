import { FormsModule } from '@angular/forms'
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  effect,
  ElementRef,
  inject,
  signal,
  viewChild,
} from '@angular/core'
import { filter, take } from 'rxjs'

import { isNotNull } from '@common/function'
import { BuriedPointService } from '@common/service'
import { IntersectionDirective, PAGE_NAME } from '@common/directive'
import { TabPanelComponent } from '@shared/modules/headless/tabs'
import { RadioModule } from '@shared/modules/headless'

import { FlowModule, FlowComponent, FlowService } from '../shared/components/flow'
import { SceneDistributionComponent } from './scene-distribution/scene-distribution.component'
import { SceneFluctuateComponent } from './scene-fluctuate/scene-fluctuate.component'
import { SceneTrendComponent } from './scene-trend/scene-trend.component'
import { FluctuationDiagnosisService } from '../fluctuation-diagnosis.service'

@Component({
  selector: 'app-scene-analysis',
  template: `
    <div class="flex items-center justify-center w-full h-7.5 px-5 -translate-y-full">
      <span class="font-black text-base">业务链路定位</span>
      <app-radio-group class="relative flex flex-1 items-start justify-center gap-x-3" [(ngModel)]="source">
        <app-radio
          class="line-radio text-xs"
          activeClass="active"
          value="fluctuation_c_scene_type_all"
          (mouseup)="onTabClick('全部')"
        >
          全部
        </app-radio>
        <app-radio
          class="line-radio text-xs"
          activeClass="active"
          value="fluctuation_c_scene_type_own"
          (mouseup)="onTabClick('自有渠道')"
        >
          自有渠道
        </app-radio>
        <app-radio
          class="line-radio text-xs"
          activeClass="active"
          value="fluctuation_c_scene_type_tencent"
          (mouseup)="onTabClick('腾讯外输')"
        >
          腾讯外输
        </app-radio>
      </app-radio-group>
      <span class="font-black invisible">业务链路定位</span>
    </div>
    <div class="text-xs text-neutral-400 mt-[-20px] px-5 pb-3">
      说明：业务转化链路部分的卡片皆可点击，点击后可联动下方的趋势图切换指标。
    </div>

    <app-flow [token]="source()" />

    <div #anchor id="anchor-fluctuation-reason" class="mt-10">
      <header class="relative flex items-center p-5">
        <span class="font-black text-base">
          <strong class="text-primary">{{ flowService.metrics()?.showName }}</strong>
          -细分定位
        </span>
      </header>
      <div class="grid grid-cols-12 gap-5">
        <!-- <app-scene-distribution class="col-span-6 min-h-116" [class.hidden]="flowService.metrics()?.showType !== 'num'" /> -->
        <!-- <app-scene-fluctuate    class="col-span-6 min-h-116" [class.hidden]="flowService.metrics()?.showType !== 'num'" /> -->
        <app-scene-trend class="col-span-12 min-h-116" />
      </div>
    </div>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    FormsModule,
    FlowModule,
    RadioModule,
    // SceneDistributionComponent,
    // SceneFluctuateComponent,
    SceneTrendComponent,
    // IntersectionDirective,
  ],
  providers: [FlowService],
})
export class SceneAnalysisComponent implements AfterViewInit {
  readonly page_name = inject(PAGE_NAME)
  readonly flowService = inject(FlowService)
  readonly service = inject(FluctuationDiagnosisService)
  readonly buriedPointService = inject(BuriedPointService)
  readonly tabPanel = inject(TabPanelComponent)

  flow = viewChild(FlowComponent)
  anchor = viewChild('anchor', { read: ElementRef })
  source = signal('fluctuation_c_scene_type_all')

  constructor() {
    effect(() => {
      if (this.tabPanel.isActive() && this.service.config()) {
        const filter = this.service.config()[this.source()].levelData.param.filter[0]

        this.flowService.setTab(filter)
        this._subscribeToMetricsDataListChange()
      }
    })
  }

  ngAfterViewInit(): void {
    this.tabPanel.isActive$
      .pipe(
        filter(isActive => isActive),
        take(1)
      )
      .subscribe(() => {
        this.flowService.setAnchor(this.anchor())
        this._subscribeToMetricsDataListChange()
      })
  }

  private _subscribeToMetricsDataListChange() {
    this.flow()
      .metricsDataList$.pipe(filter(isNotNull), take(1))
      .subscribe(data => {
        this.flowService.setFilter(data[0])
        this.flowService.setMetrics(data[0])
        this.flowService.setParam(this.flow().param())
      })
  }

  onVisible(graph_position: number) {
    console.log('曝光埋点上报:页面内的图表（分区）', graph_position)
    this.buriedPointService.addStat('dida_dpm_caerus_indicator_graph_exposure', {
      page_name: this.page_name,
      graph_position,
    })
  }

  onTabClick(tab_name: string) {
    console.log('埋点上报：点击 -> tab按钮', tab_name, 6)
    this.buriedPointService.addStat('dida_dpm_caerus_fluctuation_diagnose_tab_click', {
      page_name: this.page_name,
      tab_position: 6,
      tab_name,
    })
  }
}
