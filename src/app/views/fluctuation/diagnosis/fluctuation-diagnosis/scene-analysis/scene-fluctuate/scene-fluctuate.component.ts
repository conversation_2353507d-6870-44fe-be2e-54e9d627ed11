import { AfterViewInit, ChangeDetectionStrategy, Component, computed, DestroyRef, inject, signal, viewChild } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { combineLatest, debounceTime } from 'rxjs';

import { SwitchMap } from '@common/decorator';
import { BuriedPointService } from '@common/service';
import { IntersectionDirective, PAGE_NAME } from '@common/directive';
import { groupBy, isNotNull } from '@common/function';
import { QueryEngineFormService } from '@common/service/query-engine';
import { MetricsData, MetricsItem } from '@api/caerus/model';
import { QueryInputVo, QueryOutputVo } from '@api/query-engine/model';
import { QueryEngineApiService } from '@api/query-engine';
import { GraphComponent } from '@shared/components/graph';
import { LineSpinComponent } from '@shared/components/line-spin';
import { Bar, getPlotLinesLabel, SeriesItem, sortBy } from '../../shared/lib';
import { FlowService } from '../../shared/components/flow';


@Component({
  selector: 'app-scene-fluctuate',
  template: `
    <div class="flex flex-col h-full" appIntersection (visible)="onVisible()">
      <header class="text-center text-sm space-x-1">
        <strong>[{{title()}}]</strong>
        <span class="inline-block text-neutral-500 scale-90">{{showName()}}</span>
        <span class="inline-block text-neutral-500 scale-90">{{suffix()}}</span>
      </header>
      <div class="flex-1 min-h-0 relative flex items-center justify-center text-xs">
        @if (loading()) {
          <app-line-spin />
        }
        @else {
          @if (option()) {
            <app-graph [options]="option()" />
          }
          @else if (errorMessage()) {
            <span>{{errorMessage()}}</span>
          }
          @else {
            <span></span>
          }
        }
      </div>
    </div>
  `,
  host: {
    class: 'relative pt-2.5',
    '[class.rounded-xl]': 'errorMessage()',
    '[class.bg-neutral-100]': 'errorMessage()',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    GraphComponent,
    LineSpinComponent,
    IntersectionDirective,
  ],
})
export class SceneFluctuateComponent implements AfterViewInit {

  readonly page_name = inject(PAGE_NAME);
  readonly destroyRef = inject(DestroyRef);
  readonly formService = inject(QueryEngineFormService);
  readonly apiService = inject(QueryEngineApiService);
  readonly buriedPointService = inject(BuriedPointService);
  readonly flowService = inject(FlowService);
  
  highChartsRef = viewChild(GraphComponent);


  showName = signal('');
  title = signal('场景波动');
  suffix = signal('');
  ownResult = signal<QueryOutputVo>(null);
  outResult = signal<QueryOutputVo>(null);

  metricsName = signal<string>(null);
  dimensionName = signal<string>(null);

  loading = signal(false);
  errorMessage = signal<string>(null);
  option = computed(() => {
    if (this.ownResult() && this.outResult()) {
      const chart = new Bar();
      const own = this.ownResult();
      const out = this.outResult();
      const types = ['学校','场站','办公','住宅','医院','购物','酒店','旅游','地铁公交站','娱乐休闲','美食','生活服务','其他'];
      const obj = groupBy<{[key:string]: string}>(out.data, this.dimensionName());
      const keys = Object.keys(obj).sort(sortBy(types, null));
      const own_diff_value = +own.data[0][`${this.metricsName()}_DIFF`];
      const own_diff_ratio = +own.data[0][`${this.metricsName()}_DIFF_RATIO`];
      const { showType } = this.flowService.metrics();
      const { labelText, labelColor } = getPlotLinesLabel(own_diff_value, own_diff_ratio, showType);
      const filter = this.flowService.filter();
      const selected = filter[0]?.value[0]?.value;

      const values = keys.map(key => {
        const item = obj[key][0];
        const name = item[this.dimensionName()];
        const diff  = +item[`${this.metricsName()}_DIFF`];
        const ratio = +item[`${this.metricsName()}_DIFF_RATIO`];
        const result = new SeriesItem();

        let rate = 1;
        let flucuate_ratio = NaN;

        if (showType === 'rate') {
          rate = 100;
        }
        
        if (own.compareData) {
          const own_value = +own.compareData[0][this.metricsName()];
          flucuate_ratio = +(diff / own_value * 100).toFixed(2);
        }

        result.name = name;
        result.y = parseFloat((diff * rate).toFixed(2));
        result.diff = parseFloat((diff * rate).toFixed(2));
        result.flucuate_ratio = flucuate_ratio;
        result.ratio = ratio;
        result.unit = showType;
        return result;
      }).sort(sortBy(types, null));

      if (filter && selected) {
        const index = keys.indexOf(selected);

        chart.colors = chart.colors.map((color, i) => {
          if (index === i) {
            return color;
          } else {
            return '#ddd';
          }
        })
      }
      
      chart.setCategories(keys);
      chart.setSeries('较对比期涨跌', values);
      chart.subtitle.text = labelText;
      chart.subtitle.style.color = labelColor;      
      return chart.getOption();
    }

    return null;
  });
  
  
  ngAfterViewInit() {
    combineLatest([
      this.formService.form.valueChanges,
      this.flowService.param$,
      this.flowService.metrics$,
      this.flowService.tab$,
      this.flowService.filter$,
    ])
    .pipe(
      debounceTime(100),
      takeUntilDestroyed(this.destroyRef)
    ).subscribe(([body, param, metrics]) => {
      if (isNotNull(body, metrics, param)) {
        this.suffix.set('');
        this.showName.set(metrics.parentShowName);
        this.metricsName.set(metrics.extendName);
        this.dimensionName.set(param.dimension[0].extendName);
        this.loading.set(true);
        this.fetchOwnResult();
        this.fetchOutResult();
      }
    })
  }


  @SwitchMap()
  fetchOwnResult() {
    const metrics = this.flowService.metrics();

    this.ownResult.set(null);
    return this._fetchMetrics(metrics, [], (data) => {
      this.ownResult.set(data);
      this.loading.set(false);
    });
  }


  @SwitchMap()
  fetchOutResult() {
    const metrics = this.flowService.metrics();
    const dimension = this.flowService.param()?.dimension || [];

    this.outResult.set(null);
    this._fetchMetrics(metrics, dimension, (data) => {
      this.outResult.set(data);
    });
  }


  _fetchMetrics(
    { extendName }: MetricsData, 
    dimension: MetricsItem[],
    callback: (result: QueryOutputVo) => void
  ) {    
    const body: QueryInputVo = this.formService.form.getRawValue();
    const tabFilter = this.flowService.tab();
    // const filter = this.flowService.filter();

    if (dimension) {
      body.dimensions = dimension;
    }

    if (tabFilter) {
      this.suffix.set(`(${tabFilter?.value[0]?.value})`);      
      body.filter.items = body.filter.items.concat(tabFilter);
    }

    // if (filter) {
    //   body.filter.items = body.filter.items.concat(filter);
    // }

    body.metrics = [{ extendName }];

    // console.log('%c[场景波动请求参数]', 'color:red', body);
    return this.apiService.search(body).subscribe(res => {
      if (res.data?.data) {
        callback(res.data);
      }
      else {
        // this.option.set(null);
        console.error(res.error);
        this.errorMessage.set(res.error);
        this.loading.set(false);
      }
    })
  }

  
  onVisible() {
    console.log('波动诊断图表曝光埋点上报:', this.title());
    this.buriedPointService.addStat('dida_dpm_caerus_diagnose_graph_exposure', {
      page_name: this.page_name,
      graph_name: this.title()
    });
  }
  
}
