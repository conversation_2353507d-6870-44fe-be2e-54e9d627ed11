import { AfterViewInit, ChangeDetectionStrategy, Component, inject, input, signal } from '@angular/core';
import { combineLatest, debounceTime, finalize, startWith } from 'rxjs';
import { toObservable } from '@angular/core/rxjs-interop';

import { sortCategoriesFn } from '@common/function';
import { QueryEngineApiService } from '@api/query-engine';
import { QueryInputVo } from '@api/query-engine/model';
import { QueryEngineFormService } from '@common/service/query-engine';
import { LineSpinComponent } from '@shared/components/line-spin';
import { ChartComponent } from '@shared/components/chart';

import { FluctuationPerformanceComponent } from '../../fluctuation-performance.component';
import { BarChart } from './bar';


@Component({
  selector: 'app-distribution-item',
  template: `
    <div class="relative flex flex-col items-center h-100">
      @if (loading()) {
        <app-line-spin class="m-auto" />
      }
      @else {
        @if (option()) {
          <header class="text-left text-sm translate-y-6">
            <span class="inline-block text-neutral-500">{{data().showName}}</span>
          </header>
          <app-charts class="w-full" [options]="option()" />
        }
        @else if (errorMessage()) {
          <span class="text-xs">{{errorMessage()}}</span>
        }
        @else {
          <span></span>
        }
      }
    </div>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    ChartComponent,
    LineSpinComponent,
  ],
})
export class DistributionItemComponent implements AfterViewInit {
  
  readonly formService = inject(QueryEngineFormService);
  readonly apiService = inject(QueryEngineApiService);
  readonly parent = inject(FluctuationPerformanceComponent);

  data = input<{
    title: string,
    showName: string,
    extendName: string,
    theme: string[],
    metrics: any
  }>(null);

  data$ = toObservable(this.data);
  loading = signal(false);
  errorMessage = signal<string>(null);
  option = signal(null);

  ngAfterViewInit(): void {
    combineLatest([
      this.formService.form.valueChanges.pipe(startWith(null)),
      this.data$,
    ]).pipe(
      debounceTime(100)
    ).subscribe(() => {
      this.loading.set(true);
      this.query();
    })
  }

  query() {
    const input: QueryInputVo = this.formService.form.getRawValue();
    const { extendName, theme, metrics } = this.data();
    const dimensions = [{ id: null, extendName }];
    const body = { ...input, dimensions, metrics };
    let chart: BarChart = null;

    if (this.data().extendName === 'c_scene_type') {
      body.orders = [{
        "extendName": "c_scene_type",
        "type":"desc",
        "source":2
      }];
    }

    this.option.set(null);
    this.apiService.search(body).pipe(
      finalize(() => this.loading.set(false))
    ).subscribe(res => {
      if (res.data?.data) {
        try {
          const { dt, compareDt } = input;

          if (this.data().extendName === 'c_scene_type') {
            chart = new BarChart(res.data, dt, compareDt);
          } else {
            chart = new BarChart(res.data, dt, compareDt, sortCategoriesFn);
          }
          
          chart.color = theme;
          this.option.set(chart?.getOption() || null);
        }
        catch (e: any) {
          console.log(e);
          this.errorMessage.set(e);
        }
      } else {
        this.errorMessage.set(res.error || res.message);
      }
    })
  }

}
