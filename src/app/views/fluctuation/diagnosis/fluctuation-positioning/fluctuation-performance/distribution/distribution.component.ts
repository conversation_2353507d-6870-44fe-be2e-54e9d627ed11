import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, DestroyRef, inject, signal } from '@angular/core';
import { filter } from 'rxjs';

import { TabsModule } from '@shared/modules/headless';
import { isNotUndefinedOrNotNull } from '@common/function';
import { DistributionItemComponent } from './distribution-item/distribution-item.component';
import { FluctuationPerformanceComponent } from '../fluctuation-performance.component';

@Component({
  selector: 'app-distribution',
  template: `
    <app-tab-group class="relative block px-2 -translate-y-10 z-10" [selectedIndex]="index()">
      <div class="flex items-center">
        <span class="mr-auto font-black text-base">选择维度对比</span>
        <app-tab-list class="relative inline-flex items-center gap-x-0.5 p-0.5 bg-[#ebebec] rounded-lg">
          @for (item of dimensions(); track $index) {
            <app-tab class="dida-radio flex-none px-5 w-auto" activeClass="active">{{item.title}}</app-tab>
          }
        </app-tab-list>
      </div>


      <app-tab-panels>
        @for (item of dimensions(); track $index) {
          <app-tab-panel>
            <app-distribution-item [data]="item" />
          </app-tab-panel>
        }
      </app-tab-panels>
    </app-tab-group>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    TabsModule,
    DistributionItemComponent,
  ],
})
export class DistributionComponent implements AfterViewInit {

  cdr = inject(ChangeDetectorRef);
  parent = inject(FluctuationPerformanceComponent);
  destroyRef = inject(DestroyRef);

  dimensions = signal([]);
  index = signal(null);

  ngAfterViewInit(): void {
    this._subscribeToParentMetricsChange();
  }

  private _subscribeToParentMetricsChange() {
    this.parent.metric$.pipe(
      filter(isNotUndefinedOrNotNull),
      takeUntilDestroyed(this.destroyRef)
    ).subscribe((metric) => {
      console.log(metric);
      
      const { key: showName } = metric;
      const { bottomBarDimensionExtendNameList } = metric;
      
      if (/DAU/.test(showName)) {
        this.dimensions.set([]);
        this.index.set(-1);
      }
      else {
        this.dimensions.set([
          {
            title: `场景对比`,
            showName,
            extendName: bottomBarDimensionExtendNameList[1],
            theme: ['#fcca00', '#fcca00'],
            metrics: [metric], 
          },
          {
            title: `里程对比`,
            showName,
            extendName: bottomBarDimensionExtendNameList[0],
            theme: ['#68bbc4', '#68bbc4'],
            metrics: [metric], 
          }
        ]);
        this.index.set(0);
        this.cdr.markForCheck();
      }
    })
  }
}
