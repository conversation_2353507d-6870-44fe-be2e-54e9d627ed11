import { QueryOutputVo, QueryDt, QueryOutputHeaderVo } from "@api/query-engine/model";
import { createPoint, createValueElement, groupBy, isUndefinedOrNull } from "@common/function";

export class ItemStyle {
  color = 'auto';
  borderColor = '#000';
  borderWidth = 0;
  borderType = 'solid';
  borderRadius = 0;
  shadowOffsetX = 0;
  shadowOffsetY = 0;
  opacity = 1;

  decal = {
    symbol: 'rect',
    symbolSize: 1,
    symbolKeepAspect: true,
    color: 'rgba(0, 0, 0, 0.2)',
    dashArrayX: [1, 0],
    dashArrayY: [4, 3],
    dirty: false,
    backgroundColor: null,
    rotation: -0.5235987755982988,
    maxTileWidth: 512,
    maxTileHeight: 512,
  };
}

class SeriesItem {
  name: string;
  type = 'bar';
  xAxisIndex: number;
  data: number[];
  itemStyle: ItemStyle;
}



export class BarChart {
  title = {
    text: '',
    textAlign: 'left',
    left: 'center',
    textStyle: {
      fontSize: 14
    }
  };

  tooltip = {
    trigger: 'axis',
    axisPointer: { type: 'shadow' },
    confine: true,
    formatter: (params) => {
      const result = [] as string[];
      const map = new Map();

      params.forEach(item => {
        if (map.has(item.seriesName)) {
          const arr = map.get(item.seriesName) as any[];
          arr.push(item);
          arr.reverse();
        } else {
          map.set(item.seriesName, [item]);
        }
      });

      const merged = [...map.values()].flat(1);
      
      result.push('<table>');
      merged.forEach((item, index) => {
        const { seriesName, value, color } = item;
        const previousItem = merged[index-1];

        if (
          merged.length > 1 &&
          previousItem
        ) {
          const currentValue  = value;
          const previousValue = previousItem.value; 
          const ratioValue = ((currentValue - previousValue) / previousValue) * 100;
          
          result.push(`
            <tr>
              <td class="flex items-center pr-2">
                ${createPoint(color)}
                ${seriesName}: 
              </td>
              <td class="text-right">
                ${isUndefinedOrNull(value) ? '-' : Intl.NumberFormat().format(value)}
              </td>
              <td>
                ${Number.isFinite(ratioValue) ? createValueElement(ratioValue, '(较对比期 {n}%)') : '(较对比期 --)'}
              </td>
            </tr>
          `);
        } else {
          result.push(`
            <tr>
              <td class="flex items-center pr-2">
                ${createPoint(color)}
                ${seriesName}: 
              </td>
              <td class="text-right">
                ${isUndefinedOrNull(value) ? '-' : Intl.NumberFormat().format(value)}
              </td>
              <td></td>
            </tr>
          `);
        }
      })

      
      result.push('</table>');
      return result.join('');
    }
  };

  legend = {
    top: 'bottom'
  };

  color: string[];
  grid = {
    left: '3%',
    right: '4%',
    // bottom: '3%',
    containLabel: true
  };
  xAxis = { type: 'value' };
  yAxis = { type: 'category', data: [] as string[] };
  
  series: SeriesItem[];
  sortFn = (a, b) => 0;

  constructor(properties: QueryOutputVo, dt: QueryDt, compareDt: QueryDt, sortFn?: (a, b) => number) {
    if (sortFn) {
      this.sortFn = sortFn;
    }

    const { headers, data, compareData } = properties;
    const categories = this.getCategories(headers, data);
    const series = this.getSeries(headers, data, `当前期 (${dt.startTime} ~ ${dt.endTime})`);
    const compareSeries = this.getSeries(headers, compareData, `对比期 (${compareDt.startTime} ~ ${compareDt.endTime})`)
    compareSeries.itemStyle = new ItemStyle();

    this.setSeries([compareSeries, series]);
    this.setCategories(categories);
  }

  protected getColumnFields(headers: { [key: string]: QueryOutputHeaderVo }) {
    return Object.keys(headers).filter(key => {
      return headers[key].columnType === 'dimension';
    })
  }


  protected getNumberFields(headers: { [key: string]: QueryOutputHeaderVo }) {
    return Object.keys(headers).filter(key => {
      const { dateFilter, columnType } = headers[key];

      return (
        dateFilter === 0 && 
        columnType === 'metrics'
      );
    })
  }


  protected getDimensionField(headers: { [key: string]: QueryOutputHeaderVo }) {
    return Object.keys(headers).filter(key => {
      const { dateFilter, columnType } = headers[key];

      return (
        dateFilter === 0 && 
        columnType === 'dimension'
      );
    })
  }


  protected getCategories(
    headers: { [key: string]: QueryOutputHeaderVo },
    data: { [key: string]: string }[]
  ) {
    if (!data) { return }

    const columnFields = this.getColumnFields(headers);
    const categories = columnFields.map(key => data.map(item => item[key])).flat(1);

    return [...new Set(categories)].sort(this.sortFn);
  }
  

  setCategories(value: string[]) {
    this.yAxis.data = value;
  }


  setSeries(series: SeriesItem[]) {
    this.series = series;
  }


  getSeries(
    headers: { [key: string]: QueryOutputHeaderVo },
    data: { [key: string]: string }[],
    name: string
  ) {
    const [dimensionField] = this.getDimensionField(headers);
    const groupData = groupBy<{[key: string]: { [key: string]: string }}>(data, dimensionField);
    const series = new SeriesItem();
    const [numberFields] = this.getNumberFields(headers);
    
    series.name = name;
    series.data = [];
    
    Object.keys(groupData).sort(this.sortFn).forEach((key, index) => {
      const value = groupData[key][0][numberFields];
      
      series.data.push(+(Number(<any>value).toFixed(2)) || null);
    })

    return series;
  }


  getOption() {
    return this;
  }

}
