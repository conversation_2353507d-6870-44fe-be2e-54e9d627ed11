import { FormsModule } from '@angular/forms';
import { ChangeDetectionStrategy, Component, inject, signal } from '@angular/core';
import { toObservable } from '@angular/core/rxjs-interop';

import { PAGE_NAME } from '@common/directive';
import { BuriedPointService } from '@common/service';
import { HeadingComponent } from '@shared/components/heading';
import { RadioModule } from '@shared/modules/headless';
import { FluctuationPerformanceComponent } from './fluctuation-performance';
import { FluctuationAreaComponent } from './fluctuation-area';


@Component({
  selector: 'app-fluctuation-positioning',
  template: `
    <div class="px-5 mt-5">
      <app-heading [title]="'波动表现'">
        <app-radio-group ngProjectAs="[tab]" [(ngModel)]="view" class="relative w-80 flex items-start gap-x-0.5 p-0.5 bg-[#ebebec] rounded-lg">
          <app-radio class="dida-radio-new" activeClass="active" value="finish" (mouseup)="onTabClick('完单视角')">完单视角</app-radio>
          <app-radio class="dida-radio-new" activeClass="active" value="book"   (mouseup)="onTabClick('下单视角')">下单视角</app-radio>
          <app-radio-thumb [offsetX]="2" class="bg-linear-to-r from-[#465867] to-[#1E2C3A] rounded-md" />
        </app-radio-group>
      </app-heading>
      <app-fluctuation-performance />
      <app-fluctuation-area />
    </div>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    FormsModule,
    RadioModule,
    HeadingComponent,
    FluctuationPerformanceComponent,
    FluctuationAreaComponent,
  ],
})
export class FluctuationPositioningComponent {

  readonly page_name = inject(PAGE_NAME);
  readonly buriedPointService = inject(BuriedPointService);
  
  view = signal<'finish' | 'book'>('finish');
  view$ = toObservable(this.view);

  onTabClick(tab_name: string) {
    console.log('埋点上报：点击 -> tab按钮', tab_name, 2);
    this.buriedPointService.addStat('dida_dpm_caerus_fluctuation_diagnose_tab_click', {
      page_name: this.page_name,
      tab_position: 2,
      tab_name,
    });
  }

}
