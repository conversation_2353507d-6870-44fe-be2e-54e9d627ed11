import { FormsModule } from '@angular/forms'
import { NgTemplateOutlet } from '@angular/common'
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  computed,
  DestroyRef,
  effect,
  inject,
  signal,
} from '@angular/core'
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop'
import { combineLatest, debounceTime, filter, finalize, lastValueFrom, startWith } from 'rxjs'
import { NzTableModule } from 'ng-zorro-antd/table'
import { NzButtonModule } from 'ng-zorro-antd/button'
import { NzDropDownModule } from 'ng-zorro-antd/dropdown'
import { NzIconModule } from 'ng-zorro-antd/icon'
import { NzPopoverModule } from 'ng-zorro-antd/popover'
import { NzToolTipModule } from 'ng-zorro-antd/tooltip'
import { NzInputModule } from 'ng-zorro-antd/input'
import { NzModalService } from 'ng-zorro-antd/modal'
import { format } from 'date-fns'

import { SwitchMap } from '@common/decorator'
import { isPreview, isTest, isEmulator } from '@common/const'
import { isNotEmpty, isNotNull } from '@common/function'
import { CaerusApiService } from '@api/caerus'
import { QueryEngineFormService } from '@common/service/query-engine'
import { FilterItemVo } from '@api/caerus/model'
import { QueryEngineApiService } from '@api/query-engine'
import { DiagnosisComponent } from '@views/fluctuation/diagnosis'
import { SubFilterComponent } from '@views/monitor/price/components'
import { SearchPipe } from '@shared/pipes/search'
import { ValueFormatter } from '@shared/components/value-formatter'
import { IconSearchComponent } from '@shared/modules/icons'

import { FluctuationPositioningComponent } from '../../fluctuation-positioning.component'
import { FluctuationAreaComponent } from '../fluctuation-area.component'
import { BuriedPointService } from '@common/service'

@Component({
  selector: 'app-table-transform',
  templateUrl: './table-transform.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    FormsModule,
    NgTemplateOutlet,
    NzInputModule,
    NzButtonModule,
    NzIconModule,
    NzTableModule,
    NzPopoverModule,
    NzToolTipModule,
    NzDropDownModule,
    ValueFormatter,
    SubFilterComponent,
    IconSearchComponent,
  ],
  providers: [SearchPipe],
})
export class TableTransformComponent implements AfterViewInit {
  readonly destroyRef = inject(DestroyRef)
  readonly caerusApiService = inject(CaerusApiService)
  readonly apiService = inject(QueryEngineApiService)
  readonly formService = inject(QueryEngineFormService)
  readonly searchPipe = inject(SearchPipe)
  readonly modal = inject(NzModalService)
  readonly rootParent = inject(DiagnosisComponent)
  readonly root = inject(FluctuationPositioningComponent)
  readonly parent = inject(FluctuationAreaComponent)
  readonly buriedPointService = inject(BuriedPointService)

  firstRowData = signal<any>({})
  listOfData = signal([])
  listOfDisplayData = signal([])
  compareData = signal([])

  loading = signal(false)
  metrics = signal(null)
  metrics$ = toObservable(this.metrics)
  searchValue = signal<string>(null)
  visible = signal<boolean>(null)
  metricsMap = new Map()
  dimensionMap = new Map()

  listOfColumnsLevel_1 = computed(() => {
    return [
      {
        key: 'c_davg_finish_ord_cnt', /** 日均完单量 */
        colspan: 2,
      },
      {
        key: 'c_davg_finish_ord_cnt_FLUCTUATION', /** 波动占比 */
        rowspan: 2,
        className: 'text-center!',
        sortFn: (a: any, b: any) => a.c_davg_finish_ord_cnt_FLUCTUATION - b.c_davg_finish_ord_cnt_FLUCTUATION,
      },
      {
        key: 'driver_pass_active_prop', /** 车乘比 */
        colspan: 2,
      },
      {
        key: 'davg_c_pass_active_uv', /** 乘客DAU */
        colspan: 2,
      },
      {
        key: 'c_active_book_pass_rate', /** 乘客下单率 */
        colspan: 2,
      },
      {
        key: 'davg_c_pass_book_ord_cnt', /** 人均下单频次 */
        colspan: 2,
      },
      {
        key: 'c_book_reply_ord_rate', /** 下单接单率 */
        colspan: 2,
      },
      {
        key: 'c_reply_finish_ord_rate', /** 接单完单率 */
        colspan: 2,
      },
      {
        key: 'davg_c_driver_active_uv', /** 车主DAU */
        colspan: 2,
      },
      // {
      //   key: 'c_active_reply_driver_rate', /** 车主接单率 */
      //   colspan: 2,
      // },
      {
        key: 'davg_c_reply_driver_pv', /** 车主人均接单频次 */
        colspan: 2,
      },
      {
        key: 'c_book_cancel_ord_rate', /** 下单取消率 */
        colspan: 2,
      },
      {
        key: 'c_afreply_book_cancel_ord_rate', /** 下单-接单后取消率 */
        colspan: 2,
      },
      {
        key: 'c_bereply_book_cancel_ord_rate', /** 下单-接单前取消率 */
        colspan: 2,
      },
      {
        key: 'driver_c_afreply_book_cancel_ord_rate', /** 下单-接单后车主取消率 */
        colspan: 2,
      },
      {
        key: 'pass_c_afreply_book_cancel_ord_rate', /** 下单-接单后乘客取消率 */
        colspan: 2,
      },
      {
        key: 'c_afreply_reply_cancel_ord_rate', /** 接单-接单后取消率 */
        colspan: 2,
      },
    ]
  })

  listOfColumns = computed(() => {
    if (this.metrics()) {
      return [
        // 日均完单量
        {
          name: '数值',
          title: `${this.metricsMap.get('c_davg_finish_ord_cnt')?.aliasName}(数值)`,
          dataUnit: this.metricsMap.get('c_davg_finish_ord_cnt')?.dataUnit,
          key: 'c_davg_finish_ord_cnt',
          sortFn: (a: any, b: any) => a.c_davg_finish_ord_cnt - b.c_davg_finish_ord_cnt,
          sortOrder: 'descend',
          filter: this.subFilter_1,
        },
        {
          name: '涨跌幅',
          title: `${this.metricsMap.get('c_davg_finish_ord_cnt')?.aliasName}(涨跌幅)`,
          dataUnit: this.metricsMap.get('c_davg_finish_ord_cnt')?.dataUnit,
          key: 'c_davg_finish_ord_cnt_DIFF',
          sortFn: (a: any, b: any) => a.c_davg_finish_ord_cnt_DIFF - b.c_davg_finish_ord_cnt_DIFF,
          sortOrder: null,
          filter: this.subFilter_2,
        },

        /** 车乘比 */
        {
          name: '数值',
          title: `${this.metricsMap.get('driver_pass_active_prop')?.aliasName}(数值)`,
          dataUnit: this.metricsMap.get('driver_pass_active_prop')?.dataUnit,
          key: 'driver_pass_active_prop',
          sortFn: (a: any, b: any) => a.driver_pass_active_prop - b.driver_pass_active_prop,
          sortOrder: 'descend',
          filter: this.subFilter_29,
        },
        {
          name: '涨跌幅',
          title: `${this.metricsMap.get('driver_pass_active_prop')?.aliasName}(涨跌幅)`,
          dataUnit: this.metricsMap.get('driver_pass_active_prop')?.dataUnit,
          key: 'driver_pass_active_prop_DIFF',
          sortFn: (a: any, b: any) => a.driver_pass_active_prop_DIFF - b.driver_pass_active_prop_DIFF,
          sortOrder: null,
          filter: this.subFilter_30,
        },

        // 乘客DAU
        {
          name: '数值',
          title: `${this.metricsMap.get('davg_c_pass_active_uv')?.aliasName}(数值)`,
          dataUnit: this.metricsMap.get('davg_c_pass_active_uv')?.dataUnit,
          key: 'davg_c_pass_active_uv',
          sortFn: (a: any, b: any) => a.davg_c_pass_active_uv - b.davg_c_pass_active_uv,
          sortOrder: null,
          filter: this.subFilter_3,
        },
        {
          name: '涨跌幅',
          title: `${this.metricsMap.get('davg_c_pass_active_uv')?.aliasName}(涨跌幅)`,
          dataUnit: this.metricsMap.get('davg_c_pass_active_uv')?.dataUnit,
          key: 'davg_c_pass_active_uv_DIFF',
          sortFn: (a: any, b: any) => a.davg_c_pass_active_uv_DIFF - b.davg_c_pass_active_uv_DIFF,
          sortOrder: null,
          filter: this.subFilter_4,
        },

        // 乘客下单率
        {
          name: '数值',
          title: `${this.metricsMap.get('c_active_book_pass_rate')?.aliasName}(数值)`,
          dataUnit: this.metricsMap.get('c_active_book_pass_rate')?.dataUnit,
          key: 'c_active_book_pass_rate',
          sortFn: (a: any, b: any) => a.c_active_book_pass_rate - b.c_active_book_pass_rate,
          sortOrder: null,
          filter: this.subFilter_5,
          suffix: '%',
        },
        {
          name: '涨跌幅',
          title: `${this.metricsMap.get('c_active_book_pass_rate')?.aliasName}(涨跌幅)`,
          dataUnit: this.metricsMap.get('c_active_book_pass_rate')?.dataUnit,
          key: 'c_active_book_pass_rate_DIFF',
          sortFn: (a: any, b: any) => a.c_active_book_pass_rate_DIFF - b.c_active_book_pass_rate_DIFF,
          sortOrder: null,
          filter: this.subFilter_6,
          suffix: 'pp',
          step: 0.01,
        },

        // 人均下单频次
        {
          name: '数值',
          title: `${this.metricsMap.get('davg_c_pass_book_ord_cnt')?.aliasName}(数值)`,
          dataUnit: this.metricsMap.get('davg_c_pass_book_ord_cnt')?.dataUnit,
          key: 'davg_c_pass_book_ord_cnt',
          sortFn: (a: any, b: any) => a.davg_c_pass_book_ord_cnt - b.davg_c_pass_book_ord_cnt,
          sortOrder: null,
          filter: this.subFilter_7,
          step: 0.01,
        },
        {
          name: '涨跌幅',
          title: `${this.metricsMap.get('davg_c_pass_book_ord_cnt')?.aliasName}(涨跌幅)`,
          dataUnit: this.metricsMap.get('davg_c_pass_book_ord_cnt')?.dataUnit,
          key: 'davg_c_pass_book_ord_cnt_DIFF',
          sortFn: (a: any, b: any) => a.davg_c_pass_book_ord_cnt_DIFF - b.davg_c_pass_book_ord_cnt_DIFF,
          sortOrder: null,
          filter: this.subFilter_8,
          step: 0.01,
        },

        // 下单接单率
        {
          name: '数值',
          title: `${this.metricsMap.get('c_book_reply_ord_rate')?.aliasName}(数值)`,
          dataUnit: this.metricsMap.get('c_book_reply_ord_rate')?.dataUnit,
          key: 'c_book_reply_ord_rate',
          sortFn: (a: any, b: any) => a.c_book_reply_ord_rate - b.c_book_reply_ord_rate,
          sortOrder: null,
          filter: this.subFilter_9,
          suffix: '%',
        },
        {
          name: '涨跌幅',
          title: `${this.metricsMap.get('c_book_reply_ord_rate')?.aliasName}(涨跌幅)`,
          dataUnit: this.metricsMap.get('c_book_reply_ord_rate')?.dataUnit,
          key: 'c_book_reply_ord_rate_DIFF',
          sortFn: (a: any, b: any) => a.c_book_reply_ord_rate_DIFF - b.c_book_reply_ord_rate_DIFF,
          sortOrder: null,
          filter: this.subFilter_10,
          suffix: 'pp',
          step: 0.01,
        },

        // 接单完单率
        {
          name: '数值',
          title: `${this.metricsMap.get('c_reply_finish_ord_rate')?.aliasName}(数值)`,
          dataUnit: this.metricsMap.get('c_reply_finish_ord_rate')?.dataUnit,
          key: 'c_reply_finish_ord_rate',
          sortFn: (a: any, b: any) => a.c_reply_finish_ord_rate - b.c_reply_finish_ord_rate,
          sortOrder: null,
          filter: this.subFilter_11,
          suffix: '%',
        },
        {
          name: '涨跌幅',
          title: `${this.metricsMap.get('c_reply_finish_ord_rate')?.aliasName}(涨跌幅)`,
          dataUnit: this.metricsMap.get('c_reply_finish_ord_rate')?.dataUnit,
          key: 'c_reply_finish_ord_rate_DIFF',
          sortFn: (a: any, b: any) => a.c_reply_finish_ord_rate_DIFF - b.c_reply_finish_ord_rate_DIFF,
          sortOrder: null,
          filter: this.subFilter_12,
          suffix: 'pp',
          step: 0.01,
        },

        // 车主DAU
        {
          name: '数值',
          title: `${this.metricsMap.get('davg_c_driver_active_uv')?.aliasName}(数值)`,
          dataUnit: this.metricsMap.get('davg_c_driver_active_uv')?.dataUnit,
          key: 'davg_c_driver_active_uv',
          sortFn: (a: any, b: any) => a.davg_c_driver_active_uv - b.davg_c_driver_active_uv,
          sortOrder: null,
          filter: this.subFilter_13,
        },
        {
          name: '涨跌幅',
          title: `${this.metricsMap.get('davg_c_driver_active_uv')?.aliasName}(涨跌幅)`,
          dataUnit: this.metricsMap.get('davg_c_driver_active_uv')?.dataUnit,
          key: 'davg_c_driver_active_uv_DIFF',
          sortFn: (a: any, b: any) => a.davg_c_driver_active_uv_DIFF - b.davg_c_driver_active_uv_DIFF,
          sortOrder: null,
          filter: this.subFilter_14,
        },

        // 车主接单率
        // {
        //   name: '数值',
        //   title: `${this.metricsMap.get('c_active_reply_driver_rate')?.aliasName}(数值)`,
        //   dataUnit: this.metricsMap.get('c_active_reply_driver_rate')?.dataUnit,
        //   key: 'c_active_reply_driver_rate',
        //   sortFn: (a: any, b: any) => a.c_active_reply_driver_rate - b.c_active_reply_driver_rate,
        //   sortOrder: null,
        //   filter: this.subFilter_31,
        //   suffix: '%',
        // },
        // {
        //   name: '涨跌幅',
        //   title: `${this.metricsMap.get('c_active_reply_driver_rate')?.aliasName}(涨跌幅)`,
        //   dataUnit: this.metricsMap.get('c_active_reply_driver_rate')?.dataUnit,
        //   key: 'c_active_reply_driver_rate_DIFF',
        //   sortFn: (a: any, b: any) => a.c_active_reply_driver_rate_DIFF - b.c_active_reply_driver_rate_DIFF,
        //   sortOrder: null,
        //   filter: this.subFilter_32,
        //   suffix: 'pp',
        //   step: 0.01,
        // },

        // 车主人均接单频次
        {
          name: '数值',
          title: `${this.metricsMap.get('davg_c_reply_driver_pv')?.aliasName}(数值)`,
          dataUnit: this.metricsMap.get('davg_c_reply_driver_pv')?.dataUnit,
          key: 'davg_c_reply_driver_pv',
          sortFn: (a: any, b: any) => a.davg_c_reply_driver_pv - b.davg_c_reply_driver_pv,
          sortOrder: null,
          filter: this.subFilter_15,
          step: 0.01,
        },
        {
          name: '涨跌幅',
          title: `${this.metricsMap.get('davg_c_reply_driver_pv')?.aliasName}(涨跌幅)`,
          dataUnit: this.metricsMap.get('davg_c_reply_driver_pv')?.dataUnit,
          key: 'davg_c_reply_driver_pv_DIFF',
          sortFn: (a: any, b: any) => a.davg_c_reply_driver_pv_DIFF - b.davg_c_reply_driver_pv_DIFF,
          sortOrder: null,
          filter: this.subFilter_16,
          step: 0.01,
        },

        /** 下单取消率 */
        {
          name: '数值',
          title: `${this.metricsMap.get('c_book_cancel_ord_rate')?.aliasName}(数值)`,
          dataUnit: this.metricsMap.get('c_book_cancel_ord_rate')?.dataUnit,
          key: 'c_book_cancel_ord_rate',
          sortFn: (a: any, b: any) => a.c_book_cancel_ord_rate - b.c_book_cancel_ord_rate,
          sortOrder: null,
          filter: this.subFilter_17,
          suffix: '%',
        },
        {
          name: '涨跌幅',
          title: `${this.metricsMap.get('c_book_cancel_ord_rate')?.aliasName}(涨跌幅)`,
          dataUnit: this.metricsMap.get('c_book_cancel_ord_rate')?.dataUnit,
          key: 'c_book_cancel_ord_rate_DIFF',
          sortFn: (a: any, b: any) => a.c_book_cancel_ord_rate_DIFF - b.c_book_cancel_ord_rate_DIFF,
          sortOrder: null,
          filter: this.subFilter_18,
          suffix: 'pp',
          step: 0.01,
        },

        /** 下单-接单后取消率 */
        {
          name: '数值',
          title: `${this.metricsMap.get('c_afreply_book_cancel_ord_rate')?.aliasName}(数值)`,
          dataUnit: this.metricsMap.get('c_afreply_book_cancel_ord_rate')?.dataUnit,
          key: 'c_afreply_book_cancel_ord_rate',
          sortFn: (a: any, b: any) => a.c_afreply_book_cancel_ord_rate - b.c_afreply_book_cancel_ord_rate,
          sortOrder: null,
          filter: this.subFilter_19,
          suffix: '%',
        },
        {
          name: '涨跌幅',
          title: `${this.metricsMap.get('c_afreply_book_cancel_ord_rate')?.aliasName}(涨跌幅)`,
          dataUnit: this.metricsMap.get('c_afreply_book_cancel_ord_rate')?.dataUnit,
          key: 'c_afreply_book_cancel_ord_rate_DIFF',
          sortFn: (a: any, b: any) => a.c_afreply_book_cancel_ord_rate_DIFF - b.c_afreply_book_cancel_ord_rate_DIFF,
          sortOrder: null,
          filter: this.subFilter_20,
          suffix: 'pp',
          step: 0.01,
        },

        /** 下单-接单前取消率 */
        {
          name: '数值',
          title: `${this.metricsMap.get('c_bereply_book_cancel_ord_rate')?.aliasName}(数值)`,
          dataUnit: this.metricsMap.get('c_bereply_book_cancel_ord_rate')?.dataUnit,
          key: 'c_bereply_book_cancel_ord_rate',
          sortFn: (a: any, b: any) => a.c_bereply_book_cancel_ord_rate - b.c_bereply_book_cancel_ord_rate,
          sortOrder: null,
          filter: this.subFilter_21,
          suffix: '%',
        },
        {
          name: '涨跌幅',
          title: `${this.metricsMap.get('c_bereply_book_cancel_ord_rate')?.aliasName}(涨跌幅)`,
          dataUnit: this.metricsMap.get('c_bereply_book_cancel_ord_rate')?.dataUnit,
          key: 'c_bereply_book_cancel_ord_rate_DIFF',
          sortFn: (a: any, b: any) => a.c_bereply_book_cancel_ord_rate_DIFF - b.c_bereply_book_cancel_ord_rate_DIFF,
          sortOrder: null,
          filter: this.subFilter_22,
          suffix: 'pp',
          step: 0.01,
        },

        /** 下单-接单后车主取消率 */
        {
          name: '数值',
          title: `${this.metricsMap.get('driver_c_afreply_book_cancel_ord_rate')?.aliasName}(数值)`,
          dataUnit: this.metricsMap.get('driver_c_afreply_book_cancel_ord_rate')?.dataUnit,
          key: 'driver_c_afreply_book_cancel_ord_rate',
          sortFn: (a: any, b: any) => a.driver_c_afreply_book_cancel_ord_rate - b.driver_c_afreply_book_cancel_ord_rate,
          sortOrder: null,
          filter: this.subFilter_23,
          suffix: '%',
        },
        {
          name: '涨跌幅',
          title: `${this.metricsMap.get('driver_c_afreply_book_cancel_ord_rate')?.aliasName}(涨跌幅)`,
          dataUnit: this.metricsMap.get('driver_c_afreply_book_cancel_ord_rate')?.dataUnit,
          key: 'driver_c_afreply_book_cancel_ord_rate_DIFF',
          sortFn: (a: any, b: any) =>
            a.driver_c_afreply_book_cancel_ord_rate_DIFF - b.driver_c_afreply_book_cancel_ord_rate_DIFF,
          sortOrder: null,
          filter: this.subFilter_24,
          suffix: 'pp',
          step: 0.01,
        },

        /** 下单-接单后乘客取消率 */
        {
          name: '数值',
          title: `${this.metricsMap.get('pass_c_afreply_book_cancel_ord_rate')?.aliasName}(数值)`,
          dataUnit: this.metricsMap.get('pass_c_afreply_book_cancel_ord_rate')?.dataUnit,
          key: 'pass_c_afreply_book_cancel_ord_rate',
          sortFn: (a: any, b: any) => a.pass_c_afreply_book_cancel_ord_rate - b.pass_c_afreply_book_cancel_ord_rate,
          sortOrder: null,
          filter: this.subFilter_25,
          suffix: '%',
        },
        {
          name: '涨跌幅',
          title: `${this.metricsMap.get('pass_c_afreply_book_cancel_ord_rate')?.aliasName}(涨跌幅)`,
          dataUnit: this.metricsMap.get('pass_c_afreply_book_cancel_ord_rate')?.dataUnit,
          key: 'pass_c_afreply_book_cancel_ord_rate_DIFF',
          sortFn: (a: any, b: any) =>
            a.pass_c_afreply_book_cancel_ord_rate_DIFF - b.pass_c_afreply_book_cancel_ord_rate_DIFF,
          sortOrder: null,
          filter: this.subFilter_26,
          suffix: 'pp',
          step: 0.01,
        },

        /** 接单-接单后取消率 */
        {
          name: '数值',
          title: `${this.metricsMap.get('c_afreply_reply_cancel_ord_rate')?.aliasName}(数值)`,
          dataUnit: this.metricsMap.get('c_afreply_reply_cancel_ord_rate')?.dataUnit,
          key: 'c_afreply_reply_cancel_ord_rate',
          sortFn: (a: any, b: any) => a.c_afreply_reply_cancel_ord_rate - b.c_afreply_reply_cancel_ord_rate,
          sortOrder: null,
          filter: this.subFilter_27,
          suffix: '%',
        },
        {
          name: '涨跌幅',
          title: `${this.metricsMap.get('c_afreply_reply_cancel_ord_rate')?.aliasName}(涨跌幅)`,
          dataUnit: this.metricsMap.get('c_afreply_reply_cancel_ord_rate')?.dataUnit,
          key: 'c_afreply_reply_cancel_ord_rate_DIFF',
          sortFn: (a: any, b: any) => a.c_afreply_reply_cancel_ord_rate_DIFF - b.c_afreply_reply_cancel_ord_rate_DIFF,
          sortOrder: null,
          filter: this.subFilter_28,
          suffix: 'pp',
          step: 0.01,
        },
      ]
    }
    return []
  })

  subFilter_1 = signal<FilterItemVo>(null)
  subFilter_1$ = toObservable(this.subFilter_1)
  subFilter_2 = signal<FilterItemVo>(null)
  subFilter_2$ = toObservable(this.subFilter_2)

  subFilter_3 = signal<FilterItemVo>(null)
  subFilter_3$ = toObservable(this.subFilter_3)
  subFilter_4 = signal<FilterItemVo>(null)
  subFilter_4$ = toObservable(this.subFilter_4)

  subFilter_5 = signal<FilterItemVo>(null)
  subFilter_5$ = toObservable(this.subFilter_5)
  subFilter_6 = signal<FilterItemVo>(null)
  subFilter_6$ = toObservable(this.subFilter_6)

  subFilter_7 = signal<FilterItemVo>(null)
  subFilter_7$ = toObservable(this.subFilter_7)
  subFilter_8 = signal<FilterItemVo>(null)
  subFilter_8$ = toObservable(this.subFilter_8)

  subFilter_9 = signal<FilterItemVo>(null)
  subFilter_9$ = toObservable(this.subFilter_9)
  subFilter_10 = signal<FilterItemVo>(null)
  subFilter_10$ = toObservable(this.subFilter_10)

  subFilter_11 = signal<FilterItemVo>(null)
  subFilter_11$ = toObservable(this.subFilter_11)
  subFilter_12 = signal<FilterItemVo>(null)
  subFilter_12$ = toObservable(this.subFilter_12)

  subFilter_13 = signal<FilterItemVo>(null)
  subFilter_13$ = toObservable(this.subFilter_13)
  subFilter_14 = signal<FilterItemVo>(null)
  subFilter_14$ = toObservable(this.subFilter_14)

  subFilter_15 = signal<FilterItemVo>(null)
  subFilter_15$ = toObservable(this.subFilter_15)
  subFilter_16 = signal<FilterItemVo>(null)
  subFilter_16$ = toObservable(this.subFilter_16)

  subFilter_17 = signal<FilterItemVo>(null)
  subFilter_17$ = toObservable(this.subFilter_17)
  subFilter_18 = signal<FilterItemVo>(null)
  subFilter_18$ = toObservable(this.subFilter_18)

  subFilter_19 = signal<FilterItemVo>(null)
  subFilter_19$ = toObservable(this.subFilter_19)
  subFilter_20 = signal<FilterItemVo>(null)
  subFilter_20$ = toObservable(this.subFilter_20)

  subFilter_21 = signal<FilterItemVo>(null)
  subFilter_21$ = toObservable(this.subFilter_21)
  subFilter_22 = signal<FilterItemVo>(null)
  subFilter_22$ = toObservable(this.subFilter_22)

  subFilter_23 = signal<FilterItemVo>(null)
  subFilter_23$ = toObservable(this.subFilter_23)
  subFilter_24 = signal<FilterItemVo>(null)
  subFilter_24$ = toObservable(this.subFilter_24)

  subFilter_25 = signal<FilterItemVo>(null)
  subFilter_25$ = toObservable(this.subFilter_25)
  subFilter_26 = signal<FilterItemVo>(null)
  subFilter_26$ = toObservable(this.subFilter_26)

  subFilter_27 = signal<FilterItemVo>(null)
  subFilter_27$ = toObservable(this.subFilter_27)
  subFilter_28 = signal<FilterItemVo>(null)
  subFilter_28$ = toObservable(this.subFilter_28)

  subFilter_29 = signal<FilterItemVo>(null)
  subFilter_29$ = toObservable(this.subFilter_29)
  subFilter_30 = signal<FilterItemVo>(null)
  subFilter_30$ = toObservable(this.subFilter_30)

  subFilter_31 = signal<FilterItemVo>(null)
  subFilter_31$ = toObservable(this.subFilter_31)
  subFilter_32 = signal<FilterItemVo>(null)
  subFilter_32$ = toObservable(this.subFilter_32)

  constructor() {
    effect(() => {
      if (this.parent.area()) {
        this.searchValue.set(null)
      }
    })
  }

  ngAfterViewInit(): void {
    this._fetchConfig()
    this._subscribeToFilterItemsChanges()
  }

  private _subscribeToFilterItemsChanges() {
    combineLatest([
      this.metrics$,
      this.parent.area$,
      this.parent.dataType$,
      this.parent.c_ord_type$,
      this.parent.c_firlev_channel$,
      this.parent.c_scene_type$,
      this.subFilter_1$,
      this.subFilter_2$,
      this.subFilter_3$,
      this.subFilter_4$,
      this.subFilter_5$,
      this.subFilter_6$,
      this.subFilter_7$,
      this.subFilter_8$,
      this.subFilter_9$,
      this.subFilter_10$,
      this.subFilter_11$,
      this.subFilter_12$,
      this.subFilter_13$,
      this.subFilter_14$,
      this.subFilter_15$,
      this.subFilter_16$,
      this.subFilter_17$,
      this.subFilter_18$,
      this.subFilter_19$,
      this.subFilter_20$,
      this.subFilter_21$,
      this.subFilter_22$,
      this.subFilter_23$,
      this.subFilter_24$,
      this.subFilter_25$,
      this.subFilter_26$,
      this.subFilter_27$,
      this.subFilter_28$,
      this.subFilter_29$,
      this.subFilter_30$,
      this.subFilter_31$,
      this.subFilter_32$,
      this.formService.form.valueChanges.pipe(startWith(null)),
    ])
      .pipe(startWith([]), debounceTime(300), takeUntilDestroyed(this.destroyRef))
      .subscribe(([metrics]) => {
        if (isNotNull(metrics)) {
          this.query()
        }
      })
  }

  private _fetchConfig() {
    this.caerusApiService.fetchConfig('fluctuation_position_area_v3').subscribe(res => {
      if (res.data) {
        const { transfer } = res.data
        const list = Object.keys(transfer).map(key => transfer[key])
        const metrics = list.filter(item => item?.bizExpression !== undefined).sort((a, b) => a.index - b.index)
        const dimensions = list.filter(item => item?.bizExpression === undefined)

        dimensions.forEach(({ name, value }) => {
          this.dimensionMap.set(name, { extendName: value })
        })

        metrics.forEach(item => {
          this.metricsMap.set(item.value, item)
        })

        this.metrics.set(metrics)
      }
    })
  }

  private _dimensions() {
    switch (this.parent.area()) {
      case 'city_bio_region':
        return [this.dimensionMap.get('大区')]
      case 'province_name':
        return [this.dimensionMap.get('省topValue'), this.dimensionMap.get('省')]
      case 'city_name':
        return [this.dimensionMap.get('市topValue'), this.dimensionMap.get('市')]
    }

    return []
  }

  private _body() {
    const body = this.formService.value()

    body.metrics = this.metrics()
      .filter(item => item.name !== '波动占比')
      .map(({ value }) => ({ extendName: value }))
    body.filter.items = body.filter.items.concat({
      conditionType: 2,
      condition: '=',
      extendName: 'c_firlev_channel',
      value: [
        {
          key: '自有渠道',
          value: '自有渠道',
        },
      ],
    })
    body.dimensions = this._dimensions()
    body.extraDimensionGroups = [[]]
    body.extraDimensionsExcludeFilter = [[
      "city_bio_region",
      "province_name",
      "city_name",
      "is_top20_city"
    ]];
    body.metricsFilter = {
      items: [
        this.subFilter_1(),
        this.subFilter_2(),
        this.subFilter_3(),
        this.subFilter_4(),
        this.subFilter_5(),
        this.subFilter_6(),
        this.subFilter_7(),
        this.subFilter_8(),
        this.subFilter_9(),
        this.subFilter_10(),
        this.subFilter_11(),
        this.subFilter_12(),
        this.subFilter_13(),
        this.subFilter_14(),
        this.subFilter_15(),
        this.subFilter_16(),
        this.subFilter_17(),
        this.subFilter_18(),
        this.subFilter_19(),
        this.subFilter_20(),
        this.subFilter_21(),
        this.subFilter_22(),
        this.subFilter_23(),
        this.subFilter_24(),
        this.subFilter_25(),
        this.subFilter_26(),
        this.subFilter_27(),
        this.subFilter_28(),
        this.subFilter_29(),
        this.subFilter_30(),
        this.subFilter_31(),
        this.subFilter_32(),
      ].filter(isNotNull),
    }

    return body
  }

  @SwitchMap()
  private query() {
    const body = this._body()

    this.loading.set(true)

    body.metrics = body.metrics.concat({
      extendName: 'c_davg_finish_ord_cnt',
      customType: 'proportion_value',
      proportionDimension: [],
    } as any)

    return this.apiService
      .search(body, 'table-transform')
      .pipe(finalize(() => this.loading.set(false)))
      .subscribe(res => {
        if (res.data?.data) {
          const { data, compareData } = res.data
          this.compareData.set(compareData as any)

          const first = data.find(item => item[this.parent.area()] === '整体') as any
          const list = data
            .filter(item => item !== first)
            .filter(item => (item['city_bio_region'] ? !['其他', '其它'].includes(item['city_bio_region']) : item))
            .map(item => {
              return { ...item, c_davg_finish_ord_cnt_FLUCTUATION: this.getFluctuationRatio(item) }
            })

          if (first) {
            first.c_davg_finish_ord_cnt_FLUCTUATION = this.getFluctuationRatio(first)
          }

          this.firstRowData.set(first)
          this.listOfData.set(list as any)
          this.listOfDisplayData.set(list as any)
        }
      })
  }

  getFluctuationRatio(data: any) {
    if (isNotEmpty(data)) {
      const city_name = data[this.parent.area()]
      const compareData = this.compareData().find(item => item[this.parent.area()] === city_name)

      if (
        Number.isFinite(+data.c_davg_finish_ord_cnt_DIFF) &&
        Number.isFinite(+compareData?.['c_davg_finish_ord_cnt:proportion_value'])
      ) {
        return +data.c_davg_finish_ord_cnt_DIFF / +compareData['c_davg_finish_ord_cnt:proportion_value']
      }
    }
    return null
  }

  reset(): void {
    this.searchValue.set(null)
    this.search()
  }

  search(): void {
    const key = this.parent.area()
    const list = this.searchPipe.transform(this.listOfData(), this.searchValue(), key)

    this.visible.set(false)
    this.listOfDisplayData.set(list)
  }

  drill(value: string) {
    this.rootParent.area.set(value)
  }

  async jumpPriceMonitor(data: any) {
    let queryParams = ''
    const { dt, compareDt } = this.formService.form.getRawValue()
    const { startTime: st, endTime: et } = dt
    const { startTime: compareSt, endTime: compareEt } = compareDt
    const dtStartTime = new Date(st.replace(/-/g, '/'))
    const cityRegion = data[this.parent.area()]
    const regionType = this.parent.area()
    const params = {
      st,
      et,
      compareSt,
      compareEt,
      cityRegion,
      regionType,
    }

    const MIN_DATE = new Date(2025, 0, 8)
    const MIN_DATE_STR = format(MIN_DATE, 'yyyy-MM-dd')

    if (dtStartTime < MIN_DATE) {
      this.modal.confirm({
        nzTitle: '提示',
        nzContent: `价格指标仅支持查看${MIN_DATE_STR}之后的数据，当前时间周期无价格数据。`,
        nzCancelText: null,
        nzOkText: null,
      })

      return
    }

    Object.keys(params).forEach(key => {
      if (params[key] !== undefined && params[key] !== null) {
        queryParams += `${key}=${params[key]}&`
      }
    })

    const pathname = isPreview() ? '/preonline/' : isTest() ? '/test-caerus/' : isEmulator() ? '/aliyun-caerus/' : '/'
    this.buriedPointService.addStat('dida_dpm_caerus_diagnose_Price_Click', {
      page_name: this.root.page_name,
    })

    window.open(`${location.origin}${pathname}monitor/price?${queryParams}`, '_blank')
  }

  download() {
    this.apiService.exportToExcel(this._body()).subscribe()
  }
}
