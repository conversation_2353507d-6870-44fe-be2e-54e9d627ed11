<nz-table #groupingTable [nzData]="listOfDisplayData()" [nzLoading]="loading()" nzBordered nzSize="small" nzTableLayout="auto" [nzScroll]="{ x: '4300px' }" nzShowSizeChanger [nzShowTotal]="totalTemplate">
  <thead>
    <tr>
      <th nzWidth="130px" rowspan="2" nzLeft class="text-center!">
        @switch (parent.area()) {
          @case ('city_bio_region') { 区域 }
          @case ('province_name')   { 省份 }
          @case ('city_name')       { 城市 }
        }

        <nz-filter-trigger [(nzVisible)]="visible" [nzActive]="searchValue()?.length > 0" [nzDropdownMenu]="menu">
          <SearchIcon />
        </nz-filter-trigger>

        <nz-dropdown-menu #menu="nzDropdownMenu">
          <div class="ant-table-filter-dropdown">
            <div class="search-box">
              <input type="text" nz-input autofocus placeholder="请输入" [(ngModel)]="searchValue" />
              <button nz-button nzSize="small" nzType="primary" (click)="search()" class="search-button">搜索</button>
              <button nz-button nzSize="small" (click)="reset()">重置</button>
            </div>
          </div>
        </nz-dropdown-menu>
      </th>

      @for (item of listOfColumnsLevel_1(); track $index) {
        <th
          [class]="item.className"
          [colspan]="item.colspan"
          [rowspan]="item.rowspan"
          nz-popover
          nzPopoverPlacement="top"
          [nzPopoverMouseEnterDelay]="0.5"
          [nzPopoverTitle]="metricsMap.get(item.key)?.name"
          [nzPopoverContent]="metricsMap.get(item.key)?.bizExpression"
          [nzShowSort]="!!item?.sortFn"
          [nzSortFn]="item?.sortFn || null"
        >
          @if (metricsMap.get(item.key)?.name) {
            {{metricsMap.get(item.key)?.name}}
          } @else {
            波动占比
          }
        </th>
      }
      
      <th nzWidth="130px" rowspan="2" nzRight class="text-center!">查看价格数据</th>
    </tr>

    <tr>
      @for (column of listOfColumns(); track $index) {
        <th
          class="text-right!"
          nzCustomFilter
          [(nzSortOrder)]="column.sortOrder"
          [nzSortFn]="column.sortFn"
        >
          {{column.name}}

          <ng-container>
            <nz-filter-trigger #target [nzActive]="column.filter() !== null" [nzDropdownMenu]="menu">
              <nz-icon nzType="filter" nzTheme="fill" />
            </nz-filter-trigger>
            <nz-dropdown-menu #menu="nzDropdownMenu">
              <app-sub-filter
                [title]="column.title"
                [(value)]="column.filter"
                [suffix]="column?.suffix"
                [step]="column?.step"
                (valueChange)="target.hide()"
                [extendName]="column.key"
              />
            </nz-dropdown-menu>
          </ng-container>
        </th>
      }
    </tr>
  </thead>
  <tbody>
    @if (firstRowData()) {
      <tr>
        <td nzLeft align="center" class="bg-[#fafafa]!">
          <a (click)="drill(null)">全国</a>
        </td>

        <ng-template *ngTemplateOutlet="rowTemplate; context: { $implicit: firstRowData() }"></ng-template>

        <td align="center" nzRight>
          <span class="cursor-not-allowed text-neutral-400" nz-tooltip="价格指标暂不支持当前维度">点击查看价格</span>
        </td>
      </tr>
    }

    @for (data of groupingTable.data; track $index) {
      @if (data) {
        <tr>
          <td nzLeft align="center" class="bg-[#fafafa]!">
            @let areaKey = parent.area();
            <a (click)="drill(data[areaKey])">{{ data[areaKey] }}</a>
          </td>

          <ng-template *ngTemplateOutlet="rowTemplate; context: { $implicit: data }"></ng-template>

          <td align="center" nzRight>
            <ng-template *ngTemplateOutlet="viewPriceTemplate; context: { $implicit: data }"></ng-template>
          </td>
        </tr>
      }
    }
  </tbody>
</nz-table>

<ng-template #rowTemplate let-data>
  <!-- 完单量 -->
  <td class="whitespace-nowrap text-right bg-[#F6FBFF]">
    <value-formatter [value]="data.c_davg_finish_ord_cnt" />
  </td>

  <td class="whitespace-nowrap text-right bg-[#F6FBFF]">
    <value-formatter useColor [value]="data.c_davg_finish_ord_cnt_DIFF" />
    <value-formatter showBracket useColor useMultiplier="false" [value]="data.c_davg_finish_ord_cnt_DIFF_RATIO" suffix="%" />
  </td>

  <!-- 波动占比 -->
  <td class="whitespace-nowrap text-right bg-[#F6FBFF]">
    <value-formatter useColor [value]="data.c_davg_finish_ord_cnt_FLUCTUATION" suffix="%" />
  </td>

  <!-- 车乘比 -->
  <td class="whitespace-nowrap text-right bg-[#FFF8F9]">
    <value-formatter [value]="data.driver_pass_active_prop" />
  </td>

  <td class="whitespace-nowrap text-right bg-[#FFF8F9]">
    <value-formatter useColor [value]="data.driver_pass_active_prop_DIFF" />
  </td>
  
  <!-- 乘客DAU -->
  <td class="whitespace-nowrap text-right bg-[#FFF8F9]">
    <value-formatter [value]="data.davg_c_pass_active_uv" />
  </td>

  <td class="whitespace-nowrap text-right bg-[#FFF8F9]">
    <value-formatter useColor [value]="data.davg_c_pass_active_uv_DIFF" />
    <value-formatter showBracket useColor useMultiplier="false" [value]="data.davg_c_pass_active_uv_DIFF_RATIO" suffix="%" />
  </td>

  <!-- 下单率 -->
  <td class="whitespace-nowrap text-right bg-[#FFF8F9]">
    <value-formatter [value]="data.c_active_book_pass_rate" suffix="%" />
  </td>

  <td class="whitespace-nowrap text-right bg-[#FFF8F9]">
    <value-formatter useColor [value]="data.c_active_book_pass_rate_DIFF" suffix="pp" />
  </td>

  <!-- 人均下单频次 -->
  <td class="whitespace-nowrap text-right bg-[#FFF8F9]">
    <value-formatter [value]="data.davg_c_pass_book_ord_cnt" />
  </td>

  <td class="whitespace-nowrap text-right bg-[#FFF8F9]">
    <value-formatter useColor [value]="data.davg_c_pass_book_ord_cnt_DIFF" />
  </td>

  <!-- 下单接单率 -->
  <td class="whitespace-nowrap text-right bg-[#FFF8F9]">
    <value-formatter [value]="data.c_book_reply_ord_rate" suffix="%" />
  </td>

  <td class="whitespace-nowrap text-right bg-[#FFF8F9]">
    <value-formatter useColor [value]="data.c_book_reply_ord_rate_DIFF" suffix="pp" />
  </td>

  <!-- 接单完单率 -->
  <td class="whitespace-nowrap text-right bg-[#FFF8F9]">
    <value-formatter [value]="data.c_reply_finish_ord_rate" suffix="%" />
  </td>

  <td class="whitespace-nowrap text-right bg-[#FFF8F9]">
    <value-formatter useColor [value]="data.c_reply_finish_ord_rate_DIFF" suffix="pp" />
  </td>


  <!-- 车主DAU -->
  <td class="whitespace-nowrap text-right bg-[#F9FFFA]">
    <value-formatter [value]="data.davg_c_driver_active_uv" />
  </td>

  <td class="whitespace-nowrap text-right bg-[#F9FFFA]">
    <value-formatter useColor [value]="data.davg_c_driver_active_uv_DIFF" />
    <value-formatter showBracket useColor useMultiplier="false" [value]="data.davg_c_driver_active_uv_DIFF_RATIO" suffix="%" />
  </td>


  <!-- 车主接单率 -->
  <!-- <td class="whitespace-nowrap text-right bg-[#F9FFFA]">
    <value-formatter [value]="data.c_active_reply_driver_rate" suffix="%" />
  </td>

  <td class="whitespace-nowrap text-right bg-[#F9FFFA]">
    <value-formatter useColor [value]="data.c_active_reply_driver_rate_DIFF" suffix="pp" />
  </td> -->

  
  <!-- 车主接单频次 -->
  <td class="whitespace-nowrap text-right bg-[#F9FFFA]">
    <value-formatter [value]="data.davg_c_reply_driver_pv" />
  </td>

  <td class="whitespace-nowrap text-right bg-[#F9FFFA]">
    <value-formatter useColor [value]="data.davg_c_reply_driver_pv_DIFF" />
  </td>


  <!-- 下单取消率 -->
  <td class="whitespace-nowrap text-right bg-[#F9FFFA]">
    <value-formatter [value]="data.c_book_cancel_ord_rate" suffix="%" />
  </td>

  <td class="whitespace-nowrap text-right bg-[#F9FFFA]">
    <value-formatter useColor [value]="data.c_book_cancel_ord_rate_DIFF" suffix="pp" />
  </td>


  <!-- 下单-接单后取消率 -->
  <td class="whitespace-nowrap text-right bg-[#F9FFFA]">
    <value-formatter [value]="data.c_afreply_book_cancel_ord_rate" suffix="%" />
  </td>

  <td class="whitespace-nowrap text-right bg-[#F9FFFA]">
    <value-formatter useColor [value]="data.c_afreply_book_cancel_ord_rate_DIFF" suffix="pp" />
  </td>


  <!-- 下单-接单前取消率 -->
  <td class="whitespace-nowrap text-right bg-[#F9FFFA]">
    <value-formatter [value]="data.c_bereply_book_cancel_ord_rate" suffix="%" />
  </td>

  <td class="whitespace-nowrap text-right bg-[#F9FFFA]">
    <value-formatter useColor [value]="data.c_bereply_book_cancel_ord_rate_DIFF" suffix="pp" />
  </td>


  <!-- 下单-接单后车主取消率 -->
  <td class="whitespace-nowrap text-right bg-[#F9FFFA]">
    <value-formatter [value]="data.driver_c_afreply_book_cancel_ord_rate" suffix="%" />
  </td>

  <td class="whitespace-nowrap text-right bg-[#F9FFFA]">
    <value-formatter useColor [value]="data.driver_c_afreply_book_cancel_ord_rate_DIFF" suffix="pp" />
  </td>


  <!-- 下单-接单后乘客取消率 -->
  <td class="whitespace-nowrap text-right bg-[#F9FFFA]">
    <value-formatter [value]="data.pass_c_afreply_book_cancel_ord_rate" suffix="%" />
  </td>

  <td class="whitespace-nowrap text-right bg-[#F9FFFA]">
    <value-formatter useColor [value]="data.pass_c_afreply_book_cancel_ord_rate_DIFF" suffix="pp" />
  </td>


  <!-- 下单-接单后取消率 -->
  <td class="whitespace-nowrap text-right bg-[#F9FFFA]">
    <value-formatter [value]="data.c_afreply_reply_cancel_ord_rate" suffix="%" />
  </td>

  <td class="whitespace-nowrap text-right bg-[#F9FFFA]">
    <value-formatter useColor [value]="data.c_afreply_reply_cancel_ord_rate_DIFF" suffix="pp" />
  </td>
</ng-template>

<ng-template #viewPriceTemplate let-data>
  @if (
    (
      this.parent.area() === 'city_bio_region' ? true : 
      (
        data?.province_city_top_value !== undefined && 
        data?.province_city_top_value !== '-1' &&
        data?.province_city_top_value !== '0'
      ) ||
      (
        data?.city_top_value !== undefined && 
        data?.city_top_value !== '-1' &&
        data?.city_top_value !== '0'
      )
    ) &&
    this.formService.dtType.value !== 'ym' &&
    this.formService.dtType.value !== 'yw'
  ) {
    <a (click)="jumpPriceMonitor(data)">点击查看价格</a>
  } @else {
    <span class="cursor-not-allowed text-neutral-400" nz-tooltip="价格指标暂不支持当前维度">点击查看价格</span>
  }
</ng-template>

<ng-template #totalTemplate let-total> 共 {{ total }} 条记录 </ng-template>
