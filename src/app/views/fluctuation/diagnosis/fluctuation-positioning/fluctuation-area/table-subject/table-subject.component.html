<nz-table #groupingTable nzTableLayout="auto" [nzData]="listOfDisplayData()" [nzLoading]="loading()" nzBordered nzSize="small" [nzScroll]="{ x: '1600px' }" nzShowSizeChanger [nzShowTotal]="totalTemplate">
  <thead>
    <tr>
      <th nzWidth="130px" rowspan="2" nzLeft class="text-center!">
        @switch (parent.area()) {
          @case ('city_bio_region') { 区域 }
          @case ('province_name')   { 省份 }
          @case ('city_name')       { 城市 }
        }

        <nz-filter-trigger [(nzVisible)]="visible" [nzActive]="searchValue()?.length > 0" [nzDropdownMenu]="menu">
          <SearchIcon />
        </nz-filter-trigger>

        <nz-dropdown-menu #menu="nzDropdownMenu">
          <div class="ant-table-filter-dropdown">
            <div class="search-box">
              <input type="text" nz-input autofocus placeholder="请输入" [(ngModel)]="searchValue" />
              <button nz-button nzSize="small" nzType="primary" (click)="search()" class="search-button">搜索</button>
              <button nz-button nzSize="small" (click)="reset()">重置</button>
            </div>
          </div>
        </nz-dropdown-menu>
      </th>
      
      @for (item of listOfColumnsLevel_1(); track $index) {
        <th
          [class]="item.className"
          [colspan]="item.colspan"
          [rowspan]="item.rowspan"
          nz-popover
          nzPopoverPlacement="top"
          [nzPopoverMouseEnterDelay]="0.5"
          [nzPopoverTitle]="metricsMap.get(item.key)?.name"
          [nzPopoverContent]="metricsMap.get(item.key)?.bizExpression"
          [nzShowSort]="!!item?.sortFn"
          [nzSortFn]="item?.sortFn || null"
        >
          @if (metricsMap.get(item.key)?.name) {
            {{metricsMap.get(item.key)?.name}}
          } @else {
            波动占比<br>(对大盘影响)
          }
        </th>
      }

      <th nzWidth="130px" rowspan="2" nzRight class="text-center!">查看价格数据</th>
    </tr>

    <tr>
      @for (column of listOfColumns(); track $index) {
        <th
          class="text-right!"
          nzCustomFilter
          [(nzSortOrder)]="column.sortOrder"
          [nzSortFn]="column.sortFn"
        >
          {{column.name}}

          <ng-container>
            <nz-filter-trigger #target [nzActive]="column.filter() !== null" [nzDropdownMenu]="menu">
              <nz-icon nzType="filter" nzTheme="fill" />
            </nz-filter-trigger>
            <nz-dropdown-menu #menu="nzDropdownMenu">
              <app-sub-filter
                [title]="column.title"
                [(value)]="column.filter"
                [suffix]="column.suffix"
                (valueChange)="target.hide()"
                [extendName]="column.key"
              />
            </nz-dropdown-menu>
          </ng-container>
        </th>
      }
    </tr>
  </thead>

  <tbody>
    @if (firstRowData()) {
      <tr>
        <td nzLeft align="center" class="bg-[#fafafa]!">
          <a (click)="drill(null)">全国</a>
        </td>

        <ng-template *ngTemplateOutlet="finishTemplate; context: { $implicit: firstRowData() }"></ng-template>

        <td align="center" nzRight>
          <span class="cursor-not-allowed text-neutral-400" nz-tooltip="价格指标暂不支持当前维度">点击查看价格</span>
        </td>
      </tr>
    }

    @for (data of groupingTable.data; track $index) {
      @if (data) {
        <tr>
          <td nzLeft align="center" class="bg-[#fafafa]!">
            @let areaKey = parent.area();
            <a (click)="drill(data[areaKey])">{{ data[areaKey] }}</a>
          </td>

          <ng-template *ngTemplateOutlet="finishTemplate; context: { $implicit: data }"></ng-template>

          <td align="center" nzRight>
            <ng-template *ngTemplateOutlet="viewPriceTemplate; context: { $implicit: data }"></ng-template>
          </td>
        </tr>
      }
    }
  </tbody>
</nz-table>


<ng-template #finishTemplate let-data>
  <!-- 完单量 -->
  <td align="right" class="bg-[#F6FBFF]">
    <value-formatter [value]="data.c_davg_finish_ord_cnt" />
  </td>

  <td align="right" class="bg-[#F6FBFF] whitespace-nowrap">
    <value-formatter useColor [value]="data.c_davg_finish_ord_cnt_DIFF" />
    <value-formatter showBracket useColor useMultiplier="false" [value]="data.c_davg_finish_ord_cnt_DIFF_RATIO" suffix="%" />
  </td>

  <!-- 波动占比 -->
  <td align="right" class="bg-[#F6FBFF]">
    <value-formatter useColor [value]="data.c_davg_finish_ord_cnt_FLUCTUATION" suffix="%" />
  </td>

  <!-- 下单量 -->
  <td align="right" class="bg-[#F6FBFF]">
    <value-formatter [value]="data.c_davg_book_ord_cnt" />
  </td>

  <td align="right" class="bg-[#F6FBFF] whitespace-nowrap">
    <value-formatter useColor [value]="data.c_davg_book_ord_cnt_DIFF" />
    <value-formatter showBracket useColor useMultiplier="false" [value]="data.c_davg_book_ord_cnt_DIFF_RATIO" suffix="%" />
  </td>

  <!-- 接单量 -->
  <td align="right" class="bg-[#F6FBFF]">
    <value-formatter [value]="data.c_davg_reply_ord_cnt" />
  </td>

  <td align="right" class="bg-[#F6FBFF] whitespace-nowrap">
    <value-formatter useColor [value]="data.c_davg_reply_ord_cnt_DIFF" />
    <value-formatter showBracket useColor useMultiplier="false" [value]="data.c_davg_reply_ord_cnt_DIFF_RATIO" suffix="%" />
  </td>

  <!-- 下单接单率 -->
  <td align="right" class="bg-[#FFF8F9]">
    <value-formatter [value]="data.c_book_reply_ord_rate" suffix="%" />
  </td>

  <td align="right" class="bg-[#FFF8F9]">
    <value-formatter useColor [value]="data.c_book_reply_ord_rate_DIFF" suffix="pp" />
  </td>

  <!-- 接单完单率 -->
  <td align="right" class="bg-[#FFF8F9]">
    <value-formatter [value]="data.c_reply_finish_ord_rate" suffix="%" />
  </td>

  <td align="right" class="bg-[#FFF8F9]">
    <value-formatter useColor [value]="data.c_reply_finish_ord_rate_DIFF" suffix="pp" />
  </td>
</ng-template>


<ng-template #viewPriceTemplate let-data>
  @if (
    (this.parent.c_firlev_channel() === 'ALL' || this.parent.c_firlev_channel() === '自有渠道') &&
    this.parent.c_scene_type() === 'ALL' &&
    (
      this.parent.area() === 'city_bio_region' ? true : 
      (
        data?.province_city_top_value !== undefined && 
        data?.province_city_top_value !== '-1' &&
        data?.province_city_top_value !== '0'
      ) ||
      (
        data?.city_top_value !== undefined && 
        data?.city_top_value !== '-1' &&
        data?.city_top_value !== '0'
      )
    ) &&
    this.formService.dtType.value !== 'ym' &&
    this.formService.dtType.value !== 'yw'
  ) {
    <a (click)="jumpPriceMonitor(data)">点击查看价格</a>
  } @else {
    <span class="cursor-not-allowed text-neutral-400" nz-tooltip="价格指标暂不支持当前维度">点击查看价格</span>
  }
</ng-template>

<ng-template #totalTemplate let-total> 共 {{ total }} 条记录 </ng-template>