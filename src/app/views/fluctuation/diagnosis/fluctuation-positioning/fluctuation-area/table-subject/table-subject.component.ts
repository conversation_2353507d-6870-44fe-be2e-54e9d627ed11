import { FormsModule } from '@angular/forms'
import { NgTemplateOutlet } from '@angular/common'
import { ActivatedRoute, Router } from '@angular/router'
import {
  AfterViewInit,
  ChangeDetectionStrategy,
  Component,
  computed,
  DestroyRef,
  inject,
  OnDestroy,
  signal,
} from '@angular/core'
import { Subscription, combineLatest, debounceTime, filter, finalize, lastValueFrom, startWith } from 'rxjs'
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop'
import { NzTableModule } from 'ng-zorro-antd/table'
import { NzIconModule } from 'ng-zorro-antd/icon'
import { NzPopoverModule } from 'ng-zorro-antd/popover'
import { NzButtonModule } from 'ng-zorro-antd/button'
import { NzDropDownModule } from 'ng-zorro-antd/dropdown'
import { NzToolTipModule } from 'ng-zorro-antd/tooltip'
import { NzInputModule } from 'ng-zorro-antd/input'
import { NzModalService } from 'ng-zorro-antd/modal'
import { format } from 'date-fns'

import { CaerusApiService } from '@api/caerus'
import { isPreview, isTest, isEmulator } from '@common/const'
import { ValueFormatter } from '@shared/components/value-formatter'
import { FilterItemVo } from '@api/query-engine/model'
import { SubFilterComponent } from '@views/monitor/price/components'
import { QueryEngineApiService } from '@api/query-engine'
import { isNotEmpty, isNotNull } from '@common/function'
import { DiagnosisComponent } from '@views/fluctuation/diagnosis'
import { QueryEngineFormService } from '@common/service/query-engine'
import { SwitchMap } from '@common/decorator'
import { IconSearchComponent } from '@shared/modules/icons'
import { SearchPipe } from '@shared/pipes/search'

import { FluctuationPositioningComponent } from '../../fluctuation-positioning.component'
import { FluctuationAreaComponent } from '../fluctuation-area.component'
import { BuriedPointService } from '@common/service'

@Component({
  selector: 'app-table-subject',
  templateUrl: './table-subject.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    FormsModule,
    NgTemplateOutlet,
    NzInputModule,
    NzButtonModule,
    NzIconModule,
    NzTableModule,
    NzPopoverModule,
    NzToolTipModule,
    NzDropDownModule,
    ValueFormatter,
    SubFilterComponent,
    IconSearchComponent,
  ],
  providers: [SearchPipe],
})
export class TableSubjectComponent implements AfterViewInit {
  // cdr = inject(ChangeDetectorRef);
  readonly router = inject(Router)
  readonly route = inject(ActivatedRoute)
  readonly destroyRef = inject(DestroyRef)
  readonly caerusApiService = inject(CaerusApiService)
  readonly apiService = inject(QueryEngineApiService)
  readonly formService = inject(QueryEngineFormService)
  readonly searchPipe = inject(SearchPipe)
  readonly modal = inject(NzModalService)
  readonly rootParent = inject(DiagnosisComponent)
  readonly root = inject(FluctuationPositioningComponent)
  readonly parent = inject(FluctuationAreaComponent)
  readonly buriedPointService = inject(BuriedPointService)

  firstRowData = signal<any>({})
  listOfData = signal<Array<any>>([])
  listOfDisplayData = signal<Array<any>>([])
  compareData = signal<Array<any>>([])
  loading = signal(false)
  metrics = signal<any>(null)
  metrics$ = toObservable(this.metrics)
  searchValue = signal<string>(null)
  visible = signal<boolean>(null)
  metricsMap = new Map()
  dimensionMap = new Map()

  listOfColumnsLevel_1 = computed(() => {
    return [
      {
        key: 'c_davg_finish_ord_cnt',
        colspan: 2,
      },
      {
        key: 'c_davg_finish_ord_cnt_FLUCTUATION',
        rowspan: 2,
        className: 'text-center!',
        sortFn: (a: any, b: any) => a.c_davg_finish_ord_cnt_FLUCTUATION - b.c_davg_finish_ord_cnt_FLUCTUATION,
      },
      {
        key: 'c_davg_book_ord_cnt',
        colspan: 2,
      },
      {
        key: 'c_davg_reply_ord_cnt',
        colspan: 2,
      },
      {
        key: 'c_book_reply_ord_rate',
        colspan: 2,
      },
      {
        key: 'c_reply_finish_ord_rate',
        colspan: 2,
      },
    ]
  })

  listOfColumns = computed(() => {
    if (this.metrics()) {
      return [
        // 完单量
        {
          name: '数值',
          title: `${this.metricsMap.get('c_davg_finish_ord_cnt')?.aliasName}(数值)`,
          dataUnit: this.metricsMap.get('c_davg_finish_ord_cnt')?.dataUnit,
          key: 'c_davg_finish_ord_cnt',
          sortFn: (a: any, b: any) => a.c_davg_finish_ord_cnt - b.c_davg_finish_ord_cnt,
          sortOrder: 'descend',
          filter: this.subFilter_1,
        },
        {
          name: '涨跌幅',
          title: `${this.metricsMap.get('c_davg_finish_ord_cnt')?.aliasName}(涨跌幅)`,
          dataUnit: this.metricsMap.get('c_davg_finish_ord_cnt')?.dataUnit,
          key: 'c_davg_finish_ord_cnt_DIFF',
          sortFn: (a: any, b: any) => a.c_davg_finish_ord_cnt_DIFF - b.c_davg_finish_ord_cnt_DIFF,
          sortOrder: null,
          filter: this.subFilter_2,
        },

        // 下单量
        {
          name: '数值',
          title: `${this.metricsMap.get('c_davg_book_ord_cnt')?.aliasName}(数值)`,
          dataUnit: this.metricsMap.get('c_davg_book_ord_cnt')?.dataUnit,
          key: 'c_davg_book_ord_cnt',
          sortFn: (a: any, b: any) => a.c_davg_book_ord_cnt - b.c_davg_book_ord_cnt,
          sortOrder: null,
          filter: this.subFilter_3,
        },
        {
          name: '涨跌幅',
          title: `${this.metricsMap.get('c_davg_book_ord_cnt')?.aliasName}(涨跌幅)`,
          dataUnit: this.metricsMap.get('c_davg_book_ord_cnt')?.dataUnit,
          key: 'c_davg_book_ord_cnt_DIFF',
          sortFn: (a: any, b: any) => a.c_davg_book_ord_cnt_DIFF - b.c_davg_book_ord_cnt_DIFF,
          sortOrder: null,
          filter: this.subFilter_4,
        },

        // 接单量
        {
          name: '数值',
          title: `${this.metricsMap.get('c_davg_reply_ord_cnt')?.aliasName}(数值)`,
          dataUnit: this.metricsMap.get('c_davg_reply_ord_cnt')?.dataUnit,
          key: 'c_davg_reply_ord_cnt',
          sortFn: (a: any, b: any) => a.c_davg_reply_ord_cnt - b.c_davg_reply_ord_cnt,
          sortOrder: null,
          filter: this.subFilter_5,
        },
        {
          name: '涨跌幅',
          title: `${this.metricsMap.get('c_davg_reply_ord_cnt')?.aliasName}(涨跌幅)`,
          dataUnit: this.metricsMap.get('c_davg_reply_ord_cnt')?.dataUnit,
          key: 'c_davg_reply_ord_cnt_DIFF',
          sortFn: (a: any, b: any) => a.c_davg_reply_ord_cnt_DIFF - b.c_davg_reply_ord_cnt_DIFF,
          sortOrder: null,
          filter: this.subFilter_6,
        },

        // 下单接单率
        {
          name: '数值',
          title: `${this.metricsMap.get('c_book_reply_ord_rate')?.aliasName}(数值)`,
          dataUnit: this.metricsMap.get('c_book_reply_ord_rate')?.dataUnit,
          key: 'c_book_reply_ord_rate',
          sortFn: (a: any, b: any) => a.c_book_reply_ord_rate - b.c_book_reply_ord_rate,
          sortOrder: null,
          filter: this.subFilter_7,
          suffix: '%',
        },
        {
          name: '涨跌幅',
          title: `${this.metricsMap.get('c_book_reply_ord_rate')?.aliasName}(涨跌幅)`,
          dataUnit: this.metricsMap.get('c_book_reply_ord_rate')?.dataUnit,
          key: 'c_book_reply_ord_rate_DIFF',
          sortFn: (a: any, b: any) => a.c_book_reply_ord_rate_DIFF - b.c_book_reply_ord_rate_DIFF,
          sortOrder: null,
          filter: this.subFilter_8,
          suffix: 'pp',
        },

        // 接单完单率
        {
          name: '数值',
          title: `${this.metricsMap.get('c_reply_finish_ord_rate')?.aliasName}(数值)`,
          dataUnit: this.metricsMap.get('c_reply_finish_ord_rate')?.dataUnit,
          key: 'c_reply_finish_ord_rate',
          sortFn: (a: any, b: any) => a.c_reply_finish_ord_rate - b.c_reply_finish_ord_rate,
          sortOrder: null,
          filter: this.subFilter_9,
          suffix: '%',
        },
        {
          name: '涨跌幅',
          title: `${this.metricsMap.get('c_reply_finish_ord_rate')?.aliasName}(涨跌幅)`,
          dataUnit: this.metricsMap.get('c_reply_finish_ord_rate')?.dataUnit,
          key: 'c_reply_finish_ord_rate_DIFF',
          sortFn: (a: any, b: any) => a.c_reply_finish_ord_rate_DIFF - b.c_reply_finish_ord_rate_DIFF,
          sortOrder: null,
          filter: this.subFilter_10,
          suffix: 'pp',
        },
      ]
    }
    return []
  })

  viewLabel = computed(() => {
    return this.root.view() === 'finish' ? '完单量' : '下单量'
  })

  subscription: Subscription
  subFilter_1 = signal<FilterItemVo>(null)
  subFilter_1$ = toObservable(this.subFilter_1)
  subFilter_2 = signal<FilterItemVo>(null)
  subFilter_2$ = toObservable(this.subFilter_2)

  subFilter_3 = signal<FilterItemVo>(null)
  subFilter_3$ = toObservable(this.subFilter_3)
  subFilter_4 = signal<FilterItemVo>(null)
  subFilter_4$ = toObservable(this.subFilter_4)

  subFilter_5 = signal<FilterItemVo>(null)
  subFilter_5$ = toObservable(this.subFilter_5)
  subFilter_6 = signal<FilterItemVo>(null)
  subFilter_6$ = toObservable(this.subFilter_6)

  subFilter_7 = signal<FilterItemVo>(null)
  subFilter_7$ = toObservable(this.subFilter_7)
  subFilter_8 = signal<FilterItemVo>(null)
  subFilter_8$ = toObservable(this.subFilter_8)

  subFilter_9 = signal<FilterItemVo>(null)
  subFilter_9$ = toObservable(this.subFilter_9)
  subFilter_10 = signal<FilterItemVo>(null)
  subFilter_10$ = toObservable(this.subFilter_10)

  ngAfterViewInit(): void {
    this._fetchConfig()
    this._subscribeToFilterItemsChanges()
  }

  private _subscribeToFilterItemsChanges() {
    combineLatest([
      this.metrics$,
      this.parent.area$,
      this.parent.dataType$,
      this.parent.c_ord_type$,
      this.parent.c_firlev_channel$,
      this.parent.c_scene_type$,
      this.subFilter_1$,
      this.subFilter_2$,
      this.subFilter_3$,
      this.subFilter_4$,
      this.subFilter_5$,
      this.subFilter_6$,
      this.subFilter_7$,
      this.subFilter_8$,
      this.subFilter_9$,
      this.subFilter_10$,
      this.formService.form.valueChanges.pipe(startWith(null)),
    ])
      .pipe(startWith([]), debounceTime(300), takeUntilDestroyed(this.destroyRef))
      .subscribe(([metrics]) => {
        if (isNotNull(metrics)) {
          this.query()
        }
      })
  }

  private _fetchConfig() {
    this.caerusApiService.fetchConfig('fluctuation_position_area_v2').subscribe(res => {
      if (res.data) {
        const { subject } = res.data
        const list = Object.keys(subject.metrics).map(key => subject.metrics[key])
        const metrics = list.filter(item => item?.bizExpression !== undefined).sort((a, b) => a.index - b.index)
        const dimensions = list.filter(item => item?.bizExpression === undefined)

        dimensions.forEach(({ name, value }) => {
          this.dimensionMap.set(name, { extendName: value })
        })

        metrics.forEach(item => {
          this.metricsMap.set(item.value, item)
        })

        this.metrics.set(metrics)
      }
    })
  }

  private _orderTypeFilter() {
    const c_ord_type = this.parent.c_ord_type()

    if (c_ord_type !== 'ALL') {
      return [this.parent.c_ord_typeMap.get(c_ord_type)]
    }
    return []
  }

  private _firlevChannelFilter() {
    const c_firlev_channel = this.parent.c_firlev_channel()

    if (c_firlev_channel !== 'ALL') {
      return [this.parent.c_firlev_channelMap.get(c_firlev_channel)]
    }
    return []
  }

  private _sceneTypeFilter() {
    const c_scene_type = this.parent.c_scene_type()

    if (c_scene_type !== 'ALL') {
      return [this.parent.c_scene_typeMap.get(c_scene_type)]
    }
    return []
  }

  private _dimensions() {
    switch (this.parent.area()) {
      case 'city_bio_region':
        return [this.dimensionMap.get('大区')]
      case 'province_name':
        return [this.dimensionMap.get('省topValue'), this.dimensionMap.get('省')]
      case 'city_name':
        return [this.dimensionMap.get('市topValue'), this.dimensionMap.get('市')]
    }

    return []
  }

  private _body() {
    const body = this.formService.value()

    body.metrics = this.metrics()
      .filter(item => item.name !== '波动占比')
      .map(({ value }) => ({ extendName: value }))
    body.dimensions = this._dimensions()
    body.extraDimensionGroups = [[]]
    body.extraDimensionsExcludeFilter = [[
      "city_bio_region",
      "province_name",
      "city_name",
      "is_top20_city"
    ]];
    body.filter.items = body.filter.items.concat(
      this._orderTypeFilter(),
      this._firlevChannelFilter(),
      this._sceneTypeFilter()
    )

    body.metricsFilter = {
      items: [
        this.subFilter_1(),
        this.subFilter_2(),
        this.subFilter_3(),
        this.subFilter_4(),
        this.subFilter_5(),
        this.subFilter_6(),
        this.subFilter_7(),
        this.subFilter_8(),
        this.subFilter_9(),
        this.subFilter_10(),
      ].filter(isNotNull),
    }

    return body
  }

  @SwitchMap()
  private query() {
    const body = this._body()

    this.loading.set(true)

    body.metrics = body.metrics.concat({
      extendName: 'c_davg_finish_ord_cnt',
      customType: 'proportion_value',
      proportionDimension: [],
    } as any)

    return this.apiService
      .search(body, 'table-subject')
      .pipe(finalize(() => this.loading.set(false)))
      .subscribe(res => {
        if (res.data?.data) {
          const { data, compareData } = res.data
          this.compareData.set(compareData as any)

          const first = data.find(item => item[this.parent.area()] === '整体') as any
          const list = data
            .filter(item => item !== first)
            .map(item => {
              return { ...item, c_davg_finish_ord_cnt_FLUCTUATION: this.getFluctuationRatio(item) }
            })

          if (first) {
            first.c_davg_finish_ord_cnt_FLUCTUATION = this.getFluctuationRatio(first)
          }

          this.firstRowData.set(first)
          this.listOfData.set(list as any)
          this.listOfDisplayData.set(list as any)
        }
      })
  }

  getFluctuationRatio(data: any) {
    if (isNotEmpty(data)) {
      const city_name = data[this.parent.area()]
      const compareData = this.compareData().find(item => item[this.parent.area()] === city_name)

      if (
        Number.isFinite(+data.c_davg_finish_ord_cnt_DIFF) &&
        Number.isFinite(+compareData?.['c_davg_finish_ord_cnt:proportion_value'])
      ) {
        return +data.c_davg_finish_ord_cnt_DIFF / +compareData['c_davg_finish_ord_cnt:proportion_value']
      }
    }
    return null
  }

  reset(): void {
    this.searchValue.set(null)
    this.search()
  }

  search(): void {
    const key = this.parent.area()
    const list = this.searchPipe.transform(this.listOfData(), this.searchValue(), key)

    this.visible.set(false)
    this.listOfDisplayData.set(list)
  }

  drill(value: string | any) {
    this.rootParent.area.set(value)
  }

  async jumpPriceMonitor(data: any) {
    let queryParams = ''
    const { dt, compareDt } = this.formService.form.getRawValue()
    const { startTime: st, endTime: et } = dt
    const { startTime: compareSt, endTime: compareEt } = compareDt
    const dtStartTime = new Date(st.replace(/-/g, '/'))
    const orderType = this._orderTypeFilter()?.at(0)?.value?.at(0)?.key || '0'
    const cityRegion = data[this.parent.area()]
    const regionType = this.parent.area()
    const params = {
      st,
      et,
      compareSt,
      compareEt,
      orderType,
      cityRegion,
      regionType,
    }

    const MIN_DATE = new Date(2025, 0, 8)
    const MIN_DATE_STR = format(MIN_DATE, 'yyyy-MM-dd')

    if (dtStartTime < MIN_DATE) {
      this.modal.confirm({
        nzTitle: '提示',
        nzContent: `价格指标仅支持查看${MIN_DATE_STR}之后的数据，当前时间周期无价格数据。`,
        nzCancelText: null,
        nzOkText: null,
      })

      return
    }

    Object.keys(params).forEach(key => {
      if (params[key] !== undefined && params[key] !== null) {
        queryParams += `${key}=${params[key]}&`
      }
    })

    const pathname = isPreview() ? '/preonline/' : isTest() ? '/test-caerus/' : isEmulator() ? '/aliyun-caerus/' : '/'
    this.buriedPointService.addStat('dida_dpm_caerus_diagnose_Price_Click', {
      page_name: this.root.page_name,
    })

    window.open(`${location.origin}${pathname}monitor/price?${queryParams}`, '_blank')
  }

  download() {
    this.apiService.exportToExcel(this._body()).subscribe()
  }
}
