import { FormsModule } from '@angular/forms'
import { ChangeDetectionStrategy, Component, computed, effect, inject, signal, viewChild } from '@angular/core'
import { toObservable } from '@angular/core/rxjs-interop'
import { NzRadioModule } from 'ng-zorro-antd/radio'
import { NzButtonComponent } from 'ng-zorro-antd/button'
import { NzIconModule } from 'ng-zorro-antd/icon'

import { CaerusApiService } from '@api/caerus'
import { FilterItemVo } from '@api/caerus/model'
import { isUndefinedOrNull } from '@common/function'
import { QueryEngineFormService } from '@common/service/query-engine'
import { BuriedPointService } from '@common/service'
import { IntersectionDirective, PAGE_NAME } from '@common/directive'
import { RadioModule } from '@shared/modules/headless'

import { FluctuationPositioningComponent } from '../fluctuation-positioning.component'
import { TableTransformComponent } from './table-transform/table-transform.component'
import { TableSubjectComponent } from './table-subject/table-subject.component'
import { DiagnosisComponent } from '../../diagnosis.component'

@Component({
  selector: 'app-fluctuation-area',
  template: `
    <div class="py-10">
      <header class="relative flex items-center p-5">
        <div class="flex items-center gap-x-2">
          <span class="font-black text-base">波动区域</span>
        </div>

        <div class="flex-1 min-w-0 flex justify-center">
          <app-radio-group
            [(ngModel)]="dataType"
            class="relative w-80 flex items-start gap-x-0.5 p-0.5 bg-[#ebebec] rounded-lg"
          >
            <app-radio
              class="dida-radio-new"
              activeClass="active"
              value="subject"
              (mouseup)="onTabClick('业务主题', 3)"
            >
              业务主题
            </app-radio>
            <app-radio
              class="dida-radio-new"
              activeClass="active"
              value="transform"
              (mouseup)="onTabClick('自有乘客转化链路', 3)"
            >
              自有乘客转化链路
            </app-radio>
            <app-radio-thumb [offsetX]="2" class="bg-linear-to-r from-[#465867] to-[#1E2C3A] rounded-md" />
          </app-radio-group>
        </div>

        <div class="invisible">波动区域</div>
      </header>

      <div class="flex items-center flex-nowrap px-5 pb-5">
        <div class="flex items-center flex-wrap gap-x-5 gap-y-2">
          <app-radio-group class="flex gap-1" [(ngModel)]="area">
            <label class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap">
              区域粒度：
            </label>
            <div class="flex gap-1 flex-wrap items-start">
              <app-radio class="tag-radio" activeClass="active" [disabled]="regionDisabled()" value="city_bio_region">
                按区域
              </app-radio>
              <app-radio class="tag-radio" activeClass="active" [disabled]="provinceDisabled()" value="province_name">
                按省份
              </app-radio>
              <app-radio class="tag-radio" activeClass="active" value="city_name">按城市</app-radio>
            </div>
          </app-radio-group>

          @if (dataType() === 'subject') {
            <app-radio-group class="flex gap-1" [(ngModel)]="c_ord_type">
              <label class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap">
                订单类型：
              </label>
              <div class="flex gap-1 flex-wrap items-start">
                <app-radio class="tag-radio" activeClass="active" value="ALL">全部</app-radio>
                @for (item of c_ord_typeOption(); track item) {
                  <app-radio class="tag-radio" activeClass="active" [value]="item">{{ item }}</app-radio>
                }
              </div>
            </app-radio-group>

            <app-radio-group class="flex gap-1" [(ngModel)]="c_firlev_channel">
              <label class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap">
                一级业务渠道：
              </label>
              <div class="flex gap-1 flex-wrap items-start">
                <app-radio class="tag-radio" activeClass="active" value="ALL">全部</app-radio>
                @for (item of c_firlev_channelOption(); track item) {
                  <app-radio class="tag-radio" activeClass="active" [value]="item">{{ item }}</app-radio>
                }
              </div>
            </app-radio-group>

            <app-radio-group class="flex gap-1" [(ngModel)]="c_scene_type">
              <label class="inline-flex items-start justify-end text-xs font-bold leading-5 whitespace-nowrap">
                场景：
              </label>
              <div class="flex gap-1 flex-wrap items-start">
                <app-radio class="tag-radio" activeClass="active" value="ALL">全部</app-radio>
                @for (item of c_scene_typeOption(); track item) {
                  <app-radio class="tag-radio" activeClass="active" [value]="item">{{ item }}</app-radio>
                }
              </div>
            </app-radio-group>
          }
        </div>

        <button class="ml-auto!" nz-button (click)="download()">
          <span nz-icon nzType="download" nzTheme="outline"></span>
          下载
        </button>
      </div>

      <article appIntersection (visible)="onVisible(4)">
        <app-table-subject [hidden]="dataType() !== 'subject'" />
        <app-table-transform [hidden]="dataType() !== 'transform'" />
      </article>
    </div>
  `,
  host: {
    id: 'anchor-fluctuation-area',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    FormsModule,
    NzButtonComponent,
    NzIconModule,
    NzRadioModule,
    RadioModule,
    TableTransformComponent,
    TableSubjectComponent,
    IntersectionDirective,
  ],
  providers: [{ provide: PAGE_NAME, useValue: 'fluctuate-diagnosis' }],
})
export class FluctuationAreaComponent {
  readonly page_name = inject(PAGE_NAME)
  readonly apiService = inject(CaerusApiService)
  readonly root = inject(DiagnosisComponent)
  readonly parent = inject(FluctuationPositioningComponent)
  readonly buriedPointService = inject(BuriedPointService)
  readonly formService = inject(QueryEngineFormService)

  subjectTable = viewChild(TableSubjectComponent)
  transformTable = viewChild(TableTransformComponent)

  dataType = signal<'subject' | 'transform'>('subject')
  dataType$ = toObservable(this.dataType)

  area = signal<string>('city_bio_region')
  area$ = toObservable(this.area)

  c_ord_type = signal('ALL')
  c_ord_typeOption = signal<string[]>([])
  c_ord_type$ = toObservable(this.c_ord_type)
  c_ord_typeMap = new Map([['ALL', null]])

  c_firlev_channel = signal('ALL')
  c_firlev_channelOption = signal<string[]>([])
  c_firlev_channel$ = toObservable(this.c_firlev_channel)
  c_firlev_channelMap = new Map([['ALL', null]])

  c_scene_type = signal('ALL')
  c_scene_typeOption = signal<string[]>([])
  c_scene_type$ = toObservable(this.c_scene_type)
  c_scene_typeMap = new Map([['ALL', null]])

  // 大区 city_bio_region > 省份 province_name >城市 city_name
  regionDisabled = computed(() => {
    const { extendName } = this.root.areaForm$() || {}

    if (extendName === 'city_bio_region' || isUndefinedOrNull(extendName)) {
      return false
    }

    return true
  })

  provinceDisabled = computed(() => {
    const { extendName } = this.root.areaForm$() || {}

    if (extendName === 'city_bio_region' || extendName === 'province_name' || isUndefinedOrNull(extendName)) {
      return false
    }

    return true
  })

  constructor() {
    effect(() => {
      const { extendName } = this.root.areaForm$() || {}

      if (extendName) {
        if (extendName === 'is_top20_city') {
          this.area.set('city_name')
        } else {
          this.area.set(extendName)
        }
      }
    })

    effect(() => {
      let fliter_options: any = {
        area: this.area(),
      }

      if (this.dataType() === 'subject') {
        fliter_options = {
          area: this.area(),
          c_ord_type: this.c_ord_type(),
          c_firlev_channel: this.c_firlev_channel(),
          c_scene_type: this.c_scene_type(),
        }
      }

      console.log('埋点上报:筛选项点击', fliter_options, this.page_name)

      this.buriedPointService.addStat('dida_dpm_caerus_diagnose_R_fliter', {
        page_name: this.page_name,
        fliter_options,
      })
    })
  }

  download() {
    this.dataType() === 'subject' ? this.subjectTable().download() : this.transformTable().download()
    this.buriedPointService.addStat('dida_dpm_caerus_diagnose_download_Click', {
      page_name: this.page_name,
    })
  }

  ngAfterViewInit(): void {
    this.apiService.fetchConfig('fluctuation_position_area').subscribe(res => {
      if (res.data) {
        const { subject } = res.data
        const { filter } = subject

        filter.forEach(item => {
          const { extendName, value } = item
          this[extendName + 'Option'].set(value.map(child => child.value))
          value.forEach(child => {
            this[extendName + 'Map'].set(child.value, new FilterItemVo(extendName, [child]))
          })
        })
      }
    })
  }

  onVisible(graph_position: number) {
    console.log('曝光埋点上报:页面内的图表（分区）', graph_position)
    this.buriedPointService.addStat('dida_dpm_caerus_indicator_graph_exposure', {
      page_name: this.page_name,
      graph_position,
    })
  }

  onTabClick(tab_name: string, tab_position: number) {
    console.log('埋点上报：点击 -> tab按钮', tab_name, tab_position)
    this.buriedPointService.addStat('dida_dpm_caerus_fluctuation_diagnose_tab_click', {
      page_name: this.page_name,
      tab_position,
      tab_name,
    })
  }
}
