import { AfterViewInit, ChangeDetectionStrategy, Component, DestroyRef, inject, signal } from '@angular/core';
import { FormGroup, FormsModule } from '@angular/forms';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzAnchorModule } from 'ng-zorro-antd/anchor';
import { NzDrawerService } from 'ng-zorro-antd/drawer';
import { takeUntilDestroyed, toSignal } from '@angular/core/rxjs-interop';
import { addDays, format, } from 'date-fns';

import { CaerusApiService } from '@api/caerus';
import { BuriedPointService } from '@common/service';
import { DimensionValueForm, FilterItemForm, QueryEngineFormService, setDefaultDateRange, setDefaultMonthRange, setDefaultWeekRange } from '@common/service/query-engine';
import { PAGE_NAME, PageEnterLeaveDirective } from '@common/directive';
import { DimensionValueMenuVo } from '@api/caerus/model';
import { SkeletonComponent    } from '@shared/components/skeleton';
import { SelfhelpBtnComponent } from '@shared/components/selfhelp-btn';
import { BackTopComponent     } from '@shared/components/back-top';
import { NavigationComponent  } from '@shared/components/navigation';
import { AreaSelectComponent  } from '@shared/components/area-select';
import { DateCompareComponent } from '@shared/components/date-compare';

import { FluctuationPositioningComponent } from './fluctuation-positioning';
import { FluctuationDiagnosisComponent } from './fluctuation-diagnosis';


@Component({
  selector: 'app-diagnosis',
  templateUrl: './diagnosis.component.html',
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: {
    class: 'block'
  },
  hostDirectives: [
    PageEnterLeaveDirective,
  ],
  imports: [
    // DatePipe,
    // RouterLink,
    FormsModule,
    NzAnchorModule,
    NzButtonModule,
    NavigationComponent,
    AreaSelectComponent,
    DateCompareComponent,
    FluctuationPositioningComponent,
    FluctuationDiagnosisComponent,
    // IconDataTrendComponent,
    // IconBackTopComponent,
    SelfhelpBtnComponent,
    BackTopComponent,
    SkeletonComponent,
  ],
  providers: [
    { provide: PAGE_NAME, useValue: 'fluctuate-diagnosis' },
    QueryEngineFormService
  ],
})
export class DiagnosisComponent implements AfterViewInit {

  readonly destroyRef = inject(DestroyRef);
  readonly formService = inject(QueryEngineFormService);
  readonly buriedPointService = inject(BuriedPointService);
  readonly drawerService = inject(NzDrawerService);
  readonly apiService = inject(CaerusApiService);
  
  querying = signal(false);
  area = signal(null);
  areaForm = new FormGroup(new FilterItemForm({}, new FormGroup(new DimensionValueForm())));
  areaForm$ = toSignal(this.areaForm.valueChanges);
  commonData = signal<string>(null);
  
  ngAfterViewInit(): void {
    this._subscribeToDtTypeChange();
    this.formService.dt.reset({
      startTime: format(addDays(new Date(), -7), 'yyyy-MM-dd'),
      endTime: format(addDays(new Date(), -1), 'yyyy-MM-dd'),
    })
    this.formService.addDateCompare(
      format(addDays(new Date(), -14), 'yyyy-MM-dd'),
      format(addDays(new Date(), -8), 'yyyy-MM-dd')
    );
    this.fetchCommon();
  }


  private fetchCommon() {
    this.apiService.fetchFlucatuationUpdateTime().subscribe(res => {
      if (!res.data) {
        return;
      }

      setTimeout(() => {
        this.commonData.set(res.data.minDt);
      }, 0)
    });
  }


  private _subscribeToDtTypeChange() {
    this.formService.form.get('dtType').valueChanges.pipe(
      takeUntilDestroyed(this.destroyRef)
    ).subscribe(type => {
      const dtControl = this.formService.dt;
      const compareDtControl = this.formService.compareDt;
      
      if (type === 'dt') {
        setDefaultDateRange(dtControl, compareDtControl);
      }
      
      if (type === 'yw') {
        setDefaultWeekRange(dtControl, compareDtControl);
      }
      
      if (type === 'ym') {
        setDefaultMonthRange(dtControl, compareDtControl);
      }
    })
  }

  onAreaChange(area: DimensionValueMenuVo) {
    const { filterItems } = this.formService;

    if (area) {
      const { extendName, key, value } = area;

      if (!filterItems.controls.includes(this.areaForm)) {
        filterItems.push(this.areaForm);
      }
      
      this.areaForm.patchValue({ extendName, value: [{key, value}] });
    } else {
      this.areaForm.patchValue({ extendName: null });
      filterItems.clear();
    }
  }


  async reset() {
    this.area.set(null);
  }
  
}
