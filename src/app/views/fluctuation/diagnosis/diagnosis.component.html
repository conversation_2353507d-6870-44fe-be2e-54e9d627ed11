<app-navigation>
  <ng-container ngProjectAs="[extra]">
    @if (commonData()) {
      <span class="text-xs">指标数据已全部更新至 {{commonData()}}	</span>
    } @else {
      <app-skeleton class="w-56 h-4" />
    }
  </ng-container>
</app-navigation>

<div class="sticky top-0 z-40 py-2 mb-1 bg-white" [class.shadow-lg]="true">
  <div class="flex justify-center">
    <div class="max-w-(--breakpoint-2xl) flex-1 min-w-0 flex items-center gap-x-2 text-xs pl-5">
      <app-date-compare class="!px-0 -ml-10" gap-x="0.5" [class.pointer-events-none]="querying()" />

      <div class="flex items-center">
        <label class="inline-flex items-center font-bold leading-0 whitespace-nowrap">区域：</label>
        <app-area-select [(ngModel)]="area" (ngModelChange)="onAreaChange($event)" />
      </div>

      <button nz-button nzType="default" (click)="reset()">重置</button>
    </div>
    <!-- <div class="w-17.5"></div> -->
  </div>
</div>

<!-- <pre class="text-xs">{{formService.value() | json}}</pre> -->

<div class="flex justify-center">
  <div class="max-w-(--breakpoint-2xl) flex-1 min-w-0">
    <app-fluctuation-positioning id="anchor-fluctuation-positioning" />
    <app-fluctuation-diagnosis   id="anchor-fluctuation-diagnosis" />
  </div>

  <div class="tools-bar pointer-events-none">
    <app-selfhelp-btn />

    <div class="my-auto flex flex-col justify-center pointer-events-auto">
      <nz-anchor [nzBounds]="200" [nzTargetOffset]="50">
        <nz-link nzTitle="表现" nzHref="#anchor-fluctuation-performance"></nz-link>
        <nz-link nzTitle="区域" nzHref="#anchor-fluctuation-area"></nz-link>
        <nz-link nzTitle="定位" nzHref="#anchor-fluctuation-diagnosis"></nz-link>
        <nz-link nzTitle="细分" nzHref="#anchor-fluctuation-reason"></nz-link>
      </nz-anchor>
    </div>

    <app-back-top />
  </div>
</div>