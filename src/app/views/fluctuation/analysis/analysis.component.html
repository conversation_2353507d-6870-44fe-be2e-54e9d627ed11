<app-navigation>
  <ng-container ngProjectAs="[title]">
    <span routerLink="/cockpit" class="cursor-pointer">Caerus</span>- 波动分析
  </ng-container>
</app-navigation>


<div class="max-w-(--breakpoint-2xl) mx-auto flex flex-col gap-y-10 p-5 pb-10">
  <header class="flex justify-center">
    <div>
      <nz-date-picker nzMode="month" [nzFormat]="'yyyy/MM'" [nzDisabledDate]="disabledMonth" [(ngModel)]="month" />
    </div>
    <div class="flex-1 min-w-0 flex items-center justify-center">
      <app-radio-group [(ngModel)]="type" class="dida-radio-wrapper">
        @for (item of types(); track $index) {
          <app-radio class="dida-radio" activeClass="dida-radio-active" [value]="item.value">{{item.label}}波动</app-radio>
        }
      </app-radio-group>
    </div>
    <div class="w-34"></div>
  </header>

  <div class="bg-neutral-100 rounded-sm py-3 px-5 text-neutral-400 text-xs leading-5">
    <h4 class="flex items-center gap-x-1.5">
      <svg class="inline-flex h-em w-em text-base text-primary" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round">
          <g transform="translate(1.000000, 1.000000)" fill-rule="nonzero" stroke="currentColor" stroke-width="2">
            <path d="M14.2105301,1.8947326 L1.42105301,1.8947326 C0.636229115,1.8947326 0,2.53096172 0,3.31578561 L0,16.578947 C0,17.3637946 0.636229115,18 1.42105301,18 L15.6315831,18 C16.4164306,18 17.0526361,17.3637946 17.0526361,16.578947 L17.0526361,9.9473663 L17.0526361,6.63157596"></path>
            <polyline points="12.4307559 4.01934895 14.4404091 2.00967211 12.4307559 0"></polyline>
            <line x1="8.52631803" y1="6.15789162" x2="8.52631803" y2="14.2105253"></line>
            <line x1="12.3157927" y1="9.47368197" x2="12.3157927" y2="14.2105253"></line>
            <line x1="4.73684335" y1="9.47368197" x2="4.73684335" y2="14.2105253"></line>
          </g>
        </g>
      </svg>
      波动占比说明：
    </h4>

    <div class="px-6">
      <p>波动占比=（A维度本期完单量-A维度上期完单量）/大盘上期的完单量，反映的是单个维度，如城市、区域、订单类型（城际/市内）等，在某一时间段的波动对大盘的影响；</p>
      <p>波动占比使用完单量作为衡量指标，支持月环比和年同比两种计算口径，以城市维度为例，成都的月环比波动占比=（成都本月完单量-成都上月完单量）/大盘上月的完单量；</p>
      <p class="text-primary">假如成都的波动占比按照月环比计算是+15%，意味着在其他城市不变的情况下，成都相对上个月的完单量波动，会让大盘比上个月增长15%；</p>
    </div>
  </div>

  <div>
    <span class="whitespace-nowrap">波动参照：</span>
    <nz-radio-group [(ngModel)]="dateType">
      <label nz-radio nzValue="month">月环比</label>
      <label nz-radio nzValue="year">年同比</label>
    </nz-radio-group>
  </div>

  <section class="relative flex items-center justify-center mx-auto w-3/4 h-80 rounded-lg">
    @if (option()) {
      <app-graph [options]="option()" />
    }
  </section>

  <ng-template #titleTemplate let-data>
    <span class="text-xs opacity-70">{{data?.metricsName}}</span>
    @if (data?.metricsDetail) {
      <p>{{data?.metricsDetail}}</p>
    }
  </ng-template>

  <article>
    <nz-table #groupingTable [nzData]="listOfDisplayData()" nzBordered nzShowSizeChanger nzSize="small" nzTitle="注：此处数据均为全部渠道数据。" [nzShowTotal]="totalTemplate">
      <thead>
        @if (titleOfData()) {
          <tr>
            <th rowspan="2" nzWidth="120px" nzCustomFilter>
              {{title()}}
              <nz-filter-trigger [(nzVisible)]="visible" [nzActive]="searchValue()?.length > 0" [nzDropdownMenu]="menu">
                <SearchIcon />
              </nz-filter-trigger>

              <nz-dropdown-menu #menu="nzDropdownMenu">
                <div class="ant-table-filter-dropdown">
                  <div class="search-box">
                    <input type="text" nz-input placeholder="搜索城市" [(ngModel)]="searchValue" />
                    <button nz-button nzSize="small" nzType="primary" (click)="search()" class="search-button">搜索</button>
                    <button nz-button nzSize="small" (click)="reset()">重置</button>
                  </div>
                </div>
              </nz-dropdown-menu>
            </th>
            <th colspan="2" dida-tooltips [tip]="titleTemplate" [context]="titleOfData()[0]" placement="top,topLeft,right">完单量</th>
            <th rowspan="2" [nzSortFn]="sortFluctuationRatioFn">波动占比<br>(对大盘影响)</th>
            <th colspan="2" dida-tooltips [tip]="titleTemplate" [context]="titleOfData()[1]">乘客DAU</th>
            <th colspan="2" dida-tooltips [tip]="titleTemplate" [context]="titleOfData()[2]">乘客下单率</th>
            <th colspan="2" dida-tooltips [tip]="titleTemplate" [context]="titleOfData()[3]">乘客人均下单频次</th>
            <th colspan="2" dida-tooltips [tip]="titleTemplate" [context]="titleOfData()[4]" placement="top,topRight,left">下单接单率(乘客)</th>
            <th colspan="2" dida-tooltips [tip]="titleTemplate" [context]="titleOfData()[5]" placement="top,topRight,left">接单完单率(乘客)</th>
            <th colspan="2" dida-tooltips [tip]="titleTemplate" [context]="titleOfData()[6]" placement="top,topRight,left">车主人均接单频次</th>
          </tr>
        }

        <tr>
          @for (column of listOfColumns; track $index) {
            <th [(nzSortOrder)]="column.sortOrder" [nzSortFn]="column.sortFn">{{column.name}}</th>
          }
        </tr>
      </thead>

      <tbody>
        @for (data of groupingTable.data; track $index) {
          <tr>
            <!-- 城市 -->
            <td align="center">
              <a (click)="drill(data.dimenValue, 'table')">{{ data.dimenValue }}</a>
            </td>

            <!-- 完单量 -->
            <td align="right">{{ data.finishOrderCntValue | number:'1.0-0' }}</td>
            <td align="right" [class.text-rose-400]="data.finishOrderCntIncrease > 0" [class.text-emerald-400]="data.finishOrderCntIncrease < 0">
              {{ data.finishOrderCntIncrease | number }} ({{data.finishOrderYearOrMonthGrowth}}%)
            </td>

            <!-- 波动占比 -->
            <td align="right">
              {{data.finishOrderCntFluctuationRatio | number}}@if (data.finishOrderCntFluctuationRatio !== null) {%}
            </td>

            <!-- 乘客DAU -->
            <td align="right">
              {{ (data.dauValue | number:'1.0-0') || '-' }}
            </td>
            <td align="right" [class.text-rose-400]="data.dauIncrease > 0" [class.text-emerald-400]="data.dauIncrease < 0">
              {{ (data.dauIncrease | number) || '-' }}
            </td>

            <!-- 下单率 -->
            <td align="right">
              {{ (data.bookDauRateValue | number) || '-' }}@if (data.bookDauRateValue !== null) {%}
            </td>
            <td align="right" [class.text-rose-400]="data.bookDauRateIncrease > 0" [class.text-emerald-400]="data.bookDauRateIncrease < 0">
              {{ (data.bookDauRateIncrease | number) || '-' }}@if (data.bookDauRateIncrease !== null) {%}
            </td>

            <!-- 人均下单频次 -->
            <td align="right">{{ data.passAvgBookCntValue | number }}</td>
            <td align="right" [class.text-rose-400]="data.passAvgBookCntIncrease > 0" [class.text-emerald-400]="data.passAvgBookCntIncrease < 0">
              {{ data.passAvgBookCntIncrease | number }}
            </td>

            <!-- 下单接单率 -->
            <td align="right">{{ data.bookReplyRateValue | number }}@if (data.bookReplyRateValue !== null) {%}</td>
            <td align="right" [class.text-rose-400]="data.bookReplyRateIncrease > 0" [class.text-emerald-400]="data.bookReplyRateIncrease < 0">
              {{ data.bookReplyRateIncrease | number }}@if (data.bookReplyRateIncrease !== null) {%}
            </td>

            <!-- 接单完单率 -->
            <td align="right">{{ data.replyFinishRateValue | number }}@if (data.replyFinishRateValue !== null) {%}</td>
            <td align="right" [class.text-rose-400]="data.replyFinishRateIncrease > 0" [class.text-emerald-400]="data.replyFinishRateIncrease < 0">
              {{ data.replyFinishRateIncrease | number }}@if (data.replyFinishRateIncrease !== null) {%}
            </td>

            <!-- 车主接单频次 -->
            <td align="right">{{ data.driAvgReplyCntValue | number }}</td>
            <td align="right" [class.text-rose-400]="data.driAvgReplyCntIncrease > 0" [class.text-emerald-400]="data.driAvgReplyCntIncrease < 0">
              {{ data.driAvgReplyCntIncrease | number }}
            </td>
          </tr>
        }
      </tbody>
    </nz-table>
  </article>
</div>

<div class="fixed-widgets justify-center gap-y-5!">
  <div class="flex flex-col items-center justify-center w-full btn">
    <DataTrendIcon iconBtn nzTooltipTitle="自助分析" nzTooltipPlacement="left" nz-tooltip class="xxl text-4xl" />
    <span class="text-xs text-slate-500 scale-75 -translate-y-1 whitespace-nowrap origin-top">自助分析</span>
  </div>

  <div class="flex justify-center w-full animate__swing">
    <button (click)="goHome()" class="btn flex flex-col items-center justify-center rounded-sm bg-[#61ac85] text-white text-xs w-8 h-8 leading-none">
      <span class="inline-block scale-90">返回</span>
      <span class="inline-block scale-90">大盘</span>
    </button>
  </div>

  <div class="flex-1 flex flex-col justify-center"></div>
</div>

<ng-template #totalTemplate let-total> 共 {{ total }} 条记录 </ng-template>

<ng-template #footerTemplate>
  <span class="text-xs text-neutral-400 scale-75 inline-block origin-left">
    注：波动占比反应的是单个城市/区域，或者某种订单类型（城际/市内），在某一时间段的波动对大盘的影响；以成都为例，它的波动占比=（成都本期完单量-成都上期完单量）/大盘本期的完单量。 
  </span>
</ng-template>