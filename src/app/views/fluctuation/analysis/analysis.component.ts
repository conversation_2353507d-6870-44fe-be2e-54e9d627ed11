import { DatePipe, DecimalPipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, DestroyRef, computed, effect, inject, input, linkedSignal, model, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { addDays, differenceInCalendarMonths, addMonths } from 'date-fns';
import { NzTableModule, NzTableSortFn, NzTableSortOrder } from 'ng-zorro-antd/table';
import { NzDropDownModule } from 'ng-zorro-antd/dropdown';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzButtonModule } from 'ng-zorro-antd/button';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzDrawerService } from 'ng-zorro-antd/drawer';
import { NzRadioModule } from 'ng-zorro-antd/radio';

import { BuriedPointService } from '@common/service';
import { PAGE_NAME, PageEnterLeaveDirective } from '@common/directive';
import { getMaxLength, fillEmpty, isNotUndefinedOrNotNull, toNumber, transformValueSuffix } from '@common/function';
import { FluctuationDetailInfo, FluctuationInput, MetricsInfoVo } from '@views/cockpit/models';
import { RadioModule } from '@shared/modules/headless/radio';
import { IconDataTrendComponent, IconSearchComponent } from '@shared/modules/icons';
import { GraphComponent, NegativeStack, NegativeStackSeries } from '@shared/components/graph';
import { TooltipsDirective } from '@shared/modules/tooltips';
import { CockpitApiService } from '@views/cockpit/api';
import { FluctuateDrillService } from '@views/cockpit/services';
import { NavigationComponent } from '@shared/components/navigation';
import { SearchPipe } from '@shared/pipes/search';
import { SwitchMap } from '@common/decorator';


interface ColumnItem {
  name: string;
  sortOrder?: NzTableSortOrder | null;
  sortFn?: NzTableSortFn<FluctuationDetailInfo> | null;
}

@Component({
  selector: 'app-analysis',
  templateUrl: './analysis.component.html',
  styleUrl: './analysis.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    PageEnterLeaveDirective,
  ],
  imports: [
    RouterLink,
    FormsModule,
    NzInputModule,
    NzButtonModule,
    NzRadioModule,
    NzDatePickerModule,
    NzTableModule,
    NzToolTipModule,
    NzDropDownModule,
    RadioModule,
    NavigationComponent,
    IconSearchComponent,
    IconDataTrendComponent,
    GraphComponent,
    TooltipsDirective,
    DecimalPipe,
  ],
  providers: [
    { provide: PAGE_NAME, useValue: 'fluctuate-analysis-details' },
    DatePipe,
    SearchPipe,
  ]
})
export class AnalysisComponent {

  readonly router = inject(Router);
  readonly route = inject(ActivatedRoute);
  readonly datePipe = inject(DatePipe);
  readonly searchPipe = inject(SearchPipe);
  readonly apiService = inject(CockpitApiService);
  readonly destroyRef = inject(DestroyRef);
  readonly buriedPointService = inject(BuriedPointService);
  readonly fluctuateDrillService = inject(FluctuateDrillService);
  readonly yesterday = signal(addDays(new Date(), -1));
  readonly drawerService = inject(NzDrawerService);
  
  type = model<'1' | '2' | '3' | '4'>('1');
  date = input<string>();

  types = signal([
    { label: '核心区域', value: '1' },
    { label: '省份', value: '4' },
    { label: '城市', value: '2' },
    { label: '城际/市内', value: '3' },
  ]);

  title = computed(() => {
    if (this.type()) {
      const { label } = this.types().find(item => item.value === this.type())
      return label;
    }
    return '';
  })
  
  today = signal(new Date());
  dateType = signal<'month' | 'year'>('month');
  month = linkedSignal({
    source: () => this.today(),
    computation: (source) => {
      if (source.getDate() === 1) {
        return this.yesterday()
      }
      return this.today();
    }
  });
  searchValue = signal<string>(null);
  visible = signal<boolean>(null);

  option = signal<NegativeStack>(null);
  titleOfData = signal<MetricsInfoVo[]>([]);
  listOfData = signal<FluctuationDetailInfo[]>([]);
  listOfDisplayData = signal<FluctuationDetailInfo[]>([]);
  listOfColumns: ColumnItem[] = [
    {
      name: '数值',
      sortFn: (a: FluctuationDetailInfo, b: FluctuationDetailInfo) => a.finishOrderCntValue - b.finishOrderCntValue,
      sortOrder: null,
    },
    {
      name: '涨跌幅',
      sortFn: (a: FluctuationDetailInfo, b: FluctuationDetailInfo) => a.finishOrderCntIncrease - b.finishOrderCntIncrease,
      sortOrder: null,
    },

    {
      name: '数值',
      sortFn: (a: FluctuationDetailInfo, b: FluctuationDetailInfo) => a.dauValue - b.dauValue,
      sortOrder: null,
    },
    {
      name: '涨跌幅',
      sortFn: (a: FluctuationDetailInfo, b: FluctuationDetailInfo) => a.dauIncrease - b.dauIncrease,
      sortOrder: null,
    },

    {
      name: '数值',
      sortFn: (a: FluctuationDetailInfo, b: FluctuationDetailInfo) => a.bookDauRateValue - b.bookDauRateValue,
      sortOrder: null,
    },
    {
      name: '涨跌幅',
      sortFn: (a: FluctuationDetailInfo, b: FluctuationDetailInfo) => a.bookDauRateIncrease - b.bookDauRateIncrease,
      sortOrder: null,
    },

    {
      name: '数值',
      sortFn: (a: FluctuationDetailInfo, b: FluctuationDetailInfo) => a.passAvgBookCntValue - b.passAvgBookCntValue,
      sortOrder: null,
    },
    {
      name: '涨跌幅',
      sortFn: (a: FluctuationDetailInfo, b: FluctuationDetailInfo) => a.passAvgBookCntIncrease - b.passAvgBookCntIncrease,
      sortOrder: null,
    },

    {
      name: '数值',
      sortFn: (a: FluctuationDetailInfo, b: FluctuationDetailInfo) => a.bookReplyRateValue - b.bookReplyRateValue,
      sortOrder: null,
    },
    {
      name: '涨跌幅',
      sortFn: (a: FluctuationDetailInfo, b: FluctuationDetailInfo) => a.bookReplyRateIncrease - b.bookReplyRateIncrease,
      sortOrder: null,
    },

    {
      name: '数值',
      sortFn: (a: FluctuationDetailInfo, b: FluctuationDetailInfo) => a.replyFinishRateValue - b.replyFinishRateValue,
      sortOrder: null,
    },
    {
      name: '涨跌幅',
      sortFn: (a: FluctuationDetailInfo, b: FluctuationDetailInfo) => a.replyFinishRateIncrease - b.replyFinishRateIncrease,
      sortOrder: null,
    },

    {
      name: '数值',
      sortFn: (a: FluctuationDetailInfo, b: FluctuationDetailInfo) => a.driAvgReplyCntValue - b.driAvgReplyCntValue,
      sortOrder: null,
    },
    {
      name: '涨跌幅',
      sortFn: (a: FluctuationDetailInfo, b: FluctuationDetailInfo) => a.driAvgReplyCntIncrease - b.driAvgReplyCntIncrease,
      sortOrder: null,
    },
  ];


  sortFluctuationRatioFn = (a: FluctuationDetailInfo, b: FluctuationDetailInfo) => {
    return a.finishOrderCntFluctuationRatio - b.finishOrderCntFluctuationRatio;
  };


  constructor() {
    // 根据路由参数设置month
    effect(() => {
      if (this.date() !== undefined) {
        const [year, month] = this.date().split('-').map(toNumber);
        const timestamp = new Date().setFullYear(year, month - 1);

        this.month.set(new Date(timestamp));
      }
    });


    effect(() => {
      if (!this.type()) {
        this.type.set('1');
      }
    })


    effect(() => {
      this.buriedPointService.addStat('dida_dpm_caerus_dimension_fluctuation_click', {
        source_type: this.type()
      });
    })


    effect(() => {
      this.buriedPointService.addStat('dida_dpm_caerus_bore_fluctuation', {
        caliber: this.dateType(),
        source_type: this.type(),
        page_name: 'fluctuate-analysis-details',
      });
    })

    
    effect(() => {
      const type = this.type();
      const area = '全国';
      const rideType = '全部';
      const dateType = this.dateType();
      const date = this.datePipe.transform(this.month(), 'yyyy-MM');
      const dataType = 'top50';

      this.fetchFluctuationDetail({ type, area, rideType, dateType, date, dataType });
      this.fetchFluctuationOverview({ type, area, rideType, dateType, date, dataType });
    })
  }


  @SwitchMap()
  fetchFluctuationDetail(body: FluctuationInput) {
    return this.apiService.fetchFluctuationDetail(body).subscribe(res => {
      if (res.data) {
        this.titleOfData.set(res.data.title);
        this.listOfDisplayData.set(res.data.list);
        this.listOfData.set(res.data.list);
      }
    })
  }


  @SwitchMap()
  fetchFluctuationOverview(body: FluctuationInput) {
    this.option.set(null);
    return  this.apiService.fetchFluctuationOverview(body).subscribe(res => {
      if (res.data) {
        const chart = new NegativeStack();
        const { negative, forward, totalIncrease, totalYearOrMonthGrowth } = res.data;
        const negativeCategories = negative.map(item => item.dimenValue);
        const forwardCategories  = forward.map(item => item.dimenValue);
        const negativeIncrease   = negative.map(item => item.increase);
        const forwardIncrease    = forward.map(item => item.increase);
        const negativeData       = negative.map(item => ({ y: item.increase, fluctuationRatio: item.fluctuationRatio, ratio: this.dateType() === 'month' ? item.quarterOnQuarterGrowth : item.yearOnYearGrowth }));
        const forwardData        = forward.map(item => ({ y: item.increase, fluctuationRatio: item.fluctuationRatio, ratio: this.dateType() === 'month' ? item.quarterOnQuarterGrowth : item.yearOnYearGrowth }));
        const max = Math.max(
          Math.abs(totalIncrease ?? 0),
          Math.abs(negativeIncrease[0] ?? 0), 
          forwardIncrease[0] ?? 0,
        ); // 有时降幅绝对值可能会比涨幅绝对值大，所以需要使用max获取最大值

        if (negative.length > 0) {
          chart.yAxis.min = -(max);
        }

        if (forward.length > 0) {
          chart.yAxis.max = max;
        }
        
        if (this.type() === '3') {
          chart.setTheme(['#5087ec', '#de868f']);
          chart.plotOptions.series.stacking = undefined;
        }

        const maxLength = getMaxLength(negativeCategories, forwardCategories);

        chart.chart.spacing = [20, 50, 15, 50];
        chart.setCategories(0, fillEmpty(maxLength)(negativeCategories));
        chart.setCategories(1, fillEmpty(maxLength)(forwardCategories));

        chart.setValue([
          new NegativeStackSeries('完单量降幅', negativeData, 0),
          new NegativeStackSeries('完单量涨幅', forwardData,  1),
        ]);

        if (isNotUndefinedOrNotNull(totalIncrease)) {
          const trendText  = totalIncrease >= 0 ? '涨幅' : '降幅';
          const labelTitle = `大盘完单量${trendText}`;
          const labelValue = Intl.NumberFormat().format(totalIncrease);
          const labelRatio = `(${transformValueSuffix(totalYearOrMonthGrowth)})`;
          const labelText  = `${labelTitle} ${labelValue} ${labelRatio}`;
          const labelColor = totalIncrease >= 0 ? '#fb7185' : '#34d399';

          chart.setPlotLines(totalIncrease, labelText, labelColor);          
        }

        this.option.set(chart.getOption());

        chart.click.pipe(
          takeUntilDestroyed(this.destroyRef)
        ).subscribe(({ name }) => {
          this.drill(name, 'graph');
        })
      }
    })
  }


  disabledMonth = (current: Date): boolean => {
    // Can not select days before today and today
    const startDateForMonth = new Date().setDate(1);
    const dateRight = addMonths(startDateForMonth, 1);
    
    return differenceInCalendarMonths(current, dateRight) > -1;
  }


  drill(value: string, enter_type: 'graph' | 'table') {
    this.router.navigate(['/cockpit']);

    if (this.type() === '3') {
      this.fluctuateDrillService.rideType.set(value as any);
    } else {
      this.fluctuateDrillService.areaName.set(value);
    }

    this.buriedPointService.addStat('dida_dpm_caerus_drill_fluctuation', {
      source_type: this.type(),
      area_name: value,
      page_name: 'fluctuate-analysis-details',
      enter_type
    });
  }


  goHome() {
    this.fluctuateDrillService.reset();
    this.router.navigate(['/cockpit'], { relativeTo: this.route });
    this.buriedPointService.addStat('dida_dpm_caerus_back_large_cup', {
      source_type: 'fluctuate-analysis-details'
    });
  }
  

  reset(): void {
    this.searchValue.set(null);
    this.search();
  }


  search(): void {
    const list = this.searchPipe.transform(this.listOfData(), this.searchValue(), 'dimenValue');

    this.visible.set(false);
    this.listOfDisplayData.set(list);
  }


}
