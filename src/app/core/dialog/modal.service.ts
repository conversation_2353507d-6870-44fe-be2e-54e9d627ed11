import { Injectable, Injector, inject } from '@angular/core';
import { ComponentType, Overlay, OverlayConfig } from '@angular/cdk/overlay';
import { ComponentPortal } from '@angular/cdk/portal';
import { ModalRef } from './modal-ref';
import { MODAL_DATA, ModalConfig } from './modal-config';

@Injectable()
export class ModalService {

  readonly #overlay = inject(Overlay);
  readonly #injector = inject(Injector);

  #modalRef: ModalRef<any> | null;

  open<T>(
    content: ComponentType<T>,
    userConfig?: ModalConfig
  ): ModalRef<T> {
    const config = {...new ModalConfig(), ...userConfig};
    const overlayRef = this._createOverlay();
    const modalRef = new ModalRef<T>(overlayRef);
    const _injector = this._createInjector(config, modalRef);
    const portal = new ComponentPortal(content, undefined, _injector);
    const contentRef = overlayRef.attach(portal);
    const modalContainerRef = overlayRef.hostElement.parentElement;

    modalContainerRef.classList.add('z-999!');
    modalRef.instance = contentRef.instance;
    
    this.#modalRef = modalRef;
    return this.#modalRef;
  }


  private _createOverlay() {
    const overlayConfig = new OverlayConfig();
    const positionStrategy = this.#overlay.position().global();
    
    positionStrategy.centerHorizontally();
    positionStrategy.centerVertically();
    overlayConfig.scrollStrategy = this.#overlay.scrollStrategies.block();
    overlayConfig.positionStrategy = positionStrategy;
    overlayConfig.disposeOnNavigation = true;

    return this.#overlay.create(overlayConfig);
  }


  private _createInjector<T>(config: ModalConfig, modalRef: ModalRef<T>): Injector {
    const userInjector = config && config.viewContainerRef && config.viewContainerRef.injector;

    return Injector.create({
      parent: userInjector || this.#injector,
      providers: [
        { provide: ModalRef, useValue: modalRef },
        { provide: MODAL_DATA, useValue: config.data }
      ],
    });
  }


  close(): void {
    if (this.#modalRef) {
      this.#modalRef.close();
    }
  }
  
}
