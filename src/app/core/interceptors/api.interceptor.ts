import {
  HttpErrorResponse,
  HttpHandlerFn,
  HttpInterceptorFn,
  HttpRequest,
  HttpStatusCode,
} from '@angular/common/http';
import { catchError, throwError } from 'rxjs';
import { inject } from '@angular/core';

import { Util } from '@common/class';
import { UserInfoService } from '../../api/user-info.service';

export const apiInterceptor: HttpInterceptorFn = (req, next: HttpHandlerFn) => {
  let resetReq: HttpRequest<any>;
  const userInfo = inject(UserInfoService);

  if (req.url.indexOf('/current_user') < 0) {
    if (userInfo.ts && userInfo.privateName) {
      resetReq = req.clone({
        setHeaders: {
          'token-ts': userInfo.ts,
          'token-user': userInfo.privateName,
        },
        withCredentials: true,
      });
    }
  } else {
    resetReq = req;
  }

  return next(resetReq).pipe(
    catchError((err) => {
      console.log('err', err);
      if (err instanceof HttpErrorResponse) {
        if (<string>err.error?.message) {
          if (
            err.status === HttpStatusCode.Forbidden ||
            err.status === HttpStatusCode.Found ||
            err.status === 0
          ) {
            // Util.jumpTeese();
            debugger;
            location.reload();
          }

          if ((<string>err.error.message).indexOf('Unexpected token') !== -1) {
            console.warn('登录过期');
            // Util.jumpTeese();
            location.reload();
          }
        } else {
          console.error('请求失败', err);
        }
      }

      return throwError(() => err);
    })
  );
};
