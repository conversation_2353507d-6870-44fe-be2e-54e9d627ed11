import { AfterViewInit, ChangeDetectionStrategy, Component, HostListener, inject, input, signal } from '@angular/core';
import { DragDropModule } from '@angular/cdk/drag-drop';
import { OverlayModule } from '@angular/cdk/overlay';
import { AnimationEvent } from '@angular/animations';

import { IconCloseComponent } from '@shared/modules/icons';
import { Animations } from '@common/animation';
import { ModalService } from './modal.service';

export type ModalVisibleState = 'invisible' | 'visible' | 'leave';

@Component({
  selector: 'app-modal',
  template: `
    <div [@opacityAnimation]="visible()" class="modal-backdrop"></div>

    <div class="modal">
      <div class="absolute inset-0 z-0" (click)="close()"></div>
      <div [@modalOutToInAnimation]="visibleState()" (@modalOutToInAnimation.done)="animationDone($event)" class="relative z-10 m-auto">
        <div cdkDragBoundary="body" cdkDrag class="modal-content">
          <header cdkDragHandle class="modal-header">
            <div class="flex-1">
              <h3 class="modal-title">{{title()}}</h3>
              @if (subTitle()) {
                <sub class="modal-subtitle">{{subTitle()}}</sub>
              }
            </div>
            
            <CloseIcon iconBtn class="xl" (click)="close()" />
          </header>
          
          <div class="modal-body" [innerHTML]="content()"></div>

          <!-- <footer class="modal-footer">
            <button (click)="close()" class="btn-confirm">关闭</button>
          </footer> -->
        </div>
      </div>
    </div>
  `,
  styleUrl: 'modal.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
  animations: [
    ...Animations.OpacityAnimation,
    ...Animations.ModalOutToInAnimation,
  ],
  imports: [
    OverlayModule,
    DragDropModule,
    IconCloseComponent,
  ],
})
export class ModalComponent implements AfterViewInit {

  readonly modalService = inject(ModalService);

  title = input.required<string>();
  subTitle = input<string>();
  content = input.required<string>();
  width = input<string | number>();
  
  visible = signal(false);
  visibleState = signal<ModalVisibleState>('invisible');


  ngAfterViewInit(): void {
    this.visible.set(true);
    this.visibleState.set('visible');
  }


  animationDone(event: AnimationEvent): void {
    if (event.fromState === 'visible' && event.toState === 'leave') {
      this.modalService.close();
    }
  }
  

  @HostListener('document:keyup.escape')
  close() {
    this.visible.set(false);
    this.visibleState.set('leave');
  }
  
}
