import { Injectable, Injector, TemplateRef, ViewContainerRef } from '@angular/core';
import { TemplatePortal, ComponentPortal } from '@angular/cdk/portal';
import { Overlay, OverlayRef } from '@angular/cdk/overlay';


export interface ComponentType<T> {
  new (...args: any[]): T;
}


@Injectable()
export class ModalService {

  private overlayRef: OverlayRef | undefined;

  constructor(
    private readonly overlay: Overlay,
  ) { }


  open<T>(ref: ComponentType<T>, viewContainer: ViewContainerRef, injector?: Injector);
  open<T>(ref: TemplateRef<T>, viewContainer: ViewContainerRef);
  open<T>(ref: TemplateRef<T> | ComponentType<T>, viewContainer?: ViewContainerRef, injector?: Injector) {
    let portal: any;
    
    this.overlayRef = this.overlay.create({
      scrollStrategy: this.overlay.scrollStrategies.block(),
      positionStrategy: this.overlay.position().global().centerHorizontally().centerVertically(),
      disposeOnNavigation: true,
    });

    this.overlayRef.backdropClick().subscribe(() => {
      this.close();
    });

    this.overlayRef.outsidePointerEvents().subscribe(event => {
      console.log(event);
    })
    
    const dom = this.overlayRef.hostElement.parentElement.classList;

    dom.add('z-50!');
    dom.remove('z-1001!');

    if (ref instanceof TemplateRef) {
      portal = new TemplatePortal(ref, viewContainer);
      const { instance } = this.overlayRef.attach(portal);
      return instance;
    }
    else {
      portal = new ComponentPortal(ref, viewContainer, injector);
      const componentRef = this.overlayRef.attach(portal);
      const { instance } = componentRef;

      if (instance.ok) {
        instance.ok.subscribe(() => this.close());
      }

      if (instance.cancel) {
        instance.cancel.subscribe(() => this.close());
      }

      return componentRef;
    }
  }


  close(): void {
    // this.overlayRef?.dispose();
    this.overlayRef?.detach();
  }


}
