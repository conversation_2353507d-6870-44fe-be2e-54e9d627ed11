@reference "../../../../styles.css";

.dida-tooltip {
  @apply
    relative 
    flex flex-col gap-y-2
    px-3 py-2 
    w-full text-xs rounded
    break-all
    before:content-[''] 
    before:absolute 
    before:block 
    before:w-0
    before:h-0
    before:border-[5px]
    before:border-r-transparent
    before:border-b-transparent
    before:rotate-[135deg]
    before:z-10
  ;
}


.dida-tooltip-placement-leftTop {
  @apply 
    before:-right-[5px];
}


.dida-tooltip-placement-leftBottom {
  @apply 
    before:-right-[5px]
    before:bottom-[15%];
}


.dida-tooltip-placement-left {
  @apply 
    -translate-x-[5px]
    before:top-[50%]
    before:-right-[5px]
    before:-translate-y-[50%];
}


.dida-tooltip-placement-rightTop {
  @apply 
    translate-x-[15px]
    before:-rotate-45 
    before:-left-[5px];
}


.dida-tooltip-placement-rightBottom {
  @apply 
    translate-x-[15px]
    before:-rotate-45
    before:-left-[5px]
    before:bottom-[15%];
}


.dida-tooltip-placement-right {
  @apply
    translate-x-[15px]
    before:-rotate-45
    before:top-1/2
    before:-left-[5px]
    before:-translate-y-1/2;
}


.dida-tooltip-placement-top {
  @apply
    -translate-y-[5px]
    before:rotate-[-135deg] 
    before:left-1/2
    before:-bottom-[5px]
    before:-translate-x-1/2;
}


.dida-tooltip-placement-topLeft {
  @apply
    -translate-y-[5px]
    before:rotate-[-135deg] 
    before:left-[15%]
    before:-bottom-[5px];
}


.dida-tooltip-placement-topRight {
  @apply
    -translate-y-[5px]
    before:rotate-[-135deg] 
    before:right-[15%]
    before:-bottom-[5px];
}


.dida-tooltip-placement-bottom {
  @apply
    translate-y-[5px]
    /* translate-x-[2.5px] */
    before:rotate-45
    before:left-[50%]
    before:-top-[5px]
    before:-translate-x-[50%];
}


.dida-tooltip-placement-bottomLeft {
  @apply
    translate-y-[5px]
    before:rotate-45
    before:left-[15%]
    before:-top-[5px];
}


.dida-tooltip-placement-bottomRight {
  @apply
    translate-y-[5px]
    before:rotate-45
    before:right-[15%]
    before:-top-[5px];
}