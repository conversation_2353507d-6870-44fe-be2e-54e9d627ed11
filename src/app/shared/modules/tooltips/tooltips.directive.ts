import { ComponentPortal } from '@angular/cdk/portal';
import { ConnectedOverlayPositionChange, ConnectionPositionPair, Overlay, OverlayRef } from '@angular/cdk/overlay';
import { Directive, effect, ElementRef, HostListener, Input, OnDestroy, Optional, TemplateRef } from '@angular/core';

import { getPlacementName, POSITION_MAP } from '@common/const';
import { TooltipsComponent } from './tooltips.component';
import { ListboxComponent } from '../headless/listbox';

interface Placement {
  leftTop: string;
  leftBottom: string;
  left: string;
  rightTop: string;
  rightBottom: string;
  right: string;
  topLeft: string;
  topRight: string;
  top: string;
  bottomLeft: string;
  bottomRight: string;
  bottom: string;
}

@Directive({
  selector: '[appTooltips], [dida-tooltips]',
})
export class TooltipsDirective implements OnDestroy {

  @Input()
  tip: string | TemplateRef<void>;

  @Input()
  offsetX = 0;

  @Input()
  offsetY = 0;

  @Input()
  context: { [key:string]: any };

  @Input()
  set placement(value: keyof Placement | string) {
    const positions = value.split(',') as Array<keyof Placement>;
    
    if (positions) {
      this.positions = [];
      positions.forEach(position => {
        if (this.#positionMap.has(position)) {
          this.positions.push(this.#positionMap.get(position));
        }
      })
    }
  }

  @Input()
  backdropClass: string;

  @Input()
  hasBackdrop: boolean;

  @Input()
  tipClass = `bg-black/70 before:border-black/70 text-white`;

  private overlayRef: OverlayRef | undefined;
  private positions = [ POSITION_MAP.top ];
  

  #positionMap = new Map<keyof Placement, ConnectionPositionPair>([
    ['leftTop',     POSITION_MAP.leftTop],
    ['leftBottom',  POSITION_MAP.leftBottom],
    ['left',        POSITION_MAP.left],
    ['rightTop',    POSITION_MAP.rightTop],
    ['rightBottom', POSITION_MAP.rightBottom],
    ['right',       POSITION_MAP.right],
    ['topLeft',     POSITION_MAP.topLeft],
    ['topRight',    POSITION_MAP.topRight],
    ['top',         POSITION_MAP.top],
    ['bottomLeft',  POSITION_MAP.bottomLeft],
    ['bottomRight', POSITION_MAP.bottomRight],
    ['bottom',      POSITION_MAP.bottom],
  ]);

  constructor(
    @Optional()
    private readonly listbox: ListboxComponent,
    private readonly overlay: Overlay,
    private readonly host: ElementRef<HTMLElement>,
  ) {
    if (this.listbox) {
      effect(() => {
        if (!this.listbox.visible()) {
          this.close();
        }
      })
    }
  }


  ngOnDestroy(): void {
    this.close();
  }
  

  private get positionStrategy() {
    const { offsetX, offsetY, positions } = this;

    return this.overlay
      .position()
      .flexibleConnectedTo(this.host.nativeElement)
      .withPositions(positions)
      .withFlexibleDimensions(true)
      .withPush(true)
      .withGrowAfterOpen(true)
      .withDefaultOffsetX(offsetX)
      .withDefaultOffsetY(offsetY);
  }

  
  @HostListener('mouseenter')
  open() {
    const { positionStrategy, hasBackdrop, backdropClass } = this;
    
    if (this.tip) {
      this.overlayRef = this.overlay.create({
        scrollStrategy: this.overlay.scrollStrategies.reposition(),
        positionStrategy,
        hasBackdrop,
        backdropClass,
        disposeOnNavigation: true
      });

      const componentPortal = new ComponentPortal(TooltipsComponent);
      const { instance } = this.overlayRef.attach(componentPortal);

      positionStrategy.positionChanges.subscribe((position: ConnectedOverlayPositionChange) => {
        instance.placement = getPlacementName(position);
      })
      
      instance.tip = this.tip;
      instance.tipClass = this.tipClass;
      instance.context = this.context;
    }
  }

  
  @HostListener('mouseleave')
  close() {
    this.overlayRef && this.overlayRef.dispose();
  }

}
