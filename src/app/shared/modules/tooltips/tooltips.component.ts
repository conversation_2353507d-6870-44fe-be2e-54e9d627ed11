import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, NgTemplateOutlet } from '@angular/common';
import { Component, ChangeDetectionStrategy, Input, ChangeDetectorRef, inject, TemplateRef, signal } from '@angular/core';
import { Animations } from '@common/animation';

@Component({
  selector: 'app-tooltips',
  template: `
    <div
      class="dida-tooltip shadow-1 {{tipClass || ''}}"
      [style.maxWidth.px]="maxWidth"
      [@slideMotion]="'enter'"
      
      [ngClass]="'dida-tooltip-placement-' + placement"
    >
      @if (html()) {
        <span [innerHTML]="html()"></span>
      }
      @else {
        <ng-template *ngTemplateOutlet="template(); context: { $implicit: context }"></ng-template>
      }
    </div>
  `,
  styleUrls: ['./tooltips.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NgStyle,
    NgClass,
    NgTemplateOutlet,
  ],
  animations: [Animations.slideMotion],
})
export class TooltipsComponent {

  @Input()
  tipClass: string;
  
  @Input()
  set tip(value: string | TemplateRef<any>) {
    if (typeof value === 'string') {
      this.html.set(value);
    } else if (value instanceof TemplateRef) {
      this.template.set(value);
    }
  }

  @Input()
  context: { [key:string]: any };

  @Input()
  maxWidth = 260;

  @Input()
  set placement(value: string) {
    this._placement = value;
    this.cdr.detectChanges();
  }
  get placement() {
    return this._placement;
  }

  private _placement: string;
  readonly cdr = inject(ChangeDetectorRef);

  html = signal('');
  template = signal<TemplateRef<any>>(null);

}
