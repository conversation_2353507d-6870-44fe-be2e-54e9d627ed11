import { AfterViewChecked, ChangeDetectionStrategy, Component, ElementRef, input, signal, viewChild } from '@angular/core';
import { IconChevronLeftComponent, IconChevronRightComponent } from '../icons';

@Component({
  selector: 'app-carousel',
  template: `
    <button class="button-prev" (click)="prev()">
      <ChevronLeftIcon height="1.3em" />
    </button>

    <div [style.height.px]="height()" class="overflow-hidden">
      <div #carouselContainer class="flex flex-nowrap pb-10 overflow-x-auto">
        <ng-content />
      </div>
    </div>

    <button class="button-next" (click)="next()">
      <ChevronRightIcon height="1.3em" />
    </button>
  `,
  styleUrl: './carousel.component.css',
  host: {
    class: 'flex items-stretch gap-x-1 overflow-x-hidden',
    '[style.height.px]': 'height()'
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    IconChevronLeftComponent,
    IconChevronRightComponent,
  ],
})
export class CarouselComponent implements AfterViewChecked {

  carouselContainer = viewChild<ElementRef<HTMLElement>>('carouselContainer');

  distance = input(256);
  height = signal<number>(null);

  ngAfterViewChecked(): void {
    const { offsetHeight } = this.carouselContainer().nativeElement;
    this.height.set(offsetHeight - 40);
  }

  prev() {
    this.carouselContainer().nativeElement.scrollBy({ left: -this.distance(), behavior: 'smooth' });
  }

  next() {
    this.carouselContainer().nativeElement.scrollBy({ left: this.distance(), behavior: 'smooth' });
  }

}
