import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'GraphBarIcon',
  template: `
    <svg viewBox="0 0 20 19" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="barchar" transform="translate(0.666602, 0.000000)" fill-rule="nonzero">
          <path d="M0.593756795,7.12499456 C0.593756795,11.0600204 3.78372552,14.2499891 7.71875136,14.2499891 C11.6537772,14.2499891 14.8437459,11.0600204 14.8437459,7.12499456 C14.8437459,3.18996872 11.6537772,3.95516651e-16 7.71875136,3.95516651e-16 C3.78372552,3.95516651e-16 0.593756795,3.18996872 0.593756795,7.12499456 Z" id="路径" fill="#E9EAEB"></path>
          <polygon id="路径" fill="#BCC0C4" points="12.171873 3.26562976 12.7656298 3.26562976 12.7656298 5.0468784 12.171873 5.0468784"></polygon>
          <path d="M11.5781162,3.85936481 L13.3593648,3.85936481 L13.3593648,4.4531216 L11.5781162,4.4531216 L11.5781162,3.85936481 Z M16.328127,3.85936481 L16.9218621,3.85936481 L16.9218621,5.64061345 L16.328127,5.64061345 L16.328127,3.85936481 Z" id="形状" fill="#BCC0C4"></path>
          <path d="M15.7343702,4.4531216 L17.5156189,4.4531216 L17.5156189,5.0468784 L15.7343702,5.0468784 L15.7343702,4.4531216 Z M13.9531216,0.593756795 L14.5468784,0.593756795 L14.5468784,2.37500544 L13.9531216,2.37500544 L13.9531216,0.593756795 Z" id="形状" fill="#BCC0C4"></path>
          <polygon id="路径" fill="#BCC0C4" points="13.3593648 1.18749185 15.1406134 1.18749185 15.1406134 1.78124864 13.3593648 1.78124864"></polygon>
          <path d="M16.328127,17.5156189 L17.8124864,17.5156189 L17.8124864,18.5156189 L16.328127,18.5156189 L16.328127,17.5156189 Z M18.4062432,17.5156189 L19,17.5156189 L19,18.5156189 L18.4062432,18.5156189 L18.4062432,17.5156189 Z M0,17.5156189 L0.593756795,17.5156189 L0.593756795,18.5156189 L0,18.5156189 L0,17.5156189 Z" id="形状" fill="#2A5082"></path>
          <polygon id="路径" fill="#A3D4FF" points="10.3906243 7.71875136 13.0624973 7.71875136 13.0624973 16.6249946 10.3906243 16.6249946"></polygon>
          <path d="M13.3593648,16.9218621 L10.0937568,16.9218621 L10.0937568,7.42186209 L13.3593648,7.42186209 L13.3593648,16.9218621 Z M10.6874918,16.3281053 L12.7656298,16.3281053 L12.7656298,8.01561888 L10.6874918,8.01561888 L10.6874918,16.3281053 L10.6874918,16.3281053 Z" id="形状" fill="#2A5082"></path>
          <polygon id="路径" fill="#A3D4FF" points="2.67187296 3.56249728 5.34374592 3.56249728 5.34374592 16.6249946 2.67187296 16.6249946"></polygon>
          <path d="M5.64063519,16.9218621 L2.37500544,16.9218621 L2.37500544,3.26562976 L5.64063519,3.26562976 L5.64063519,16.9218621 Z M2.96876223,16.3281053 L5.0468784,16.3281053 L5.0468784,3.85936481 L2.96876223,3.85936481 L2.96876223,16.3281053 Z" id="形状" fill="#2A5082"></path>
          <polygon id="路径" fill="#E9EAEB" points="6.53125951 10.3906243 9.20313247 10.3906243 9.20313247 16.6249946 6.53125951 16.6249946"></polygon>
          <path d="M9.5,16.9218621 L6.23437024,16.9218621 L6.23437024,10.0937351 L9.5,10.0937351 L9.5,16.9218621 Z M6.82812704,16.3281053 L8.90624321,16.3281053 L8.90624321,10.6874918 L6.82812704,10.6874918 L6.82812704,16.3281053 Z" id="形状" fill="#2A5082"></path>
          <polygon id="路径" fill="#E9EAEB" points="14.2499891 11.5781162 16.9218621 11.5781162 16.9218621 16.6249946 14.2499891 16.6249946"></polygon>
          <path d="M17.2187514,16.9218621 L13.9531216,16.9218621 L13.9531216,11.2812486 L17.2187514,11.2812486 L17.2187514,16.9218621 Z M14.5468784,16.3281053 L16.6249946,16.3281053 L16.6249946,11.8749837 L14.5468784,11.8749837 L14.5468784,16.3281053 L14.5468784,16.3281053 Z M1.18751359,17.5156189 L15.7343702,17.5156189 L15.7343702,18.1093539 L1.18751359,18.1093539 L1.18751359,17.5156189 Z" id="形状" fill="#2A5082"></path>
        </g>
      </g>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconGraphBarComponent {

}
