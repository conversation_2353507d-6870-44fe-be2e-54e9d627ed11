import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'app-icon-users, UsersIcon',
  template: `
    <svg viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g>
          <circle fill="#F3EDFC" cx="10" cy="10" r="10"></circle>
          <path d="M9.9225,4.9585 C8.516,4.9585 7.374,6.0865 7.374,7.479 C7.374,8.8715 8.5155,10 9.9225,10 C11.33,10 12.4715,8.8715 12.4715,7.479 C12.4715,6.0865 11.331,4.9585 9.9225,4.9585 L9.9225,4.9585 Z" id="路径" fill="#8D53E6" fill-rule="nonzero"></path>
          <path d="M9.9225,4.9585 C8.516,4.9585 7.374,6.0865 7.374,7.479 C7.374,8.8715 8.5155,10 9.9225,10 C11.33,10 12.4715,8.8715 12.4715,7.479 C12.4715,6.0865 11.331,4.9585 9.9225,4.9585 L9.9225,4.9585 Z" id="路径" fill="#8D53E6" fill-rule="nonzero"></path>
          <path d="M8.9675,10.8405 C7.1495,10.8405 5.675,12.2985 5.675,14.0965 L5.675,14.3055 C5.675,15.041 7.1495,15.0415 8.9675,15.0415 L11.0915,15.0415 C12.9095,15.0415 14.3835,15.0145 14.3835,14.3055 L14.3835,14.0965 C14.3835,12.2985 12.9095,10.8405 11.0915,10.8405 L8.9675,10.8405 L8.9675,10.8405 Z" id="路径" fill="#8D53E6" fill-rule="nonzero"></path>
        </g>
      </g>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconUsersComponent {

}
