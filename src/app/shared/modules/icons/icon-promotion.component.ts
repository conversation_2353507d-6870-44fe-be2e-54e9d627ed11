import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'app-icon-promotion, PromotionIcon',
  template: `
    <svg viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g transform="translate(0.270313, 0.000000)" fill="currentColor" fill-rule="nonzero">
          <path d="M11.9210593,1.85797993 C12.5176381,1.85797993 13.0029982,2.34334002 13.0029982,2.9399188 C13.0029982,3.53649757 12.5176381,4.02185767 11.9210593,4.02185767 C11.3244805,4.02185767 10.839139,3.53649757 10.839139,2.9399188 C10.839139,2.34334002 11.3244805,1.85797993 11.9210593,1.85797993 M11.9210593,0 C10.2973892,0 8.98115911,1.31624872 8.98115911,2.9399188 C8.98115911,4.56358887 10.2974078,5.87983759 11.9210593,5.87983759 C13.5447108,5.87983759 14.8609781,4.56358887 14.8609781,2.9399188 C14.8609781,1.31624872 13.5447294,0 11.9210593,0 Z M3.422975,9.2412763 C4.28593235,9.2412763 4.98798865,9.94333259 4.98798865,10.8062899 C4.98798865,11.6692287 4.28593235,12.371285 3.422975,12.371285 C2.56003622,12.371285 1.85797993,11.6692287 1.85797993,10.8062899 C1.85797993,9.94333259 2.56001764,9.2412763 3.422975,9.2412763 M3.422975,7.38329637 C1.532499,7.38329637 0,8.91581395 0,10.8062899 C0,12.6967474 1.53251758,14.2292649 3.422975,14.2292649 C5.31343241,14.2292649 6.84596857,12.6967474 6.84596857,10.8062899 C6.84596857,8.91581395 5.31343241,7.38329637 3.422975,7.38329637 Z M15.2611498,13.405994 C15.7635476,13.405994 16.2358833,13.6016393 16.591129,13.9568851 C16.9463748,14.3121308 17.1420201,14.7844851 17.1420201,15.2868829 C17.1420201,15.7892806 16.9463748,16.2615977 16.591129,16.6168435 C16.2358833,16.9720892 15.7635476,17.1677345 15.2611498,17.1677345 C14.7587521,17.1677345 14.2864164,16.9720892 13.9311706,16.6168435 C13.5759249,16.2615977 13.3802796,15.7892806 13.3802796,15.2868829 C13.3802796,14.7844851 13.5759249,14.3121494 13.9311706,13.9569037 C14.2864164,13.6016579 14.7587521,13.405994 15.2611498,13.405994 M15.2611498,11.5480141 C13.1962281,11.5480141 11.5222997,13.2219611 11.5222997,15.2868643 C11.5222997,17.3517674 13.1962467,19.0256959 15.2611498,19.0256959 C17.3260344,19.0256959 19,17.3517674 19,15.2868829 C19,13.2219611 17.3260344,11.5480141 15.2611498,11.5480141 Z"></path>
          <path d="M5.89182156,10.0795411 C5.66150637,10.0795411 5.43087532,9.9944642 5.25100429,9.82300981 C4.87961268,9.46902747 4.86549203,8.88099541 5.21949295,8.5096038 L9.75296397,3.75317519 C10.1069277,3.38178358 10.6949784,3.36766294 11.06637,3.72166385 C11.4377616,4.07564619 11.4518637,4.66367825 11.0978813,5.03506986 L6.5644103,9.79149847 C6.38188235,9.98301904 6.13703775,10.0795411 5.89182156,10.0795411 L5.89182156,10.0795411 Z M12.1339838,15.4678873 C11.9852576,15.4678873 11.8387312,15.4319676 11.7068342,15.3632458 L5.83561767,12.3161588 C5.38022679,12.0798237 5.20265965,11.5190668 5.4389947,11.0636759 C5.67534832,10.608285 6.23612382,10.4306993 6.69149612,10.6670529 L12.5627127,13.71414 C13.0181036,13.9504751 13.1956707,14.511232 12.9593357,14.9666229 C12.7939569,15.2852478 12.469758,15.4678873 12.1339838,15.4678873 L12.1339838,15.4678873 Z"></path>
        </g>
      </g>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconPromotionComponent {

}
