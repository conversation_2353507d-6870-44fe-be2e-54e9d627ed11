import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'app-icon-chevron-down, ChevronDownIcon',
  template: `
    <svg style="width: 0.8em;" viewBox="0 0 20 12" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g transform="translate(0.668446, 0.897388)" fill="currentColor" fill-rule="nonzero">
        <path d="M0.397009919,0.41399613 C0.926516363,-0.115350407 1.78484634,-0.115350407 2.31435278,0.41399613 L9.49150689,7.59115023 L16.668661,0.41399613 C17.0091106,0.0615022116 17.5132654,-0.0798661751 17.987353,0.0442260599 C18.4614405,0.168318295 18.8316817,0.538559494 18.9557739,1.01264706 C19.0798662,1.48673462 18.9384978,1.99088942 18.5860039,2.33133903 L10.4501783,10.4671646 C9.92067187,10.9965111 9.0623419,10.9965111 8.53283545,10.4671646 L0.397009919,2.33133903 C-0.13233664,1.80183258 -0.13233664,0.943502577 0.397009919,0.41399613 L0.397009919,0.41399613 Z"></path>
      </g>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconChevronDownComponent {

}
