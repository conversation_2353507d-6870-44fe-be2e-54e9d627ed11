import { ChangeDetectionStrategy, Component, input, numberAttribute } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'SortDownIcon',
  template: `
    <svg viewBox="0 0 20 14" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round">
        <g transform="translate(1.000000, 1.000000)" stroke="currentColor" stroke-width="1.5">
          <line x1="18" y1="1.53947368" x2="9.71052632" y2="1.53947368"></line>
          <line x1="15.6315789" y1="5.92105263" x2="9.71052632" y2="5.92105263"></line>
          <line x1="12.0789474" y1="10.3026316" x2="9.71052632" y2="10.3026316"></line>
          <path d="M3.55263158,0 L3.55263158,11.8421053 M3.55263158,11.8421053 L7.10526316,8.28947368 M3.55263158,11.8421053 L0,8.28947368"></path>
        </g>
      </g>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconSortDownComponent {


}
