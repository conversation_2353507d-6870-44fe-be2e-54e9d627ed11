import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'app-icon-caliber, CaliberIcon',
  template: `
    <svg class="inline-flex h-em w-em text-base text-primary" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
      <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round">
        <g transform="translate(1.000000, 1.000000)" fill-rule="nonzero" stroke="currentColor" stroke-width="2">
          <path d="M14.2105301,1.8947326 L1.42105301,1.8947326 C0.636229115,1.8947326 0,2.53096172 0,3.31578561 L0,16.578947 C0,17.3637946 0.636229115,18 1.42105301,18 L15.6315831,18 C16.4164306,18 17.0526361,17.3637946 17.0526361,16.578947 L17.0526361,9.9473663 L17.0526361,6.63157596"></path>
          <polyline points="12.4307559 4.01934895 14.4404091 2.00967211 12.4307559 0"></polyline>
          <line x1="8.52631803" y1="6.15789162" x2="8.52631803" y2="14.2105253"></line>
          <line x1="12.3157927" y1="9.47368197" x2="12.3157927" y2="14.2105253"></line>
          <line x1="4.73684335" y1="9.47368197" x2="4.73684335" y2="14.2105253"></line>
        </g>
      </g>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconCaliberComponent {

}
