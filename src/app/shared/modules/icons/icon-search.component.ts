import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'app-icon-search, SearchIcon',
  template: `
    <svg viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g fill="currentColor" fill-rule="nonzero">
        <path d="M8.53147554,0 C13.2435161,0 17.0629511,3.81943497 17.0629511,8.53147554 C17.0629511,10.5992919 16.3271113,12.4954124 15.1033778,13.9724241 L19.7615635,18.6300765 C20.0664849,18.9305493 20.0807585,19.4178392 19.7939477,19.735645 C19.5071369,20.0534508 19.0209419,20.0890817 18.6908633,19.8164848 L18.6300765,19.7615635 L13.9724241,15.1033778 C12.4436879,16.3726608 10.5184592,17.0660375 8.53147554,17.0629511 C3.81943494,17.0629511 0,13.2435161 0,8.53147554 C0,3.81943494 3.81943497,0 8.53147554,0 Z M8.53147554,1.59965167 C4.7029759,1.59965167 1.59965167,4.7029759 1.59965167,8.53147554 C1.59965167,12.3599752 4.7029759,15.4632994 8.53147554,15.4632994 C12.3599752,15.4632994 15.4632994,12.3599752 15.4632994,8.53147554 C15.4632994,4.70297587 12.3599752,1.59965167 8.53147554,1.59965167 Z" id="Shape"></path>
      </g>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconSearchComponent {

}
