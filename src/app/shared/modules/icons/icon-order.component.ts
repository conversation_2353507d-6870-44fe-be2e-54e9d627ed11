import { ChangeDetectionStrategy, Component, input, numberAttribute } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'OrderIcon',
  template: `
    @switch (style()) {
      @case (1) {
        <svg viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
          <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g fill-rule="nonzero">
              <path d="M0,10 C0,15.5228475 4.4771525,20 10,20 C15.5228475,20 20,15.5228475 20,10 C20,4.4771525 15.5228475,0 10,0 C4.4771525,0 0,4.4771525 0,10 Z" fill="#69ADF4"></path>
              <path d="M6.76539361,4.87918939 L13.2501949,4.87918939 C14.0296181,4.87918939 14.4193297,5.26890101 14.4193297,6.04832424 L14.4193297,13.9516758 C14.4193297,14.731099 14.0296181,15.1208106 13.2501949,15.1208106 L6.76539361,15.1208106 C5.98597038,15.1208106 5.59625877,14.731099 5.59625877,13.9516758 L5.59625877,6.04832424 C5.59625877,5.26890101 5.98597038,4.87918939 6.76539361,4.87918939 Z" fill="#FFFFFF"></path>
              <path d="M7.61496492,7.80982074 L12.4473889,7.80982074 C12.7331774,7.80982074 12.8760717,7.952715 12.8760717,8.23850352 L12.8760717,8.23850352 C12.8760717,8.52429203 12.7331774,8.66718629 12.4473889,8.66718629 L7.61496492,8.66718629 C7.32917641,8.66718629 7.18628215,8.52429203 7.18628215,8.23850352 L7.18628215,8.23850352 C7.18628215,7.952715 7.32917641,7.80982074 7.61496492,7.80982074 Z" fill-opacity="0.31" fill="#6AAEF4"></path>
              <path d="M7.61496492,9.61808262 L10.7326578,9.61808262 C11.0184463,9.61808262 11.1613406,9.76097687 11.1613406,10.0467654 L11.1613406,10.0467654 C11.1613406,10.3325539 11.0184463,10.4754482 10.7326578,10.4754482 L7.61496492,10.4754482 C7.32917641,10.4754482 7.18628215,10.3325539 7.18628215,10.0467654 L7.18628215,10.0467654 C7.18628215,9.76097688 7.32917641,9.61808262 7.61496492,9.61808262 L7.61496492,9.61808262 Z" fill-opacity="0.31" fill="#6AAEF4"></path>
              <path d="M11.3873733,13.1566641 L12.7903352,13.1566641 C13.0761237,13.1566641 13.2190179,13.2995583 13.2190179,13.5853468 L13.2190179,13.5853468 C13.2190179,13.8711354 13.0761237,14.0140296 12.7903352,14.0140296 L11.3873733,14.0140296 C11.1015848,14.0140296 10.9586906,13.8711354 10.9586906,13.5853468 L10.9586906,13.5853468 C10.9586906,13.2995583 11.1015848,13.1566641 11.3873733,13.1566641 Z" fill="#6AAEF4"></path>
            </g>
          </g>
        </svg>
      }
      @case (2) {
        <svg viewBox="0 0 17 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
          <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g transform="translate(0.193333, 0.406667)" fill-rule="nonzero">
              <path d="M12.9983799,0.755623092 L14.709373,0.756297154 C15.208386,0.756297154 15.6869607,0.954529287 16.0398161,1.30738475 C16.3926716,1.66024021 16.5909037,2.13881491 16.5909037,2.63782788 L16.5909037,17.1184693 C16.5909037,17.6174822 16.3926716,18.0960569 16.0398161,18.4489124 C15.6869607,18.8017679 15.208386,19 14.709373,19 L1.8815307,19 C1.38251773,19 0.903943028,18.8017679 0.551087574,18.4489124 C0.19823212,18.0960569 -9.57899196e-16,17.6174822 -9.57899196e-16,17.1184693 L-9.57899196e-16,2.63782788 C-9.57899196e-16,2.13881491 0.19823212,1.66024021 0.551087574,1.30738475 C0.903943028,0.954529287 1.38251773,0.756297154 1.8815307,0.756297154 L3.59095101,0.755623092 L3.59184975,1.41126038 C3.59203452,2.39582556 4.35114,3.21380603 5.33295095,3.28739859 L5.47338047,3.29279109 L11.1179726,3.29279109 C12.102618,3.29259749 12.9206298,2.53336493 12.9941108,1.5514652 L12.9995033,1.41126038 L12.9986046,0.755623092 L12.9983799,0.755623092 Z M9.27913246,10.3724604 L5.03793666,10.3724604 C4.64822786,10.3724604 4.33230646,10.6883818 4.33230646,11.0780906 C4.33230646,11.4677994 4.64822786,11.7837208 5.03793666,11.7837208 L9.27913246,11.7837208 C9.53122217,11.7837695 9.76418351,11.6493092 9.89024242,11.4310012 C10.0163013,11.2126933 10.0163013,10.9437127 9.89024242,10.7254047 C9.76418351,10.5070967 9.53122217,10.3726364 9.27913246,10.3726851 L9.27913246,10.3724604 Z M11.6343038,7.07562498 L5.03793666,7.07562498 C4.64828991,7.07562498 4.3324188,7.39149608 4.3324188,7.78114283 C4.3324188,8.17078958 4.64828991,8.48666068 5.03793666,8.48666068 L11.6343038,8.48666068 C12.0239506,8.48666068 12.3398217,8.17078958 12.3398217,7.78114283 C12.3398217,7.39149608 12.0239506,7.07562498 11.6343038,7.07562498 L11.6343038,7.07562498 Z" id="形状" fill="#FF866E"></path>
              <path d="M5.94342611,0 L10.6472529,0 C11.1668232,0 11.5880182,0.421195 11.5880182,0.940765364 C11.5880182,1.46033573 11.1668232,1.88153073 10.6472529,1.88153073 L5.94342611,1.88153073 C5.42385575,1.88153073 5.00266075,1.46033573 5.00266075,0.940765364 C5.00266075,0.421195 5.42385575,0 5.94342611,0 Z" id="路径" fill="#FF6A36" opacity="0.6"></path>
            </g>
          </g>
        </svg>
      }
    }
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconOrderComponent {

  style = input(1, { transform: numberAttribute });
  
}
