import { ChangeDetectionStrategy, Component, input, numberAttribute } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'app-icon-taxi, TaxiIcon',
  template: `
    @switch (style()) {
      @case (1) {
        <svg viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
          <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g transform="translate(0.000000, -0.004384)" fill-rule="nonzero">
              <path d="M0,10.0007787 C0,13.5736597 1.90579526,16.8752061 4.99987139,18.6616925 C8.09394753,20.448179 11.9060525,20.448179 15.0001286,18.6616925 C18.0942047,16.8752061 20,13.5736597 20,10.0007787 C20,6.42789765 18.0942047,3.12635132 15.0001286,1.33986485 C11.9060525,-0.********* 8.09394753,-0.********* 4.99987139,1.33986485 C1.90579526,3.12635132 0,6.42789765 0,10.0007787 L0,10.0007787 Z" fill="#F2A430"></path>
              <path d="M16.3251708,9.62396708 C16.3251708,9.2591495 15.8624021,8.96329743 15.2916875,8.96329743 C15.1180772,8.96221932 14.9455988,8.9913039 14.7819424,9.04925444 L13.9996668,6.84835489 C13.8197568,6.55616765 13.7971015,6.24265774 13.5119107,6.24265774 L12.2805264,6.24265774 L12.1049475,5.35310251 C12.0326504,5.10189479 11.6871564,4.99794677 11.4016325,4.99794677 L8.58837249,4.99794677 C8.30284857,4.99794677 7.95535566,5.10122845 7.88505747,5.35343568 L7.70914544,6.24299091 L6.47809429,6.24299091 C6.19290355,6.24299091 6.11394303,6.59214967 5.990005,6.84835489 L5.20806263,9.04925444 C5.0443926,8.9913563 4.87192346,8.96227327 4.69831751,8.96329743 C4.12760288,8.96329743 3.66483426,9.2591495 3.66483426,9.62396708 C3.66483426,9.98878467 4.12760288,10.2846367 4.69831751,10.2846368 L4.71197735,10.2843036 C4.71464268,10.3006288 4.71597535,10.3156213 4.71997334,10.3326128 C4.67166417,11.1588663 5.13509911,13.8481883 5.21505914,14.2499874 C5.31900717,14.7014284 5.48958854,14.778723 5.77477928,14.778723 L6.82991839,14.778723 C7.11510913,14.778723 7.28235883,14.5418415 7.28235883,14.2499874 L7.28235883,13.6762743 C7.41662503,13.6899341 7.55655507,13.7029276 7.70881227,13.7115899 C9.37397967,13.8105405 10.8532401,13.8225345 12.2808596,13.7115899 C12.4307846,13.6999291 12.5717141,13.6859361 12.7079793,13.6706104 L12.7079793,14.2499874 C12.7079793,14.5418415 12.8745627,14.778723 13.1600866,14.778723 L14.2152257,14.778723 C14.5004165,14.778723 14.7029819,14.556834 14.7749459,14.2499874 C14.9421956,13.7702273 15.6191904,10.9439738 15.4429452,10.2769739 C15.9416958,10.2303306 16.3251708,9.95613434 16.3251708,9.62396708 L16.3251708,9.62396708 Z M6.42278861,11.332113 C6.79079444,11.332113 7.08912211,11.6304407 7.08912211,11.9984465 C7.08912211,12.3664523 6.79079444,12.66478 6.42278861,12.66478 C6.05478278,12.66478 5.75645511,12.3664523 5.75645511,11.9984465 C5.75645511,11.6304407 6.05478278,11.332113 6.42278861,11.332113 L6.42278861,11.332113 Z M12.9841746,7.13154664 C13.2696985,7.13154664 13.3753123,7.26981083 13.4832584,7.64129175 L13.7414626,8.69876302 C13.7414626,8.99061709 13.5102449,9.22749866 13.224721,9.22749866 C13.224721,9.22749866 12.4860903,9.34477336 11.7534566,9.44372387 C11.0204898,9.54200806 9.14342829,9.50569289 8.58837249,9.44372387 C7.98041334,9.37388381 7.37271308,9.30180826 6.76528403,9.22749866 C6.47976012,9.22749866 6.2485424,8.99061711 6.2485424,8.69876302 L6.5070798,7.64162492 C6.61435949,7.26981083 6.72030653,7.1318798 7.00583042,7.1318798 L12.9841746,7.1318798 L12.9841746,7.13154664 Z M13.6321839,12.66478 C13.2641781,12.66478 12.9658504,12.3664523 12.9658504,11.9984465 C12.9658504,11.6304407 13.2641781,11.332113 13.6321839,11.332113 C14.0001898,11.332113 14.2985174,11.6304407 14.2985174,11.9984465 C14.2985174,12.3664523 14.0001898,12.66478 13.6321839,12.66478 L13.6321839,12.66478 Z" fill="#FFFFFF"></path>
            </g>
          </g>
        </svg>
      }
      @case (2) {
        <svg viewBox="0 0 20 18" version="1.1" xmlns="http://www.w3.org/2000/svg">
          <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g transform="translate(0.289062, 0.929197)" fill-rule="nonzero">
              <path d="M11.5822208,1.64169684 C11.6319969,1.64445516 11.6801391,1.62348026 11.7120127,1.58514829 C11.7438863,1.54681631 11.7557261,1.49565541 11.7439329,1.44721793 L11.5423821,0.618148468 C11.4907567,0.401746486 11.2595036,0.125232842 10.9707319,0.0861013287 C9.99376207,-0.0287004429 9.00670929,-0.0287004429 8.02973942,0.0861013287 C7.7666625,0.125232842 7.51419352,0.413533086 7.45808931,0.664116209 L7.26054588,1.44556781 C7.24834251,1.4941517 7.25994632,1.54564376 7.29180879,1.58429735 C7.32367126,1.62295094 7.37200243,1.64416834 7.42202231,1.64146111 C8.8077877,1.56812774 10.1964637,1.56820643 11.5822208,1.64169684 L11.5822208,1.64169684 Z" id="路径" fill="#FFBE69"></path>
              <path d="M2.66660054,13.3604064 L4.07533501,13.3604064 C4.59583128,13.3604064 4.85607942,13.6206545 4.85607942,14.1411508 L4.85607942,16.0449224 C4.85607942,16.5654187 4.59583128,16.8256668 4.07533501,16.8256668 L2.66660054,16.8256668 C2.14610427,16.8256668 1.88585613,16.5654187 1.88585613,16.0449224 L1.88585613,14.1411508 C1.88585613,13.6206545 2.14610427,13.3604064 2.66660054,13.3604064 Z" id="路径" fill="#FFBE69"></path>
              <path d="M14.9010917,13.3604064 L16.3098262,13.3604064 C16.8303224,13.3604064 17.0905706,13.6206545 17.0905706,14.1411508 L17.0905706,16.0449224 C17.0905706,16.5654187 16.8303224,16.8256668 16.3098262,16.8256668 L14.9010917,16.8256668 C14.3805954,16.8256668 14.1203473,16.5654187 14.1203473,16.0449224 L14.1203473,14.1411508 C14.1203473,13.6206545 14.3805954,13.3604064 14.9010917,13.3604064 Z" id="路径" fill="#FFBE69"></path>
              <path d="M18.6487591,6.70993498 L16.8470594,6.70993498 C16.6733269,6.71002758 16.5256531,6.83687686 16.4993547,7.00860743 L15.8628783,4.39033204 C15.7016376,3.71448837 14.981712,2.85029484 14.0762654,2.72818566 C11.0237625,2.36916628 7.93969892,2.36916628 4.88719604,2.72818566 C4.0649628,2.85029484 3.27573205,3.75102684 3.10058316,4.53436429 L2.48767995,6.95674639 C2.43888054,6.81467174 2.30528385,6.71923201 2.15506209,6.71912853 L0.353598092,6.71912853 C0.259929264,6.71868933 0.169949568,6.75560786 0.103582375,6.82170958 C0.0372151821,6.88781131 0,6.97764231 0,7.07131215 L0,7.66700693 C0,7.84468347 0.132485118,7.99447926 0.30880901,8.01636176 L1.82315142,8.20777615 C1.55182388,8.39424017 1.36936731,8.67924017 1.34485118,9.0043146 L0.904739523,11.7218332 L0.898374759,11.7218332 L0.898374759,13.9440788 C0.898374759,14.2280258 1.12855927,14.4582103 1.41250627,14.4582103 L17.5507194,14.4582103 C17.8349268,14.4582103 18.0653224,14.2278147 18.0653224,13.9436073 L18.0653224,11.7218332 L18.0587219,11.7218332 L17.6186103,9.0043146 C17.5950371,8.67664711 17.4092802,8.39141138 17.1348882,8.20282578 L18.6935482,8.00693248 C18.8689889,7.98406146 19,7.83450278 19,7.65757765 L19,7.0621186 C19,6.8679806 18.8428964,6.71045476 18.6487591,6.70993498 Z M3.38157572,12.9075652 C2.92557304,12.9075652 2.51448466,12.6328379 2.34004613,12.2115189 C2.1656076,11.7902 2.26218176,11.3052855 2.5847255,10.9829441 C2.90726925,10.6606027 3.39224432,10.5643328 3.81345369,10.7390357 C4.23466307,10.9137386 4.50913225,11.3249993 4.5088464,11.7810019 C4.50845577,12.4033 4.00387393,12.9075652 3.38157572,12.9075652 Z M4.21771714,7.83319809 C4.07892055,7.83328709 3.94745237,7.77091188 3.85973048,7.66335086 C3.77200859,7.55578983 3.73734705,7.41446352 3.76534742,7.27852058 L4.18070722,5.24509629 C4.32662533,4.59329729 4.92562036,4.08340896 5.60995037,3.98180847 L5.66746898,3.98180847 C8.18837203,3.68313662 10.7357222,3.68313662 13.2566252,3.98180847 L13.1988709,3.98180847 C13.9532133,4.08340896 14.5515011,4.5388432 14.6856326,5.10129977 L15.1950495,7.26249081 C15.2275371,7.40040794 15.1951823,7.54560968 15.1072126,7.65668581 C15.0192429,7.76776194 14.8853145,7.83251971 14.7436227,7.8324908 L4.21771714,7.83319809 Z M15.5953224,12.9075652 C15.1393198,12.9075652 14.7282314,12.6328379 14.5537929,12.2115189 C14.3793543,11.7902 14.4759285,11.3052855 14.7984722,10.9829441 C15.121016,10.6606027 15.605991,10.5643328 16.0272004,10.7390357 C16.4484098,10.9137386 16.722879,11.3249993 16.7225931,11.7810019 C16.7222025,12.4033 16.2176207,12.9075652 15.5953224,12.9075652 L15.5953224,12.9075652 Z" id="形状" fill="#FFA227"></path>
            </g>
          </g>
        </svg>
      }
      @case (3) {
        <svg viewBox="0 0 20 17" version="1.1" xmlns="http://www.w3.org/2000/svg">
          <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g fill="currentColor" fill-rule="nonzero">
              <path d="M18.1858,7.6748 L17.2414,4.0948 C16.8214,2.5348 15.8128,1.6044 14.5454,1.6044 L13.46,1.6044 L13.4634,1.5516 L13.4234,1.4072 C13.1892,0.5392 12.5714,-2.84217094e-16 11.811,-2.84217094e-16 L8.1374,-2.84217094e-16 C7.3774,-2.84217094e-16 6.7592,0.54 6.525,1.4072 L6.5,1.4994 L6.495,1.6052 L5.455,1.6052 C4.1872,1.6052 3.18,2.5362 2.7622,4.083 L1.7308,7.7054 C0.719,8.1044 0,9.0896 0,10.2416 L0,12.0598 C0,13.2446 0.76,14.2524 1.8182,14.628 L1.8182,15.6962 C1.81831041,16.1981811 2.2252189,16.6052 2.7272,16.6052 L3.6362,16.6052 C4.1381811,16.6052 4.54508959,16.1981811 4.5452,15.6962 L4.5452,14.787 L15.4546,14.787 L15.4546,15.696 C15.4547104,16.1979811 15.8616189,16.6048896 16.3636,16.605 L17.2726,16.605 C17.7745811,16.6048896 18.1814896,16.1979811 18.1816,15.696 L18.1816,14.628 C19.24,14.2524 20,13.2446 20,12.0598 L20,10.2416 C20,9.0584 19.2412,8.0516 18.1858,7.6748 Z M3.1818,12.9688 C2.42870452,12.9688 1.8182,12.3582955 1.8182,11.6052 C1.8182,10.8521045 2.42870452,10.2416 3.1818,10.2416 C3.93489548,10.2416 4.5454,10.8521045 4.5454,11.6052 C4.54451832,12.35793 3.93452998,12.9679183 3.1818,12.9688 L3.1818,12.9688 Z M3.6756,7.5142 L4.5142,4.5692 C4.607,4.2248 4.8848,3.4232 5.4542,3.4232 L14.5454,3.4232 C15.1152,3.4232 15.393,4.2246 15.4854,4.5642 L16.2638,7.514 L3.6756,7.5142 Z M16.8182,12.9688 C16.0651045,12.9688 15.4546,12.3582955 15.4546,11.6052 C15.4546,10.8521045 16.0651045,10.2416 16.8182,10.2416 C17.5712955,10.2416 18.1818,10.8521045 18.1818,11.6052 C18.1809183,12.35793 17.57093,12.9679183 16.8182,12.9688 L16.8182,12.9688 Z" id="形状"></path>
            </g>
          </g>
        </svg>
      }
    }
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconTaxiComponent {

  style = input(1, { transform: numberAttribute });
  
}
