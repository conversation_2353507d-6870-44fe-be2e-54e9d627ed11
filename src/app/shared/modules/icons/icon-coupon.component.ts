import { ChangeDetectionStrategy, Component, input, numberAttribute } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'CouponIcon',
  template: `
    <svg viewBox="0 0 20 14" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g fill="#FB9F00" fill-rule="nonzero">
          <path d="M17.25,0 C18.7687831,0 20,0.963561082 20,2.15217391 L20,4.89130435 C20,5.0469749 19.9209824,5.19626958 19.7803301,5.30634528 C19.6396778,5.41642099 19.4489124,5.47826087 19.25,5.47826087 C18.2835017,5.4782609 17.5,6.09143613 17.5,6.84782609 C17.5,7.60421605 18.2835017,8.21739127 19.25,8.2173913 C19.6644444,8.2173913 20,8.48 20,8.80434783 L20,11.5434783 C20,12.7320911 18.7687831,13.6956522 17.25,13.6956522 L2.75,13.6956522 C2.02065465,13.6956522 1.32118139,13.4689059 0.805456352,13.065295 C0.289731311,12.6616841 0,12.1142703 0,11.5434783 L0,8.80434783 C0,8.48 0.335555556,8.2173913 0.75,8.2173913 C1.37521479,8.21739132 1.9529371,7.95635367 2.2655445,7.53260871 C2.5781519,7.10886375 2.5781519,6.58678843 2.2655445,6.16304347 C1.9529371,5.73929851 1.37521479,5.47826085 0.75,5.47826087 C0.335786438,5.47826087 0,5.21547148 0,4.89130435 L0,2.15217391 C0,0.963561082 1.23121694,0 2.75,0 L17.25,0 L17.25,0 Z M8.46956522,3.50956522 C8.25676445,3.28465477 7.90585572,3.26372204 7.66782609,3.46173913 L7.64,3.48652174 C7.41482176,3.69937333 7.39388163,4.0505965 7.59217391,4.28869565 L8.90956522,5.86956522 L8.04347826,5.86956522 L8.00869565,5.87043478 C7.69852153,5.88884782 7.45643601,6.14580158 7.45652174,6.45652174 L7.4573913,6.49130435 C7.47580434,6.80147847 7.7327581,7.04356399 8.04347826,7.04347826 L9.51086957,7.04347826 L9.51086957,7.82608696 L8.04347826,7.82608696 L8.00869565,7.82695652 C7.69852153,7.84536956 7.45643601,8.10232332 7.45652174,8.41304348 L7.4573913,8.44782609 C7.47580434,8.75800021 7.7327581,9.00008572 8.04347826,9 L9.51086957,9 L9.51086957,9.7826087 L9.51173913,9.8173913 C9.53015217,10.1275654 9.78710593,10.3696509 10.0978261,10.3695652 L10.1326087,10.3686957 C10.4427828,10.3502826 10.6848683,10.0933289 10.6847826,9.7826087 L10.6847826,9 L11.9565217,9 L11.9913043,8.99913043 C12.3014785,8.9807174 12.543564,8.72376364 12.5434783,8.41304348 L12.5426087,8.37826087 C12.5241957,8.06808675 12.2672419,7.82600123 11.9565217,7.82608696 L10.6847826,7.82608696 L10.6847826,7.04347826 L11.9565217,7.04347826 L11.9913043,7.0426087 C12.3014785,7.02419566 12.543564,6.7672419 12.5434783,6.45652174 L12.5426087,6.42173913 C12.5241957,6.11156501 12.2672419,5.86947949 11.9565217,5.86956522 L11.0895652,5.86956522 L12.4073913,4.28869565 L12.4304348,4.25956522 C12.6177997,4.00372598 12.5680803,3.64532522 12.3181482,3.45015024 C12.0682162,3.25497526 11.7084511,3.29360472 11.5056522,3.5373913 L10,5.34347826 L8.49434783,3.53695652 L8.47,3.50913043 L8.46956522,3.50956522 Z" id="形状"></path>
        </g>
      </g>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconCouponComponent {


}
