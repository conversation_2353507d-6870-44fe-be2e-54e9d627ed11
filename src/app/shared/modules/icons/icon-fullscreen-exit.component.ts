import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'app-icon-fullscreen-exit, FullscreenExitIcon',
  template: `
    <svg viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g transform="translate(0.454499, 0.035061)" fill="currentColor" fill-rule="nonzero">
        <path d="M7.35532486,10.5856633 L2.06026542,10.5856633 C1.47545262,10.5857528 1.00141565,11.0598623 1.00141565,11.6446752 C1.00141565,12.229488 1.47545262,12.7035975 2.06026542,12.703687 L4.79781116,12.703687 L0.254650166,17.2521431 C-0.105177433,17.6723169 -0.0809847783,18.2986524 0.310181409,18.6898186 C0.701347596,19.0809848 1.32768307,19.1051774 1.74785693,18.7453498 L6.29631297,14.2021888 L6.29631297,16.9397346 C6.2964025,17.5245474 6.77051204,17.9985844 7.35532485,17.9985844 C7.94013765,17.9985844 8.4142472,17.5245474 8.41433673,16.9397346 L8.41433673,11.6446751 C8.41433673,11.059799 7.94020096,10.5856633 7.35532486,10.5856633 L7.35532486,10.5856633 Z M18.6920471,0.307952918 C18.2789928,-0.102650973 17.6118946,-0.102650973 17.1988403,0.307952918 L12.6503843,4.85111391 L12.6503843,2.11356817 C12.6502948,1.52875537 12.1761852,1.0547184 11.5913724,1.0547184 C11.0065596,1.0547184 10.53245,1.52875537 10.5323605,2.11356817 L10.5323605,7.40862761 C10.5323605,7.99350371 11.0064963,8.46763948 11.5913724,8.46763948 L16.8864318,8.46763948 C17.4712446,8.46754995 17.9452816,7.99344041 17.9452816,7.4086276 C17.9452816,6.82381479 17.4712446,6.34970525 16.8864318,6.34961572 L14.1488861,6.34961572 L18.6920471,1.80115968 C19.102651,1.38810538 19.102651,0.72100722 18.6920471,0.307952918 L18.6920471,0.307952918 Z" id="Shape"></path>
      </g>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconFullscreenExitComponent {

}
