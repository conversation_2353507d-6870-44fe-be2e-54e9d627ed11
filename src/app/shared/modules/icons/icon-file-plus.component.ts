import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'app-icon-file-plus, FilePlusIcon',
  template: `
    <svg viewBox="0 0 19 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g transform="translate(0.666667, 0.333333)" fill="currentColor" fill-rule="nonzero">
        <path d="M0,2.51807498 C0,1.12738057 1.12738059,0 2.51807501,0 L9.86725675,0 C10.5351102,0 11.1756277,0.265216628 11.6478955,0.737436236 L15.0904638,4.18180312 C15.5626834,4.65407092 15.8279,5.29458842 15.8279,5.96244187 L15.8279,8.81326249 C15.8279,9.21060374 15.5057913,9.53271245 15.10845,9.53271245 C14.7111088,9.53271245 14.389,9.21060374 14.389,8.81326249 L14.389,6.83477498 L11.5112,6.83477498 C10.1205056,6.83477498 8.99312501,5.70739441 8.993125,4.3167 L8.993125,1.43889999 L2.51807501,1.43889999 C1.92206311,1.43889999 1.43890001,1.92206309 1.43890001,2.51807498 L1.43890001,15.10845 C1.43890001,15.7044619 1.92206311,16.187625 2.51807501,16.187625 L7.91395,16.187625 C8.31129126,16.187625 8.6334,16.5097337 8.6334,16.907075 C8.6334,17.3044162 8.31129126,17.626525 7.91395,17.626525 L2.51807501,17.626525 C1.12738059,17.626525 0,16.4991444 0,15.10845 L0,2.51807498 L0,2.51807498 Z M10.432025,1.59897762 L10.432025,4.3167 C10.432025,4.91271189 10.9151881,5.395875 11.5112,5.395875 L14.2298217,5.395875 C14.1855887,5.32402564 14.1330686,5.25762102 14.0733413,5.19802624 L10.6298737,1.75545799 C10.5702679,1.69574306 10.5038648,1.64322425 10.432025,1.59897762 L10.432025,1.59897762 Z M13.9249548,19 C16.1042138,19 17.9502775,17.1768687 17.9502775,14.9740028 C17.9502775,12.7711368 16.1278207,10.9493545 13.9249548,10.9493545 C11.7146695,10.9493545 9.90030649,12.7718113 9.90030649,14.9740028 C9.90030649,17.1923819 11.7146695,18.9993255 13.9249548,18.9993255 L13.9249548,19 Z M13.9249548,17.5889787 C13.6477417,17.5889787 13.4103232,17.3987741 13.4103232,17.1053734 L13.4103232,15.4576081 L11.8893609,15.4576081 C11.6241972,15.4536366 11.4100972,15.2398351 11.4057556,14.9746773 L11.4057556,14.9740028 C11.4057556,14.7129773 11.627661,14.4903975 11.8893609,14.4903975 L13.4103232,14.4903975 L13.4103232,12.850726 C13.4103232,12.5492314 13.6484162,12.3590268 13.9249548,12.3590268 C14.2014933,12.3590268 14.4402608,12.5492314 14.4402608,12.8500515 L14.4402608,14.4903975 L15.9612231,14.4903975 C16.2222485,14.4903975 16.4448284,14.7123028 16.4448284,14.9740028 C16.4448284,15.2357027 16.222923,15.4576081 15.9612231,15.4576081 L14.4402608,15.4576081 L14.4402608,17.1053734 C14.4402608,17.3980996 14.2021678,17.5889787 13.9249548,17.5889787 L13.9249548,17.5889787 Z"></path>
      </g>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconFilePlusComponent {

}
