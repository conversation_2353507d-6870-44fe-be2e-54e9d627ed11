import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'app-icon-minus-circle, MinusCircleIcon',
  template: `
    <svg viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g transform="translate(1.000000, 1.000000)" fill="none" stroke="currentColor" stroke-width="2">
        <path d="M5,9 L13,9 M9,18 C4.02944,18 0,13.9706 0,9 C0,4.02944 4.02944,0 9,0 C13.9706,0 18,4.02944 18,9 C18,13.9706 13.9706,18 9,18 Z"></path>
      </g>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconMinusCircleComponent {

}
