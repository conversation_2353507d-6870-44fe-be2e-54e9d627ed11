import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'app-icon-trending-down, TrendingDownIcon',
  template: `
    <svg viewBox="0 0 20 13" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round">
        <g transform="translate(1.000000, 1.000000)" stroke="currentColor" stroke-width="2">
          <path d="M17.4548264,10.9089256 L11.0772503,4.43175103 C10.9627066,4.31546189 10.9047802,4.25709913 10.8535083,4.21128165 C10.0251936,3.46980197 8.77252163,3.46980197 7.94409782,4.21128165 C7.89282587,4.25709913 7.83446311,4.3153528 7.7199194,4.43175103 C7.60526659,4.54814927 7.54799473,4.60640293 7.49672278,4.65222042 C6.66829897,5.3937001 5.41515795,5.3937001 4.58677778,4.65222042 C4.53548401,4.60640293 4.47817942,4.54814927 4.36357025,4.43175103 L0,0 M17.4548264,10.9089256 L17.454281,4.36357025 M17.4548264,10.9089256 L10.9089256,10.9089256"></path>
        </g>
      </g>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconTrendingDownComponent {

}
