import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'FolderIcon',
  template: `
    <svg viewBox="0 0 20 16" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="doc" transform="translate(0.691648, 0.391658)" fill-rule="nonzero">
          <path d="M18.1630192,15.1491629 L0.836982289,15.1491629 C0.374010539,15.1491629 1.98064074e-06,14.8169126 1.98064074e-06,14.4047769 L1.98064074e-06,2.25676132 C1.98064074e-06,1.84462567 0.374010539,1.51237536 0.836982289,1.51237536 L18.1630192,1.51237536 C18.6259909,1.51237536 19.000002,1.84462567 19.000002,2.25676132 L19.000002,14.4065925 C19.000002,14.815097 18.6241754,15.1509784 18.1630192,15.1509784 L18.1630192,15.1491629 Z" id="路径" fill="#FFE9B4"></path>
          <path d="M9.5108942,5.10721483 L1.98064074e-06,5.10721483 L1.98064074e-06,0.837889069 C1.98064074e-06,0.374917319 0.374918312,0 0.837890084,0 L7.34672822,0 C7.71648833,-0.000558746842 8.04260192,0.2420875 8.14830478,0.596417515 L9.5108942,5.10721483 Z" id="路径" fill="#FFB02C"></path>
          <path d="M18.1630192,15.1491629 L0.836982289,15.1491629 C0.614853464,15.1496463 0.401683256,15.0616199 0.244614086,14.9045508 C0.0875449151,14.7474816 -0.000481439063,14.5343114 0,14.3121826 L0,3.49770716 C-0.000239819721,3.27573551 0.0878931948,3.06279897 0.244936086,2.90592641 C0.401978977,2.74905384 0.615011036,2.66115196 0.836982289,2.66163267 L18.1630192,2.66163267 C18.3849904,2.66115196 18.5980225,2.74905384 18.7550654,2.90592641 C18.9121083,3.06279897 19.0002413,3.27573551 19,3.49770716 L19,14.3139981 C18.9985023,14.7754808 18.6245032,15.1490742 18.1630192,15.1500706 L18.1630192,15.1491629 Z" id="路径" fill="#FFCA28"></path>
        </g>
      </g>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconFolderComponent {

}
