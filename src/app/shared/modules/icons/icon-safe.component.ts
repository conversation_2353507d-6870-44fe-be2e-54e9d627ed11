import { ChangeDetectionStrategy, Component, input, numberAttribute } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'SafeIcon',
  template: `
    <svg viewBox="0 0 18 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g transform="translate(0.953320, 0.562695)" fill-rule="nonzero">
          <path d="M8.03889473,19 C7.7430707,19 7.44688744,18.9106125 7.19425681,18.7316262 L3.45492198,16.0819736 C1.21685338,14.3256043 0,11.8425548 0,9.06900646 L0,4.03921619 C0,3.75685312 0.162735967,3.49980595 0.417860149,3.37884754 L7.222827,0.152318024 C7.24830391,0.140119072 7.27451581,0.129518569 7.30131049,0.120578066 C7.77557141,-0.0400235897 8.29902714,-0.0403616985 8.77398541,0.120578066 C8.80075939,0.129495684 8.82715301,0.140209505 8.85249003,0.152318024 L15.6599082,3.37884754 C15.9150707,3.49974173 16.0777683,3.75682077 16.0777683,4.03917393 L16.0777683,9.06900646 C16.0777683,11.8428929 14.8609572,14.3257733 12.6517546,16.0605671 L8.88387075,18.7314571 C8.63124012,18.9104434 8.33505686,19 8.03889473,19 Z" id="路径" fill="#4ED89D"></path>
          <path d="M8.03889473,12.9990558 C7.63529854,12.9990558 7.3080938,12.6718299 7.3080938,12.2682337 L7.3080938,5.26940374 C7.3080938,4.86582869 7.63529854,4.53860281 8.03889473,4.53860281 C8.44246978,4.53860281 8.76969565,4.86582869 8.76969565,5.26940374 L8.76969565,12.2682548 C8.76969565,12.671851 8.44246978,12.9990558 8.03889473,12.9990558 L8.03889473,12.9990558 Z" id="路径" fill="#FFFFFF"></path>
          <path d="M3.80866826,8.76882928 C3.80866826,8.36521197 4.135873,8.03800722 4.53944805,8.03800722 L11.5382991,8.03800722 C11.9419165,8.03800722 12.2691212,8.3652331 12.2691212,8.76880815 C12.2691212,9.17238321 11.9418953,9.49960908 11.5383203,9.49960908 L4.53946918,9.49960908 C4.135873,9.49960908 3.80866826,9.17238321 3.80866826,8.76880815 L3.80866826,8.76882928 Z" id="路径" fill="#FFFFFF"></path>
        </g>
      </g>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconSafeComponent {


}
