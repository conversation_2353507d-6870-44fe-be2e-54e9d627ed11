import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'GraphColumnIcon',
  template: `
    <svg viewBox="0 0 24 16">
      <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g fill="currentColor">
          <polygon fill-opacity="0.2" points="24 15 24 16 0 16 0 15"></polygon>
          <path d="M13,3 L16,3 L16,14 L13,14 L13,3 Z M4,6 L7,6 L7,14 L4,14 L4,6 Z" fill-opacity="0.5"></path>
          <path d="M17,5 L20,5 L20,14 L17,14 L17,5 Z M8,0 L11,0 L11,14 L8,14 L8,0 Z" fill-opacity="1"></path>
        </g>
      </g>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconGraphColumnComponent {

}
