import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'FileEditIcon',
  template: `
    <svg viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g transform="translate(0.666667, 0.333333)" fill="currentColor" fill-rule="nonzero">
        <path d="M17.1473963,8.84889303 L18.3998441,10.1096352 L9.44964142,18.9964453 L9.04262552,18.5864671 L9.45201123,19 L8.20193328,19 L8.20193328,17.7398503 L8.19778611,17.7357031 L17.1473963,8.84889303 L17.1473963,8.84889303 Z M14.8113502,1.01031334e-15 C15.7929601,1.01031334e-15 16.5887122,0.795752082 16.5887122,1.77736202 L16.5887122,7.10944808 L14.8113502,8.8868101 L14.8113502,1.77736202 L1.77736202,1.77736202 L1.77736202,17.1811662 L6.51699408,17.1811662 L4.73963206,18.9585282 L1.77736202,18.9585282 C0.795752082,18.9585282 0,18.1627761 0,17.1811662 L0,1.77736202 C0,0.795752082 0.795752082,1.01031334e-15 1.77736202,1.01031334e-15 L14.8113502,1.01031334e-15 Z M8.8868101,7.10944808 L8.8868101,8.8868101 L4.14717807,8.8868101 L4.14717807,7.10944808 L8.8868101,7.10944808 Z M12.4415341,3.55472404 L12.4415341,5.33208606 L4.14717807,5.33208606 L4.14717807,3.55472404 L12.4415341,3.55472404 Z"></path>
      </g>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconFileEditComponent {

}
