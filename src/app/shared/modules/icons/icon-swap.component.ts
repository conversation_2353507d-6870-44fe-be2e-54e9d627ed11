import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'SwapIcon',
  template: `
    <svg viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g stroke="none" [attr.stroke-width]="strokeWidth()" fill="none" fill-rule="evenodd">
        <g transform="translate(0.220982, 0.220982)" fill="currentColor" fill-rule="nonzero">
          <path d="M18.4722222,19 L11.0833333,19 C10.7666667,19 10.5555556,18.7888889 10.5555556,18.4722222 L10.5555556,11.0833333 C10.5555556,10.7666667 10.7666667,10.5555556 11.0833333,10.5555556 L18.4722222,10.5555556 C18.7888889,10.5555556 19,10.7666667 19,11.0833333 L19,18.4722222 C19,18.7888889 18.7888889,19 18.4722222,19 L18.4722222,19 Z M11.6111111,17.9444444 L17.9444444,17.9444444 L17.9444444,11.6111111 L11.6111111,11.6111111 L11.6111111,17.9444444 L11.6111111,17.9444444 Z M8.97222223,19 C4.48611113,19 2.1111111,16.625 2.1111111,12.1388889 C2.1111111,11.8222222 2.32222221,11.6111111 2.6388889,11.6111111 C2.95555559,11.6111111 3.16666667,11.8222222 3.16666667,12.1388889 C3.16666667,16.0444445 5.06666667,17.9444445 8.97222223,17.9444444 C9.2888889,17.9444444 9.5,18.1555555 9.5,18.4722222 C9.5,18.7888889 9.2888889,19 8.97222223,19 L8.97222223,19 Z" id="形状"></path>
          <path d="M4.75,14.7777778 C4.59166667,14.7777778 4.4861111,14.725 4.38055556,14.6194444 L2.26944444,12.5083333 C2.05833333,12.2972222 2.05833333,11.9805556 2.26944444,11.7694444 C2.48055554,11.5583333 2.79722221,11.5583333 3.00833333,11.7694444 L5.11944444,13.8805556 C5.33055554,14.0916667 5.33055554,14.4083333 5.11944444,14.6194444 C5.01388887,14.725 4.90833333,14.7777778 4.75,14.7777778 L4.75,14.7777778 Z" id="路径"></path>
          <path d="M0.52777777,14.7777778 C0.369444436,14.7777778 0.263888873,14.725 0.158333333,14.6194444 C-0.0527777696,14.4083333 -0.0527777696,14.0916667 0.158333333,13.8805556 L2.26944444,11.7694444 C2.48055554,11.5583333 2.79722221,11.5583333 3.00833333,11.7694444 C3.21944446,11.9805555 3.21944444,12.2972222 3.00833333,12.5083333 L0.89722223,14.6194444 C0.791666666,14.725 0.686111127,14.7777778 0.52777777,14.7777778 Z" id="路径"></path>
          <path d="M16.3611111,7.3888889 C16.0444444,7.3888889 15.8333333,7.17777779 15.8333333,6.8611111 C15.8333333,2.95555554 13.9333333,1.05555554 10.0277778,1.05555556 C9.7111111,1.05555556 9.5,0.844444461 9.5,0.52777777 C9.5,0.211111079 9.7111111,2.15044969e-15 10.0277778,2.15044969e-15 C14.5138889,2.15044969e-15 16.8888889,2.375 16.8888889,6.8611111 C16.8888889,7.17777777 16.6777778,7.3888889 16.3611111,7.3888889 Z" id="路径"></path>
          <path d="M16.3611111,7.3888889 C16.2027778,7.3888889 16.0972222,7.33611113 15.9916667,7.23055556 L13.8805556,5.11944444 C13.6694445,4.90833333 13.6694445,4.59166667 13.8805556,4.38055556 C14.0916667,4.16944446 14.4083333,4.16944446 14.6194444,4.38055556 L16.7305556,6.49166667 C16.9416667,6.70277777 16.9416667,7.01944444 16.7305556,7.23055556 C16.625,7.33611113 16.5194445,7.3888889 16.3611111,7.3888889 L16.3611111,7.3888889 Z" id="路径"></path>
          <path d="M16.3611111,7.3888889 C16.2027778,7.3888889 16.0972222,7.33611113 15.9916667,7.23055556 C15.7805556,7.01944446 15.7805556,6.70277779 15.9916667,6.49166667 L18.1027778,4.38055556 C18.3138889,4.16944446 18.6305555,4.16944446 18.8416667,4.38055556 C19.0527778,4.59166667 19.0527778,4.90833333 18.8416667,5.11944444 L16.7305556,7.23055556 C16.625,7.33611113 16.5194445,7.3888889 16.3611111,7.3888889 L16.3611111,7.3888889 Z" id="路径"></path>
          <path d="M4.22222223,8.44444444 C1.9,8.44444444 1.07522485e-15,6.54444444 1.07522485e-15,4.22222223 C1.07522485e-15,1.90000002 1.9,2.15044969e-15 4.22222223,2.15044969e-15 C6.54444446,2.15044969e-15 8.44444444,1.9 8.44444444,4.22222223 C8.44444444,6.54444446 6.54444444,8.44444444 4.22222223,8.44444444 Z M4.22222223,1.05555556 C2.48055556,1.05555556 1.05555556,2.48055556 1.05555556,4.22222223 C1.05555556,5.9638889 2.48055556,7.3888889 4.22222223,7.3888889 C5.9638889,7.3888889 7.3888889,5.9638889 7.3888889,4.22222223 C7.3888889,2.48055556 5.9638889,1.05555556 4.22222223,1.05555556 Z" id="形状"></path>
        </g>
      </g>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconSwapComponent {

  strokeWidth = input('1');

}
