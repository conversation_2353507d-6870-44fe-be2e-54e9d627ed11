import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'app-icon-copy, CopyIcon',
  template: `
    <svg viewBox="0 0 19 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g transform="translate(0.766667, -0.000213)" fill="currentColor" fill-rule="nonzero">
        <path d="M10.8360407,1.43222368e-05 C12.6174466,1.43222368e-05 14.0844868,1.37467471 14.0844868,3.09861589 L14.0844868,3.2022777 L14.4450496,3.2022777 C16.1611035,3.2022777 17.5842,4.47889557 17.6878619,6.11156931 L17.6934957,6.30199155 L17.6934957,16.9014129 C17.6934957,18.5611288 16.3334976,19.8985917 14.6411057,19.9943662 L14.4450496,20 L6.85745493,20 C5.14140107,20 3.71830453,18.7245089 3.61464269,17.0918352 L3.6090089,16.9002862 L3.6090089,16.7943708 L3.24844603,16.7954976 C1.53239217,16.7954976 0.109295626,15.52226 0.00563381231,13.8873327 L0,13.6969105 L0,5.26537333 C0,4.43157173 0.351548789,3.6360799 0.970139448,3.05579906 L3.28224879,0.889041616 C3.90103005,0.315251161 4.71443011,-0.00245806799 5.55830186,1.43222368e-05 L5.42872458,0.00228232129 C5.45009726,0.000727823455 5.47152076,1.43222368e-05 5.49294984,1.43222368e-05 L5.51548503,0.00115555354 L5.55830186,1.43222368e-05 L10.8360407,1.43222368e-05 Z M14.0844868,13.6969105 C14.0844868,15.4208517 12.6185734,16.7954976 10.8360407,16.7954976 L5.29802055,16.7943708 L5.29914732,16.9014129 C5.29914732,17.6180316 5.90196336,18.2287349 6.69632838,18.3019743 L6.85745493,18.3098616 L14.4450496,18.3098616 C15.2619499,18.3098616 15.9165968,17.7442286 15.9954699,17.0433845 L16.0033572,16.9014129 L16.0033572,6.30199155 C16.0033572,5.58537286 15.3994145,4.97466953 14.6061761,4.90030344 L14.4450496,4.89354288 L14.0844868,4.89241611 L14.0844868,13.6969105 Z M10.8360407,1.69016722 L6.33801904,1.68904046 L6.33801904,3.23946075 C6.3381003,4.87731149 5.06351905,6.23235625 3.42872747,6.33241404 L3.23943196,6.33804785 L1.69013841,6.33804785 L1.69013841,13.6969105 C1.69013841,14.4146559 2.29295445,15.0242325 3.08731951,15.0974718 L3.24844603,15.1053592 L10.8360407,15.1053592 C11.7081522,15.1053592 12.3943484,14.4631066 12.3943484,13.6969105 L12.3943484,3.09861589 C12.3943484,2.33354656 11.7070254,1.69016722 10.8360407,1.69016722 L10.8360407,1.69016722 Z M4.64675389,1.95720909 C4.5722446,2.00584683 4.50211979,2.06089104 4.43717673,2.12171589 L2.12619412,4.28847333 C2.01464724,4.3924923 1.92066328,4.5138724 1.84788468,4.64790944 L3.23943196,4.64790944 C3.96844499,4.64790944 4.56900752,4.09354404 4.64112008,3.38368591 L4.64788063,3.23946075 L4.64675389,1.95720909 Z"></path>
      </g>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconCopyComponent {

}
