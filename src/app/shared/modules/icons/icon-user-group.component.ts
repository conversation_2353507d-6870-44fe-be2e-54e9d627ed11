import { ChangeDetectionStrategy, Component, input, numberAttribute } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'UserGroupIcon',
  template: `
    @switch (style()) {
      @case (1) {
        <svg viewBox="0 0 20 13" version="1.1" xmlns="http://www.w3.org/2000/svg">
          <g transform="translate(0.046875, 0.437500)" fill="currentColor" fill-rule="nonzero">
            <path d="M13.6589421,7.54448363 C13.1899244,7.07546599 12.6519899,6.69259446 12.058539,6.40735516 C12.0776826,6.38821159 12.0949118,6.37289673 12.1140554,6.35375315 C12.4567254,6.01108312 12.7228212,5.61481108 12.9104282,5.1706801 C13.1056927,4.71123426 13.2033249,4.22307305 13.2033249,3.72151134 C13.2033249,3.21994962 13.1037783,2.73178841 12.9104282,2.27234257 C12.7228212,1.83012594 12.4548111,1.43002519 12.1140554,1.08926952 C11.7713854,0.746599496 11.3751134,0.480503778 10.9309824,0.292896725 C10.4715365,0.0976322418 9.98337531,0 9.4818136,0 C8.98025189,0 8.49209068,0.0995465995 8.03264484,0.292896725 C7.59042821,0.480503778 7.19032746,0.748513854 6.84957179,1.08926952 C6.50690176,1.43193955 6.24080605,1.82821159 6.05319899,2.27234257 C5.85793451,2.73178841 5.76030227,3.21994962 5.76030227,3.72151134 C5.76030227,4.22307305 5.85984887,4.71123426 6.05319899,5.1706801 C6.24080605,5.61289673 6.50881612,6.01299748 6.84957179,6.35375315 C6.87254408,6.37672544 6.89743073,6.39969773 6.92040302,6.42267003 C6.34035264,6.70790932 5.81198992,7.08503778 5.35062972,7.54639798 C4.81269521,8.08433249 4.38770781,8.71224181 4.09098237,9.41289673 C3.78468514,10.1384383 3.62770781,10.9080101 3.62770781,11.7005542 C3.62770781,11.8326448 3.63345088,11.9666499 3.64110831,12.0987406 L5.26256927,12.0987406 C5.25108312,11.9666499 5.24342569,11.8345592 5.24342569,11.7005542 C5.24342569,9.39183879 7.08120907,7.51002519 9.37460957,7.44302267 C9.40906801,7.44493703 9.44352645,7.44493703 9.47989924,7.44493703 C9.52201511,7.44493703 9.56221662,7.44302267 9.60433249,7.44302267 C11.9092191,7.49662469 13.7623174,9.382267 13.7623174,11.7005542 C13.7623174,11.8345592 13.7546599,11.9685642 13.7431738,12.0987406 L15.3646348,12.0987406 C15.3722922,11.9666499 15.3780353,11.8345592 15.3780353,11.7005542 C15.3780353,10.9080101 15.2229723,10.1384383 14.9147607,9.41289673 C14.6199496,8.71224181 14.1968766,8.08433249 13.6589421,7.54448363 Z M9.58518892,5.82539043 C9.55838791,5.82539043 9.5315869,5.82347607 9.50478589,5.82347607 C9.46649874,5.82347607 9.42821159,5.82347607 9.38801008,5.82539043 C8.26811083,5.77561713 7.37410579,4.85481108 7.37410579,3.72151134 C7.37410579,2.55758186 8.31596977,1.61571788 9.47989924,1.61571788 C10.6438287,1.61571788 11.5856927,2.55758186 11.5856927,3.72151134 C11.5856927,4.85098237 10.6993451,5.77178841 9.58518892,5.82539043 L9.58518892,5.82539043 Z" id="Shape"></path>
            <path d="M18.603728,9.81108312 C18.3529471,9.21571788 17.9930479,8.68161209 17.5355164,8.22216625 C17.1354156,7.82206549 16.6778841,7.49662469 16.1724937,7.25350126 C16.1878086,7.2381864 16.2031234,7.22287154 16.2203526,7.20755668 C16.511335,6.91657431 16.7391436,6.577733 16.8980353,6.20060453 C17.0645844,5.81007557 17.1469018,5.39465995 17.1469018,4.96775819 C17.1469018,4.54085642 17.06267,4.12544081 16.8980353,3.73491184 C16.7391436,3.35778338 16.5094207,3.01894207 16.2203526,2.7279597 C15.9293703,2.43697733 15.590529,2.20916877 15.2134005,2.05027708 C14.8228715,1.88372796 14.4074559,1.80141058 13.9805542,1.80141058 C13.7910327,1.80141058 13.6015113,1.8186398 13.417733,1.85118388 C13.6129975,2.26085642 13.7374307,2.71264484 13.7718892,3.1893199 C13.840806,3.18166247 13.9097229,3.17783375 13.9805542,3.17783375 C14.9702771,3.17783375 15.7723929,3.97994962 15.7723929,4.96967254 C15.7723929,5.9306801 15.018136,6.71556675 14.0686146,6.76151134 C14.0456423,6.76151134 14.02267,6.75959698 13.9996977,6.75959698 C13.9671537,6.75959698 13.9346096,6.75959698 13.9001511,6.76151134 C13.8465491,6.75959698 13.7910327,6.7538539 13.7393451,6.74619647 C14.2523929,7.18458438 14.6926952,7.70528967 15.039194,8.28725441 C16.5343073,8.73521411 17.6235768,10.1192947 17.6235768,11.7598992 C17.6235768,11.8747607 17.6178338,11.9877078 17.608262,12.0987406 L18.9885139,12.0987406 C18.9961713,11.9877078 19,11.8728463 19,11.7598992 C18.9980856,11.0822166 18.865995,10.4275063 18.603728,9.81108312 L18.603728,9.81108312 Z M0.394357683,9.81108312 C0.645138539,9.21571788 1.00503778,8.68161209 1.46256927,8.22216625 C1.86267003,7.82206549 2.32020151,7.49662469 2.82559194,7.25350126 C2.81027708,7.2381864 2.79496222,7.22287154 2.777733,7.20755668 C2.48675063,6.91657431 2.25894207,6.577733 2.10005038,6.20060453 C1.93350126,5.81007557 1.85118388,5.39465995 1.85118388,4.96775819 C1.85118388,4.54085642 1.93541562,4.12544081 2.10005038,3.73491184 C2.25894207,3.35778338 2.48866499,3.01894207 2.777733,2.7279597 C3.06871537,2.43697733 3.40755668,2.20916877 3.78468514,2.05027708 C4.17521411,1.88372796 4.59062972,1.80141058 5.01753149,1.80141058 C5.2070529,1.80141058 5.39657431,1.8186398 5.58035264,1.85118388 C5.38508816,2.26085642 5.26065491,2.71264484 5.22619647,3.1893199 C5.1572796,3.18166247 5.08836272,3.17783375 5.01753149,3.17783375 C4.02780856,3.17783375 3.2256927,3.97994962 3.2256927,4.96967254 C3.2256927,5.9306801 3.97994962,6.71556675 4.92947103,6.76151134 C4.95244332,6.76151134 4.97541562,6.75959698 4.99838791,6.75959698 C5.03093199,6.75959698 5.06347607,6.75959698 5.09793451,6.76151134 C5.15153652,6.75959698 5.2070529,6.7538539 5.25874055,6.74619647 C4.7456927,7.18458438 4.30539043,7.70528967 3.95889169,8.28725441 C2.46377834,8.73521411 1.37450882,10.1192947 1.37450882,11.7598992 C1.37450882,11.8747607 1.38025189,11.9877078 1.38982368,12.0987406 L0.0114861461,12.0987406 C0.00382871537,11.9877078 0,11.8728463 0,11.7598992 C0,11.0822166 0.13209068,10.4275063 0.394357683,9.81108312 L0.394357683,9.81108312 Z" id="Shape"></path>
          </g>
        </svg>
      }
      @case (2) {
        <svg style="height: 0.7em;" viewBox="0 0 20 17" version="1.1" xmlns="http://www.w3.org/2000/svg">
          <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g id="人群包判断" transform="translate(-0.000039, 0.666667)" fill-rule="nonzero">
              <path d="M10.0509291,6.70053868 C11.9012318,6.70053868 13.4011985,5.200572 13.4011985,3.35026934 C13.4011985,1.49996668 11.9012318,0 10.0509291,0 C8.20062647,0 6.7006598,1.49996668 6.7006598,3.35026934 C6.7006598,5.200572 8.20062647,6.70053868 10.0509291,6.70053868 Z M6.7442133,8.17298205 C5.31113559,8.70064947 4.42833963,10.0491329 4.10838891,11.5416779 L3.75828577,13.1724215 C3.51874151,14.2930865 4.0782365,15.4212897 5.18466294,15.7186262 C6.33464288,16.0285261 7.9905135,16.332563 10.0509291,16.332563 C12.1130199,16.332563 13.768053,16.0285261 14.9180329,15.7186262 C16.0244594,15.4212897 16.5847919,14.2930865 16.3444101,13.1724215 L15.9943069,11.5416779 C15.6743562,10.0491329 14.7915603,8.70064947 13.3584825,8.17298205 C12.4321331,7.83125458 11.2788029,7.538106 10.0517667,7.538106 C8.82473056,7.538106 7.67056279,7.83125456 6.74337575,8.17298205 L6.7442133,8.17298205 Z" id="形状" fill="#FFA717"></path>
              <path d="M18.0078188,5.44418768 C18.0078188,6.60062685 17.0703396,7.53810602 15.9139005,7.53810602 C14.7574613,7.53810602 13.8199821,6.60062685 13.8199821,5.44418768 C13.8199821,4.28774851 14.7574613,3.35026934 15.9139005,3.35026934 C17.0703396,3.35026934 18.0078188,4.28774851 18.0078188,5.44418768 L18.0078188,5.44418768 Z M17.0404285,10.803781 C16.8704024,9.89669562 16.4934971,9.03818909 15.9139005,8.37567334 L16.3904763,8.37567334 C17.9700174,8.37584163 19.3347629,9.47950542 19.6653646,11.0240612 L19.9459496,12.3348541 C20.1863314,13.4555192 19.6117602,14.5544076 18.4843946,14.7596115 C18.2758404,14.7981397 18.0488596,14.8349926 17.8026148,14.8684953 L17.0404285,10.803781 L17.0404285,10.803781 Z M1.99269382,5.44418768 C1.99269382,6.19227266 2.39179238,6.8835317 3.03965298,7.25757419 C3.68751357,7.63161669 4.48571075,7.63161669 5.13357134,7.25757419 C5.78143194,6.8835317 6.1805305,6.19227266 6.1805305,5.44418768 C6.1805305,4.28774851 5.24305133,3.35026934 4.08661216,3.35026934 C2.930173,3.35026934 1.99269382,4.28774851 1.99269382,5.44418768 L1.99269382,5.44418768 Z M2.96008409,10.803781 C3.13011025,9.8975332 3.50701555,9.03902667 4.08661216,8.37567334 L3.61003636,8.37567334 C2.03049525,8.37584163 0.665749695,9.47950542 0.335148076,11.0240612 L0.0537254524,12.3348541 C-0.185818811,13.4555192 0.388752392,14.5544076 1.51611802,14.7596115 C1.72383471,14.7981397 1.95165304,14.8349926 2.19789783,14.8684953 L2.96008409,10.803781 L2.96008409,10.803781 Z" id="形状" fill="#FFDDA6"></path>
            </g>
          </g>
        </svg>
      }
    }
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconUserGroupComponent {

  style = input(1, { transform: numberAttribute });
  
}
