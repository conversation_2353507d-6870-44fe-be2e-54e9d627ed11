import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'MapArrowRightIcon',
  template: `
    <svg viewBox="0 0 20 18" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="bold-map-arrow-up" transform="translate(0.667738, 0.332262)" fill="currentColor" fill-rule="nonzero">
          <path d="M1.10691442,15.6779772 L8.1008167,-0.0065278899 C8.66131687,-1.26432831 10.3380674,-1.26432831 10.8995176,-0.0065278899 L17.8934199,15.6779772 C18.5270701,17.0982277 17.1201196,18.5726282 15.8015192,17.8686779 L10.1927174,14.874277 C9.75666724,14.6415269 9.24271706,14.6415269 8.80761693,14.874277 L3.1988151,17.8686779 C1.88021468,18.5716782 0.472314217,17.0991777 1.10691442,15.6779772 Z" id="路径" transform="translate(9.500000, 8.550122) rotate(90.000000) translate(-9.500000, -8.550122) "></path>
        </g>
      </g>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconMapArrowRightComponent {

}
