import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'Chart3DIcon',
  template: `
    <svg viewBox="0 0 19 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="chart-3D" transform="translate(0.066602, 0.000000)" fill-rule="nonzero">
          <path d="M0,12.278506 C0,16.2109162 3.18784839,19.3987646 7.12025867,19.3987646 C11.0526689,19.3987646 14.2405173,16.2109162 14.2405173,12.278506 C14.2405173,9.73468251 12.8834037,7.38409271 10.680388,6.11218098 C8.47737232,4.84026925 5.76314502,4.84026925 3.5601293,6.11218098 C1.35711359,7.38409271 0,9.73468251 0,12.278506 Z" fill="#E9EAEB"></path>
          <polygon fill="#FFFFFF" points="5.79114371 6.64557475 5.75950585 6.64557475 1.96203456 8.86075861 1.96203456 12.9747013 5.79114371 15.1898852 9.55697713 12.9747013 9.55697713 8.86075861"></polygon>
          <path d="M5.79114371,15.5696323 L1.67722421,13.1645749 L1.67722421,8.67086187 L5.69620693,6.32910336 L5.85444263,6.32910336 L5.94937941,6.36074123 L9.87342535,8.67088505 L9.87342535,13.1328906 L5.79114371,15.5696323 Z M2.27848277,12.7848046 L5.75950585,14.810138 L9.24052892,12.7848277 L9.24052892,9.05063218 L5.75950585,7.02532188 L2.27848277,9.05063218 L2.27848277,12.7848277 L2.27848277,12.7848046 Z" id="形状" fill="#2A5082"></path>
          <polygon fill="#2A5082" points="5.79114371 11.4556896 1.80379886 9.14556896 2.15190812 8.57594827 5.79114371 10.6962185 9.39874144 8.57594827 9.7468507 9.14556896"></polygon>
          <polygon fill="#2A5082" points="5.44305763 11.0759657 6.07595406 11.0759657 6.07595406 15.1898852 5.44308081 15.1898852"></polygon>
          <polygon fill="#BCC0C4" points="17.4683679 4.4303909 18.1012875 4.4303909 18.1012875 6.32912654 17.4683679 6.32912654"></polygon>
          <path d="M16.8354715,5.06328733 L18.7342071,5.06328733 L18.7342071,5.69620693 L16.8354715,5.69620693 L16.8354715,5.06328733 Z M1.96203456,2.84810347 L2.59495417,2.84810347 L2.59495417,4.74683911 L1.96203456,4.74683911 L1.96203456,2.84810347 Z" id="形状" fill="#BCC0C4"></path>
          <polygon fill="#BCC0C4" points="1.32911495 3.48102307 3.2278506 3.48102307 3.2278506 4.1139195 1.32911495 4.1139195"></polygon>
          <polygon fill="#A3D4FF" points="9.588615 0.949367822 9.55697713 0.949367822 5.75950585 3.16455168 5.75950585 15.1898852 9.588615 17.405069 13.3544484 15.1898852 13.3544484 3.16455168"></polygon>
          <path d="M9.588615,17.7848161 L5.4746955,15.3797587 L5.4746955,2.97470129 L9.49367822,0.632919607 L9.65191392,0.632919607 L9.7468507,0.664557475 L13.6708966,2.97470129 L13.6708966,15.3481208 L9.588615,17.7848161 Z M6.07595406,15.0000116 L9.55697713,17.0253219 L13.0380002,15.0000116 L13.0380002,3.35442525 L9.55697713,1.32911495 L6.07595406,3.35442525 L6.07595406,15.0000116 Z" id="形状" fill="#2A5082"></path>
          <polygon fill="#2A5082" points="9.588615 5.75948267 5.60127015 3.44936203 5.94937941 2.87974133 9.588615 5.00001159 13.1962127 2.87974133 13.544322 3.44936203"></polygon>
          <polygon fill="#2A5082" points="9.24052892 5.37975872 9.87342535 5.37975872 9.87342535 17.405069 9.2405521 17.405069"></polygon>
          <polygon fill="#E9EAEB" points="13.3860863 6.32912654 13.3544484 6.32912654 9.55697713 8.5443104 9.55697713 17.405069 13.3860863 19.620276 17.1519197 17.405069 17.1519197 8.5443104"></polygon>
          <path d="M13.3860863,20 L9.27216679,17.5949658 L9.27216679,8.35443683 L13.2911495,6.01265515 L13.4493852,6.01265515 L13.544322,6.04431619 L17.4683679,8.35443683 L17.4683679,17.5633047 L13.3860863,20 Z M9.87342535,17.2152186 L13.3544484,19.2405521 L16.8354715,17.2152186 L16.8354715,8.73418396 L13.3544484,6.70887367 L9.87342535,8.73418396 L9.87342535,17.2152186 Z" id="形状" fill="#2A5082"></path>
          <polygon fill="#2A5082" points="13.3860863 11.1392414 9.39874144 8.82912075 9.7468507 8.25950005 13.3860863 10.3797471 16.993684 8.25950005 17.3417933 8.82912075"></polygon>
          <polygon fill="#2A5082" points="13.0380002 10.7594943 13.6708966 10.7594943 13.6708966 19.620276 13.0380234 19.620276"></polygon>
          <path d="M15.9177415,2.53165525 C15.2215462,2.53165525 14.6519255,1.96203456 14.6519255,1.26581604 C14.6519255,0.569597515 15.2215462,-4.21604006e-16 15.9177415,-4.21604006e-16 C16.6139369,-4.21604006e-16 17.1835576,0.569620693 17.1835576,1.26581604 C17.1835576,1.96201138 16.6139369,2.53165525 15.9177415,2.53165525 Z M15.9177415,0.632919607 C15.5696323,0.632919607 15.2848219,0.917729954 15.2848219,1.26581604 C15.2848219,1.61390212 15.5696323,1.89873564 15.9177415,1.89873564 C16.2658508,1.89873564 16.5506612,1.6139253 16.5506612,1.26581604 C16.5506612,0.917706776 16.2658508,0.632919607 15.9177415,0.632919607 Z" id="形状" fill="#BCC0C4"></path>
        </g>
      </g>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconChart3DComponent {

}
