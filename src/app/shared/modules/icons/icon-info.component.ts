import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  standalone: true,
  selector: 'InfoIcon',
  template: `
    <svg viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g transform="translate(0.666667, 0.666667)" fill="currentColor" fill-rule="nonzero">
        <path d="M9.5,3.16413562e-16 C14.74685,3.16413562e-16 19,4.25315 19,9.5 C19,14.74685 14.74685,19 9.5,19 C4.25315,19 3.16413562e-16,14.74685 3.16413562e-16,9.5 C3.16413562e-16,4.25315 4.25315,3.16413562e-16 9.5,3.16413562e-16 Z M9.5,1.425 C5.040225,1.425 1.425,5.040225 1.425,9.5 C1.425,13.959775 5.040225,17.575 9.5,17.575 C13.959775,17.575 17.575,13.959775 17.575,9.5 C17.575,5.040225 13.959775,1.425 9.5,1.425 Z M9.5,7.12499999 C9.89350288,7.12499999 10.2125,7.44399711 10.2125,7.83749999 L10.2125,12.825 L10.925,12.825 C11.3185029,12.825 11.6375,13.1439971 11.6375,13.5375 C11.6375,13.9310029 11.3185029,14.25 10.925,14.25 L8.075,14.25 C7.68149712,14.25 7.3625,13.9310029 7.3625,13.5375 C7.3625,13.1439971 7.68149712,12.825 8.075,12.825 L8.7875,12.825 L8.7875,8.54999999 L8.075,8.54999999 C7.68149712,8.54999999 7.3625,8.23100288 7.3625,7.83749999 C7.3625,7.44399711 7.68149712,7.12499999 8.075,7.12499999 L9.5,7.12499999 Z M9.5,4.275 C9.83940232,4.275 10.153023,4.45606896 10.3227242,4.74999999 C10.4924253,5.04393102 10.4924253,5.40606899 10.3227242,5.70000001 C10.153023,5.99393104 9.83940232,6.17500001 9.5,6.17500001 C8.9753295,6.17500001 8.55000002,5.74967051 8.55000002,5.225 C8.55000002,4.7003295 8.9753295,4.275 9.5,4.275 L9.5,4.275 Z" id="Shape"></path>
      </g>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconInfoComponent {

}
