import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'KefuIcon',
  template: `
    <svg viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g transform="translate(0.000000, -0.000000)" fill="#3591F4" fill-rule="nonzero">
          <path d="M10.326,0 C14.793,0 18.455,3.787 18.679,8.544 L18.684,8.704 L18.749,8.745 C19.4731354,9.24237656 19.9310874,10.0426882 19.993,10.919 L20.0000144,11.125 L20.0000144,12.456 C20.0036012,13.5974629 19.3318034,14.6330205 18.288,15.095 L18.179,15.137 L18.059,15.34 C16.8622005,17.2807367 14.9296551,18.6532913 12.703,19.144 L12.49,19.185 L12.45,19.248 C12.139,19.671 11.667,19.941 11.152,19.991 L10.978,20 C9.958,20 9.13,19.156 9.13,18.114 C9.13,17.073 9.958,16.228 10.979,16.228 C11.665,16.23 12.283,16.618 12.599,17.221 L12.646,17.321 L12.766,17.289 C13.9585551,16.9425646 15.0301018,16.2687339 15.859,15.344 L16.069,15.096 L16.003,15.068 C15.0558505,14.6241135 14.424655,13.7006837 14.355,12.657 L14.348,12.457 L14.348,11.125 C14.35,9.665 15.413,8.459 16.795,8.269 L16.913,8.256 L16.911,8.219 C16.557,4.666 13.851,1.909 10.567,1.779 L10.327,1.775 L9.674,1.775 C6.364,1.775 3.586,4.448 3.119,7.961 L3.086,8.256 L3.205,8.269 C4.529,8.451 5.561,9.567 5.646,10.944 L5.652,11.125 L5.652,12.456 C5.652,14.047 4.385,15.341 2.826,15.341 C1.267,15.341 0,14.047 0,12.456 L0,11.126 C0,10.158 0.478,9.277 1.25,8.745 L1.315,8.702 L1.321,8.543 C1.541,3.88 5.065,0.149 9.412,0.004 L9.674,0 L10.326,0 L10.326,0 Z" id="路径"></path>
        </g>
      </g>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconKefuComponent {

}
