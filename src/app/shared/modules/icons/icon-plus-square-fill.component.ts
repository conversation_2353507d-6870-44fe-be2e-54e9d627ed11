import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'PlusSquareFillIcon',
  template: `
    <svg style="height: 0.8em" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g transform="translate(0.500000, 0.500000)" fill="currentColor" fill-rule="nonzero">
          <path d="M2.71428571,0 C1.2171875,0 0,1.2171875 0,2.71428571 L0,16.2857143 C0,17.7828125 1.2171875,19 2.71428571,19 L16.2857143,19 C17.7828125,19 19,17.7828125 19,16.2857143 L19,2.71428571 C19,1.2171875 17.7828125,0 16.2857143,0 L2.71428571,0 Z M8.48214286,13.2321429 L8.48214286,10.5178571 L5.76785714,10.5178571 C5.20379464,10.5178571 4.75,10.0640625 4.75,9.5 C4.75,8.9359375 5.20379464,8.48214286 5.76785714,8.48214286 L8.48214286,8.48214286 L8.48214286,5.76785714 C8.48214286,5.20379464 8.9359375,4.75 9.5,4.75 C10.0640625,4.75 10.5178571,5.20379464 10.5178571,5.76785714 L10.5178571,8.48214286 L13.2321429,8.48214286 C13.7962054,8.48214286 14.25,8.9359375 14.25,9.5 C14.25,10.0640625 13.7962054,10.5178571 13.2321429,10.5178571 L10.5178571,10.5178571 L10.5178571,13.2321429 C10.5178571,13.7962054 10.0640625,14.25 9.5,14.25 C8.9359375,14.25 8.48214286,13.7962054 8.48214286,13.2321429 Z" id="形状"></path>
        </g>
      </g>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconPlusSquareFillComponent {

}
