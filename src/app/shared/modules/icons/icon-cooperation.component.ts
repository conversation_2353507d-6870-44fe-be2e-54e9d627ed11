import { ChangeDetectionStrategy, Component, input, numberAttribute } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'app-icon-cooperation, CooperationIcon',
  template: `
    @switch (style()) {
      @case (1) {
        <svg viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
          <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g fill="#FF7900" fill-rule="nonzero">
              <path d="M10,0 C4.48,0 0,4.48 0,10 C0,15.52 4.48,20 10,20 C15.52,20 20,15.52 20,10 C20,4.48 15.52,0 10,0 Z M12.52,12.76 L9.94,10.18 C9.86,10.1 9.74,10.1 9.66,10.18 C9.58,10.26 9.58,10.38 9.66,10.46 L12.24,13.04 L11.26,14.02 L8.8,11.58 C8.72,11.5 8.6,11.5 8.52,11.58 C8.44,11.66 8.44,11.78 8.52,11.86 L10.96,14.3 L10.42,14.84 C10.18,15.08 9.8,15.08 9.58,14.84 L6.5,11.78 L4.24,9.5 C3.92,9.18 3.92,8.68 4.24,8.36 L6.6,6 C6.92,5.68 7.42,5.68 7.74,6 L10,8.28 L13.5,11.78 L12.52,12.76 Z M15.76,9.5 L13.78,11.48 L10.28,8 L12.26,6.02 C12.58,5.7 13.08,5.7 13.4,6.02 L15.76,8.38 C16.08,8.7 16.08,9.2 15.76,9.5 Z"></path>
            </g>
          </g>
        </svg>
      }
      @case (2) {
        <svg viewBox="0 0 20 18" version="1.1" xmlns="http://www.w3.org/2000/svg">
          <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g id="cooperation-fill" fill="currentColor" fill-rule="nonzero">
              <path d="M12.5157654,7.04753174 C12.4993682,7.03061983 12.4768178,7.02107433 12.4532619,7.02107433 C12.429706,7.02107433 12.4071556,7.03061983 12.3907584,7.04753174 L11.7233105,7.7149797 C10.4515067,8.98636743 8.38994655,8.98636743 7.11814275,7.7149797 C5.84575032,6.44258726 5.84575032,4.37997216 7.11814275,3.109812 L8.79457559,1.43337916 C8.81248734,1.41584686 8.82220562,1.39159355 8.82135647,1.36654378 C8.82050733,1.341494 8.80916895,1.31795448 8.79011106,1.30167538 C6.68629569,-0.526693433 3.52669879,-0.416531616 1.55533233,1.5539216 C-0.51844411,3.62769804 -0.51844411,6.98949278 1.55533233,9.06103695 L8.86377588,16.3694805 L8.85707908,16.3784096 L9.36157152,16.882902 C9.71086251,17.2308423 10.2757439,17.2308423 10.6250349,16.882902 L16.4244657,11.081239 C16.4413776,11.0648418 16.4509231,11.0422914 16.4509231,11.0187355 C16.4509231,10.9951796 16.4413776,10.9726291 16.4244657,10.956232 L12.5157654,7.04753174 Z M18.4446677,1.5539216 C16.3711249,-0.517879226 13.0110951,-0.517879226 10.9375523,1.5539216 L10.3303756,2.16109834 L10.3348401,2.16556288 L8.25436687,4.24603612 C7.6120288,4.88849192 7.61003285,5.92937655 8.24990234,6.57429104 L8.25436687,6.57875558 C8.8990758,7.22159819 9.94237741,7.22159819 10.5870863,6.57875558 L11.3170378,5.84880413 L12.4063843,4.75945763 C12.4186027,4.7466283 12.4355452,4.73936722 12.4532619,4.73936722 C12.4709786,4.73936722 12.4879211,4.7466283 12.5001395,4.75945763 L17.5606898,9.82000788 C17.5966116,9.85319293 17.6520071,9.85319293 17.687929,9.82000788 L18.4446677,9.06103695 C20.5184441,6.98949278 20.5184441,3.62769804 18.4446677,1.5539216 Z" id="形状"></path>
            </g>
          </g>
        </svg>
      }
    }
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconCooperationComponent {

  style = input(1, { transform: numberAttribute });
  
}
