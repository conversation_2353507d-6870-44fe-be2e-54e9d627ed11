import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'app-icon-data-trend, DataTrendIcon',
  template: `
    <svg viewBox="0 0 20 17" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g transform="translate(0.666667, 0.000000)" fill-rule="nonzero">
          <path d="M7.12499999,8.75781249 C7.12499999,11.9550234 9.71685156,14.546875 12.9140625,14.546875 C16.1112734,14.546875 18.703125,11.9550234 18.703125,8.75781249 C18.703125,5.56060155 16.1112734,2.96874999 12.9140625,2.96874999 C9.71685156,2.96874999 7.12499999,5.56060155 7.12499999,8.75781249 L7.12499999,8.75781249 Z" fill-opacity="0.2" fill="#0B77E3"></path>
          <polygon fill="#A3D4FF" points="2.07812501 12.171875 5.64062501 12.171875 5.64062501 15.734375 2.07812501 15.734375"></polygon>
          <path d="M5.9375,16.03125 L1.78125,16.03125 L1.78125,11.875 L5.9375,11.875 L5.9375,16.03125 L5.9375,16.03125 Z M2.37500001,15.4375 L5.34375,15.4375 L5.34375,12.46875 L2.37500001,12.46875 L2.37500001,15.4375 Z" fill="#0B77E3"></path>
          <polygon fill="#E9EAEB" points="5.64062501 7.71875 9.20312501 7.71875 9.20312501 15.734375 5.64062501 15.734375"></polygon>
          <path d="M9.5,16.03125 L5.34375,16.03125 L5.34375,7.42187499 L9.5,7.42187499 L9.5,16.03125 Z M5.9375,15.4375 L8.90624999,15.4375 L8.90624999,8.01562499 L5.9375,8.01562499 L5.9375,15.4375 L5.9375,15.4375 Z" fill="#0B77E3"></path>
          <polygon fill="#A3D4FF" points="9.20312501 10.390625 12.765625 10.390625 12.765625 15.734375 9.20312501 15.734375"></polygon>
          <path d="M13.0625,16.03125 L8.90624999,16.03125 L8.90624999,10.09375 L13.0625,10.09375 L13.0625,16.03125 L13.0625,16.03125 Z M9.5,15.4375 L12.46875,15.4375 L12.46875,10.6875 L9.5,10.6875 L9.5,15.4375 Z" fill="#0B77E3"></path>
          <polygon fill="#FFFFFF" points="12.765625 6.234375 16.328125 6.234375 16.328125 15.734375 12.765625 15.734375"></polygon>
          <path d="M16.625,16.03125 L12.46875,16.03125 L12.46875,5.9375 L16.625,5.9375 L16.625,16.03125 Z M13.0625,15.4375 L16.03125,15.4375 L16.03125,6.53124999 L13.0625,6.53124999 L13.0625,15.4375 L13.0625,15.4375 Z M16.625,2.96874999 L16.03125,2.96874999 L16.03125,0.593750007 L13.65625,0.593750007 L13.65625,-3.95516952e-16 L16.625,-3.95516952e-16 L16.625,2.96874999 Z" fill="#0B77E3"></path>
          <path d="M4.53090626,2.47089062 L6.9029375,0.0988593749 L7.32301563,0.518640617 L4.95098437,2.89067188 L4.53090626,2.47089062 Z M3.35557813,3.64651561 L3.94339063,3.05870312 L4.36317187,3.47848438 L3.77535937,4.06629688 L3.35557813,3.64651561 Z" fill="#BCC0C4"></path>
          <path d="M18.40625,15.4375 L19,15.4375 L19,16.03125 L18.40625,16.03125 L18.40625,15.4375 Z M-3.88578059e-16,15.4375 L0.593750007,15.4375 L0.593750007,16.03125 L-3.88578059e-16,16.03125 L-3.88578059e-16,15.4375 Z M1.18750001,15.4375 L17.8125,15.4375 L17.8125,16.03125 L1.18750001,16.03125 L1.18750001,15.4375 Z M4.66093751,6.44218749 L4.2453125,6.0265625 L7.71875,2.55312501 L10.6875,5.521875 L16.1203125,0.0890624999 L16.5359375,0.504687507 L10.6875,6.35312499 L7.71875,3.384375 L4.66093751,6.44218749 Z M2.76331251,7.50440624 L3.64503125,6.62268749 L4.0648125,7.04246874 L3.18309375,7.92418749 L2.76331251,7.50440624 Z M1.57432813,8.69339062 L2.16214063,8.10557812 L2.58192187,8.52535936 L1.99410937,9.11317186 L1.57432813,8.69339062 Z M0.386828139,9.88089061 L0.974640639,9.29307811 L1.39442188,9.71285937 L0.806609382,10.3006719 L0.386828139,9.88089061 Z" fill="#0B77E3"></path>
          <path d="M2.16807814,4.83401563 L2.75589064,4.24620313 L3.17567188,4.66598437 L2.58785938,5.25379687 L2.16807814,4.83401563 Z M0.980578124,6.02151562 L1.56839062,5.43370312 L1.98817189,5.85348436 L1.40035939,6.44129686 L0.980578124,6.02151562 Z" fill="#0B77E3"></path>
        </g>
      </g>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconDataTrendComponent {

}
