import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'GraphPieIcon',
  template: `
    <svg viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="饼状图" transform="translate(0.433333, 0.666667)" fill-rule="nonzero">
          <path d="M8.79881982,10.0465556 L5.50829705,17.1183265 C2.87628152,15.8916266 1.14548494,13.302318 1.01834379,10.4012607 C0.891202632,7.5002034 2.38884131,4.76940608 4.90344952,3.31715128 L8.79881982,10.0465556 Z" id="路径" fill="#96DDFF"></path>
          <path d="M3.79266032,4.08557392 L8.79881982,10.0465556 L4.90344952,3.30193499 C4.5104791,3.5293766 4.13872355,3.7916425 3.79266032,4.08557392 Z" id="路径" fill="#69BAF9"></path>
          <path d="M8.79881982,0.380407263 L8.79881982,10.0465556 L18.4649682,10.0465556 C18.4649682,4.7080893 14.1372862,0.380407263 8.79881982,0.380407263 Z" id="路径" fill="#79DEB4"></path>
          <path d="M8.79881982,10.0465556 L5.16593052,17.8372962 C7.8297457,19.0856103 10.9468934,18.8852145 13.4289372,17.3060826 C15.910981,15.7269506 17.4132703,12.988357 17.4112421,10.0465556 L8.79881982,10.0465556 Z M8.79881982,1.45311172 C7.2904478,1.44832069 5.80756189,1.84200721 4.50021782,2.59437749 L8.79881982,10.0351434 L8.79881982,1.45311172 Z" id="形状" fill="#EDF4FF"></path>
          <path d="M8.79881982,10.0465556 L8.77979944,10.0884004 L17.0878939,12.3137829 C17.2897959,11.5697683 17.39213,10.8022622 17.3922197,10.0313393 L8.79881982,10.0465556 Z M8.79881982,1.45315572 C8.29866076,1.45415317 7.79959799,1.49998546 7.30762336,1.59010234 L8.79881982,10.0465556 L8.79881982,1.45315572 Z" id="形状" fill="#D8E3F0"></path>
          <path d="M6.11314459,10.0465556 C6.11305717,11.0061132 6.62492486,11.8928184 7.45591157,12.3726225 C8.28689827,12.8524265 9.31074135,12.8524265 10.1417281,12.3726225 C10.9727148,11.8928184 11.4845824,11.0061132 11.484495,10.0465556 C11.4845824,9.08699801 10.9727148,8.20029281 10.1417281,7.72048876 C9.31074135,7.24068472 8.28689827,7.24068472 7.45591157,7.72048876 C6.62492486,8.20029281 6.11305717,9.08699801 6.11314459,10.0465556 L6.11314459,10.0465556 Z" id="路径" fill="#EDF4FF"></path>
          <path d="M9.27432888,7.40652927 C9.95360323,7.93394615 10.2674614,8.806032 10.0800688,9.64535686 C9.89267619,10.4846817 9.23769816,11.1404595 8.3986026,11.3288761 C7.55950703,11.5172928 6.68703885,11.204499 6.15879346,10.5258688 C6.39971382,11.8650628 7.60411466,12.8124726 8.96237966,12.7312351 C10.3206447,12.6499976 11.4035346,11.5657854 11.4831147,10.2074223 C11.5626947,8.84905914 10.6138159,7.64581528 9.27432888,7.40652927 L9.27432888,7.40652927 Z" id="路径" fill="#D8E3F0"></path>
          <polygon id="路径" fill="#FFEBCC" points="0.380407241 10.0465556 8.6200284 10.0465556 8.6200284 15.8097255 0.380407241 15.8097255"></polygon>
          <path d="M4.9072536,14.4554757 C3.60626078,14.9385929 1.81454261,14.88914 0.380407241,14.729369 L0.380407241,15.7945093 L8.6200284,15.7945093 L8.6200284,10.0465556 L7.83638946,10.0465556 C7.83638946,10.0465556 7.42554962,13.5348902 4.9072536,14.4554757 Z" id="路径" fill="#FFDEAD"></path>
          <path d="M8.79881982,0 C8.58872669,0 8.41841256,0.170314133 8.41841256,0.380407263 L8.41841256,1.08796476 C6.97227639,1.14664793 5.56218516,1.5566189 4.3100142,2.28244353 C4.13026896,2.38820103 4.06918517,2.61896201 4.17306759,2.79979741 L4.38989972,3.18020467 C2.16797458,4.61136912 0.772784981,7.02639272 0.642888247,9.66614836 L0.380407241,9.66614836 C0.170314119,9.66614838 0,9.83646251 0,10.0465556 L0,15.8097255 C0,16.0198187 0.170314119,16.1901328 0.380407241,16.1901328 L3.4236653,16.1901328 C3.90860835,16.612874 4.44092828,16.9779663 5.00996356,17.2780975 L4.82356399,17.6585048 C4.73431416,17.8486409 4.8160546,18.0751301 5.00615948,18.1644465 C7.7218021,19.4255862 10.8873278,19.2504955 13.4473186,17.6975515 C16.0073094,16.1446074 17.625067,13.4180583 17.7612147,10.4269629 L18.4649682,10.4269629 C18.6750613,10.4269629 18.8453754,10.2566487 18.8453754,10.0465556 C18.8390863,4.5006033 14.3447721,0.00628913457 8.79881982,0 L8.79881982,0 Z M8.6200284,9.66614836 L6.52778851,9.66614836 C6.71834738,8.48107116 7.78637812,7.64052207 8.98304527,7.73384084 C10.1797124,7.82715961 11.1045284,8.82311531 11.1090877,10.023407 C11.1136134,11.2236986 10.1963622,12.2266256 9.00043566,12.3289992 L9.00043566,10.0465556 C9.00043566,9.8364625 8.83012153,9.66614836 8.6200284,9.66614836 L8.6200284,9.66614836 Z M8.41841256,1.8449752 L8.41841256,7.00710165 C8.13800477,7.03966959 7.8637974,7.11270614 7.60434102,7.22393378 L5.02517983,2.75414854 C6.07678798,2.20820793 7.23472169,1.89795439 8.41841256,1.8449752 L8.41841256,1.8449752 Z M4.77030699,3.83450514 L6.95004056,7.60814513 C6.29050457,8.10431049 5.86077317,8.84707307 5.75936584,9.66614836 L1.40370277,9.66614836 C1.52998498,7.29610661 2.78103047,5.12904137 4.77030699,3.83450514 Z M0.760814504,10.4269629 L8.23962116,10.4269629 L8.23962116,15.4293183 L0.760814504,15.4293183 L0.760814504,10.4269629 Z M4.65998887,16.1901328 L5.51210113,16.1901328 L5.32950565,16.5705401 C5.09988174,16.4554448 4.87640691,16.3284704 4.65998887,16.1901328 Z M8.79881982,18.2595828 C7.72763505,18.2626673 6.66634678,18.0545462 5.67567624,17.6470926 L6.35280116,16.1901328 L8.63524469,16.1901328 C8.84533782,16.1901328 9.01565196,16.0198187 9.01565196,15.8097255 L9.01565196,13.1012259 C10.475857,13.0045782 11.664228,11.8894848 11.8534901,10.4383751 L17.0004002,10.4383751 C16.7875246,14.8144412 13.1800578,18.2545858 8.79881982,18.2595828 Z M17.4112401,9.66614836 L11.8382738,9.66614836 C11.6606995,8.2756654 10.565906,7.18087192 9.175423,7.00329759 L9.175423,0.760814526 C14.0062827,0.964262988 17.8755684,4.83520218 18.0769528,9.66614836 L17.4112401,9.66614836 Z" id="形状" fill="#3D3D63"></path>
          <path d="M1.86779962,12.5230069 L4.22632459,12.5230069 C4.36225295,12.5230398 4.48787001,12.4505417 4.55584368,12.3328298 C4.62381736,12.2151179 4.62381736,12.0700813 4.55584368,11.9523694 C4.48787001,11.8346575 4.36225295,11.7621595 4.22632459,11.7621923 L1.86779962,11.7621923 C1.65774245,11.7622432 1.48748444,11.9325424 1.48748444,12.1425996 C1.48748444,12.3526568 1.65774245,12.522956 1.86779962,12.5230069 Z M6.10934051,13.3332743 L1.86779962,13.3332743 C1.65774245,13.3333252 1.48748444,13.5036244 1.48748444,13.7136816 C1.48748444,13.9237387 1.65774245,14.094038 1.86779962,14.0940888 L6.10934051,14.0940888 C6.31939767,14.094038 6.48965569,13.9237387 6.48965569,13.7136816 C6.48965569,13.5036244 6.31939767,13.3333252 6.10934051,13.3332743 L6.10934051,13.3332743 Z" id="形状" fill="#3D3D63"></path>
        </g>
      </g>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconGraphPieComponent {

}
