import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'TaskStatisticsIcon',
  template: `
    <svg style="height: 0.75em;" viewBox="0 0 19 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g fill="currentColor" fill-rule="nonzero">
        <path d="M8.96435643,18.7356747 L1.92475246,18.7356747 C1.56831683,18.7356747 1.2831683,18.4547135 1.2831683,18.103512 L1.2831683,2.896488 C1.2831683,2.54528653 1.56831683,2.26432533 1.92475246,2.26432533 L16.0752475,2.26432533 C16.4316831,2.26432533 16.7168317,2.54528653 16.7168317,2.896488 L16.7168317,9.65711646 C16.7168317,10.0083179 17.0019802,10.2892791 17.3584158,10.2892791 C17.7148515,10.2892791 18,10.0083179 18,9.65711646 L18,2.896488 C18,1.84288356 17.1445544,1 16.0752475,1 L1.92475246,1 C0.855445524,1 0,1.84288356 0,2.896488 L0,18.103512 C0,19.1571165 0.855445551,20 1.92475246,20 L8.96435643,20 C9.32079207,20 9.60594059,19.7190388 9.60594059,19.3678373 C9.60594059,19.0166359 9.32079207,18.7356747 8.96435643,18.7356747 Z"></path>
        <path d="M3.07550239,7.23784162 C2.9279523,6.91398548 3.00172735,6.49487756 3.25256249,6.28532358 C3.50339762,6.09481996 3.82800778,6.19007177 3.99031287,6.51392791 L4.53624816,7.63789921 L6.15929902,6.1329207 C6.39537915,5.90431637 6.73474431,5.98051783 6.8970494,6.28532358 C7.0741095,6.59012936 7.01508946,7.02828764 6.77900935,7.23784162 L5.15595849,8.74282012 C4.91987836,8.97142445 4.6100232,9.04762591 4.32967806,8.97142445 C4.03457791,8.895223 3.78374277,8.66661867 3.63619268,8.34276256 L3.07550239,7.23784162"></path>
        <path d="M8.59833796,8 C8.26592799,8 8,7.77777777 8,7.5 C8,7.22222225 8.26592799,7 8.59833796,7 L13.4016621,7 C13.734072,7 14,7.22222223 14,7.5 C14,7.77777779 13.734072,8 13.4016621,8 L8.59833796,8"></path>
        <path d="M4,0.667314599 L4,3.33346291 C4,3.70376129 4.22222223,4 4.5,4 C4.77777775,4 5,3.70376129 5,3.33346291 L5,0.667314599 C5,0.297016227 4.77777777,0.000777512954 4.5,0.000777512954 C4.22222221,-0.0177374014 4,0.297016227 4,0.667314599"></path>
        <path d="M13,0.666666671 L13,3.33333333 C13,3.70370369 13.2222222,4 13.5,4 C13.7777778,4 14,3.70370369 14,3.33333333 L14,0.666666671 C14,0.296296308 13.7777778,0 13.5,0 C13.2222222,0 13,0.296296308 13,0.666666671"></path>
        <path d="M8,0.667314599 L8,3.33346291 C8,3.70376129 8.22222223,4 8.5,4 C8.77777777,4 9,3.70376129 9,3.33346291 L9,0.667314599 C9,0.297016227 8.77777777,0.000777512954 8.5,0.000777512954 C8.22222223,-0.0177374014 8,0.297016227 8,0.667314599"></path>
        <path d="M8.75000001,13 C8.33333335,13 8,12.7777778 8,12.5 C8,12.2222222 8.33333335,12 8.75000001,12 L11.25,12 C11.6666667,12 12,12.2222222 12,12.5 C12,12.7777778 11.6666667,13 11.25,13 L8.75000001,13"></path>
        <path d="M4.99986074,12.6481481 C5.37020532,12.6481481 5.666481,12.3518518 5.666481,11.9814815 C5.666481,11.6111111 5.37020532,11.3148148 4.99986074,11.3148148 C4.62951617,11.3148148 4.33324049,11.6111111 4.33324049,11.9814815 C4.33324049,12.3518518 4.64803339,12.6481481 4.99986074,12.6481481 M4.99986074,14 C3.88882698,14 3,13.1111111 3,12 C3,10.8888889 3.88882701,10 4.99986074,10 C6.11089451,10 6.99972149,10.8888889 6.99972152,12 C7.01823874,13.0925926 6.11089451,14 4.99986074,14 Z"></path>
        <path d="M14,17.0166667 L14,17.6166667 L15.7,17.6166667 C15.7,18.6166667 15,18.8166667 14,18.8166667 C13,18.8166667 12.2,18.0166666 12.2,17.0166667 C12.2,16.0166667 13,15.2166667 14,15.2166666 L14,14.6166667 L13.4,14.6166667 L13.4,17.0166667 C13.4,17.1666667 13.4666667,17.3333333 13.5833333,17.45 C13.7,17.5666667 13.85,17.6333333 14.0166667,17.6333333 L14.0166667,17.0166667 L14.6166667,17.0166667 L14.6166667,14.6166667 C14.6166667,14.4666667 14.55,14.3 14.4333333,14.1833333 C14.3166667,14.0666667 14.1666667,14 14,14 C12.3333333,14 11,15.35 11,17 C11,18.6666667 12.35,20 14,20 C15.6666667,20 17,18.65 17,17 C17,16.8333333 16.9333333,16.6833333 16.8166667,16.5666667 C16.7,16.45 16.55,16.3833333 16.3833333,16.3833333 L13.9833333,16.3833333 L13.9833333,17.0166667 L14.5833333,17.0166667 L14,17.0166667 L14,17.0166667 Z"></path>
        <path d="M15.6605505,12.6635945 L15.6605505,13.327189 C16.7614679,13.327189 17.6422018,14.2119816 17.6422018,15.3179723 L18.3027523,15.3179723 L18.3027523,14.6543779 L16.3211009,14.6543779 L16.3211009,12.6635945 L15.6605505,12.6635945 L15.6605505,13.327189 L15.6605505,12.6635945 L15,12.6635945 L15,15.3179723 C15,15.5023042 15.0733945,15.6682028 15.2018349,15.797235 C15.3302752,15.9262673 15.4954129,16 15.6788991,16 L18.3211009,16 C18.5045872,16 18.6697248,15.9262673 18.7981651,15.797235 C18.9266055,15.6682028 19,15.5023041 19,15.3179723 C19,13.4746544 17.5137615,12 15.6972477,12 C15.5137615,12 15.3486238,12.0737327 15.2201835,12.202765 C15.0917431,12.3317972 15.0183486,12.4976959 15.0183486,12.6820277 L15.6605505,12.6820277 L15.6605505,12.6635945 L15.6605505,12.6635945 Z"></path>
      </g>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconTaskStatisticsComponent {

}
