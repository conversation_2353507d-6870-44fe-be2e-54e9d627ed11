import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'GraphBarStackIcon',
  template: `
    <svg id="icon-graph-bar-stack" viewBox="0 0 20 12">
      <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" fill="currentColor">
        <path d="M18,0 L18,5 L9,5 L9,0 L18,0 Z M22,7 L22,12 L12,12 L12,7 L22,7 Z" fill-opacity="0.5"></path>
        <path d="M12,7 L12,12 L2,12 L2,7 L12,7 Z M9,0 L9,5 L2,5 L2,0 L9,0 Z" fill-opacity="1"></path>
      </g>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconGraphBarStackComponent {

}
