import { ChangeDetectionStrategy, Component, input, numberAttribute } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'UserIcon',
  template: `
    @switch (style()) {
      @case (1) {
        <svg viewBox="0 0 20 21" version="1.1" xmlns="http://www.w3.org/2000/svg">
          <g id="user" transform="translate(0.561721, 0.265625)" fill="currentColor" fill-rule="nonzero">
            <path d="M18.1533665,16.321608 C17.6784921,15.1959799 16.9950751,14.1859296 16.1282409,13.3190955 C15.2614067,12.4522613 14.2513565,11.7713568 13.1257283,11.2939698 C13.1156781,11.2889447 13.1056278,11.2864322 13.0955776,11.281407 C14.6609042,10.1507538 15.6784921,8.30904523 15.6784921,6.23115578 C15.6784921,2.78894472 12.8895474,0 9.44733636,0 C6.0051253,0 3.21618058,2.78894472 3.21618058,6.23115578 C3.21618058,8.30904523 4.23376852,10.1507538 5.79909515,11.2839196 C5.7890449,11.2889447 5.77899465,11.2914573 5.7689444,11.2964824 C4.64331626,11.7713568 3.63326601,12.4522613 2.76643183,13.321608 C1.89959766,14.1884422 1.21869314,15.1984925 0.741306207,16.3241206 C0.273969523,17.4246231 0.0252258045,18.5929648 4.90710707e-05,19.7939698 C-0.00241238646,19.9070352 0.0880398748,20 0.201105201,20 L1.70864289,20 C1.81919565,20 1.90713535,19.9120603 1.90964792,19.8040201 C1.95989917,17.8643216 2.73879364,16.0477387 4.11567807,14.6708543 C5.54030118,13.2462312 7.43226098,12.4623116 9.44733636,12.4623116 C11.4624117,12.4623116 13.3543715,13.2462312 14.7789946,14.6708543 C16.1558791,16.0477387 16.9347735,17.8643216 16.9850248,19.8040201 C16.9875374,19.9145729 17.0754771,20 17.1860298,20 L18.6935675,20 C18.8066328,20 18.8970851,19.9070352 18.8946236,19.7939698 C18.8694469,18.5929648 18.6207032,17.4246231 18.1533665,16.321608 Z M9.44733636,10.5527638 C8.29407003,10.5527638 7.20864289,10.1030151 6.39205998,9.28643216 C5.57547706,8.46984925 5.12572832,7.38442211 5.12572832,6.23115578 C5.12572832,5.07788945 5.57547706,3.99246231 6.39205998,3.1758794 C7.20864289,2.35929648 8.29407003,1.90954774 9.44733636,1.90954774 C10.6006027,1.90954774 11.6860298,2.35929648 12.5026127,3.1758794 C13.3191957,3.99246231 13.7689444,5.07788945 13.7689444,6.23115578 C13.7689444,7.38442211 13.3191957,8.46984925 12.5026127,9.28643216 C11.6860298,10.1030151 10.6006027,10.5527638 9.44733636,10.5527638 Z"></path>
          </g>
        </svg>
      }
      @case (2) {
        <svg viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
          <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g id="user" fill="currentColor" fill-rule="nonzero">
              <path d="M9.99999999,10 C7.23863636,10 4.99999999,7.76136363 4.99999999,5 C4.99999999,2.23863635 7.23863636,0 9.99999999,0 C12.7613636,0 15,2.23863637 15,5 C15,7.76136361 12.7613636,10 9.99999999,10 Z M10,8.18181819 C11.7572697,8.18181819 13.1818182,6.75726966 13.1818182,5 C13.1818182,3.24273034 11.7572697,1.81818181 10,1.81818181 C8.24273034,1.81818181 6.81818181,3.24273034 6.81818181,5 C6.81818181,6.75726966 8.24273034,8.18181819 10,8.18181819 L10,8.18181819 Z M19.4622727,14.62 C19.6213637,14.9663636 19.7331818,15.4654545 19.8304546,16.1281818 C19.8978618,16.5925724 19.9501621,17.0590307 19.9872727,17.5268182 L19.9977273,17.6627273 L20,17.7272727 C20,18.981401 18.9841274,19.998495 17.73,20 L2.26999999,20 C1.01587258,19.998495 0,18.981401 0,17.7272727 L0.00227271307,17.6627273 L0.0127272656,17.5268182 C0.0515624898,17.0591973 0.103856895,16.5927918 0.169545448,16.1281818 C0.266818168,15.4654545 0.37863635,14.9663636 0.537727266,14.62 C1.23636362,13.1031818 6.37954545,10.9090909 10,10.9090909 C13.6204545,10.9090909 18.7636364,13.1031818 19.4622727,14.62 Z M17.7181818,15.3127273 C17.5909091,15.2013636 17.4090909,15.0677273 17.1840909,14.9231818 C16.6804546,14.5990909 16.015,14.2527273 15.28,13.9390909 C13.5181818,13.1872727 11.6286364,12.7272727 10,12.7272727 C8.37136364,12.7272727 6.48227272,13.1872727 4.72,13.9390909 C3.985,14.2527273 3.31954545,14.5990909 2.81590909,14.9231818 C2.59136363,15.0677273 2.40909092,15.2013636 2.28181818,15.3127273 C2.23454544,15.3545455 2.19818182,15.39 2.1740909,15.4168182 C2.11272727,15.5777273 2.03545453,15.9354545 1.96863635,16.3922727 C1.92363635,16.6968182 1.88545453,17.0259091 1.85363636,17.3559091 C1.83727272,17.5259091 1.82545453,17.6631818 1.81863635,17.7518182 C1.83162135,17.9919987 2.0294724,18.1804852 2.26999999,18.1818182 L17.73,18.1818182 C17.9705276,18.1804852 18.1683787,17.9919987 18.1813636,17.7518182 C18.1745455,17.6631818 18.1631818,17.5259091 18.1463636,17.3563636 C18.1158767,17.0341126 18.077532,16.7126533 18.0313636,16.3922727 C17.9645455,15.9354546 17.8872727,15.5777273 17.8259091,15.4168182 C17.7920437,15.3800666 17.7560741,15.3453113 17.7181818,15.3127273 Z" id="形状"></path>
            </g>
          </g>
        </svg>
      }
    }

    
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconUserComponent {

  style = input(1, { transform: numberAttribute });
  
}
