import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'app-icon-Flow, FlowIcon',
  template: `
    <svg viewBox="0 0 20 18" version="1.1" xmlns:xlink="http://www.w3.org/1999/xlink">
      <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g transform="translate(0.202622, 0.502551)" fill="currentColor" fill-rule="nonzero">
          <path d="M6.06723908,3.36748247 L6.06723908,3.46748247 L6.06723908,3.36748247 Z M6.02730687,3.62521809 L6.02730687,3.83999779 C6.0702628,4.14068934 6.11321876,4.44138092 6.24208657,4.74207247 L6.24208657,4.7850284 C6.24208657,4.82798434 6.2850425,4.82798434 6.2850425,4.87094029 L6.2850425,4.82798436 L6.2850425,4.91389622 L6.32799845,5.04276403 C6.62869,5.64414716 7.05824939,6.11666246 7.61667657,6.46030997 C8.17510375,6.80395747 8.81944282,6.97578122 9.46378188,6.97578122 C10.1081209,6.97578122 10.75246,6.76100152 11.3108872,6.41735404 C11.8693144,6.07370653 12.2988738,5.55823528 12.5566094,4.95685217 L12.5995653,4.87094029 L12.5995653,4.82798436 L12.6425213,4.69911654 C12.814345,4.22660122 12.8573009,3.75408593 12.8573009,3.28157061 C12.814345,2.80905529 12.6854772,2.33653999 12.4706975,1.9069806 C12.2559178,1.47742121 11.9122703,1.09081779 11.5686228,0.790126219 C11.1820194,0.446478712 10.75246,0.231699038 10.2799447,0.102831226 C9.80742937,-0.0260365865 9.33491407,-0.0260365865 8.86239875,0.0598752951 C8.38988343,0.145787177 7.91736813,0.31761092 7.53076469,0.618302476 C7.14416125,0.876038101 6.80051375,1.26264154 6.54277812,1.64924498 C6.2850425,2.07880434 6.11321876,2.50836373 6.07026282,2.98087903 L6.07026282,3.45339435 C6.02730687,3.49635028 6.02730687,3.53930623 6.02730687,3.62521809 Z M8.26101564,2.25062811 C8.56170719,1.9069806 8.99126656,1.73515686 9.46378188,1.73515686 C9.9362972,1.73515686 10.3658566,1.9069806 10.6665481,2.25062811 C11.0101956,2.55131966 11.1820194,2.98087905 11.1820194,3.45339435 C11.1820194,3.92590967 11.0101956,4.35546904 10.6665481,4.65616059 C10.3658566,4.9998081 9.93629718,5.17163184 9.46378188,5.17163184 C8.99126658,5.17163184 8.56170719,4.9998081 8.26101564,4.65616059 C7.91736813,4.35546904 7.74554439,3.92590965 7.74554439,3.45339435 C7.74554439,2.98087903 7.91736813,2.55131966 8.26101564,2.25062811 L8.26101564,2.25062811 Z M16.6803794,8.35037122 L16.6374234,8.22150341 C16.2937759,6.37439811 15.2628334,4.69911654 13.7164197,3.58226216 L13.7164197,3.49635028 L13.7164197,3.58226216 C13.7164197,4.22660122 13.5445959,4.87094029 13.2439044,5.42936747 C14.0600672,6.20257434 14.6184944,7.14760496 14.87623,8.26445934 C15.5205691,8.13559153 16.1219522,8.17854746 16.6803794,8.35037122 Z M4.00837783,8.22150341 C4.26611345,7.14760498 4.82454063,6.15961841 5.64070344,5.42936747 C5.34001188,4.87094029 5.21114407,4.22660122 5.16818814,3.58226216 L5.03932032,3.66817404 L4.91045251,3.75408591 C3.44995064,4.91389622 2.50492001,6.54622185 2.24718438,8.35037122 C2.80561156,8.17854748 3.40699469,8.13559153 4.00837783,8.22150341 L4.00837783,8.22150341 Z M2.34718438,9.29237811 L2.24718438,9.29237811 L2.34718438,9.29237811 Z M2.30422845,9.29237811 L2.20422845,9.29237811 L2.30422845,9.29237811 Z M3.54995062,9.07759842 L3.44995062,9.07759842 L3.54995062,9.07759842 L3.54995062,9.07759842 Z M11.7404466,14.5789822 C11.0531516,14.8796737 10.2799447,15.0514975 9.46378188,15.0514975 C8.690575,15.0514975 7.91736813,14.8796737 7.18711719,14.5789822 C6.88642563,15.0944534 6.49982219,15.5669687 6.02730687,15.9106162 C7.05824937,16.4690434 8.21805969,16.769735 9.46378188,16.769735 C10.6665481,16.769735 11.8263584,16.4690434 12.9002569,15.9106162 C12.4277416,15.5669687 12.0411381,15.0944534 11.7404466,14.5789822 L11.7404466,14.5789822 Z M6.02730687,14.7508059 L6.11321876,14.664894 L6.15617469,14.6219381 C6.24208657,14.5360262 6.2850425,14.4071584 6.37095438,14.3212465 L6.41391031,14.2782906 L6.45686626,14.2353347 L6.41391031,14.2782906 L6.45686626,14.2353347 L6.45686626,14.1923787 C6.71460189,13.7198634 6.88642563,13.1614362 6.88642563,12.603009 C6.88642563,12.0445819 6.75755782,11.4861547 6.54277812,10.9706834 C6.2850425,10.4552122 5.89843906,10.0256528 5.46887969,9.72496123 C4.99636437,9.38131372 4.48089312,9.20948998 3.92246594,9.1235781 L3.62177439,9.1235781 L3.44995062,9.03766622 C3.32108281,9.03766622 3.14925907,9.03766622 3.02039126,9.08062217 C2.93447937,9.08062217 2.80561156,9.1235781 2.7196997,9.1235781 C2.67674377,9.1235781 2.63378782,9.1235781 2.59083189,9.16653403 C2.50492001,9.20948996 2.41900814,9.20948996 2.33309626,9.25244591 L2.29014031,9.25244591 L2.1612725,9.29540184 C1.68875718,9.46722558 1.25919781,9.76791716 0.915550326,10.1545206 C0.57190282,10.541124 0.314167195,11.0136394 0.142343452,11.4861547 C-0.0294802916,11.95867 -0.0294802916,12.5170972 0.0564315699,13.0325684 C0.142343452,13.5480397 0.357123126,14.020555 0.657814701,14.4501143 C0.958506277,14.8796737 1.34510969,15.2233212 1.81762501,15.4810568 C2.29014033,15.7387925 2.76265563,15.8676603 3.27812688,15.9106162 C3.79359813,15.9535722 4.30906938,15.8676603 4.7815847,15.6528806 C5.25410002,15.4381009 5.68365939,15.1374094 6.02730687,14.7508059 Z M4.65271689,13.6769075 C4.35202533,14.020555 3.92246594,14.1923787 3.44995062,14.1923787 C2.97743531,14.1923787 2.54787594,14.020555 2.24718438,13.6769075 C1.90353687,13.3762159 1.73171313,12.9466565 1.73171313,12.4741412 C1.73171313,12.0016259 1.90353687,11.5720665 2.24718438,11.271375 C2.54787594,10.9277275 2.97743533,10.7559037 3.44995062,10.7559037 C3.92246594,10.7559037 4.35202531,10.9277275 4.65271689,11.271375 C4.99636439,11.6150225 5.16818814,12.0445819 5.16818814,12.4741412 C5.16818814,12.9466565 4.99636439,13.3762159 4.65271689,13.6769075 Z M15.5776131,9.07759842 L15.4776131,9.07759842 L15.5776131,9.07759842 Z M18.6563525,11.1425072 C18.4845288,10.7129478 18.1838372,10.2833884 17.8401897,9.98269685 C17.4965422,9.63904935 17.0669828,9.38131372 16.6374234,9.25244591 C16.5515116,9.20948998 16.4655997,9.20948998 16.3796878,9.16653403 C16.1219522,9.08062215 15.9071725,9.03766622 15.6494369,9.03766622 L15.0910097,9.03766622 C14.5325825,9.1235781 13.9741553,9.33835777 13.50164,9.68200528 C13.0291247,10.0256528 12.6854772,10.4552122 12.4277416,10.9706834 C12.1700059,11.4861547 12.0840941,12.0445818 12.0840941,12.645965 C12.12705,13.2043921 12.2988738,13.7628193 12.5995653,14.2782906 C12.6854772,14.4071584 12.7713891,14.4930703 12.8573009,14.6219381 L12.9002569,14.664894 L12.9861687,14.7508059 C13.0291247,14.8367178 13.1150366,14.8796737 13.2009484,14.9655856 C13.2868603,15.0085415 13.3298163,15.0944534 13.4157281,15.1374094 C13.50164,15.1803653 13.5445959,15.2233212 13.6305078,15.3092331 L13.6734638,15.352189 L13.7593756,15.395145 L13.7164197,15.395145 L13.8023316,15.395145 C14.2318909,15.6528806 14.6614503,15.7817484 15.1339656,15.8676603 C15.6064809,15.9106162 16.0789962,15.8676603 16.5515116,15.7387925 C17.0240269,15.6099247 17.4535863,15.352189 17.7972338,15.0514975 C18.1408813,14.7508059 18.4415728,14.3642025 18.6563525,13.9346431 C18.8711322,13.5050837 18.957044,13.0325684 19,12.5600531 C18.9140881,12.0875378 18.8281763,11.6150225 18.6563525,11.1425072 L18.6563525,11.1425072 Z M16.6803794,13.6769075 C16.3796878,14.020555 15.9501284,14.1923787 15.4776131,14.1923787 C15.0050978,14.1923787 14.5755384,14.020555 14.2748469,13.6769075 C13.9311994,13.3762159 13.7593756,12.9466565 13.7593756,12.4741412 C13.7593756,12.0016259 13.9311994,11.5720665 14.2748469,11.271375 C14.6184944,10.9277275 15.0480537,10.7559037 15.4776131,10.7559037 C15.9071725,10.7559037 16.3796878,10.9277275 16.6803794,11.271375 C17.0240269,11.6150225 17.1958506,12.0445819 17.1958506,12.4741412 C17.1958506,12.9466565 17.0240269,13.3762159 16.6803794,13.6769075 Z"></path>
          <polygon points="15.0980538 9.03766622 15.1480537 9.03766622 15.0480537 9.13766622"></polygon>
        </g>
      </g>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconFlowComponent {

}
