import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'FluctuateIcon',
  template: `
    <svg style="height: 0.8em;" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g transform="translate(0.333333, 0.333333)" fill-rule="nonzero">
          <path d="M2.0250468e-15,3.5625 C2.0250468e-15,2.93261084 0.250222484,2.32852029 0.695621383,1.88312139 C1.14102028,1.43772248 1.74511083,1.18749998 2.37499999,1.18749998 L16.625,1.18749998 C17.2548892,1.18749998 17.8589797,1.43772248 18.3043786,1.88312139 C18.7497775,2.32852029 19,2.93261084 19,3.5625 L19,11.875 L2.0250468e-15,11.875 L2.0250468e-15,3.5625 Z" id="路径" fill="#F1F4FE"></path>
          <path d="M2.37499999,0 C1.06332372,0 0,1.06332372 0,2.37499999 L0,11.875 L19,11.875 L19,2.37499999 C19,1.06332372 17.9366763,0 16.625,0 L2.37499999,0 L2.37499999,0 Z M15.264125,5.17037499 L12.5411875,7.89212499 C12.2071056,8.22642109 11.7538647,8.4142447 11.28125,8.4142447 C10.8086353,8.4142447 10.3553944,8.22642109 10.0213125,7.89212499 L8.139125,6.00993751 C8.02773244,5.89826298 7.87648237,5.83550113 7.71875,5.83550113 C7.56101763,5.83550113 7.40976756,5.89826298 7.298375,6.00993751 L4.576625,8.73287499 C4.34445829,8.96504167 3.96804171,8.96504166 3.73587502,8.73287497 C3.50370832,8.50070827 3.50370831,8.12429169 3.73587499,7.89212499 L6.45999999,5.17037499 C6.79408192,4.83607888 7.24732282,4.64825526 7.71993749,4.64825526 C8.19255216,4.64825526 8.64579307,4.83607888 8.979875,5.17037499 L10.8620625,7.0525625 C10.9734551,7.16423702 11.1247051,7.22699887 11.2824375,7.22699887 C11.4401699,7.22699887 11.59142,7.16423702 11.7028125,7.0525625 L14.4245625,4.32962499 C14.6567292,4.0974583 15.0331458,4.09745831 15.2653125,4.329625 C15.4974792,4.5617917 15.4974792,4.93820828 15.2653125,5.17037499 L15.264125,5.17037499 Z" id="形状" fill="#4370FE"></path>
          <path d="M19,11.875 L0,11.875 L0,13.0625 C0,13.6923892 0.250222495,14.2964797 0.695621393,14.7418786 C1.14102029,15.1872775 1.74511083,15.4375 2.37499999,15.4375 L8.31249999,15.4375 L8.139125,15.96 C7.87001811,16.7662791 7.29165145,17.4326395 6.53124999,17.8125 L5.34375001,17.8125 C5.01583094,17.8125 4.75000001,18.0783309 4.75000001,18.40625 C4.75000001,18.7341691 5.01583094,19 5.34375001,19 L13.65625,19 C13.9841691,19 14.25,18.7341691 14.25,18.40625 C14.25,18.0783309 13.9841691,17.8125 13.65625,17.8125 L12.46875,17.8125 C11.7083485,17.4326395 11.1299819,16.7662791 10.860875,15.96 L10.6875,15.4375 L16.625,15.4375 C17.2548892,15.4375 17.8589797,15.1872775 18.3043786,14.7418786 C18.7497775,14.2964797 19,13.6923892 19,13.0625 L19,11.875 L19,11.875 Z" id="路径" fill="#A5BBFE"></path>
        </g>
      </g>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconFluctuateComponent {

}
