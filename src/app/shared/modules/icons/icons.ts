import { IconArrowRightComponent } from "./icon-arrow-right.component";
import { IconBackTopComponent } from "./icon-back-top.component";
import { IconCalendarComponent } from "./icon-calendar.component";
import { IconCaliberComponent } from "./icon-caliber.component";
import { IconCardComponent } from "./icon-card.component";
import { IconChart3DComponent } from "./icon-chart-3d.component";
import { IconChartBarComponent } from "./icon-chart-bar.component";
import { IconChartLineComponent } from "./icon-chart-line.component";
import { IconChevronDownComponent } from "./icon-chevron-down.component";
import { IconChevronLeftComponent } from "./icon-chevron-left.component";
import { IconChevronRightComponent } from "./icon-chevron-right.component";
import { IconCityComponent } from "./icon-city.component";
import { IconCloseFillComponent } from "./icon-close-fill.component";
import { IconCloseComponent } from "./icon-close.component";
import { IconCollectComponent } from "./icon-collect.component";
import { IconCooperationComponent } from "./icon-cooperation.component";
import { IconCopyComponent } from "./icon-copy.component";
import { IconCouponComponent } from "./icon-coupon.component";
import { IconDataCompareComponent } from "./icon-data-compare.component";
import { IconDataTrendComponent } from "./icon-data-trend.component";
import { IconDeleteComponent } from "./icon-delete.component";
import { IconDownloadComponent } from "./icon-download.component";
import { IconEditContainerComponent } from "./icon-edit-container.component";
import { IconEditComponent } from "./icon-edit.component";
import { IconExpandComponent } from "./icon-expand.component";
import { IconFastArrowRightComponent } from "./icon-fast-arrow-right.component";
import { IconFengKongComponent } from "./icon-fengkong.component";
import { IconFileEditComponent } from "./icon-file-edit.component";
import { IconFilePlusComponent } from "./icon-file-plus.component";
import { IconFileTimeComponent } from "./icon-file-time.component";
import { IconFlowComponent } from "./icon-flow.component";
import { IconFluctuateComponent } from "./icon-fluctuate.component";
import { IconFolderSharedComponent } from "./icon-folder-shared.component";
import { IconFolderComponent } from "./icon-folder.component";
import { IconFullscreenExitComponent } from "./icon-fullscreen-exit.component";
import { IconFullscreenComponent } from "./icon-fullscreen.component";
import { IconGraphBarStackComponent } from "./icon-graph-bar-stack.component";
import { IconGraphBarComponent } from "./icon-graph-bar.component";
import { IconGraphColumnStackComponent } from "./icon-graph-column-stack.component";
import { IconGraphColumnComponent } from "./icon-graph-column.component";
import { IconGraphLineComponent } from "./icon-graph-line.component";
import { IconGraphPieComponent } from "./icon-graph-pie.component";
import { IconHelpFillComponent } from "./icon-help-fill.component";
import { IconHomeComponent } from "./icon-home.component";
import { IconKefuComponent } from "./icon-kefu.component";
import { IconLabComponent } from "./icon-lab.component";
import { IconLoadingComponent } from "./icon-loading.component";
import { IconLogoComponent } from "./icon-logo.component";
import { IconMapArrowRightComponent } from "./icon-map-arrow-right.component";
import { IconMapComponent } from "./icon-map.component";
import { IconMarketingComponent } from "./icon-marketing.component";
import { IconMaximizeSquareComponent } from "./icon-maximize-square.component";
import { IconMinusCircleComponent } from "./icon-minus-circle.component";
import { IconMoreSquareComponent } from "./icon-more-square.component";
import { IconMoreComponent } from "./icon-more.component";
import { IconOkrComponent } from "./icon-okr.component";
import { IconOpenComponent } from "./icon-open.component";
import { IconOrderComponent } from "./icon-order.component";
import { IconPlayComponent } from "./icon-play.component";
import { IconPlusCircleComponent } from "./icon-plus-circle.component";
import { IconPlusSquareFillComponent } from "./icon-plus-square-fill.component";
import { IconRelationComponent } from "./icon-relation.component";
import { IconReportAnalyseComponent } from "./icon-report-analyse.component";
import { IconReportAnnualComponent } from "./icon-report-annual.component";
import { IconReportDataComponent } from "./icon-report-data.component";
import { IconReportComponent } from "./icon-report.component";
import { IconSafeComponent } from "./icon-safe.component";
import { IconSearchComponent } from "./icon-search.component";
import { IconSettingComponent } from "./icon-setting.component";
import { IconSortDownComponent } from "./icon-sort-down.component";
import { IconSortUpComponent } from "./icon-sort-up.component";
import { IconSquareDottedPlusComponent } from "./icon-square-dotted-plus.component";
import { IconSuccessFillComponent } from "./icon-success-fill.component";
import { IconSwapComponent } from "./icon-swap.component";
import { IconTagComponent } from "./icon-tag.component";
import { IconTaskStatisticsComponent } from "./icon-task-statistics.component";
import { IconTaxiComponent } from "./icon-taxi.component";
import { IconTodoListComponent } from "./icon-todo-list.component";
import { IconTransferComponent } from "./icon-transfer.component";
import { IconTrendingDownComponent } from "./icon-trending-down.component";
import { IconTrendingUpComponent } from "./icon-trending-up.component";
import { IconUserFillComponent } from "./icon-user-fill.component";
import { IconUserGroupFillComponent } from "./icon-user-group-fill.component";
import { IconUserGroupComponent } from "./icon-user-group.component";
import { IconUserComponent } from "./icon-user.component";
import { IconUsersComponent } from "./icon-users.component";
import { IconInfoFillComponent } from './icon-info-fill.component';
import { IconInfoComponent } from "./icon-info.component";

export default [
  {
    type: 'Colors',
    children: [
      {
        name: 'Chart3D',
        component: IconChart3DComponent,
      },
      {
        name: 'DataCompare',
        component: IconDataCompareComponent,
      },
      {
        name: 'DataTrend',
        component: IconDataTrendComponent,
      },
      {
        name: 'GraphBar',
        component: IconGraphBarComponent,
      },
      {
        name: 'ChartLine',
        component: IconChartLineComponent,
      },
      {
        name: 'GraphPie',
        component: IconGraphPieComponent,
      },
      {
        name: 'Lab',
        component: IconLabComponent,
      },
      {
        name: 'TodoList',
        component: IconTodoListComponent,
      },
      {
        name: 'ChartBar',
        component: IconChartBarComponent,
      },
      {
        name: 'ReportAnalyse',
        component: IconReportAnalyseComponent,
      },
      {
        name: 'ReportAnnual',
        component: IconReportAnnualComponent,
      },
      {
        name: 'ReportData',
        component: IconReportDataComponent,
      },
      {
        name: 'Map',
        component: IconMapComponent,
      },

      {
        name: 'FileTime',
        component: IconFileTimeComponent,
      },
      {
        name: 'Kefu',
        component: IconKefuComponent,
      },

      {
        name: 'Caliber',
        component: IconCaliberComponent,
      },
      {
        name: 'City',
        component: IconCityComponent,
      },

    ],
  },

  
  
  {
    type: 'Colors Fill',
    children: [
      {
        name: 'Order',
        component: IconOrderComponent,
      },
      {
        name: 'Card',
        component: IconCardComponent,
      },
      {
        name: 'Collect',
        component: IconCollectComponent,
      },
      {
        name: 'Cooperation',
        component: IconCooperationComponent,
      },
      {
        name: 'Taxi',
        component: IconTaxiComponent,
      },
      {
        name: 'Users',
        component: IconUsersComponent,
      },
      {
        name: 'Coupon',
        component: IconCouponComponent,
      },
      {
        name: 'FolderShared',
        component: IconFolderSharedComponent,
      },
      {
        name: 'Folder',
        component: IconFolderComponent,
      },
      {
        name: 'Report',
        component: IconReportComponent,
      },
      {
        name: 'Tag',
        component: IconTagComponent,
      },
      {
        name: 'Safe',
        component: IconSafeComponent,
      },
      {
        name: 'FengKong',
        component: IconFengKongComponent,
      },
      {
        name: 'Fluctuate',
        component: IconFluctuateComponent,
      },
    ]
  },




  {
    type: 'Arrows',
    children: [
      {
        name: 'ArrowRight',
        component: IconArrowRightComponent,
      },
      {
        name: 'BackTop',
        component: IconBackTopComponent,
      },
      {
        name: 'ChevronDown',
        component: IconChevronDownComponent,
      },
      {
        name: 'ChevronLeft',
        component: IconChevronLeftComponent,
      },
      {
        name: 'ChevronRight',
        component: IconChevronRightComponent,
      },
      {
        name: 'Download',
        component: IconDownloadComponent,
      },
      {
        name: 'Expand',
        component: IconExpandComponent,
      },
      {
        name: 'Fullscreen',
        component: IconFullscreenComponent,
      },
      {
        name: 'FullscreenExit',
        component: IconFullscreenExitComponent,
      },
      {
        name: 'FastArrowRight',
        component: IconFastArrowRightComponent,
      },
      {
        name: 'MapArrowRight',
        component: IconMapArrowRightComponent,
      },
      {
        name: 'SortDown',
        component: IconSortDownComponent,
      },
      {
        name: 'SortUp',
        component: IconSortUpComponent,
      },
      {
        name: 'TrendingDown',
        component: IconTrendingDownComponent,
      },
      {
        name: 'TrendingUp',
        component: IconTrendingUpComponent,
      },
      {
        name: 'Swap',
        component: IconSwapComponent,
      },
      {
        name: 'Transfer',
        component: IconTransferComponent,
      },

    ]
  },




  {
    type: 'Actions',
    children: [
      {
        name: 'Copy',
        component: IconCopyComponent,
      },
      {
        name: 'Delete',
        component: IconDeleteComponent,
      },
      {
        name: 'EditContainer',
        component: IconEditContainerComponent,
      },
      {
        name: 'Edit',
        component: IconEditComponent,
      },
      {
        name: 'FileEdit',
        component: IconFileEditComponent,
      },
      {
        name: 'FilePlus',
        component: IconFilePlusComponent,
      },
      {
        name: 'Search',
        component: IconSearchComponent,
      },
      {
        name: 'Setting',
        component: IconSettingComponent,
      },
      {
        name: 'SquareDottedPlus',
        component: IconSquareDottedPlusComponent,
      },
      {
        name: 'PlusSquareFill',
        component: IconPlusSquareFillComponent,
      },
      {
        name: 'Open',
        component: IconOpenComponent,
      },
      {
        name: 'Play',
        component: IconPlayComponent,
      },
      {
        name: 'Close',
        component: IconCloseComponent,
      },
    ]
  },



  {
    type: 'Graph',
    children: [
      {
        name: 'GraphBarStack',
        component: IconGraphBarStackComponent,
      },
      {
        name: 'GraphColumnStack',
        component: IconGraphColumnStackComponent,
      },
      {
        name: 'GraphColumn',
        component: IconGraphColumnComponent,
      },
      {
        name: 'GraphLine',
        component: IconGraphLineComponent,
      },
    ]
  },



  {
    type: 'Circle',
    children: [
      {
        name: 'PlusCircle',
        component: IconPlusCircleComponent,
      },
      {
        name: 'MinusCircle',
        component: IconMinusCircleComponent,
      },
      {
        name: 'CloseFill',
        component: IconCloseFillComponent,
      },
      {
        name: 'SuccessFill',
        component: IconSuccessFillComponent,
      },
      {
        name: 'HelpFill',
        component: IconHelpFillComponent,
      },
      {
        name: 'InfoFill',
        component: IconInfoFillComponent,
      },
      {
        name: 'Info',
        component: IconInfoComponent,
      },
      {
        name: 'Loading',
        component: IconLoadingComponent,
      },
      {
        name: 'Logo',
        component: IconLogoComponent,
      },
      {
        name: 'Okr',
        component: IconOkrComponent,
      },
    ]
  },



  {
    type: 'Others',
    children: [
      {
        name: 'Calendar',
        component: IconCalendarComponent,
      },
      {
        name: 'Flow',
        component: IconFlowComponent,
      },
      {
        name: 'Home',
        component: IconHomeComponent,
      },
      {
        name: 'Marketing',
        component: IconMarketingComponent,
      },
      {
        name: 'MaximizeSquare',
        component: IconMaximizeSquareComponent,
      },
      {
        name: 'MoreSquare',
        component: IconMoreSquareComponent,
      },
      {
        name: 'More',
        component: IconMoreComponent,
      },
      {
        name: 'Relation',
        component: IconRelationComponent,
      },
      {
        name: 'TaskStatistics',
        component: IconTaskStatisticsComponent,
      },
      {
        name: 'UserGroup',
        component: IconUserGroupComponent,
      },
      {
        name: 'User',
        component: IconUserComponent,
      },
      {
        name: 'UserFill',
        component: IconUserFillComponent,
      },
      {
        name: 'UserGroupFill',
        component: IconUserGroupFillComponent,
      },
    ]
  }
];
