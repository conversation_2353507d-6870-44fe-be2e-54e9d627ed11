import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'ChartLineIcon',
  template: `
    <svg viewBox="0 0 20 16" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="折线图" transform="translate(0.266583, 0.815763)" fill-rule="nonzero">
          <path d="M0.36137014,14.4944659 L3.88748859,14.4944659 L3.88748859,4.97736951 L0.365173921,7.05043807 L0.36137014,14.4944659 L0.36137014,14.4944659 Z M18.7679367,0.0286315276 C18.6255339,-0.0299694982 18.4618481,0.00306800397 18.3533229,0.112315036 L15.7743496,2.6874846 C15.7031477,2.75926939 15.663455,2.85644807 15.6640332,2.95755409 L15.6640332,14.4944659 L18.9999843,14.4944659 L18.9999843,0.382384511 C19.0013802,0.22836449 18.9097693,0.0886953953 18.7679367,0.0286315276 L18.7679367,0.0286315276 Z" id="形状" fill="#FFF6E6"></path>
          <polygon id="路径" fill="#96DDFF" points="16.0444191 3.24664254 16.0444191 14.4944659 9.96595382 14.4944659 9.96595382 4.62361653 13.0051864 5.9967867 15.7553306 3.24664254 16.0444191 3.24664254"></polygon>
          <polygon id="路径" fill="#C7ECFF" points="15.7553306 3.24664254 13.0051864 5.9967867 9.96595382 4.62361653 9.96595382 5.38437563 13.0051864 6.75754582 15.7553306 4.00740166 16.0444191 4.00740166 16.0444191 3.24664254 15.7553306 3.24664254"></polygon>
          <polygon id="路径" fill="#69BAF9" points="13.0051864 11.9877646 9.96595382 10.6145945 9.96595382 14.4944659 16.0444191 14.4944659 16.0444191 9.23762048 15.7553306 9.23762048 13.0051864 11.9877646"></polygon>
          <polygon id="路径" fill="#F6716F" points="9.96595382 4.62361653 9.96595382 14.4944659 3.88748859 14.4944659 3.88748859 4.97736951 6.73272762 3.15915525 9.96595382 4.62361653"></polygon>
          <polygon id="路径" fill="#F6A2A2" points="6.73272762 3.15915525 3.88748859 4.97736951 3.88748859 5.73812861 6.73272762 3.91991434 9.96595382 5.38437563 9.96595382 4.62361653 6.73272762 3.15915525"></polygon>
          <polygon id="路径" fill="#F0504D" points="3.88748859 11.2954739 3.88748859 14.4944659 9.96595382 14.4944659 9.96595382 10.9417209 6.73272762 9.4772596 3.88748859 11.2954739"></polygon>
          <path d="M5.39759539,3.15915525 C5.39759539,3.89652842 5.99535445,4.49428748 6.73272762,4.49428748 C7.47010079,4.49428748 8.06785985,3.89652842 8.06785985,3.15915525 C8.06785985,2.42178208 7.47010079,1.82402302 6.73272762,1.82402302 C5.99535445,1.82402302 5.39759539,2.42178208 5.39759539,3.15915525 L5.39759539,3.15915525 Z" id="路径" fill="#EDF4FF"></path>
          <path d="M11.6700542,6.01580569 C11.6700542,6.75317886 12.2678133,7.35093792 13.0051864,7.35093792 C13.7425596,7.35093792 14.3403187,6.75317886 14.3403187,6.01580569 C14.3403187,5.27843252 13.7425596,4.68067346 13.0051864,4.68067346 C12.2678133,4.68067346 11.6700542,5.27843252 11.6700542,6.01580569 L11.6700542,6.01580569 Z" id="路径" fill="#EDF4FF"></path>
          <path d="M18.6195886,2.87006678 C18.8296665,2.87006677 18.9999682,2.69976506 18.9999682,2.48968724 L18.9999682,0.382384511 C18.9999682,0.172306699 18.8296665,0.00200496257 18.6195886,0.00200496257 L16.5122859,0.00200496257 C16.3763675,0.00200496257 16.2507595,0.0744648094 16.1827908,0.192168163 C16.1148221,0.309871517 16.1148221,0.454897527 16.1827908,0.572600881 C16.2507595,0.690304235 16.3763675,0.762796971 16.5122859,0.76276407 L17.7028739,0.76276407 L15.7743496,2.6950922 L13.9295088,4.5627558 C13.1783372,4.07803699 12.1799176,4.25941656 11.6472314,4.97736951 L10.148536,4.28127493 C10.1267703,4.26890293 10.1038363,4.25871004 10.0800677,4.25084456 L8.41400525,3.52051582 C8.57106292,2.80847192 8.26189747,2.07480013 7.64259728,1.68990936 C7.0232971,1.3050186 6.22857021,1.35262897 5.65965044,1.80870342 C5.09073066,2.26477787 4.87136745,3.03011267 5.11231073,3.71831319 L0.741749677,6.38097006 L0.741749677,2.26526329 L0.912920475,2.43263031 C1.00588138,2.54118161 1.15184388,2.58846502 1.29079678,2.55504039 C1.42974968,2.52161576 1.53824109,2.41312434 1.57166572,2.27417145 C1.60509036,2.13521855 1.55780694,1.98925605 1.44925564,1.89629514 L0.616224425,1.07847909 C0.514411193,0.982864453 0.368385817,0.950738875 0.235844888,0.994795605 C0.0925201195,1.05367355 -0.000766475957,1.19360344 4.74572867e-06,1.34854859 L4.74572867e-06,14.4944659 C4.74572867e-06,14.7045437 0.170311289,14.8748454 0.38038911,14.8748454 L18.6386076,14.8748454 C18.7869836,14.8628049 18.9146999,14.7653172 18.9654416,14.6253684 C19.0161833,14.4854195 18.9806239,14.3287324 18.8744429,14.2243964 L18.0718421,13.4103842 C17.9753556,13.3138976 17.8347234,13.2762154 17.7029203,13.3115319 C17.5711173,13.3468484 17.4681674,13.4497983 17.4328508,13.5816014 C17.3975343,13.7134044 17.4352166,13.8540366 17.5317031,13.9505231 L17.7028739,14.1178901 L16.4247986,14.1178901 L16.4247986,3.14013628 L18.2392091,1.32192201 L18.2392091,2.50870621 C18.2493524,2.71132179 18.4167195,2.87032052 18.6195886,2.87006678 Z M13.0051864,5.04203403 C13.5345826,5.04203403 13.9637429,5.47119438 13.9637429,6.0005905 C13.9637429,6.52998662 13.5345826,6.95914697 13.0051864,6.95914697 C12.4757903,6.95914697 12.04663,6.52998662 12.04663,6.0005905 C12.04663,5.47119438 12.4757903,5.04203403 13.0051864,5.04203403 Z M6.73272762,2.18918739 C7.26002296,2.18918739 7.68748029,2.61664472 7.68748029,3.14394006 C7.68748029,3.6712354 7.26002296,4.09869273 6.73272762,4.09869273 C6.20543228,4.09869273 5.77797495,3.6712354 5.77797495,3.14394006 C5.78628428,2.62259613 6.21131748,2.20433637 6.73272762,2.20440258 L6.73272762,2.18918739 Z M6.35234808,4.8138063 L6.35234808,6.21740685 C6.35239893,6.42744871 6.52268576,6.59769433 6.73272763,6.59769433 C6.94276951,6.59769433 7.11305634,6.42744871 7.11310718,6.21740685 L7.11310718,4.82902149 C7.50296592,4.73978155 7.84901668,4.51626238 8.09068262,4.19759143 L9.58177048,4.87466704 L9.58177048,14.1140863 L7.11310718,14.1140863 L7.11310718,12.8436186 C7.11305634,12.6335768 6.94276951,12.4633312 6.73272763,12.4633312 C6.52268576,12.4633312 6.35239893,12.6335768 6.35234808,12.8436186 L6.35234808,14.1140863 L4.26786812,14.1140863 L4.26786812,5.11050235 L5.50410168,4.34974325 C5.7343188,4.58847253 6.0290622,4.75500916 6.35234808,4.82902149 L6.35234808,4.8138063 Z M0.741749677,7.26725441 L3.50710903,5.5783692 L3.50710903,14.1140863 L0.741749677,14.1140863 L0.741749677,7.26725441 Z M15.6640395,14.1140863 L13.3817622,14.1140863 L13.3817622,12.8436186 C13.3817622,12.6335408 13.2114605,12.4632391 13.0013826,12.4632391 C12.7913048,12.4632391 12.6210031,12.6335408 12.6210031,12.8436186 L12.6210031,14.1140863 L10.3387258,14.1140863 L10.3387258,5.22081241 L11.320105,5.66585649 C11.2957706,5.78094319 11.2830277,5.89817836 11.2820671,6.01580569 C11.2767481,6.8244967 11.8357308,7.52757976 12.6248069,7.7046909 L12.6248069,9.10829145 C12.6248577,9.31833332 12.7951446,9.48857893 13.0051864,9.48857893 C13.2152283,9.48857893 13.3855151,9.31833332 13.385566,9.10829145 L13.385566,7.7046909 C13.9290464,7.58278092 14.3789387,7.2035149 14.5910108,6.68848244 C14.803083,6.17344998 14.7506901,5.58736066 14.4506287,5.11810993 L15.6640395,3.90089537 L15.6640395,14.1140863 Z" id="形状" fill="#3D3D63"></path>
          <path d="M6.73272762,11.0216006 C6.94280545,11.0216006 7.11310718,10.8512989 7.11310718,10.641221 L7.11310718,8.43501964 C7.11305634,8.22497778 6.94276951,8.05473216 6.73272763,8.05473216 C6.52268576,8.05473216 6.35239893,8.22497778 6.35234808,8.43501964 L6.35234808,10.641221 C6.35234808,10.8512989 6.5226498,11.0216006 6.73272762,11.0216006 L6.73272762,11.0216006 Z" id="路径" fill="#3D3D63"></path>
        </g>
      </g>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconChartLineComponent {

}
