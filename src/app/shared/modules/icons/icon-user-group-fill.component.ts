import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'UserGroupFillIcon',
  template: `
    <svg style="height: 0.9em;" viewBox="0 0 20 16" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g transform="translate(0.428695, 0.166667)" fill="currentColor" fill-rule="nonzero">
          <path d="M12.9533763,6.53610216 C12.8905402,6.49179763 12.8503193,6.42214356 12.8433585,6.34557455 C12.8363977,6.26900555 12.8633986,6.19324041 12.9172152,6.13833108 C13.6322306,5.31215509 14.0268219,4.25670349 14.0291662,3.16408818 C14.029831,2.83559539 13.99344,2.50807582 13.9206832,2.18774097 C13.9121808,2.15509157 13.906137,2.12185089 13.9026027,2.08829819 C13.9024448,1.94332095 14.0163345,1.82385616 14.1611539,1.81709063 C14.1701942,1.81709063 14.2822933,1.81076246 14.3274946,1.81076246 C15.1605151,1.81263802 15.9408483,2.21854969 16.4206034,2.89955035 C16.9003585,3.580551 17.0198959,4.45198372 16.7412418,5.23701795 C16.4698488,6.02766517 15.8223395,6.63110311 15.0145537,6.84618281 C14.3144213,7.0366138 13.5664912,6.92409624 12.9533763,6.53610216 L12.9533763,6.53610216 Z M15.5298481,7.83789844 C15.455715,7.83300691 15.38297,7.85968953 15.329582,7.91135534 C15.276194,7.96302115 15.2471413,8.03485246 15.2496003,8.109106 C15.2604895,8.18437259 15.2953633,8.25412017 15.349043,8.30799155 C16.3995236,9.45593046 16.9662729,10.9651028 16.9310871,12.520749 L16.9310871,13.3253314 C16.9345373,13.457222 17.043247,13.5619055 17.1751739,13.5603945 L18.540252,13.5603945 C18.7898916,13.5603945 18.9922646,13.358005 18.9922646,13.1083654 L18.9922646,11.3093552 C19.0569739,10.3719235 18.7133456,9.4522737 18.0497708,8.7869663 C17.386196,8.1216589 16.4674453,7.77563372 15.5298481,7.83789844 L15.5298481,7.83789844 Z M3.98544629,6.84347073 C4.68520188,7.03470288 5.43310635,6.92317326 6.04662374,6.53610216 C6.10945982,6.49179763 6.14968069,6.42214356 6.15664151,6.34557455 C6.16360233,6.26900555 6.1366014,6.19324041 6.08278475,6.13833108 C5.36776941,5.31215509 4.97317808,4.25670349 4.97083376,3.16408818 C4.97016896,2.83559539 5.00656003,2.50807582 5.07931678,2.18774097 C5.08781924,2.15509157 5.09386299,2.12185089 5.09739728,2.08829819 C5.09755523,1.94332095 4.98366546,1.82385616 4.83884609,1.81709063 C4.82980583,1.81709063 4.71770672,1.81076246 4.67250544,1.81076246 C3.84016258,1.81294776 3.06052709,2.21849705 2.58090821,2.89876548 C2.10128932,3.57903391 1.98120903,4.44959836 2.25875817,5.23430587 C2.53015122,6.02495309 3.17766047,6.62839103 3.98544629,6.84347073 L3.98544629,6.84347073 Z M3.75039974,8.109106 C3.75285871,8.03485246 3.72380599,7.96302115 3.67041798,7.91135534 C3.61702997,7.85968953 3.54428497,7.83300691 3.47015192,7.83789844 C2.53255468,7.77563372 1.61380403,8.1216589 0.950229205,8.7869663 C0.286654376,9.4522737 -0.0569739271,10.3719235 0.00773542408,11.3093552 L0.00773542408,13.1083654 C0.00773542408,13.2282465 0.0553580426,13.3432177 0.140126847,13.4279865 C0.224895652,13.5127553 0.339866823,13.5603945 0.459748016,13.5603945 L1.82482607,13.5603945 C1.95675299,13.5619055 2.06546272,13.457222 2.06891287,13.3253314 L2.06891287,12.520749 C2.03372713,10.9651028 2.60047639,9.45593046 3.65095698,8.30799155 C3.70463667,8.25412017 3.73951046,8.18437259 3.75039974,8.109106 L3.75039974,8.109106 Z M9.50723219,6.32817637 C11.2547098,6.32817637 12.6713204,4.91156584 12.6713204,3.16408818 C12.6713204,1.41661053 11.2547098,0 9.50723219,0 C7.75975454,0 6.34314401,1.41661053 6.34314401,3.16408818 C6.34314401,4.91156584 7.75975454,6.32817637 9.50723219,6.32817637 L9.50723219,6.32817637 Z M11.1064528,8.03587996 L7.89354722,8.03587996 C6.68280119,7.95693902 5.49691889,8.40453024 4.64027435,9.26377277 C3.78362981,10.1230153 3.33963169,11.3102476 3.4222386,12.520749 L3.4222386,14.9164157 C3.42223859,15.0362969 3.46986121,15.1512681 3.55463001,15.2360369 C3.63939882,15.3208057 3.75436999,15.3684284 3.87425119,15.3684284 L15.1257488,15.3684284 C15.24563,15.3684284 15.3606012,15.3208057 15.44537,15.2360369 C15.5301388,15.1512681 15.5777614,15.0362969 15.5777614,14.9164157 L15.5777614,12.520749 C15.6603683,11.3102476 15.2163702,10.1230153 14.3597257,9.26377277 C13.5030811,8.40453024 12.3171988,7.95693902 11.1064528,8.03587996 L11.1064528,8.03587996 Z"></path>
        </g>
      </g>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconUserGroupFillComponent {

}
