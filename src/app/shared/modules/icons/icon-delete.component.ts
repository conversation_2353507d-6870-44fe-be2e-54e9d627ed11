import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'app-icon-delete, DeleteIcon',
  template: `
    <svg viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g transform="translate(0.750000, 0.749910)" fill="currentColor" fill-rule="nonzero">
          <path d="M18.2570592,2.92113995 L14.791626,2.92113995 C13.9152871,2.92113995 13.8773124,2.81013703 13.6368061,2.08959177 L13.4420641,1.4985499 C13.1451518,0.60229584 12.3063725,-0.00204934392 11.3622199,5.22239637e-06 L7.62512164,5.22239637e-06 C6.68168573,-0.00120778565 5.84391286,0.602977993 5.54722486,1.4985499 L5.35248291,2.08959177 C5.11197657,2.81013703 5.07400189,2.92113995 4.19766304,2.92113995 L0.730282349,2.92113995 C0.326958541,2.92113996 0,3.24809852 0,3.65142232 C0,4.05474613 0.326958541,4.38170468 0.730282349,4.38170469 L1.99610513,4.38170469 L2.74294056,15.5793677 C2.72388074,16.5308299 3.10997062,17.4455832 3.80498943,18.0956543 C4.5,18.7457253 5.4384955,19.0698869 6.38656274,18.9873521 L12.6046737,18.9873521 C13.5527409,19.0698869 14.4912282,18.7457253 15.186247,18.0956543 C15.8812658,17.4455832 16.2673557,16.5308299 16.2482959,15.5793677 L16.9912365,4.38170469 L18.2570592,4.38170469 C18.6603831,4.38170469 18.9873416,4.05474614 18.9873416,3.65142232 C18.9873416,3.24809851 18.6603831,2.92113995 18.2570592,2.92113995 Z M6.93281395,1.96008835 C7.03158665,1.66161503 7.3107298,1.46020795 7.62512164,1.46057471 L11.3622199,1.46057471 C11.6766118,1.46020795 11.9557549,1.66161503 12.0545276,1.96008835 L12.2492696,2.55113023 C12.2911391,2.67868621 12.3349561,2.80234736 12.3816941,2.92113995 L6.60272632,2.92113995 C6.6494644,2.80137364 6.69328134,2.67771249 6.73515084,2.55113023 L6.93281395,1.96008835 Z M14.7887048,15.481023 C14.8319242,16.0610541 14.6100355,16.6294364 14.1852659,17.026771 C13.7604963,17.4241055 13.1785855,17.607607 12.6027263,17.5258136 L6.38461531,17.5258136 C5.80875609,17.607607 5.22684529,17.4241055 4.80207569,17.026771 C4.37730609,16.6294364 4.1554174,16.0610541 4.19863676,15.481023 L3.45666987,4.38170469 L4.1937682,4.38170469 C4.31548192,4.38170469 4.41577404,4.36904646 4.52580326,4.36125679 C4.5583162,4.37068994 4.59152664,4.37752738 4.62512166,4.38170469 L14.3622199,4.38170469 C14.3951585,4.37742686 14.427714,4.37059022 14.4595909,4.36125679 C14.5696201,4.36904646 14.6699122,4.38170469 14.791626,4.38170469 L15.5306717,4.38170469 L14.7887048,15.481023 Z" id="Shape"></path>
        </g>
      </g>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconDeleteComponent {

}
