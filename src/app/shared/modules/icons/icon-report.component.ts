import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'ReportIcon',
  template: `
    <svg style="height: 0.7em;" viewBox="0 0 17 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="report-notice" transform="translate(0.055000, 0.000000)" fill="#686FE8" fill-rule="nonzero">
          <path d="M15.2757895,1 C14.4115789,1 13.7105263,1.66421053 13.7105263,2.48421053 L13.7105263,3.40315789 L3.13157895,3.40315789 L3.13157895,2.48315789 C3.13157895,1.66421053 2.42947368,1 1.56526316,1 C0.701052632,1 -1.77635684e-15,1.66421053 -1.77635684e-15,2.48421053 L-1.77635684e-15,18.5157895 C-1.77635684e-15,19.3357895 0.701052632,20 1.56526316,20 L15.2757895,20 C16.14,20 16.8421053,19.3357895 16.8421053,18.5157895 L16.8421053,2.48210526 C16.8389474,1.66315789 16.14,1.00105263 15.2757895,1.00105263 L15.2757895,1 Z M4.68842105,15.1894737 C4.4400855,15.4213121 4.05465134,15.4213121 3.80631579,15.1894737 L2.92,14.3494737 C2.80299159,14.2420231 2.73639051,14.0904392 2.73639051,13.9315789 C2.73639051,13.7727187 2.80299159,13.6211348 2.92,13.5136842 C3.16862817,13.2805326 3.55558235,13.2805326 3.80421053,13.5136842 L4.24631579,13.9326316 L5.52421053,12.7221053 C5.77254608,12.4902668 6.15798024,12.4902668 6.40631579,12.7221053 C6.52467064,12.8287955 6.5924977,12.980484 6.59310302,13.1398274 C6.59370029,13.2991708 6.52701997,13.4513669 6.40947368,13.5589474 L4.68842105,15.1884211 L4.68842105,15.1894737 Z M4.68842105,9.77052632 C4.4400855,10.0023648 4.05465134,10.0023648 3.80631579,9.77052632 L2.92,8.92947368 C2.80264434,8.82200078 2.73581658,8.67018395 2.73581658,8.51105263 C2.73581658,8.35192132 2.80264434,8.20010449 2.92,8.09263158 C3.16862817,7.85947993 3.55558235,7.85947993 3.80421053,8.09263158 L4.24631579,8.51263158 L5.52421053,7.30210526 C5.77254608,7.07026682 6.15798024,7.07026682 6.40631579,7.30210526 C6.52447829,7.40934513 6.59212314,7.56131379 6.59272781,7.72088323 C6.59332441,7.88045267 6.52682552,8.03292626 6.40947368,8.14105263 L4.68842105,9.77052632 L4.68842105,9.77052632 Z M14.3368421,15.3052632 C14.3368421,15.6315789 14.0568421,15.8957895 13.7126316,15.8957895 L8.70421053,15.8957895 C8.36,15.8957895 8.08,15.6305263 8.08,15.3052632 C8.08,14.9789474 8.36,14.7136842 8.70421053,14.7136842 L13.7147368,14.7136842 C13.8759146,14.7091592 14.0322565,14.7690209 14.1491958,14.8800343 C14.2661351,14.9910476 14.3340392,15.144068 14.3378947,15.3052632 L14.3368421,15.3052632 Z M8.70315789,13.1947368 C8.35894737,13.1947368 8.07894737,12.9294737 8.07894737,12.6031579 C8.07894737,12.2768421 8.35894737,12.0126316 8.70315789,12.0126316 L13.7136842,12.0126316 C14.0568421,12.0126316 14.3368421,12.2778947 14.3368421,12.6031579 C14.3368421,12.9294737 14.0568421,13.1947368 13.7126316,13.1947368 L8.70421053,13.1947368 L8.70315789,13.1947368 Z M14.3368421,9.88736842 C14.3368421,10.2136842 14.0568421,10.4789474 13.7126316,10.4789474 L8.70421053,10.4789474 C8.36,10.4789474 8.08,10.2136842 8.08,9.88736842 C8.08,9.56105263 8.36,9.29578947 8.70421053,9.29578947 L13.7147368,9.29578947 C13.8759146,9.29126445 14.0322565,9.35112619 14.1491958,9.46213952 C14.2661351,9.57315285 14.3340392,9.72617321 14.3378947,9.88736842 L14.3368421,9.88736842 Z M13.7126316,6.59473684 C14.0568421,6.59473684 14.3368421,6.86 14.3368421,7.18631579 C14.3368421,7.51263158 14.0568421,7.77684211 13.7126316,7.77684211 L8.70421053,7.77684211 C8.36,7.77684211 8.08,7.51157895 8.08,7.18631579 C8.08,6.86 8.36,6.59473684 8.70421053,6.59473684 L13.7147368,6.59473684 L13.7126316,6.59473684 Z M11.6178947,0 C12.3136842,0 12.8789474,0.448421053 12.8789474,1 C12.8789474,1.55157895 12.3136842,2 11.6178947,2 L6.21368421,2 C5.51894737,2 4.95263158,1.55157895 4.95263158,1 C4.95578947,0.448421053 5.51789474,0 6.21368421,0 L11.6178947,0 Z" id="形状"></path>
        </g>
      </g>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconReportComponent {

}
