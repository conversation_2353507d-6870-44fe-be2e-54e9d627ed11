import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'app-icon-fullscreen, FullscreenIcon',
  template: `
    <svg viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g transform="translate(0.000000, 0.000000)" fill="currentColor" fill-rule="nonzero">
        <path d="M7,11.4444445 L2.22222221,16.2222222 L2.22222221,13.3333333 C2.22222221,12.7222222 1.72222221,12.2222222 1.11111112,12.2222222 C0.5,12.2222222 -8.8817842e-16,12.7222222 -8.8817842e-16,13.3333333 L-8.8817842e-16,18.8888889 C-8.8817842e-16,19.5 0.5,20 1.11111112,20 L6.66666667,20 C7.27777779,20 7.77777779,19.5 7.77777779,18.8888889 C7.77777779,18.2777778 7.27777779,17.7777778 6.66666667,17.7777778 L3.77777779,17.7777778 L8.55555555,13 C8.94444443,12.5555555 8.88888888,11.8333333 8.44444445,11.4444445 C8,11.0555556 7.38888891,11.0555556 7,11.4444445 Z M18.8888889,0 L13.3333333,0 C12.7222222,0 12.2222222,0.5 12.2222222,1.11111112 C12.2222222,1.72222224 12.7222222,2.22222224 13.3333333,2.22222221 L16.2222222,2.22222221 L11.4444445,7 C11.0555556,7.44444445 11.1111111,8.16666667 11.5555555,8.55555555 C11.9444444,8.88888888 12.6111111,8.88888888 13,8.55555555 L17.7777778,3.77777779 L17.7777778,6.66666667 C17.7777778,7.27777779 18.2777778,7.77777779 18.8888889,7.77777779 C19.5,7.77777779 20,7.27777779 20,6.66666667 L20,1.11111112 C20,0.5 19.5,0 18.8888889,0 L18.8888889,0 Z"></path>
      </g>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconFullscreenComponent {

}
