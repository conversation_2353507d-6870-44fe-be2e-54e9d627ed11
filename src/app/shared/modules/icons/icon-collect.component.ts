import { ChangeDetectionStrategy, Component, input, numberAttribute } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'app-icon-collect, CollectIcon',
  template: `
    @switch (style()) {
      @case (1) {
        <svg viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
          <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g fill-rule="nonzero">
              <path d="M0,10 C0,15.5228475 4.4771525,20 10,20 C15.5228475,20 20,15.5228475 20,10 C20,4.4771525 15.5228475,0 10,0 C4.4771525,0 0,4.4771525 0,10 Z" fill="#50DCB6"></path>
              <path d="M14.99375,12.3582901 C14.9847315,12.3331878 14.9660572,12.3127288 14.9418791,12.301462 C14.917701,12.2901953 14.8900233,12.2890547 14.865,12.2982939 L14.1,12.5770264 L14.1,5.84994687 C14.1,5.37997624 13.72,5 13.25,5 L6.75,5 C6.28,5 5.9,5.37997625 5.9,5.84994687 L5.9,12.5770264 L5.13375,12.2982939 C5.1225,12.2945441 5.11124999,12.2920442 5.1,12.2920442 C5.045,12.2920442 5,12.3370414 5,12.392038 L5,13.1369914 C5,13.1782389 5.02625,13.2157365 5.06625,13.2307356 L9.8625,14.9756265 C9.95125,15.0081245 10.0475,15.0081245 10.13625,14.9756265 L14.93375,13.2319855 C14.97375,13.2169864 15,13.1794888 15,13.1382414 L15,12.3932879 C15,12.3807887 14.9975,12.3695394 14.99375,12.3582901 L14.99375,12.3582901 Z M11.9875469,7.64625 L10.6961557,10.04125 L11.4134569,10.04125 C11.4682507,10.04125 11.513082,10.08625 11.513082,10.14125 L11.513082,10.48 C11.513082,10.535 11.4682507,10.58 11.4134569,10.58 L10.4632819,10.58 L10.4632819,11.0675 L11.4134569,11.0675 C11.4682507,11.0675 11.513082,11.1125 11.513082,11.1675 L11.513082,11.50625 C11.513082,11.56125 11.4682507,11.60625 11.4134569,11.60625 L10.4632819,11.60625 L10.4632819,12.4 C10.4632819,12.455 10.4184506,12.5 10.3636568,12.5 L9.74224481,12.5 C9.68745098,12.5 9.64261965,12.455 9.64261965,12.4 L9.64261965,11.6075 L8.69618063,11.6075 C8.6413868,11.6075 8.59655547,11.5625 8.59655547,11.5075 L8.59655547,11.16875 C8.59655547,11.11375 8.6413868,11.06875 8.69618063,11.06875 L9.64261965,11.06875 L9.64261965,10.58125 L8.69618063,10.58125 C8.6413868,10.58125 8.59655547,10.53625 8.59655547,10.48125 L8.59655547,10.1425 C8.59655547,10.0875 8.6413868,10.0425 8.69618063,10.0425 L9.4060099,10.0425 L8.11212813,7.64750001 C8.08597653,7.60000001 8.10341093,7.53875001 8.1519782,7.5125 C8.16692198,7.50375 8.18311106,7.5 8.19930015,7.5 L8.89294033,7.5 C8.93036521,7.50020798 8.96451678,7.52145196 8.98135766,7.55500001 L10.036139,9.665 L10.0772344,9.665 L11.1320158,7.55500001 C11.1482049,7.52125001 11.1830737,7.5 11.2204331,7.5 L11.8991295,7.5 C11.9539234,7.5 11.9987547,7.545 12,7.59875001 C12,7.615 11.9950187,7.63125001 11.9875469,7.64625 Z" fill="#FFFFFF"></path>
            </g>
          </g>
        </svg>
      }
      @case (2) {
        <svg viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
          <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g id="money-collect-fill" fill="currentColor" fill-rule="nonzero">
              <path d="M19.9875,14.7175 C19.95,14.6125 19.835,14.56 19.73,14.5975 L18.2,15.155 L18.2,1.7 C18.2,0.76 17.44,0 16.5,0 L3.5,0 C2.56,0 1.8,0.76 1.8,1.7 L1.8,15.155 L0.2675,14.5975 C0.245,14.59 0.2225,14.585 0.2,14.585 C0.09,14.585 0,14.675 0,14.785 L0,16.275 C0,16.3575 0.0525,16.4325 0.1325,16.4625 L9.725,19.9525 C9.9025,20.0175 10.095,20.0175 10.2725,19.9525 L19.8675,16.465 C19.9475,16.435 20,16.36 20,16.2775 L20,14.7875 C20,14.7625 19.995,14.74 19.9875,14.7175 Z M13.8925,5.2925 L11.3,10.0825 L12.74,10.0825 C12.85,10.0825 12.94,10.1725 12.94,10.2825 L12.94,10.96 C12.94,11.07 12.85,11.16 12.74,11.16 L10.8325,11.16 L10.8325,12.135 L12.74,12.135 C12.85,12.135 12.94,12.225 12.94,12.335 L12.94,13.0125 C12.94,13.1225 12.85,13.2125 12.74,13.2125 L10.8325,13.2125 L10.8325,14.8 C10.8325,14.91 10.7425,15 10.6325,15 L9.385,15 C9.275,15 9.185,14.91 9.185,14.8 L9.185,13.215 L7.285,13.215 C7.175,13.215 7.085,13.125 7.085,13.015 L7.085,12.3375 C7.085,12.2275 7.175,12.1375 7.285,12.1375 L9.185,12.1375 L9.185,11.1625 L7.285,11.1625 C7.175,11.1625 7.085,11.0725 7.085,10.9625 L7.085,10.285 C7.085,10.175 7.175,10.085 7.285,10.085 L8.71,10.085 L6.1125,5.295 C6.06,5.2 6.095,5.0775 6.1925,5.025 C6.2225,5.0075 6.255,5 6.2875,5 L7.68,5 C7.755,5 7.8225,5.0425 7.8575,5.11 L9.975,9.33 L10.0575,9.33 L12.175,5.11 C12.2075,5.0425 12.2775,5 12.3525,5 L13.715,5 C13.825,5 13.915,5.09 13.9175,5.1975 C13.9175,5.23 13.9075,5.2625 13.8925,5.2925 Z" id="形状"></path>
            </g>
          </g>
        </svg>
      }
    }
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconCollectComponent {

  style = input(1, { transform: numberAttribute });
  
}
