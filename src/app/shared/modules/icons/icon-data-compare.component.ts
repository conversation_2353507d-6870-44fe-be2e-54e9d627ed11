import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'app-icon-data-compare, DataCompareIcon',
  template: `
    <svg viewBox="0 0 20 18" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g transform="translate(0.666667, 0.333333)" fill-rule="nonzero">
          <path d="M1.18750001,9.05468748 C1.18750001,12.4158579 3.91226704,15.140625 7.2734375,15.140625 C10.634608,15.140625 13.359375,12.4158579 13.359375,9.05468748 C13.359375,5.69351702 10.634608,2.96874999 7.2734375,2.96874999 C3.91226704,2.96874999 1.18750001,5.69351702 1.18750001,9.05468748 L1.18750001,9.05468748 Z" fill="#0b77e333"></path>
          <path d="M18.40625,16.328125 L19,16.328125 L19,17.328125 L18.40625,17.328125 L18.40625,16.328125 Z M-3.88578059e-16,16.328125 L0.593750007,16.328125 L0.593750007,17.328125 L-3.88578059e-16,17.328125 L-3.88578059e-16,16.328125 Z M1.18750001,16.328125 L16.03125,16.328125 L16.03125,17.328125 L1.18750001,17.328125 L1.18750001,16.328125 Z" fill="#0B77E3"></path>
          <polygon fill="#A3D4FF" points="2.37500001 8.60937498 4.453125 8.60937498 4.453125 16.625 2.37500001 16.625"></polygon>
          <path d="M4.75000001,16.921875 L2.07812501,16.921875 L2.07812501,8.31249999 L4.75000001,8.31249999 L4.75000001,16.921875 L4.75000001,16.921875 Z M2.671875,16.328125 L4.15625,16.328125 L4.15625,8.90624999 L2.671875,8.90624999 L2.671875,16.328125 Z" fill="#0B77E3"></path>
          <polygon fill="#FFFFFF" points="5.34375 12.765625 7.42187501 12.765625 7.42187501 16.625 5.34375 16.625"></polygon>
          <polygon fill="#0B77E3" points="7.71875 16.921875 5.34375 16.921875 5.34375 16.328125 7.12499999 16.328125 7.12499999 13.0625 5.34375 13.0625 5.34375 12.46875 7.71875 12.46875"></polygon>
          <polygon fill="#A3D4FF" points="9.5 2.671875 11.578125 2.671875 11.578125 16.625 9.5 16.625"></polygon>
          <path d="M11.875,16.921875 L9.20312501,16.921875 L9.20312501,2.37499998 L11.875,2.37499998 L11.875,16.921875 Z M9.79687499,16.328125 L11.28125,16.328125 L11.28125,2.96874999 L9.79687499,2.96874999 L9.79687499,16.328125 Z" fill="#0B77E3"></path>
          <polygon fill="#FFFFFF" points="12.46875 8.01562499 14.546875 8.01562499 14.546875 16.625 12.46875 16.625"></polygon>
          <polygon fill="#0B77E3" points="14.84375 16.921875 12.46875 16.921875 12.46875 16.328125 14.25 16.328125 14.25 8.31249999 12.46875 8.31249999 12.46875 7.71874998 14.84375 7.71874998"></polygon>
          <polygon fill="#0b77e333" points="15.734375 2.96874999 16.328125 2.96874999 16.328125 4.74999999 15.734375 4.74999999"></polygon>
          <path d="M15.140625,3.5625 L16.921875,3.5625 L16.921875,4.15624998 L15.140625,4.15624998 L15.140625,3.5625 Z M13.65625,0 L14.25,0 L14.25,1.78125 L13.65625,1.78125 L13.65625,0 Z" fill="#0b77e333"></path>
          <polygon fill="#0b77e333" points="13.0625 0.593749985 14.84375 0.593749985 14.84375 1.18749999 13.0625 1.18749999"></polygon>
          <polygon fill="#0B77E3" points="16.625 16.328125 17.8125 16.328125 17.8125 16.921875 16.625 16.921875"></polygon>
        </g>
      </g>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconDataCompareComponent {

}
