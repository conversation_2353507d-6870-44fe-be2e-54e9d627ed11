import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'app-icon-success-fill, SuccessFillIcon',
  template: `
    <svg viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g fill="currentColor" fill-rule="nonzero">
        <path d="M10,-6.23891183e-16 C4.48780487,-6.23891183e-16 -6.23891183e-16,4.48780487 -6.23891183e-16,10 C-6.23891183e-16,15.5121951 4.48780487,20 10,20 C15.5121951,20 20,15.5121951 20,10 C20,4.48780487 15.5121951,-6.23891183e-16 10,-6.23891183e-16 Z M15.4634146,7.99999999 L15.4146341,8.04878049 L9.07317074,14.3902439 C8.82926829,14.6341464 8.3902439,14.6829268 8.09756097,14.4390244 L8.04878049,14.3902439 L4.63414633,10.9756098 C4.3414634,10.6829268 4.3414634,10.2439024 4.63414633,9.95121952 C4.87804878,9.70731707 5.31707317,9.65853659 5.60975609,9.90243902 L5.65853657,9.95121952 L8.53658536,12.8292683 L14.3414634,7.02439025 C14.5853659,6.7804878 15.0243903,6.73170732 15.3170732,6.97560975 L15.3658537,7.02439025 C15.6585366,7.31707318 15.6585366,7.75609757 15.4634146,7.99999999 L15.4634146,7.99999999 Z" id="形状"></path>
      </g>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconSuccessFillComponent {

}
