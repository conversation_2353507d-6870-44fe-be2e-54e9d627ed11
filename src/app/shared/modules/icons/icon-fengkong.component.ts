import { ChangeDetectionStrategy, Component, input, numberAttribute } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'FengKongIcon',
  template: `
    @switch (style()) {
      @case (1) {
        <svg viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
          <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g transform="translate(0.466667, 0.000000)" fill-rule="nonzero">
              <path d="M18.7293333,5 C18.5913333,8.83 17.8206667,11.932 16.4173333,14.3053333 C14.7966667,17.046 12.4493333,18.942 9.376,19.9926667 L9.376,20 C9.37254618,19.9985863 9.36897757,19.9974711 9.36533334,19.9966667 C9.36187952,19.9980804 9.35831091,19.9991956 9.35466668,20 L9.35466668,19.9926667 C6.28066668,18.942 3.93400002,17.046 2.31333334,14.3053333 C0.90866668,11.932 0.138,8.83 3.55271368e-16,5 C0.597359657,4.99359649 1.15601076,4.70327088 1.5045575,4.21809592 C1.85310424,3.73292097 1.94993185,3.11082417 1.76533334,2.54266666 L9.35333334,0.00733333984 L9.35333334,0 L9.364,0.00333333984 L9.37533334,0 L9.37533334,0.00733333984 L16.9633333,2.54266666 C16.7787013,3.11093498 16.8756064,3.73315512 17.2243075,4.2183617 C17.5730085,4.70356827 18.1318557,4.9938067 18.7293333,5 Z" fill="#D0D6FE"></path>
              <path d="M9.35333334,2.00533334 L9.35333334,2 L9.35333334,2.00533334 L9.37,2 L9.37,17.7946667 L9.35333334,17.7886667 L9.35333334,17.7946667 L9.35333334,17.7886667 C6.94933334,16.9586667 5.11266668,15.4613333 3.84533334,13.2966667 C2.74733334,11.4226667 2.14466668,8.97333332 2.03666668,5.94866666 C2.84494736,5.93661793 3.49301205,5.27636486 3.49,4.468 C3.49,4.30733334 3.46466666,4.15266666 3.41733334,4.008 L9.35333334,2.00533334 Z" fill="#8493DC"></path>
              <path d="M16.6866667,5.94866666 C16.5786667,8.97333332 15.976,11.4233333 14.878,13.2973333 C13.6073333,15.4666667 11.766,16.966 9.35333334,17.7946667 L9.35333334,2 L15.3053333,4.008 C15.2586667,4.15266666 15.2326667,4.30733334 15.2326667,4.468 C15.2303819,5.27632665 15.878432,5.93626212 16.6866667,5.94866666 Z" fill="#6D7ECD"></path>
              <polygon fill="#FFFFFF" points="9.35333334 9.87866666 9.11066668 9.87866666 9.11066668 11.3333333 9.35333334 11.3333333 9.35333334 13 6.68666668 13 6.68666668 9 7.17133334 9 7.17133334 6.66666666 9.35333334 6.66666666 9.35333334 7.63666666 8.14133334 7.63666666 8.14133334 8.97 9.35333334 8.97 9.35333334 9.87866666"></polygon>
              <polygon fill="#D0D6FE" points="9.35333334 9.87866666 9.596 9.87866666 9.596 11.3333333 9.35333334 11.3333333 9.35333334 13 12.02 13 12.02 9 11.5353333 9 11.5353333 6.66666666 9.35333334 6.66666666 9.35333334 7.63666666 10.5653333 7.63666666 10.5653333 8.97 9.35333334 8.97 9.35333334 9.87866666"></polygon>
            </g>
          </g>
        </svg>
      }
      @case (2) {
        <svg viewBox="0 0 19 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
          <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g id="风控中心" fill="#68A6F9" fill-rule="nonzero">
              <path d="M17.7375566,1.99095023 C14.841629,1.53846154 11.9457014,0.904977376 9.14027149,0 C6.33484163,0.904977376 3.52941176,1.62895928 0.542986425,2.08144796 C0.180995475,2.1719457 0,2.44343891 0,2.80542986 L0,10.5882353 C0.180995475,11.8552036 0.542986425,13.2126697 1.17647059,14.2986425 C1.99095023,15.4751131 2.98642534,16.561086 4.16289593,17.3755656 C5.52036199,18.5520362 7.14932127,19.4570136 8.95927602,20 L9.32126697,20 C11.040724,19.4570136 12.6696833,18.5520362 14.0271493,17.3755656 C15.2036199,16.4705882 16.199095,15.4751131 17.1040724,14.2986425 C17.8280543,13.1221719 18.1900452,11.8552036 18.280543,10.5882353 L18.280543,2.71493213 C18.280543,2.35294118 18.0090498,2.08144796 17.7375566,1.99095023 L17.7375566,1.99095023 Z M11.7647059,9.77375566 L7.78280543,14.5701357 C7.51131222,14.9321267 7.33031674,14.841629 7.42081448,14.3891403 L8.23529412,10.7692308 L6.78733032,10.7692308 C6.42533937,10.7692308 6.15384615,10.5882353 6.15384615,10.2262443 C6.15384615,10.1357466 6.15384615,10.0452489 6.24434389,9.86425339 L7.60180995,5.88235294 C7.69230769,5.42986425 7.87330317,5.0678733 8.50678733,5.0678733 L9.86425339,5.0678733 C10.3167421,5.0678733 10.5882353,5.42986425 10.3167421,5.88235294 L9.23076923,8.41628959 L11.040724,8.41628959 C12.0361991,8.50678733 12.3076923,9.14027149 11.7647059,9.77375566 L11.7647059,9.77375566 Z" id="形状"></path>
            </g>
          </g>
        </svg>
      }
    }
    
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconFengKongComponent {

  style = input(1, { transform: numberAttribute });

}
