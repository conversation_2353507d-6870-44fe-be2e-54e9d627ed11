import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  standalone: true,
  selector: 'OkrIcon',
  template: `
    <svg viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <path fill="currentColor" d="M9.26572495,1.61342206 C10.249713,1.61342206 11.1966278,1.76664353 12.0863878,2.04703882 L11.8562242,2.27380659 L11.7063861,2.44235021 C11.4828132,2.72341795 11.3431394,3.06101659 11.3032136,3.41683875 C7.90815787,2.49709271 4.31556971,3.97021679 2.56628738,6.99937476 C0.817005058,10.0285327 1.35343382,13.8476302 3.87084509,16.2871559 C6.38825635,18.7266817 10.2499799,19.1696633 13.26229,17.3644566 C16.2746,15.5592499 17.6756632,11.9624038 16.6695788,8.61717538 C17.0601785,8.55181218 17.4204155,8.36699139 17.6999085,8.08856132 L18.0181213,7.77292509 C19.3493087,11.5500189 18.0646624,15.7473959 14.8412083,18.1529821 C11.6177542,20.5585684 7.19584517,20.6198563 3.9058865,18.3045464 C0.615927827,15.9892364 -0.786452945,11.8291019 0.4378176,8.01658922 C1.66208815,4.20407657 5.23182332,1.61483202 9.26572495,1.61342206 L9.26572495,1.61342206 Z M9.26572495,6.21006611 C9.67353163,6.21006611 10.0705253,6.26216141 10.4474375,6.36022315 L9.04791911,7.74994187 C7.39607939,7.86576331 6.13005968,9.25283086 6.17759378,10.8947074 C6.22512788,12.5365839 7.56930932,13.8493021 9.2251277,13.8709077 C10.8809461,13.8925133 12.2594754,12.6153219 12.3505353,10.9752538 L13.7376959,9.599325 C14.114336,10.9811685 13.8213787,12.458169 12.9450694,13.595503 C12.0687602,14.732837 10.7088623,15.4010247 9.26572495,15.4033542 C7.61009659,15.4033542 6.08023289,14.5272388 5.25241871,13.1050322 C4.42460452,11.6828256 4.42460452,9.93059475 5.25241871,8.50838812 C6.08023289,7.08618149 7.61009659,6.21006611 9.26572495,6.21006611 Z M16.3312846,0.00153221468 L16.8456771,2.82080723 C16.8741685,2.97856641 16.998664,3.10205396 17.157711,3.1303146 L20,3.64054209 L16.6031557,7.00988218 C16.5218482,7.09041271 16.4086105,7.13084817 16.2942113,7.12020164 L14.4250973,6.94552916 C14.3106981,6.93488263 14.1974604,6.97531809 14.1161529,7.05584862 L9.86198774,11.2755679 C9.71127315,11.4245952 9.46741538,11.4245952 9.31670078,11.2755679 L8.7698691,10.7346961 C8.69723494,10.6628319 8.65641405,10.565254 8.65641405,10.4634941 C8.65641405,10.3617341 8.69723494,10.2641562 8.7698691,10.1922921 L13.0163106,5.98176613 C13.1022707,5.89600825 13.1423556,5.77502302 13.1244412,5.6554044 L12.8355781,3.68804074 C12.8176637,3.56842212 12.8577486,3.44743689 12.9437087,3.36167902 L13.2541978,3.05523608 L13.6218417,2.69056899 L16.3312846,0 L16.3312846,0.00153221468 Z" id="Shape"></path>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconOkrComponent {

}
