import { ChangeDetectionStrategy, Component, input, numberAttribute } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'TodoListIcon',
  template: `
    <svg viewBox="0 0 20 17" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="代办事项" transform="translate(0.100000, 0.000000)" fill-rule="nonzero">
          <path d="M0.40135193,4.14596536 L18.5986481,4.14596536 L18.5986481,15.9216308 C18.5986481,16.0639009 18.5418622,16.2002864 18.4408859,16.3005091 C18.3399096,16.4007318 18.2031027,16.4564985 18.0608365,16.4554439 L0.939163498,16.4554439 C0.644354977,16.4554439 0.405365447,16.2164393 0.405365434,15.9216308 L0.405365434,4.14596536 L0.40135193,4.14596536 Z" id="路径" fill="#FFB038"></path>
          <path d="M1.74186733,0.935143918 L17.2581327,0.935143918 C17.9962623,0.935143918 18.5946346,1.53352225 18.5946346,2.27165187 L18.5946346,4.14596536 L0.401345877,4.14596536 L0.401345877,2.2756654 C0.400281684,1.91981159 0.541170443,1.57822406 0.792798221,1.32659628 C1.044426,1.0749685 1.38601352,0.934079731 1.74186733,0.935143918 Z" id="路径" fill="#F6716F"></path>
          <path d="M0.40135193,4.14596536 L0.40135193,14.0473173 C0.40135193,14.2602076 0.485922261,14.4643784 0.636458432,14.6149146 C0.786994603,14.7654508 0.991165469,14.8500211 1.20405577,14.8500211 L14.3162231,14.8500211 C14.6729487,14.84842 15.0141365,14.7038489 15.2634136,14.4486692 L18.2253908,11.486692 C18.4803397,11.2389088 18.6249681,10.8990319 18.6267427,10.543515 L18.6267427,4.12188425 L0.40135193,4.14596536 Z" id="路径" fill="#FFE3BA"></path>
          <path d="M9.5,12.1449092 C6.36544148,12.1449092 3.31516689,12.0245036 0.40135193,11.80376 L0.40135193,14.0473173 C0.40135193,14.2602076 0.485922261,14.4643784 0.636458432,14.6149146 C0.786994603,14.7654508 0.991165469,14.8500211 1.20405577,14.8500211 L14.3162231,14.8500211 C14.6729487,14.84842 15.0141365,14.7038489 15.2634136,14.4486692 L17.8641741,11.8479087 C15.1590621,12.0405577 12.3656527,12.1449092 9.5,12.1449092 L9.5,12.1449092 Z" id="路径" fill="#FFC670"></path>
          <path d="M15.243346,14.4486692 L18.2053232,11.486692 C18.3179159,11.3698049 18.4089527,11.2339291 18.474229,11.0853401 L15.6647655,11.0853401 C15.4518752,11.0853401 15.2477044,11.1699104 15.0971682,11.3204466 C14.946632,11.4709828 14.8620617,11.6751536 14.8620617,11.8880439 L14.8620617,14.6975074 C15.0033374,14.6386386 15.1325819,14.5542896 15.243346,14.4486692 L15.243346,14.4486692 Z" id="路径" fill="#FFB038"></path>
          <path d="M2.80946346,6.42163076 L4.9486692,6.42163076 L4.9486692,8.5608365 L2.80946346,8.5608365 L2.80946346,6.42163076 Z M2.80946346,10.43515 L4.9486692,10.43515 L4.9486692,12.5743557 L2.80946346,12.5743557 L2.80946346,10.43515 Z" id="形状" fill="#FFF6E6"></path>
          <path d="M19,2.2756654 C18.9977931,1.31457424 18.2192238,0.536004946 17.2581327,0.533798064 L15.9657795,0.533798064 L15.9657795,0.40135193 C15.9657258,0.179729325 15.7860502,0 15.5644275,0 C15.3428049,0 15.1631293,0.179729325 15.1630756,0.40135193 L15.1630756,0.533798064 L12.9355725,0.533798064 L12.9355725,0.40135193 C12.9355725,0.17969138 12.7558811,0 12.5342205,0 C12.31256,0 12.1328686,0.17969138 12.1328686,0.40135193 L12.1328686,0.533798064 L9.90135193,0.533798064 L9.90135193,0.40135193 C9.90135193,0.17969138 9.72166055,0 9.5,0 C9.27833945,0 9.09864807,0.17969138 9.09864807,0.40135193 L9.09864807,0.533798064 L6.8671314,0.533798064 L6.8671314,0.40135193 C6.8671314,0.17969138 6.68744002,0 6.46577947,0 C6.24411892,0 6.06442754,0.17969138 6.06442754,0.40135193 L6.06442754,0.533798064 L3.83692437,0.533798064 L3.83692437,0.40135193 C3.83687072,0.179729325 3.65719506,0 3.43557245,0 C3.21394984,0 3.03427418,0.179729325 3.03422053,0.40135193 L3.03422053,0.533798064 L1.74186733,0.533798064 C0.780776174,0.536004946 0.00220688136,1.31457424 0,2.2756654 L0,15.9216308 C0.00220992879,16.4387484 0.422041166,16.8567807 0.939163498,16.8567807 L18.0608365,16.8567807 C18.5779588,16.8567807 18.9977901,16.4387484 19,15.9216308 L19,4.14596536 L19,4.10583016 L19,4.06569496 L19,2.2756654 Z M15.652725,10.7040558 C14.9877433,10.7040558 14.4486692,11.2431299 14.4486692,11.9081115 L14.4486692,14.4486692 C14.404576,14.4517995 14.3603163,14.4517995 14.3162231,14.4486692 L1.20405577,14.4486692 C0.982395216,14.4486692 0.802703837,14.2689778 0.802703837,14.0473173 L0.802703837,4.54731727 L18.1972962,4.54731727 L18.1972962,10.5675961 C18.2010451,10.6183649 18.2010451,10.6693411 18.1972962,10.7201099 L15.652725,10.7040558 Z M17.6273764,11.5067596 L15.251373,13.882763 L15.251373,11.9081115 C15.251373,11.686451 15.4310644,11.5067596 15.652725,11.5067596 L17.6273764,11.5067596 Z M1.74186733,1.3365019 L3.03422053,1.3365019 L3.03422053,1.47296156 C3.03418582,1.61637393 3.11067547,1.74890728 3.23486844,1.82062349 C3.35906141,1.8923397 3.51208349,1.8923397 3.63627646,1.82062349 C3.76046943,1.74890728 3.83695908,1.61637393 3.83692437,1.47296156 L3.83692437,1.3365019 L6.06442754,1.3365019 L6.06442754,1.47296156 C6.06442754,1.69462211 6.24411892,1.87431349 6.46577947,1.87431349 C6.68744002,1.87431349 6.8671314,1.69462211 6.8671314,1.47296156 L6.8671314,1.3365019 L9.09864807,1.3365019 L9.09864807,1.47296156 C9.09864807,1.69462211 9.27833945,1.87431349 9.5,1.87431349 C9.72166055,1.87431349 9.90135193,1.69462211 9.90135193,1.47296156 L9.90135193,1.3365019 L12.1328686,1.3365019 L12.1328686,1.47296156 C12.1328686,1.69462211 12.31256,1.87431349 12.5342205,1.87431349 C12.7558811,1.87431349 12.9355725,1.69462211 12.9355725,1.47296156 L12.9355725,1.3365019 L15.1630756,1.3365019 L15.1630756,1.47296156 C15.1630409,1.61637393 15.2395306,1.74890728 15.3637235,1.82062349 C15.4879165,1.8923397 15.6409386,1.8923397 15.7651316,1.82062349 C15.8893245,1.74890728 15.9658142,1.61637393 15.9657795,1.47296156 L15.9657795,1.3365019 L17.2581327,1.3365019 C17.7768183,1.3365019 18.1972962,1.75697972 18.1972962,2.2756654 L18.1972962,3.74461343 L0.802703837,3.74461343 L0.802703837,2.2756654 C0.802703837,1.75697972 1.22318166,1.3365019 1.74186733,1.3365019 Z M18.0608365,16.0540769 L0.939163498,16.0540769 C0.865338866,16.0540769 0.804875147,15.9954235 0.802703837,15.9216308 L0.802703837,15.1791297 C0.931507363,15.2254824 1.06717348,15.2499023 1.20405577,15.251373 L14.3162231,15.251373 C14.7688037,15.2455344 15.2010683,15.0625423 15.5202788,14.7416561 L18.1892691,12.0726658 L18.1892691,15.9216308 C18.1873074,15.9924155 18.1315274,16.0499387 18.0608365,16.0540769 L18.0608365,16.0540769 Z" id="形状" fill="#3D3D63"></path>
          <path d="M1.87431347,2.64892269 C1.87431347,2.87058324 2.05400485,3.05027462 2.2756654,3.05027462 C2.49732595,3.05027462 2.67701733,2.87058324 2.67701733,2.64892269 C2.67701733,2.42726214 2.49732595,2.24757076 2.2756654,2.24757076 C2.05400485,2.24757076 1.87431347,2.42726214 1.87431347,2.64892269 Z" id="路径" fill="#3D3D63"></path>
          <path d="M16.3229827,2.64892269 C16.3229827,2.87058324 16.5026741,3.05027462 16.7243346,3.05027462 C16.9459952,3.05027462 17.1256865,2.87058324 17.1256865,2.64892269 C17.1256865,2.42726214 16.9459952,2.24757076 16.7243346,2.24757076 C16.5026741,2.24757076 16.3229827,2.42726214 16.3229827,2.64892269 Z" id="路径" fill="#3D3D63"></path>
          <path d="M4.95268273,6.02027883 L2.80946346,6.02027883 C2.58780291,6.02027883 2.40811153,6.19997021 2.40811153,6.42163076 L2.40811153,8.5608365 C2.40811153,8.66728165 2.4503967,8.76936709 2.52566479,8.84463517 C2.60093288,8.91990326 2.70301831,8.96218843 2.80946346,8.96218843 L4.95268273,8.96218843 C5.17434327,8.96218843 5.35403464,8.78249704 5.35403464,8.5608365 L5.35403464,6.42163076 C5.35403464,6.19997022 5.17434327,6.02027883 4.95268273,6.02027883 Z M4.5513308,8.15948457 L3.21081537,8.15948457 L3.21081537,6.82298267 L4.5513308,6.82298267 L4.5513308,8.15948457 Z M4.95268273,10.0337981 L2.80946346,10.0337981 C2.58780292,10.0337981 2.40811153,10.2134894 2.40811153,10.43515 L2.40811153,12.5743557 C2.40811153,12.7960163 2.58780292,12.9757076 2.80946346,12.9757076 L4.95268273,12.9757076 C5.17434326,12.9757076 5.35403464,12.7960163 5.35403464,12.5743557 L5.35403464,10.43515 C5.35403464,10.2134894 5.17434326,10.0337981 4.95268273,10.0337981 L4.95268273,10.0337981 Z M4.5513308,12.1730038 L3.21081537,12.1730038 L3.21081537,10.8365019 L4.5513308,10.8365019 L4.5513308,12.1730038 Z M6.5139417,7.09188847 L7.98288973,7.09188847 C8.20451234,7.09183482 8.38414451,6.91215916 8.38414451,6.69053655 C8.38414451,6.46891394 8.20451234,6.28923828 7.98288973,6.28918463 L6.5139417,6.28918463 C6.29231909,6.28923828 6.11268692,6.46891394 6.11268692,6.69053655 C6.11268692,6.91215916 6.29231909,7.09183482 6.5139417,7.09188847 L6.5139417,7.09188847 Z M10.3910013,7.8945923 L6.5139417,7.8945923 C6.29228115,7.8945923 6.11258977,8.07428368 6.11258977,8.29594423 C6.11258977,8.51760478 6.29228115,8.69729616 6.5139417,8.69729616 L10.3910013,8.69729616 C10.6126618,8.69729616 10.7923532,8.51760478 10.7923532,8.29594423 C10.7923532,8.07428368 10.6126618,7.8945923 10.3910013,7.8945923 L10.3910013,7.8945923 Z M6.5139417,11.1054077 L7.98288973,11.1054077 C8.20455028,11.1054077 8.38424166,10.9257163 8.38424166,10.7040558 C8.38424166,10.4823952 8.20455028,10.3027038 7.98288973,10.3027038 L6.5139417,10.3027038 C6.29228115,10.3027038 6.11258977,10.4823952 6.11258977,10.7040558 C6.11258977,10.9257163 6.29228115,11.1054077 6.5139417,11.1054077 L6.5139417,11.1054077 Z M10.3910013,11.9081115 L6.5139417,11.9081115 C6.29231909,11.9081652 6.11268692,12.0878408 6.11268692,12.3094635 C6.11268692,12.5310861 6.29231909,12.7107617 6.5139417,12.7108154 L10.3910013,12.7108154 C10.6126239,12.7107617 10.792256,12.5310861 10.792256,12.3094635 C10.792256,12.0878408 10.6126239,11.9081652 10.3910013,11.9081115 L10.3910013,11.9081115 Z" id="形状" fill="#3D3D63"></path>
        </g>
      </g>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconTodoListComponent {


}
