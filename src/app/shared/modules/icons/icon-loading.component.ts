import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'app-icon-loading, LoadingIcon',
  template: `
    <svg class="animate-spin" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g stroke="none" stroke-width="1" fill="none">
        <circle stroke-opacity="0.25" stroke="currentColor" stroke-width="4" cx="12" cy="12" r="10"></circle>
        <path d="M4,12 C4,7.581722 7.581722,4 12,4 L12,0 C5.373,0 0,5.373 0,12 L4,12 Z M6,17.291 C4.7082138,15.8315689 3.99660577,13.9490098 4,12 L0,12 C0,15.042 1.135,17.824 3,19.938 L6,17.291 L6,17.291 Z" fill-opacity="0.75" fill="currentColor" fill-rule="nonzero"></path>
      </g>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconLoadingComponent {

}
