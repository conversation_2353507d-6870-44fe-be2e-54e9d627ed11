import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'UserFillIcon',
  template: `
    <svg viewBox="0 0 18 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g transform="translate(0.298646, 0.194660)" fill="currentColor" fill-rule="nonzero">
        <path d="M8.55048257,0 C11.1797585,0 13.3110822,2.09653658 13.3110822,4.68239406 C13.3110822,7.26880079 11.1797846,9.36544199 8.55048257,9.36544199 C5.92131125,9.36544199 3.79001373,7.26880079 3.79001373,4.68239406 C3.79001373,2.09593498 5.92201745,0 8.55048257,0 L8.55048257,0 L8.55048257,0 L8.55048257,0 Z" id="Path"></path>
        <path d="M6.48457432,10.6389632 L10.668833,10.6389632 C14.2503858,10.6389632 17.1534073,13.0072575 17.1534073,16.5304046 L17.1534073,16.9413107 C17.1534073,18.3297078 14.2497581,19 10.668833,19 L6.48457432,19 C2.90291689,19 0,18.3804498 0,16.9413107 L0,16.5304046 C0,13.0072575 2.90294307,10.6389632 6.48457432,10.6389632 L6.48457432,10.6389632 L6.48457432,10.6389632 L6.48457432,10.6389632 Z" id="Path"></path>
      </g>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconUserFillComponent {

}
