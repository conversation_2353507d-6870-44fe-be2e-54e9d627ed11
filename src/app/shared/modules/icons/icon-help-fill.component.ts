import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'app-icon-help-fill, HelpFillIcon',
  template: `
    <svg viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g transform="translate(0.647049, 0.647049)" fill="currentColor" fill-rule="nonzero">
          <path d="M19,9.5 C19,4.27040111 14.7427386,0 9.5,0 C4.27040111,0 0,4.25726141 0,9.5 C0,14.7295989 4.25726141,19 9.5,19 C14.7295989,19 19,14.7295989 19,9.5 Z M9.5,15.3865837 C8.93499308,15.3865837 8.47510373,14.9266943 8.47510373,14.3616874 C8.47510373,13.7966805 8.93499308,13.3367911 9.5,13.3367911 C10.0650069,13.3367911 10.5248963,13.7966805 10.5248963,14.3616874 C10.5248963,14.9266943 10.0650069,15.3865837 9.5,15.3865837 L9.5,15.3865837 Z M11.9965422,9.13208852 C11.8520055,9.27662517 11.6811895,9.40802213 11.4840941,9.56569848 C10.8928077,10.0387275 10.1701245,10.6168741 10.1701245,11.3264177 C10.1701245,11.6943292 9.86791148,11.9965422 9.5,11.9965422 C9.13208852,11.9965422 8.82987552,11.6943292 8.82987552,11.3264177 C8.82987552,9.95988935 9.92047026,9.09266943 10.6562932,8.51452282 C10.8139696,8.38312586 10.9716459,8.2648686 11.0636238,8.17289073 C11.4709544,7.76556017 11.7074689,7.20055325 11.7074689,6.62240664 C11.7074689,5.41355463 10.7219917,4.42807745 9.5131397,4.42807745 C8.30428769,4.42807745 7.31881051,5.41355463 7.31881051,6.62240664 C7.31881051,6.99031812 7.01659751,7.29253112 6.64868603,7.29253112 C6.28077455,7.29253112 5.97856155,6.99031812 5.97856155,6.62240664 C5.97856155,4.66459198 7.56846473,3.08782849 9.5131397,3.08782849 C11.4709544,3.08782849 13.0477178,4.67773167 13.0477178,6.62240664 C13.0345781,7.58160443 12.6666667,8.46196404 11.9965422,9.13208852 Z"></path>
        </g>
      </g>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconHelpFillComponent {

}
