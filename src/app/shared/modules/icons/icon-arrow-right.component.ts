import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'app-icon-arrow-right, ArrowRightIcon',
  template: `
    <svg viewBox="0 0 20 8" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g transform="translate(0.000000, 0.902439)" fill="currentColor">
          <polygon transform="translate(17.000000, 3.141762) rotate(90.000000) translate(-17.000000, -3.141762) " points="17 0.141762452 20.1417625 6.14176245 13.8582375 6.14176245"></polygon>
          <rect x="0" y="2.12005109" width="14" height="2"></rect>
        </g>
      </g>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconArrowRightComponent {

}
