import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'app-icon-play, PlayIcon',
  template: `
    <svg viewBox="0 0 15 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g fill="currentColor" fill-rule="nonzero">
          <path d="M0,17.4771582 L0,2.52291435 C0,0.433563258 2.39762591,-0.747029967 4.05369493,0.526865404 L13.7739654,8.00403138 C15.0843506,9.01197156 15.0843506,10.9880632 13.7739654,11.9960034 L4.05369493,19.4731441 C2.39762591,20.7470143 0,19.5664463 0,17.4771582 Z"></path>
        </g>
      </g>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconPlayComponent {

}
