import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'app-icon-chevron-left, ChevronLeftIcon',
  template: `
    <svg [style.height]="height()" viewBox="0 0 12 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g transform="translate(0.335113, 0.668446)" fill="currentColor" fill-rule="nonzero">
        <path d="M10.4685586,0.397708197 C10.9988361,0.928145961 10.9988361,1.78798561 10.4685586,2.31842337 L3.27878097,9.50820097 L10.4685586,16.6979786 C10.9833371,17.2309688 10.9759748,18.0781829 10.4520113,18.6021465 C9.92804772,19.12611 9.08083361,19.1334723 8.54784337,18.6186937 L0.397708182,10.4685586 C-0.132569394,9.93812079 -0.132569394,9.07828115 0.397708182,8.54784338 L8.54784337,0.397708197 C9.07828114,-0.132569399 9.93812081,-0.132569399 10.4685586,0.397708197 Z"></path>
      </g>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconChevronLeftComponent {
  
  height = input('0.8em');

}
