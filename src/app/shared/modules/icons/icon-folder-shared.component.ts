import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'FolderSharedIcon',
  template: `
    <svg viewBox="0 0 20 18" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="共享文件夹" transform="translate(0.656250, 0.285156)" fill-rule="nonzero">
          <path d="M17.8127655,1.80315225 L8.90744467,1.80315225 L7.12553096,0 L1.18723452,0 C0.530963559,0 0,0.539458976 0,1.2021015 L0,15.6273195 C0,16.2920858 0.530963559,16.829421 1.18723452,16.829421 L17.8127655,16.829421 C18.4690364,16.829421 19,16.2920858 19,15.6273195 L19,3.00525374 C19,2.34261122 18.4690364,1.80315225 17.8127655,1.80315225 Z" id="路径" fill="#4A90E2"></path>
          <path d="M9.01363738,10.9187346 C8.66107758,11.6387212 7.91772859,12.1357031 7.05756763,12.1357031 C5.85758998,12.1357031 4.8869886,11.1714733 4.8869886,9.97999106 C4.8869886,8.78850883 5.85971384,7.82427901 7.05756763,7.82427901 C7.71596244,7.82427901 8.30639392,8.11524704 8.70355466,8.57399955 L9.94600939,7.65012296 C9.79733959,7.42499441 9.71238542,7.15526492 9.71238542,6.86642075 C9.71238542,6.07209926 10.360161,5.42857143 11.160854,5.42857143 C11.9594232,5.42857143 12.6093226,6.07209926 12.6093226,6.86642075 C12.6093226,7.66074223 11.9615471,8.30427006 11.160854,8.30427006 C10.8274089,8.30427006 10.5215739,8.19170579 10.2752068,8.00480662 L8.97753186,8.9711603 C9.13894478,9.2727476 9.23027051,9.61681198 9.23027051,9.97999106 C9.23027051,10.1477755 9.21115582,10.3091885 9.1750503,10.4663537 L10.4684775,10.9803264 C10.7700648,10.5215739 11.2904091,10.2199866 11.8829645,10.2199866 C12.8153365,10.2199866 13.5714286,10.9697071 13.5714286,11.8957076 C13.5714286,12.821708 12.8153365,13.5714286 11.8829645,13.5714286 C10.9505924,13.5714286 10.1945003,12.821708 10.1945003,11.8957076 C10.1945003,11.7279231 10.2199866,11.5665102 10.2645875,11.4157165 L9.01363738,10.9187346 Z" id="路径" fill="#FFFFFF"></path>
        </g>
      </g>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconFolderSharedComponent {

}
