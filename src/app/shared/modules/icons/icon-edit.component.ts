import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'app-icon-edit, EditIcon',
  template: `
    <svg viewBox="0 0 19 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g transform="translate(0.000000, 1.000000)" fill="currentColor" fill-rule="nonzero">
        <path d="M18.2083333,17.4159632 C18.6455588,17.4159632 19,17.7704045 19,18.2076299 C19,18.6448553 18.6455588,18.9992966 18.2083333,18.9992966 L0.791666667,18.9992966 C0.35444124,18.9992966 0,18.6448553 0,18.2076299 C0,17.7704045 0.35444124,17.4159632 0.791666667,17.4159632 L18.2083333,17.4159632 Z M14.4136111,0.606241002 L14.5012222,0.690685463 L16.7258056,2.914741 C17.6134528,3.80271047 17.6501062,5.23033637 16.8091944,6.16268546 L16.7258056,6.25082434 L8.5046111,14.4720188 C8.27238887,14.704241 7.98316667,14.8689077 7.66702777,14.9507132 L7.54722223,14.9776299 L3.1371111,15.8099355 C2.72838215,15.8870984 2.30729244,15.7656407 2.00244331,15.4826558 C1.69759418,15.1996708 1.54519095,14.7887672 1.59177777,14.3754355 L1.60602777,14.2788521 L2.43833333,9.868741 C2.49902777,9.54679657 2.64522223,9.24701877 2.86002777,9.00107434 L2.94394444,8.91135213 L11.1651389,0.690685463 C12.0531084,-0.196961769 13.4807342,-0.233615151 14.4130833,0.607296566 L14.4136111,0.606241002 Z M12.3368056,1.74835213 L12.2771667,1.80218546 L4.05597223,10.0233799 C4.02806522,10.0511841 4.00677921,10.084917 3.99369444,10.1220743 L3.98366667,10.1600743 L3.2231389,14.1922966 L6.1913611,13.6323243 L4.90569444,12.3466577 C4.70272024,12.1488958 4.62184916,11.8572208 4.69400774,11.5831744 C4.76616632,11.309128 4.98019256,11.0951018 5.25423895,11.0229432 C5.52828533,10.9507846 5.81996036,11.0316557 6.01772223,11.2346299 L7.76730556,12.9842132 L15.61325,5.13879657 C15.8984888,4.8538122 15.9219684,4.39926485 15.6676111,4.08640767 L15.61325,4.0267688 L13.3891944,1.80218546 C13.1040707,1.51710578 12.6495305,1.49385448 12.3368056,1.74835213 L12.3368056,1.74835213 Z" id="Shape"></path>
      </g>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconEditComponent {

}
