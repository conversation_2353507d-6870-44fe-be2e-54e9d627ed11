import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'GraphLineIcon',
  template: `
    <svg id="icon-graph-line" viewBox="0 0 20 12">
      <g transform="translate(0.000000, 1.000000)" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <path d="M2,5.5 L7,2.5 M11,3 L15.5,5 L22,1.5 M9,4 C10.1045695,4 11,3.1045695 11,2 C11,0.8954305 10.1045695,0 9,0 C7.8954305,0 7,0.8954305 7,2 C7,3.1045695 7.8954305,4 9,4 Z" stroke-opacity="0.5" stroke="currentColor" stroke-width="2"></path>
        <polyline stroke-opacity="1" stroke="currentColor" stroke-width="2" points="2 10.5 9 6.75 15.5 10 22 6.5"></polyline>
      </g>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconGraphLineComponent {

}
