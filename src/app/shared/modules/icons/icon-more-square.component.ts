import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'app-icon-more-square, MoreSquareIcon',
  template: `
    <svg viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g transform="translate(1.000000, 1.000000)" stroke="currentColor" stroke-width="1.2">
          <rect x="0" y="0" width="7.2" height="7.2" rx="2"></rect>
          <rect x="10.8" y="0" width="7.2" height="7.2" rx="2"></rect>
          <rect x="0" y="10.8" width="7.2" height="7.2" rx="2"></rect>
          <rect x="10.8" y="10.8" width="7.2" height="7.2" rx="2"></rect>
        </g>
      </g>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconMoreSquareComponent {

}
