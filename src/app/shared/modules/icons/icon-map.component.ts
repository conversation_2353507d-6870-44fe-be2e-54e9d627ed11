import { ChangeDetectionStrategy, Component, input, numberAttribute } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'MapIcon',
  template: `
    @switch (style()) {
      @case (1) {
        <svg viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
          <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g id="地图" transform="translate(0.842105, 0.894737)" fill-rule="nonzero">
              <polygon id="路径" fill="#EFEFEF" points="5.9375 7.12499998 2.37499999 8.70833332 0 19 5.14583334 16.2291667"></polygon>
              <path d="M9.5,8.70833332 L5.9375,7.12499998 L5.14583334,16.2291667 L9.5,19 L9.5,8.70833332 Z M13.0625,7.12499998 L16.625,8.70833332 L19,19 L13.8541667,16.2291667 L13.0625,7.12499998 L13.0625,7.12499998 Z" id="形状" fill="#D9D8D7"></path>
              <polygon id="路径" fill="#EFEFEF" points="9.5 8.70833332 13.0625 7.12499998 13.8541667 16.2291667 9.5 19"></polygon>
              <path d="M9.5,0 C6.65803473,0 4.35416666,2.30386804 4.35416666,5.14583331 C4.37407512,5.90637804 4.54354882,6.65555939 4.85291666,7.35062498 C6.068125,10.2916667 9.5,13.8541667 9.5,13.8541667 C9.5,13.8541667 12.9279167,10.2916667 14.143125,7.35062498 C14.4538554,6.65583791 14.6246767,5.90664529 14.6458333,5.14583331 C14.6458333,2.30386804 12.3419653,0 9.5,0 Z" id="路径" fill="#E66847"></path>
              <path d="M6.72916668,5.14583331 C6.72916668,6.6761223 7.96971101,7.91666663 9.5,7.91666663 C11.030289,7.91666663 12.2708333,6.6761223 12.2708333,5.14583331 C12.2708333,3.61554433 11.030289,2.37499999 9.5,2.37499999 C7.96971101,2.37499999 6.72916668,3.61554433 6.72916668,5.14583331 Z" id="路径" fill="#FFFFFF"></path>
            </g>
          </g>
        </svg>
      }
      @case (2) {
        <svg viewBox="0 0 19 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
          <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g id="地图导航" transform="translate(0.437500, 0.710938)" fill-rule="nonzero">
              <path d="M4.59643836,15.5669863 L0.327945205,16.9906849 L0.327945205,5.26013699 L4.59643836,3.83643836 L4.59643836,15.5669863 Z M17.4019178,17.2483562 L13.1308219,18.6720548 L13.1308219,6.94150685 L17.4019178,5.51780822 L17.4019178,17.2483562 Z" id="形状" fill="#F3F5CD"></path>
              <path d="M6.37931507,7.4360274 C7.31890411,7.47767123 8.57863014,8.28191781 9.44534247,9.39068493 C10.3693151,10.5723288 10.7336986,11.9361644 10.4734247,13.229726 C10.3354795,13.9168493 10.0673973,14.3619178 9.65876712,14.5857534 C8.99767123,14.950137 8.15958904,14.6326027 7.41780822,14.3515068 C6.87123288,14.1458904 6.3090411,13.929863 6.1190411,14.1354795 C6.08260274,14.1745205 6.07739726,14.1927397 6.1060274,14.2473973 C6.26739726,14.5467123 7.06383562,14.9657534 8.13356164,15.3119178 C9.13041096,15.6346575 10.1350685,15.8220548 10.520274,15.7569863 C11.9049315,15.5253425 12.9642466,14.4790411 12.9746575,14.4686301 C13.0189041,14.4243836 13.0761644,14.3983562 13.1438356,14.3983562 L13.1438356,6.94150685 L4.59643836,3.83643836 L4.59643836,8.78424658 C4.7109589,8.46150685 4.87232877,8.1569863 5.10136986,7.92013699 C5.43712329,7.5739726 5.86657534,7.41260274 6.37931507,7.4360274 Z" id="路径" fill="#C6C49E"></path>
              <path d="M10.3406849,16.2671233 C9.73945205,16.2671233 8.7660274,16.0406849 7.98,15.7856164 C7.25123288,15.5487671 5.98369863,15.0646575 5.67136986,14.4842466 C5.54383562,14.2473973 5.57506849,13.9975342 5.75726027,13.8023288 C6.17369863,13.3546575 6.86342466,13.6175342 7.59219178,13.8934247 C8.23246575,14.1354795 8.95863014,14.4113699 9.41931507,14.1563014 C9.69,14.0053425 9.88260274,13.6617808 9.98931507,13.1334247 C10.2209589,11.9830137 9.89041096,10.7623288 9.05753425,9.69520548 C8.1830137,8.5760274 7.03780822,7.95917808 6.35849315,7.92794521 C5.98890411,7.90972603 5.69219178,8.0190411 5.45794521,8.26109589 C4.72136986,9.02109589 4.84369863,10.7649315 4.84369863,10.7831507 L4.84369863,10.8013699 C4.84369863,10.9341096 4.73958904,11.0434247 4.60684932,11.0486301 L4.59643836,11.0486301 L4.59643836,15.5669863 L13.1438356,18.6720548 L13.1438356,14.9865753 C12.7612329,15.3171233 11.8060274,16.0458904 10.6009589,16.2463014 C10.5254795,16.2593151 10.439589,16.2671233 10.3406849,16.2671233 L10.3406849,16.2671233 Z" id="路径" fill="#C6C49E"></path>
              <path d="M4.36739726,9.90342466 C4.07068493,9.49219178 3.64123288,8.97164384 3.09986301,8.37041096 C2.8109589,8.05027397 2.47520548,7.74054795 2.16287671,7.88630137 C1.75424658,8.07890411 1.84013699,8.79726027 2.05356164,10.2730137 C2.18630137,11.1865753 2.33726027,12.219863 2.22013699,12.7456164 C2.19150685,12.8757534 2.13424658,13.0371233 2.02753425,13.0631507 C1.69438356,13.1386301 0.929178082,12.4410959 0.515342466,11.9387671 L0.135342466,12.2536986 C0.338356164,12.5009589 1.39246575,13.7216438 2.14205479,13.5420548 C2.33726027,13.4952055 2.59232877,13.3390411 2.70164384,12.8549315 C2.83958904,12.2432877 2.68863014,11.2047945 2.54027397,10.2053425 C2.44917808,9.58328767 2.31643836,8.66972603 2.38150685,8.37821918 C2.44136986,8.41726027 2.55328767,8.50575342 2.73287671,8.70356164 C4.17739726,10.3146575 4.33876712,10.7883562 4.35178082,10.8378082 C4.36739726,10.960137 4.47150685,11.0538356 4.59643836,11.0538356 L4.59643836,8.78945205 C4.46369863,9.1590411 4.39863014,9.55726027 4.36739726,9.90342466 L4.36739726,9.90342466 Z" id="路径" fill="#F4CD7D"></path>
              <path d="M12.9746575,14.4738356 C12.9642466,14.4842466 11.9049315,15.5305479 10.520274,15.7621918 C10.1324658,15.8272603 9.12780822,15.639863 8.13356164,15.3171233 C7.06383562,14.9683562 6.26739726,14.5519178 6.1060274,14.2526027 C6.07739726,14.1979452 6.08260274,14.179726 6.1190411,14.1406849 C6.3090411,13.9350685 6.87383562,14.1510959 7.41780822,14.3567123 C8.15958904,14.6378082 8.99767123,14.9553425 9.65876712,14.5909589 C10.0673973,14.3645205 10.3354795,13.9194521 10.4734247,13.2349315 C10.7336986,11.9413699 10.3693151,10.5775342 9.44534247,9.39589041 C8.57863014,8.28712329 7.31890411,7.48287671 6.37931507,7.44123288 C5.86917808,7.41780822 5.43712329,7.57917808 5.1039726,7.92534247 C4.87232877,8.16219178 4.7109589,8.46671233 4.5990411,8.78945205 L4.5990411,11.0538356 L4.60945205,11.0538356 C4.74219178,11.0486301 4.84630137,10.9393151 4.84630137,10.8065753 L4.84630137,10.7883562 C4.84369863,10.770137 4.7239726,9.02630137 5.46054795,8.26630137 C5.69479452,8.02424658 5.98890411,7.91493151 6.36109589,7.93315068 C7.04041096,7.96438356 8.18821918,8.58123288 9.06013699,9.70041096 C9.8930137,10.7675342 10.2261644,11.9882192 9.99191781,13.1386301 C9.88520548,13.6669863 9.69260274,14.0131507 9.42191781,14.1615068 C8.96123288,14.4165753 8.23506849,14.1406849 7.59479452,13.8986301 C6.86342466,13.6227397 6.17369863,13.359863 5.75986301,13.8075342 C5.57767123,14.0027397 5.54643836,14.2526027 5.6739726,14.4894521 C5.98630137,15.069863 7.25643836,15.5539726 7.98260274,15.7908219 C8.76863014,16.0458904 9.74205479,16.2723288 10.3432877,16.2723288 C10.439589,16.2723288 10.5280822,16.2671233 10.6035616,16.2541096 C11.8060274,16.0536986 12.7638356,15.3249315 13.1464384,14.9943836 L13.1464384,14.4009589 C13.0761644,14.4035616 13.0189041,14.429589 12.9746575,14.4738356 Z" id="路径" fill="#CCA766"></path>
              <path d="M17.5763014,9.16164384 L17.2535616,8.78945205 C17.1728767,8.85972603 15.259863,10.5384932 15.0360274,12.3890411 C14.9293151,13.2661644 15.1141096,13.8309589 15.2468493,14.2421918 C15.3926027,14.6872603 15.4394521,14.8382192 15.1193151,15.1141096 C14.4868493,15.6606849 13.3416438,14.4868493 13.3312329,14.4764384 C13.2843836,14.4269863 13.2219178,14.4009589 13.1542466,14.3983562 L13.1464384,14.3983562 L13.1464384,14.9839726 C13.5628767,15.3613699 14.6378082,16.1812329 15.4420548,15.4889041 C16.0120548,14.9969863 15.8689041,14.5571233 15.7179452,14.0886301 C15.590411,13.7008219 15.4342466,13.2167123 15.5279452,12.4463014 C15.7231507,10.7857534 17.5554795,9.17726027 17.5763014,9.16164384 L17.5763014,9.16164384 Z" id="路径" fill="#F4CD7D"></path>
              <path d="M13.1412329,9.15643836 C13.0917808,9.12260274 13.0371233,9.08876712 12.9772603,9.0939726 C12.9173973,9.09917808 12.8627397,9.16164384 12.8861644,9.21630137 L12.8393151,9.21630137 C12.6336986,9.01328767 12.3473973,8.89356164 12.0558904,8.89356164 C11.9908219,8.89356164 11.9205479,8.89876712 11.8658904,8.93520548 C11.7747945,8.99506849 11.7461644,9.11479452 11.7279452,9.22410959 C11.6941096,9.41410959 11.6628767,9.61712329 11.7331507,9.79671233 C11.8554795,10.1090411 12.230274,10.2417808 12.4176712,10.5176712 C12.6363014,10.8352055 12.5634247,11.2646575 12.4515068,11.6316438 C12.3942466,11.8216438 12.3291781,12.0220548 12.3786301,12.2146575 C12.4384932,12.4436986 12.6545205,12.6076712 12.8835616,12.670137 C12.9694521,12.6935616 13.0553425,12.7065753 13.1438356,12.7091781 L13.1412329,9.15643836 C13.1438356,9.15643836 13.1412329,9.15643836 13.1412329,9.15643836 L13.1412329,9.15643836 Z" id="路径" fill="#68BBD8"></path>
              <path d="M13.5915068,12.6935616 C13.6669863,12.6883562 13.7476712,12.6779452 13.8049315,12.6310959 C13.8882192,12.5634247 13.8960274,12.4410959 13.9168493,12.3343836 C13.9558904,12.1079452 14.070411,11.8945205 14.2369863,11.7357534 C14.2942466,11.6810959 14.3619178,11.6316438 14.44,11.6212329 C14.5649315,11.6030137 14.6794521,11.6889041 14.7991781,11.7305479 C15.0984932,11.8320548 15.4368493,11.6420548 15.5982192,11.3713699 C15.759589,11.1006849 15.7856164,10.770137 15.7908219,10.4552055 C15.7934247,10.2391781 15.7882192,10.0179452 15.7127397,9.81493151 C15.6372603,9.61191781 15.4758904,9.42972603 15.2650685,9.38027397 C14.8954795,9.29438356 14.5753425,9.62232877 14.3176712,9.89821918 C14.2552055,9.96328767 14.1849315,10.0335616 14.0964384,10.0413699 C13.9532877,10.0517808 13.8543836,9.91123288 13.7736986,9.79410959 C13.6045205,9.54684932 13.3910959,9.33082192 13.1464384,9.16164384 L13.1464384,12.7091781 C13.2921918,12.719589 13.4431507,12.7065753 13.5915068,12.6935616 L13.5915068,12.6935616 Z" id="路径" fill="#8DDCFF"></path>
              <path d="M4.3960274,3.7609589 C3.8130137,3.69068493 3.02178082,3.91452055 2.49863014,4.22945205 C2.19671233,4.30753425 1.93123288,4.43506849 1.85315068,4.66931507 C1.55383562,5.56205479 2.69643836,6.37410959 3.46684932,5.97849315 C3.6490411,5.98630137 3.73753425,6.20493151 3.93534247,6.31945205 C4.13835616,6.43917808 4.37,6.47821918 4.5990411,6.45479452 L4.5990411,3.79219178 C4.52356164,3.77657534 4.45589041,3.76616438 4.3960274,3.7609589 Z M4.39342466,11.5561644 C4.33616438,11.5978082 4.27369863,11.6472603 4.25547945,11.7149315 C4.23726027,11.7852055 4.28671233,11.8736986 4.3569863,11.8658904 L4.33876712,11.9205479 C4.02643836,12.0715068 3.7739726,12.3473973 3.65684932,12.6727397 C3.63082192,12.7482192 3.61,12.8289041 3.62821918,12.9043836 C3.65945205,13.0319178 3.78438356,13.1126027 3.89890411,13.1776712 C4.10191781,13.2921918 4.31534247,13.4093151 4.5469863,13.4041096 C4.56520548,13.4041096 4.58082192,13.4015068 4.59643836,13.3989041 L4.59643836,11.4260274 C4.52616438,11.4650685 4.45849315,11.5093151 4.39342466,11.5561644 L4.39342466,11.5561644 Z" id="形状" fill="#A9D335"></path>
              <path d="M5.87958904,4.49753425 C5.73123288,4.09931507 5.05452055,3.87808219 4.59643836,3.78958904 L4.59643836,6.45219178 C4.88273973,6.42356164 5.16383562,6.29082192 5.37205479,6.0930137 C5.72082192,5.75986301 6.06178082,4.98424658 5.87958904,4.49753425 Z M6.90767123,13.3390411 C7.09767123,13.479589 7.29808219,13.6357534 7.53493151,13.6591781 C7.81863014,13.6852055 8.08931507,13.5082192 8.25328767,13.2739726 C8.41726027,13.0423288 8.49534247,12.7586301 8.56821918,12.4853425 C8.59164384,12.3968493 8.61506849,12.3031507 8.58383562,12.2172603 C8.53958904,12.0949315 8.40684932,12.0350685 8.29232877,11.9726027 C8.05287671,11.8372603 7.85767123,11.6212329 7.74575342,11.3687671 C7.70671233,11.2828767 7.67808219,11.1865753 7.69890411,11.0928767 C7.73013699,10.9445205 7.87328767,10.8508219 7.9669863,10.7310959 C8.20383562,10.4343836 8.12575342,9.97630137 7.88630137,9.68219178 C7.64684932,9.38808219 7.28506849,9.22671233 6.92849315,9.09136986 C6.68643836,9.00027397 6.4339726,8.9169863 6.17369863,8.91958904 C5.91342466,8.92479452 5.64273973,9.03150685 5.50219178,9.24753425 C5.25493151,9.63013699 5.49438356,10.1246575 5.70260274,10.5280822 C5.75205479,10.6243836 5.80150685,10.7310959 5.77287671,10.8378082 C5.72863014,11.0017808 5.52821918,11.0590411 5.36164384,11.1006849 C5.0909589,11.1709589 4.83068493,11.280274 4.59123288,11.4208219 L4.59123288,13.3936986 C4.96863014,13.3572603 5.26013699,13.0136986 5.63232877,12.9173973 C6.08520548,12.8054795 6.53808219,13.0631507 6.90767123,13.3390411 L6.90767123,13.3390411 Z" id="形状" fill="#7D992E"></path>
              <path d="M13.1438356,19 C13.1047945,19 13.0683562,18.9921918 13.0319178,18.9791781 L4.59123288,15.9131507 L0.429452055,17.300411 C0.330547945,17.3342466 0.221232877,17.3160274 0.135342466,17.2561644 C0.0494520548,17.1936986 0,17.0947945 0,16.9906849 L0,5.26013699 C0,5.11958904 0.0910958904,4.99465753 0.223835616,4.95041096 L4.49232877,3.52671233 C4.56260274,3.50328767 4.63808219,3.50328767 4.70835616,3.52931507 L13.1490411,6.59534247 L17.3108219,5.20808219 C17.409726,5.17424658 17.5190411,5.19246575 17.6049315,5.25232877 C17.6908219,5.31479452 17.740274,5.41369863 17.740274,5.51780822 L17.740274,17.2483562 C17.740274,17.3889041 17.6491781,17.5138356 17.5164384,17.5580822 L13.2479452,18.9817808 C13.2141096,18.9947945 13.1776712,19 13.1438356,19 L13.1438356,19 Z M4.59643836,15.2390411 C4.63547945,15.2390411 4.67191781,15.2468493 4.70835616,15.259863 L13.1490411,18.3258904 L17.0869863,17.0115068 L17.0869863,5.97068493 L13.2479452,7.25123288 C13.1776712,7.27465753 13.1021918,7.27465753 13.0319178,7.24863014 L4.59123288,4.18260274 L0.653287671,5.4969863 L0.653287671,16.5378082 L4.49232877,15.2572603 C4.52616438,15.2442466 4.56260274,15.2390411 4.59643836,15.2390411 L4.59643836,15.2390411 Z" id="形状" fill="#592900"></path>
              <path d="M8.19342466,10.8013699 C8.19342466,11.0514868 8.8937617,11.2542466 9.75767123,11.2542466 C10.6215808,11.2542466 11.3219178,11.0514868 11.3219178,10.8013699 C11.3219178,10.551253 10.6215808,10.3484932 9.75767123,10.3484932 C8.8937617,10.3484932 8.19342466,10.551253 8.19342466,10.8013699 Z" id="路径" fill="#999881"></path>
              <path d="M9.75767123,0.260273973 C7.88369863,0.260273973 6.36630137,1.77767123 6.36630137,3.65164384 C6.36630137,6.36369863 9.75767123,10.3510959 9.75767123,10.3510959 C9.75767123,10.3510959 13.1490411,6.25178082 13.1490411,3.65164384 C13.1490411,1.77767123 11.6316438,0.260273973 9.75767123,0.260273973 L9.75767123,0.260273973 Z M9.75767123,4.69013699 C9.12260274,4.69013699 8.60465753,4.17479452 8.60465753,3.53712329 C8.60465753,2.90205479 9.12,2.38410959 9.75767123,2.38410959 C10.3953425,2.38410959 10.9106849,2.89945205 10.9106849,3.53712329 C10.9106849,4.17479452 10.3953425,4.69013699 9.75767123,4.69013699 Z" id="形状" fill="#F04E2F"></path>
              <path d="M9.75767123,10.6113699 C9.68219178,10.6113699 9.60931507,10.5775342 9.55726027,10.520274 C9.41671233,10.3536986 6.1060274,6.42876712 6.1060274,3.65164384 C6.1060274,1.63712329 7.74575342,9.46871196e-16 9.75767123,9.46871196e-16 C11.769589,9.46871196e-16 13.4093151,1.63972603 13.4093151,3.65164384 C13.4093151,6.31424658 10.0986301,10.3458904 9.95808219,10.5176712 C9.91123288,10.5749315 9.83835616,10.6113699 9.75767123,10.6113699 C9.76027397,10.6113699 9.75767123,10.6113699 9.75767123,10.6113699 L9.75767123,10.6113699 Z M9.75767123,0.520547945 C8.03205479,0.520547945 6.62917808,1.92342466 6.62917808,3.6490411 C6.62917808,5.85876712 9.03410959,9.02890411 9.75506849,9.93205479 C10.4734247,9.01328767 12.886168,5.76767123 12.886168,3.6490411 C12.8887671,1.9260274 11.4832877,0.520547945 9.75767123,0.520547945 L9.75767123,0.520547945 Z M9.75767123,4.9530137 C8.97684932,4.9530137 8.34438356,4.31794521 8.34438356,3.53972603 C8.34438356,2.75890411 8.97945205,2.12643836 9.75767123,2.12643836 C10.5384932,2.12643836 11.1709589,2.76150685 11.1709589,3.53972603 C11.1709589,4.31794521 10.5384932,4.9530137 9.75767123,4.9530137 Z M9.75767123,2.6469863 C9.26575342,2.6469863 8.86753425,3.04520548 8.86753425,3.53712329 C8.86753425,4.0290411 9.26575342,4.42726027 9.75767123,4.42726027 C10.249589,4.42726027 10.6478082,4.02643836 10.6478082,3.53712329 C10.6478082,3.04780822 10.249589,2.6469863 9.75767123,2.6469863 L9.75767123,2.6469863 Z" id="形状" fill="#592900"></path>
              <path d="M7.4360274,3.12328767 C7.41780822,3.12328767 7.3969863,3.12068493 7.37876712,3.11547945 C7.27465753,3.08424658 7.21739726,2.97493151 7.24863014,2.87082192 C7.44123288,2.23575342 7.85767123,1.68917808 8.42506849,1.33780822 C8.51616438,1.28054795 8.63849315,1.30917808 8.69575342,1.40027397 C8.7530137,1.49136986 8.72438356,1.61369863 8.63328767,1.6709589 C8.14864469,1.9717727 7.79047526,2.43915141 7.6260274,2.98534247 C7.59739726,3.06863014 7.51931507,3.12328767 7.4360274,3.12328767 L7.4360274,3.12328767 Z" id="路径" fill="#FFFFFF"></path>
              <path d="M7.33191781,4.0290411 C7.23041096,4.0290411 7.14191781,3.94835616 7.13671233,3.84684932 C7.13150685,3.7869863 7.13150685,3.72712329 7.13150685,3.66465753 C7.13150685,3.56315068 7.13671233,3.4590411 7.14972603,3.35493151 C7.16273973,3.24821918 7.2590411,3.17013699 7.36575342,3.18054795 C7.47246575,3.19356164 7.55054795,3.28986301 7.54013699,3.39657534 C7.52972603,3.48506849 7.52452055,3.57356164 7.52452055,3.66205479 C7.52452055,3.71410959 7.52712329,3.76616438 7.52972603,3.81821918 C7.53753425,3.92753425 7.45424658,4.01863014 7.34753425,4.02643836 C7.34232877,4.0290411 7.33712329,4.0290411 7.33191781,4.0290411 L7.33191781,4.0290411 Z" id="路径" fill="#FFFFFF"></path>
            </g>
          </g>
        </svg>
      }
      @case (3) {
        <svg viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
          <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g id="graphic-map" fill-rule="nonzero">
              <polygon fill="#FFECB3" points="13.3332 2.2224 6.6668 0 0 2.2224 0 20 6.6668 17.7776 13.3332 20 20 17.7776 20 0"></polygon>
              <polygon fill="#FFE082" points="6.6668 0 6.6668 17.7776 13.3332 20 13.3332 2.2224"></polygon>
              <path d="M13.3332,5 C11.1852,5 9.4444,6.8296 9.4444,9.084 C9.4444,11.3384 13.3332,16.6668 13.3332,16.6668 C13.3332,16.6668 17.2224,11.3376 17.2224,9.084 C17.2224,6.83 15.4816,5 13.3332,5 Z" id="路径" fill="#F44336"></path>
              <path d="M11.6664,8.8888 C11.6664,9.4842903 11.9840903,10.034546 12.4998,10.3322912 C13.0155097,10.6300364 13.6508903,10.6300364 14.1666,10.3322912 C14.6823097,10.034546 15,9.4842903 15,8.8888 C15,8.2933097 14.6823097,7.74305396 14.1666,7.4453088 C13.6508903,7.14756364 13.0155097,7.14756364 12.4998,7.4453088 C11.9840903,7.74305396 11.6664,8.2933097 11.6664,8.8888 Z" id="路径" fill="#FFEBEE"></path>
            </g>
          </g>
        </svg>
      }
    }
    
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconMapComponent {

  style = input(1, { transform: numberAttribute });

}
