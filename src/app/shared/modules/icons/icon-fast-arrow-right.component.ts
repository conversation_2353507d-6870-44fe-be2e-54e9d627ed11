import { ChangeDetectionStrategy, Component, input, numberAttribute } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'FastArrowRightIcon',
  template: `
    @switch (style()) {
      @case (1) {
        <svg viewBox="0 0 20 18" version="1.1" xmlns="http://www.w3.org/2000/svg">
          <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round">
            <g transform="translate(1.000000, 1.000000)" stroke="currentColor" stroke-width="1.5">
              <polyline points="10.2857143 0 18 7.71428571 10.2857143 15.4285714"></polyline>
              <polyline points="0 0 7.71428571 7.71428571 0 15.4285714"></polyline>
            </g>
          </g>
        </svg>
      }
      @case (2) {
        <svg viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
          <g transform="translate(0.780952, 0.619048)" fill="currentColor" fill-rule="nonzero">
            <polygon points="9.49020208 0 19 9.5076206 9.49020208 19.0152412 8.25493717 17.7814278 16.530196 9.5076206 8.25638869 1.23381336 9.4916536 0"></polygon>
            <polygon points="1.23526491 0 10.7428855 9.5076206 1.23526491 19.0152412 0 17.7814278 8.27380724 9.5076206 0 1.23381336"></polygon>
          </g>
        </svg>
      }
    }
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconFastArrowRightComponent {

  style = input(1, { transform: numberAttribute });

}
