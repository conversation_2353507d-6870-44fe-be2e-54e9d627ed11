import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'app-icon-marketing, MarketingIcon',
  template: `
    <svg viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g transform="translate(0.552711, 0.531711)" fill="currentColor" fill-rule="nonzero">
          <path d="M5.23897401,15.6336891 C5.13946092,15.6336891 5.04016002,15.6118344 4.94849764,15.5685494 C4.7857162,15.4915036 4.66017652,15.3529903 4.59945921,15.1834401 L4.11101758,13.8178405 C3.85364152,13.837149 3.5877782,13.8191136 3.3159738,13.7633099 C2.86890754,13.6718597 2.4252362,13.4832304 1.99726627,13.2025144 C1.18694543,12.6712121 0.525363761,11.8566476 0.227248866,11.0234112 C-0.070866029,10.1901748 -0.0759583831,9.14072545 0.213669255,8.21603882 C0.366652059,7.72759719 0.590079094,7.30047599 0.877584918,6.94613302 C1.05263459,6.7305567 1.24656841,6.54808068 1.45768892,6.39976586 L0.965640208,5.02419372 C0.839392263,4.67112384 1.02314137,4.28240747 1.37621126,4.15615953 L12.8838706,0.0398399816 C13.2369405,-0.0864079633 13.6256568,0.0973411464 13.7519048,0.450411029 L14.2439535,1.82598318 C14.5013295,1.80667467 14.7671929,1.82471009 15.0389973,1.8805138 C15.4860635,1.97196399 15.9297349,2.16059327 16.3577048,2.44130929 C17.1680256,2.97261157 17.8296073,3.78717604 18.1277222,4.62041248 C18.4258371,5.45364891 18.4309294,6.50309822 18.1413018,7.42778484 C17.988319,7.91622647 17.764892,8.34334767 17.4773861,8.69769064 C17.3023365,8.91326697 17.1084026,9.09574299 16.8972821,9.2440578 L17.3857238,10.6096574 C17.4464259,10.779183 17.4372665,10.9658834 17.360262,11.1286532 C17.2832162,11.2914346 17.1447029,11.4169743 16.9751527,11.4776916 L5.4674934,15.5940112 C5.39417353,15.620298 5.31686368,15.6336891 5.23897401,15.6336891 L5.23897401,15.6336891 Z M4.55299148,12.3578201 C4.65250456,12.3578201 4.75180547,12.3796748 4.84346784,12.4229598 C5.00624928,12.5000056 5.13178897,12.638519 5.19250628,12.8080691 L5.64975724,14.0866743 L15.8788113,10.4278179 L15.4213482,9.1492127 C15.2951002,8.79614282 15.4788493,8.40742646 15.8319192,8.28117852 C16.2753784,8.12246681 16.6449984,7.66373058 16.845722,7.02209397 C17.0470822,6.37918427 17.0483553,5.6342153 16.8493291,5.07787562 C16.650303,4.52153593 16.176714,3.94631211 15.6133724,3.57711643 C15.0510916,3.20855731 14.4743825,3.08803826 14.0309233,3.24674996 C13.6778534,3.37299791 13.2891371,3.1892488 13.1628891,2.83617892 L12.7020311,1.54760115 L2.4731892,5.20624538 L2.93404724,6.49482314 C3.06029519,6.84789302 2.87654608,7.23660938 2.52347619,7.36285733 C2.08001703,7.52156903 1.71039699,7.98030526 1.50967337,8.62194188 C1.3083132,9.26485158 1.30704011,10.0098205 1.50606628,10.5661602 C1.70509246,11.1224999 2.17868139,11.6977237 2.74202306,12.0669194 C3.30430382,12.4354785 3.88101292,12.5559976 4.32447209,12.3972859 C4.39783021,12.3711683 4.47512274,12.3578198 4.55299148,12.3578201 L4.55299148,12.3578201 Z"></path>
          <path d="M15.6050973,19.0044031 L3.38344749,19.0044031 C3.00852292,19.0044031 2.70446694,18.7003471 2.70446694,18.3254226 L2.70446694,16.8736773 C2.15300743,16.7209067 1.62616097,16.3845991 1.17548763,15.8904286 C0.635273737,15.2978059 0.232129039,14.5025499 0.069598071,13.7085671 C-0.00551415168,13.341281 0.231280313,12.9824822 0.598566351,12.9071578 C0.96606457,12.8320456 1.32465117,13.0688401 1.39997557,13.4361261 C1.61491535,14.4855754 2.46300449,15.6092882 3.38344749,15.6092882 C3.75837206,15.6092882 4.06242803,15.9133442 4.06242803,16.2882688 L4.06242803,17.6462298 L14.9261167,17.6462298 L14.9261167,16.2882688 C14.9261167,15.9133442 15.2301727,15.6092882 15.6050973,15.6092882 C16.07614,15.6092882 16.5785856,15.3016252 16.9838521,14.7652305 C17.3899674,14.227775 17.6420389,13.5267276 17.6420389,12.9358023 C17.6420389,12.6712121 17.5911154,12.3767043 17.4945728,12.0845305 C17.3770243,11.72849 17.5705338,11.3444417 17.9265742,11.2268932 C18.2826146,11.1093447 18.666663,11.3028541 18.7842115,11.6588945 C18.927434,12.0928055 19,12.5224729 19,12.9358023 C19,13.820811 18.6513859,14.8106374 18.0672505,15.5838264 C17.7587387,15.9922757 17.4046079,16.3192472 17.0144063,16.5560417 C16.7769753,16.7001129 16.5329666,16.806628 16.2840778,16.8751626 L16.2840778,18.3254226 C16.2840778,18.7003471 15.9800218,19.0044031 15.6050973,19.0044031 L15.6050973,19.0044031 Z M6.75628334,9.87614625 L4.4522053,10.7064121 L4.13181136,9.81758418 L6.4358894,8.98731828 L6.75628334,9.87614625 L6.75628334,9.87614625 Z M6.76349751,6.91345708 C6.5854773,6.41971092 6.57996058,5.9745543 6.74694736,5.57798722 C6.91393414,5.18163233 7.23135754,4.89900668 7.69921757,4.73053463 C8.14628382,4.56948894 8.54263872,4.57924928 8.88828225,4.76024003 C9.23392578,4.94123078 9.49236275,5.26947544 9.66380534,5.744974 C9.83991592,6.23362781 9.84309864,6.6745408 9.6733535,7.06750079 C9.50360837,7.46046078 9.18894332,7.73990371 8.72872182,7.9056174 C8.29735699,8.06114638 7.90248737,8.04777895 7.5447495,7.86530293 C7.18701162,7.68303909 6.92645284,7.36561569 6.76349751,6.91345708 L6.76349751,6.91345708 Z M7.60585775,6.56144811 C7.80721791,7.12033397 8.11318352,7.32572558 8.52396675,7.17762295 C8.94238851,7.02676196 9.04678177,6.66032465 8.83693435,6.07809883 C8.63366454,5.51412062 8.33194257,5.30427319 7.93155623,5.44834438 C7.50273758,5.60302463 7.39410069,5.97391775 7.60585775,6.56144811 L7.60585775,6.56144811 Z M11.9939817,3.27518227 L10.2456068,10.7329348 L9.26702608,11.0855803 L11.0075503,3.63058615 L11.9939817,3.27518227 L11.9939817,3.27518227 Z M11.6105699,8.62894386 C11.43064,8.12989316 11.4266086,7.68282691 11.5984755,7.28753293 C11.7703425,6.89245112 12.0875537,6.61131074 12.5501092,6.44453614 C12.9971754,6.28349045 13.3924694,6.29070461 13.7364155,6.46639083 C14.0801494,6.64207705 14.3387986,6.9703217 14.5119386,7.4511248 C14.6899588,7.94508315 14.6929294,8.38960322 14.5212746,8.78468503 C14.3494076,9.17997901 14.0349548,9.4598463 13.5774916,9.62471127 C13.148673,9.77917934 12.7544399,9.76496319 12.3947924,9.58185062 C12.0349327,9.39895024 11.7735252,9.08131465 11.6105699,8.62894386 L11.6105699,8.62894386 Z M12.4488987,8.27842016 C12.6513197,8.83985219 12.9589828,9.04609253 13.3723122,8.89714118 C13.7881878,8.7473411 13.891308,8.38132815 13.6814605,7.79910233 C13.5832205,7.52623702 13.4523046,7.33633465 13.2889249,7.22918304 C13.1255452,7.1222436 12.9515564,7.10187418 12.7669586,7.16828697 C12.3432323,7.32105759 12.2373538,7.69110199 12.4488987,8.27842016 Z"></path>
        </g>
      </g>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconMarketingComponent {

}
