import { ChangeDetectionStrategy, Component, input, numberAttribute } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'app-icon-card, CardIcon',
  template: `
    @switch (style()) {
      @case (1) {
        <svg viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
          <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g fill-rule="nonzero">
              <path d="M0,10 C0,15.5228475 4.4771525,20 10,20 C15.5228475,20 20,15.5228475 20,10 C20,4.4771525 15.5228475,0 10,0 C4.4771525,0 0,4.4771525 0,10 Z" fill="#FF5E7C"></path>
              <g transform="translate(4.770070, 6.001559)">
                <path d="M1.4029618,7.10542736e-16 L9.05689789,7.10542736e-16 C9.99220577,7.10542736e-16 10.4598597,0.467653939 10.4598597,1.40296182 L10.4598597,6.59392049 C10.4598597,7.52922837 9.99220576,7.9968823 9.05689789,7.9968823 L1.4029618,7.9968823 C0.467653919,7.9968823 0,7.52922837 0,6.59392049 L0,1.40296182 C0,0.467653939 0.467653932,7.10542736e-16 1.4029618,7.10542736e-16 Z" fill="#FFFFFF"></path>
                <polygon fill="#FF5E7C" points="0 1.69914264 10.4598597 1.69914264 10.4598597 3.22681215 0 3.22681215"></polygon>
                <path d="M8.81703818,5.26890102 L7.12935307,5.26890102 C7.04662893,5.26890102 6.97220944,5.31918248 6.94134061,5.39593141 L6.62787217,6.17535463 C6.5743258,6.30852688 6.67236164,6.45362432 6.81588463,6.45362432 L8.54140295,6.45362432 C8.62728265,6.45362432 8.70383979,6.39950129 8.73247076,6.31853469 L9.00809041,5.53911146 C9.05473109,5.40724865 8.95691348,5.26890102 8.81703818,5.26890102 L8.81703818,5.26890102 Z" fill="#FF5E7C"></path>
              </g>
            </g>
          </g>
        </svg>
      }
      @case (2) {
        <svg viewBox="0 0 20 17" version="1.1" xmlns="http://www.w3.org/2000/svg">
          <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
            <g fill="currentColor" fill-rule="nonzero">
              <path d="M20,6.875 L20,14.375 C20,14.8722809 19.8024559,15.3491945 19.4508252,15.7008252 C19.0991945,16.0524559 18.6222809,16.25 18.125,16.25 L1.875,16.25 C0.839466093,16.25 9.32587341e-16,15.4105339 9.32587341e-16,14.375 L9.32587341e-16,6.875 L20,6.875 Z M6.25000001,11.25 L3.125,11.25 L3.125,13.125 L6.25000001,13.125 L6.25000001,11.25 Z M18.125,0 C18.6222809,0 19.0991945,0.197544075 19.4508252,0.549174784 C19.8024559,0.900805493 20,1.37771908 20,1.875 L20,5 L9.32587341e-16,5 L9.32587341e-16,1.875 C9.32587341e-16,0.839466093 0.839466093,0 1.875,0 L18.125,0 L18.125,0 Z" id="形状"></path>
            </g>
          </g>
        </svg>
      }
    }
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconCardComponent {

  style = input(1, { transform: numberAttribute });
  
}
