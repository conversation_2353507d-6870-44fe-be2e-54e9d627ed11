import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'app-icon-Transfer, TransferIcon',
  template: `
    <svg viewBox="0 0 20 19" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g transform="translate(0.051470, 0.201504)" fill="currentColor" fill-rule="nonzero">
          <path d="M11.762431,1.2829184 C12.0240719,0.828350472 11.7941451,0.24692638 11.2774705,0.153105677 C7.82079938,-0.483145342 4.30218508,0.894630022 2.19661605,3.70887407 C0.0910470226,6.52311811 -0.23764254,10.2875403 1.34833266,13.4241106 L0.998156798,13.3302899 C0.724340352,13.2571148 0.432285077,13.3355776 0.232005369,13.5361221 C0.0317256618,13.7366665 -0.0463512264,14.0288252 0.0271855128,14.3025447 C0.100722252,14.5762643 0.314700639,14.7899602 0.588517085,14.8631353 L2.88514226,15.4775948 C3.23515232,15.5713638 3.60435714,15.4171665 3.78370676,15.102312 C3.95852728,14.7841684 3.91800036,14.3910047 3.68195755,14.1152124 C1.65577877,11.6479643 1.37126645,8.18311713 2.96782715,5.41839149 C4.56438785,2.65366586 7.70756824,1.16822649 10.8572594,1.68991528 C11.2140424,1.74805768 11.5813967,1.59741599 11.762431,1.2829184 L11.762431,1.2829184 Z M7.1691807,17.1676889 C7.3626299,16.8493724 7.73688955,16.6890512 8.10078066,16.7686206 C11.3244999,17.4151534 14.6014431,15.9334738 16.2453501,13.0860294 C17.8892572,10.2385849 17.5338337,6.65984141 15.3619748,4.19136031 C15.163793,3.96716103 15.0882347,3.65998935 15.1597977,3.36943806 C15.2142944,3.16634528 15.3472375,2.99321942 15.5293808,2.88814625 C15.7115241,2.78307308 15.9279476,2.75465967 16.1310403,2.80915666 L18.4276654,3.42493763 C18.8420853,3.5450434 19.0843804,3.97449515 18.9728924,4.3913161 C18.8614045,4.80813704 18.4370685,5.05928368 18.0180257,4.95646154 L17.7643134,4.88906921 C19.4686134,8.07186955 19.165326,11.9553955 16.9875753,14.8350237 C14.8098245,17.7146518 11.1556617,19.0640411 7.6290343,18.2908946 C7.1268953,18.1812168 6.9115041,17.6116855 7.16785928,17.1676889 L7.1691807,17.1676889 Z"></path>
          <path d="M11.1836498,5.49163599 L9.50809126,7.1645517 L7.83253273,5.49163599 C7.57328451,5.24124483 7.16119569,5.24482542 6.90633752,5.49968359 C6.65147934,5.75454177 6.64789875,6.16663059 6.89828991,6.42587881 L7.91313925,7.4394067 L6.86525446,7.4394067 C6.50035486,7.43940672 6.20454526,7.7352163 6.20454526,8.1001159 C6.20454526,8.46501551 6.50035486,8.76082509 6.86525446,8.7608251 L8.84738207,8.7608251 L8.84738207,10.0822435 L6.86525446,10.0822435 C6.50035485,10.0822435 6.20454526,10.3780531 6.20454526,10.7429527 C6.20454526,11.1078523 6.50035485,11.4036619 6.86525446,11.4036619 L8.84738207,11.4036619 L8.84738207,13.3857895 C8.84738207,13.7506891 9.14319165,14.0464987 9.50809126,14.0464987 C9.87299087,14.0464987 10.1688004,13.7506891 10.1688004,13.3857895 L10.1688004,11.4036619 L12.1509281,11.4036619 C12.5158277,11.4036619 12.8116373,11.1078523 12.8116373,10.7429527 C12.8116373,10.3780531 12.5158277,10.0822435 12.1509281,10.0822435 L10.1688004,10.0822435 L10.1688004,8.7608251 L12.1509281,8.7608251 C12.5158277,8.7608251 12.8116373,8.46501552 12.8116373,8.1001159 C12.8116373,7.73521629 12.5158277,7.4394067 12.1509281,7.4394067 L11.1030433,7.4394067 L12.1178926,6.42587881 C12.3682838,6.16663059 12.3647032,5.75454177 12.109845,5.49968359 C11.8549868,5.24482542 11.442898,5.24124483 11.1836498,5.49163599 L11.1836498,5.49163599 Z" id="路径"></path>
        </g>
      </g>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconTransferComponent {

}
