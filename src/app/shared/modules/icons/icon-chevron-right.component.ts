import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'app-icon-chevron-right, ChevronRightIcon',
  template: `
    <svg [style.height]="height()" viewBox="0 0 11 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g transform="translate(0.001780, 0.769070)" fill="currentColor" fill-rule="nonzero">
        <path d="M0.397708182,18.6022918 C-0.132569394,18.071854 -0.132569394,17.2120144 0.397708182,16.6815766 L7.58748578,9.49179903 L0.397708182,2.30202143 C-0.117070394,1.76903118 -0.109708067,0.921817064 0.414255481,0.397853524 C0.938219029,-0.126110015 1.78543314,-0.133472328 2.31842338,0.381306256 L10.4685586,8.53144144 C10.9988361,9.06187921 10.9988361,9.92171885 10.4685586,10.4521566 L2.31842338,18.6022918 C1.78798562,19.1325694 0.928145949,19.1325694 0.397708182,18.6022918 L0.397708182,18.6022918 Z"></path>
      </g>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconChevronRightComponent {

  height = input('0.8em');

}
