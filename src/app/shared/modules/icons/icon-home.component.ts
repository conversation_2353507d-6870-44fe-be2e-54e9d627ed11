import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'app-icon-home, HomeIcon',
  template: `
    <svg viewBox="0 0 20 19" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g transform="translate(0.464411, 0.357422)" fill="currentColor" fill-rule="nonzero">
        <path d="M18.6060014,8.82829301 L10.5080728,0.734555894 L9.96527687,0.191759955 C9.70750119,-0.063919985 9.29254522,-0.063919985 9.03476955,0.191759955 L0.394045014,8.82829301 C0.136269337,9.08606869 -0.00204931922,9.42767386 2.29495302e-05,9.79233213 C0.00842936685,10.5300316 0.622480371,11.1189338 1.36017987,11.1189338 L2.25086819,11.1189338 L2.25086819,17.9447499 L16.7491782,17.9447499 L16.7491782,11.1189338 L17.6587282,11.1189338 C18.0170992,11.1189338 18.3545129,10.9785194 18.6080971,10.7249352 C18.8616813,10.471351 19,10.1339373 19,9.77556623 C19,9.41929091 18.8595856,9.08187722 18.6060014,8.82829301 Z M10.673636,16.4358191 L8.32641037,16.4358191 L8.32641037,12.1605152 L10.673636,12.1605152 L10.673636,16.4358191 Z M15.2402474,9.61000299 L15.2402474,16.4358191 L12.0149079,16.4358191 L12.0149079,11.6575383 C12.0149079,11.1943803 11.6397709,10.8192434 11.176613,10.8192434 L7.82343344,10.8192434 C7.36027551,10.8192434 6.98513855,11.1943803 6.98513855,11.6575383 L6.98513855,16.4358191 L3.75979898,16.4358191 L3.75979898,9.61000299 L1.74789125,9.61000299 L9.50211895,1.86206251 L9.98623424,2.34617781 L17.2542509,9.61000299 L15.2402474,9.61000299 Z"></path>
      </g>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconHomeComponent {

}
