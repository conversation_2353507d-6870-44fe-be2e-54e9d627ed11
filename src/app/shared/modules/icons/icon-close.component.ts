import { ChangeDetectionStrategy, Component, Input } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'app-icon-close, CloseIcon',
  template: `
    <svg viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round">
        <g transform="translate(1.000000, 1.000000)" stroke="currentColor" [attr.stroke-width]="strokeWidth">
          <path d="M0,18 L9.00001631,9.00003519 M9.00001631,9.00003519 L17.9999811,0 M9.00001631,9.00003519 L0,0 M9.00001631,9.00003519 L17.9999811,18"></path>
        </g>
      </g>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconCloseComponent {

  @Input()
  strokeWidth = '2';

}
