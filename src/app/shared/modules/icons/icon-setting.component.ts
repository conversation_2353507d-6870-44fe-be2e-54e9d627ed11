import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  standalone: true,
  selector: 'app-icon-setting, SettingIcon',
  template: `
    <svg viewBox="0 0 21 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <path d="M3.50021069,2.19006517 C4.57811098,1.22628078 5.84420459,0.495530019 7.21884876,0.0437802568 C7.56617946,-0.0702525716 7.94793916,0.0432748745 8.17606995,0.32843806 C8.73780304,1.02698482 9.5830276,1.44262014 10.5,1.44262014 C11.4169724,1.44262014 12.262197,1.02873119 12.8239301,0.32843806 C13.0520608,0.0432748745 13.4338205,-0.0702525716 13.7811512,0.0437802568 C15.1566098,0.496089282 16.4235717,1.22781701 17.4997893,2.19006517 C17.7712861,2.43301518 17.8633297,2.81852894 17.7307824,3.15755243 C17.4016573,3.9973279 17.473801,4.94099127 17.9267765,5.72121903 C18.3835127,6.5140696 19.1674891,7.0362333 20.0547124,7.17244992 C20.4161931,7.22765983 20.705142,7.50139063 20.7791906,7.85877211 C21.0736031,9.27160472 21.0736031,10.7297775 20.7791906,12.1426101 C20.7044964,12.4993107 20.4156867,12.7722135 20.0547124,12.8271859 C19.1609553,12.9622919 18.3776181,13.4965199 17.9267765,14.2784168 C17.4682903,15.069521 17.4052922,16.00732 17.7307824,16.8438298 C17.8633297,17.1828533 17.7712861,17.568367 17.4997893,17.811317 C16.4218218,18.7718188 15.1566098,19.5052929 13.7811512,19.9558556 C13.4341841,20.0702808 13.0525005,19.9574724 12.8239301,19.6729441 C12.2601717,18.96698 11.4045738,18.5561303 10.5,18.5570157 C9.5830276,18.5570157 8.73780304,18.972651 8.17606995,19.6729441 C7.94749946,19.9574724 7.56581591,20.0702808 7.21884876,19.9558556 C5.84449668,19.5051446 4.57842518,18.7755958 3.50021069,17.8130634 C3.22871387,17.5701134 3.13667028,17.1845996 3.26921764,16.8455761 C3.59890842,16.0052936 3.52675445,15.0608533 3.07322354,14.2801632 C2.6219899,13.4989341 1.83871565,12.9653918 0.945287587,12.8306787 C0.583806851,12.7754687 0.294858012,12.5017379 0.220809393,12.1443565 C-0.0736031309,10.7315239 -0.0736031309,9.27335108 0.220809393,7.86051847 C0.294858012,7.503137 0.583806851,7.22940619 0.945287587,7.17419629 C1.83871565,7.03948318 2.6219899,6.50594085 3.07322354,5.72471176 C3.53170974,4.93186119 3.59470784,3.99580854 3.26921764,3.15755243 C3.13748493,2.81902345 3.2294661,2.43446655 3.50021069,2.19181154 L3.50021069,2.19006517 Z M4.59042787,6.59440248 C3.98599508,7.64257997 3.00018294,8.41810826 1.83776072,8.75989743 C1.72048815,9.58295373 1.72048815,10.4184285 1.83776072,11.2414848 C2.98397622,11.5785336 3.97269646,12.3416959 4.59042787,13.4069797 C5.20640933,14.4705172 5.37090438,15.7069449 5.09266276,16.8647862 C5.74714306,17.3799644 6.47162125,17.7990925 7.24509797,18.1081994 C8.12254776,17.2741485 9.28821912,16.8094594 10.5,16.8106488 C11.7337129,16.8106488 12.8886781,17.2856606 13.754902,18.1081994 C14.5281105,17.7982513 15.2529268,17.3795413 15.9073372,16.8647862 C15.6235939,15.6905869 15.8034286,14.4524542 16.4095721,13.4069797 C17.0140049,12.3588022 17.9998171,11.583274 19.1622393,11.2414848 C19.2796778,10.4178552 19.2796778,9.58178068 19.1622393,8.75815106 C18.0001121,8.41678694 17.0143454,7.64191979 16.4095721,6.59440248 C15.8034286,5.54892797 15.6235939,4.31079528 15.9073372,3.13659603 C15.2529268,2.62184089 14.5281105,2.20313089 13.754902,1.8931828 C12.8771779,2.72659632 11.7115387,3.19064721 10.5,3.18898703 C9.2884613,3.19064721 8.12282213,2.72659632 7.24509797,1.8931828 C6.47199141,2.2025897 5.74718036,2.62070861 5.09266276,3.13484966 C5.37265433,4.29443728 5.20640933,5.52911867 4.59042787,6.59440248 L4.59042787,6.59440248 Z M10.5,14.7150085 C7.8905331,14.7150085 5.77514221,12.6039458 5.77514221,9.99981792 C5.77514221,7.39569005 7.8905331,5.28462731 10.5,5.28462731 C13.1094669,5.28462731 15.2248577,7.39569009 15.2248577,9.99981792 C15.2248577,12.6039457 13.1094669,14.7150085 10.5,14.7150085 Z M10.5,12.9686416 C12.1429977,12.9686416 13.4749105,11.639454 13.4749105,9.99981792 C13.4749105,8.36018185 12.1429977,7.0309942 10.5,7.0309942 C8.85700236,7.0309942 7.52508963,8.36018189 7.52508963,9.99981792 C7.52508963,11.639454 8.85700236,12.9686416 10.5,12.9686416 L10.5,12.9686416 Z" fill="currentColor"></path>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconSettingComponent {

}
