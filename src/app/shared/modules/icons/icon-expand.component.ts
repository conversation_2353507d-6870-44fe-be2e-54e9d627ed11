import { ChangeDetectionStrategy, Component, input, numberAttribute } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'app-icon-expand, ExpandIcon',
  template: `
    @switch (style()) {
      @case (1) {
        <svg viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
          <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round">
            <g transform="translate(1.000000, 1.000000)" stroke="currentColor" stroke-width="2">
              <path d="M6.42857143,18 L0,18 L0,11.5714286 M11.5714286,0 L18,0 L18,6.42857143"></path>
            </g>
          </g>
        </svg>
      }
      @case (2) {
        <svg viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
          <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round">
            <g transform="translate(1.000000, 1.000000)" stroke="currentColor" stroke-width="1.5">
              <path d="M5.82352941,5.82352941 L0.529411765,0.529411765 M0.529411765,0.529411765 L0.529411765,4.76470588 M0.529411765,0.529411765 L4.76470588,0.529411765"></path>
              <path d="M12.1764706,5.82352941 L17.4705882,0.529411765 M17.4705882,0.529411765 L17.4705882,4.76470588 M17.4705882,0.529411765 L13.2352941,0.529411765"></path>
              <path d="M5.82352941,12.1764706 L0.529411765,17.4705882 M0.529411765,17.4705882 L0.529411765,13.2352941 M0.529411765,17.4705882 L4.76470588,17.4705882"></path>
              <path d="M12.1764706,12.1764706 L17.4705882,17.4705882 M17.4705882,17.4705882 L17.4705882,13.2352941 M17.4705882,17.4705882 L13.2352941,17.4705882"></path>
            </g>
          </g>
        </svg>
      }
    }
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconExpandComponent {

  style = input(1, { transform: numberAttribute });
  
}
