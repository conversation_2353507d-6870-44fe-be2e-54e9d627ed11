import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'app-icon-open, OpenIcon',
  template: `
    <svg viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <path d="M19.1298654,7.5411662 C19.6104275,7.5411662 20,7.15159369 20,6.67103164 L20,2.03031396 C20,0.909002529 19.0909975,0 17.969686,0 L13.3289684,0 C12.8484063,0 12.4588338,0.389572513 12.4588338,0.870134563 C12.4588338,1.35069661 12.8484063,1.74026913 13.3289684,1.74026913 L17.0299407,1.74026913 L9.23353503,9.53667481 C8.91406959,9.87951917 8.92349613,10.4137816 9.25485729,10.7451427 C9.58621844,11.0765039 10.1204808,11.0859304 10.4633252,10.766465 L18.2597309,2.97005929 L18.2597309,6.67103164 C18.2597309,7.15134592 18.6495512,7.5411662 19.1298654,7.5411662 Z" fill="currentColor"></path>
      <path d="M3.14285714,0 L8.85714285,0 C9.33052978,0 9.71428571,0.383755929 9.71428571,0.857142858 C9.71428571,1.33052979 9.33052978,1.71428572 8.85714285,1.71428572 L3.14285714,1.71428572 C2.35428571,1.71428572 1.71428572,2.35428571 1.71428572,3.14285714 L1.71428572,16.8571429 C1.71428572,17.6457143 2.35428571,18.2857143 3.14285714,18.2857143 L16.8571429,18.2857143 C17.6457143,18.2857143 18.2857143,17.6457143 18.2857143,16.8571429 L18.2857143,11.1428572 C18.2857143,10.6694702 18.6694702,10.2857143 19.1428571,10.2857143 C19.6162441,10.2857143 20,10.6694702 20,11.1428572 L20,16.8571429 C20,18.5928949 18.5928949,20 16.8571429,20 L3.14285714,20 C1.40710507,20 0,18.5928949 0,16.8571429 L0,3.14285714 C0,1.40710507 1.40710507,0 3.14285714,0 Z" fill="currentColor"></path>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconOpenComponent {

}
