import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'app-icon-close-fill, CloseFillIcon',
  template: `
    <svg viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g transform="translate(0.666667, 0.666667)" [attr.fill-opacity]="fillOpacity()" fill="currentColor" fill-rule="nonzero">
        <path d="M9.5,3.16413562e-16 C14.74685,3.16413562e-16 19,4.25315 19,9.5 C19,14.74685 14.74685,19 9.5,19 C4.25315,19 3.16413562e-16,14.74685 3.16413562e-16,9.5 C3.16413562e-16,4.25315 4.25315,3.16413562e-16 9.5,3.16413562e-16 Z M13.57455,5.42545001 C13.4462648,5.29709775 13.2722326,5.22498455 13.0907625,5.22498455 C12.9092924,5.22498455 12.7352601,5.29709775 12.606975,5.42545001 L9.50047499,8.53195001 L6.39302501,5.42545001 C6.22018439,5.25260937 5.96826368,5.18510742 5.73215898,5.24837147 C5.49605429,5.31163553 5.31163553,5.49605429 5.24837147,5.73215898 C5.18510742,5.96826368 5.25260937,6.22018439 5.42545001,6.39302501 L8.53289999,9.49952501 L5.42545001,12.606975 C5.25260938,12.7798156 5.18510743,13.0317363 5.24837149,13.267841 C5.31163555,13.5039457 5.4960543,13.6883645 5.73215899,13.7516285 C5.96826368,13.8148926 6.22018438,13.7473906 6.39302501,13.57455 L9.5,10.4671 L12.6065,13.57455 C12.8736885,13.8417384 13.3068865,13.8417384 13.574075,13.57455 C13.8412635,13.3073615 13.8412635,12.8741634 13.574075,12.606975 L10.46805,9.5 L13.57455,6.3935 C13.7029023,6.26521485 13.7750154,6.09118256 13.7750154,5.9097125 C13.7750154,5.72824244 13.7029023,5.55421015 13.57455,5.425925 L13.57455,5.42545001 Z" id="Shape"></path>
      </g>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconCloseFillComponent {

  fillOpacity = input(1);

}
