import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'FileTimeIcon',
  template: `
    <svg style="height: 0.8em;" viewBox="0 0 18 20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
      <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="tto_file_time" transform="translate(0.250000, 0.945312)" fill-rule="nonzero">
          <g id="编组">
            <path d="M3.44885981,4.90641628 L11.1840023,4.90641628 L11.1840023,3.14342085 L3.44885981,3.14342085 L3.44885981,4.90641628 Z M13.7513643,0 L1.76299543,0 C0.789319939,0 0,0.789319939 0,1.76299543 L0,16.572157 C0,17.5458325 0.789319939,18.3351525 1.76299543,18.3351525 L6.46754873,18.3351525 C5.97457562,17.813876 5.56566963,17.2191036 5.25548937,16.572157 L1.76299543,16.572157 L1.76299543,1.76299543 L13.7513643,1.76299543 L13.7513643,7.04492973 C14.3799165,7.26173557 14.9737478,7.56844943 15.5143598,7.95551687 L15.5143598,1.76299543 C15.5143598,0.789319939 14.7250398,0 13.7513643,0 Z M3.44885981,8.03463129 L7.97755432,8.03463129 L7.97755432,6.27163586 L3.44885981,6.27163586 L3.44885981,8.03463129 Z" id="形状" fill="#4E6F9C"></path>
            <path d="M15.5143598,9.95916118 C14.4889945,8.8211677 13.0285098,8.172277 11.4967136,8.17412436 C8.50733444,8.17412436 6.08409722,10.598247 6.08409722,13.5867446 C6.08409722,16.0024933 7.68493477,18.1257937 10.0074163,18.7905654 C12.3298978,19.455337 14.8117462,18.5006327 16.0899727,16.4507598 C17.3681991,14.4008869 17.1333334,11.7521381 15.5143598,9.95916118 L15.5143598,9.95916118 Z M14.0775185,16.1666681 C13.9746466,16.2696513 13.865736,16.3664199 13.7513643,16.4564605 C13.7006782,16.4965686 13.6492575,16.5351341 13.5971022,16.572157 C12.3369136,17.4568773 10.657395,17.4568773 9.39720638,16.572157 C9.22581842,16.4508256 9.06502995,16.3151764 8.91656976,16.1666681 C7.55268139,14.801039 7.48777899,12.609612 8.7684574,11.1656601 C10.0491358,9.72170818 12.2326309,9.52444331 13.7513643,10.7154862 C13.865736,10.8055268 13.9746466,10.9022953 14.0775185,11.0052786 C14.7620233,11.6896885 15.1465821,12.6180045 15.1465821,13.5859733 C15.1465821,14.5539422 14.7620233,15.4822582 14.0775185,16.1666681 L14.0775185,16.1666681 Z M11.9802151,13.2929855 L11.9802151,11.0495739 L10.2172196,11.0495739 L10.2172196,14.3759055 L13.1369604,15.8561605 L13.7513643,14.6441012 L13.9342751,14.2833482 L13.7513643,14.1921132 L11.9802151,13.2929855 Z" id="形状" fill="#87A1C0"></path>
          </g>
        </g>
      </g>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconFileTimeComponent {

}
