import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'EditContainerIcon',
  template: `
    <svg viewBox="0 0 19 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round">
        <g transform="translate(1.000000, 1.623319)" stroke="currentColor" [attr.stroke-width]="strokeWidth()">
          <path d="M8.50022889,0 L3.18758583,0 C1.42713111,0 0,1.42708439 0,3.18748307 L0,13.8125076 C0,15.572925 1.42713111,17 3.18758583,17 L13.812872,17 C15.5732893,17 17.0004578,15.572925 17.0004578,13.8125076 L17.0004578,8.49995796 M5.31263372,11.6874504 L8.613679,11.0223659 C8.78886637,10.9870255 8.94977389,10.9007089 9.07613854,10.7742655 L16.4657585,3.38092222 C16.8200423,3.02644981 16.819803,2.45187009 16.4652001,2.09769285 L14.899842,0.534133978 C14.5453987,0.180108307 13.9710937,0.180347632 13.6169695,0.534676449 L6.22655173,7.92884937 C6.1004264,8.05497371 6.01430857,8.21556087 5.97896795,8.39042775 L5.31263372,11.6874504 Z" id="形状"></path>
        </g>
      </g>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconEditContainerComponent {

  strokeWidth = input('2');

}
