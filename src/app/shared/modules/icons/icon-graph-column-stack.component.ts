import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'GraphColumnStackIcon',
  template: `
    <svg viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g fill="currentColor" fill-rule="nonzero">
          <path d="M11.4285714,8.57142857 L20,8.57142857 L20,20 L11.4285714,20 L11.4285714,8.57142857 Z M0,11.4285714 L8.57142857,11.4285714 L8.57142857,20 L0,20 L0,11.4285714 Z"></path>
          <path d="M11.4285714,0 L20,0 L20,8.57142857 L11.4285714,8.57142857 L11.4285714,0 Z M0,5.71428571 L8.57142857,5.71428571 L8.57142857,11.4285714 L0,11.4285714 L0,5.71428571 Z" fill-opacity="0.5"></path>
        </g>
      </g>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconGraphColumnStackComponent {

}
