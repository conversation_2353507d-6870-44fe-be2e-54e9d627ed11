import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'TagIcon',
  template: `
    <svg style="height: 0.8em;" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="icon-menu-营销" transform="translate(0.497056, 0.501214)" fill="#62B7ED" fill-rule="nonzero">
          <path d="M10.0643549,4.8310394 L12.6210759,12.8106144 L14.0405717,13.2855891 C14.6091843,13.5705739 15.3664297,13.380584 15.5550625,12.7156194 L16.123675,11.6706751 L18.8690288,6.06597362 C19.1540136,5.40100904 18.9640237,4.64104952 18.3954112,4.45105964 L9.68573225,0.176287342 C9.02348181,0.0812924021 8.1712415,-0.108697478 7.50899106,0.0812924021 L5.04726504,1.0312418 L8.26487936,2.4561659 C8.9271298,2.74115072 9.58938024,3.4061153 10.0629979,4.8310394 L10.0643549,4.8310394 Z" id="路径" fill-opacity="0.3"></path>
          <path d="M4.57364741,18.8889334 C3.91139697,19.1739183 3.43777935,18.8889334 3.34278441,18.2239689 L0.218807952,8.62947993 C0.028818072,8.05951029 -0.159814737,7.20455583 0.218807952,6.63458619 L2.39690622,2.73979365 C2.68053397,2.26481895 3.34278441,1.97983413 3.91139697,2.16982401 L7.79261881,3.87973293 C8.3598743,4.06972281 8.83349193,4.82968233 9.02348181,5.49464691 L12.1474583,14.9941409 C12.3374481,15.6591055 12.1474583,16.1340802 11.5788457,16.3240701 L4.57364741,18.8902905 L4.57364741,18.8889334 Z M4.10002978,5.20966209 C3.62641215,5.39965197 3.43777935,5.87462667 3.53277429,6.34960137 C3.62776923,6.82457607 4.19502472,7.10956089 4.57364741,6.91957101 C5.04726504,6.72958113 5.33089279,6.25460643 5.14225998,5.77963173 C5.10338707,5.55139135 4.96223499,5.35347594 4.75909984,5.24238641 C4.55596469,5.13129687 4.31317036,5.11924159 4.10002978,5.20966209 Z" id="形状" opacity="0.6"></path>
        </g>
      </g>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconTagComponent {

  strokeWidth = input('2');

}
