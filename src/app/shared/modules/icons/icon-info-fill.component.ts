import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  standalone: true,
  selector: 'InfoFillIcon',
  template: `
    <svg viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g transform="translate(0.666667, 0.666667)" fill="currentColor" fill-rule="nonzero">
        <path d="M9.5,3.16413562e-16 C14.74685,3.16413562e-16 19,4.25315 19,9.5 C19,14.74685 14.74685,19 9.5,19 C4.25315,19 3.16413562e-16,14.74685 3.16413562e-16,9.5 C3.16413562e-16,4.25315 4.25315,3.16413562e-16 9.5,3.16413562e-16 Z M9.5,7.12499999 L8.075,7.12499999 C7.68149712,7.12499999 7.3625,7.44399711 7.3625,7.83749999 C7.3625,8.23100288 7.68149712,8.54999999 8.075,8.54999999 L8.7875,8.54999999 L8.7875,12.825 L8.075,12.825 C7.68149712,12.825 7.3625,13.1439971 7.3625,13.5375 C7.3625,13.9310029 7.68149712,14.25 8.075,14.25 L10.925,14.25 C11.3185029,14.25 11.6375,13.9310029 11.6375,13.5375 C11.6375,13.1439971 11.3185029,12.825 10.925,12.825 L10.2125,12.825 L10.2125,7.83749999 C10.2125,7.44399711 9.89350288,7.12499999 9.5,7.12499999 Z M9.5,4.275 C9.16059768,4.275 8.846977,4.45606896 8.67727584,4.74999999 C8.50757467,5.04393102 8.50757467,5.40606899 8.67727584,5.70000001 C8.846977,5.99393104 9.16059768,6.17500001 9.5,6.17500001 C10.0246705,6.17500001 10.45,5.74967051 10.45,5.225 C10.45,4.7003295 10.0246705,4.275 9.5,4.275 L9.5,4.275 Z"></path>
      </g>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconInfoFillComponent {

}
