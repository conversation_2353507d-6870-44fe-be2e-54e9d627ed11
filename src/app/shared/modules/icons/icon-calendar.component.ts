import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'app-icon-calendar, CalendarIcon',
  template: `
    <svg viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g fill="currentColor" fill-rule="nonzero">
          <path d="M4.6875,0 C5.20526695,0 5.625,0.419733047 5.625,0.9375 L5.625,2.5 L14.375,2.5 L14.375,0.9375 C14.375,0.419733047 14.794733,0 15.3125,0 C15.830267,0 16.25,0.419733047 16.25,0.9375 L16.25,2.5 L16.5625,2.5 C18.4609788,2.5 20,4.03902117 20,5.9375 L20,16.5625 C20,18.4609788 18.4609788,20 16.5625,20 L3.4375,20 C1.53902117,20 0,18.4609788 0,16.5625 L0,5.9375 C0,5.02581832 0.362164139,4.15147674 1.00682044,3.50682044 C1.65147674,2.86216414 2.52581832,2.5 3.4375,2.5 L3.75,2.5 L3.75,0.9375 C3.75,0.419733047 4.16973305,0 4.6875,0 Z M3.4375,6.875 C2.575,6.875 1.875,7.575 1.875,8.4375 L1.875,16.5625 C1.875,17.425 2.575,18.125 3.4375,18.125 L16.5625,18.125 C17.425,18.125 18.125,17.425 18.125,16.5625 L18.125,8.4375 C18.125,7.575 17.425,6.875 16.5625,6.875 L3.4375,6.875 Z"></path>
        </g>
      </g>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconCalendarComponent {

}
