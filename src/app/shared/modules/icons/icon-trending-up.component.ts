import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'app-icon-trending-up, TrendingUpIcon',
  template: `
    <svg viewBox="0 0 20 14" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd" stroke-linecap="round" stroke-linejoin="round">
        <g transform="translate(1.000000, 1.000000)" stroke="currentColor" stroke-width="2">
          <path d="M17.4548264,0.545446281 L11.0772503,7.02262087 C10.9627066,7.13891002 10.9047802,7.19727277 10.8535083,7.24309026 C10.0251936,7.98456993 8.77252163,7.98456993 7.94409782,7.24309026 C7.89282587,7.19727277 7.83446311,7.13901911 7.7199194,7.02262087 C7.60526659,6.90622263 7.54799473,6.84796897 7.49672278,6.80215148 C6.66829897,6.06067181 5.41515795,6.06067181 4.58677778,6.80215148 C4.53561491,6.84785988 4.47846305,6.90589537 4.36442115,7.02174816 L0,11.4543719 M17.4548264,0.545446281 L17.454281,7.09080165 M17.4548264,0.545446281 L10.9089256,0.545446281"></path>
        </g>
      </g>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconTrendingUpComponent {

}
