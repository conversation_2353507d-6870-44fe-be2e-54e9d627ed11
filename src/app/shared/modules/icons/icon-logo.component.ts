import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'app-icon-logo, LogoIcon',
  template: `
    <svg viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g fill="currentColor" fill-rule="nonzero">
          <path d="M10,20 C4.4771525,20 0,15.5228475 0,10 C0,4.4771525 4.4771525,0 10,0 C15.5228475,0 20,4.4771525 20,10 C20,15.5228475 15.5228475,20 10,20 Z M10,0.833339844 C4.93739339,0.833339844 0.833339844,4.93739339 0.833339844,10 C0.833339844,15.0626066 4.93739339,19.1666602 10,19.1666602 C15.0626066,19.1666602 19.1666602,15.0626066 19.1666602,10 C19.1666602,7.56885057 18.2008898,5.23727469 16.4818076,3.51819244 C14.7627253,1.79911019 12.4311494,0.833339844 10,0.833339844 Z"></path>
          <path d="M10,1.66666016 C5.39762349,1.66666016 1.66666016,5.39762349 1.66666016,10 L10,10 C9.64673641,9.99769245 9.32916561,9.78415838 9.19374327,9.45787433 C9.05832093,9.13159029 9.13135348,8.75593851 9.37916016,8.50416016 C9.43317518,8.45251867 9.49335653,8.40774018 9.55833984,8.37083984 L14.3875,4.7375 L10.7541602,9.58333984 C10.5945088,9.84620029 10.3075085,10.0047629 10,10 L18.3333398,10 C18.3333398,5.39762349 14.6023765,1.66666016 10,1.66666016 Z"></path>
          <path d="M9.58333984,9.16666016 C9.58333984,9.39677521 9.76988495,9.58332031 10,9.58332031 C10.2301151,9.58332031 10.4166602,9.39677521 10.4166602,9.16666016 C10.4166602,8.93654511 10.2301151,8.75 10,8.75 C9.76988495,8.75 9.58333984,8.93654511 9.58333984,9.16666016 L9.58333984,9.16666016 Z M18.3333398,10 C18.3392974,10.7372905 18.1976214,11.4683157 17.9166602,12.15 C17.4845156,13.1440701 16.8149159,14.0168368 15.9666602,14.6916602 C14.4583398,15.9416602 12.35,13.025 10.0208398,13.025 C7.69167969,13.025 5.5875,15.9416602 4.075,14.6916602 C3.70355566,14.3993128 3.36290536,14.0698336 3.05833984,13.7083398 C2.17273105,12.6743816 1.67996087,11.3613213 1.66666016,10"></path>
        </g>
      </g>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconLogoComponent {

}
