import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'MoreIcon',
  template: `
    <svg style="height: 0.8em;" viewBox="0 0 5 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g transform="translate(0.500000, 0.500000)" fill="currentColor" fill-rule="nonzero">
        <path d="M1.9,15.2 C2.94934102,15.2 3.8,16.050659 3.8,17.1 C3.8,18.149341 2.94934102,19 1.9,19 C0.850658975,19 0,18.149341 0,17.1 C0,16.050659 0.850658975,15.2 1.9,15.2 Z M1.9,7.6 C2.94934102,7.6 3.8,8.45065898 3.8,9.5 C3.8,10.549341 2.94934102,11.4 1.9,11.4 C0.850658975,11.4 0,10.549341 0,9.5 C0,8.45065898 0.850658975,7.6 1.9,7.6 Z M1.9,0 C2.94934102,0 3.8,0.850658975 3.8,1.9 C3.8,2.94934102 2.94934102,3.8 1.9,3.8 C0.850658975,3.8 0,2.94934102 0,1.9 C0,0.850658975 0.850658975,0 1.9,0 Z" id="Shape"></path>
      </g>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconMoreComponent {

}
