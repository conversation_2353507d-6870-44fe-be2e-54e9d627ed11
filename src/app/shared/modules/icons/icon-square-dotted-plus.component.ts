import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'SquareDottedPlusIcon',
  template: `
    <svg style="height: 0.8em" viewBox="0 0 20 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g id="页面-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="plus-square-dotted" fill="#000000" fill-rule="nonzero">
          <path d="M3.125,-1.52655666e-17 C2.9175,-1.52655666e-17 2.7125,0.02 2.51625,0.06 L2.75875,1.285 C2.87940227,1.26143431 3.0020682,1.24971197 3.125,1.25 L3.6975,1.25 L3.6975,-1.52655666e-17 L3.125,-1.52655666e-17 Z M5.99,-1.52655666e-17 L4.84375,-1.52655666e-17 L4.84375,1.25 L5.99,1.25 L5.99,-1.52655666e-17 Z M8.28125,-1.52655666e-17 L7.135,-1.52655666e-17 L7.135,1.25 L8.28125,1.25 L8.28125,-1.52655666e-17 Z M10.5725,-1.52655666e-17 L9.4275,-1.52655666e-17 L9.4275,1.25 L10.5725,1.25 L10.5725,-1.52655666e-17 Z M12.865,-1.52655666e-17 L11.71875,-1.52655666e-17 L11.71875,1.25 L12.865,1.25 L12.865,-1.52655666e-17 Z M15.15625,-1.52655666e-17 L14.01,-1.52655666e-17 L14.01,1.25 L15.15625,1.25 L15.15625,-1.52655666e-17 Z M16.875,-3.48332474e-16 L16.3025,-3.48332474e-16 L16.3025,1.25 L16.875,1.25 C17,1.25 17.12375,1.2625 17.24125,1.28625 L17.48375,0.06 C17.283272,0.020219795 17.0793866,0.000124313345 16.875,-3.48332474e-16 L16.875,-3.48332474e-16 Z M19.47375,1.3875 C19.2452893,1.04681574 18.9522652,0.754216358 18.61125,0.52625 L17.91625,1.565 C18.12125,1.7025 18.2975,1.87875 18.435,2.08375 L19.4725,1.38875 L19.47375,1.3875 Z M1.3875,0.52625 C1.04681574,0.754710702 0.754216358,1.04773476 0.52625,1.38875 L1.565,2.08375 C1.7025,1.87875 1.87875,1.7025 2.08375,1.565 L1.3875,0.5275 L1.3875,0.52625 Z M20,3.125 C20,2.9175 19.98,2.7125 19.94,2.51625 L18.715,2.75875 C18.7375,2.87625 18.75,2.99875 18.75,3.125 L18.75,3.6975 L20,3.6975 L20,3.125 Z M0.06,2.51625 C0.020219795,2.716728 0.000124313345,2.92061341 0,3.125 L0,3.6975 L1.25,3.6975 L1.25,3.125 C1.25,3 1.2625,2.87625 1.28625,2.75875 L0.06,2.51625 Z M0,4.84375 L0,5.99 L1.25,5.99 L1.25,4.84375 L0,4.84375 Z M20,5.99 L20,4.84375 L18.75,4.84375 L18.75,5.99 L20,5.99 Z M0,7.135 L0,8.28125 L1.25,8.28125 L1.25,7.135 L0,7.135 Z M20,8.28125 L20,7.135 L18.75,7.135 L18.75,8.28125 L20,8.28125 Z M0,9.4275 L0,10.5725 L1.25,10.5725 L1.25,9.4275 L0,9.4275 Z M18.75,10.5725 L20,10.5725 L20,9.4275 L18.75,9.4275 L18.75,10.5725 Z M0,11.71875 L0,12.865 L1.25,12.865 L1.25,11.71875 L0,11.71875 Z M20,12.865 L20,11.71875 L18.75,11.71875 L18.75,12.865 L20,12.865 Z M0,14.01 L0,15.15625 L1.25,15.15625 L1.25,14.01 L0,14.01 Z M20,15.15625 L20,14.01 L18.75,14.01 L18.75,15.15625 L20,15.15625 Z M0,16.3025 L0,16.875 C0,17.0825 0.02,17.2875 0.06,17.48375 L1.285,17.24125 C1.26143431,17.1205977 1.24971197,16.9979318 1.25,16.875 L1.25,16.3025 L0,16.3025 Z M20,16.875 L20,16.3025 L18.75,16.3025 L18.75,16.875 C18.75,17 18.7375,17.12375 18.71375,17.24125 L19.94,17.48375 C19.98,17.28625 20,17.08375 20,16.875 Z M0.52625,18.6125 C0.755,18.9525 1.0475,19.245 1.38875,19.47375 L2.08375,18.435 C1.87856997,18.2976751 1.70232488,18.12143 1.565,17.91625 L0.5275,18.61125 L0.52625,18.6125 Z M18.6125,19.47375 C18.9525,19.245 19.245,18.9525 19.47375,18.61125 L18.435,17.91625 C18.2975,18.12125 18.12125,18.2975 17.91625,18.435 L18.61125,19.4725 L18.6125,19.47375 Z M2.51625,19.94 C2.71375,19.98 2.91625,20 3.125,20 L3.6975,20 L3.6975,18.75 L3.125,18.75 C3,18.75 2.87625,18.7375 2.75875,18.71375 L2.51625,19.94 L2.51625,19.94 Z M16.875,20 C17.0825,20 17.2875,19.98 17.48375,19.94 L17.24125,18.715 C17.1205977,18.7385657 16.9979318,18.750288 16.875,18.75 L16.3025,18.75 L16.3025,20 L16.875,20 L16.875,20 Z M4.84375,20 L5.99,20 L5.99,18.75 L4.84375,18.75 L4.84375,20 Z M7.135,20 L8.28125,20 L8.28125,18.75 L7.135,18.75 L7.135,20 Z M9.4275,18.75 L9.4275,20 L10.5725,20 L10.5725,18.75 L9.4275,18.75 Z M11.71875,20 L12.865,20 L12.865,18.75 L11.71875,18.75 L11.71875,20 Z M14.01,20 L15.15625,20 L15.15625,18.75 L14.01,18.75 L14.01,20 Z M10.625,5.625 C10.625,5.27982203 10.345178,5 10,5 C9.65482203,5 9.375,5.27982203 9.375,5.625 L9.375,9.375 L5.625,9.375 C5.27982203,9.375 5,9.65482203 5,10 C5,10.345178 5.27982203,10.625 5.625,10.625 L9.375,10.625 L9.375,14.375 C9.375,14.720178 9.65482203,15 10,15 C10.345178,15 10.625,14.720178 10.625,14.375 L10.625,10.625 L14.375,10.625 C14.720178,10.625 15,10.345178 15,10 C15,9.65482203 14.720178,9.375 14.375,9.375 L10.625,9.375 L10.625,5.625 Z" id="形状"></path>
        </g>
      </g>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconSquareDottedPlusComponent {

}
