import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconBtnDirective } from '../headless/button';

@Component({
  selector: 'app-icon-back-top, BackTopIcon',
  template: `
    <svg viewBox="0 0 17 20" version="1.1" xmlns="http://www.w3.org/2000/svg">
      <g transform="translate(0.750000, 0.081250)" fill="currentColor" fill-rule="nonzero">
        <path d="M15.7416561,0.894412277 C15.7416561,1.38838254 15.3501128,1.78882455 14.8671196,1.78882455 L0.874536449,1.78882455 C0.391543305,1.78882455 0,1.38838254 0,0.894412277 C0,0.400442016 0.391543305,0 0.874536449,0 L14.8671196,0 C15.3501128,0 15.7416561,0.400442016 15.7416561,0.894412277 Z M8.70607404,5.95499694 L11.949213,9.19813586 C12.3001614,9.53709325 12.8580117,9.53224569 13.2030167,9.18724064 C13.5480217,8.8422356 13.5528693,8.28438539 13.2139119,7.9334369 L9.25792641,3.9774514 C9.08944251,3.80044303 8.90958122,3.63462407 8.71949022,3.48105258 C8.50930334,3.32095278 8.20967522,3.14833121 7.81166176,3.14833121 C7.4136483,3.14833121 7.11402018,3.32095278 6.9038333,3.48105258 C6.72495084,3.61789766 6.53801868,3.80482983 6.3645027,3.9774514 L6.3358815,4.006967 L2.4085172,7.9334369 C2.05928022,8.28267387 2.05928022,8.84889888 2.4085172,9.19813586 C2.75775417,9.54737283 3.32397918,9.54737283 3.67321616,9.19813586 L6.91635507,5.95499694 L6.91546066,18.1055877 C6.91546066,18.599558 7.31590268,19 7.80987294,19 C8.3038432,19 8.70428521,18.599558 8.70428521,18.1055877 L8.70517963,5.95410253 L8.70607404,5.95499694 Z" id="Shape"></path>
      </g>
    </svg>
  `,
  styleUrls: ['./icon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
})
export class IconBackTopComponent {

}
