import { Directive, HostBinding, Inject, InjectionToken, Input, Optional, Self } from "@angular/core";
import { isNull } from "@common/function";


export const DEFAULT_CLASS_LIST = new InjectionToken('default_class_list');
export const DEFAULT_ACTIVE_CLASS_LIST = new InjectionToken('default_active_class_list');
export const DEFAULT_DISABLED_CLASS_LIST = new InjectionToken('default_disabled_class_list');
export const ENABLE_HOST_BINDING = new InjectionToken('enable_host_binding');

@Directive({
  standalone: true
})
export class HeadLessDirective {

  @Input()
  set class(value: string) {
    this._className = value;
  }


  @Input()
  get selected() { return this._selected; }
  set selected(state: any | null) {
    this._selected = state;
  }


  @Input()
  get disabled() { return this._disabled; }
  set disabled(state: any | null) {
    this._disabled = state;
  }


  @Input()
  activeClass = '';


  @Input()
  disabledClass = '';

  
  @HostBinding('class')
  get classList() {
    if (this._enableHostBinding) {
      return this.className;
    }
  }

  get className() {
    return `
      ${this._className}
      ${
        this._disabled ? this.disabledClass : 
        this._selected ? this.activeClass : ''
      }
    `;
  }


  private _className = '';
  private _selected = false;
  private _disabled = false;
  private _enableHostBinding = true;


  constructor(
    @Inject(DEFAULT_CLASS_LIST) @Self() @Optional() defaultClassList: string,
    @Inject(DEFAULT_ACTIVE_CLASS_LIST) @Self() @Optional() defaultActiveClassList: string,
    @Inject(DEFAULT_DISABLED_CLASS_LIST) @Self() @Optional() defaultDisabledClassList: string,
    @Inject(ENABLE_HOST_BINDING) @Self() @Optional() enableHostBinding: boolean,
  ) {
    this._className = defaultClassList || '';
    this.activeClass = defaultActiveClassList || '';
    this.disabledClass = defaultDisabledClassList || '';
    this._enableHostBinding = isNull(enableHostBinding) ? true : enableHostBinding;
  }
  
}