import { ChangeDetectionStrategy, Component } from '@angular/core';
import { DEFAULT_CLASS_LIST, HeadLessDirective } from '../headless.directive';

@Component({
  selector: 'app-breadcrumb',
  template: `
    <ng-content />
  `,
  styles: [
    `
      :host::ng-deep app-breadcrumb-item:last-child app-breadcrumb-separator{
        display: none;
      }
    `
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: HeadLessDirective,
      inputs: ['class']
    }
  ],
  providers: [
    {
      provide: DEFAULT_CLASS_LIST,
      useValue: `flex items-center gap-x-1`
    }
  ]
})
export class BreadcrumbComponent {

}
