import { ChangeDetectionStrategy, Component } from '@angular/core';
import { HeadLessDirective, DEFAULT_CLASS_LIST } from '../headless.directive';
import { BreadcrumbSeparatorComponent } from './breadcrumb-separator.component';

@Component({
  selector: 'app-breadcrumb-item',
  template: `
    <span class="contents">
      <ng-content />
    </span>
    <app-breadcrumb-separator />
  `,
  styles: [
    `
      :host[routerLink] span,
      :host[ng-reflect-router-link] span {
        text-underline-offset: 3px;
        cursor: pointer;
      }

      :host[routerLink] span:hover,
      :host[ng-reflect-router-link] span:hover {
        text-decoration-line: underline;
        color: #1890ff;
      }
    `
  ],
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: HeadLessDirective,
      inputs: ['class']
    }
  ],
  imports: [
    BreadcrumbSeparatorComponent,
  ],
  providers: [
    {
      provide: DEFAULT_CLASS_LIST,
      useValue: `inline-flex items-center gap-x-1`
    }
  ]
})
export class BreadcrumbItemComponent {

}
