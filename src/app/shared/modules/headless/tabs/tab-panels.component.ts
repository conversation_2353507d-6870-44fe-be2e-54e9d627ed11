import { AfterContentChecked, ChangeDetectionStrategy, Component, ContentChildren, QueryList } from '@angular/core';
import { TabPanelComponent } from './tab-panel.component';

@Component({
  selector: 'app-tab-panels',
  template: `<ng-content />`,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TabPanelsComponent implements AfterContentChecked {

  @ContentChildren(TabPanelComponent, { descendants: true })
  tabPanels: QueryList<TabPanelComponent> = new QueryList<TabPanelComponent>();
  

  ngAfterContentChecked() {
    this.tabPanels.toArray().forEach((item, i) => {
      item.setIndex(i);
    });
  }

}
