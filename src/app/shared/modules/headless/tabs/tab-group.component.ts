import { Component, Input, ChangeDetectionStrategy, Output, EventEmitter } from '@angular/core';

@Component({
  selector: 'app-tab-group',
  template: `<ng-content />`,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TabGroupComponent {

  @Input()
  selectedIndex = 0;

  
  @Output()
  selectedIndexChange = new EventEmitter<number>();


  changeSelectedIndex(value: number) {
    this.selectedIndex = value;
    this.selectedIndexChange.emit(value);
  }

}
