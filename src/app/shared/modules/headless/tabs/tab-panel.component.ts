import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, DestroyRef, computed, inject, signal } from '@angular/core';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { startWith } from 'rxjs';

import { isNotUndefinedOrNotNull } from '@common/function';
import { TabGroupComponent } from './tab-group.component';

@Component({
  selector: 'app-tab-panel',
  template: `
    @if (isActive()) {
      <ng-content />
    }
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class TabPanelComponent implements AfterViewInit {

  readonly destroyRef = inject(DestroyRef);
  readonly parent = inject(TabGroupComponent);
  readonly cdr = inject(ChangeDetectorRef);
  
  isActive = computed(() => {
    if (isNotUndefinedOrNotNull(this.#index(), this.#selectedIndex())) {
      return this.#index() === this.#selectedIndex();
    }
    return false;
  });
  isActive$ = toObservable(this.isActive);
  #selectedIndex = signal(null);
  #index = signal(null);


  ngAfterViewInit(): void {
    this.parent.selectedIndexChange.pipe(
      startWith(this.parent.selectedIndex),
      takeUntilDestroyed(this.destroyRef)
    ).subscribe((index) => {
      this.#selectedIndex.set(index);
      this.cdr.markForCheck();
    })
  }

  
  setIndex(value: number) {
    this.#index.set(value);
  }

}
