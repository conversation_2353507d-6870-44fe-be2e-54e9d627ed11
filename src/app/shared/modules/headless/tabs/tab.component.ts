import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, DestroyRef, ElementRef, HostListener, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FocusableOption, FocusOrigin } from '@angular/cdk/a11y';
import { startWith } from 'rxjs';

import { DEFAULT_ACTIVE_CLASS_LIST, DEFAULT_CLASS_LIST, HeadLessDirective } from '../headless.directive';
import { TabGroupComponent } from './tab-group.component';
import { TabListComponent } from './tab-list.component';

@Component({
  selector: 'app-tab',
  template: `
    <ng-content />
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: {
    'tabindex': '0'
  },
  hostDirectives: [
    {
      directive: HeadLessDirective,
      inputs: ['class', 'activeClass']
    }
  ],
  providers: [
    {
      provide: DEFAULT_CLASS_LIST,
      useValue: `
        w-full rounded-lg py-2.5 text-sm font-medium leading-5 text-neutral-100
        ring-white/60 ring-offset-2 ring-offset-blue-400 bg-transparent
        focus:outline-hidden focus:ring-2 hover:bg-white/[0.12] hover:text-white
      `
    },
    {
      provide: DEFAULT_ACTIVE_CLASS_LIST,
      useValue: `text-neutral-700! bg-white shadow-sm focus:bg-white`
    },
    // {
    //   provide: ENABLE_HOST_BINDING,
    //   useValue: false
    // }
  ]
})
export class TabComponent implements AfterViewInit, FocusableOption {

  private _index: number;
  readonly destroyRef = inject(DestroyRef);
  readonly cdr = inject(ChangeDetectorRef);
  readonly root = inject(TabGroupComponent);
  readonly parent = inject(TabListComponent);
  readonly headLess = inject(HeadLessDirective);
  readonly elementRef = inject(ElementRef);


  ngAfterViewInit(): void {
    this.root.selectedIndexChange.pipe(
      startWith(this.root.selectedIndex),
      takeUntilDestroyed(this.destroyRef)
    ).subscribe(() => {
      if (this.isActive()) {
        this.parent.activeElement.set(this.elementRef.nativeElement);
      }

      this.headLess.selected = this.isActive();
      this.cdr.markForCheck();
    })
  }

  
  private isActive() {
    return this._index === this.root.selectedIndex;
  }
  

  setIndex(value: number) {
    this._index = value;
    this.headLess.selected = this.isActive();
    this.cdr.markForCheck();
  }


  focus(origin?: FocusOrigin): void {
    this.elementRef.nativeElement.focus();
  }

  
  @HostListener('click')
  handleClick() {
    this.root.changeSelectedIndex(this._index);
  }

}
