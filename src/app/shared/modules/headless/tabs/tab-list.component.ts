import { AfterContentChecked, AfterViewInit, ChangeDetectionStrategy, Component, ContentChildren, DestroyRef, HostListener, QueryList, inject, signal } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { FocusKeyManager } from '@angular/cdk/a11y';

import { DEFAULT_CLASS_LIST, HeadLessDirective } from '../headless.directive';
import { TabGroupComponent } from './tab-group.component';
import { TabComponent } from './tab.component';

@Component({
  selector: 'app-tab-list',
  template: `<ng-content></ng-content>`,
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: {
    'class': 'relative'
  },
  hostDirectives: [
    {
      directive: HeadLessDirective,
      inputs: ['class']
    }
  ],
  providers: [
    {
      provide: DEFAULT_CLASS_LIST,
      useValue: `flex space-x-1 rounded-xl bg-neutral-900/20 p-1`
    }
  ]
})
export class TabListComponent implements AfterViewInit, AfterContentChecked {

  readonly destroyRef = inject(DestroyRef);
  readonly parent = inject(TabGroupComponent);
  public keyManager: FocusKeyManager<TabComponent>;
  
  @ContentChildren(TabComponent, { descendants: true })
  tabs: QueryList<TabComponent> = new QueryList<TabComponent>();
  activeElement = signal<HTMLElement>(null);

  ngAfterViewInit(): void {
    this.keyManager = new FocusKeyManager(this.tabs).withHorizontalOrientation('ltr').withWrap();

    this.parent.selectedIndexChange.pipe(
      takeUntilDestroyed(this.destroyRef)
    ).subscribe(index => {
      this.keyManager.updateActiveItem(index);
    })
  }
  

  ngAfterContentChecked() {
    const count = this.tabs.toArray().length;

    /**
     * 当tab数量等于当前选择索引时，设置当前选中索引为最后一项
     */
    if (count === this.parent.selectedIndex) {
      this.parent.changeSelectedIndex(count - 1);
    }
    
    this.tabs.toArray().forEach((item, i) => {
      item.setIndex(i);
    });
  }


  @HostListener('keydown', ['$event'])
  onKeydown(event: KeyboardEvent) {
    const { activeItemIndex } = this.keyManager;
    const count = this.tabs.toArray().length - 1;
    const isNext = event.code === 'ArrowRight';
    const isPrev = event.code === 'ArrowLeft';

    let index: number;

    if (isNext) {
      if (activeItemIndex >= count) {
        index = 0;
      } else {
        index = activeItemIndex + 1;
      }
    }

    if (isPrev) {
      if (activeItemIndex <= 0) {
        index = count; 
      } else {
        index = activeItemIndex - 1;
      }
    }

    if (index !== undefined) {
      // const item = this.tabs.toArray()[index];
      
      this.keyManager.onKeydown(event);
      // item.elementRef.nativeElement.click();
      this.parent.changeSelectedIndex(index);
    }
  }
}
