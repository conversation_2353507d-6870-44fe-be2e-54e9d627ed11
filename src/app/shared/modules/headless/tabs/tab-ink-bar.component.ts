import { ChangeDetectionStrategy, Component, inject } from '@angular/core';
import { TabListComponent } from './tab-list.component';

@Component({
  selector: 'app-tab-ink-bar',
  template: '',
  host: {
    'class': 'absolute h-0.5 bottom-0 bg-primary transition-all',
    '[style.left.px]': 'parent.activeElement()?.offsetLeft || 0',
    '[style.width.px]': 'parent.activeElement()?.offsetWidth || 0',
  },
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class TabInkBarComponent {

  parent = inject(TabListComponent);

}
