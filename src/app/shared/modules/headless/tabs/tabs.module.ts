import { NgModule } from '@angular/core';
import { TabComponent } from './tab.component';
import { TabGroupComponent } from './tab-group.component';
import { TabPanelsComponent } from './tab-panels.component';
import { TabListComponent } from './tab-list.component';
import { TabPanelComponent } from './tab-panel.component';
import { TabInkBarComponent } from './tab-ink-bar.component';


@NgModule({
  imports: [
    TabGroupComponent,
    TabListComponent,
    TabComponent,
    TabPanelsComponent,
    TabPanelComponent,
    TabInkBarComponent,
  ],
  exports: [
    TabGroupComponent,
    TabListComponent,
    TabComponent,
    TabPanelsComponent,
    TabPanelComponent,
    TabInkBarComponent,
  ]
})
export class TabsModule { }
