import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, ContentChild } from '@angular/core';
import { CdkOverlayOrigin, OverlayModule } from '@angular/cdk/overlay';

import { BaseOverlayDirective } from '@common/directive';
import { HeadLessDirective } from '../headless.directive';


@Component({
  selector: 'app-popover',
  template: `
    <ng-content select="app-popover-button"></ng-content>

    <ng-template
      #overlay="cdkConnectedOverlay"
      cdkConnectedOverlay
      [cdkConnectedOverlayHasBackdrop]="false"
      [cdkConnectedOverlayOrigin]="overlayOrigin"
      [cdkConnectedOverlayPositions]="listOfPositions"
      [cdkConnectedOverlayScrollStrategy]="scrollStrategy"
      [cdkConnectedOverlayOpen]="visible()"
      (positionChange)="onPositionChange($event)"
      (overlayOutsideClick)="handleOutside($event)"
      (detach)="close()"
    >
      <div
        class="relative"
        [style.min-width.px]="overlayOrigin?.elementRef?.nativeElement?.offsetWidth"
      >
        <ng-content select="app-popover-panel"></ng-content>
      </div>
    </ng-template>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: HeadLessDirective,
      inputs: ['class']
    }
  ],
  imports: [
    CommonModule,
    OverlayModule,
  ],
})
export class PopoverComponent extends BaseOverlayDirective {

  @ContentChild(CdkOverlayOrigin, { static: true })
  public overlayOrigin: CdkOverlayOrigin;
  
}
