import { ChangeDetectionStrategy, Component, HostListener } from '@angular/core';
import { CdkOverlayOrigin } from '@angular/cdk/overlay';

import { DEFAULT_CLASS_LIST, HeadLessDirective } from '../headless.directive';
import { PopoverComponent } from './popover.component';

@Component({
  selector: 'app-popover-button',
  template: `
    <ng-content></ng-content>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: {
    'tabindex': '0'
  },
  hostDirectives: [
    CdkOverlayOrigin,
    {
      directive: HeadLessDirective,
      inputs: ['class']
    }
  ],
  providers: [
    {
      provide: DEFAULT_CLASS_LIST,
      useValue: `
        group inline-flex items-center gap-x-1 rounded-md px-2.5 py-2
        text-xs text-neutral-500 font-medium cursor-pointer 
        transition-colors duration-300
        hover:bg-black/5
        focus:bg-black/10
        focus:outline
        focus:outline-offset-1
        focus:outline-black/15
      `
    }
  ]
})
export class PopoverButtonComponent {
  

  constructor(private readonly parent: PopoverComponent) {}

  @HostListener('click', ['$event'])
  handleClick(event: MouseEvent) {
    this.parent.toggle(event);
  }

}
