import { ChangeDetectionStrategy, Component } from '@angular/core';

import { DEFAULT_CLASS_LIST, HeadLessDirective } from '../headless.directive';

@Component({
  selector: 'app-popover-panel',
  template: `
    <ng-content></ng-content>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: HeadLessDirective,
      inputs: ['class']
    }
  ],
  providers: [
    {
      provide: DEFAULT_CLASS_LIST,
      useValue: `block mt-1 mb-1`
    }
  ]
})
export class PopoverPanelComponent {

}
