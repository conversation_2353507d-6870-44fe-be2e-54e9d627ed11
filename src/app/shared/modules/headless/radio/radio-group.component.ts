import { Component, EventEmitter, ChangeDetectionStrategy, forwardRef, Input, ChangeDetectorRef, Output, ContentChildren, QueryList, AfterContentInit, HostListener, signal, inject, input, booleanAttribute } from '@angular/core';
import { NG_VALUE_ACCESSOR, ControlValueAccessor } from '@angular/forms';
import { FocusKeyManager } from '@angular/cdk/a11y';

import { DEFAULT_CLASS_LIST, HeadLessDirective } from '../headless.directive';
import { RadioComponent } from './radio.component';


@Component({
  selector: 'app-radio-group',
  host: {
    tabindex: '0',
    role: 'radio-group'
  },
  template: `<ng-content></ng-content>`,
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: HeadLessDirective,
      inputs: ['class']
    }
  ],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => RadioGroupComponent),
      multi: true
    },
    {
      provide: DEFAULT_CLASS_LIST,
      useValue: `
        inline-flex items-stretch leading-none 
        hover:bg-black/5 text-[#737981]
        transition-all duration-300 rounded
      `
    }
  ]
})
export class RadioGroupComponent implements AfterContentInit, ControlValueAccessor {

  private readonly cdr = inject(ChangeDetectorRef);
  private _value: any = null;
  private _selected: any = null;
  public keyManager: FocusKeyManager<RadioComponent>;
  activeElement = signal<HTMLElement>(null);
  activeElementIndex = signal<number>(null);

  @ContentChildren(RadioComponent, { descendants: true })
  items: QueryList<RadioComponent>;

  disabledAccessibility = input(false, { transform: booleanAttribute });
  enabledJumpToFocus = input(false, { alias: 'jumpToFocus', transform: booleanAttribute });
  orientation = input<'horizontal' | 'vertical'>('horizontal', { alias: 'radioGroupOrientation' });

  @Input()
  get selected() { return this._selected; }
  set selected(selected: any | null) {
    this._selected = selected;
    this.value = selected ? selected.value : null;
    this._checkSelectedRadioButton();
  }

  _checkSelectedRadioButton() {
    if (this._selected && !this._selected.checked) {
      this._selected.checked = true;
    }
  }
  
  @Input()
  get value() { return this._value; }
  set value(newValue: any) {
    if(this._value !== newValue){
      this._value = newValue;
      this._controlValueAccessorChangeFn(newValue);
      this._emitChangeEvent();
      this.onTouched();
    }
  }

  @Output() 
  change: EventEmitter<any> = new EventEmitter<any>();


  ngAfterContentInit() {
    if (this.orientation() === 'vertical') {
      this.keyManager = new FocusKeyManager(this.items).withWrap();
    } else {
      this.keyManager = new FocusKeyManager(this.items).withHorizontalOrientation('ltr').withWrap();
    }
  }

  _controlValueAccessorChangeFn: (value: any) => void = () => {};

  onTouched: any = () => {};

  registerOnChange(fn: (value: any) => void) {
    this._controlValueAccessorChangeFn = fn;
  }

  registerOnTouched(fn: (value: any) => void) {
    this.onTouched = fn;
  }

  writeValue(value: string | number | null | any) {        
    this.value = value;
    this.cdr.markForCheck();
  }

  valueChanged(event: any){
    this._controlValueAccessorChangeFn(this.value);    
  }

  _emitChangeEvent(): void {
    this.change.emit(this._value);

    if (this.enabledJumpToFocus()) {
      this.keyManager.setActiveItem(this.activeElementIndex());
      this.activeElement()?.scrollIntoView({
        behavior: 'smooth', // 平滑滚动
        block: 'center',    // 元素会滚动到视口的中央
        inline: 'center'   // 元素会尽量靠近视口的左边或者右边
      })
    }
  }


  @HostListener('keydown', ['$event'])
  onKeydown(event: KeyboardEvent) {
    if (this.disabledAccessibility()) { return; }
    const { activeItemIndex } = this.keyManager;
    const count = this.items.toArray().length - 1;
    const isNext = (
      (this.orientation() === 'horizontal' && event.code === 'ArrowRight') ||
      (this.orientation() === 'vertical'   && event.code === 'ArrowDown')
    );

    const isPrev = (
      (this.orientation() === 'horizontal' && event.code === 'ArrowLeft') ||
      (this.orientation() === 'vertical'   && event.code === 'ArrowUp')
    );

    let index: number;

    if (isNext) {
      if (activeItemIndex >= count) {
        index = 0;
      } else {
        index = activeItemIndex + 1;
      }
    }

    if (isPrev) {
      if (activeItemIndex <= 0) {
        index = count; 
      } else {
        index = activeItemIndex - 1;
      }
    }

    if (index !== undefined) {
      const item = this.items.toArray()[index];
      
      this.keyManager.onKeydown(event);
      item.elementRef.nativeElement.click();
    }
  }

}
