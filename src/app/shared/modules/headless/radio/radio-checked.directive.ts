import { DestroyRef, Directive, TemplateRef, ViewContainerRef, inject } from '@angular/core';

import { RadioComponent } from './radio.component';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';

@Directive({
  selector: '[appRadioChecked], [radioChecked]',
})
export class RadioCheckedDirective {

  readonly #parent = inject(RadioComponent, { optional: true });
  readonly destroyRef = inject(DestroyRef);

  constructor(
    private readonly viewContainerRef: ViewContainerRef,
    private readonly templateRef: TemplateRef<void>,
  ) {
    if (this.#parent) {
      this.#parent.checked$.pipe(
        takeUntilDestroyed(this.destroyRef)
      ).subscribe(state => {
        this.viewContainerRef.clear();
        
        if (state) {
          this.viewContainerRef.createEmbeddedView(this.templateRef, null);
        }
      })
    }
  }

}
