import { Component, OnInit, ChangeDetectionStrategy, Input, ChangeDetectorRef, HostListener, ElementRef, DestroyRef, inject, input, booleanAttribute, effect, computed } from '@angular/core';
import { FocusOrigin, FocusableOption } from '@angular/cdk/a11y';
import { coerceBooleanProperty } from '@angular/cdk/coercion';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { startWith } from 'rxjs/operators';
import { BehaviorSubject } from 'rxjs';
import * as _ from 'lodash';

import { DEFAULT_DISABLED_CLASS_LIST, HeadLessDirective } from '../headless.directive';
import { RadioGroupComponent } from './radio-group.component';

function getElementIndex(element) {
  let index = 0;
  let sibling = element.previousElementSibling;
  
  // 遍历前面的兄弟元素，逐个计数
  while (sibling) {
      index++;
      sibling = sibling.previousElementSibling;
  }
  
  return index;
}

@Component({
  selector: 'app-radio',
  template: `
    <!-- (change)="_onInputChange($event)" (click)="_onInputClick($event)" -->
    <input type="radio" class="sr-only" [disabled]="disabledRadio()" [checked]="checked" [name]="name()" >

    <ng-content></ng-content>
  `,
  host: {
    tabindex: '-1',
    role: 'radio'
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: HeadLessDirective,
      inputs: ['class', 'activeClass']
    },
  ],
  providers: [
    {
      provide: DEFAULT_DISABLED_CLASS_LIST,
      useValue: `
        bg-neutral-200 cursor-not-allowed
        border border-neutral-300 
        text-neutral-500/60
        hover:text-neutral-500/60
      `
    }
  ],
  exportAs: 'radio'
})
export class RadioComponent implements OnInit, FocusableOption {

  public readonly elementRef = inject<ElementRef<HTMLElement>>(ElementRef);
  private readonly parent = inject(RadioGroupComponent);
  private readonly cdr = inject(ChangeDetectorRef);
  private readonly headLess = inject(HeadLessDirective);
  
  private _value: any = null;
  private _checked: boolean = false;
  private _checkedSubject = new BehaviorSubject<boolean>(this._checked);

  public checked$ = this._checkedSubject.asObservable();

  // @Input() name!: string;
  name = input<string>(null);

  @Input()
  get checked(): boolean { return this._checked; }
  set checked(value: boolean) {
    const newCheckedState = coerceBooleanProperty(value);
    if (this._checked !== newCheckedState) {
      this._checked = newCheckedState;
      this._checkedSubject.next(this._checked);

      if (this._checked) {
        this.parent.selected = this;
      }

      this.headLess.selected = this._checked;
    }
  }
  
  
  @Input()
  get value(): any { return this._value; }
  set value(value: any) {
    if (this._value !== value) {
      this._value = value;
    }
  }

  disabledRadio = input(false, { alias: 'disabled', transform: booleanAttribute });
  enabledRadio = computed(() => !this.disabledRadio());

  readonly destroyRef = inject(DestroyRef);
  
  constructor() {
    effect(() => {
      this.headLess.disabled = this.disabledRadio();
      this.cdr.markForCheck();
    })
  }

  
  ngOnInit(): void {
    this.parent.change.pipe(
      startWith(this.parent.value),
      takeUntilDestroyed(this.destroyRef)
    )
    .subscribe(value => {
      // this.checked = _.isEqual(value, this.value);
      this.checked = value === this.value;
      if (this.checked) {
        const index = getElementIndex(this.elementRef.nativeElement);

        this.parent.activeElement.set(this.elementRef.nativeElement);
        this.parent.activeElementIndex.set(index);
      }
      this.cdr.markForCheck();
    })
  }

  
  _onInputClick(event: Event) {
    event.stopPropagation();
  }


  focus(origin?: FocusOrigin): void {
    this.elementRef.nativeElement.focus();
  }


  @HostListener('click', ['$event'])
  _onInputChange(event: Event) {
    event.stopPropagation();
    if (this.enabledRadio()) {
      this.checked = true;
      // 防止raido change时触发2次修改的问题
      // this.parent._controlValueAccessorChangeFn(this.value);
      this.parent._emitChangeEvent();
      this.parent.keyManager.setActiveItem(this.siblingIndex);
      this.cdr.markForCheck();
    }
  }


  get siblingIndex() {
    const siblings = Array.from(this.elementRef.nativeElement.parentElement.children);
    const siblingIndex = siblings.indexOf(this.elementRef.nativeElement);

    return siblingIndex;
  }

}
