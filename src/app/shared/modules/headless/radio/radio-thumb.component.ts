import { AfterViewInit, ChangeDetectionStrategy, Component, ElementRef, Renderer2, computed, inject, input } from '@angular/core';
import { RadioGroupComponent } from './radio-group.component';

@Component({
  selector: 'app-radio-thumb',
  template: ``,
  host: {
    'class': 'absolute',
    '[style.transform]': 'transform()',
    '[style.width.px]': 'parent.activeElement()?.offsetWidth || 0',
    '[style.height.px]': 'parent.activeElement()?.offsetHeight || 0',
  },
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class RadioThumbComponent implements AfterViewInit {
  
  offsetX = input(0);
  readonly parent = inject(RadioGroupComponent);
  readonly renderer2 = inject(Renderer2);
  readonly element = inject(ElementRef);

  transform = computed(() => {
    const x = (this.parent.activeElement()?.offsetLeft || 0) - this.offsetX();

    return `translateX(${x}px)`;
  })


  ngAfterViewInit(): void {
    setTimeout(() => {
      if (!!this.parent.activeElement()) {
        this.renderer2.addClass(this.element.nativeElement, 'transition-all');
      }
    }, 1000)
  }

}
