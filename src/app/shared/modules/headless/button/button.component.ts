import { AfterViewInit, ChangeDetectionStrategy, Component, ElementRef, inject } from '@angular/core';

@Component({
  selector: 'app-button',
  template: `
    <ng-content />
  `,
  host: {
    'tabindex': '0'
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class ButtonComponent implements AfterViewInit {

  private readonly elementRef = inject(ElementRef);


  ngAfterViewInit(): void {
    const element = this.elementRef.nativeElement as HTMLElement;

    if (element.getAttribute('UIButton') === null) {
      element.setAttribute('UIButton', '');
    }
  }

}
