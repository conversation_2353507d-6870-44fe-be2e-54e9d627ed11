import { AfterViewInit, Directive, ElementRef, Input, booleanAttribute, inject } from '@angular/core';


@Directive({
  selector: '[iconBtn]',
})
export class IconBtnDirective implements AfterViewInit {

  @Input()
  set className(value: string) {
    const element = this.#elementRef.nativeElement as HTMLElement;

    element.classList.add(...value.split(' '));
  }
  
  @Input({ transform: booleanAttribute }) enabled: boolean;

  readonly #elementRef = inject(ElementRef);

  ngAfterViewInit(): void {
    const element = this.#elementRef.nativeElement as HTMLElement;

    if (this.enabled) {
      if (element.getAttribute('iconBtn') === null) {
        element.setAttribute('iconBtn', '');
      }

      if (element.getAttribute('tabindex') === null) {
        element.setAttribute('tabindex', '0');
      }
    } else {
      element.removeAttribute('iconBtn');
      element.removeAttribute('tabindex');
    }
  }

}
