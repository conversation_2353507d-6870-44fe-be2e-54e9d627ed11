import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconBtnDirective } from './icon-btn.directive';

@Component({
  selector: 'app-icon-btn',
  template: `
    <ng-content />
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: IconBtnDirective,
      inputs: ['enabled: iconBtn']
    }
  ],
  imports: [
    CommonModule,
  ],
})
export class IconBtnComponent {

}
