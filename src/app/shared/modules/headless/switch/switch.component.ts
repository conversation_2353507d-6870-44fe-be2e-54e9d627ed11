import { ChangeDetectionStrategy, ChangeDetectorRef, Component, HostListener, Input, forwardRef } from '@angular/core';
import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { HeadLessDirective } from '../headless.directive';
import { isUndefined } from '@common/function';

@Component({
  selector: 'app-switch',
  template: `<ng-content></ng-content>`,
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: HeadLessDirective,
      inputs: ['class', 'activeClass']
    }
  ],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => SwitchComponent),
      multi: true
    },
  ]
})
export class SwitchComponent implements ControlValueAccessor {

  private _checked: boolean | string | number = null;

  @Input()
  trueValue: string | number | undefined;

  @Input()
  falseValue: string | number | undefined;
  
  @Input()
  get checked() { return this._checked; }
  set checked(state: boolean | string | number) {
    if (isUndefined(this.trueValue, this.falseValue)) {
      this._checked = state;
      this.valueChanged(this._checked);
    } else {      
      this._checked = state || state === this.trueValue;
      this._checked ?
        this.valueChanged(this.trueValue) :
        this.valueChanged(this.falseValue);
    }

    this.headLess.selected = this._checked;
  }

  constructor(
    private readonly cdr: ChangeDetectorRef,
    private readonly headLess: HeadLessDirective,
  ) {}

  _controlValueAccessorChangeFn: (value: any) => void = () => {};

  onTouched: any = () => {};

  registerOnChange(fn: (value: any) => void) {
    this._controlValueAccessorChangeFn = fn;
  }

  registerOnTouched(fn: (value: any) => void) {
    this.onTouched = fn;
  }

  writeValue(value: boolean | string | number) {    
    this.checked = value && value === this.trueValue;
    this.cdr.markForCheck();
  }

  valueChanged(value: boolean | string | number){
    this._controlValueAccessorChangeFn(value);
  }

  @HostListener('click')
  toggle() {
    this.checked = !this.checked;
  }

}
