import { AfterContentChecked, ChangeDetectionStrategy, ChangeDetectorRef, Component, DestroyRef, ElementRef, HostListener, Input, effect, inject, input } from '@angular/core';
import { coerceBooleanProperty } from '@angular/cdk/coercion';
import { FocusOrigin, FocusableOption } from '@angular/cdk/a11y';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { BehaviorSubject, startWith } from 'rxjs';
import * as _ from 'lodash';

import { DEFAULT_CLASS_LIST, DEFAULT_ACTIVE_CLASS_LIST, HeadLessDirective, DEFAULT_DISABLED_CLASS_LIST } from '../headless.directive';
import { ListboxComponent } from './listbox.component';


@Component({
  selector: 'app-listbox-option',
  template: `
    <ng-content></ng-content>
  `,
  host: {
    tabindex: '-1'
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: HeadLessDirective,
      inputs: ['class', 'activeClass']
    }
  ],
  providers: [
    {
      provide: DEFAULT_CLASS_LIST,
      useValue: `
        relative flex items-center justify-between gap-x-2 p-2 
        cursor-default select-none whitespace-nowrap rounded-md
        hover:bg-neutral-100 focus:bg-neutral-100 text-neutral-600 transition-all duration-300
      `
    },
    {
      provide: DEFAULT_ACTIVE_CLASS_LIST,
      useValue: `bg-blue-50 hover:bg-blue-100 text-blue-400! font-semibold`
    },
    {
      provide: DEFAULT_DISABLED_CLASS_LIST,
      useValue: `
        cursor-not-allowed
        !text-neutral-500/60
        hover:text-neutral-500/60
      `
    }
  ]
})
export class ListboxOptionComponent implements AfterContentChecked, FocusableOption {

  private _value: any = null;
  private _selected: boolean = false;
  private _selectedSubject = new BehaviorSubject<boolean>(this._selected);

  public label: string;
  public selected$ = this._selectedSubject.asObservable();

  @Input()
  get selected(): boolean { return this._selected; }
  set selected(value: boolean) {
    const newCheckedState = coerceBooleanProperty(value);
    if (this._selected !== newCheckedState) {
      this._selected = newCheckedState;
      this._selectedSubject.next(this._selected);

      if (this._selected) {
        this.parent.selected = this;
      }

      this.headLess.selected = this._selected;
    }
  }
  

  @Input()
  get value(): any { return this._value; }
  set value(value: any) {
    if (this._value !== value) {
      this._value = value;
    }
  }


  disabledOption = input(false);
  
  readonly destroyRef = inject(DestroyRef);
  
  constructor(
    private readonly host: ElementRef<HTMLElement>,
    private readonly parent: ListboxComponent,
    private readonly cdr: ChangeDetectorRef,
    private readonly headLess: HeadLessDirective
  ) {
    effect(() => {
      this.headLess.disabled = this.disabledOption();
      this.cdr.markForCheck();
    });
  }

  
  ngOnInit(): void {
    this.parent.change.pipe(
      startWith(this.parent.value),
      takeUntilDestroyed(this.destroyRef)
    ).subscribe(value => {
      this.selected = _.isEqual(value, this.value);
      this.cdr.markForCheck();
    })
  }


  ngAfterContentChecked(): void {
    this.label = this.label || this.host?.nativeElement?.textContent;
  }


  focus(origin?: FocusOrigin): void {
    this.host.nativeElement.focus();
  }

  
  @HostListener('click', ['$event'])
  _onInputChange(event: Event) {
    event.stopPropagation();
    if (!this.disabledOption()) {
      this.selected = true;
      this.parent._controlValueAccessorChangeFn(this.value);
      this.parent._emitChangeEvent();
      this.parent.close();
    }
  }


  @HostListener('keydown.enter', ['$event'])
  onKeydown(event) {
    if (this.host.nativeElement.focus) {
      this._onInputChange(event);
    }
  }
  
}

