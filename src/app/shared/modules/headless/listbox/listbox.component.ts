import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { ChangeDetectionStrategy, Component, ContentChild, EventEmitter, Input, Output, forwardRef, input } from '@angular/core';
import { CdkOverlayOrigin, OverlayModule } from '@angular/cdk/overlay';

import { Animations } from '@common/animation';
import { BaseOverlayDirective } from '@common/directive';
import { DEFAULT_CLASS_LIST, HeadLessDirective } from '../headless.directive';
import { ListboxOptionsComponent } from './listbox-options.component';
import { ListboxOptionComponent } from './listbox-option.component';

/**
 * @example
 * ```html
 *  <app-listbox #select [(ngModel)]="test" class="inline-block w-48">
      <app-listbox-button style="min-width: 188px;">
        <span class="block truncate">{{select?.selected?.label || '请选择'}}</span>
        
        <span class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
          <app-icon class="text-gray-400" type="chevron-up-down" fontSize="20px"></app-icon>
        </span>
      </app-listbox-button>

      <app-listbox-options>
        <app-listbox-option *ngFor="let option of options" [value]="option.id">
          {{option.name}}
        </app-listbox-option>
      </app-listbox-options>
    </app-listbox>
 * ```
 */
@Component({
  selector: 'app-listbox',
  template: `
    <ng-content select="app-listbox-button"></ng-content>

    <ng-template
      #overlay="cdkConnectedOverlay"
      cdkConnectedOverlay
      [cdkConnectedOverlayHasBackdrop]="false"
      [cdkConnectedOverlayOrigin]="overlayOrigin"
      [cdkConnectedOverlayPositions]="listOfPositions"
      [cdkConnectedOverlayScrollStrategy]="scrollStrategy"
      [cdkConnectedOverlayOpen]="visible()"
      [cdkConnectedOverlayOffsetX]="offsetX()"
      [cdkConnectedOverlayOffsetY]="offsetY()"
      (positionChange)="onPositionChange($event)"
      (overlayOutsideClick)="handleOutside($event)"
      (detach)="close()"
    >
      @if (visible()) {
        <div
          class="relative"
          [style.min-width.px]="overlayOrigin?.elementRef?.nativeElement?.offsetWidth"
          [@slideMotion]="'enter'"
        >
          <ng-content select="app-listbox-options"></ng-content>
        </div>
      }
    </ng-template>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: HeadLessDirective,
      inputs: ['class']
    }
  ],
  animations: [
    Animations.slideMotion,
  ],
  imports: [
    OverlayModule,
  ],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => ListboxComponent),
      multi: true
    },
    {
      provide: DEFAULT_CLASS_LIST,
      useValue: `inline-block`
    }
  ]
})
export class ListboxComponent extends BaseOverlayDirective implements ControlValueAccessor  {

  @ContentChild(CdkOverlayOrigin, { static: true })
  public overlayOrigin: CdkOverlayOrigin;

  @ContentChild(ListboxOptionsComponent)
  public container: ListboxOptionsComponent;

  @Input()
  get selected() { return this._selected; }
  set selected(selected: ListboxOptionComponent | null) {
    this._selected = selected;
    this.value = selected ? selected.value : null;
    if (this._selected && !this._selected.selected) {
      this._selected.selected = true;
    }
  }


  @Input()
  get value() { return this._value; }
  set value(newValue: any) {
    if(this._value !== newValue){
      this._value = newValue;
      this.cdr.markForCheck();
    }
  }

  offsetX = input(0);
  offsetY = input(0);

  @Output()
  readonly change: EventEmitter<any> = new EventEmitter<any>();

  
  private _value: any = null;
  private _selected: ListboxOptionComponent = null;


  constructor() {
    super();
    this.scrollStrategy = this.scrollStrategyOptions.reposition();
  }


  override open() {
    super.open();
    
    setTimeout(() => {
      const index = this.container.items.toArray().findIndex(item => item === this._selected);
      
      this.container.keyManager.setActiveItem(index);
    })
  }
  
  
  _controlValueAccessorChangeFn: (value: any) => void = () => {};

  onTouched: any = () => {};


  writeValue(value: string | number) {    
    this.value = value;
    this._emitChangeEvent();
    this.cdr.markForCheck();
  }


  registerOnChange(fn) {
    this._controlValueAccessorChangeFn = fn;
  }
  
  
  registerOnTouched(fn) {
    this.onTouched = fn;
  }

  
  _emitChangeEvent(): void {
    this.change.emit(this._value);
  }


}
