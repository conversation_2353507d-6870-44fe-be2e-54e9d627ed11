import { ChangeDetectionStrategy, Component, HostListener } from '@angular/core';
import { CdkOverlayOrigin } from '@angular/cdk/overlay';

import { DEFAULT_CLASS_LIST, HeadLessDirective } from '../headless.directive';
import { ListboxComponent } from './listbox.component';

@Component({
  selector: 'app-listbox-button',
  template: `
    <ng-content />
  `,
  host: {
    'tabindex': '0'
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    CdkOverlayOrigin,
    {
      directive: HeadLessDirective,
      inputs: ['class']
    }
  ],
  providers: [
    {
      provide: DEFAULT_CLASS_LIST,
      useValue: `
        relative inline-block  w-full cursor-default rounded-lg 
        bg-white py-2 pl-3 pr-10 text-left shadow-md whitespace-nowrap
        focus:outline-hidden 
        focus-visible:ring-2 
        focus-visible:ring-offset-2 
        focus-visible:ring-white/75
        focus-visible:ring-offset-orange-300 sm:text-sm
        focus-visible:border-indigo-500 
      `
    }
  ]
})
export class ListboxButtonComponent {

  constructor(private readonly parent: ListboxComponent) {}

  @HostListener('click', ['$event'])
  handleClick(event: MouseEvent) {
    this.parent.toggle(event);
  }

}
