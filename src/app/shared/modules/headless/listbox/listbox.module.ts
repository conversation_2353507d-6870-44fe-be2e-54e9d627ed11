import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { ListboxComponent         } from './listbox.component';
import { ListboxButtonComponent   } from './listbox-button.component';
import { ListboxOptionComponent   } from './listbox-option.component';
import { ListboxOptionsComponent  } from './listbox-options.component';
import { ListboxSelectedDirective } from './listbox-selected.directive';


@NgModule({
  imports: [
    CommonModule,
    ListboxComponent,
    ListboxButtonComponent,
    ListboxOptionComponent,
    ListboxOptionsComponent,
    ListboxSelectedDirective,
  ],
  exports: [
    ListboxComponent,
    ListboxButtonComponent,
    ListboxOptionComponent,
    ListboxOptionsComponent,
    ListboxSelectedDirective,
  ]
})
export class ListboxModule { }
