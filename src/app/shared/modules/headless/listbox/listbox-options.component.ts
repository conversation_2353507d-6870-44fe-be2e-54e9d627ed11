import { AfterContentInit, ChangeDetectionStrategy, Component, ContentChildren, HostListener, Input, QueryList } from '@angular/core';
import { FocusKeyManager } from '@angular/cdk/a11y';

import { DEFAULT_CLASS_LIST, HeadLessDirective } from '../headless.directive';
import { ListboxOptionComponent } from './listbox-option.component';

@Component({
  selector: 'app-listbox-options',
  template: `
    <ng-content></ng-content>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: HeadLessDirective,
      inputs: ['class']
    }
  ],
  providers: [
    {
      provide: DEFAULT_CLASS_LIST,
      useValue: `
        block mt-1 mb-1 p-1.5 max-h-60 min-w-full overflow-auto
        rounded-md bg-white text-base shadow space-y-1
        ring-1 ring-black/5
        focus:outline-hidden sm:text-sm 
      `
    }
  ]
})
export class ListboxOptionsComponent implements AfterContentInit {

  @ContentChildren(ListboxOptionComponent, { descendants: true })
  items: QueryList<ListboxOptionComponent>;

  @Input('listBoxOrientation')
  orientation: 'horizontal' | 'vertical' = 'vertical';
  
  public keyManager: FocusKeyManager<ListboxOptionComponent>;

  
  ngAfterContentInit() {
    if (this.orientation === 'vertical') {
      this.keyManager = new FocusKeyManager(this.items).withWrap();
    } else {
      this.keyManager = new FocusKeyManager(this.items).withHorizontalOrientation('ltr').withWrap();
    }
  }


  @HostListener('keydown', ['$event'])
  onKeydown(event: KeyboardEvent) {
    this.keyManager.onKeydown(event);
  }

}

