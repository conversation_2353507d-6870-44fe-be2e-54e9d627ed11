import { ChangeDetectorRef, DestroyRef, Directive, TemplateRef, ViewContainerRef, inject } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { delay, startWith } from 'rxjs';

import { isNotEmpty, isNotNull } from '@common/function';
import { ListboxOptionComponent } from './listbox-option.component';
import { ListboxComponent } from './listbox.component';

class ListboxSelectedContext {
  selected: boolean;
  visible: boolean;
  label: string;
  value: string;
}

@Directive({
  selector: '[appListboxSelected], [listboxSelected]',
})
export class ListboxSelectedDirective {

  #context = new ListboxSelectedContext();
  readonly #parent = inject(ListboxOptionComponent, { optional: true });
  readonly #root = inject(ListboxComponent);
  readonly destroyRef = inject(DestroyRef);

  constructor(
    private readonly cdr: ChangeDetectorRef,
    private readonly viewContainerRef: ViewContainerRef,
    private readonly templateRef: TemplateRef<ListboxSelectedContext>,
  ) {
    if (this.#parent) {
      this.#parent.selected$.pipe(
        delay(100),
        takeUntilDestroyed(this.destroyRef)
      ).subscribe(state => {
        this.viewContainerRef.clear();
        
        if (state) {
          this.#context.label = this.#root.selected?.label;
          this.#context.value = this.#root.selected?.value;
          this.viewContainerRef.createEmbeddedView(this.templateRef, this.#context);
        }
      })
    }
    else {
      this.#root.visibleChange.pipe(
        delay(10),
        startWith(this.#root.visible()),
        takeUntilDestroyed(this.destroyRef)
      ).subscribe(visible => {
        this.viewContainerRef.clear();
        this.#context.visible  = visible;
        this.#context.selected = isNotNull(this.#root.value) && isNotEmpty(this.#root.value);
        this.#context.label    = this.#root.selected?.label;
        this.#context.value    = this.#root.selected?.value;
        this.viewContainerRef.createEmbeddedView(this.templateRef, this.#context);
        this.cdr.markForCheck();
      })

      this.#root.change.pipe(
        delay(100),
        takeUntilDestroyed(this.destroyRef)
      ).subscribe(state => {
        this.viewContainerRef.clear();
        this.#context.selected = isNotNull(state) && isNotEmpty(state);
        this.#context.label    = this.#root.selected?.label;
        this.#context.value    = this.#root.selected?.value;
        this.viewContainerRef.createEmbeddedView(this.templateRef, this.#context);
        this.cdr.markForCheck();
      })
    }
  }

}
