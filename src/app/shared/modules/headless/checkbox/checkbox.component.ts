import { ControlValueAccessor, NG_VALUE_ACCESSOR } from '@angular/forms';
import { ChangeDetectionStrategy, ChangeDetectorRef, Component, HostListener, booleanAttribute, computed, effect, forwardRef, inject, input, signal } from '@angular/core';
import { DEFAULT_ACTIVE_CLASS_LIST, DEFAULT_CLASS_LIST, DEFAULT_DISABLED_CLASS_LIST, HeadLessDirective } from '../headless.directive';


@Component({
  selector: 'app-checkbox',
  template: `
    <label class="cursor-[inherit] select-none inline-flex items-center gap-x-1">
      <input type="checkbox" class="sr-only" [disabled]="disabled()" [checked]="value()" (change)="valueChanged()">
      <ng-content></ng-content>
    </label>
  `,
  host: {
    tabindex: '-1',
    role: 'checkbox'
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: HeadLessDirective,
      inputs: ['class', 'activeClass']
    },
  ],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => CheckboxComponent),
      multi: true
    },
    {
      provide: DEFAULT_CLASS_LIST,
      useValue: `
        inline-flex items-center h-5 px-1.5 text-xs rounded-sm 
        cursor-pointer whitespace-nowrap leading-none uppercase
        hover:text-[#1890ff]
      `
    },
    {
      provide: DEFAULT_ACTIVE_CLASS_LIST,
      useValue: `
        bg-[#1890ff] text-white hover:!text-white
      `
    },
    {
      provide: DEFAULT_DISABLED_CLASS_LIST,
      useValue: `
        bg-neutral-200 cursor-not-allowed
        border border-neutral-300 
        text-neutral-500/60
        hover:text-neutral-500/60
      `
    }
  ]
})
export class CheckboxComponent implements ControlValueAccessor {

  readonly headLess = inject(HeadLessDirective);
  readonly cdr = inject(ChangeDetectorRef);

  disabled = input(false, { transform: booleanAttribute });
  enabled = computed(() => !this.disabled());
  value = signal(false);

  constructor() {
    effect(() => {
      this.headLess.disabled = this.disabled();
      this.cdr.markForCheck();
    })
  }


  _controlValueAccessorChangeFn: (value: any) => void = () => {};

  
  onTouched: any = () => {};

  
  registerOnChange(fn: (value: any) => void) {
    this._controlValueAccessorChangeFn = fn;
  }

  
  registerOnTouched(fn: (value: any) => void) {
    this.onTouched = fn;
  }

  
  writeValue(value: boolean | null) {    
    this.value.set(value);
    this.headLess.selected = value;
    this.cdr.markForCheck();
  }

  
  @HostListener('click')
  valueChanged(){
    if (this.enabled()) {
      this.value.update(state => !state);
      this._controlValueAccessorChangeFn(this.value());
      this.headLess.selected = this.value();
      this.cdr.markForCheck();
    }
  }

}
