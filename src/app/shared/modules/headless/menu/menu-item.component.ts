import { ChangeDetectionStrategy, Component, ElementRef, HostBinding, HostListener, inject } from '@angular/core';
import { FocusOrigin, FocusableOption } from '@angular/cdk/a11y';

import { DEFAULT_CLASS_LIST, ENABLE_HOST_BINDING, HeadLessDirective } from '../headless.directive';
import { MenuComponent } from './menu.component';

@Component({
  selector: 'app-menu-item',
  template: `
    <ng-content></ng-content>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: {
    tabindex: '-1',
    role: 'menu-item',
  },
  hostDirectives: [
    {
      directive: HeadLessDirective,
      inputs: ['class: className']
    }
  ],
  providers: [
    {
      provide: DEFAULT_CLASS_LIST,
      useValue: `
        group flex w-full items-center gap-x-1.5 px-2 py-1.5 text-sm text-gray-900 
        hover:bg-black/5 focus:bg-black/5 rounded select-none cursor-pointer
      `
    },
    {
      provide: ENABLE_HOST_BINDING,
      useValue: false
    }
  ]
})
export class MenuItemComponent implements FocusableOption {
  
  protected readonly element  = inject(ElementRef);
  protected readonly headLess = inject(HeadLessDirective);
  protected readonly parent   = inject(MenuComponent);
  
  @HostBinding('class')
  get className() {
    return this.headLess.className;
  }

  focus(origin?: FocusOrigin): void {
    this.element.nativeElement.focus();
  }

  @HostListener('keydown.enter')
  onKeydown() {
    this.element.nativeElement.click();
  }
  
}
