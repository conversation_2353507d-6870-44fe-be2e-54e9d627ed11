import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, HostListener } from '@angular/core';
import { CdkOverlayOrigin } from '@angular/cdk/overlay';
import { DEFAULT_CLASS_LIST, HeadLessDirective } from '../headless.directive';
import { MenuComponent } from './menu.component';

@Component({
  selector: 'app-menu-button',
  template: `
    <ng-content></ng-content>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: {
    'tabindex': '-1'
  },
  hostDirectives: [
    CdkOverlayOrigin,
    {
      directive: HeadLessDirective,
      inputs: ['class: className']
    },
  ],
  providers: [
    {
      provide: DEFAULT_CLASS_LIST,
      useValue: `
        group inline-flex items-center gap-x-1 rounded-md px-2.5 py-2
        text-xs font-medium cursor-pointer
        hover:bg-black/5 focus:bg-black/5
      `
    },
    // {
    //   provide: ENABLE_HOST_BINDING,
    //   useValue: false
    // }
  ]
})
export class MenuButtonComponent {
  
  constructor(
    protected readonly parent: MenuComponent,
    protected readonly headLess: HeadLessDirective
  ) {}


  @HostListener('click', ['$event'])
  handleClick(event: MouseEvent) {
    this.parent.toggle(event);
  }

}
