import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MenuComponent } from './menu.component';
import { MenuButtonComponent } from './menu-button.component';
import { MenuSeparatorComponent } from './menu-separator.component';
import { MenuItemsComponent } from './menu-items.component';
import { MenuItemComponent } from './menu-item.component';


@NgModule({
  imports: [
    CommonModule,
    MenuComponent,
    MenuButtonComponent,
    MenuItemComponent,
    MenuItemsComponent,
    MenuSeparatorComponent,
  ],
  exports: [
    MenuComponent,
    MenuButtonComponent,
    MenuItemComponent,
    MenuItemsComponent,
    MenuSeparatorComponent,
  ]
})
export class MenuModule { }
