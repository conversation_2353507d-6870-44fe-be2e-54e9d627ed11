import { ChangeDetectionStrategy, Component, ContentChild } from '@angular/core';
import { CdkOverlayOrigin, OverlayModule } from '@angular/cdk/overlay';

import { Animations } from '@common/animation';
import { BaseOverlayDirective } from '@common/directive';
import { HeadLessDirective } from '../headless.directive';
import { MenuItemsComponent } from './menu-items.component';

@Component({
  selector: 'app-menu',
  template: `
    <ng-content select="app-menu-button"></ng-content>

    <ng-template
      #overlay="cdkConnectedOverlay"
      cdkConnectedOverlay
      [cdkConnectedOverlayHasBackdrop]="false"
      [cdkConnectedOverlayOrigin]="overlayOrigin"
      [cdkConnectedOverlayPositions]="listOfPositions"
      [cdkConnectedOverlayScrollStrategy]="scrollStrategy"
      [cdkConnectedOverlayOpen]="visible()"
      (positionChange)="onPositionChange($event)"
      (overlayOutsideClick)="handleOutside($event)"
      (detach)="close()"
    >
      <div
        class="relative"
        [@slideMotion]="'enter'"
        [style.min-width.px]="overlayOrigin?.elementRef?.nativeElement?.offsetWidth"
      >
        <ng-content select="app-menu-items"></ng-content>
      </div>
    </ng-template>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  animations: [
    Animations.slideMotion,
  ],
  hostDirectives: [
    {
      directive: HeadLessDirective,
      inputs: ['class']
    }
  ],
  imports: [
    OverlayModule,
  ],
})
export class MenuComponent extends BaseOverlayDirective {

  @ContentChild(CdkOverlayOrigin, { static: true })
  public overlayOrigin: CdkOverlayOrigin;

  @ContentChild(MenuItemsComponent)
  container: MenuItemsComponent;

  // override open() {
  //   super.open();
    
  //   setTimeout(() => {
  //     this.container.keyManager.setActiveItem(0);
  //   })
  // }

}
