import { ChangeDetectionStrategy, Component } from '@angular/core';
import { HeadLessDirective, DEFAULT_CLASS_LIST } from '../headless.directive';

@Component({
  selector: 'app-menu-separator',
  host: {
    role: 'menu-separator'
  },
  template: ``,
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: HeadLessDirective,
      inputs: ['class: className']
    }
  ],
  providers: [
    {
      provide: DEFAULT_CLASS_LIST,
      useValue: `
        block h-px bg-gray-100 -mx-1 my-1
      `
    }
  ]
})
export class MenuSeparatorComponent {

}
