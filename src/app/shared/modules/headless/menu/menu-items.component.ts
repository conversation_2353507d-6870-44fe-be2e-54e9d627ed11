import { AfterContentInit, ChangeDetectionStrategy, Component, ContentChildren, HostListener, QueryList } from '@angular/core';
import { FocusKeyManager } from '@angular/cdk/a11y';

import { DEFAULT_CLASS_LIST, HeadLessDirective } from '../headless.directive';
import { MenuItemComponent } from './menu-item.component';

@Component({
  selector: 'app-menu-items',
  host: {
    role: 'items'
  },
  template: `
    <ng-content></ng-content>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  hostDirectives: [
    {
      directive: HeadLessDirective,
      inputs: ['class: className']
    }
  ],
  providers: [
    {
      provide: DEFAULT_CLASS_LIST,
      useValue: `
        block p-1 rounded-md bg-white shadow-1
      `
    }
  ]
})
export class MenuItemsComponent implements AfterContentInit {

  @ContentChildren(MenuItemComponent, { descendants: true }) items: QueryList<MenuItemComponent>;

  public keyManager: FocusKeyManager<MenuItemComponent>;

  
  ngAfterContentInit() {
    this.keyManager = new FocusKeyManager(this.items).withWrap();
  }


  @HostListener('keydown', ['$event'])
  onKeydown(event: KeyboardEvent) {
    this.keyManager.onKeydown(event);    
  }
  
}
