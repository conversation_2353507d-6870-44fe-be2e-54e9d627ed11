import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';

import { TabsModule } from './tabs/tabs.module';
import { RadioModule } from './radio/radio.module';
import { ListboxModule } from './listbox/listbox.module';
import { PopoverModule } from './popover/popover.module';
import { SwitchModule } from './switch/switch.module';
import { MenuModule } from './menu/menu.module';
import { ButtonModule } from './button/button.module';



@NgModule({
  imports: [
    CommonModule,
  ],
  exports: [
    TabsModule,
    RadioModule,
    PopoverModule,
    MenuModule,
    ListboxModule,
    SwitchModule,
    ButtonModule
  ]
})
export class HeadlessModule { }
