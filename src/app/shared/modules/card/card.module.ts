import { NgModule } from '@angular/core';
import { CardComponent } from './card.component';
import { CardHeaderComponent } from './card-header.component';
import { CardTitleComponent } from './card-title.component';
import { CardExtraComponent } from './card-extra.component';
import { CardDescriptionComponent } from './card-description.component';
import { CardContentComponent } from './card-content.component';
import { CardFooterComponent } from './card-footer.component';


@NgModule({
  imports: [
    CardComponent,
    CardHeaderComponent,
    CardTitleComponent,
    CardExtraComponent,
    CardDescriptionComponent,
    CardContentComponent,
    Card<PERSON>ooterComponent,
  ],
  exports: [
    Card<PERSON>omponent,
    CardHeaderComponent,
    CardTitleComponent,
    CardExtraComponent,
    CardDescriptionComponent,
    CardContentComponent,
    CardFooterComponent,
  ]
})
export class CardModule { }
