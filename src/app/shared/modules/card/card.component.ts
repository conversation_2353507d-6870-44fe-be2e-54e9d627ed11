import { ChangeDetectionStrategy, Component } from '@angular/core';

/**
 * Card component
 * @example
 * ```html
 * <app-card>
 *   <app-card-header>
 *     <app-card-title></app-card-title>
 *     <app-card-description></app-card-description>
 *   </app-card-header>
 *   <app-card-content>
 *     
 *   </app-card-content>
 *   <app-card-footer>
 *     
 *   </app-card-footer>
 * </app-card>
 * ```
 */
@Component({
  selector: 'app-card',
  template: `
    <ng-content class="" />
  `,
  host: {
    class: 'text-sm text-muted-foreground shadow-xs hover:shadow-lg transition-all rounded-sm overflow-hidden'
  },
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class CardComponent {

}
