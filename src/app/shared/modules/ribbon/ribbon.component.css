@reference "../../../../styles.css";

:host ::ng-deep .ribbon-link {
  @apply
    inline-flex items-center 
    after:content-[''] 
    after:inline-block after:w-1.5 after:h-1.5 
    after:translate-y-px 
    after:border-white
    after:border-r 
    after:border-b 
    after:-rotate-45 
    after:animate-[animate-border_1s_ease-in-out_1.8s_forwards]
    text-white
    hover:underline
    underline-offset-2
    animate-[animate-text_1s_ease-in-out_1.8s_forwards]
  ;
}

@keyframes animate-border {
  0% {
    border-color: #fff;
  }

  100% {
    border-color: #1890ff;
  }
}

@keyframes animate-text {
  0% {
    color: #fff;
  }

  100% {
    color: #1890ff;
  }
}

/* .ribbon-drop-wrapper {
  animation: ribbon-drop .8s ease-in-out forwards;
} */

.ribbon-content-wrapper {
  @apply
    relative flex items-center h-10 max-2xl:h-8 bg-[#0071e3] text-white
    animate-[animate-background_1s_ease-in-out_1.8s_forwards];
}

.ribbon-content-gallery {
  @apply
    flex items-center w-full h-full px-1
    md:w-212.5 
    lg:w-240
    xl:w-256
    2xl:w-320

    mx-auto overflow-hidden;
}

.ribbon-content-scroller {
  @apply
    flex items-center w-full h-full
    transition-transform duration-1000
    whitespace-nowrap;
}

@keyframes animate-background {
  0% {
    background-color: #0071e3;
    color: #fff;
  }

  100% {
    background-color: #f5f5f5;
    color: #000000b3;
  }
}


@keyframes ribbon-drop {
  0% {
    transform: translateY(-100%);
  }

  100% {
    transform: translateY(0);
  }
}