import { AfterViewInit, ChangeDetectionStrategy, Component, computed, contentChildren, output, signal } from '@angular/core';

import { Animations } from '@common/animation';
import { IconChevronLeftComponent, IconChevronRightComponent, IconCloseComponent } from '../icons';
import { RibbonItemComponent } from './ribbon-item/ribbon-item.component';

@Component({
  selector: 'app-ribbon',
  template: `
    @if (undestroyed() && count() > 0) {
      <div class="overflow-hidden" [class.ribbon-drop-wrapper]="loaded()" [@openClose]="visible()" (@openClose.done)="animationDone($event)">
        <div class="ribbon-content-wrapper">
          <div class="ribbon-content-gallery text-xs">
            @if (count() > 1) {
              <ChevronLeftIcon iconBtn (click)="prev()" />
            }

            <div class="flex-1 min-w-0 h-full overflow-hidden">
              <div class="ribbon-content-scroller" [style.width.%]="count() * 100" [style.transform]="translateX()">
                <ng-content></ng-content>
              </div>
            </div>

            @if (count() > 1) {
              <ChevronRightIcon iconBtn (click)="next()" />
            }
          </div>

          <CloseIcon iconBtn class="absolute right-6 text-xs" (click)="close()" />
        </div>
      </div>
    }
  `,
  styleUrls: ['./ribbon.component.css'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  animations: [
    ...Animations.OpenCloseAnimation
  ],
  imports: [
    IconChevronLeftComponent,
    IconChevronRightComponent,
    IconCloseComponent,
  ],
})
export class RibbonComponent implements AfterViewInit {

  ribbonItems = contentChildren(RibbonItemComponent, { descendants: true });
  
  onClose = output();
  loaded = signal(false);
  visible = signal(true);
  undestroyed = signal(true);
  index = signal(0);

  count = computed(() => this.ribbonItems().length)

  translateX = computed(() => {
    const x = -((this.index() / this.count()) * 100);
    
    return `translateX(${x}%)`;
  })

  
  ngAfterViewInit(): void {
    setTimeout(() => {
      this.loaded.set(true);
    }, 800)
  }


  prev(): void {
    if (this.index() === 0) {
      this.index.set(this.count() - 1);
    } else {
      this.index.update(value => value - 1);
    }
  }


  next(): void {
    if (this.index() === this.count() - 1) {
      this.index.set(0);
    } else {
      this.index.update(value => value + 1);
    }
  }


  close(): void {
    this.visible.set(false);
  }


  animationDone(event: AnimationEvent | any): void {
    if (event.fromState === true && event.toState === false) {
      this.undestroyed.set(false);
      this.onClose.emit();
    }
  }

}
