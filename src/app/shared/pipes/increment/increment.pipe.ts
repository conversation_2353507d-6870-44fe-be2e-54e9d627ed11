import { Pipe, PipeTransform } from '@angular/core';
import { Observable } from 'rxjs';

@Pipe({
  name: 'increment',
  standalone: true
})
export class IncrementPipe implements PipeTransform {

  transform(value: number) {
    return new Observable<number>(subscribe => {
      if (!value) {
        subscribe.next(value);
      }
      else {
        let num = 0;
        let interval = null;
        
        interval = setInterval(() => {
          if (num < value) {
            if (value <= 20) {
              num = value;
            } else {
              num += Math.floor((value / 20));
            }
          } else {
            clearInterval(interval);
            num = value;
          }

          subscribe.next(num);
        }, 10);
      }
    });
  }

}
