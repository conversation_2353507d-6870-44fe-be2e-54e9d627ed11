import { Pipe, PipeTransform } from '@angular/core';
import { isObject, isString } from '@common/function';
import pinyin4js from 'pinyin4js';

@Pipe({
  name: 'search',
})
export class SearchPipe implements PipeTransform {

  transform<T = any>(list: T[], keyword: string, key: string): T[] {

    try {
      keyword = keyword.replace(/(\*|\$|\^)/ig, '\\$1');

      return list
        .map(item => {
          if (isString(item)) {
            return item;
          }
          else if (isObject(item)) {
            return {...item, [key]: String(item[key])};
          }
        })
        .filter((item: string | any) => {
          if (isString(item)) {
            return item.toUpperCase().match(keyword) !== null ||
                   item.toUpperCase().match(keyword.toUpperCase()) !== null ||
                   pinyin4js.getShortPinyin(item).match(keyword) !== null ||
                   pinyin4js.convertToPinyinString(item, '', pinyin4js.WITHOUT_TONE).match(keyword) !== null;
          }
          else if (isObject(item)) {
            return item[key].toUpperCase().match(keyword) !== null ||
                   item[key].toUpperCase().match(keyword.toUpperCase()) !== null ||
                   pinyin4js.getShortPinyin(item[key]).match(keyword) !== null ||
                   pinyin4js.convertToPinyinString(item[key], '', pinyin4js.WITHOUT_TONE).match(keyword) !== null;
          }
        });
    } catch {
      return list;
    }
  }

}
