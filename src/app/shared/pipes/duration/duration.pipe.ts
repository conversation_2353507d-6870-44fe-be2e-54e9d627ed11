import { Pipe, PipeTransform } from '@angular/core';
import { _pad } from '@common/function';
import { format } from 'date-fns';

@Pipe({
  name: 'duration',
  pure: false
})
export class DurationPipe implements PipeTransform {

  transform(milliseconds: number): any {
    try {
      const seconds = Math.floor(milliseconds / 1000);
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      const remainingSeconds = seconds % 60;

      return format(new Date(0, 0, 0, hours, minutes, remainingSeconds), 'HH:mm:ss');
    } catch(error) {
      return milliseconds;
    }
  }

}
