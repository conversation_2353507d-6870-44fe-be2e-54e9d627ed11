import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'replaceBy',
  standalone: true
})
export class ReplaceByPipe implements PipeTransform {

  transform<T>(value: any, replaceFn: string, replaceValue?: string): any;
  transform<T>(value: any, replaceFn: ((params: T) => T), replaceValue?: string): any;
  transform<T>(value: any, replaceFn: ((params: T) => T) | string, replaceValue = ''): any {
    if (typeof replaceFn === 'string') {
      return (<string>value).replace(replaceFn, replaceValue);
    }
    if (typeof replaceFn === 'function') {
      return replaceFn(value);
    }
  }

}
