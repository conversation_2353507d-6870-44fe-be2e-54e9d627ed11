import { ChangeDetectionStrategy, Component, booleanAttribute, input } from '@angular/core';
import { IconTrendingDownComponent, IconTrendingUpComponent } from '@shared/modules/icons';
import { ValueFormatter } from '../value-formatter';

@Component({
  selector: 'app-indicator-compare',
  template: `
    <span class="truncate text-neutral-950">{{label()}}</span>
    <span class="inline-flex items-center gap-x-1">
      <value-formatter useColor useMultiplier="false" [value]="value()" [suffix]="symbol()" />

      @if (this.iconVisible()) {
        @if (value() > 0) {
          <TrendingUpIcon />
        } @else if (value() < 0) {
          <TrendingDownIcon />
        }
      }
    </span>
  `,
  host: {
    'class': 'flex-1 min-w-0 flex w-full gap-x-1 text-xs',
    '[class.text-neutral-500]': 'value() == 0',
    '[class.text-green-500]': 'value() < 0',
    '[class.text-red-600]': 'value() > 0',
    '[class]': 'justifyContent()'
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    ValueFormatter,
    IconTrendingDownComponent,
    IconTrendingUpComponent,
  ]
})
export class IndicatorCompareComponent {

  label = input<string>();
  value = input<number>(null);
  justifyContent = input('justify-center');
  iconVisible = input(false, { transform: booleanAttribute });
  symbol = input('%');

}
