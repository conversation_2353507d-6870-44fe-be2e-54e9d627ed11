import { NgModule } from '@angular/core';
import { IndicatorComponent } from './indicator.component';
import { IndicatorCardComponent } from './indicator-card.component';
import { IndicatorTabsCardComponent } from './indicator-tabs-card.component';
import { IndicatorHeaderComponent } from './indicator-header.component';
import { IndicatorAvatarComponent } from './indicator-avatar.component';
import { IndicatorTitleComponent } from './indicator-title.component';
import { IndicatorSubtitleComponent } from './indicator-subtitle.component';
import { IndicatorValueComponent } from './indicator-value.component';
import { IndicatorValueUnitComponent } from './indicator-value-unit.component';
import { IndicatorCompareComponent } from './indicator-compare.component';
import { IndicatorProgressComponent } from './indicator-progress.component';
import { IndicatorContentComponent } from './indicator-content.component';
import { IndicatorGraphComponent } from './indicator-graph.component';


@NgModule({
  imports: [
    IndicatorCardComponent,
    IndicatorTabsCardComponent,
    IndicatorHeaderComponent,
    IndicatorAvatarComponent,
    IndicatorTitleComponent,
    IndicatorSubtitleComponent,
    IndicatorComponent,
    IndicatorValueComponent,
    IndicatorValueUnitComponent,
    IndicatorCompareComponent,
    IndicatorProgressComponent,
    IndicatorContentComponent,
    IndicatorGraphComponent,
  ],
  exports: [
    IndicatorCardComponent,
    IndicatorTabsCardComponent,
    IndicatorHeaderComponent,
    IndicatorAvatarComponent,
    IndicatorTitleComponent,
    IndicatorSubtitleComponent,
    IndicatorComponent,
    IndicatorValueComponent,
    IndicatorValueUnitComponent,
    IndicatorCompareComponent,
    IndicatorProgressComponent,
    IndicatorContentComponent,
    IndicatorGraphComponent,
  ]
})
export class IndicatorModule { }
