import { ChangeDetectionStrategy, Component, computed, input } from '@angular/core';

@Component({
  selector: 'app-indicator-subtitle',
  template: `<ng-content></ng-content>`,
  host: {
    '[class]': 'classList()'
  },
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class IndicatorSubtitleComponent {

  scale = input('scale-75');

  classList = computed(() => {
    return `
      inline-block text-xs 
      leading-none text-neutral-400 
      font-normal whitespace-nowrap 
      origin-left
      ${this.scale()}
    `;
  })
}
