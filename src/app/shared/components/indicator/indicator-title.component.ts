import { ChangeDetectionStrategy, Component, input } from '@angular/core';
import { NzPopoverDirective, NzPopoverModule } from 'ng-zorro-antd/popover';

@Component({
  selector: 'app-indicator-title',
  template: `<ng-content></ng-content>`,
  host: {
    'class': 'font-normal leading-none truncate',
    '[class]': 'fontSize()'
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NzPopoverModule
  ],
  hostDirectives: [
    {
      directive: NzPopoverDirective,
      inputs: ['nzPopoverTitle', 'nzPopoverContent', 'nzPopoverPlacement']
    },
  ]
})
export class IndicatorTitleComponent {

  fontSize = input('text-lg');

}
