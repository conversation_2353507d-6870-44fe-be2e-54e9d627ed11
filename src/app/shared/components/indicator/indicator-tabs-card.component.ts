import { ChangeDetectionStrategy, Component, computed, inject, input, signal } from '@angular/core';
import { MetricsMenuVo } from '@api/caerus/model';
import { HomeService } from '@views/home';


@Component({
  standalone: true,
  selector: 'app-indicator-tabs-card',
  template: `
    <ng-content />
  `,
  styleUrl: './indicator-tabs-card.component.css',
  host: {
    '[class]': 'classList()',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [],
})
export class IndicatorTabsCardComponent {

  homeService = inject(HomeService);
  metrics = input.required<MetricsMenuVo[][]>();
  valueMap = input.required<Map<string, {
    ym_DIFF: number;
    year_COMPARE_VALUE: number;
    ym_DIFF_RATIO: number;
    year_DIFF_RATIO: number;
    value: number;
    year_DIFF: number;
    ym_COMPARE_VALUE: number;
  }>>();

  trendMap = input<Map<string, {
    trendVo: Array<{ year: number, month: number, value: number }>;
  }>>();
  
  classList = computed(() => {
    return `card-theme ${this.metric()?.color || ''}`
  })

  tabIndex = signal(0);
  hasTabs = computed(() => {
    if (this.metrics()) {
      return this.metrics().length > 1;
    }
    return false;
  })

  tabs = computed(() => {
    if (this.metrics()) {
      return this.metrics().map(item => item.at(0));
    }
    return [];
  })

  metric = computed(() => {
    if (this.metrics()) {
      return this.metrics().at(this.tabIndex()).at(0);
    }
  });

  value = computed(() => {
    if (this.metrics()) {
      const { extendName } = this.metrics().at(this.tabIndex()).find(item => item.metricsType === 'value') || {};
      return this.valueMap().get(`COMPARE_${extendName}`);
    }
  });
  
  trendValue = computed(() => {
    if (this.metrics()) {
      const { extendName } = this.metrics().at(this.tabIndex()).find(item => item.metricsType === 'value') || {};
      return this.trendMap()?.get(`COMPARE_${extendName}`);
    }
  });

  target = computed(() => {
    if (this.metrics()) {
      const { extendName } = this.metrics().at(this.tabIndex()).find(item => item.metricsType === 'target') || {};
      return this.valueMap().get(`COMPARE_${extendName}`);
    }
  });

  targetMetric = computed(() => {
    if (this.metrics()) {
      return this.metrics().at(this.tabIndex()).find(item => item.metricsType === 'target') || {} as any;
    }
  });

  rate = computed(() => {
    if (this.metrics()) {
      const { extendName } = this.metrics().at(this.tabIndex()).find(item => item.metricsType === 'rate') || {};
      return this.valueMap().get(`COMPARE_${extendName}`);
    }
  });

  rateMetric = computed(() => {
    if (this.metrics()) {
      return this.metrics().at(this.tabIndex()).find(item => item.metricsType === 'rate') || {} as any;
    }
  });

  timeDiffRate = computed<number>(() => {
    if (
      this.rate() &&
      this.homeService.timeProgressValue()
    ) {
      const rate = this.rate().value * 100;
      const diff = rate - this.homeService.timeProgressValue();

      return Number(diff.toFixed(2));
    }
  })

}
