@reference "../../../../styles.css";

.tick {
  @apply
    absolute z-10
    w-px h-full bg-transparent

    after:content-['']
    after:absolute
    after:left-0 
    after:-translate-x-1
    after:-translate-y-2.5
    after:-rotate-45
    after:block
    after:w-0
    after:h-0
    after:border-4
    after:border-r-transparent
    after:border-t-transparent
  after:border-black
    after:scale-65
}


.tick-label {
  @apply 
  absolute -top-[11px] scale-65 origin-left
  text-xs text-neutral-600 leading-none whitespace-nowrap;
}

.tick-label.start {
  @apply left-1.5;
}

.tick-label.end {
  @apply -right-1;
}