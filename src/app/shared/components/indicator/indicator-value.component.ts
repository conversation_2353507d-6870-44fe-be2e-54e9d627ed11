import { DecimalPipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, input } from '@angular/core';

@Component({
  selector: 'app-indicator-value',
  template: `
    @if (isNumber(value())) {
      {{value() | number: '1.0-2'}}
    }
    @else {
      {{value() || nullValue()}}
    }
    @if (value() !== undefined && value() !== null && unit()) {
      <span class="text-sm">
        {{unit()}}
      </span>
    }
    @if (value() !== undefined && value() !== null && suffix()) {
      <span>
        {{suffix()}}
      </span>
    }
    <ng-content />
  `,
  host: {
    '[class]': 'classList()'
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    DecimalPipe,
  ],
})
export class IndicatorValueComponent {

  value = input.required<number | string>();
  fontSize = input('text-3xl');
  fontColor = input('text-neutral-800');
  textAlign = input('text-center');
  unit = input<string>(null);
  suffix = input<string>(null);
  nullValue = input<string | number>(0);

  classList = computed(() => {
    return `
      relative inline-flex items-end justify-center gap-x-0.5 
      ${this.fontColor()} ${this.textAlign()} ${this.fontSize()}
      leading-none font-sans 
    `;
  })

  isNumber(value: string | number) {
    return typeof value === 'number';
  }

}
