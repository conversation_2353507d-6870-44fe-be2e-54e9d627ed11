import { ChangeDetectionStrategy, Component, booleanAttribute, input } from '@angular/core';

@Component({
  selector: 'app-indicator, app-indicator-card-group, app-indicator-compare-group, app-indicator-value-group, app-indicator-content-group',
  template: `<ng-content></ng-content>`,
  styleUrl: './indicator.component.css',
  host: {
    '[class.flex-col]': 'vertical()',
    '[class]': 'alignItems()+ " " +gap()'
  },
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class IndicatorComponent {

  alignItems = input('items-center');

  vertical = input(false, { transform: booleanAttribute });

  gap = input('gap-2');

}
