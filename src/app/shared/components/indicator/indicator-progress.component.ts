import { AsyncPipe, DecimalPipe } from '@angular/common';
import { ChangeDetectionStrategy, Component, booleanAttribute, computed, inject, input, signal } from '@angular/core';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { addDays } from 'date-fns';

import { isEveryFinite, isNotUndefinedOrNotNull } from '@common/function';
import { CockpitService } from '@views/cockpit/services';
import { DelayPipe } from '@shared/pipes/delay';

function getMaxDaysInCurrentMonth() {
  var now = new Date();
  var year = now.getFullYear();
  var month = now.getMonth() + 1; // 月份从 0 开始，需要加 1
  var date = new Date(year, month, 0);

  return date.getDate();
}

@Component({
  selector: 'app-indicator-progress',
  template: `
    @if (circle()) {
      @if (value() !== null && total() !== null) {
        <span class="absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 z-10 block w-12 h-12" nz-tooltip="{{ value() | number }} / {{ total() | number }}"></span>
      }
      <svg [attr.width]="size()" [attr.height]="size()" viewBox="-11.25 -11.25 112.5 112.5" version="1.1" xmlns="http://www.w3.org/2000/svg" style="transform:rotate(-90deg)">
        <circle r="35" cx="45" cy="45" stroke-width="10" fill="transparent" [attr.stroke-dasharray]="strokeDasharray() + 'px'" stroke-dashoffset="0" stroke="#e8e8e8"></circle>
        <circle r="35" cx="45" cy="45" stroke-width="10" fill="transparent" [attr.stroke-dasharray]="strokeDasharray() + 'px'" [attr.stroke-dashoffset]="progess()" [attr.stroke]="fillColor()" stroke-linecap="butt"></circle>
        @if (value === null || total() === null) {
          <text x="38px" y="48px" fill="#333" font-size="26px" font-weight="bold" style="transform:rotate(90deg) translate(0px, -86px)">-</text>
        } @else {
          <text [attr.x]="textOffsetX()" y="46px" fill="#333" [attr.font-size]="svgTextSize()" font-weight="bold" style="text-anchor: middle; transform:rotate(90deg) translate(0px, -86px)">
            @if (percent() !== null) {
              {{ percent() + '%' }}
            } @else {
              null
            }
          </text>
        }
      </svg>
    } @else {
      <div class="relative flex items-center justify-center gap-x-2">
        <div class="flex-1 relative">
          <header class="relative h-5">
            <span class="absolute left-0 scale-75 origin-left whitespace-nowrap">
              {{ parentFormat() }} 
              @if (diffLabelEnabled()) {
                <span [innerHTML]="diffLabelFormat()"></span>
              }
            </span>
          </header>
          <div class="relative flex items-center h-3 bg-neutral-200 rounded-lg overflow-hidden">
            @if (value() !== null && value() >= 0 && total() !== null) {
              <span class="absolute block inset-0" nz-tooltip="{{ value() | number }} / {{ total() | number }}"></span>
            }

            <div
              [style.width.%]="width() | delay | async"
              [style.backgroundColor]="fillColor()"
              class="w-0 h-full rounded-lg text-right text-xs leading-none transition-all duration-1000"
            >
            </div>
          </div>
        </div>
      </div>

      <div class="relative flex items-center justify-end text-xs text-neutral-400">
        <span class="absolute left-0 scale-75 origin-left whitespace-nowrap">{{ label() }}</span>

        @if (targetLabel()) {
          <span class="inline-block scale-75 origin-right text-right whitespace-nowrap">
            {{ targetLabelFormat() }}
          </span>
        }
      </div>
    }
  `,
  styleUrl: './indicator-progress.component.css',
  host: {
    class: 'relative',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [AsyncPipe, DecimalPipe, NzToolTipModule, DelayPipe],
  providers: [DecimalPipe]
})
export class IndicatorProgressComponent {
  service = inject(CockpitService);
  decimalPipe = inject(DecimalPipe);
  circle = input(false, { transform: booleanAttribute });
  size = input(80);
  fractionDigits = input(0);
  svgTextSize = input('12px');

  strokeDasharray = signal(219.8);

  label = input<string>();
  targetLabel = input<string>();
  diffLabel = input<string>();
  diffLabelDisabled = input(false, {transform: booleanAttribute});
  diffLabelEnabled = computed(() => !this.diffLabelDisabled());

  value = input.required<number>();
  total = input.required<number>();
  nullValuePlaceholder = input('--');

  
  // yesterday = signal(addDays(new Date(), -1));
  // today = signal(new Date().getDate());
  // date = computed(() => this.yesterday().getDate());

  // isCurrentMonth = computed(() => {
  //   const currentMonth = new Date().getMonth() + 1;
  //   return this.today() > 1 && this.service.dateMonth() === currentMonth;
  // });
  
  // days = signal(getMaxDaysInCurrentMonth());
  // datePercent = computed(() => {
  //   return (this.date() / this.days()) * 100;
  // });


  diffValue = computed(() => {
    if (isEveryFinite(this.total(), this.value())) {
      return this.value() - this.total();
    }
    return null;
  });

  
  diffLabelFormat = computed(() => {
    if (isNotUndefinedOrNotNull<any>(this.diffValue(), this.diffLabel())) {
      return this.diffLabel().replace(/{n}/, `${this.decimalPipe.transform(this.diffValue())}`);
    }
    // console.log({
    //   diffValue: this.diffValue(),
    //   diffLabel: this.diffLabel(),
    //   value: this.value(),
    //   total: this.total(),
    // });
    
    return this.nullValuePlaceholder() ?? '(--)';
  })


  targetLabelFormat = computed(() => {
    if (this.targetLabel() === '暂无目标') {
      return this.targetLabel();
    }
    if (this.targetLabel()) {
      const total = this.total() ?? '未设置';
      const totalStr = typeof total === 'number' ? this.decimalPipe.transform(total) : total;
      return `${this.targetLabel()}: ${totalStr}`;
    }
    return '';
  })

  
  fillColor = computed(() => {
    if (this.total() === null) {
      return 'rgba(0, 0, 0, 0)';
    } else if (this.percent() < 100) {
      return '#3b82f6';
    }

    return 'rgb(34 197 94)';
  });


  width = computed(() => {
    return Math.min(100, Math.ceil((this.value() / this.total()) * 100));
  });


  percent = computed(() => {
    let value = 0;
    if (this.value() === null || this.total() === null || this.total() === 0) {
      return 0;
    }

    if (this.fractionDigits() === 0) {
      value = Math.ceil((this.value() / this.total()) * 100) || 0;
    } else {
      value = parseFloat(((this.value() / this.total()) * 100).toFixed(this.fractionDigits())) || 0;
    }

    return Number.isFinite(value) ? value : null;
  });

  parentFormat = computed(() => {
    if (!isEveryFinite(this.value(), this.total())) {
      return this.nullValuePlaceholder();
    }
    return `${this.decimalPipe.transform(this.percent())}%`;
  })
  
  textOffsetX = computed(() => {
    return 45;
  });

  
  progess = computed(() => {
    const percent = Math.min(1, this.value() / this.total());

    return `${this.strokeDasharray() - percent * this.strokeDasharray()}px`;
  });

}
