import { ChangeDetectionStrategy, Component } from '@angular/core';

@Component({
  selector: 'app-line-spin',
  template: `
    <div class="m-auto loader-inner flex items-center justify-center">
      <div></div>
      <div></div>
      <div></div>
      <div></div>
      <div></div>
    </div>
  `,
  styles: [
    `
      .loader-inner > div {
        display: inline-block;
        width: 4px;
        height: 35px;
        margin: 2px;
        background-color: #5484E1;
        border-radius: 2px;
        animation: line-scale-pulse-out-rapid .9s 0s infinite cubic-bezier(0.11,.49,.38,.78);
      }

      .loader-inner > div:nth-child(1),
      .loader-inner > div:nth-child(5) {
        animation-delay: 0.5s;
      }

      .loader-inner > div:nth-child(2),
      .loader-inner > div:nth-child(4) {
        animation-delay: 0.25s;
      }

      @keyframes line-scale-pulse-out-rapid {
        0%, 90% {
          transform: scaley(1)
        }

        80% {
          transform: scaley(.3)
        }
      }
    `
  ],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class LineSpinComponent {

}
