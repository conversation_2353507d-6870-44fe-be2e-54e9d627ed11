import { OverlayModule } from '@angular/cdk/overlay';
import { NgTemplateOutlet } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, effect, input, output, signal } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { NzInputModule } from 'ng-zorro-antd/input';
import { sortBy } from 'lodash';

import { Animations } from '@common/animation';
import { IconChevronDownComponent, IconCloseFillComponent, IconSearchComponent } from '@shared/modules/icons';
import { groupBy, isNull } from '@common/function';
import { ListboxModule, TabsModule } from '@shared/modules/headless';
import { DimensionValueMenuVo } from '@api/caerus/model';
import { SearchPipe } from '@shared/pipes/search';

@Component({
  selector: 'app-more-area',
  template: `
    <app-listbox [offsetX]="-10" [offsetY]="5" [(ngModel)]="value" class="inline-flex h-4.5" (ngModelChange)="onValueChange($event)">
      <app-listbox-button class="link inline-flex items-center gap-x-1">
        <ng-container *listboxSelected="let selected = selected">
          <span class="truncate text-xs" [class.text-primary]="selected">{{ selected ? value()?.value : '更多' }}</span>
        </ng-container>

        <ChevronDownIcon class="text-xs scale-75" />
      </app-listbox-button>

      <app-listbox-options class="block max-w-md min-w-125 max-h-72 min-h-72 bg-white border border-neutral-200 rounded-sm shadow-69 cursor-pointer">
        <app-tab-group class="flex flex-col absolute inset-0">
          <app-tab-list class="flex gap-x-2 w-full px-2 border-b border-gray-200">
            <app-tab class="tab" activeClass="active">按城市查询</app-tab>
            <app-tab class="tab" activeClass="active">按省份查询</app-tab>
            <div class="flex-1 min-w-0"></div>
            <div class="flex items-center">
              <nz-input-group [nzSuffix]="suffixIconSearch" nzSize="small" class="w-44!">
                <input style="font-size: 12px;" type="text" spellcheck="false" nz-input placeholder="请输入(支持拼音、首拼)" [(ngModel)]="keyword" />
              </nz-input-group>
            </div>
            <app-tab-ink-bar />
          </app-tab-list>

          <app-tab-panels class="flex-1 min-h-0 overflow-auto">
            <app-tab-panel>
              <div class="title mx-3 pt-2">完单量排名top 20</div>
              <div>
                <ng-template *ngTemplateOutlet="cityTemplate; context: { $implicit: cityGroupOne() }"></ng-template>
              </div>
              <div>
                <div class="title mx-3 pt-2">完单量排名21-60</div>
                <ng-template *ngTemplateOutlet="cityTemplate; context: { $implicit: cityGroupTwo() }"></ng-template>
              </div>
              <div>
                <div class="title mx-3 pt-2">完单量排名61-120</div>
                <ng-template *ngTemplateOutlet="cityTemplate; context: { $implicit: cityGroupThree() }"></ng-template>
              </div>
              <div>
                <div class="title mx-3 pt-2">完单量排名120+</div>
                <ng-template *ngTemplateOutlet="cityTemplate; context: { $implicit: cityGroupFour() }"></ng-template>
              </div>
            </app-tab-panel>

            <app-tab-panel>
              <ng-template *ngTemplateOutlet="contentTemplate; context: { $implicit: provinces() }"></ng-template>
            </app-tab-panel>
          </app-tab-panels>
        </app-tab-group>
      </app-listbox-options>

      <ng-template #contentTemplate let-options>
        <div class="grid grid-cols-4 gap-x-2 gap-y-1.5 px-3 py-3">
          @for (option of options | search: keyword() : 'value'; track option) {
            <app-listbox-option class="text-xs hover:text-primary/50" activeClass="text-primary" [value]="option">
              {{ option.showValue }}
            </app-listbox-option>
          } @empty {
            <div class="col-span-4 flex flex-col items-center justify-center h-full min-h-44 text-xs select-none cursor-default gap-1">
              <span class="text-[#0a1220]/70 font-semibold">暂无结果</span>
              <span class="text-[#0a1220]/40">调整关键词后重试</span>
            </div>
          }
        </div>
      </ng-template>

      <ng-template #cityTemplate let-options>
        <div class="grid grid-cols-4 gap-x-2 gap-y-1.5 px-3 py-3">
          @for (option of options | search: keyword() : 'value'; track option) {
            <app-listbox-option class="text-xs hover:text-primary/50" activeClass="text-primary" [value]="option">
              {{ option.showValue }}
            </app-listbox-option>
          }
        </div>
      </ng-template>
    </app-listbox>

    <ng-template #suffixIconSearch>
      @switch (true) {
        @case (!!keyword()) {
          <CloseFillIcon class="ant-input-clear-icon" (click)="keyword.set(null)" />
        }
        @default {
          <SearchIcon />
        }
      }
    </ng-template>
  `,
  styleUrl: 'more-area.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
  animations: [Animations.slideMotion],
  host: {
    class: 'link inline-flex items-center gap-x-0.5 leading-0 px-1.5',
  },
  imports: [
    NgTemplateOutlet,
    FormsModule,
    OverlayModule,
    NzInputModule,
    ListboxModule,
    TabsModule,
    IconChevronDownComponent,
    IconCloseFillComponent,
    IconSearchComponent,
    SearchPipe,
  ],
})
export class MoreAreaComponent {
  area = input<DimensionValueMenuVo | string>();
  areaChange = output<DimensionValueMenuVo>();
  options = input<DimensionValueMenuVo[]>();

  keyword = signal('');
  value = signal(null);

  provinces = computed(() => {
    const { province_name } = groupBy<DimensionValueMenuVo>(this.options(), 'extendName');

    return province_name;
  });

  citys = computed(() => {
    const { city_name } = groupBy<DimensionValueMenuVo>(this.options(), 'extendName');

    return city_name;
  });

  cityGroupOne = computed(() => {
    return sortBy(
      this.citys().filter(c => c.showOrder < 21),
      [
        function (o) {
          return o.showOrder;
        },
      ]
    );
  });

  cityGroupTwo = computed(() => {
    return sortBy(
      this.citys().filter(c => c.showOrder > 20 && c.showOrder < 61),
      [
        function (o) {
          return o.showOrder;
        },
      ]
    );
  });

  cityGroupThree = computed(() => {
    return sortBy(
      this.citys().filter(c => c.showOrder > 60 && c.showOrder < 121),
      [
        function (o) {
          return o.showOrder;
        },
      ]
    );
  });

  cityGroupFour = computed(() => {
    return this.citys().filter(c => c.showOrder > 120);
  });

  constructor() {
    effect(() => {
      if (isNull(this.area()) || (<any>this.area())?.defaultShow === 1) {
        this.value.set(null);
      } else {
        const city = this.options().find(item => item.value === this.area());

        if (city) {
          this.onValueChange(city);
        }
      }
    });
  }

  filterFn = (items: Array<{ cityName: string }>) => {
    if (items && this.keyword()) {
      return items.filter(item => item.cityName.indexOf(this.keyword()) > -1);
    }
    return items;
  };

  onValueChange(value: DimensionValueMenuVo) {
    this.value.set(value);
    this.areaChange.emit(value);
  }
}
