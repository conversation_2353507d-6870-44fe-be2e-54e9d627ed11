import { FormsModule, NG_VALUE_ACCESSOR } from '@angular/forms';
import { ChangeDetectionStrategy, Component, computed, forwardRef, inject, signal } from '@angular/core';
import { rxResource } from '@angular/core/rxjs-interop';
import { map } from 'rxjs';

import { CaerusApiService } from '@api/caerus';
import { DimensionValueMenuVo } from '@api/caerus/model';
import { IconLoadingComponent } from '@shared/modules/icons';
import { RadioModule } from '@shared/modules/headless';

import { MoreAreaComponent } from './more-area/more-area.component';

@Component({
  selector: 'app-area-select',
  template: `
    @if (areaDataResource.isLoading()) {
      <LoadingIcon class="text-xs" />
    } @else {
      <app-radio-group class="relative flex gap-x-0.5" [ngModel]="value()" (ngModelChange)="onValueChange($event)">
        <div class="flex flex-nowrap items-start"><!-- gap-0.5 -->
          @for (item of defaultShowCitys(); track $index) {
            <app-radio class="tag-radio" activeClass="active" [value]="item">{{ item?.showValue || '全部' }}</app-radio>
          }
          <app-more-area [options]="moreCitys()" [area]="value()" (areaChange)="onValueChange($event)" />
        </div>
      </app-radio-group>
    }
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [FormsModule, RadioModule, MoreAreaComponent, IconLoadingComponent],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => AreaSelectComponent),
      multi: true
    },
  ]
})
export class AreaSelectComponent {
  readonly apiService = inject(CaerusApiService);

  value = signal<DimensionValueMenuVo | string>(null);

  defaultShowCitys = computed(() => {
    return [
      null,
      this.areaDataResource.value()?.filter(item => item.defaultShow === 1) || []
    ].flat(1);
  });

  moreCitys = computed(() => {
    if (this.areaDataResource.value()) {
      return this.areaDataResource.value()
        .filter(item => item.defaultShow === 0)
        .sort((a, b) => a.showOrder - b.showOrder);
    }
    return [];
  });

  areaDataResource = rxResource({
    loader: () => this.apiService.fetchHomeCityData().pipe(
      map(res => res.data)
    )
  })

  _controlValueAccessorChangeFn: (value: any) => void = () => {};
  onTouched: any = () => {};
  
  registerOnChange(fn: (value: any) => void) {
    this._controlValueAccessorChangeFn = fn;
  }

  registerOnTouched(fn: (value: any) => void) {
    this.onTouched = fn;
  }

  writeValue(value: string) {    
    if (value === '全国' || !value) {
      this.value.set(null);
    } else {
      const city = this.defaultShowCitys().find(item => item?.value === value);
      
      if (city) {
        this.value.set(city);
      } else {
        this.value.set(value);
      }
    }
  }

  onValueChange(value: DimensionValueMenuVo) {
    this.value.set(value);
    this._controlValueAccessorChangeFn(value);
  }

}
