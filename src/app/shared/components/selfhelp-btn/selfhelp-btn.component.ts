import { ChangeDetectionStrategy, Component, computed, inject } from '@angular/core';
import { RouterLink } from '@angular/router';
import { UserInfoService } from '@api/user-info.service';
import { IconDataTrendComponent } from '@shared/modules/icons';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';

@Component({
  selector: 'app-selfhelp-btn',
  template: `
    @if (selfHelpVisible()) {
      <div class="flex flex-col items-center justify-center w-full btn animate__swing">
        <DataTrendIcon iconBtn nzTooltipTitle="自助分析" nzTooltipPlacement="left" nz-tooltip class="xxl text-4xl" routerLink="/analysis/self-help" />
        <span class="text-xs text-slate-500 scale-75 -translate-y-1 whitespace-nowrap origin-top">自助分析</span>
      </div>
    }
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: {
    'class': 'pointer-events-auto'
  },
  imports: [
    RouterLink,
    NzToolTipModule,
    IconDataTrendComponent,
  ]
})
export class SelfhelpBtnComponent {

  readonly userInfoService = inject(UserInfoService);

  selfHelpVisible = computed(() => {
    const menu = this.userInfoService.menu();
    return menu.some(item => item.url === '/analysis/self-help');;
  });
  
}
