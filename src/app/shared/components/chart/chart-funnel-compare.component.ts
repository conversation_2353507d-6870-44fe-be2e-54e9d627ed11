import { ChangeDetectionStrategy, Component, computed, input, output } from '@angular/core'
import { ChartComponent } from './chart.component'
import { toDecimals } from '@common/function'

type LabelPosition =
  /** 漏斗图左侧，orient 为'vertical'时有效。 */
  | 'left'
  /** 漏斗图右侧，orient 为'vertical'时有效。 */
  | 'right'
  /** 漏斗图上侧，orient 为'horizontal'时有效。 */
  | 'top'
  /** 漏斗图下侧，orient 为'horizontal'时有效。 */
  | 'bottom'
  /** 漏斗图梯形内部。 */
  | 'inside'
  /** 漏斗图梯形外部。 */
  | 'outside'
  /** 漏斗图梯形内部右侧。 */
  | 'insideRight'
  /** 漏斗图梯形内部左侧。 */
  | 'insideLeft'
  /** 漏斗图左侧上部。 */
  | 'leftTop'
  /** 漏斗图左侧下部。 */
  | 'leftBottom'
  /** 漏斗图右侧上部。 */
  | 'rightTop'
  /** 漏斗图右侧下部。 */
  | 'rightBottom'
  /** 同 'inside'。 */
  | 'inner'
  /** 同 'inside'。 */
  | 'center'

class SeriesLabel {
  show = true
  fontSize = '12px'
  color = '#000'
  position: LabelPosition
  opacity: 1

  constructor({ show, position, opacity }: Partial<SeriesLabel> = {}) {
    this.show = show ?? true
    this.position = position ?? 'outside'
    this.opacity = opacity ?? 1
  }
}

interface SeriesProperties {
  data: Array<{ value: number; name: string; label: SeriesLabel }>
  position: 'inside' | 'outside' | string
  funnelAlign: string
  left: string
  itemStyle: any
}

interface FormatterParams {
  componentType: 'series'
  // 系列类型
  seriesType: string
  // 系列在传入的 option.series 中的 index
  seriesIndex: number
  // 系列名称
  seriesName: string
  // 数据名，类目名
  name: string
  // 数据在传入的 data 数组中的 index
  dataIndex: number
  // 传入的原始数据项
  data: Object
  // 传入的数据值。在多数系列下它和 data 相同。在一些系列下是 data 中的分量（如 map、radar 中）
  value: number
  // 坐标轴 encode 映射信息，
  // key 为坐标轴（如 'x' 'y' 'radius' 'angle' 等）
  // value 必然为数组，不会为 null/undefined，表示 dimension index 。
  // 其内容如：
  // {
  //     x: [2] // dimension index 为 2 的数据映射到 x 轴
  //     y: [0] // dimension index 为 0 的数据映射到 y 轴
  // }
  encode: Object
  // 维度名列表
  dimensionNames: Array<String>
  // 数据的维度 index，如 0 或 1 或 2 ...
  // 仅在雷达图中使用。
  dimensionIndex: number
  // 数据图形的颜色
  color: string
  // 百分比
  percent: number
}

class Series {
  width = '35%'
  top = '12%'
  height = '90%'
  maxSize = '100%'
  bottom = '5%'
  sort = 'none'
  orient = 'vertical'
  type = 'funnel'
  left: string
  funnelAlign: string
  label = {
    show: true,
    fontSize: '12px',
    color: '#000',
    position: 'outside',
    // verticalAlign: 'top'
  } as any
  labelLine = { show: true }
  data: Array<{ value: number; name: string }>
  itemStyle: any

  constructor({ position, data, left, funnelAlign, itemStyle }: SeriesProperties) {
    this.data = data
    this.label.position = position
    this.left = left
    this.funnelAlign = funnelAlign
    this.itemStyle = itemStyle
    this.label.formatter = (params: FormatterParams) => {
      const { name, value, dataIndex } = params
      const index = Math.max(dataIndex - 1, 0)
      const ratioValue = toDecimals(value / data[index].value, 100, 0)

      if (position === 'left' || position === 'right') {
        return `${name}
${Number.isFinite(value) ? Intl.NumberFormat().format(value) : '-'}
        `
      } else {
        return `${name}
${Number.isFinite(ratioValue) ? ratioValue + '%' : '-'}`
      }
    }
  }
}

class Funnel {
  color = []

  tooltip = {
    show: false,
    trigger: 'item',
    textStyle: { fontSize: 12 },
  } as any

  legend = {
    show: false,
    left: 'center',
    top: 'top',
    orient: 'horizontal',
    type: 'plain',
    data: ['ios', 'android'],
  }

  series: Series[]

  setCategories(value: string[]) {
    this.legend.data = value
  }

  setTheme(values: string[]) {
    this.color = [...values, ...this.color]
  }

  setValue(values: Series[]) {
    this.series = values
  }

  getOption() {
    // return JSON.parse(JSON.stringify(this));
    return this
  }
}

@Component({
  selector: 'app-chart-funnel-compare',
  template: `
    <app-charts [options]="option()" (click)="clickChart($event)" />
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [ChartComponent],
})
export class ChartFunnelCompareComponent {
  click = output()
  themes = input<string[]>([])
  value = input<any>([])

  clickChart(params) {
    this.click.emit(params)
  }

  option = computed(() => {
    const arr = []
    console.log('this.value', this.value())
    this.value().forEach((val, i) => {
      if (Array.isArray(val)) {
        arr.push(
          new Series({
            position: 'outside',
            left: i === 0 ? '12%' : '50%',
            funnelAlign: i === 0 ? 'right' : 'left',
            itemStyle: {
              opacity: i !== 0 ? 0.3 : 1,
            },
            data: val.map((item, index) => {
              const label = new SeriesLabel({
                show: index > 0,
                position: i === 0 ? 'leftTop' : 'rightTop',
                opacity: 1,
              })

              if (label.position === 'rightTop' || label.position === 'leftTop') {
                const rightLabel = <string>item.rightLabel
                if (rightLabel.endsWith('率')) {
                  label.color = '#1890ff'
                }
              }

              return {
                ...item,
                name: item.rightLabel,
                label,
              }
            }),
          }),
          new Series({
            position: i == 0 ? 'left' : 'right',
            funnelAlign: i === 0 ? 'right' : 'left',
            left: i === 0 ? '12%' : '50%',
            itemStyle: {
              opacity: i !== 0 ? 0.3 : 1,
            },
            data: val.map(item => {
              const label = new SeriesLabel({ position: i == 0 ? 'left' : 'right', opacity: 1 })

              return {
                ...item,
                name: item.leftLabel,
                label,
              }
            }),
          })
        )
      }
    })

    const options = new Funnel()

    if (Array.isArray(this.value()[0])) {
      options.setCategories(this.value()[0].map(item => (item ? item.name : '')))
    }
    options.setTheme(this.themes())
    options.setValue(arr)

    return options.getOption()
  })
}
