import {
  ChangeDetectionStrategy,
  Component,
  computed,
  input,
  output,
} from '@angular/core';
import { ChartComponent } from './chart.component';
import { toDecimals } from '@common/function';

type LabelPosition =
  /** 漏斗图左侧，orient 为'vertical'时有效。 */
  | 'left'
  /** 漏斗图右侧，orient 为'vertical'时有效。 */
  | 'right'
  /** 漏斗图上侧，orient 为'horizontal'时有效。 */
  | 'top'
  /** 漏斗图下侧，orient 为'horizontal'时有效。 */
  | 'bottom'
  /** 漏斗图梯形内部。 */
  | 'inside'
  /** 漏斗图梯形外部。 */
  | 'outside'
  /** 漏斗图梯形内部右侧。 */
  | 'insideRight'
  /** 漏斗图梯形内部左侧。 */
  | 'insideLeft'
  /** 漏斗图左侧上部。 */
  | 'leftTop'
  /** 漏斗图左侧下部。 */
  | 'leftBottom'
  /** 漏斗图右侧上部。 */
  | 'rightTop'
  /** 漏斗图右侧下部。 */
  | 'rightBottom'
  /** 同 'inside'。 */
  | 'inner'
  /** 同 'inside'。 */
  | 'center';

class SeriesLabel {
  show = true;
  fontSize = '12px';
  color = '#000';
  position: LabelPosition;

  constructor({ show, position }: Partial<SeriesLabel> = {}) {
    this.show = show ?? true;
    this.position = position ?? 'outside';
  }
}

interface SeriesProperties {
  data: Array<{ value: number; name: string; label: SeriesLabel }>;
  position: 'inside' | 'outside' | string;
}

interface FormatterParams {
  componentType: 'series';
  // 系列类型
  seriesType: string;
  // 系列在传入的 option.series 中的 index
  seriesIndex: number;
  // 系列名称
  seriesName: string;
  // 数据名，类目名
  name: string;
  // 数据在传入的 data 数组中的 index
  dataIndex: number;
  // 传入的原始数据项
  data: Object;
  // 传入的数据值。在多数系列下它和 data 相同。在一些系列下是 data 中的分量（如 map、radar 中）
  value: number;
  // 坐标轴 encode 映射信息，
  // key 为坐标轴（如 'x' 'y' 'radius' 'angle' 等）
  // value 必然为数组，不会为 null/undefined，表示 dimension index 。
  // 其内容如：
  // {
  //     x: [2] // dimension index 为 2 的数据映射到 x 轴
  //     y: [0] // dimension index 为 0 的数据映射到 y 轴
  // }
  encode: Object;
  // 维度名列表
  dimensionNames: Array<String>;
  // 数据的维度 index，如 0 或 1 或 2 ...
  // 仅在雷达图中使用。
  dimensionIndex: number;
  // 数据图形的颜色
  color: string;
  // 百分比
  percent: number;
}

class Series {
  top = 'middle';
  height = '60%';
  maxSize = '100%';
  sort = 'none';
  orient = 'vertical';
  type = 'funnel';
  label = {
    show: true,
    fontSize: '12px',
    color: '#000',
    position: 'outside',
    // verticalAlign: 'top'
  } as any;

  labelLine = { show: true };
  data: Array<{ value: number; name: string }>;

  constructor({ position, data }: SeriesProperties) {
    this.data = data;
    this.label.position = position;
    this.label.formatter = (params: FormatterParams) => {
      const { name, value, dataIndex } = params;
      const index = Math.max(dataIndex - 1, 0);
      const ratioValue = toDecimals(value / data[index].value, 100, 0);

      if (position === 'left') {
        return `${name}
${Number.isFinite(value) ? Intl.NumberFormat().format(value) : '-'}
        `;
      } else {
        return `${name}
${Number.isFinite(ratioValue) ? ratioValue + '%' : '-'}`;
      }
    };
  }
}

class Funnel {
  color = [
    '#5b8ff9',
    '#61ddaa',
    '#65789b',
    '#f6bd16',
    '#7262fd',
    '#78d3f8',
    '#9661bc',
    '#f6903d',
    '#008685',
    '#f08bb4',
  ];

  tooltip = {
    show: false,
    trigger: 'item',
    textStyle: { fontSize: 12 },
  } as any;

  legend = {
    show: false,
    left: 'center',
    top: 'top',
    orient: 'horizontal',
    type: 'plain',
    data: ['ios', 'android'],
  };

  series: Series[];

  setCategories(value: string[]) {
    this.legend.data = value;
  }

  setTheme(values: string[]) {
    this.color = [...values, ...this.color];
  }

  setValue(values: Series[]) {
    this.series = values;
  }

  getOption() {
    // return JSON.parse(JSON.stringify(this));
    return this;
  }
}

@Component({
  selector: 'app-chart-funnel',
  template: `
    <app-charts [options]="option()" (click)="clickChart($event)" />
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [ChartComponent],
})
export class ChartFunnelComponent {
  click = output();
  themes = input<string[]>([]);
  value = input<
    Array<{
      name: string;
      value: number;
      leftLabel: string;
      rightLabel: string;
    }>
  >([]);

  clickChart(params) {
    this.click.emit(params);
  }

  option = computed(() => {
    const series = [
      new Series({
        position: 'outside',
        data: this.value().map((item, index) => {
          const label = new SeriesLabel({
            show: index > 0,
            position: 'rightTop',
          });

          return {
            ...item,
            name: item.rightLabel,
            label,
          };
        }),
      }),
      new Series({
        position: 'left',
        data: this.value().map((item, index) => {
          const label = new SeriesLabel({ position: 'left' });

          return {
            ...item,
            name: item.leftLabel,
            label,
          };
        }),
      }),
    ];

    const options = new Funnel();

    options.setCategories(this.value().map((item) => item.name));
    options.setTheme(this.themes());
    options.setValue(series);

    return options.getOption();
  });
}
