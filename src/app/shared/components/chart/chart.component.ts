import {
  ChangeDetectionStrategy,
  Component,
  HostListener,
  input,
  output,
} from '@angular/core';
import { NGX_ECHARTS_CONFIG, NgxEchartsModule } from 'ngx-echarts';

import * as echarts from './custom-echarts';

@Component({
  selector: 'app-charts',
  template: `
    @if (options()) {
      <div echarts class="h-full" [options]="options()" (chartInit)="onChartInit($event)"></div>
    }
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgxEchartsModule],
  providers: [{ provide: NGX_ECHARTS_CONFIG, useValue: { echarts } }],
})
export class ChartComponent {
  options = input(null);
  click = output();

  echartsInstance: any;
  selectedLegend = null;
  isPressShift = false;

  onChartInit(instance: any) {
    this.echartsInstance = instance;
    this.echartsInstance.on('legendselectchanged', (event) => {
      if (this.isPressShift) {
        this.multiSelectStrategy(event);
      } else {
        this.singleSelectStrategy(event);
      }
    });
    this.echartsInstance.on('click', (params) => {
      this.click.emit(params);
    });
  }

  private singleSelectStrategy(event: any) {
    if (this.selectedLegend !== event.name) {
      this.selectedLegend = event.name;
    } else {
      this.selectedLegend = null;
    }

    if (this.selectedLegend === null) {
      this.selectAll(event.selected);
    } else {
      this.selectOne(event.selected, event.name);
    }
  }

  private multiSelectStrategy(event: any) {
    this.toggleSelect(event.name);
  }

  private selectOne(list: any, name: string) {
    Object.keys(list).forEach((key) => {
      if (key !== name) {
        this.unselect(key);
      } else {
        this.select(name);
      }
    });
  }

  private selectAll(list: any) {
    Object.keys(list).forEach((key) => this.select(key));
  }

  private select(name: string) {
    this.echartsInstance.dispatchAction({
      type: 'legendSelect',
      name, // 图例名称
    });
  }

  private unselect(name: string) {
    this.echartsInstance.dispatchAction({
      type: 'legendUnSelect',
      name, // 图例名称
    });
  }

  private toggleSelect(name: string) {
    console.log(name);
  }

  @HostListener('window:resize')
  handleResize() {
    if (this.echartsInstance) {
      this.echartsInstance.resize();
    }
  }

  @HostListener('document:keydown', ['$event'])
  handleKeyDown(event: KeyboardEvent) {
    this.isPressShift = event.shiftKey;
  }

  @HostListener('document:keyup', ['$event'])
  handleKeyUp(event: KeyboardEvent) {
    this.isPressShift = false;
  }
}
