import { ChangeDetectionStrategy, Component, computed, input } from '@angular/core';
import { QueryOutputVo } from '@api/query-engine/model';
import { ChartComponent } from './chart.component';


export class xAxisItem {
  type = 'category';
  data: string[];
  axisTick = {
    alignWithLabel: true
  };

  constructor() {

  }
}


export class SeriesItem {
  name: string;
  type = 'line';
  xAxisIndex: number;
  smooth = true;
  // areaStyle: {},
  data: number[];

  constructor() {

  }
}

class LineMultipleXAxis {

  color = ['#5470C6', '#EE6666'];
  tooltip = { trigger: 'axis' };
  legend = {};
  xAxis = [];
  yAxis = [{ type: 'value' }];
  series = [];
  dataZoom = [
    { type: 'slider', start: 0, end: 100 },
    { start: 0, end: 100 }
  ];


  setXaxis() {}
  setSeries(series: SeriesItem[]) {
    this.series = series;
  }

  getOption() {
    // return JSON.parse(JSON.stringify(this));
    return this;
  }
  
}


@Component({
  selector: 'app-chart-multiple-x-axis',
  template: `
    @if (option()) {
      <app-charts [options]="option()" />
    }
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    ChartComponent,
  ],
})
export class ChartMultipleXAxisComponent {

  data = input.required<QueryOutputVo>();

  option = computed(() => {
    const options = new LineMultipleXAxis();

    console.log(this.data());
    console.log(options.getOption());
    
    return null;
  })

}
