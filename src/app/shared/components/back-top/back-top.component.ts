import { ChangeDetectionStrategy, Component } from '@angular/core';
import { IconBackTopComponent } from '@shared/modules/icons';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';

@Component({
  selector: 'app-back-top',
  template: `
    <button class="backTop" nzTooltipTitle="返回顶部" nzTooltipPlacement="left" nz-tooltip (click)="backtop()">
      <BackTopIcon />
    </button>
  `,
  styles: ``,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NzToolTipModule,
    IconBackTopComponent,
  ],
})
export class BackTopComponent {

  backtop() {
    window.scrollTo({ top: 0, behavior: 'smooth' });
  }
  
}
