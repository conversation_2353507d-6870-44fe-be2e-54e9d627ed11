import { ControlValueAccessor, FormsModule, NG_VALUE_ACCESSOR } from '@angular/forms';
import { ChangeDetectionStrategy, Component, computed, forwardRef, input, signal } from '@angular/core';
import { differenceInCalendarDays, addDays, differenceInCalendarMonths, addMonths, subYears, format, getISOWeek } from 'date-fns';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzSelectModule } from 'ng-zorro-antd/select';


@Component({
  selector: 'app-date-filter',
  template: `
    <nz-select class="w-28 mr-2!" [(ngModel)]="dateType" (ngModelChange)="onDateTypeChange($event)">
      @for (item of unitOptions(); track $index) {
        <nz-option [nzLabel]="item.label" [nzValue]="item.value"></nz-option>
      }
    </nz-select>

    <nz-date-picker
      [nzShowToday]="false"
      [nzMode]="dateMode()"
      [nzFormat]="dateFormat()"
      [nzDisabledDate]="dateMode() === 'month' ? disabledMonth : disabledDate"
      [(ngModel)]="date"
      (ngModelChange)="handleDateChange($event)"
    />
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    FormsModule,
    NzDatePickerModule,
    NzSelectModule,
  ],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => DateFilterComponent),
      multi: true
    }
  ]
})
export class DateFilterComponent implements ControlValueAccessor {

  defaultMonth = input<Date>();
  today = signal(new Date());
  date = signal<Date>(null);
  
  dateType = signal<'dt' | 'yw' | 'ym'>('ym');

  dateMode = computed(() => {
    switch(this.dateType()) {
      case 'dt':
        return null;
      case 'yw':
        return 'week';
      case 'ym':
        return 'month';
    }
  });

  dateFormat = computed(() => {
    switch(this.dateType()) {
      case 'dt':
        return 'yyyy/MM/dd';
      case 'yw':
        return 'yyyy第ww周';
      case 'ym':
        return 'yyyy/MM';
    }
  })
  
  format = computed(() => {
    switch(this.dateType()) {
      case 'dt':
        return 'yyyy-MM-dd';
      case 'yw':
        return 'yyyy-ww';
      case 'ym':
        return 'yyyy-MM';
    }
  })
  
  unitOptions = signal([
    { label: '日', value: 'dt' },
    { label: '周', value: 'yw' },
    { label: '月', value: 'ym' },
  ]);

  startTime = computed(() => {
    const value = this.date();

    if (value) {
      const date = subYears(value, 2);
      date.setMonth(0);
      date.setDate(1);
      
      // const week = getISOWeek(date);
      // const year = date.getFullYear();

      // if (this.dateType() === 'yw') {
      //   return `${year}-${week}`;
      // }
      return format(date, this.format());
    }

    return value;
  })
  
  endTime = computed(() => {
    const value = this.date();

    if (value) {
      const week = getISOWeek(value);
      const year = value.getFullYear();
      
      if (this.dateType() === 'yw') {
        return `${year}-${week}`;
      }
      return format(value, this.format());
    }
    
    return value;
  })
  
  change = (_: any) => { };
  registerOnTouched(fn: any) { }

  registerOnChange(fn: any) {
    this.change = fn;
  }

  writeValue(value: Date) {
    this.date.set(value);
    this.handleDateChange(this.date());
  }

  handleDateChange(value: Date) {
    if (value) {
      this.change(format(value, this.format()));
    } else {
      this.change(value);
    }
  }
    
  disabledDate = (current: Date): boolean => {
    // Can not select days before today and today
    return differenceInCalendarDays(current, this.today()) > -1;
  }

  disabledMonth = (current: Date): boolean => {
    // Can not select days before today and today
    const startDateForMonth = new Date().setDate(1);
    const dateRight = addMonths(startDateForMonth, 1);
    
    return differenceInCalendarMonths(current, dateRight) > -1;
  }

  onDateTypeChange(value: 'dt' | 'yw' | 'ym') {
    const now = new Date().setHours(0, 0, 0, 0);

    switch (value) {
      case 'ym':
        this.date.set(this.defaultMonth());
        break;
      case 'yw':
      case 'dt':
        this.date.set(addDays(now, -1));
        break;
      default:
        this.date.set(null);
        break;
    }

    this.handleDateChange(this.date());
  }
  
}
