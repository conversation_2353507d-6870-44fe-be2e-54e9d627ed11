import { OverlayModule } from '@angular/cdk/overlay';
import { AfterViewInit, ChangeDetectionStrategy, Component, computed, DestroyRef, ElementRef, forwardRef, inject, input, linkedSignal, model, signal, viewChild } from '@angular/core';
import { ControlValueAccessor, FormsModule, NG_VALUE_ACCESSOR } from '@angular/forms';
import { NzCheckboxModule } from 'ng-zorro-antd/checkbox';
import { NzIconDirective } from 'ng-zorro-antd/icon';
import { NzInputModule } from 'ng-zorro-antd/input';
import { rxResource, takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { map, startWith, tap } from 'rxjs';

import { NewBaseOverlayDirective } from '@common/directive';
import { groupBy, isNotUndefinedOrNotNull } from '@common/function';
import { CaerusApiService } from '@api/caerus';
import { DimensionValueMenuVo } from '@api/caerus/model';
import { IconChevronDownComponent } from '@shared/modules/icons';
import { RadioModule } from '@shared/modules/headless';
import { SearchPipe } from '@shared/pipes/search';


@Component({
  selector: 'app-city-picker',
  template: `
    <div cdkOverlayOrigin class="city-picker line-input" [class.disabled]="disabled()" (click)="toggle($event)">
      <span class="text-sm select-none truncate">{{label() || placeholder()}}</span>
      <ChevronDownIcon class="ml-auto text-neutral-400" [class.rotate-180]="visible()" />
    </div>

    <ng-template
      #overlay="cdkConnectedOverlay"
      cdkConnectedOverlay
      [cdkConnectedOverlayHasBackdrop]="false"
      [cdkConnectedOverlayOrigin]="cdkOverlayOrigin()"
      [cdkConnectedOverlayPositions]="listOfPositions"
      [cdkConnectedOverlayScrollStrategy]="scrollStrategy"
      [cdkConnectedOverlayOpen]="visible()"
      (positionChange)="onPositionChange($event)"
      (overlayOutsideClick)="handleOutside($event)"
      (detach)="close()"
    >
      <ng-template #suffixIconSearch>
        <nz-icon nzType="search" />
      </ng-template>
      
      <div class="py-1">
        <div class="flex flex-col gap-y-2 min-w-[520px] h-80 bg-white border border-neutral-200 rounded-sm shadow-xl p-2" [style.width.px]="cdkOverlayOrigin().elementRef.nativeElement.offsetWidth">
          <header class="flex items-center gap-x-2">
            <div>
              <nz-input-group [nzSuffix]="suffixIconSearch" nzSize="small">
                <input type="text" nz-input placeholder="请输入" [(ngModel)]="keyword" nzSize="small" />
              </nz-input-group>
            </div>

            <app-radio-group class="inline-flex items-center gap-x-0.5 ml-auto" [ngModel]="type()">
              <app-radio class="line-radio text-xs!" activeClass="active" [value]="0" (mouseup)="scrollTo(0)">核心区域</app-radio>
              <app-radio class="line-radio text-xs!" activeClass="active" [value]="1" (mouseup)="scrollTo(1)">top城市</app-radio>
              <app-radio class="line-radio text-xs!" activeClass="active" [value]="2" (mouseup)="scrollTo(2)">省份</app-radio>
              <app-radio class="line-radio text-xs!" activeClass="active" [value]="3" (mouseup)="scrollTo(3)">城市</app-radio>
            </app-radio-group>
          </header>

          <div>
            <nz-checkbox-wrapper class="w-full">
              <label class="ml-0!" nz-checkbox [nzDisabled]="isMaximum() && !nationwide()" [(ngModel)]="nationwide" (ngModelChange)="updateValue()">
                <dd class="text-xs mb-0">全国</dd>
              </label>
            </nz-checkbox-wrapper>
          </div>
          <div #scrollContainer class="flex-1 min-h-0 overflow-auto" (scroll)="onScroll()">
            <nz-checkbox-wrapper class="w-full">
              <dl class="grid grid-cols-4 gap-1">
                <dt class="sticky top-0 z-10 flex items-start gap-x-1 bg-white col-span-4 border-b border-neutral-200 text-xs font-bold pb-2">
                  核心区域
                </dt>
                @for (item of regions() | search: keyword(): 'showValue'; track item) {
                  <label class="ml-0!" nz-checkbox [nzDisabled]="isMaximum() && !item.checked" [nzValue]="item" [(ngModel)]="item.checked" (ngModelChange)="updateValue()">
                    <dd class="text-xs mb-0">{{item.showValue}}</dd>
                  </label>
                }
              </dl>
            </nz-checkbox-wrapper>
            <nz-checkbox-wrapper class="w-full">
              <dl class="grid grid-cols-4 gap-1">
                <dt class="sticky top-0 z-10 flex items-start gap-x-1 bg-white col-span-4 border-b border-neutral-200 text-xs font-bold pb-2">
                  top城市
                </dt>
                @for (item of top20() | search: keyword(): 'showValue'; track item) {
                  <label class="ml-0!" nz-checkbox [nzDisabled]="isMaximum() && !item.checked" [nzValue]="item" [(ngModel)]="item.checked" (ngModelChange)="updateValue()">
                    <dd class="text-xs mb-0">{{item.showValue}}</dd>
                  </label>
                }
              </dl>
            </nz-checkbox-wrapper>
            <nz-checkbox-wrapper class="w-full">
              <dl class="grid grid-cols-4 gap-1">
                <dt class="sticky top-0 z-10 flex items-start gap-x-1 bg-white col-span-4 border-b border-neutral-200 text-xs font-bold pb-2">
                  省份
                </dt>
                @for (item of province() | search: keyword(): 'showValue'; track item) {
                  <label class="ml-0!" nz-checkbox [nzDisabled]="isMaximum() && !item.checked" [nzValue]="item" [(ngModel)]="item.checked" (ngModelChange)="updateValue()">
                    <dd class="text-xs mb-0">{{item.showValue}}</dd>
                  </label>
                }
              </dl>
            </nz-checkbox-wrapper>
            <nz-checkbox-wrapper class="w-full">
              <dl class="grid grid-cols-4 gap-1">
                <dt class="sticky top-0 z-10 flex items-start gap-x-1 bg-white col-span-4 border-b border-neutral-200 text-xs font-bold pb-2">
                  城市
                </dt>
                @for (item of cities() | search: keyword(): 'showValue'; track item) {
                  <label class="ml-0!" nz-checkbox [nzDisabled]="isMaximum() && !item.checked" [nzValue]="item" [(ngModel)]="item.checked" (ngModelChange)="updateValue()">
                    <dd class="text-xs mb-0">{{item.showValue}}</dd>
                  </label>
                }
              </dl>
            </nz-checkbox-wrapper>
          </div>
        </div>
      </div>
    </ng-template>
  `,
  styleUrl: './city-picker.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    FormsModule,
    OverlayModule,
    NzCheckboxModule,
    NzInputModule,
    NzIconDirective,
    RadioModule,
    IconChevronDownComponent,
    SearchPipe,
  ],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => CityPickerComponent),
      multi: true
    },
  ]
})
export class CityPickerComponent extends NewBaseOverlayDirective implements AfterViewInit, ControlValueAccessor {

  readonly destroyRef = inject(DestroyRef);
  apiService = inject(CaerusApiService);
  maxMultipleCount = input<number>(null);
  maxMultipleCount$ = toObservable(this.maxMultipleCount);
  placeholder = input<string>(null);
  disabled = model(false);
  
  override scrollStrategy = this.scrollStrategyOptions.reposition();
  _scrollContainer = viewChild<ElementRef<HTMLElement>>('scrollContainer');

  isManual = signal(false);
  keyword = signal<string>(null);
  value = signal<Array<{ key: string, value: string }>>([]);
  nationwide = signal(true);
  type = signal(0);
  
  isMaximum = linkedSignal({
    source: () => this.value(),
    computation: (source) => {
      console.clear();
      console.log(source);
      if (source.length >= this.maxMultipleCount()) {
        return true;
      }
      return false;
    }
  })
  
  label = computed(() => {
    if (this.value()) {
      const labels = this.value()
        .filter(isNotUndefinedOrNotNull)
        .map(item => item?.value || '全国');

      if (this.nationwide()) {
        labels.unshift('全国');
      }

      return labels.join('、');
    }
    return '';
  })

  areaDataResource = rxResource({
    loader: () => this.apiService.fetchHomeCityData().pipe(
      map(res => groupBy<DimensionValueMenuVo & { checked?: boolean }>(res.data, 'extendName')),
      tap(res => console.log(res))
    )
  })

  regions = linkedSignal(() => {
    if (this.areaDataResource?.value()) {
      return this.areaDataResource.value()['city_bio_region'];
    }
    return [];
  })

  cities = linkedSignal(() => {
    if (this.areaDataResource?.value()) {
      return this.areaDataResource.value()['city_name'];
    }
    return [];
  })

  province = linkedSignal(() => {
    if (this.areaDataResource?.value()) {
      return this.areaDataResource.value()['province_name'];
    }
    return [];
  })

  top20 = linkedSignal(() => {
    if (this.areaDataResource?.value()) {
      return this.areaDataResource.value()['is_top20_city'];
    }
    return [];
  })

  ngAfterViewInit(): void {
    this.maxMultipleCount$.pipe(
      startWith(this.maxMultipleCount()),
      takeUntilDestroyed(this.destroyRef)
    ).subscribe(count => {
      // console.log('[maxMultipleCount]', count);
      if (this.value().length >= count) {
        this.restore();
      }
      this.updateValue();
    })
  }

  _controlValueAccessorChangeFn: (value: any) => void = () => {};
  onTouched: any = () => {};
  
  registerOnChange(fn: (value: any) => void) {
    this._controlValueAccessorChangeFn = fn;
  }

  registerOnTouched(fn: (value: any) => void) {
    this.onTouched = fn;
  }

  writeValue(value: Array<{ key: string, value: string }>) {   
  }
  
  setDisabledState(isDisabled: boolean): void {
    this.disabled.set(isDisabled);
  }

  private restore() {
    this.regions.update(items => items.map(item => ({ ...item, checked: false })));
    this.top20.update(items => items.map(item => ({ ...item, checked: false })));
    this.province.update(items => items.map(item => ({ ...item, checked: false })));
    this.cities.update(items => items.map(item => ({ ...item, checked: false })));
  }

  updateValue() {
    const regions  = this.regions().filter(item => item.checked);
    const top20    = this.top20().filter(item => item.checked);
    const province = this.province().filter(item => item.checked);
    const cities   = this.cities().filter(item => item.checked);
    const value    = [regions, cities, top20, province].flat(1).map(({ key, value, extendName }) => ({ key, value, extendName })) as any[];
    
    if (this.nationwide()) {
      value.unshift(null);
    }

    // console.log('[全国]', this.nationwide());
    // console.log('[value]', value);
    this._controlValueAccessorChangeFn(value);
    this.value.set(value);
  }

  scrollTo(index: number) {
    const element = this._scrollContainer().nativeElement.getElementsByTagName('nz-checkbox-wrapper')[index] as HTMLElement;

    this.isManual.set(true);
    this._scrollContainer().nativeElement.scrollTo({ top: element.offsetTop - 80, behavior: 'smooth' })
    setTimeout(() => {
      this.isManual.set(false);
    }, 500);
  }

  onScroll() {
    if (this.isManual()) { return; }

    const elements = this._scrollContainer().nativeElement.getElementsByTagName('nz-checkbox-wrapper') as any;
    const obj = {
      scrollTop: this._scrollContainer().nativeElement.scrollTop,
      s1: elements[0].offsetTop - 83,
      s2: elements[1].offsetTop - 83,
      s3: elements[2].offsetTop - 83,
      s4: elements[3].offsetTop - 83,
    };
    const arr = [
      obj.scrollTop > obj.s1,
      obj.scrollTop > obj.s2,
      obj.scrollTop > obj.s3,
      obj.scrollTop > obj.s4,
    ];

    const index = arr.findIndex(item => item === false);
    
    if (index === -1) {
      this.type.set(3);
    } else {
      this.type.set(Math.max(index - 1, 0));
    }
  }

}
