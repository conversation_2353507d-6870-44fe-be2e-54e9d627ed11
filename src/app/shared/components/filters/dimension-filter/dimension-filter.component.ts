import { ControlValueAccessor, FormsModule, NG_VALUE_ACCESSOR } from '@angular/forms';
import { ChangeDetectionStrategy, Component, forwardRef, inject, input, signal } from '@angular/core';

import { CaerusApiService } from '@api/caerus';
import { RadioModule } from '@shared/modules/headless';
import { FilterItemVo } from '@api/caerus/model';

@Component({
  selector: 'app-dimension-filter',
  template: `
    <strong class="inline-flex items-center font-bold text-xs/none whitespace-nowrap">{{label()}}：</strong>
    <app-radio-group [(ngModel)]="value" (ngModelChange)="change($event)" class="relative flex flex-nowrap"><!-- gap-0.5 -->
      <app-radio class="tag-radio" activeClass="active" [value]="null">全部</app-radio>
      @for (item of options(); track $index) {
        <app-radio class="tag-radio" activeClass="active" [value]="item.value">{{ item.key }}</app-radio>
      }
    </app-radio-group>
  `,
  host: {
    class: 'inline-flex items-center'
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    FormsModule,
    RadioModule,
  ],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => DimensionFilterComponent),
      multi: true
    }
  ]
})
export class DimensionFilterComponent implements ControlValueAccessor {

  readonly caerusApiService = inject(CaerusApiService);
  
  label = input.required<string>();
  options = input.required<Array<{ key: string, value: Partial<FilterItemVo> }>>();
  value = signal<FilterItemVo>(null);

  change = (_: any) => { };
  registerOnTouched(fn: any) { }

  registerOnChange(fn: any) {
    this.change = fn;
  }

  writeValue(value: FilterItemVo) {
    this.value.set(value);
  }

}
