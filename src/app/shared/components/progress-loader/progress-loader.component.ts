import { ChangeDetectionStrategy, Component } from '@angular/core';

@Component({
  selector: 'app-progress-loader',
  template: `
    <div class="loader"></div>
  `,
  styles: `
    :host { display: contents; }

    .loader {
      display: block;
      --height-of-loader: 4px;
      --loader-color: #0071e2;
      width: 130px;
      height: var(--height-of-loader);
      border-radius: 30px;
      background-color: rgba(0,0,0,0.2);
      top: 50%;
      left: 50%;
      position: absolute;
      transform: translate(-50%, -50%);
    }

    .loader::before {
      content: "";
      position: absolute;
      background: var(--loader-color);
      top: 0;
      left: 0;
      width: 0%;
      height: 100%;
      border-radius: 30px;
      animation: moving 1s ease-in-out infinite;
      ;
    }

    @keyframes moving {
      50% {
        width: 100%;
      }

      100% {
        width: 0;
        right: 0;
        left: unset;
      }
    }
  `,
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class ProgressLoaderComponent {

}
