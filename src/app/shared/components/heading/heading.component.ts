import { ChangeDetectionStrategy, Component, input, TemplateRef } from '@angular/core';
import { IconHelpFillComponent } from '@shared/modules/icons';
import { NzPopoverModule } from 'ng-zorro-antd/popover';

@Component({
  selector: 'app-heading',
  template: `
    <header class="relative flex items-center gap-x-2 px-5 py-3 max-2xl:px-3 max-2xl:py-2 border-l-4 border-emerald-500 bg-linear-to-r from-neutral-300/40 via-white via-20%">
      <div class="relative z-20 flex items-center gap-x-2">
        <ng-content select="[icon]" />
        <span class="text-lg">{{ title() }}</span>
        @if (helpTemplate()) {
          <HelpFillIcon class="text-neutral-400 cursor-help" nz-popover nzPopoverPlacement="rightTop" [nzPopoverArrowPointAtCenter]="true" [nzPopoverContent]="helpTemplate()" />
        }
        <p class="text-xs text-neutral-400 font-normal" [innerHTML]="description()"></p>
      </div>

      <div class="absolute inset-0 z-10 flex items-center justify-center">
        <ng-content select="[tab]" />
      </div>

      <div class="relative z-20 flex items-center gap-x-2">
        <ng-content select="[extra]" />
      </div>
    </header>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    NzPopoverModule,
    IconHelpFillComponent
  ]
})
export class HeadingComponent {

  title = input.required<string>();
  description = input<string>(null);
  helpTemplate = input<TemplateRef<void>>(null);

  
}
