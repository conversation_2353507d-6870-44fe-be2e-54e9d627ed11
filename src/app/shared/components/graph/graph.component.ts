import { ChangeDetectionStrategy, ChangeDetectorRef, Component, Input, OnInit, HostListener, OnDestroy, inject, DestroyRef, signal, input, booleanAttribute, computed } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { debounceTime, filter, map, skip, startWith, tap } from 'rxjs';
import * as _ from 'lodash';

import * as Highcharts from 'highcharts';
import noDataToDisplay from 'highcharts/modules/no-data-to-display.js';
import { HighchartsChartModule } from 'highcharts-angular';
// import Drilldown from 'highcharts/modules/drilldown';
// import highcharts3D from 'highcharts/highcharts-3d.js'
import Annotations from 'highcharts/modules/annotations';
import { RelationControlService } from '@views/cockpit/services';
import patternFill from 'highcharts/modules/pattern-fill.js';
import { SeriesItem } from '@common/chart/highcharts';
import { LegendControlService } from '@common/service';
import { isNotNull, isNotUndefinedOrNotNull, toDecimals } from '@common/function';
import { Before } from '@common/decorator';
import { Util } from '@common/class';
import { QueryEngineFormService } from '@common/service/query-engine';
import seriesLabel from 'highcharts/modules/series-label.js';
// import boostCanvas from 'highcharts/modules/boost-canvas.js';
// import cylinder from 'highcharts/modules/cylinder.js';
// import boost from 'highcharts/modules/boost.js';
// import More from 'highcharts/highcharts-more';


@Component({
  selector: 'app-graph',
  template: `
    <highcharts-chart 
      [Highcharts]="Highcharts"
      [options]="chartOptions"
      [oneToOne]="oneToOneFlag"
      (chartInstance)="handleChartInstance($event)"
      class="absolute inset-0 block"
    ></highcharts-chart>
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    HighchartsChartModule
  ]
})
export class GraphComponent implements OnInit, OnDestroy {

  @Input()
  set options(value: any) {
    this.chartOptions = value;
    this.cdr.markForCheck();
  }

  destroyRef = inject(DestroyRef);
  relationControlService = inject(RelationControlService, { optional: true });
  legendControlService = inject(LegendControlService, { optional: true });
  formService = inject(QueryEngineFormService, { optional: true });
  cdr = inject(ChangeDetectorRef);

  multiple = input(100);
  showAvgPlotLines = input(false, { transform: booleanAttribute });
  showAnnotations = input(false, { transform: booleanAttribute });
  showEveryAnnotations = input(false, { transform: booleanAttribute });
  showSeriesLabel = input(false, { transform: booleanAttribute });
  noHandleColors = input(false, { transform: booleanAttribute });
  yAxisFormatEnabled = input(false, { transform: booleanAttribute });
  sourceType = input<'trend' | 'constitute' | 'comparison'>(null);
  
  chartOptions: any = this.defaultOptions();
  chartInstance: Highcharts.Chart | undefined;
  Highcharts: typeof Highcharts = Highcharts; // required
  oneToOneFlag = true; // optional boolean, defaults to false
  currentSeries = signal<any[]>([]);

  handleColors = computed(() => {
    return !this.noHandleColors();
  })

  ngOnInit(): void {
    try {
      Highcharts.setOptions({
        lang: {
          resetZoom: '重置',
          resetZoomTitle: '重置缩放比例为1:1',
          thousandsSep: ',',
          numericSymbols: [ 'K', 'M', 'B', 'T'],
          invalidDate: 'invalid date',
          noData: '无数据',
          months: ['一月', '二月', '三月', '四月', '五月', '六月', '七月', '八月', '九月', '十月', '十一月', '十二月'],
          shortMonths: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
          weekdays: ['周日', '周一', '周二', '周三', '周四', '周五', '周六'],
        }
      });
    } catch (error) {
      console.log('Highcharts', Highcharts);
      console.error(error);
    }
    
    // More(Highcharts);
    // Drilldown(Highcharts);
    // highcharts3D(Highcharts);
    Annotations(Highcharts);
    // cylinder(Highcharts);
    // boost(Highcharts);
    // boostCanvas(Highcharts);
    noDataToDisplay(Highcharts);
    patternFill(Highcharts);
    
    if (this.showSeriesLabel()) {
      seriesLabel(Highcharts);
    }
  }

  
  ngOnDestroy(): void {
    try {
      this.chartInstance?.destroy();
    } catch(e) {}
  }


  private defaultOptions() {
    return {
      chart: { backgroundColor: 'transparent' },
      title: { text: '' },
      credits: { enabled: false }
    };
  }


  handleChartInstance(event: Highcharts.Chart): void {
    this.chartInstance = event;
    this.handleWindowResize();
    this._subscribeToMetricsChange();
    this._subscribeToVisibleChange();
    this._subscribeToLegendItemClickEvent();
    this._subscribeToShowTypeChange();
  }


  /**
   * 此方法用于`趋势分析`时监听`设置坐标轴`功能时的更新series
   */
  private _subscribeToMetricsChange() {
    if (this.formService && this.yAxisFormatEnabled()) {
      this.formService.metrics.valueChanges.pipe(
        debounceTime(100),
        startWith(this.formService.metrics.value),
        filter(() => this.sourceType() === 'trend'),
        takeUntilDestroyed(this.destroyRef),
      ).subscribe((metrics) => {
        const showType = this.legendControlService.showType();
        const series = this._filterSeriesByShowType(showType);

        // console.log('[[showType]]', showType, '[sourceType]', this.sourceType());
        
        const newSeries = _.cloneDeep(series).map((serie: SeriesItem) => {
          const metric = metrics.find((item) => item.aliasName === serie.name);
          serie.yAxis = metric?.yAxis || 0;
          return serie;
        });

        if (metrics.every(item => item.yAxis !== null)) {
          this._updateSeries(newSeries, 'MetricsChange');
          this.chartOptions.series = newSeries;
        }
      })
    }
  }


  private _subscribeToVisibleChange() {
    if (this.relationControlService) {
      this.relationControlService.visibleChange.pipe(
        tap(() => this.handleWindowResize()),
        filter(data => data !== null),
        takeUntilDestroyed(this.destroyRef)
      ).subscribe(data => {
        const { index } = data;
        const newSeries = this.chartOptions.series.map((item, i) => {
          const visible = data.isShowAll || (i === index);

          return {...item, visible };
        })

        if (this.chartInstance) {
          // console.log('[update series]', newSeries);
          this.chartInstance.update({ series: newSeries });
        }
      })
    }
  }


  private _subscribeToShowTypeChange() {
    if (this.legendControlService) {
      this.legendControlService.showType$.pipe(
        startWith(this.legendControlService.showType()),
        tap(() => this.handleWindowResize()),
        takeUntilDestroyed(this.destroyRef)
      ).subscribe(() => {
        const { activeLegendItem } = this.legendControlService;

        if (activeLegendItem) {
          const { current } = activeLegendItem;
          this._focus(current);
        } else {
          this._showAll();
        }
      })
    }
  }


  private _filterSeriesByShowType(showType: 'number' | 'ratio' | null) {
    const { series } = this.chartOptions;
    const newSeries: SeriesItem[] = [];
    
    (<SeriesItem[]>series).forEach((item) => {
      if (showType === 'ratio' && item.isProportion) {
        // 如果只有主X轴
        const isOnlyXAxis_0 = (<SeriesItem[]>series).every(item => item.xAxis === 0);

        if (isOnlyXAxis_0) {
          item.linkedTo = undefined;
        } else {
          // 如果是双X轴图，则将次轴linkedTo主轴
          if (item.xAxis === 1) {
            item.linkedTo = '0';
          } else {
            item.linkedTo = undefined;
          }
        }
        
        newSeries.push(item);
      }
      
      if (showType === 'number' && !item.isProportion) {
        newSeries.push(item);
      }
      
      if (showType === null) {
        if (item.isProportion) {
          item.linkedTo = '0';
        }
        newSeries.push(item);
      }
    })

    // console.log('[newSeries]', newSeries);
    
    return newSeries;
  }


  private _subscribeToLegendItemClickEvent() {
    if (this.legendControlService) {
      this.legendControlService.legendItemClickEvent$.pipe(
        filter(isNotNull),
        takeUntilDestroyed(this.destroyRef)
      ).subscribe(({ before, current, shiftKey }) => {
        if (shiftKey) {
          this._toggle(current);
        } else {
          if (before === current) {
            this._showAll();
          } else {
            this._focus(current);
          }
        }
      })
    }
  }


  private _focus(name: string) {
    name = name.replace(/-占比$/, '');
    const showType = this.legendControlService.showType();
    const series = this._filterSeriesByShowType(showType);
    const newSeries = series.map(item => {
      return { ...item, visible: (
        item.name === name ||
        item.name === `${name}-占比`
      ) }
    });

    this._updateSeries(newSeries, '_focus');
  }


  private _showAll() {
    const showType = this.legendControlService.showType();
    const series = this._filterSeriesByShowType(showType);
    const newSeries = series.map(item => ({...item, visible: true}));

    setTimeout(() => this.legendControlService.emit(null), 0);
    this._updateSeries(newSeries, '_showAll');
  }


  private _toggle(name: string) {
    const series = this.currentSeries();
    const newSeries = series.map(item => {
      if (
        item.name === name ||
        item.name === `${name}-占比`
      ) {
        return { ...item, visible: !item.visible };
      }

      return item;
    });

    this._updateSeries(newSeries);
  }


  private _genAnnotations(series: any) {
    const { yAxis, xAxis, dataUnit } = series;
    const _unit = dataUnit === '%' ? '%' : '';
    const _multiple = dataUnit === '%' ? this.multiple() : 1;
    const values = series.data.map(item => item === null ? null : item?.y === undefined ? item : item.y).filter(isNotNull);
    const max = Math.max(...values);
    const min = Math.min(...values);
    const maxValueIndex = (<any[]>series.data).findIndex(item => item?.y === undefined ? item === max : item.y === max);
    const minValueIndex = (<any[]>series.data).findIndex(item => item?.y === undefined ? item === min : item.y === min);
    const maxText = `MAX ${Intl.NumberFormat().format(toDecimals(max, _multiple))}${_unit}`;
    const minText = `MIN ${Intl.NumberFormat().format(toDecimals(min, _multiple))}${_unit}`;
    const maxIndex = series.data[maxValueIndex]?.x || maxValueIndex;
    const minIndex = series.data[minValueIndex]?.x || minValueIndex;
    
    return {
      draggable: '',
      labels: [
        { point: { xAxis: xAxis ?? 0, yAxis: yAxis ?? 0, x: maxIndex, y: max }, text: maxText },
        { point: { xAxis: xAxis ?? 0, yAxis: yAxis ?? 0, x: minIndex, y: min }, text: minText }
      ]
    }
  }


  private getAnnotations(newSeries: any[]): any[] {
    const visibleLegends = newSeries.filter(item => item.visible);
    const isOnlyOne = visibleLegends.length === 1;
    const [series] = visibleLegends;

    if (this.showEveryAnnotations()) {
      return newSeries.map(item => this._genAnnotations(item));
    }

    if (this.showAnnotations() && isOnlyOne) {
      return [
        this._genAnnotations(series)
      ] as any;
    }

    return [];
  }


  @Before(ctx => ctx.showAvgPlotLines())
  private _updatePlotLines(newSeries: any[]) {
    const visibleLegends = newSeries.filter(item => item.visible);
    const isOnlyOne = visibleLegends.length === 1;
    const [series] = visibleLegends;

    this._clearPlotLine();
    
    if (isOnlyOne) {
      const { yAxis: yIndex, dataUnit } = series;
      const values = series.data.map(item => item === null ? null : item?.y === undefined ? item : item.y).filter(isNotUndefinedOrNotNull);
      const avgValue = Util.sum(values) / values.length;
      const _multiple = dataUnit === '%' ? this.multiple() : 1;

      this.chartInstance.yAxis[yIndex]?.addPlotLine({
        id: 'AvgPlotLines',
        label: {
          text: `
            <span style="padding-right:14px">
              <span class="guild-label-left">AVG ${Intl.NumberFormat().format(toDecimals(avgValue, _multiple))}${dataUnit === '%' ? '%' : ''}</span>
            </span>
          `,
          useHTML: true,
        },
        value: avgValue,
        color: '#ef4444',
        dashStyle: 'ShortDot',
        zIndex: 100,
        width: 2,
      })
    }
  }


  private _clearPlotLine() {
    this.chartInstance.yAxis.forEach(axis => {
      axis.removePlotLine('AvgPlotLines');
    })
  }


  private _updateSeries(newSeries: any[], from?: string) {
    // console.log('[_updateSeries]', from);
    // console.log('[_updateSeries]', newSeries);
    
    const { colors, yAxis } = this.chartOptions;
    const newColors = (<string[]>colors).slice(0, newSeries.length / 2);
    const isOnlyXAxis_0 = (<SeriesItem[]>newSeries).every(item => item.xAxis === 0 || item.xAxis === undefined); // xAxis为`undefined`时，即为主轴    
    const primary_series = newSeries.filter(item => item.yAxis === 0).filter(item => item.visible);
    const secondary_series = newSeries.filter(item => item.yAxis === 1).filter(item => item.visible);
    const isPercentForPrimarySeries = primary_series.every(item => item.dataUnit === '%' || item.isPercent);
    const isPercentForSecondarySeries = secondary_series.every(item => item.dataUnit === '%' || item.isPercent);
    const annotations = this.getAnnotations(newSeries);
    const newYAxis = this.yAxisFormatEnabled() ? yAxis.map((item, index) => {
      item.labels.format = undefined;
      item.labels.formatter = function() {
        if ((index === 0 ? isPercentForPrimarySeries : isPercentForSecondarySeries)) {
          return toDecimals(this.value) + '%';
        }

        return Intl.NumberFormat('en-US', {
          notation: 'compact',
          compactDisplay: 'short'
        }).format(this.value);
      };
      return item;
    }) : yAxis;
    
    if (this.chartInstance) {
      this.chartInstance.update({ series: [], annotations: [], yAxis: [] }, false, true);
      
      setTimeout(() => {
        if (this.chartInstance) {
          const options: Highcharts.Options = {
            series: newSeries,
            yAxis: newYAxis,
            annotations,
          };

          /**
           * 如果X轴为双轴，则将colors长度设为series长度一半，使主轴与次轴图例颜色一致
           */
          if (!isOnlyXAxis_0 && this.handleColors()) {
            options.colors = newColors;
          }

          this.chartInstance.update(options, false, true);
        }
      }, 100);

      setTimeout(() => {
        if (this.chartInstance) {
          this.chartInstance.redraw();
          this._updatePlotLines(newSeries);
          this.legendControlService.legends.set(newSeries);
        }
      }, 200);
    }

    this.currentSeries.set(newSeries);
  }


  @HostListener('window:resize')
  handleWindowResize(): void {
    try {
      this.chartInstance?.reflow();
    } catch(e) {}
  }

}
