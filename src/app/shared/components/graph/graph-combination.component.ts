import { ChangeDetectionStrategy, Component, computed, input } from '@angular/core';
import { GraphComponent } from './graph.component';
import { BaseGraph } from './graph-base';
import { handleLegendItemClick } from '@common/function';

export class CombinationSeries {
  constructor(
    public name: string,
    public type: string,
    public yAxis: number,
    public zIndex: number,
    public data: Array<number> = [],
    public stacking?: string,
    public tooltip?: {
      valueSuffix: string
    }
  ) {
  }
}


class Combination extends BaseGraph<CombinationSeries> {

  chart = {
    spacing: [20, 10, 0, 10],
    // margin: [null, null, 50, null],
    type: 'areaspline',
    zoomType: 'x'
  };

  plotOptions = {
    series: {
      turboThreshold: 999999999,
      marker: {
        enabled: true,
        radius: 2,
        fillColor: '#FFFFFF',
        symbol: 'circle',
        lineWidth: 1,
        lineColor: null,
      }
    }
  };

  yAxis = [
    {
      title: { enabled: false },
      labels: {
        enabled: true,
        style: { fontSize: '10px' }
      },
      gridLineWidth: 0,
      lineWidth: 0,
      tickWidth: 0,
      opposite: false,
    },
    {
      title: { enabled: false },
      labels: {
        enabled: true,
        format: '{value}%',
        style: {
          fontSize: "10px",
        }
      },
      // min: 0,
      // max: 1.2,
      gridLineWidth: 0,
      lineWidth: 0,
      tickWidth: 0,
      opposite: true,
    }
  ];

  xAxis = [
    {
      categories: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
      // gridLineWidth: 0,
      tickWidth: 1,
      tickColor: "#ccd6eb",
      lineColor: '#ccd6eb',
      labels: {
        enabled: true,
        step: 0,
        x: null,
        y: 15,
        rotation: null,
        style: { fontSize: '10px', fontFamily: '', color: '#24273E' }
      },
      crosshair: true,
      // index: 0,
      // isX: true,
    }
  ];


  constructor() {
    super();
    this.legend.verticalAlign = 'top';
    this.legend.margin = 20;
    this.legend.padding = 0;
    this.legend.y = -10;
  }

  setCategories(values: string[]) {
    this.xAxis[0].categories = values;
  }

  override getOption() {
    const value = JSON.parse(JSON.stringify(this));

    value.plotOptions = {
      series: {
        ...value.plotOptions.series,
        events: {
          legendItemClick: handleLegendItemClick(this),
        }
      }
    }

    return value;
  }

}


@Component({
  selector: 'app-graph-combination',
  template: `
    <app-graph [options]="option()" />
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    GraphComponent,
  ],
})
export class GraphCombinationComponent {
  
  themes = input<string[]>([]);
  value = input<CombinationSeries[]>([]);
  categories = input<string[]>([]);

  option = computed(() => {
    const options = new Combination();
    
    options.setCategories(this.categories());
    options.setTheme(this.themes());
    options.setValue(this.value());

    return options.getOption();
  });
  
}
