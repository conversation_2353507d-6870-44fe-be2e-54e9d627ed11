import { EventEmitter } from "@angular/core";

export class Tooltip {
  outside = true;
  crosshairs = true;
  shared = true;
  split = false;
  shadow = false;
  hideDelay = 0;
  borderColor = 'rgba(0, 0, 0, 0.1)';
  borderWidth = 2;
  valueSuffix = null;
  useHTML = true;
  style = { zIndex: 9999 };
  headerFormat = undefined;
  pointFormat = undefined;
  format = undefined;
  
  followPointer = false;
}


export class Legend {
  constructor({ verticalAlign, enabled }: Partial<{[K in keyof Legend]: any}> = {}) {
    this.verticalAlign = verticalAlign;
    this.enabled = enabled ?? true;
  }
  
  enabled = true;
  align = 'center';
  verticalAlign = 'top';
  layout = 'horizontal';
  itemStyle = {
    fontSize: '12px',
    fontFamily: 'Avenir, wf_SegoeUI, Segoe UI, Segoe, Segoe WP, Tahoma, Verdana, Arial, sans-serif',
    fontWeight: 400,
    color: '#24273E'
  };
  itemDistance = 20;
  itemMarginBottom = 0;
  margin = 12;
  maxHeight = null;
  padding = 8;
  floating = false;
  x = null;
  y = null;
  borderWidth = 0;
  borderRadius = 0;
  borderColor = '#999';
  backgroundColor = 'transparent';
}


export class BaseGraph<T> {

  title = { text: '', floating: false, style: { fontWeight: 400, fontSize: '16px' } };
  colors = ['#5b8ff9', '#61ddaa', '#65789b', '#f6bd16', '#7262fd', '#78d3f8', '#9661bc', '#f6903d', '#008685', '#f08bb4'];
  credits = { enabled: false };
  accessibility = { enabled: false };

  legend = new Legend();
  tooltip = new Tooltip();
  series: T[] = [];

  click = new EventEmitter<any>();

  
  setTitle(value: string) {
    this.title.text = value;
  }

  setTheme(values: string[]) {
    this.colors = [...values, ...this.colors];
  }

  getOption() {
    return JSON.parse(JSON.stringify(this));
  }

  setValue(values: T[]) {
    this.series = values;
  }

}