import { PlotOptions, Point, XAxisOptions, YAxisOptions } from 'highcharts';
import { BaseGraph } from './graph-base';
import { handleLegendItemClick } from '@common/function';

export class NegativeStackSeries {
  
  constructor(
    public name: string,
    public data: Array<number | Point | any> = [],
    public index?: number,
  ) {
  }
  
}

export class NegativeStack extends BaseGraph<NegativeStackSeries> {
  
  chart = {
    spacing: [20, 10, 15, 10],
    margin: [null, null, null, null],
    type: 'bar',
    zoomType: 'y'
  };

  subtitle = {
    text: '',
    style: {
      color: '#666',
    }
  }

  xAxis: XAxisOptions[] = [
    {
      categories: [],
      // reversed: true,
      lineWidth: 0,
      plotLines: [],
    }, 
    {
      categories: [],
      opposite: true,
      // reversed: true,
      lineWidth: 0,
      linkedTo: 0,
      plotLines: [],
    }
  ]

  yAxis: YAxisOptions = {
    title: {
      text: null
    },
    gridLineWidth: 0,
    lineWidth: 1,
    lineColor: '#ccd6eb',
    tickWidth: 1,
    tickColor: '#ccd6eb',
    plotLines: [],
  };

  plotOptions: PlotOptions = {
    series: {
      stacking: 'normal',
      allowPointSelect: true,
      cursor: 'pointer',
    }
  };


  constructor() {
    super();
    this.tooltip.shared = false;
    this.tooltip.split = true;
    this.setTheme(['#68bbc4', '#ffbf6b']);
  }

  
  setCategories(index: number, values: string[]) {
    this.xAxis[index].categories = values;
  }


  setPlotLines(value: number, labelText: string, labelColor = '#6c6c6c', align: any = 'center') {
    this.yAxis.plotLines = [{
      color: '#bbbbbb',
      dashStyle: 'Dash',
      width: 2,
      value,
      label: {
        align,
        text: labelText,
        rotation: 0,
        x: -1,
        y: -5,
        style: {
          color: labelColor,
          backgroundColor: '#fff'
          // padding: '2px 3px'
        }
      },
      zIndex: 5
    }]
  }


  override getOption() {
    const value = super.getOption();
    return {
      ...value,
      tooltip: {
        ...value.tooltip,
        formatter: tooltipFormatter,
      },
      plotOptions: {
        series: {
          ...value.plotOptions.series,
          events: {
            legendItemClick: handleLegendItemClick(this),
            click: (event) => {
              const { xAxis } = event.point.series.chart;
              const seriesIndex = event.point.series.index;
              const pointIndex = event.point.index;
              const name = xAxis[seriesIndex]['categories'][pointIndex];

              this.click.emit({ name });
            }
          }
        }
      }
    };
  }
  
}


function tooltipFormatter(params: any) {
  const { chart: { xAxis } } = this.series;  
  const seriesIndex = this.series.index;
  const pointIndex = this.point.index;
  const category = xAxis[seriesIndex]['categories'][pointIndex];
  const point = this.series.points[pointIndex];
  const seriesName = this.series.name;
  const value = Intl.NumberFormat().format(point.y);
  const template = `
    <span class="block text-sm font-bold">${category}</span>
    <span class="block">
      <em style="background-color: ${point.color};" class="w-2 h-2 rounded-full inline-block mr-1"></em>
      <span>${seriesName}：</span>
      <strong>${value} (${point.options.ratio}%)</strong>
    </span>
    <span class="block">
      <em style="background-color: ${point.color};" class="w-2 h-2 rounded-full inline-block mr-1"></em>
      <span>波动占比：</span>
      <b>${point.options.fluctuationRatio}%</b>
    </span>
  `;

  return template;
}