import { ChangeDetectionStrategy, Component, booleanAttribute, computed, input } from '@angular/core';
import { GraphComponent } from './graph.component';
import { BaseGraph } from './graph-base';
import { handleLegendItemClick } from '@common/function';

export class LineSeries {
  constructor(
    public name: string,
    public data: Array<number> = []
  ) {
  }
}



class Spline extends BaseGraph<LineSeries> {

  chart = {
    spacing: [10, 10, 15, 10],
    margin: [null, null, null, null],
    type: 'line',
    zoomType: 'x'
  };

  plotOptions = {
    line: {
      lineWidth: 2,
      turboThreshold: 999999999,
      animation: true,
      dataLabels: {
        enabled: true,
        inside: false,
        format: '{point.y}',
        color: null
      },
      marker: {
        enabled: true,
        radius: 2,
        fillColor: '#FFFFFF',
        symbol: 'circle',
        lineWidth: 1,
        lineColor: null,
      }
    }
  };
  
  yAxis = [
    {
      title: { enabled: false },
      gridLineWidth: 1,
      gridLineDashStyle: "Solid",
      gridLineColor: "#ccd6eb55",
      lineColor: "#ccd6eb",
      lineWidth: 1,
      tickWidth: 0,
      tickColor: "#ccd6eb",
      labels: {
        enabled: true,
        format: '{value}',
        x: -8,
        y: null,
        style: { fontSize: '10px', color: '#24273E' }
      },
      opposite: false,
      type: 'linear',
    }
  ];


  xAxis = [
    {
      scrollbar: { enabled: false },
      gridLineWidth: 0,
      gridLineDashStyle: "Solid",
      gridLineColor: "#e6e6e6",
      lineWidth: 1,
      lineColor: "#ccd6eb",
      tickWidth: 1,
      tickColor: "#ccd6eb",
      startOnTick: false,
      endOnTick: false,
      showFirstLabel: true,
      showLastLabel: true,
      labels: {
        enabled: true,
        y: 20,
        style: { fontSize: '10px', fontFamily: '', color: '#24273E' }
      },
      categories: [],
      crosshair: true,
      opposite: false,
    }
  ];


  constructor() {
    super();
    this.legend.verticalAlign = 'top';
  }


  setCategories(values: string[]) {
    this.xAxis[0].categories = values;
  }
  

  setSuffix(symbol: string) {
    this.tooltip.valueSuffix = symbol;
    this.plotOptions.line.dataLabels.format = `{point.y}${symbol}`;
    this.yAxis.forEach(item => {
      item.labels.format = `{value}${symbol}`;
    })
  }

  override getOption() {
    const value = JSON.parse(JSON.stringify(this));

    value.plotOptions = {
      line: {
        ...value.plotOptions.line,
        events: {
          legendItemClick: handleLegendItemClick(this),
        }
      }
    }

    return value;
  }

}



@Component({
  selector: 'app-graph-spline',
  template: `
    <app-graph [options]="option()" />
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    GraphComponent
  ],
})
export class GraphSplineComponent {

  themes = input<string[]>([]);
  value = input<LineSeries[]>([]);
  categories = input<string[]>([]);
  isPercent = input(false, { transform: booleanAttribute });
  suffix = input<string>(null);

  option = computed(() => {
    const options = new Spline();

    if (this.suffix()) {
      options.setSuffix(this.suffix());
    }
    
    options.setCategories(this.categories());
    options.setTheme(this.themes());
    options.setValue(this.value());

    return options.getOption();
  });
  
}
