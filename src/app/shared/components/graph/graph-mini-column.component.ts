import { booleanAttribute, ChangeDetectionStrategy, Component, computed, input } from '@angular/core';
import { sortCategoriesFn, toDecimals } from '@common/function';
import { HighChartBuilder } from '@common/chart/highcharts';
import { GraphComponent } from './graph.component';
import { SeriesOptionsType } from 'highcharts';


class MiniColumn extends HighChartBuilder {

  constructor() {
    super('column');

    this.setChart('spacing', [5, 10, 5, 10])
      .setLegend('enabled', false)
      .setXAxis('labels', { enabled: false })
      .setXAxis('gridLineWidth', 0)
      .setXAxis('lineWidth', 0)
      .setXAxis('tickWidth', 0)
      .setYAxis('labels', { enabled: false })
      .setYAxis('gridLineWidth', 0)
      .setYAxis('lineWidth', 0)
      .setYAxis('tickWidth', 0)
      .setPlotOptions('column', {
        borderRadius: 2,
        borderWidth: 0
      })
  }

}


@Component({
  selector: 'app-graph-mini-column',
  template: `
    @if (option()) {
      <app-graph [options]="option()" />
    }
  `,
  host: {
    'class': 'block relative h-full',
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    GraphComponent,
  ],
})
export class GraphMiniColumnComponent {
  
  isPercent = input(false, { transform: booleanAttribute });
  isTwotone = input(false, { transform: booleanAttribute });
  themes = input<string[]>(['rgb(80, 135, 236)']);
  values = input<string>(null);
  name = input.required<string>();
  
  
  option = computed(() => {
    if (this.values()) {
      const xAxis = this.handleCategories();
      const series = this.handleSeries();
      const chart = new MiniColumn()
        .setSeries(series)
        .setXAxis('categories', xAxis)
        .setTheme(this.themes());
      
      if (this.isPercent()) {
        chart.setTooltip('valueSuffix', '%');
      }
      
      return chart.getOption();
    }
    
    return null;
  });

  
  private handleCategories() {
    const xAxis: string[] = [];
    const valueObject = JSON.parse(this.values()) as Array<{ [key: string]: string }>;
    const obj = {} as any;

    valueObject.forEach(item => {
      const [key] = Object.keys(item);
      const [value] = Object.values(item);
      obj[key] = value;
    })

    Object.keys(obj).sort(sortCategoriesFn).forEach((key) => {
      if (key) {
        xAxis.push(key);
      }
    })

    return xAxis;
  }


  private handleSeries() {
    const data = [];
    const valueObject = JSON.parse(this.values()) as Array<{ [key: string]: string }>;
    const obj = {} as any;
    const red = '#ffbf6b';
    const green = '#68bbc4';
    const blue = '#5087ec';

    valueObject.forEach(item => {
      const [key] = Object.keys(item);
      const [value] = Object.values(item);
      obj[key] = value;
    })

    Object.keys(obj).sort(sortCategoriesFn).forEach((key) => {
      if (key) {
        if (this.isPercent()) {
          const value = obj[key] === null ? null : parseFloat(obj[key]);
          const y = toDecimals(value);
          const color = this.isTwotone() ? (y > 0 ? red : green) : blue;
          data.push({ y, color });
        } else {
          const valueStr = parseFloat(obj[key]).toFixed(2);
          const y = obj[key] === null ? null : +valueStr;
          const color = this.isTwotone() ? (y > 0 ? red : green) : blue;
          data.push({ y, color });
        }
      }
    })

    return [
      { name: this.name(), data }
    ] as SeriesOptionsType[];
  }
  
}
