import { ChangeDetectionStrategy, Component, booleanAttribute, computed, input } from '@angular/core';

import { groupBy } from '@common/function';
import { HighChartBuilder } from '@common/chart/highcharts';
import { TrendVo } from '@views/cockpit/models';
import { GraphComponent } from './graph.component';


class MiniArea extends HighChartBuilder {

  constructor() {
    super('areaspline');

    this.setChart('spacing', [0, 0, 0, 0])
      .setLegend('margin', 3)
      .setLegend('padding', 0)
      .setLegend('itemStyle', { fontSize: '10px' })
      .setLegend('itemDistance', 10)
      .setXAxis('labels', { enabled: false })
      .setXAxis('categories', ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'])
      .setXAxis('gridLineWidth', 0)
      .setXAxis('lineWidth', 0)
      .setXAxis('tickWidth', 0)
      .setYAxis('gridLineWidth', 0)
      .setYAxis('lineWidth', 0)
      .setYAxis('labels', { enabled: false })
      .setTooltip('outside', false)
      .setPlotOptions('areaspline', {
        turboThreshold: 999999999,
        marker: {
          enabled: true,
          radius: 2,
          fillColor: '#FFFFFF',
          lineWidth: 1,
          lineColor: null,
        }
      })
  }
  
}


@Component({
  selector: 'app-graph-mini-area',
  template: `
    <app-graph [options]="option()" />
  `,
  host: {
    'class': 'block relative h-full'
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    GraphComponent,
  ],
})
export class GraphMiniAreaComponent {

  value = input<TrendVo[]>([]);
  themes = input<string[]>(['#ff9900', '#65789b59', 'rgba(34, 197, 94, 0.8)']);
  isPercent = input(false, { transform: booleanAttribute });
  fillColors = ['rgba(74, 222, 128, 0.08)', 'rgba(206, 206, 206, 0.2)', 'rgba(255, 153, 0, 0.3)'];

  
  option = computed(() => {
    const series = this.handleSeries();
    const chart = new MiniArea()
      .setSeries(series)
      .setTheme(this.themes());

    if (this.isPercent()) {
      chart.setTooltip('valueSuffix', '%');
      chart.setYAxis('max', 100);
    }
    
    return chart.getOption();
  });
  

  private handleSeries() {
    const series = [];

    if (this.value()?.length > 0) {
      const values = groupBy(this.value(), 'year') as {[key: number]: TrendVo[]};
      
      Object.keys(values).forEach((name, index) => {
        const data = values[name].sort((a, b) => a.month - b.month).map(item => item.value);
        const fillColor = this.fillColors[index];
        const zIndex = 10 + index;
          
        series.push({ name, fillColor, zIndex, data });
      })

      series.reverse();
    }

    return series;
  }

}
