import { ChangeDetectionStrategy, Component, computed, input } from '@angular/core';
import { GraphComponent } from './graph.component';
import { BaseGraph } from './graph-base';


class PiePlotOptions {
  allowPointSelect = true;
  showInLegend = true;
  cursor = 'pointer';
  allowCenterText = false;
  size = '70%';
  borderColor = '#fff';
  borderWidth = 1;
  innerSize = '0%';
  startAngle = 0;
  endAngle = null;
  center = ['50%', '50%'];
  animation = false;
  dataLabels = {
    enabled: true,
    format: '<b>{point.name}</b><br>{point.percentage:.2f}%',
    distance: 5,
    style: {
      fontSize: '10px'
    }
  };
}


export interface PieSeriesData {
  name: string;
  y: number;
  n: number;
}

export class PieSeries {
  colorByPoint = true;

  constructor(
    public data: PieSeriesData[] = []
  ) {}
}


class Pie extends BaseGraph<PieSeries> {

  chart = {
    spacing: [10, 10, 15, 10],
    type: 'pie',
  };
  
  plotOptions = {
    pie: new PiePlotOptions()
  };

  constructor() {
    super();
    this.legend.enabled = false;
    this.title.style.fontSize = '14px';
    this.tooltip.headerFormat = '';
    this.tooltip.pointFormat = `
      <span>{point.name}</span>: 
      <b>{point.y}</b>
    `;
  }
  
}


@Component({
  selector: 'app-graph-pie',
  template: `
    <app-graph [options]="option()" />
  `,
  host: {
    'class': 'block relative h-full'
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    GraphComponent,
  ],
})
export class GraphPieComponent {

  chartTitle = input<string>('');
  themes = input<string[]>([]);
  value = input<PieSeries>();

  option = computed(() => {
    const options = new Pie();

    options.setTheme(this.themes());
    options.setTitle(this.chartTitle());
    options.setValue([this.value()]);
    
    return options.getOption();
  });

}
