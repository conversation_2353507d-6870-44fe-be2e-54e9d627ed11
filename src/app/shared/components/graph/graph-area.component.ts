import { ChangeDetectionStrategy, Component, computed, input } from '@angular/core';

import { groupBy, handleLegendItemClick } from '@common/function';
import { TrendVo } from '@views/cockpit/models';

import { GraphComponent } from './graph.component';
import { BaseGraph, Legend } from './graph-base';

class Series {
  constructor(
    public name: string,
    public fillColor: string,
    public zIndex: number,
    public data: Array<number> = []
  ) {
  }
}


class Area extends BaseGraph<Series> {

  chart = {
    spacing: [10, 10, 15, 10],
    // margin: [-30, 0, 24, 0],
    backgroundColor: 'rgba(0, 0, 0, 0)',
    type: 'areaspline',
    zoomType: 'x',
  };

  override legend = new Legend({ verticalAlign: 'bottom' });

  plotOptions = {
    areaspline: {
      turboThreshold: 999999999,
      marker: {
        enabled: true,
        radius: 2,
        fillColor: '#FFFFFF',
        lineWidth: 1,
        lineColor: null,
      }
    }
  };

  yAxis = [
    {
      title: { enabled: false },
      gridLineWidth: 0,
      gridLineDashStyle: "Solid",
      gridLineColor: "#e6e6e6",
      lineColor: "#ccd6eb",
      lineWidth: 1,
      tickWidth: 0,
      tickColor: "#ccd6eb",
      labels: {
        // enabled: false,
        format: '{value:,.0f}',
        x: -8,
        y: null,
        style: { fontSize: '10px', color: '#24273E' }
      },
      index: 0
    }
  ];

  xAxis = {
    categories: ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月', '11月', '12月'],
    title: {
      enabled: false,
      text: "",
      margin: 30,
      style: { fontSize: '12px', color: '#24273E' }
    },
    gridLineWidth: 0,
    gridLineDashStyle: 'Solid',
    gridLineColor: '#e6e6e6',
    lineWidth: 1,
    lineColor: '#ccd6eb',
    tickWidth: 1,
    tickColor: '#ccd6eb',
    crosshair: true,
    labels: {
      enabled: true,
      format: null,
      step: 0,
      x: null,
      y: 20,
      rotation: null,
      style: { fontSize: '10px', color: '#24273E' }
    },
  };
  
  setCategories(values: string[]) {
    this.xAxis.categories = [...values];
  }

  override getOption() {
    const value = JSON.parse(JSON.stringify(this));

    value.plotOptions = {
      ...value.plotOptions,
      series: {
        events: {
          legendItemClick: handleLegendItemClick(this),
        }
      }
    }

    return value;
  }

}

@Component({
  selector: 'app-graph-area',
  template: `
    <app-graph [options]="option()" />
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    GraphComponent,
  ],
})
export class GraphAreaComponent {

  themes = input<string[]>(['#10b981', '#65789b59']);
  value = input<TrendVo[]>([]);

  option = computed(() => {
    const options = new Area();
    const fillColors = ['rgba(91, 143, 249, 0.1)', 'rgba(206, 206, 206, 0.2)', 'rgba(16, 185, 129, 0.2)'];
    const series = [];
    
    if (this.value()?.length > 0) {
      const values = groupBy<TrendVo>(this.value(), 'year');

      Object.keys(values).forEach((key, index) => {
        const data = values[key].sort((a, b) => Number(a.month) - Number(b.month)).map(item => item.value);

        series.push(new Series(key, fillColors[index], index, data));
      })

      series.reverse();
    }

    options.setTheme(this.themes());
    options.setValue(series);

    return options.getOption();
  });

}
