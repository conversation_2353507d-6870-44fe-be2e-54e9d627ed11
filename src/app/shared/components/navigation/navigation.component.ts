import { RouterLink, RouterLinkActive } from '@angular/router';
import { AfterViewInit, ChangeDetectionStrategy, Component, inject, signal } from '@angular/core';

import { isDev, isEmulator, isPreview, VERSION } from '@common/const';
import { BuriedPointService } from '@common/service';
import { UraApiService } from '@api/ura-api.service';
import { UserInfoService } from '@api/user-info.service';
import { IconLogoComponent } from '@shared/modules/icons';
import { MenuModule } from '@shared/modules/headless';

class Link {
  childMenu: Link[] = [];
  name: string;
  icon: string;
  url: string;
}

@Component({
  selector: 'app-navigation',
  template: `
    <header class="relative z-50 flex justify-center transition-transform duration-300 bg-white border-b border-neutral-200">
      <div class="bg-white flex items-center gap-x-10 max-2xl:gap-x-6 px-5 w-full py-3 max-2xl:py-2 max-w-(--breakpoint-2xl)">
        <span class="flex items-center gap-x-1.5 font-extrabold text-lg">
          <LogoIcon class="text-3xl text-[#FFA400]" />
          <ng-content select="[title]">
            <span routerLink="/home" class="cursor-pointer" [title]="'v' + version.full">Caerus</span>
          </ng-content>
        </span>

        <ng-content select="[afterTitle]"></ng-content>

        <div class="flex-1 h-full">
          <nav class="flex items-center gap-x-5 max-2xl:gap-x-3 h-full">
            @for (item of links(); track $index) {
              @if (item.childMenu.length > 0) {
                <app-menu>
                  <app-menu-button class="nav-link">{{item.name}}</app-menu-button>
                  <app-menu-items class="space-y-1">
                    @for(child of item.childMenu; track child) {
                      <app-menu-item [routerLink]="child.url" routerLinkActive="bg-gray-200/70">{{child.name}}</app-menu-item>
                    }
                  </app-menu-items>
                </app-menu>
              } @else {
                <a class="nav-link" [routerLink]="item.url" routerLinkActive="active" (click)="handlerClick(item)">{{item.name}}</a>
              }
            }
          </nav>
        </div>
      
        <div class="flex items-center whitespace-nowrap px-5">
          <ng-content select="[extra]"></ng-content>
        </div>
      </div>
      <div class="w-17.5"></div>
    </header>
  `,
  styleUrl: './navigation.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    RouterLink,
    RouterLinkActive,
    IconLogoComponent,
    MenuModule,
  ],
})
export class NavigationComponent implements AfterViewInit {

  readonly uraApiService = inject(UraApiService);
  readonly buriedPointService = inject(BuriedPointService);
  readonly userInfo = inject(UserInfoService);

  links = signal<Link[]>([]);
  version = VERSION;

  ngAfterViewInit(): void {
    this.uraApiService.getMenu(this.userInfo.privateName, 'Caerus').subscribe(res => {
      if (res?.data) {
        const data = JSON.parse(res.data);
        const links = data
          .sort((a,b) => a.sort - b.sort)
          .filter(item => isDev() || isPreview() || isEmulator() || item.icon === 'true');

        // console.log('[links]', links);
        this.userInfo.menu.set(links);
        this.links.set(links);
      }
    })
  }

  handlerClick(event: Link) {
    // console.log('埋点上报：点击 -> 导航');
    this.buriedPointService.addStat('dida_dpm_caerus_navigation_click', {
      page_name: event.name,
    });
  }

}
