import { Component, ChangeDetectionStrategy, input, booleanAttribute } from '@angular/core';

@Component({
  selector: 'app-skeleton',
  template: ``,
  styles: [
    `
      :host {
        background: linear-gradient(90deg,rgba(190,190,190,.2) 25%,rgba(129,129,129,.24) 37%,rgba(190,190,190,.2) 63%);
        background-size: 400% 100%;
        animation: ant-skeleton-loading 1.4s ease infinite
      }
    
      @keyframes ant-skeleton-loading {
        0% {
          background-position: 100% 50%
        }

        to {
          background-position: 0 50%
        }
      }
    `
  ],
  host: {
    'class': 'block',
    '[class.rounded-full]': 'circle()',
    '[class.rounded-sm]': '!circle()'
  },
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class SkeletonComponent {

  circle = input(false, { transform: booleanAttribute });

}
