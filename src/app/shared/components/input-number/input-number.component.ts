import { AfterViewInit, ChangeDetectionStrategy, Component, computed, DestroyRef, forwardRef, inject, input, model, signal } from '@angular/core';
import { takeUntilDestroyed, toObservable } from '@angular/core/rxjs-interop';
import { ControlValueAccessor, FormsModule, NG_VALUE_ACCESSOR } from '@angular/forms';
import { distinctUntilChanged, skip } from 'rxjs';
import { toDecimals } from '@common/function';

@Component({
  selector: 'app-input-number',
  template: `
    <input
      type="number"
      class="line-input"
      [(ngModel)]="value"
      (ngModelChange)="handleValueChange($event)"
      [disabled]="disabled()"
      [placeholder]="placeholder()"
      [step]="step()"
    />

    @if (suffix()) {
      <span class="absolute right-2">{{suffix()}}</span>
    }
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  host: {
    'class': 'relative inline-flex items-center'
  },
  imports: [
    FormsModule
  ],
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => InputNumberComponent),
      multi: true
    },
  ]
})
export class InputNumberComponent implements AfterViewInit, ControlValueAccessor {

  destroyRef = inject(DestroyRef);
  
  step = input<number>(null);
  placeholder = input<string>('请输入');
  suffix = input<'%' | 'pp' | string>(null);
  suffix$ = toObservable(this.suffix);

  value = signal<number>(null);
  disabled = model(false);
  
  isPercent = computed(() => {
    return this.suffix() === '%';
  })

  ngAfterViewInit(): void {
    let beforeSuffix: string;
    let isPercentBeforeSuffix: boolean;
    this.suffix$.pipe(
      skip(1),
      distinctUntilChanged(),
      takeUntilDestroyed(this.destroyRef)
    ).subscribe(suffix => {
      if (suffix !== beforeSuffix) {
        // console.log('[suffix change]', suffix);

        if (suffix === '%' || suffix === 'pp') {
          if (isPercentBeforeSuffix) {
            // 如果suffix之前也为%或pp，则不处理value
          } else {
            this.value.update(value => toDecimals(value));
          }
        } else {
          // 如果指标类型为数值，则将value除以100
          this.value.update(value => toDecimals(value / 100, 1));
        }

        this.handleValueChange(this.value());
      }

      beforeSuffix = suffix;
      isPercentBeforeSuffix = beforeSuffix === '%' || beforeSuffix === 'pp';
    })
  }
  
  _controlValueAccessorChangeFn: (value: any) => void = () => {};

  
  onTouched: any = () => {};

  
  registerOnChange(fn: (value: any) => void) {
    this._controlValueAccessorChangeFn = fn;
  }

  
  registerOnTouched(fn: (value: any) => void) {
    this.onTouched = fn;
  }

  
  writeValue(value: number | null) {    
    if (this.isPercent()) {
      if (Number.isFinite(value)) {
        this.value.set(toDecimals(value));
      }
    } else {
      this.value.set(value);
    }
  }


  setDisabledState(isDisabled: boolean): void {
    this.disabled.set(isDisabled);
  }
  

  handleValueChange(value: number) {
    // console.log('[handleValueChange]', value, 'isPercent?', this.isPercent());
    if (this.isPercent()) {
      this._controlValueAccessorChangeFn(toDecimals(value / 100, 1, 4));
    } else {
      this._controlValueAccessorChangeFn(value);
    }
  }
  
}
