import { Date<PERSON>ip<PERSON>, KeyValuePipe } from '@angular/common';
import { ControlValueAccessor, FormsModule, NG_VALUE_ACCESSOR } from '@angular/forms';
import { ChangeDetectionStrategy, Component, computed, forwardRef, inject, input, signal, viewChild } from '@angular/core';
import { CdkConnectedOverlay, OverlayModule } from '@angular/cdk/overlay';
import { differenceInCalendarDays, differenceInCalendarMonths, addMonths } from 'date-fns';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { NzDatePickerModule } from 'ng-zorro-antd/date-picker';
import { NzButtonComponent } from 'ng-zorro-antd/button';
import { addDays } from 'date-fns';

import { BaseOverlayDirective } from '@common/directive';
import { IconCalendarComponent, IconChevronLeftComponent, IconChevronRightComponent } from '@shared/modules/icons';
import { Args, Before, PipeTransform, Transfer } from '@common/decorator';
import { EventInterceptor, isUndefined } from '@common/function';


class ParseDatePipe implements PipeTransform {
  transform([start, end]: any[]) {
    if (isUndefined(start, end)) {
      return [start, end];
    }
    
    const startDate = Math.min(start, end);
    const endDate = Math.max(end, start);

    return [
      new Date(startDate), 
      new Date(endDate)
    ];
  }
}


@Component({
  selector: 'app-range-picker',
  template: `
    <div cdkOverlayOrigin class="flex items-center gap-x-0.5 h-full pl-2 pr-0.5 py-1.5 border border-neutral-300/90 rounded-xs text-xs text-neutral-700" (click)="toggle($event)">
      <CalendarIcon class="text-base" />
      <div class="flex-1 min-w-0 truncate">
        @for (item of rangeDate(); track $index) {
          @switch (mode()) {
            @case ('week')  { <span>{{item | date: 'YYYY第ww周'}}</span> }
            @case ('month') { <span>{{item | date: 'yyyy-MM'}}</span> }
            @default        { <span>{{item | date: 'yyyy-MM-dd'}}</span> }
          }
          
          @if ($first) {
            <span>至</span>
          }
        }
        @empty {
          <span>{{placeholder()}}</span>
        }
      </div>
      
      @if (!mode() && rangeDate() !== null) {
        <ChevronLeftIcon iconBtn class="sm" nz-tooltip="向前平移一天" (click)="prevDate($event)" />
        <ChevronRightIcon iconBtn class="sm" nz-tooltip="向后平移一天" (click)="nextDate($event)" [class.disabled]="disabled()" />
      }
    </div>

    <ng-template
      #overlay="cdkConnectedOverlay"
      cdkConnectedOverlay
      [cdkConnectedOverlayPanelClass]="'custom-range-picker-overlay'"
      [cdkConnectedOverlayHasBackdrop]="false"
      [cdkConnectedOverlayOrigin]="cdkOverlayOrigin"
      [cdkConnectedOverlayPositions]="listOfPositions"
      [cdkConnectedOverlayScrollStrategy]="scrollStrategy"
      [cdkConnectedOverlayOpen]="visible()"
      (positionChange)="onPositionChange($event)"
      (overlayOutsideClick)="handleOutside($event)"
      (detach)="close()"
    >
      <div class="flex flex-col bg-white rounded-sm shadow-1">
        <div class="relative z-20 flex items-stretch divide-x divide-[#f0f0f0] border-b border-b-[#f0f0f0] ">
          @if (rangeMap() && rangeMap()?.size > 0) {
            <aside class="flex flex-col items-center min-w-[140px] max-w-[160px] h-[269px] overflow-auto">
              @for (item of (rangeMap() | keyvalue : originalOrder); track $index) {
                <div class="menu-item" (click)="onRangeDateChange(item.value)">
                  <span class="font-bold text-xs">{{item.key}}</span>
                </div>
              }
            </aside>
          }
          
          <section class="flex-1 min-w-0">
            @if (mode() === 'month') {
              <nz-range-picker nzInline [nzMode]="mode()" [nzDisabledDate]="disabledMonth" [(ngModel)]="rangeDate" (ngModelChange)="onRangeDateChange($event)" />
            }
            @else {
              <nz-range-picker nzInline [nzMode]="mode()" [nzDisabledDate]="disabledDate" [(ngModel)]="rangeDate" (ngModelChange)="onRangeDateChange($event)" />
            }
          </section>
        </div>
      
        <div class="flex justify-end py-2 px-4">
          <button nz-button nzType="primary" nzSize="small" (click)="close()">确定</button>
        </div>
      </div>
    </ng-template>
  `,
  styleUrl: './range-picker.component.css',
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    FormsModule,
    OverlayModule,
    NzDatePickerModule,
    NzButtonComponent,
    NzToolTipModule,
    IconCalendarComponent,
    IconChevronRightComponent,
    IconChevronLeftComponent,
    DatePipe,
    KeyValuePipe,
  ],
  providers: [
    DatePipe,
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => RangePickerComponent),
      multi: true
    }
  ]
})
export class RangePickerComponent extends BaseOverlayDirective implements ControlValueAccessor {

  datePipe = inject(DatePipe);
  mode = input<string>(null);
  placeholder = input('');
  rangeMap = input<Map<string, Date[]>>();
  connectedOverlay = viewChild(CdkConnectedOverlay);

  today = new Date();
  yesterday = signal(addDays(new Date(), -1));
  rangeDate = signal<Date[]>([]);
  disabled = computed(() => {
    if (this.rangeDate()) {
      const [, end] = this.rangeDate();
      const yesterdayStr = new Date(this.yesterday()).toLocaleDateString();
      const endStr = new Date(end).toLocaleDateString()

      return (yesterdayStr === endStr);
    }
    
    return false;
  })

  change = (_: any) => { };
  registerOnTouched(fn: any) { }


  registerOnChange(fn: any) {
    this.change = fn;
  }


  writeValue(value: Date[]) {
    // console.log('[writeValue]', value);
    this.rangeDate.set(value);
  }


  disabledDate = (current: Date): boolean => {
    // Can not select days before today and today
    return differenceInCalendarDays(current, this.today) > -1;
  }


  disabledMonth = (current: Date): boolean => {
    // Can not select days before today and today
    const startDateForMonth = new Date().setDate(1);
    const dateRight = addMonths(startDateForMonth, 1);
    
    return differenceInCalendarMonths(current, dateRight) > -1;
  }

  
  originalOrder = (): number => {
    return 0;
  }
  

  @Transfer()
  onRangeDateChange(@Args(ParseDatePipe) [start, end]: Date[]) {
    this.rangeDate.set([start, end]);
    this.change(this.rangeDate());
  }

  
  override open() {
    this.visible.set(true);

    setTimeout(() => {
      const dom = this.connectedOverlay().overlayRef.hostElement?.parentElement?.classList;
      
      dom?.add('z-1001!');
      dom?.remove('z-50!');
    })
  }


  @Before(EventInterceptor)
  prevDate(event: MouseEvent) {
    const [start, end] = this.rangeDate();
    const newStart = addDays(new Date(start), -1);
    const newEnd = addDays(new Date(end), -1);

    this.rangeDate.set([newStart, newEnd]);
    this.change(this.rangeDate());
  }

  
  @Before(EventInterceptor)
  nextDate(event: MouseEvent) {
    const [start, end] = this.rangeDate();
    const newStart = addDays(new Date(start), 1);
    const newEnd = addDays(new Date(end), 1);

    this.rangeDate.set([newStart, newEnd]);
    this.change(this.rangeDate());
  }

}
