<form [formGroup]="formService.form">
  <div [class]="`flex items-center gap-x-${gapX()}`">
    <div class="whitespace-nowrap">
      @if (dateLabel()) {
        <label class="inline-flex items-center justify-end min-w-16 font-bold leading-5 whitespace-nowrap">
          <span [class]="dateLabelClass()">{{dateLabel()}}：</span>
        </label>
      }

      @if (showDtType()) {
        <nz-select formControlName="dtType" class="w-24">
          @for (item of unitOptions(); track $index) {
            <nz-option [nzLabel]="item.label" [nzValue]="item.value"></nz-option>
          }
        </nz-select>
      }
    </div>

    @switch (dtType()) {
      @case ('dt') { <app-date-picker  [form]="formService.form.get('dt')" [rangeMap]="rangeMap()" /> }
      @case ('yw') { <app-week-picker  [form]="formService.form.get('dt')" [rangeMap]="weekRangeMap()" /> }
      @case ('ym') { <app-month-picker [form]="formService.form.get('dt')" [rangeMap]="monthRangeMap()" /> }
    }

    @if (compareVisible()) {
      <label class="inline-flex items-center justify-end min-w-14 font-bold leading-5 whitespace-nowrap">对比期：</label>

      @switch (dtType()) {
        @case ('dt') { <app-date-picker  [form]="formService.form.get('compareDt')" [rangeMap]="compareRangeMap()" /> }
        @case ('yw') { <app-week-picker  [form]="formService.form.get('compareDt')" /> }
        @case ('ym') { <app-month-picker [form]="formService.form.get('compareDt')" /> }
      }

      @if (deleteBtn()) {
        <DeleteIcon iconBtn class="xl" (click)="removeDateCompare()" />
      }
    }
    @else {
      @if (compareBtn()) {
        <button nz-button nzType="default" (click)="addDateCompare()">添加日期对比</button>
      }
    }
  </div>
</form>