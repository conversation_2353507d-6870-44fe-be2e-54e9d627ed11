import { DatePipe } from '@angular/common';
import { AfterViewInit, ChangeDetectionStrategy, Component, effect, inject, input, signal } from '@angular/core';
import { FormGroup, FormsModule } from '@angular/forms';
import { startWith, take } from 'rxjs';

import { QueryDt } from '@common/service/query-engine';
import { RangePickerComponent } from '../range-picker';


@Component({
  selector: 'app-date-picker',
  template: `
    <app-range-picker class="block min-w-32 h-8" [ngModel]="value()" (ngModelChange)="handleValueChange($event)" [rangeMap]="rangeMap()" />
  `,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [
    FormsModule,
    RangePickerComponent,
  ],
  providers: [
    DatePipe,
  ]
})
export class DatePickerComponent implements AfterViewInit {

  datePipe = inject(DatePipe);
  rangeMap = input<Map<string, Date[]>>();
  form = input.required<any>();
  value = signal<Date[]>(null);

  // 注释原因：
  // 使用effect时如果监听form同时监听value，会导赋值致死循环
  // 则只能监听formChange一次，所以导致更高级的功能无法实现
  // 目前改为非双向绑定value，通过ngModelChange来设置value
  // 解决了赋值死循环的问题
  // constructor() {
  //   effect(() => {
  //     if (this.value()) {
  //       const [startTime, endTime] = this.value().map(value => {
  //         return this.datePipe.transform(value, 'yyyy-MM-dd');
  //       })
        
  //       this.form().get('startTime').patchValue(startTime);
  //       this.form().get('endTime').patchValue(endTime);
  //     }
  //   })
  // }


  ngAfterViewInit(): void {
    (<FormGroup<QueryDt>>this.form()).valueChanges.pipe(
      startWith((<FormGroup<QueryDt>>this.form()).getRawValue()),
      // take(1)
    ).subscribe(value => {
      if (value) {
        const { startTime, endTime } = value;

        if (startTime && endTime) {
          this.value.set([new Date(startTime), new Date(endTime)]);
        }
      }
    })
  }


  handleValueChange(value: Date[]) {
    // console.log('[handleValueChange]', value);
    const [startTime, endTime] = value.map(value => this.datePipe.transform(value, 'yyyy-MM-dd'));
    
    this.form().get('startTime').patchValue(startTime);
    this.form().get('endTime').patchValue(endTime);
  }

}
