import { DecimalPipe } from '@angular/common';
import { booleanAttribute, ChangeDetectionStrategy, Component, computed, input, output } from '@angular/core';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';

@Component({
  selector: 'value-formatter',
  template: `
    @if (useLink()) {
      <a
        [nz-tooltip]="this.linkTip()"
        [class.text-neutral-500!]="useColor() && value() == 0"
        [class.text-green-500!]="useColor() && value() < 0"
        [class.text-red-600!]="useColor() && value() > 0"
        class="underline! underline-offset-2"
        (click)="onLinkClick.emit()"
      >
        @if (value() === null || value() === undefined) {
          <span>-</span>
        } @else {
          {{value() > 0 && this.showPrefix() ? '+' : ''}}{{(value() * multiplier()) | number: digitsInfo()}}{{suffix()}}
        }
      </a>
    }
    @else {
      @if (showBracket()) {&#40;}
      @if (value() === null || value() === undefined || value() === 'undefined') {
        <span>-</span>
      } @else {
        <span class="inline-flex gap-x-0">
          <span>{{value() > 0 && this.showPrefix() ? '+' : ''}}</span>
          <span>{{(value() * multiplier()) | number: digitsInfo()}}</span>
          <span>{{suffix()}}</span>
        </span>
      }
      @if (showBracket()) {&#41;}
    }
  `,
  host: {
    'class': 'inline-flex items-center align-text-top',
    '[class.text-neutral-500]': 'useColor() && value() == 0',
    '[class.text-green-500]': 'useColor() && value() < 0',
    '[class.text-red-600]': 'useColor() && value() > 0',
    '[attr.data-value]': 'value()'
  },
  changeDetection: ChangeDetectionStrategy.OnPush,
  preserveWhitespaces: true,
  imports: [
    DecimalPipe,
    NzToolTipModule,
  ],
})
export class ValueFormatter {

  value = input.required<string | number | any>();
  useLink = input(false, { transform: booleanAttribute });
  useColor = input(false, { transform: booleanAttribute });
  useMultiplier = input(true, { transform: booleanAttribute });
  showBracket = input(false, { transform: booleanAttribute });
  showPrefix = computed(() => this.useColor());
  linkTip = input<string>();
  suffix = input<string>();
  digitsInfo = input<string>('1.0-2');

  onLinkClick = output();

  multiplier = computed(() => {
    const hasSymbol = ['%', 'pp'].includes(this.suffix());

    return (
      hasSymbol && this.useMultiplier()
        ? 100 : 1
    )
  });
  
}
