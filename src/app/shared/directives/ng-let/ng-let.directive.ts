import { Directive, Input, TemplateRef, ViewContainerRef } from '@angular/core';

interface NgLetContext<T> {
  $implicit: T;
  ngLet: T;
}

@Directive({
  selector: '[ngLet]',
})
export class NgLetDirective<T> {
  @Input()
  set ngLet(value: T) {
    this.viewContainerRef.clear();
    this.viewContainerRef.createEmbeddedView(this.templateRef, {
      $implicit: value,
      ngLet: value,
    });
  }

  constructor(
    private viewContainerRef: ViewContainerRef,
    private templateRef: TemplateRef<NgLetContext<T>>
  ) {}
}
