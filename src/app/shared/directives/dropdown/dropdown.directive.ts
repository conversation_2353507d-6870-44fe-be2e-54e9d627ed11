import { TemplatePortal } from '@angular/cdk/portal';
import { Overlay, OverlayContainer, OverlayRef } from '@angular/cdk/overlay';
import { Directive, ElementRef, inject, Injector, input, TemplateRef, ViewContainerRef } from '@angular/core';
import { POSITION_MAP } from '@common/const';

@Directive({
  selector: '[appDropdown]',
  host: {
    '(click)': 'open()',
  }
})
export class DropdownDirective {

  overlay = inject(Overlay);
  overlayContainer = inject(OverlayContainer, { optional: true });
  viewContainer = inject(ViewContainerRef);
  elementRef = inject(ElementRef);
  injector = inject(Injector);

  dropdownMenu = input<TemplateRef<any>>(null);
  dropdownMenuContext = input<object | null>(null);
  outsideClosable = input<boolean>(true, { alias: 'dropdownMenuOutsideClosable'});

  _overlayRef: OverlayRef;
  
  open() {
    if (this.dropdownMenu() && !this._overlayRef) {
      const portal = new TemplatePortal(this.dropdownMenu(), this.viewContainer, this.dropdownMenuContext(), this.injector);

      this._overlayRef = this.overlay.create({
        disposeOnNavigation: true,
        panelClass: 'pointer-events-none!',
        scrollStrategy: this.overlay.scrollStrategies.reposition(),
        positionStrategy: this.overlay.position().flexibleConnectedTo(this.elementRef.nativeElement).withPositions([
          POSITION_MAP.bottomLeft,
          POSITION_MAP.topLeft,
          POSITION_MAP.bottomRight,
          POSITION_MAP.topRight,
        ]),
      });

      this._overlayRef.outsidePointerEvents().subscribe(() => {
        if (this.outsideClosable()) {
          this.close();
        }
      })
    
      // const dom = this._overlayRef.hostElement.parentElement.classList;
      const dom = this.overlayContainer.getContainerElement().classList
  
      dom?.add('z-50!');
      dom?.remove('z-1001!');

      this._overlayRef.attach(portal);
    }
  }
  

  close(): void {
    this._overlayRef?.detach();
    this._overlayRef = null;
  }

}
