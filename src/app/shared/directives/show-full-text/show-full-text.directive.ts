import { Directive, HostListener, ElementRef, <PERSON>derer2, <PERSON><PERSON><PERSON>t, On<PERSON>estroy, Input } from '@angular/core';

@Directive({
  selector: '[appShowFullText], [showFullText]',
})
export class ShowFullTextDirective implements OnInit, OnDestroy {

  @Input()
  textOverflow: 'ellipsis' | 'initial' = 'ellipsis';

  @Input()
  overflow: 'hidden' | 'visible' = 'hidden';

  @Input()
  delay = null;

  @Input()
  leaveDelay = 0;

  @Input()
  paddingRight = 5;

  @Input()
  width: string = '100%';

  @Input()
  shadow: boolean = false;

  private host: HTMLDivElement;
  private child: any;
  private timer: any;


  constructor(
    private readonly element: ElementRef,
    private readonly render: Renderer2,
  ) {
    this.host = this.element.nativeElement;
  }


  ngOnInit() {
    this.child = this.host.children[0];
    this.render.setStyle(this.host, 'overflow', 'hidden');
    this.render.setStyle(this.host, 'position', 'relative');
    this.render.setStyle(this.host, 'white-space', 'nowrap');
    // this.render.setStyle(this.host, 'line-height', '1');
    this.render.setAttribute(this.child, 'style', `
      display: inline-block;
      // line-height: 1;
      vertical-align: top;
      text-overflow: ${ this.textOverflow };
      white-space: nowrap;
      overflow: ${ this.overflow };
      width: ${ this.width };
      padding-right: ${this.paddingRight}px;
    `);
  }


  ngOnDestroy() {
    this.render.removeAttribute(this.host, 'style');
  }


  @HostListener('mouseenter', ['$event'])
  onMouseEnter() {
    clearTimeout(this.timer);

    this.render.setStyle(this.child, 'width', 'auto');
    this.render.removeStyle(this.child, 'white-space');
    this.render.removeStyle(this.child, 'text-overflow');

    if (this.delay === null) {
      this.render.removeStyle(this.child, 'transition');
    } else {
      this.render.setStyle(this.child, 'transition', `transform ${this.delay}s ease 0s`);
    }
  }


  @HostListener('mousemove', ['$event'])
  onMouseMove(event: MouseEvent) {
    const offsetX = Math.floor(event.pageX - this.host.getBoundingClientRect().left);
    const x       = Math.floor((offsetX / this.host.offsetWidth) * (this.child.offsetWidth - this.host.offsetWidth));

    this.render.setStyle(this.child, 'transform', `translate3d(-${x}px, 0, 0)`);
  }


  @HostListener('mouseleave')
  onMouseLeave() {
    const speed = 0.45;
    const { leaveDelay } = this;

    this.render.setStyle(this.child, 'transform',  `translate3d(0, 0, 0)`);
    this.render.setStyle(this.child, 'transition', `transform ${speed}s ease ${leaveDelay}s`);

    this.timer = setTimeout(() => this.restore(), speed * 1000 + leaveDelay * 1000);
  }


  private restore(): void {
    this.render.setStyle(this.child, 'width', this.width);
    this.render.setStyle(this.child, 'white-space', 'nowrap');
    this.render.setStyle(this.child, 'text-overflow', this.textOverflow);
  }


}
