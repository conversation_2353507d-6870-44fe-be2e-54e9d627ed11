import { AfterViewInit, DestroyRef, Directive, TemplateRef, ViewContainerRef, inject, input } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { ActivatedRoute } from '@angular/router';

/**
 * @example
 * ```html
 *  <pre *queryRouteParam="'version=next'" class="text-xs">
      {{form.value | json}}
    </pre>
 * ```
 */
@Directive({
  selector: '[queryRouteParam]',
  standalone: true
})
export class QueryRouteParamDirective implements AfterViewInit {

  readonly route = inject(ActivatedRoute);
  readonly viewContainerRef = inject(ViewContainerRef);
  readonly templateRef = inject(TemplateRef);
  readonly destroyRef = inject(DestroyRef);

  queryString = input<string>(null, { alias: 'queryRouteParam' });
  
  ngAfterViewInit(): void {
    this.route.queryParamMap.pipe(
      takeUntilDestroyed(this.destroyRef)
    ).subscribe(paramMap => {
      const [key, value] = this.queryString().split('=');
      
      if (
        (paramMap.get(key) !== null) &&
        (
          (value === undefined && paramMap.get(key) === '') ||
          (value !== undefined && paramMap.get(key) === value)
        )
      ) {
        this._render();
      }
    })
  }

  private _render() {
    this.viewContainerRef.clear();
    this.viewContainerRef.createEmbeddedView(this.templateRef);
  }

}
