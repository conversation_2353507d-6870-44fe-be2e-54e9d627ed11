import { inject } from '@angular/core';
import { ActivatedRouteSnapshot, CanActivateFn, Router, RouterStateSnapshot, Routes } from '@angular/router';
import { Observable } from 'rxjs';
import { UraApiService } from '@api/ura-api.service';
import { UserInfoService } from '@api/user-info.service';
import { isDev } from '@common/const';
import { UserService } from '@common/service';

const AuthGuard: CanActivateFn = (route: ActivatedRouteSnapshot, state: RouterStateSnapshot) => {
  const userInfo = inject(UserInfoService);
  const userService = inject(UserService);
  const uraApiService = inject(UraApiService);
  const router = inject(Router);

  if (isDev() && userInfo.ts && userInfo.privateName) {
    return true;
  }

  return new Observable(subscribe => {
    userInfo.fetchCurrentUser().subscribe(res => {
      const { user, ts } = res;

      if (user && ts) {
        userInfo.privateName = user;
        userInfo.ts = ts;

        userInfo.getRoleInfo(user).subscribe(res => {
          console.log('[roles]', res.data);
          if (Array.isArray(res.data)) {
            userService.setRoles(res.data);
          }
        })

        uraApiService.getMenu(userInfo.privateName, 'Caerus').subscribe(res => {
          const data = JSON.parse(res.data) as any[];
          const merge = [data, ...data.map(item => item.childMenu)].flat(1);
          const hasAuth = merge.some(item => {
            switch (true) {
              case item.url === '/analysis/self-help' && state.url.startsWith('/analysis/self-help'):
              case item.url === '/monitor/business' && state.url.startsWith('/monitor/business'):
              case item.url === '/example':
              case state.url.split('?')[0] === item.url:
                return true;
              default:
                return false;
            }
          });
          const links = data.sort((a, b) => a.sort - b.sort);
          userInfo.menu.set(links);
          userInfo.config.set(data);

          if (hasAuth) {
            subscribe.next(true);
          } else if (links.length > 0) {
            console.log(`无[${state.url}]权限, 即将跳转[${links[0].name}]`);
            subscribe.next(false);
            router.navigateByUrl(links[0].url);
          } else {
            subscribe.next(false);
            router.navigateByUrl('/exception/403');
          }
        });
      } else {
        subscribe.next(false);
      }
    });
  });
};

export const routes: Routes = [
  {
    path: 'home',
    canActivate: [AuthGuard],
    loadChildren: () => import('./views/home/<USER>').then(mod => mod.ROUTES),
    title: '首页',
  },
  {
    path: 'cockpit',
    canActivate: [AuthGuard],
    loadChildren: () => import('./views/cockpit/cockpit.routes').then(mod => mod.ROUTES),
    title: '管理驾驶舱',
  },
  {
    path: 'fluctuate',
    canActivate: [AuthGuard],
    loadChildren: () => import('./views/fluctuation/fluctuation.routes').then(mod => mod.ROUTES),
  },
  {
    path: 'market',
    canActivate: [AuthGuard],
    loadChildren: () => import('./views/market/market.routes').then(mod => mod.ROUTES),
  },
  {
    path: 'fluctuate-analysis',
    redirectTo: 'fluctuate/analysis',
    pathMatch: 'full',
  },
  {
    path: 'analysis',
    canActivate: [AuthGuard],
    loadChildren: () => import('./views/analysis/analysis.routes').then(mod => mod.ROUTES),
  },
  {
    path: 'monitor',
    canActivate: [AuthGuard],
    loadChildren: () => import('./views/monitor/monitor.routes').then(mod => mod.ROUTES),
  },
  {
    path: 'exception',
    loadChildren: () => import('./views/exception/exception.routes').then(mod => mod.ROUTES),
  },
  {
    path: 'example',
    loadComponent: () => import('./views/example/example.component').then(mod => mod.ExampleComponent),
  },
  {
    path: '',
    redirectTo: 'home',
    pathMatch: 'full',
  },
  {
    path: '**',
    redirectTo: 'exception',
  },
];
