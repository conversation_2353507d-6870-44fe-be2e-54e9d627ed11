import { ApplicationConfig, importProvidersFrom } from '@angular/core';
import { provideAnimations } from '@angular/platform-browser/animations';
import { provideRouter, withComponentInputBinding, withInMemoryScrolling } from '@angular/router';
import { provideHttpClient, withFetch, withInterceptors, withInterceptorsFromDi } from '@angular/common/http';
import { provideNzIcons } from 'ng-zorro-antd/icon';
import { NzModalModule } from 'ng-zorro-antd/modal';
import { NzDrawerModule } from 'ng-zorro-antd/drawer';
import { registerLocaleData } from '@angular/common';
import { ToolOutline, DashboardOutline, MenuUnfoldOutline, MenuFoldOutline, AppstoreOutline, ForkOutline, DownloadOutline, FilterOutline, AlertFill } from '@ant-design/icons-angular/icons';
import zh from '@angular/common/locales/zh';

import { ModalModule } from '@core/modal';
import { ModalModule as DialogModule } from '@core/dialog';
import { apiInterceptor } from '@core/interceptors';
import { routes } from './app.routes';

registerLocaleData(zh);

export const appConfig: ApplicationConfig = {
  providers: [
    provideNzIcons([
      ToolOutline, DashboardOutline, MenuUnfoldOutline, MenuFoldOutline, AppstoreOutline, 
      ForkOutline, DownloadOutline, FilterOutline, AlertFill
    ]),
    importProvidersFrom(NzDrawerModule, NzModalModule, ModalModule.forRoot(), DialogModule.forRoot()),
    provideAnimations(),
    provideHttpClient(
      withInterceptorsFromDi(), 
      withFetch(), 
      withInterceptors([apiInterceptor])
    ),
    provideRouter(
      routes, 
      withInMemoryScrolling({ scrollPositionRestoration: 'enabled' }),
      withComponentInputBinding(), 
    ),
  ],
};
