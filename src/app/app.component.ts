import { RouterOutlet } from '@angular/router';
import { AfterViewInit, Component, effect, inject } from '@angular/core';

import { BuriedPointService } from '@common/service';
import { _pad, base64toBlob } from '@common/function';
import { DebugDirective } from '@shared/directives';

@Component({
  selector: 'app-root',
  template: `
    <h2 *debug class="absolute right-3 z-100 text-2xl leading-16 text-neutral-400 space-x-3 w-16">
      <span class="hidden  xs:block  sm:hidden">XS</span>
      <span class="hidden  sm:block  md:hidden">SM</span>
      <span class="hidden  md:block  lg:hidden">MD</span>
      <span class="hidden  lg:block  xl:hidden">LG</span>
      <span class="hidden  xl:block 2xl:hidden">XL</span>
      <span class="hidden 2xl:block 3xl:hidden">2XL</span>
      <span class="hidden 3xl:block 4xl:hidden">3XL</span>
    </h2>
    <router-outlet />
  `,
  imports: [
    RouterOutlet,
    DebugDirective,
  ],
})
export class AppComponent implements AfterViewInit {

  buriedPointService = inject(BuriedPointService);
  waterMarkRef: HTMLElement;


  constructor() {
    effect(() => {
      const username = this.buriedPointService.username();

      if (this.waterMarkRef) {
        this.waterMarkRef.style.backgroundImage = `url(${this.water(username)})`;
      }
    })
  }


  ngAfterViewInit(): void {
    this.addWatermark();

    // 监听 DOM 变化
    const targetNode = document.body;  // 监听整个 body 或你感兴趣的父节点
    const config = { childList: true, subtree: true };  // 监听子节点的变化，及所有子树的变化

    const observer = new MutationObserver((mutationsList, observer) => {
      mutationsList.forEach(mutation => {
        mutation.removedNodes.forEach((removedNode: any) => {
          if (removedNode.id === 'watermark') {
            console.log('水印元素被删除了');
            // 在这里做你需要的处理，如重新添加水印
            this.addWatermark();
          }
        });
      });
    });

    // 开始监听
    observer.observe(targetNode, config);
  }


  addWatermark() {
    const username = this.buriedPointService.username();
    this.waterMarkRef = document.createElement('div');
    this.waterMarkRef.id = 'watermark';
    this.waterMarkRef.classList.add('_bgWaterMark');
    this.waterMarkRef.style.backgroundImage = `url(${this.water(username)})`;
    document.body.appendChild(this.waterMarkRef);
  }


  water(text: string) {
    const canvas = document.createElement('canvas');
    const devicePixelRatio = window.devicePixelRatio || 1;
    canvas.width = 300 * devicePixelRatio;
    canvas.height = 150 * devicePixelRatio;
    
    const ctx = canvas.getContext('2d');
    const date = new Date();
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();

    ctx.fillStyle = 'rgba(0, 0, 0, 0.07)';
    ctx.font = `${devicePixelRatio * 12}px Arial`;
    ctx.rotate((-25 * Math.PI) / 180);
    ctx.translate(-20, 100);
    ctx.fillText(`${text} ${year}-${_pad(month)}-${_pad(day)}`, 12, 24);
    
    
    return URL.createObjectURL(base64toBlob(ctx.canvas.toDataURL()));
  }
  
}
