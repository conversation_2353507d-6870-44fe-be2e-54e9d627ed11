/** @type {import('tailwindcss').Config} */
const plugin = require('tailwindcss/plugin');

module.exports = {
  darkMode: 'class',
  content: [
    "./src/**/*.{html,ts}",
  ],
  theme: {
    extend: {
      // key: 300 / 4 = 75
      // value: 300 / 16 = 18.75
      'spacing': {
        'unset': 'unset',
        'em': '1em',
        '25': '6.25rem',   // 100px
        '26': '6.5rem', // 104px
        '34': '8.5rem', // 136px
        '46': '11.5rem', // 184px
        '116': '29rem', // 464px
      },
      'colors': {
        'primary': '#1890ff',
      },
      'lineHeight': {
        '0': '0'
      },
      'scale': {
        '60': '.6',
        '65': '.65',
        '70': '.7',
        '80': '.8',
        '85': '.85',
      },
      'width': {
        '7.5': '1.875rem',
        '18':  '4.5rem',
        '62':  '15.5rem',
      },
      'height': {
        '4.5': '1.125rem',
        '7.5': '1.875rem',
        '15': '3.75rem',
        '50': '12.5rem'
      },
      'maxWidth': {
        '1/4': '25%',
        '1/3': '33.333333%',
      },
      'minWidth': {
        '125': '31.25rem'
      },
      'minHeight': {
        '50': '12.5rem', // 200px
        '64.5': '16.125rem'
      },
      'zIndex': {
        '999': 999,
        '1001': 1001
      },
      'textUnderlineOffset': {
        '3': '3px'
      },
      'textShadow': {
        sm: '0 0 1px var(--tw-shadow-color)',
        DEFAULT: '0 0 4px var(--tw-shadow-color)',
        lg: '0 0 6px var(--tw-shadow-color)',
      },
    },
  },
  plugins: [
    plugin(function ({ matchUtilities, theme }) {
      matchUtilities(
        {
          'text-shadow': (value) => ({
            textShadow: value,
          }),
        },
        { values: theme('textShadow') }
      )
    }),
  ],
}